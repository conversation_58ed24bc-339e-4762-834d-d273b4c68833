<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title></title>
    <style type="text/css">
    #periodData {
        height:;
    }

    p {
        margin: 10px 0;
        padding: 0;
    }

    table {
        border-collapse: collapse;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        display: block;
        margin: 0;
        padding: 0;
    }

    img,
    a img {
        border: 0;
        height: auto;
        outline: none;
        text-decoration: none;
    }

    #bodyTable,
    #bodyCell {
        height: 100%;
        margin: 0;
        padding: 0;
        width: 100%;
    }

    .mcnPreviewText {
        display: none !important;
    }

    #outlook a {
        padding: 0;
    }

    img {
        -ms-interpolation-mode: bicubic;
    }

    table {
        mso-table-lspace: 0pt;
        mso-table-rspace: 0pt;
    }

    .ReadMsgBody {
        width: 100%;
    }

    .ExternalClass {
        width: 100%;
    }

    p,
    a,
    li,
    td,
    blockquote {
        mso-line-height-rule: exactly;
    }

    a[href^=tel],
    a[href^=sms] {
        color: inherit;
        cursor: default;
        text-decoration: none;
    }

    p,
    a,
    li,
    td,
    body,
    table,
    blockquote {
        -ms-text-size-adjust: 100%;
        -webkit-text-size-adjust: 100%;
    }

    .ExternalClass,
    .ExternalClass p,
    .ExternalClass td,
    .ExternalClass div,
    .ExternalClass span,
    .ExternalClass font {
        line-height: 100%;
    }

    a[x-apple-data-detectors] {
        color: inherit !important;
        text-decoration: none !important;
        font-size: inherit !important;
        font-family: inherit !important;
        font-weight: inherit !important;
        line-height: inherit !important;
    }

    .templateContainer {
        max-width: 600px !important;
    }

    a.mcnButton {
        display: block;
    }

    .mcnImage,
    .mcnRetinaImage {
        vertical-align: bottom;
    }

    .mcnTextContent {
        word-break: break-word;
    }

    .mcnTextContent img {
        height: auto !important;
    }

    .mcnDividerBlock {
        table-layout: fixed !important;
    }

    /*
    @tab Page
    @section Heading 1
    @style heading 1
    */
    h1 {
        /*@editable*/
        color: #00b3b7;
        /*@editable*/
        font-family: 'Roboto', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        /*@editable*/
        font-size: 40px;
        /*@editable*/
        font-style: normal;
        /*@editable*/
        font-weight: bold;
        /*@editable*/
        line-height: 150%;
        /*@editable*/
        letter-spacing: normal;
        /*@editable*/
        text-align: left;
    }

    /*
    @tab Page
    @section Heading 2
    @style heading 2
    */
    h2 {
        /*@editable*/
        color: #00b3b7;
        /*@editable*/
        font-family: 'Roboto', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        /*@editable*/
        font-size: 28px;
        /*@editable*/
        font-style: normal;
        /*@editable*/
        font-weight: bold;
        /*@editable*/
        line-height: 150%;
        /*@editable*/
        letter-spacing: normal;
        /*@editable*/
        text-align: left;
    }

    /*
    @tab Page
    @section Heading 3
    @style heading 3
    */
    h3 {
        /*@editable*/
        color: #00b3b7;
        /*@editable*/
        font-family: Helvetica;
        /*@editable*/
        font-size: 22px;
        /*@editable*/
        font-style: normal;
        /*@editable*/
        font-weight: bold;
        /*@editable*/
        line-height: 150%;
        /*@editable*/
        letter-spacing: normal;
        /*@editable*/
        text-align: left;
    }

    /*
    @tab Page
    @section Heading 4
    @style heading 4
    */
    h4 {
        /*@editable*/
        color: #00b3b7;
        /*@editable*/
        font-family: 'Roboto', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        /*@editable*/
        font-size: 20px;
        /*@editable*/
        font-style: italic;
        /*@editable*/
        font-weight: normal;
        /*@editable*/
        line-height: 125%;
        /*@editable*/
        letter-spacing: normal;
        /*@editable*/
        text-align: left;
    }

    /*
    @tab Header
    @section Header Container Style
    */
    #templateHeader {
        /*@editable*/
        background-color: #ffffff;
        /*@editable*/
        background-image: none;
        /*@editable*/
        background-repeat: no-repeat;
        /*@editable*/
        background-position: center;
        /*@editable*/
        background-size: contain;
        /*@editable*/
        border-top: 1px none;
        /*@editable*/
        border-bottom: 10px none;
        /*@editable*/
        padding-top: 0px;
        /*@editable*/
        padding-bottom: 0px;
    }

    /*
    @tab Header
    @section Header Interior Style
    */
    .headerContainer {
        /*@editable*/
        background-color: #ffffff;
        /*@editable*/
        background-image: none;
        /*@editable*/
        background-repeat: no-repeat;
        /*@editable*/
        background-position: center;
        /*@editable*/
        background-size: cover;
        /*@editable*/
        border-top: 0;
        /*@editable*/
        border-bottom: 0;
        /*@editable*/
        padding-top: 0;
        /*@editable*/
        padding-bottom: 0;
    }

    /*
    @tab Header
    @section Header Text
    */
    .headerContainer .mcnTextContent,
    .headerContainer .mcnTextContent p {
        /*@editable*/
        color: #747575;
        /*@editable*/
        font-family: Helvetica;
        /*@editable*/
        font-size: 16px;
        /*@editable*/
        line-height: 150%;
        /*@editable*/
        text-align: left;
    }

    /*
    @tab Header
    @section Header Link
    */
    .headerContainer .mcnTextContent a,
    .headerContainer .mcnTextContent p a {
        /*@editable*/
        color: #96c33d;
        /*@editable*/
        font-weight: normal;
        /*@editable*/
        text-decoration: underline;
    }

    /*
    @tab Body
    @section Body Container Style
    */
    #templateBody {
        /*@editable*/
        background-color: #ffffff;
        /*@editable*/
        background-image: none;
        /*@editable*/
        background-repeat: no-repeat;
        /*@editable*/
        background-position: center;
        /*@editable*/
        background-size: cover;
        /*@editable*/
        border-top: 0;
        /*@editable*/
        border-bottom: 0;
        /*@editable*/
        padding-top: 18px;
        /*@editable*/
        padding-bottom: 18px;
    }

    /*
    @tab Body
    @section Body Interior Style
    */
    .bodyContainer {
        /*@editable*/
        background-color: transparent;
        /*@editable*/
        background-image: none;
        /*@editable*/
        background-repeat: no-repeat;
        /*@editable*/
        background-position: center;
        /*@editable*/
        background-size: cover;
        /*@editable*/
        border-top: 0;
        /*@editable*/
        border-bottom: 0;
        /*@editable*/
        padding-top: 0;
        /*@editable*/
        padding-bottom: 0;
    }

    /*
    @tab Body
    @section Body Text
    */
    .bodyContainer .mcnTextContent,
    .bodyContainer .mcnTextContent p {
        /*@editable*/
        color: #747575;
        /*@editable*/
        font-family: 'Roboto', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        /*@editable*/
        font-size: 16px;
        /*@editable*/
        line-height: 150%;
        /*@editable*/
        text-align: left;
    }

    /*
    @tab Body
    @section Body Link
    */
    .bodyContainer .mcnTextContent a,
    .bodyContainer .mcnTextContent p a {
        /*@editable*/
        color: #95c33d;
        /*@editable*/
        font-weight: normal;
        /*@editable*/
        text-decoration: underline;
    }

    /*
    @tab Footer
    @section Footer Style
    */
    #templateFooter {
        /*@editable*/
        background-color: #00b3b7;
        /*@editable*/
        background-image: none;
        /*@editable*/
        background-repeat: no-repeat;
        /*@editable*/
        background-position: center;
        /*@editable*/
        background-size: cover;
        /*@editable*/
        border-top: 0;
        /*@editable*/
        border-bottom: 0;
        /*@editable*/
        padding-top: 0px;
        /*@editable*/
        padding-bottom: 0px;
    }

    /*
    @tab Footer
    @section Footer Interior Style
    */
    .footerContainer {
        /*@editable*/
        background-color: transparent;
        /*@editable*/
        background-image: none;
        /*@editable*/
        background-repeat: no-repeat;
        /*@editable*/
        background-position: center;
        /*@editable*/
        background-size: cover;
        /*@editable*/
        border-top: 0;
        /*@editable*/
        border-bottom: 0;
        /*@editable*/
        padding-top: 0;
        /*@editable*/
        padding-bottom: 0;
    }

    /*
    @tab Footer
    @section Footer Text
    */
    .footerContainer .mcnTextContent,
    .footerContainer .mcnTextContent p {
        /*@editable*/
        color: #FFFFFF;
        /*@editable*/
        font-family: 'Roboto', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        /*@editable*/
        font-size: 12px;
        /*@editable*/
        line-height: 150%;
        /*@editable*/
        text-align: center;
    }

    /*
    @tab Footer
    @section Footer Link
    */
    .footerContainer .mcnTextContent a,
    .footerContainer .mcnTextContent p a {
        /*@editable*/
        color: #ffffff;
        /*@editable*/
        font-weight: normal;
        /*@editable*/
        text-decoration: underline;
    }

    @media only screen and (min-width:768px) {
        .templateContainer {
            width: 600px !important;
        }

    }

    @media only screen and (max-width: 480px) {

        body,
        table,
        td,
        p,
        a,
        li,
        blockquote {
            -webkit-text-size-adjust: none !important;
        }

    }

    @media only screen and (max-width: 480px) {
        body {
            width: 100% !important;
            min-width: 100% !important;
        }

    }

    @media only screen and (max-width: 480px) {
        .mcnRetinaImage {
            max-width: 100% !important;
        }

    }

    @media only screen and (max-width: 480px) {
        .mcnImage {
            width: 100% !important;
        }

    }

    @media only screen and (max-width: 480px) {

        .mcnCartContainer,
        .mcnCaptionTopContent,
        .mcnRecContentContainer,
        .mcnCaptionBottomContent,
        .mcnTextContentContainer,
        .mcnBoxedTextContentContainer,
        .mcnImageGroupContentContainer,
        .mcnCaptionLeftTextContentContainer,
        .mcnCaptionRightTextContentContainer,
        .mcnCaptionLeftImageContentContainer,
        .mcnCaptionRightImageContentContainer,
        .mcnImageCardLeftTextContentContainer,
        .mcnImageCardRightTextContentContainer,
        .mcnImageCardLeftImageContentContainer,
        .mcnImageCardRightImageContentContainer {
            max-width: 100% !important;
            width: 100% !important;
        }

    }

    @media only screen and (max-width: 480px) {
        .mcnBoxedTextContentContainer {
            min-width: 100% !important;
        }

    }

    @media only screen and (max-width: 480px) {
        .mcnImageGroupContent {
            padding: 9px !important;
        }

    }

    @media only screen and (max-width: 480px) {

        .mcnCaptionLeftContentOuter .mcnTextContent,
        .mcnCaptionRightContentOuter .mcnTextContent {
            padding-top: 9px !important;
        }

    }

    @media only screen and (max-width: 480px) {

        .mcnImageCardTopImageContent,
        .mcnCaptionBottomContent:last-child .mcnCaptionBottomImageContent,
        .mcnCaptionBlockInner .mcnCaptionTopContent:last-child .mcnTextContent {
            padding-top: 18px !important;
        }

    }

    @media only screen and (max-width: 480px) {
        .mcnImageCardBottomImageContent {
            padding-bottom: 9px !important;
        }

    }

    @media only screen and (max-width: 480px) {
        .mcnImageGroupBlockInner {
            padding-top: 0 !important;
            padding-bottom: 0 !important;
        }

    }

    @media only screen and (max-width: 480px) {
        .mcnImageGroupBlockOuter {
            padding-top: 9px !important;
            padding-bottom: 9px !important;
        }

    }

    @media only screen and (max-width: 480px) {

        .mcnTextContent,
        .mcnBoxedTextContentColumn {
            padding-right: 18px !important;
            padding-left: 18px !important;
        }

    }

    @media only screen and (max-width: 480px) {

        .mcnImageCardLeftImageContent,
        .mcnImageCardRightImageContent {
            padding-right: 18px !important;
            padding-bottom: 0 !important;
            padding-left: 18px !important;
        }

    }

    @media only screen and (max-width: 480px) {
        .mcpreview-image-uploader {
            display: none !important;
            width: 100% !important;
        }

    }

    @media only screen and (max-width: 480px) {

        /*
    @tab Mobile Styles
    @section Heading 1
    @tip Make the first-level headings larger in size for better readability on small screens.
    */
        h1 {
            /*@editable*/
            font-size: 30px !important;
            /*@editable*/
            line-height: 125% !important;
        }

    }

    @media only screen and (max-width: 480px) {

        /*
    @tab Mobile Styles
    @section Heading 2
    @tip Make the second-level headings larger in size for better readability on small screens.
    */
        h2 {
            /*@editable*/
            font-size: 26px !important;
            /*@editable*/
            line-height: 125% !important;
        }

    }

    @media only screen and (max-width: 480px) {

        /*
    @tab Mobile Styles
    @section Heading 3
    @tip Make the third-level headings larger in size for better readability on small screens.
    */
        h3 {
            /*@editable*/
            font-size: 20px !important;
            /*@editable*/
            line-height: 150% !important;
        }

    }

    @media only screen and (max-width: 480px) {

        /*
    @tab Mobile Styles
    @section Heading 4
    @tip Make the fourth-level headings larger in size for better readability on small screens.
    */
        h4 {
            /*@editable*/
            font-size: 18px !important;
            /*@editable*/
            line-height: 150% !important;
        }

    }

    @media only screen and (max-width: 480px) {

        /*
    @tab Mobile Styles
    @section Boxed Text
    @tip Make the boxed text larger in size for better readability on small screens. We recommend a font size of at least 16px.
    */
        .mcnBoxedTextContentContainer .mcnTextContent,
        .mcnBoxedTextContentContainer .mcnTextContent p {
            /*@editable*/
            font-size: 14px !important;
            /*@editable*/
            line-height: 150% !important;
        }

    }

    @media only screen and (max-width: 480px) {

        /*
    @tab Mobile Styles
    @section Header Text
    @tip Make the header text larger in size for better readability on small screens.
    */
        .headerContainer .mcnTextContent,
        .headerContainer .mcnTextContent p {
            /*@editable*/
            font-size: 16px !important;
            /*@editable*/
            line-height: 150% !important;
        }

    }

    @media only screen and (max-width: 480px) {

        /*
    @tab Mobile Styles
    @section Body Text
    @tip Make the body text larger in size for better readability on small screens. We recommend a font size of at least 16px.
    */
        .bodyContainer .mcnTextContent,
        .bodyContainer .mcnTextContent p {
            /*@editable*/
            font-size: 16px !important;
            /*@editable*/
            line-height: 150% !important;
        }

    }

    @media only screen and (max-width: 480px) {

        /*
    @tab Mobile Styles
    @section Footer Text
    @tip Make the footer content text larger in size for better readability on small screens.
    */
        .footerContainer .mcnTextContent,
        .footerContainer .mcnTextContent p {
            /*@editable*/
            font-size: 14px !important;
            /*@editable*/
            line-height: 150% !important;
        }

    }
</style>
</head>
<body>
    <div style="width: 100%;margin: auto; padding-top: 10px;padding-bottom: 10px;">
        <div style="padding: 10px 0;max-width: 700px;background: #FFFFFF;margin: auto;">
            <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                <tbody>
                <tr>
                    <td align="left" valign="middle" width="18%" class="mcnFollowIconContent" style="padding-left: 8px; mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                        <img width="92px" height="68px" src="https://web.learning-genie.com/modules/core/img/us/logo.png" style="border: 0;height: auto;outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;">
                    </td>
                    <td align="left" valign="middle" width="82%" class="mcnFollowIconContent" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                        <div id="logoStr" style="float: left; font-size: 22px;">
                            <span>Import/Sync Summary Report</span>
                        </div>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div style="padding: 10px;width: 700px;background: #FFFFFF;margin: auto;margin-bottom: 19px;box-sizing: border-box;-moz-box-sizing: border-box;">
            <div id="AgencyInfo" style="clear:both">
                <div class="agencyInfoItem">
                    <div style="float: left;margin-left: 14px;font-size: 14px;color: #2E2E2E;font-weight: 700;">
                        Agency: <span id="agencyName">{agencyName}</span>
                    </div>
                    <div style="float: right;margin-right:14px;font-size: 14px;font-weight:600;">
                        Import time: <span id="importTime" style="color:#169195;">{importTime}</span>
                    </div>
                </div>
            </div>
            <div>
                <div style="display:@showError">
                    <div style="margin-top: 25px;">
                    <hr style="border:1px dotted #DFDEDE;" />
                </div>
                    <div id="textInfo" style="margin-top: 16px;margin-left: 14px;">
                    <div style="color: #FF3F3F;font-size: 16px;font-weight: 600;"><span>Oops, we have found the following children who
        							were not imported successfully. You might need to do a manual correction based on the issues prompted.</span><br></div>
                    <!--<div style="margin-top: 10px;color: #2E2E2E;font-size: 14px;"><span>You can go to the web to manually modify the-->
        							<!--child rating period or modify the child information in the import file, and then import it again.</span><br></div>-->
                    <!--<div style="margin-top: 1px;color: #2E2E2E;font-size: 14px;"><span>Click the link below to modify the child rating-->
        							<!--periods on the web.</span><br></div>-->
                    <!--<div style="margin-top: 6px;font-size: 14px;"><span><a href="@webServer/#/login" style="color: #17999D;">@webServer/#/login</a></span></div>-->
                </div>
                    <div id="unsuccessfultitle" style="margin-top: 15px">
                    <div class="agencyInfoItem" style="height: 50px;">
                        <div style="float: left;margin-left: 14px;font-size: 18px;color: #2E2E2E;font-weight: 600;line-height: 50px">
                            Children with unsuccessful update: <span>{unsuccessfulCount}</span>
                        </div>
                        <div style="float: right;margin-right:14px;font-size: 16px;font-weight:600; line-height: 50px">
                            <a href="@webServer/#/login?type=ImportError&importId=@importId&importTime={importTime}" target="_blank">
                                <button style="color:#FFFFFF;border:1px solid #FFFFFF; background:#2AB4B8; height:47px;width: 150px; border-radius: 24px; cursor: pointer;">Modify Manually</button>
                            </a>
                        </div>

                    </div>
                </div>
                    <div style="display:@errorReport">
                    <div style="margin-left: 16px;color: #2E2E2E;font-size: 16px;margin-top: 8px;display:@canbe;">The following errors can be
                        corrected online:</div>
                    <div style="width:667px;height: 125px;border: 1px solid rgb(218,218,218,1);background-color: #FCFCFE; border-radius: 3px;margin-left: 16px;margin-top: 8px;display:@NOTE_MISSED;">
                        <div style="width:645px;height: 49px; margin-left: 14px;margin-top: 12px;">
                            <div style="float: left;height: 45px; width:520px;font-weight: bold;">
                                Some notes taken outside of the new rating period will not appear within the new rating period.
                            </div>
                            <div style="float: right;height: 45px;width: 100px;color: #008488;font-size: 14px;font-weight: bold;font-family: SourceSansPro-Semibold,SourceSansPro;">
                                <span>Children: </span><span>{NOTE_MISSED}</span>
                            </div>
                        </div>
                        <div style="float: left;width: 645px;height:55px;margin-left: 14px;margin-right: 8px;font-size: 14px; color: #2E2E2E;font-weight: 400;">
                            If you update a new entry date of a child (e.g. transfer from IT to PS class), they might lose the notes that are dated before their entry date. You can choose to either edit the entry date, or to ignore these notes.
                        </div>
                    </div>
                    <div style="width:667px;height: 140px;border: 1px solid rgb(218,218,218,1);background-color: #FCFCFE; border-radius: 3px;margin-left: 16px; margin-top: 8px; display:@ENTRY_NOUPDATE;">
                        <div style="width:645px;height: 60px; margin-left: 14px;margin-top: 12px;">
                            <div style="float: left;height: 60px; width:520px;font-weight: bold;">
                                IT-PS: The entry date in the import file was not updated for some children transferring from IT to PS
                                classrooms.
                            </div>
                            <div style="float: right;height: 60px; width:100px;color: #008488;font-size: 14px;font-weight: bold;font-family: SourceSansPro-Semibold,SourceSansPro;">
                                <span>Children: </span><span>{ENTRY_NOUPDATE}</span>
                            </div>
                        </div>
                        <div style="float: left;width: 645px;height: 60px;margin-left: 14px;margin-right: 8px;font-size: 14px; color: #2E2E2E;font-weight: 400;">
                            If a child changes classes, but the entry date in the import file is not updated accordingly, the child will not be transferred. The entry date should be updated prior to the date on which the child changes classes.
                        </div>
                    </div>
                    <div style="width:667px;height: 114px;border: 1px solid rgb(218,218,218,1);background-color: #FCFCFE; border-radius: 3px;margin-left: 16px;margin-top: 8px; display:@ENTRY_AFTER_NOLOCK_COMPLETE;">
                        <div style="width:645px;height: 49px; margin-left: 14px;margin-top: 12px;">
                            <div style="float: left;height: 45px; width:520px;font-weight: bold;">
                                IT-PS: Child is 100% rated in the current rating period but has not been locked.
                            </div>
                            <div style="float: right;height: 45px;width:100px;color: #008488;font-size: 14px;font-weight: bold;font-family: SourceSansPro-Semibold,SourceSansPro;">
                                <span>Children: </span><span>{ENTRY_AFTER_NOLOCK_COMPLETE}</span>
                            </div>
                        </div>
                        <div style=" float:left;width: 645px;height:36px;margin-left: 14px;margin-right: 8px;font-size: 14px; color: #2E2E2E;font-weight: 400;">
                            Switching to the next rating period requires students to be locked after they are 100% rated in their current rating period.
                        </div>
                    </div>
                    <div style="width:667px;height: 127px;border: 1px solid rgb(218,218,218,1);background-color: #FCFCFE; border-radius: 3px;margin-left: 16px;margin-top: 8px; display:@updateChildInfo; ">
                        <div style="width:645px;height: 49px; margin-left: 14px;margin-top: 12px;">
                            <div style="float: left;height: 45px; width:520px;font-weight: bold;">
                                Update of child information in classes with individualized rating periods
                            </div>
                            <div style="float: right;height: 45px;width:100px;margin-top: 8px;color: #008488;font-size: 14px;font-weight: bold;font-family: SourceSansPro-Semibold,SourceSansPro;">
                                <span>Children: </span><span>{updateCount}</span>
                            </div>
                        </div>
                        <div style=" float:left;width: 645px;height:54px;margin-left: 14px;margin-right: 8px;font-size: 14px; color: #2E2E2E;font-weight: 400;">
                            Newly added children need you to set their alias types and rating periods. Please confirm or edit the aliases and rating periods if the entry date is updated or the children are newly imported. You can also choose to import before making changes.
                        </div>
                    </div>
                    <div style="width:667px;height: 127px;border: 1px solid rgb(218,218,218,1);background-color: #FCFCFE; border-radius: 3px;margin-left: 16px;margin-top: 8px; display:@ENTRY_IN_ACTIVE_LOCK;">
                        <div style="width:645px;height: 49px; margin-left: 14px;margin-top: 12px;">
                            <div style="float: left;height: 45px; width:520px;font-weight: bold;">
                                IT-PS: The entry date is set to start within a rating period that was locked.
                            </div>
                            <div style="float: right;height: 45px;width:100px;margin-top: 8px;color: #008488;font-size: 14px;font-weight: bold;font-family: SourceSansPro-Semibold,SourceSansPro;">
                                <span>Children: </span><span>{ENTRY_IN_ACTIVE_LOCK}</span>
                            </div>
                        </div>
                        <div style=" float:left;width: 645px;height:54px;margin-left: 14px;margin-right: 8px;font-size: 14px; color: #2E2E2E;font-weight: 400;">
                            If the entry date is set before the end date of the locked rating period, the child will fail to transfer. The entry date should be immediately after the end date of the locked rating period.
                        </div>
                    </div>
                    <div style="width:667px;height: 150px;border: 1px solid rgb(218,218,218,1);background-color: #FCFCFE; border-radius: 3px;margin-left: 16px;margin-top: 8px; display:@aliasChange;">
                        <div style="width:645px;height: 90px; margin-left: 14px;margin-top: 12px;">
                            <div style="float: left;height: 90px; width:520px;font-weight: bold;">
                                The alias type before and after a child's routine transfer is inconsistent. In this case the child will adopt the alias type of the new class, but the locked rating period and alias of the previous rating period will not change.
                            </div>
                            <div style="float: right;height: 45px;width:100px;margin-top: 8px;color: #008488;font-size: 14px;font-weight: bold;font-family: SourceSansPro-Semibold,SourceSansPro;">
                                <span>Children: </span><span>{ChangeCount}</span>
                            </div>
                        </div>
                        <div style=" float:left;width: 645px;height:36px;margin-left: 14px;margin-right: 8px;font-size: 14px; color: #2E2E2E;font-weight: 400;">
                            If these children need to change classes, please switch their rating period alias type and reconfirm, or modify the alias type.
                        </div>
                    </div>
                    <div style="width:667px;height: 95px;border: 1px solid rgb(218,218,218,1);background-color: #FCFCFE; border-radius: 3px;margin-left: 16px;margin-top: 8px; display:@ALIAS_NOTSAME;">
                        <div style="width:645px;height: 40px; margin-left: 14px;margin-top: 12px;">
                            <div style="float: left;height: 40px; width:520px;font-weight: bold;">
                                Rating period alias types before and after transfer are inconsistent.
                            </div>
                            <div style="float: right;height: 20px; width:100px;margin-top: 8px;color: #008488;font-size: 14px;font-weight: bold;font-family: SourceSansPro-Semibold,SourceSansPro;">
                                <span>Children: </span><span>{ALIAS_NOTSAME}</span>
                            </div>
                        </div>
                        <div style=" float:left;width: 645px;height:36px;margin-left: 14px;margin-right: 8px;font-size: 14px; color: #2E2E2E;font-weight: 400;">
                            The rating period alias before and after the transfer is inconsistent (for example, Time 1 to Fall) so the child will not transfer.
                        </div>
                    </div>



                    <div style="display:@cannot;">
                        <div style="margin-left: 16px;color: #2E2E2E;font-size: 16px;margin-top: 8px;">The following errors cannot be
                            corrected:</div>
                        <div style="width:667px;height: 153px;border: 1px solid rgb(218,218,218,1);background-color: #FCFCFE; border-radius: 3px;margin-left: 16px;margin-top: 8px;display:@TRANSFER_OVER_WEEK;">
                            <div style="width:645px;height: 69px; margin-left: 14px;margin-top: 12px;">
                                <div style="float: left;height: 69px; width:520px;font-weight: bold;">
                                    PS-IT: The child has been assigned the PS framework for more than a week and notes have been added, so the transfer to the IT class failed.
                                </div>
                                <div style="float: right;height: 45px;width:100px;margin-top: 8px;color: #008488;font-size: 14px;font-weight: bold;font-family: SourceSansPro-Semibold,SourceSansPro;">
                                    <span>Children: </span><span>{TRANSFER_OVER_WEEK}</span>
                                </div>
                            </div>
                            <div style=" float:left;width: 645px;height:54px;margin-left: 14px;margin-right: 8px;font-size: 14px; color: #2E2E2E;font-weight: 400;">
                                These children cannot be transferred to the IT framework because they have been assigned the PS framework for more than a week and notes have been added. Please contact Learning Genie Customer Support to transfer.
                            </div>
                        </div>
                        <div style="width:667px;height: 150px;border: 1px solid rgb(218,218,218,1);background-color: #FCFCFE; border-radius: 3px;margin-left: 16px;margin-top: 8px; display:@ALIAS_ERROR;">
                            <div style="width:645px;height: 90px; margin-left: 14px;margin-top: 12px;">
                                <div style="float: left;height: 90px; width:520px;font-weight: bold;">
                                    Rating period alias types before and after transfer are inconsistent.
                                </div>
                                <div style="float: right;height: 45px;width:100px;margin-top: 8px;color: #008488;font-size: 14px;font-weight: bold;font-family: SourceSansPro-Semibold,SourceSansPro;">
                                    <span>Children: </span><span>{ALIAS_ERROR}</span>
                                </div>
                            </div>
                            <div style=" float:left;width: 645px;height:36px;margin-left: 14px;margin-right: 8px;font-size: 14px; color: #2E2E2E;font-weight: 400;">
                                The rating period alias before and after the transfer is inconsistent (for example, Time 1 to Fall) so the child will not transfer.
                            </div>
                        </div>
                    </div>
                </div>
                </div>
                <div>
                    <div style="margin-top: 25px;">
                        <hr style="border:1px dotted #DFDEDE;" />
                    </div>
                    <div style="margin-left: 16px;color: #2E2E2E;font-size: 16px;margin-top: 21px;font-weight: bold;">
                        Congratulations! These centers have been imported successfully!
                    </div>
                    <div id="AgencyImportInfo" style="clear:both;margin-top: 18px;">
                        <div class="agencyInfoItem">
                            <div style="clear:both;">
                                <table style="width: 100%;" cellspacing="0">
                                    <thead style="background-color:rgba(217,237,247,1);font-size: 14px;color:#3B3B3B;text-align:center;">
                                    <tr style="height: 30px;" height="30px">
                                        <th>Center/Site</th>
                                        <th>Class count</th>
                                        <th>Children count</th>
                                        <th>Teacher count</th>
                                        <th>Parent count</th>
                                    </tr>
                                    </thead>
                                    <tbody id="agencyImportInfoData" style="background-color: #FAFBFC;color: #58666E;font-size: 14px;text-align:center;">
                                    @{agencyImportData}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div style="display: @showError2">
                        <div style="margin-top: 33px;">
                            <hr />
                        </div>
                        <div id="textInfo2" style="margin-top: 26px;margin-left: 14px;font-weight: bold;">
                            <div style="color: #FF3F3F;font-size: 16px;"><span>Oops, we have found the following children who were not imported successfully. You might need to do a manual correction based on the issues prompted.</span><br></div>
                            <div style="margin-top: 10px;color: #2E2E2E;font-size: 14px;width: 567px;"><span>You can go to the web to manually modify the child rating period or modify the child information in the import file, and then import it again.</span><br></div>
                            <div style="margin-top: 1px;color: #2E2E2E;font-size: 14px;"><span>Click on the link below to go to the web to modify the child's rating period.</span><br></div>
                            <div style="margin-top: 6px;font-size: 14px;"><span><a href="https://web.learning-genie.com/#/login" style="color: #17999D;">https://web.learning-genie.com/#/login</a></span></div>
                        </div>
                        <div id ="AgencyImportInfo2" style="clear:both;margin-top: 24px;">
                            <div class="agencyInfoItem">
                                <div style="clear:both;">
                                    <table style="width: 100%;" cellspacing="0">
                                        <thead style="background-color:#2AB4B8;font-size: 14px;color: #FFFFFF;text-align:center;">
                                        <tr style="height: 30px;" height="30px">
                                            <th>Center / Site</th>
                                            <th>Class</th>
                                            <th>Child Name</th>
                                            <th>Reason</th>
                                        </tr>
                                        </thead>
                                        <tbody id = "agencyImportInfoData2" style="background-color: #FAFBFC;color: #58666E;font-size: 14px;text-align:center;">
                                        @{childData}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div style="display:@entryDateDisplayAttr;">
                        <div style="width:667px;height: 80px;margin-left: 16px;margin-top: 8px;color: #2E2E2E;font-size: 16px;line-height:20px;font-weight: bold;">
                            The following children's entry dates have been revised, and some notes were transferred to the new class. The
                            class rating progress before and after the transfer (which may have been 100% completed) will be affected. This
                            new data will be reflected on the Assessment Progress Dashboard.
                        </div>
                        <div style="clear:both;margin-top: 18px;">
                            <div class="agencyInfoItem">
                                <div style="clear:both;">
                                    <table style="width: 100%;" cellspacing="0">
                                        <thead style="background-color:rgba(217,237,247,1);font-size: 14px;color:#3B3B3B;text-align:center;">
                                        <tr style="height: 30px;" height="30px">
                                            <th>Center </th>
                                            <th>Class</th>
                                            <th>First Name</th>
                                            <th>Last Name</th>
                                            <th>Date of Birth</th>
                                            <th>Entry Date</th>
                                            <th>Rating Period</th>
                                        </tr>
                                        </thead>
                                        <tbody id="childImportInfoData" style="background-color: #FAFBFC;color: #58666E;font-size: 14px;text-align:center;">
                                        @{childImportInfoData}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div style="display:@newImportDisplayAttr; margin-top: 20px">
                        <div style="width:667px;height: 20px;margin-left: 16px;margin-bottom: 10px;color: #2E2E2E;font-size: 16px;line-height:20px; font-weight: bold;">
                            Newly Imported Classes
                        </div>
                        <div style="clear:both;">
                            <div class="agencyInfoItem">
                                <div style="clear:both;">
                                    <table style="width: 100%;" cellspacing="0">
                                        <thead style="background-color:rgba(231,243,252,1);font-size: 14px;color: #3B3B3B;text-align:center;">
                                        <tr style="height: 30px;" height="30px">
                                            <th>Center / Site </th>
                                            <th>Class</th>
                                            <th>Framework</th>
                                            <th>Rating Period</th>
                                        </tr>
                                        </thead>
                                        <tbody id="newImportInfoData" style="background-color: #FAFBFC;color: #58666E;font-size: 14px;text-align:center;">
                                        @{newImportClassData}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div style="display: @newClassData; margin-top: 20px">
                        <div style="width:667px;height: 20px;margin-left: 16px; margin-bottom: 10px; color: #2E2E2E;font-size: 16px;line-height:20px; font-weight: bold;">
                            Newly Imported Classes
                        </div>
                        <div style="clear:both;">
                            <div class="agencyInfoItem">
                                <div style="clear:both;">
                                    <table style="width: 100%;" cellspacing="0">
                                        <thead style="background-color:rgba(231,243,252,1);font-size: 14px;color: #3B3B3B;text-align:center;">
                                        <tr style="height: 30px;" height="30px">
                                            <th>Center/Site</th>
                                            <th>Newly Imported Classes</th>
                                        </tr>
                                        </thead>
                                        <tbody style="background-color: #FAFBFC;color: #58666E;font-size: 14px;text-align:center;">
                                        @{newClassData}
                                        </tbody>
                                    </table>
                                    <div style="text-align:center;margin-top: 10px;">
                                        <a href="@webServer/#/login?type=ImportNewClass" target="_blank">
                                            <button style="color:#FFFFFF;border:1px solid #FFFFFF; background:#2AB4B8; height:47px;width: 150px; border-radius: 24px; cursor: pointer;">
                                                Go to Setting >
                                            </button>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div style="max-width: 700px;height: 280px;background: #00B3B7; margin: auto;">
            <table align="center" border="0" cellpadding="0" cellspacing="0" height="100%" width="100%" id="bodyTable" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;height: 100%;margin: 0;padding: 0;width: 100%;">
                <tbody>
                <tr>
                    <td align="center" valign="top" id="bodyCell" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;height: 100%;margin: 0;padding: 0;width: 100%;">
                        <table border="0" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                            <tbody>

                            <tr>
                                <td align="center" valign="top" id="templateFooter" data-template-container="" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-color: #00b3b7;background-image: none;background-repeat: no-repeat;background-position: center;background-size: cover;border-top: 0;border-bottom: 0;padding-top: 0px;padding-bottom: 0px;">
                                    <table align="center" border="0" cellpadding="0" cellspacing="0" width="100%" class="templateContainer" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;max-width: 600px !important;">
                                        <tbody>
                                        <tr>
                                            <td valign="top" class="footerContainer" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-color: transparent;background-image: none;background-repeat: no-repeat;background-position: center;background-size: cover;border-top: 0;border-bottom: 0;padding-top: 0;padding-bottom: 0;">
                                                <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnFollowBlock" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                    <tbody class="mcnFollowBlockOuter">
                                                    <tr>
                                                        <td align="center" valign="top" style="padding: 9px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;" class="mcnFollowBlockInner">
                                                            <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnFollowContentContainer" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                <tbody>
                                                                <tr>
                                                                    <td align="center" style="padding-left: 9px;padding-right: 9px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                        <table border="0" cellpadding="0" cellspacing="0" width="100%" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;" class="mcnFollowContent">
                                                                            <tbody>
                                                                            <tr>
                                                                                <td align="center" valign="top" style="padding-top: 9px;padding-right: 9px;padding-left: 9px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                                    <table align="center" border="0" cellpadding="0" cellspacing="0" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                                        <tbody>
                                                                                        <tr>
                                                                                            <td align="center" valign="top" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                                                <table align="left" border="0" cellpadding="0" cellspacing="0" style="display: inline;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                                                    <tbody>
                                                                                                    <tr>
                                                                                                        <td valign="top" style="padding-right: 10px;padding-bottom: 9px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;" class="mcnFollowContentItemContainer">
                                                                                                            <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnFollowContentItem" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                                                                <tbody>
                                                                                                                <tr>
                                                                                                                    <td align="left" valign="middle" style="padding-top: 5px;padding-right: 10px;padding-bottom: 5px;padding-left: 9px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                                                                        <table align="left" border="0" cellpadding="0" cellspacing="0" width="" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                                                                            <tbody>
                                                                                                                            <tr>
                                                                                                                                <td align="center" valign="middle" width="24" class="mcnFollowIconContent" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                                                                                    <a href="https://www.facebook.com/LearningGenie/" target="_blank" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;"><img src="https://cdn-images.mailchimp.com/icons/social-block-v2/outline-light-facebook-48.png" style="display: block;border: 0;height: auto;outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;" height="24" width="24" class=""></a>
                                                                                                                                </td>
                                                                                                                            </tr>
                                                                                                                            </tbody>
                                                                                                                        </table>
                                                                                                                    </td>
                                                                                                                </tr>
                                                                                                                </tbody>
                                                                                                            </table>
                                                                                                        </td>
                                                                                                    </tr>
                                                                                                    </tbody>
                                                                                                </table>
                                                                                                <table align="left" border="0" cellpadding="0" cellspacing="0" style="display: inline;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                                                    <tbody>
                                                                                                    <tr>
                                                                                                        <td valign="top" style="padding-right: 10px;padding-bottom: 9px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;" class="mcnFollowContentItemContainer">
                                                                                                            <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnFollowContentItem" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                                                                <tbody>
                                                                                                                <tr>
                                                                                                                    <td align="left" valign="middle" style="padding-top: 5px;padding-right: 10px;padding-bottom: 5px;padding-left: 9px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                                                                        <table align="left" border="0" cellpadding="0" cellspacing="0" width="" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                                                                            <tbody>
                                                                                                                            <tr>
                                                                                                                                <td align="center" valign="middle" width="24" class="mcnFollowIconContent" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                                                                                    <a href="https://twitter.com/learninggenie?lang=en" target="_blank" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;"><img src="https://cdn-images.mailchimp.com/icons/social-block-v2/outline-light-twitter-48.png" style="display: block;border: 0;height: auto;outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;" height="24" width="24" class=""></a>
                                                                                                                                </td>
                                                                                                                            </tr>
                                                                                                                            </tbody>
                                                                                                                        </table>
                                                                                                                    </td>
                                                                                                                </tr>
                                                                                                                </tbody>
                                                                                                            </table>
                                                                                                        </td>
                                                                                                    </tr>
                                                                                                    </tbody>
                                                                                                </table>
                                                                                                <table align="left" border="0" cellpadding="0" cellspacing="0" style="display: inline;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                                                    <tbody>
                                                                                                    <tr>
                                                                                                        <td valign="top" style="padding-right: 10px;padding-bottom: 9px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;" class="mcnFollowContentItemContainer">
                                                                                                            <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnFollowContentItem" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                                                                <tbody>
                                                                                                                <tr>
                                                                                                                    <td align="left" valign="middle" style="padding-top: 5px;padding-right: 10px;padding-bottom: 5px;padding-left: 9px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                                                                        <table align="left" border="0" cellpadding="0" cellspacing="0" width="" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                                                                            <tbody>
                                                                                                                            <tr>
                                                                                                                                <td align="center" valign="middle" width="24" class="mcnFollowIconContent" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                                                                                    <a href="https://www.linkedin.com/company/learning-genie-inc" target="_blank" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;"><img src="https://cdn-images.mailchimp.com/icons/social-block-v2/outline-light-linkedin-48.png" style="display: block;border: 0;height: auto;outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;" height="24" width="24" class=""></a>
                                                                                                                                </td>
                                                                                                                            </tr>
                                                                                                                            </tbody>
                                                                                                                        </table>
                                                                                                                    </td>
                                                                                                                </tr>
                                                                                                                </tbody>
                                                                                                            </table>
                                                                                                        </td>
                                                                                                    </tr>
                                                                                                    </tbody>
                                                                                                </table>
                                                                                                <table align="left" border="0" cellpadding="0" cellspacing="0" style="display: inline;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                                                    <tbody>
                                                                                                    <tr>
                                                                                                        <td valign="top" style="padding-right: 0;padding-bottom: 9px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;" class="mcnFollowContentItemContainer">
                                                                                                            <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnFollowContentItem" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                                                                <tbody>
                                                                                                                <tr>
                                                                                                                    <td align="left" valign="middle" style="padding-top: 5px;padding-right: 10px;padding-bottom: 5px;padding-left: 9px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                                                                        <table align="left" border="0" cellpadding="0" cellspacing="0" width="" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                                                                            <tbody>
                                                                                                                            <tr>
                                                                                                                                <td align="center" valign="middle" width="24" class="mcnFollowIconContent" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                                                                                    <a href="mailto:<EMAIL>" target="_blank" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;"><img src="https://cdn-images.mailchimp.com/icons/social-block-v2/outline-light-forwardtofriend-48.png" style="display: block;border: 0;height: auto;outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;" height="24" width="24" class=""></a>
                                                                                                                                </td>
                                                                                                                            </tr>
                                                                                                                            </tbody>
                                                                                                                        </table>
                                                                                                                    </td>
                                                                                                                </tr>
                                                                                                                </tbody>
                                                                                                            </table>
                                                                                                        </td>
                                                                                                    </tr>
                                                                                                    </tbody>
                                                                                                </table>
                                                                                            </td>
                                                                                        </tr>
                                                                                        </tbody>
                                                                                    </table>
                                                                                </td>
                                                                            </tr>
                                                                            </tbody>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                                </tbody>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                                <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnDividerBlock" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;table-layout: fixed !important;">
                                                    <tbody class="mcnDividerBlockOuter">
                                                    <tr>
                                                        <td class="mcnDividerBlockInner" style="min-width: 100%;padding: 18px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                            <table class="mcnDividerContent" border="0" cellpadding="0" cellspacing="0" width="100%" style="min-width: 100%;border-top: 5px dotted #FFFFFF;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                <tbody>
                                                                <tr>
                                                                    <td style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                        <span></span>
                                                                    </td>
                                                                </tr>
                                                                </tbody>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                                <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnTextBlock" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                    <tbody class="mcnTextBlockOuter">
                                                    <tr>
                                                        <td valign="top" class="mcnTextBlockInner" style="padding-top: 9px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">

                                                            <table align="left" border="0" cellpadding="0" cellspacing="0" style="max-width: 100%;min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;" width="100%" class="mcnTextContentContainer">
                                                                <tbody>
                                                                <tr>
                                                                    <td valign="top" class="mcnTextContent" style="padding-top: 0;padding-right: 18px;padding-bottom: 9px;padding-left: 18px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;word-break: break-word;color: #FFFFFF;font-family: 'Roboto', 'Helvetica Neue', Helvetica, Arial, sans-serif;font-size: 12px;line-height: 150%;text-align: center;">
        																							<span style="font-family:roboto,helvetica neue,helvetica,arial,sans-serif"><span style="font-size:14px"><strong>Learning
        																										Genie Inc</strong></span></span><br>
                                                                        +1 760-576-4822&nbsp; |&nbsp; <a href="mailto:<EMAIL>" target="_blank" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #ffffff;font-weight: normal;text-decoration: underline;"><EMAIL></a><br>
                                                                        5962&nbsp;La&nbsp;Place&nbsp;Court&nbsp;Suite&nbsp;270, Carlsbad,&nbsp;CA&nbsp;92008<br>
                                                                        <a href="https://www.learning-genie.com/" target="_blank" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #ffffff;font-weight: normal;text-decoration: underline;">https://www.learning-genie.com/</a><br>
                                                                        <br>
                                                                        You're receiving this email because you are a registered user of Learning Genie.<br>
                                                                        Want to change how you receive these emails?<br>
                                                                        <a href="*|UNSUB|*" target="_blank" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #ffffff;font-weight: normal;text-decoration: underline;">Unsubscribe from this list</a>
                                                                    </td>
                                                                </tr>
                                                                </tbody>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>

package com.learninggenie.api.controller;

import com.learninggenie.api.model.report.SwitchAutoGenerateActionPlanOpenRequest;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.service.ReportService;
import com.learninggenie.common.filesystem.FileSystem;
import com.learninggenie.common.score.DomainScoreService;
import org.junit.jupiter.api.Test;
import org.springframework.core.env.Environment;
import org.springframework.test.util.ReflectionTestUtils;

import static org.mockito.Mockito.*;

class ReportControllerTest {
    @Test
    void testSwitchAutoGenerateActionPlanOpen() {
        ReportController reportController = new ReportController();
        ReflectionTestUtils.setField(reportController, "userProvider", mock(UserProvider.class));
        ReportService reportService = mock(ReportService.class);
        doNothing().when(reportService).switchAutoGenerateActionPlanOpen((SwitchAutoGenerateActionPlanOpenRequest) any());
        ReflectionTestUtils.setField(reportController, "reportService", reportService);
        ReflectionTestUtils.setField(reportController, "fileSystem", mock(FileSystem.class));
        ReflectionTestUtils.setField(reportController, "env", mock(Environment.class));
        ReflectionTestUtils.setField(reportController, "domainScoreService", mock(DomainScoreService.class));

        SwitchAutoGenerateActionPlanOpenRequest switchAutoGenerateActionPlanOpenRequest = new SwitchAutoGenerateActionPlanOpenRequest();
        switchAutoGenerateActionPlanOpenRequest.setOpen(true);
        reportController.switchAutoGenerateActionPlanOpen(switchAutoGenerateActionPlanOpenRequest);
        verify(reportService).switchAutoGenerateActionPlanOpen((SwitchAutoGenerateActionPlanOpenRequest) any());
    }
}


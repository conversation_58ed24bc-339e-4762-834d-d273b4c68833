package com.learninggenie.api.controller;

import com.learninggenie.api.constant.RedisKeyPrefix;
import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.model.dashboard.DashboardFrameworkRequest;
import com.learninggenie.api.model.user.ExportAgencyUsersRequest;
import com.learninggenie.api.model.user.ExportAgencyUsersResponse;
import com.learninggenie.api.service.UserService;
import com.learninggenie.common.cache.RedisCacheServiceImpl;
import com.learninggenie.common.data.model.AgencyModel;
import com.learninggenie.common.utils.MSG;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.*;

/**
 * UserController 单元测试
 */
@ExtendWith(MockitoExtension.class)
public class UserControllerTest {

    /**
     * 导出机构用户请求密钥
     */
    private static final String EXPORT_AGENCY_USERS_KEY = "0c6fd318-0217-46d3-8663-46d4b3bafe1b";

    @InjectMocks
    private UserController userController;

    @Mock
    private UserService userService;

    @Mock
    private RedisCacheServiceImpl cacheService;

    /**
     * 测试获取机构下所有员工,包括机构所有者
     */
    @Test
    public void testStaffsIncludeAgencyOwner() {
        // 数据准备
        final List<AgencyModel> staffList = new ArrayList<>();
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("A00001");
        staffList.add(agencyModel);

        HttpHeaders headers = new HttpHeaders();
        headers.set("X-UID", "U00001");

        DashboardFrameworkRequest request = new DashboardFrameworkRequest();
        request.setCenterIds("A00002");

        // 接口模拟
        when(userService.getAllStaff(anyString(), anyString(), anyBoolean())).thenReturn(staffList);
        userController.staffsIncludeAgencyOwner(headers, request);

        // 结果校验
        verify(userService, times(1)).getAllStaff(anyString(), anyString(), anyBoolean());
        Assertions.assertEquals(1, staffList.size());
    }

    /**
     * 测试导出机构用户接口，同时导出被限制的情况
     */
    @Test
    public void testExportAgencyUsersLimitError() {
        // 模拟导出限制错误
        when(cacheService.exist(RedisKeyPrefix.EXPORT_AGENCY_USERS_LIMIT)).thenReturn(true);

        // 执行接口，会抛出异常
        BusinessException businessException = Assertions.assertThrows(BusinessException.class, () -> {
            // 请求信息
            ExportAgencyUsersRequest request = new ExportAgencyUsersRequest();
            request.setKey(EXPORT_AGENCY_USERS_KEY); // 请求密钥
            userController.exportAgencyUsers(request);
        });

        Assertions.assertEquals(MSG.t("IN_PROGESS"), businessException.getDetail());
    }

    /**
     * 测试导出机构用户接口，正常的情况
     */
    @Test
    public void testExportAgencyUsers() throws IOException {
        // 请求信息
        ExportAgencyUsersRequest request = new ExportAgencyUsersRequest();
        request.setKey(EXPORT_AGENCY_USERS_KEY); // 请求密钥

        // 模拟导出限制正常
        when(cacheService.exist(RedisKeyPrefix.EXPORT_AGENCY_USERS_LIMIT)).thenReturn(false);

        // 错误邮箱
        String errorEmail = "test-export-agency-users-error-email";
        // 模拟响应数据
        ExportAgencyUsersResponse response = new ExportAgencyUsersResponse();
        response.setExcelUrl("test-export-agency-users-excel-url"); // Excel URL
        response.setErrorEmails(Collections.singletonList(errorEmail)); // 错误邮箱
        // 模拟导出
        when(userService.exportAgencyUsers(request)).thenReturn(response);

        // 执行接口
        ExportAgencyUsersResponse result = userController.exportAgencyUsers(request);

        // 验证结果
        Assertions.assertEquals(response.getExcelUrl(), result.getExcelUrl()); // 验证 Excel URL
        Assertions.assertEquals(1, result.getErrorEmails().size()); // 验证错误邮箱数量
        Assertions.assertEquals(errorEmail, result.getErrorEmails().get(0)); // 验证错误邮箱
    }

}

package com.learninggenie.api.controller;

import com.learninggenie.api.model.InkindResponse;
import com.learninggenie.api.model.SuccessResponse;
import com.learninggenie.api.model.inkind.*;
import com.learninggenie.api.service.inkind.InKindAssignmentService;
import com.learninggenie.api.service.inkind.InKindRatifyService;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * InkindController 单元测试
 */
@ExtendWith(MockitoExtension.class)
public class InkindControllerTest {

    private static InkindController inkindController;

    private static InKindRatifyService inKindRatifyService;

    private static InKindAssignmentService inKindAssignmentService;

    /**
     * 初始化 inkindController 的依赖
     */
    @BeforeClass
    public static void beforeClass() {
        inkindController = new InkindController();
        inKindRatifyService = mock(InKindRatifyService.class);
        inKindAssignmentService = mock(InKindAssignmentService.class);
        ReflectionTestUtils.setField(inkindController, "inKindRatifyService", inKindRatifyService);
        ReflectionTestUtils.setField(inkindController, "inKindAssignmentService", inKindAssignmentService);

    }


    /**
     * 测试获取审核模式
     */
    @Test
    public void testGetSetting() {
        // 数据准备
        String ratifiedMode = "1";
        final InKindSettingResponse inKindSettingResponse = new InKindSettingResponse();
        inKindSettingResponse.setRatifiedMode(ratifiedMode);

        // 接口模拟
        when(inKindRatifyService.getSetting()).thenReturn(inKindSettingResponse);

        InKindSettingResponse setting = inkindController.getSetting();

        // 结果校验
        verify(inKindRatifyService, times(1)).getSetting();
        Assertions.assertEquals(ratifiedMode, setting.getRatifiedMode());
    }

    /**
     * 测试设置审核模式
     */
    @Test
    public void testSetSetting() {
        // 数据准备
        final SuccessResponse successResponse = new SuccessResponse();
        successResponse.setSuccess(true);

        // 接口模拟
        when(inKindRatifyService.setSetting(any())).thenReturn(successResponse);

        inkindController.setSetting(any());

        // 结果校验
        verify(inKindRatifyService, times(1)).setSetting(any());
        Assertions.assertTrue(successResponse.isSuccess());
    }

    /**
     * 测试获取待批准的学校班级列表
     */
    @Test
    public void testGetFilter() {
        // 数据准备
        final GetFilterResponse getFilterResponse = new GetFilterResponse();

        // 接口模拟
        when(inKindRatifyService.getFilter(any())).thenReturn(getFilterResponse);

        inkindController.getFilter(any());

        // 结果校验
        verify(inKindRatifyService, times(1)).getFilter(any());
    }

    /**
     * 测试获取 In-Kind 批准列表
     */
    @Test
    public void testGetReviewList() {
        // 数据准备
        final GetReviewListResponse getReviewListResponse = new GetReviewListResponse();

        // 接口模拟
        when(inKindRatifyService.getReviewList(any())).thenReturn(getReviewListResponse);

        inkindController.getReviewList(any());

        // 结果校验
        verify(inKindRatifyService, times(1)).getReviewList(any());
    }

    /**
     * 测试恢复到待审核
     */
    @Test
    public void testRestoreSignature() {
        // 数据准备
        final SuccessResponse successResponse = new SuccessResponse();

        // 接口模拟
        when(inKindRatifyService.restoreSignature(any())).thenReturn(successResponse);

        inkindController.restoreSignature(any());

        // 结果校验
        verify(inKindRatifyService, times(1)).restoreSignature(any());
    }

    /**
     * 测试获取老师提交报告被驳回列表
     */
    @Test
    public void testGetReviseList() {
        // 数据准备
        final GetReviseListResponse getReviseListResponse = new GetReviseListResponse();

        // 接口模拟
        when(inKindRatifyService.getReviseList()).thenReturn(getReviseListResponse);

        inkindController.getReviseList();

        // 结果校验
        verify(inKindRatifyService, times(1)).getReviseList();
    }

    /**
     * 测试批准报告列表
     */
    @Test
    public void testRatify() {
        // 数据准备
        final InkindResponse inkindResponse = new InkindResponse();

        // 接口模拟
        when(inKindRatifyService.ratify(any())).thenReturn(inkindResponse);

        inkindController.ratify(any());

        // 结果校验
        verify(inKindRatifyService, times(1)).ratify(any());
    }

    /**
     * 测试获取 In-Kind 批准待签名列表
     */
    @Test
    public void testGetNeedSignatureList() {
        // 数据准备
        final GetNeedSignListResponse getNeedSignListResponse = new GetNeedSignListResponse();

        // 接口模拟
        when(inKindRatifyService.getNeedSignatureList()).thenReturn(getNeedSignListResponse);

        inkindController.getNeedSignatureList();

        // 结果校验
        verify(inKindRatifyService, times(1)).getNeedSignatureList();
    }

    /**
     * 测试签名提交
     */
    @Test
    public void testSignature() {
        // 数据准备
        final InkindResponse inkindResponse = new InkindResponse();

        // 接口模拟
        when(inKindRatifyService.signature(any())).thenReturn(inkindResponse);

        inkindController.signature(any());

        // 结果校验
        verify(inKindRatifyService, times(1)).signature(any());
    }

    /**
     * 测试重置提交
     */
    @Test
    public void testResubmit() {
        // 数据准备
        final SuccessResponse successResponse = new SuccessResponse();

        // 接口模拟
        when(inKindRatifyService.resubmit(any())).thenReturn(successResponse);

        inkindController.resubmit(any());

        // 结果校验
        verify(inKindRatifyService, times(1)).resubmit(any());
    }

    /**
     * 测试获取待处理旧数据
     */
    @Test
    public void testGetOldInKind() {
        // 数据准备
        final GetOldInKindResponse getOldInKindResponse = new GetOldInKindResponse();

        // 接口模拟
        when(inKindRatifyService.getOldInKind()).thenReturn(getOldInKindResponse);

        inkindController.getOldInKind();

        // 结果校验
        verify(inKindRatifyService, times(1)).getOldInKind();
    }

    /**
     * 测试获取可通知员工
     */
    @Test
    public void testGetStaff() {
        // 数据准备
        final GetStaffResponse getStaffResponse = new GetStaffResponse();

        // 接口模拟
        when(inKindRatifyService.getStaff(any())).thenReturn(getStaffResponse);

        inkindController.getStaff(any());

        // 结果校验
        verify(inKindRatifyService, times(1)).getStaff(any());
    }


    /**
     * 测试 机构获取重复活动次数开关
     */
    @Test
    public void testGetAssignmentRepeatOpen() {
        // 数据准备
        final AssignmentRepeatResponse response = new AssignmentRepeatResponse();

        // 接口模拟
        when(inKindAssignmentService.getAssignmentRepeatOpen(true)).thenReturn(response);

        inkindController.getAssignmentRepeatOpen();

        // 结果校验
        verify(inKindAssignmentService, times(1)).getAssignmentRepeatOpen(true);
    }

    /**
     * 测试 机构修改获取重复活动次数开关
     */
    @Test
    public void testUpdateAssignmentRepeatOpen() {
        /*// 数据准备


        // 接口模拟
        when(inkindService.updateAssignmentRepeatOpen(any())).thenReturn(null);

        inkindService.updateAssignmentRepeatOpen(any());

        // 结果校验
        verify(inkindService, times(1)).updateAssignmentRepeatOpen(any());*/
    }
}

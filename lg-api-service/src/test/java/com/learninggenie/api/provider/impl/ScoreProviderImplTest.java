package com.learninggenie.api.provider.impl;


import com.learninggenie.api.model.PortfolioStatistics;
import com.learninggenie.api.model.PortfolioStatisticsRequest;
import com.learninggenie.api.model.ScoreStatisticsViewModel;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.service.GroupService;
import com.learninggenie.api.service.PortfolioService;
import com.learninggenie.common.data.dao.*;
import com.learninggenie.common.data.entity.*;
import com.learninggenie.common.data.model.ChildEntity;
import com.learninggenie.common.data.model.ScoreTemplateEntity;
import com.learninggenie.common.data.model.StudentScoreEntity;
import com.learninggenie.common.data.repository.GroupRepository;
import com.learninggenie.common.score.RatingService;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.core.env.Environment;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@RunWith(MockitoJUnitRunner.class)
public class ScoreProviderImplTest {
    @InjectMocks
    private ScoreProviderImpl scoreProvider;
    @Mock
    private DomainDao domainDao;
    @Mock
    private GroupDao groupDao;
    @Mock
    private PortfolioDao portfolioDao;
    @Mock
    private ScoreDao scoreDao;
    @Mock
    private NoteDao noteDao;
    @Mock
    private GroupRepository groupRepository;
    @Mock
    private Environment env;
    @Mock
    private GroupService groupService;
    @Mock
    private StudentDao studentDao;
    @Mock
    private UserProvider userProvider;
    @Mock
    private RatingService ratingService;
    @Mock
    private PortfolioService portfolioService;



    private SimpleDateFormat format = new SimpleDateFormat("MM/dd/yyyy");

    @Ignore
    @Test
    public void testGetScoreStatisticsViewModel() throws Exception {
        List<String> groupIdList = new ArrayList<>();
        groupIdList.add("d5123cb9-6dbd-e411-af66-02c72b94b99b");
        String portfolioId = "E163164F-BDCE-E411-AF66-02C72B94B99B";
        String alias = "2016-2017 Fall";
        com.learninggenie.common.data.entity.GroupEntity groupEntity = new com.learninggenie.common.data.entity.GroupEntity();
        groupEntity.setId("d5123cb9-6dbd-e411-af66-02c72b94b99b");
        try {
            groupEntity.setCreateAtUtc(format.parse("11/11/1111"));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setCenterTimeZone("");
        groupEntity.setCenter(centerEntity);
        GroupPeriodEntity groupPeriodEntity = new GroupPeriodEntity();
        groupEntity.setCurrentPeriod(groupPeriodEntity);
        com.learninggenie.common.data.entity.DomainEntity domainEntity = new com.learninggenie.common.data.entity.DomainEntity();
        groupEntity.setDomain(domainEntity);
        Set<EnrollmentEntity> enrollmentEntitySet = new HashSet<>();
        groupEntity.setEnrollments(enrollmentEntitySet);
        Set<GroupInvitationEntity> groupInvitationEntitySet = new HashSet<>();
        groupEntity.setGroupInvitationEntities(groupInvitationEntitySet);
        Set<GroupMetaDataEntity> groupMetaDataEntitySet = new HashSet<>();
        groupEntity.setGroupMetaDataEntities(groupMetaDataEntitySet);
        groupEntity.setIconPath("");
        groupEntity.setIsDeleted(false);
        groupEntity.setName("123");
        Set<GroupPeriodEntity> groupPeriodEntitySet = new HashSet<>();
        groupEntity.setPeriods(groupPeriodEntitySet);
        GroupStageEntity groupStageEntity = new GroupStageEntity();
        groupEntity.setStage(groupStageEntity);
        Set<UserEntity> userEntitySet = new HashSet<>();
        groupEntity.setTeachers(userEntitySet);
        //通过班级获取该学校的时区
        Mockito.when(groupRepository.findById(Mockito.anyString()).orElse(null)).thenReturn(groupEntity);
        //根据班级获取所有学生
        List<ChildEntity> children = new ArrayList<>();
        ChildEntity childEntity = new ChildEntity();
        childEntity.setName("123");
        childEntity.setCreated("");
        childEntity.setId("123");
        children.add(childEntity);
        children.add(childEntity);
        Mockito.when(groupDao.getChildrenByGroups(Mockito.anyString())).thenReturn(children);
        //根据portfolio获取所有的domains
        List<com.learninggenie.common.data.model.DomainEntity> domains = new ArrayList<>();
        com.learninggenie.common.data.model.DomainEntity domainEntity1 = new com.learninggenie.common.data.model.DomainEntity();
        domainEntity1.setId("456");
        domains.add(domainEntity1);
        Mockito.when(domainDao.getAllChildDomains(Mockito.anyString())).thenReturn(domains);
        //获取所有指定班级的notes
        List<com.learninggenie.common.data.model.NoteEntity> allNotes = new ArrayList<>();
        com.learninggenie.common.data.model.NoteEntity noteEntity = new com.learninggenie.common.data.model.NoteEntity();
        noteEntity.setEnrollmentId("123");
        noteEntity.setDomainId("456");
        noteEntity.setId("789");
        allNotes.add(noteEntity);
        Mockito.when(noteDao.getGroupsNotesByDate(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(allNotes);
        //获取所有指定班级的评分记录
        List<StudentScoreEntity> allScores = new ArrayList<>();
        StudentScoreEntity studentScoreEntity = new StudentScoreEntity();
        studentScoreEntity.setStudentId("123");
        studentScoreEntity.setNoteId("789");
        studentScoreEntity.setDomainId("456");
        studentScoreEntity.setLevelId("C16B22DF-09A1-4657-8ED4-509E3310C781");
        allScores.add(studentScoreEntity);
        Mockito.when(scoreDao.getByGroups(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(allScores);
        Object[] obj = new Object[1];
        obj[0] = 10;
        Mockito.when(groupRepository.getNotePortfoliosCount(Mockito.anyList(), Mockito.anyString(), Mockito.anyString())).thenReturn(obj);

        com.learninggenie.common.data.model.DomainEntity domainEntity2 = new com.learninggenie.common.data.model.DomainEntity();
        Mockito.when(domainDao.getDomain(Mockito.anyString())).thenReturn(domainEntity2);
        ScoreTemplateEntity scoreTemplateEntry = new ScoreTemplateEntity();
        scoreTemplateEntry.setDomainLevelsJson("");
        scoreTemplateEntry.setLevelsJson("[{\"id\":\"C16B22DF-09A1-4657-8ED4-509E3310C781\",\"name\":\"Emerging\",\"type\":\"radio\",\"sortIndex\":\"\",\"value\":\"1\"},{\"id\":\"7D80B5EA-5BA6-48A3-814E-A953845B71D1\",\"name\":\"Exploring\",\"type\":\"radio\",\"sortIndex\":\"\",\"value\":\"2\"},{\"id\":\"C5F7C569-3ABB-42F4-B208-DD278F8911ED\",\"name\":\"Extending\",\"type\":\"radio\",\"sortIndex\":\"\",\"value\":\"3\"}]");
        scoreTemplateEntry.setPortfolioId(portfolioId);
        Mockito.when(portfolioDao.loadScoreTemplate(Mockito.anyString())).thenReturn(scoreTemplateEntry);

        List<com.learninggenie.common.data.model.DomainEntity> domainList = new ArrayList<>();
        List<com.learninggenie.common.data.model.NoteEntity> noteList = new ArrayList<>();
        List<StudentScoreEntity> studentScoreEntityList = new ArrayList<>();
        Mockito.when(groupDao.getDomainId(Mockito.anyString())).thenReturn("123");
        Mockito.when(domainDao.getAllChildDomains(Mockito.anyString())).thenReturn(domainList);
        List<com.learninggenie.common.data.model.NoteEntity> notes = new ArrayList<>();
        Mockito.when(noteDao.getDomainNotes(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(notes);
        List<StudentScoreEntity> scores = new ArrayList<>();
        Mockito.when(scoreDao.get(Mockito.anyString())).thenReturn(scores);
        com.learninggenie.common.data.model.NoteEntity noteEntity111 = new com.learninggenie.common.data.model.NoteEntity();
        com.learninggenie.common.data.model.DomainEntity domainEntity111 = new com.learninggenie.common.data.model.DomainEntity();
        StudentScoreEntity studentScoreEntity111 = new StudentScoreEntity();
        studentScoreEntity111.setNoteId("789");
        studentScoreEntity111.setDomainId("456");
        studentScoreEntity111.setStudentId("123");
        studentScoreEntityList.add(studentScoreEntity111);
        domainEntity111.setId("456");
        domainList.add(domainEntity111);
        noteEntity111.setEnrollmentId("123");
        noteEntity111.setDomainId("456");
        noteEntity111.setId("789");
        noteList.add(noteEntity111);

        PortfolioStatisticsRequest request = new PortfolioStatisticsRequest();
        request.setPortfolioId(portfolioId);
        request.setAlias(alias);
        request.setGroupIds("d5123cb9-6dbd-e411-af66-02c72b94b99b");
        request.setAllGroupIds("d5123cb9-6dbd-e411-af66-02c72b94b99b");
        PortfolioStatistics portfolioStatistics = scoreProvider.getPortfolioStatistics(request,"");
        Assert.assertTrue(portfolioStatistics != null);
        Assert.assertEquals("d5123cb9-6dbd-e411-af66-02c72b94b99b", portfolioStatistics.getGroupIds());
        Assert.assertEquals("2", portfolioStatistics.getChildrenCount() + "");
        Assert.assertEquals("10", portfolioStatistics.getNoteCount() + "");
        Assert.assertEquals("2", portfolioStatistics.getMeasureCount() + "");
        // Assert.assertEquals("",portfolioStatistics.get);
        //多语言补加测试
        UserEntity userEntity = new UserEntity();
        Mockito.when(userProvider.getCurrentUserId()).thenReturn(Mockito.anyString());
        Mockito.when(userProvider.checkUser(Mockito.anyString())).thenReturn(userEntity);
        org.json.JSONObject jsonObject = new org.json.JSONObject();
        Mockito.when(ratingService.getLanguageJson(Mockito.anyString(), Mockito.anyString())).thenReturn(jsonObject);

    }

    @Test
    @Ignore
    public void testGetPortfolioStatistics() throws Exception {
        List<com.learninggenie.common.data.model.DomainEntity> domainList = new ArrayList<>();
        List<com.learninggenie.common.data.model.NoteEntity> noteList = new ArrayList<>();
        List<StudentScoreEntity> studentScoreEntityList = new ArrayList<>();
        Mockito.when(groupDao.getDomainId(Mockito.anyString())).thenReturn("123");
        Mockito.when(domainDao.getAllChildDomains(Mockito.anyString())).thenReturn(domainList);
        List<com.learninggenie.common.data.model.NoteEntity> notes = new ArrayList<>();
        Mockito.when(noteDao.getDomainNotes(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(notes);
        List<StudentScoreEntity> scores = new ArrayList<>();
        Mockito.when(scoreDao.get(Mockito.anyString())).thenReturn(scores);
        String childId = "123";
        String start = "";
        String end = "";
        String portfolioId = "11111";
        com.learninggenie.common.data.model.NoteEntity noteEntity = new com.learninggenie.common.data.model.NoteEntity();
        com.learninggenie.common.data.model.DomainEntity domainEntity = new com.learninggenie.common.data.model.DomainEntity();
        StudentScoreEntity studentScoreEntity = new StudentScoreEntity();
        studentScoreEntity.setNoteId("789");
        studentScoreEntity.setDomainId("456");
        studentScoreEntity.setStudentId("123");
        studentScoreEntityList.add(studentScoreEntity);
        domainEntity.setId("456");
        domainList.add(domainEntity);
        noteEntity.setEnrollmentId("123");
        noteEntity.setDomainId("456");
        noteEntity.setId("789");
        noteList.add(noteEntity);
        ScoreStatisticsViewModel scoreStatisticsViewModel = scoreProvider.getScoreStatisticsViewModel(childId, start, end, portfolioId, domainList, noteList, studentScoreEntityList);
        Assert.assertTrue(scoreStatisticsViewModel != null);
        Assert.assertEquals("123", scoreStatisticsViewModel.getStudentId() + "");
        Assert.assertEquals("1", scoreStatisticsViewModel.getObservedMeasure().getRatedMeasures().size() + "");
    }

}
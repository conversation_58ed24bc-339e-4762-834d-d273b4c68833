package com.learninggenie.api.provider.impl;

import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.dao.prompts.PromptDao;
import com.learninggenie.common.data.dao.prompts.PromptTestRecordDao;
import com.learninggenie.common.data.dao.prompts.PromptUsageObjectDao;
import com.learninggenie.common.data.dao.prompts.PromptUsageRecordDao;
import com.learninggenie.common.data.entity.UserMetaDataEntity;
import com.learninggenie.common.data.entity.prompt.PromptEntity;
import com.learninggenie.common.data.entity.prompt.PromptTestRecordEntity;
import com.learninggenie.common.data.entity.prompt.PromptUsageObjectEntity;
import com.learninggenie.common.data.entity.prompt.PromptUsageRecordEntity;
import com.learninggenie.common.data.enums.prompt.PromptScene;
import com.learninggenie.common.data.enums.prompt.UseObject;
import com.learninggenie.common.data.enums.prompt.UseType;
import com.learninggenie.common.data.model.openai.Usage;
import com.learninggenie.common.data.model.openai.completion.chat.ChatCompletionResult;
import com.learninggenie.common.score.RatingService;
import com.theokanning.openai.completion.chat.ChatCompletionChoice;
import com.theokanning.openai.completion.chat.ChatMessage;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;


@RunWith(MockitoJUnitRunner.Silent.class)
public class PromptProviderImplTest {

    @InjectMocks
    private PromptProviderImpl promptProviderImpl;


    @Mock
    private PromptDao promptDao;


    @Mock
    private PromptUsageObjectDao promptUsageObjectDao;

    @Mock
    private UserDaoImpl userDao;


    @Mock
    private UserProvider userProvider;

    @Mock
    private PromptUsageRecordDao promptUsageRecordDao;

    @Mock
    private PromptTestRecordDao promptTestRecordDao;

    @Mock
    private RatingService ratingService;

    @Test
    public void mockCreatePromptUsageRecord() {
        // 获取使用记录
        String promptId = UUID.randomUUID().toString();
        // 模拟当前版本 Prompt
        PromptEntity promptEntity = new PromptEntity();
        promptEntity.setId(promptId);
        promptEntity.setPromptTemplate("prompt");
        promptEntity.setEvaluatePromptTemplate("evaluatePrompt");

        ChatCompletionResult completionResult = new ChatCompletionResult();
        List<ChatCompletionChoice> choices = new ArrayList<>();
        ChatCompletionChoice choice = new ChatCompletionChoice();
        ChatMessage chatMessage = new ChatMessage();
        chatMessage.setContent("score: 100");
        choice.setMessage(chatMessage);
        choices.add(choice);
        completionResult.setChoices(choices);
        Usage usage = new Usage();
        usage.setCost(0.1);
        usage.setDuration(0.1);
        usage.setPromptTokens(10L);
        usage.setCompletionTokens(10L);
        completionResult.setUsage(usage);

        String userId = UUID.randomUUID().toString();
        when(userProvider.getCurrentUserId()).thenReturn(userId);

        // 调用方法
        PromptUsageRecordEntity prompt = promptProviderImpl.createPromptUsageRecord("prompt", completionResult, promptEntity, null, UseType.EVALUATE, null);
        // 验证
        Assert.assertNotNull(prompt);
        // 验证数据
        Assert.assertEquals(prompt.getType(), UseType.EVALUATE.toString());
        Assert.assertEquals("prompt",prompt.getPrompt());

    }

    /**
     * 测试创建 Prompt 使用对象记录
     */
    @Test
    public void mockCreatePromptUsageObject() {
        // 模拟入参
        String promptUsageId = UUID.randomUUID().toString();
        String objectId = UUID.randomUUID().toString();
        // 调用方法
        PromptUsageObjectEntity promptUsageObject = promptProviderImpl.createPromptUsageObject(promptUsageId, UseObject.LESSON, objectId);
        // 验证
        Assert.assertNotNull(promptUsageObject);
        // 验证数据
        Assert.assertEquals(promptUsageObject.getUseObjectId(), objectId);
    }

    /**
     * 测试创建 Prompt 使用对象记录
     */
    @Test
    public void mockCreatePromptUsageObject2() {
        // 获取使用记录
        String useObjectId = UUID.randomUUID().toString();
        String Id = UUID.randomUUID().toString();
        List<PromptUsageObjectEntity> promptUsageObjectEntities = new ArrayList<>();
        PromptUsageObjectEntity promptUsageObjectEntity = new PromptUsageObjectEntity();
        promptUsageObjectEntity.setUseObjectId(useObjectId);
        promptUsageObjectEntity.setId(Id);
        promptUsageObjectEntity.setUsageRecordId(Id);
        promptUsageObjectEntities.add(promptUsageObjectEntity);
        when(promptUsageObjectDao.listByPromptUsageId(anyString())).thenReturn(promptUsageObjectEntities);
        // 入参
        List<String> objectIds = new ArrayList<>();
        objectIds.add(useObjectId);
        // 调用方法
        List<PromptUsageObjectEntity> promptUsageObject = promptProviderImpl.createPromptUsageObject(Id, UseObject.LESSON, objectIds);
        // 验证
        Assert.assertNotNull(promptUsageObject);
        // 验证数据
        Assert.assertEquals(promptUsageObject.get(0).getUseObjectId(), useObjectId);
        Assert.assertEquals(promptUsageObject.get(0).getId(), Id);
        Assert.assertEquals(promptUsageObject.get(0).getUsageRecordId(), Id);
    }

    /**
     * 测试创建 Prompt 使用对象记录
     */
    @Test
    public void mockCreatePromptUsageObject3() {
        // 获取使用记录
        String useObjectId = UUID.randomUUID().toString();
        String Id = UUID.randomUUID().toString();
        List<PromptUsageObjectEntity> promptUsageObjectEntities = new ArrayList<>();
        PromptUsageObjectEntity promptUsageObjectEntity = new PromptUsageObjectEntity();
        promptUsageObjectEntity.setUseObjectId(useObjectId);
        promptUsageObjectEntity.setId(Id);
        promptUsageObjectEntity.setUsageRecordId(Id);
        promptUsageObjectEntities.add(promptUsageObjectEntity);
        when(promptUsageObjectDao.listByPromptUsageId(anyString())).thenReturn(promptUsageObjectEntities);
        // 入参
        List<String> objectIds = new ArrayList<>();
        objectIds.add(Id);
        // 调用方法
        List<PromptUsageObjectEntity> promptUsageObject = promptProviderImpl.createPromptUsageObject(Id, UseObject.LESSON, objectIds);
        // 验证
        Assert.assertNotNull(promptUsageObject);
        // 验证数据
        Assert.assertEquals("LESSON", promptUsageObject.get(0).getUseObject());
        Assert.assertTrue(promptUsageObject.get(0).getActive());
    }

    /**
     * 测试根据场景获取默认 Prompt
     */
    @Test
    public void mockGetActivePromptByScene() {
        // 用户 ID
        String userId = UUID.randomUUID().toString();
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        // 用户默认 Prompt
        UserMetaDataEntity metaData = new UserMetaDataEntity();
        String promptId = UUID.randomUUID().toString();
        metaData.setMetaValue(promptId);
        when(userDao.getMetaData(anyString(), anyString())).thenReturn(metaData);
        // 调用方法
        promptProviderImpl.getActivePromptByScene("scene");
        // 验证
        verify(promptDao, times(1)).getById(anyString());
    }

    /**
     * 测试根据场景获取默认 Prompt
     */
    @Test
    public void mockGetActivePromptByScene2() {
        // 用户 ID
        String userId = UUID.randomUUID().toString();
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        // 用户默认 Prompt
        when(userDao.getMetaData(anyString(), anyString())).thenReturn(null);
        // 调用方法
        promptProviderImpl.getActivePromptByScene("scene");
        // 验证
        verify(promptDao, times(1)).getActivePromptByScene(anyString());
    }

    /**
     * 测试批量创建测试的 Prompt 使用记录
     */
    @Test
    public void mockCreatePromptTestUsageRecords() {
        // 当前用户 ID
        String userId = UUID.randomUUID().toString();
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        // 模拟入参
        String prompt = "prompt";
        PromptTestRecordEntity testRecord = new PromptTestRecordEntity();
        String promptId = UUID.randomUUID().toString();
        String testRecordId = UUID.randomUUID().toString();
        int testCount = 3;
        testRecord.setPromptId(promptId);
        testRecord.setId(testRecordId);
        testRecord.setTestCount(testCount);
        String additionalData = "additionalData";
        // 调用方法
        promptProviderImpl.createPromptTestUsageRecords(prompt, testRecord, additionalData);
        // 验证
        verify(promptUsageRecordDao, times(1)).saveBatch(anyList());
    }

    /**
     * 测试更新 Prompt 使用记录
     */
    @Test
    public void mockUpdatePromptUsageRecord() {
        // 模拟入参
        PromptUsageRecordEntity usageRecordEntity = new PromptUsageRecordEntity();
        String id = UUID.randomUUID().toString();
        usageRecordEntity.setId(id);
        String prompt = "prompt";
        ChatCompletionResult completionResult = new ChatCompletionResult();
        Usage usage = new Usage();
        usage.setDuration(10.00);
        usage.setCost(10.00);
        usage.setPromptTokens(1000L);
        usage.setCompletionTokens(1000L);
        completionResult.setUsage(usage);
        // 调用方法
        PromptUsageRecordEntity recordEntity = promptProviderImpl.updatePromptUsageRecord(usageRecordEntity, prompt, completionResult);
        // 验证
        Assert.assertNotNull(recordEntity);
        // 验证数据
        Assert.assertEquals(recordEntity.getId(), id);
        Assert.assertEquals(recordEntity.getExecuteDuration(), usage.getDuration());
        Assert.assertEquals(recordEntity.getExecuteCost(), usage.getCost());
    }

    /**
     * 测试创建 Prompt 测试记录
     */
    @Test
    public void mockCreatePromptTestRecord() {
        // 模拟入参
        String promptId = UUID.randomUUID().toString();
        int testCount = 3;
        String additionalData = "additionalData";
        // 调用方法
        PromptTestRecordEntity testRecordEntity = promptProviderImpl.createPromptTestRecord(promptId, testCount, additionalData);
        // 验证
        Assert.assertNotNull(testRecordEntity);
        // 验证数据
        Assert.assertEquals(testRecordEntity.getPromptId(), promptId);
        Assert.assertEquals(testRecordEntity.getTestCount().intValue(), testCount);
        Assert.assertEquals("PENDING", testRecordEntity.getStatus());
    }

    /**
     * 测试根据框架获取 Prompt 场景枚举
     */
    @Test
    public void testGetPromptSceneByFramework() {
        // 框架为空的情况
        promptProviderImpl.getPromptSceneByFramework(null, null, null);
        // 不会执行判断框架的方法
        verify(ratingService, never()).isITFramework(any());

        // 框架为 IT 的情况
        when(ratingService.isITFramework(anyString())).thenReturn(true);
        PromptScene promptScene = promptProviderImpl.getPromptSceneByFramework(PromptScene.PLAN_OVERVIEW, "it-id", null);
        // 验证结果为 IT 场景
        Assert.assertEquals(PromptScene.PLAN_OVERVIEW_FOR_IT, promptScene);
        // 清除测试数据
        Mockito.clearInvocations(ratingService);

        // 年龄段为 0-3 的情况
        promptScene = promptProviderImpl.getPromptSceneByFramework(PromptScene.PLAN_OVERVIEW, null, "Toddler (1-3)");
        // 验证结果为 IT 场景
        Assert.assertEquals(PromptScene.PLAN_OVERVIEW_FOR_IT, promptScene);
        // 清除测试数据
        Mockito.clearInvocations(ratingService);

        // 不属于 IT，也不是 0-3 的情况
        when(ratingService.isITFramework(any())).thenReturn(false);
        promptScene = promptProviderImpl.getPromptSceneByFramework(PromptScene.PLAN_OVERVIEW, "it2-id", null);
        // 验证 Prompt 场景不变
        Assert.assertEquals(PromptScene.PLAN_OVERVIEW, promptScene);
    }
}

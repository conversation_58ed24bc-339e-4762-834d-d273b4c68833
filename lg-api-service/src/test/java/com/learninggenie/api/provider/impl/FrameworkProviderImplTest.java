package com.learninggenie.api.provider.impl;

import com.learninggenie.common.data.model.DomainEntity;
import com.learninggenie.common.framwork.FrameworkProviderImpl;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

@RunWith(MockitoJUnitRunner.Silent.class)
public class FrameworkProviderImplTest {

    @InjectMocks
    private FrameworkProviderImpl frameworkProvider;

    /**
     * 测试过滤测评点中的非 IEP 测评点
     */
    @Test
    public void testFilterNonIEPMeasures() {
        String frameworkId = "test framework id";
        List<DomainEntity> domainEntities = new ArrayList<>();

        // 参数为空的情况
        List<DomainEntity> results = frameworkProvider.filterNonIEPMeasures(null, domainEntities);
        assertEquals(0, results.size());

        // ITE 框架的情况
        DomainEntity domain1 = new DomainEntity();
        domainEntities.add(domain1);
        DomainEntity domain2 = new DomainEntity();
        domainEntities.add(domain2);
        // 子测评点列表
        List<DomainEntity> measures = new ArrayList<>();
        domain2.setNodes(measures);
        DomainEntity measure1 = new DomainEntity();
        measures.add(measure1);
        // 设置框架 ID
        ReflectionTestUtils.setField(frameworkProvider, "idPSE2015", frameworkId);
        ReflectionTestUtils.setField(frameworkProvider, "idITE2015", frameworkId);
        // 执行方法
        results = frameworkProvider.filterNonIEPMeasures(frameworkId, domainEntities);
        // 验证结果
        assertEquals(2, results.size());
    }

    /**
     * 测试当传入的 domains 为空时，返回空 Map
     */
    @Test
    public void testConvertMappingAbbrToDomainAbbr_DomainsEmpty() {
        // 调用方法并验证结果
        assertTrue(frameworkProvider.convertMappingAbbrToDomainAbbr(Collections.emptyList()).isEmpty());
    }

    /**
     * 测试当传入的 domains 中没有需要转换的域时，返回域的自身映射
     */
    @Test
    public void testConvertMappingAbbrToDomainAbbr_NoNeedConvertDomains() {
        // 构造不需要转换的域数据
        List<DomainEntity> domains = Arrays.asList(
                new DomainEntity(){{
                    setId("1");
                    setAbbreviation("D1");
                    setNodes(new ArrayList<>());
                    setMappingAbbr("D1");
                }},
                new DomainEntity(){{
                    setId("2");
                    setAbbreviation("D2");
                    setNodes(new ArrayList<>());
                    setMappingAbbr("D2");
                }}
        );

        // 调用方法并验证结果
        Map<String, String> result = frameworkProvider.convertMappingAbbrToDomainAbbr(domains);
        assertEquals(2, result.size());
        assertEquals("D1", result.get("D1"));
        assertEquals("D2", result.get("D2"));
    }

    /**
     * 测试当传入的 domains 中有需要转换的域时，返回正确的映射
     */
    @Test
    public void testConvertMappingAbbrToDomainAbbr_NeedConvertDomains() {
        // 构造需要转换的域数据
        List<DomainEntity> domains = Arrays.asList(
                new DomainEntity(){{
                    setId("1");
                    setAbbreviation("D1");
                    setMappingAbbr("M1");
                }},
                new DomainEntity(){{
                    setId("2");
                    setAbbreviation("D2");
                    setMappingAbbr("M2");
                }}
        );

        // 调用方法并验证结果
        Map<String, String> result = frameworkProvider.convertMappingAbbrToDomainAbbr(domains);
        assertEquals(2, result.size());
        assertEquals("D1", result.get("M1"));
        assertEquals("D2", result.get("M2"));
    }

    /**
     * 测试 getMappingAbbrToDomainAbbr 方法
     */
    @Test
    public void testGetMappingAbbrToDomainAbbr() {
        // 构造测评点数据
        List<DomainEntity> measures = Arrays.asList(
                new DomainEntity(){{
                    setId("1");
                    setAbbreviation("D1");
                    setMappingAbbr("M1");
                }},
                new DomainEntity(){{
                    setId("2");
                    setAbbreviation("D2");
                    setMappingAbbr("M2");
                }},
                new DomainEntity(){{
                    setId("2");
                    setAbbreviation("D2");
                    setNodes(Arrays.asList(new DomainEntity(){{
                        setId("3");
                        setAbbreviation("D3.1");
                        setMappingAbbr("M3.1");
                    }}));
                }}
        );

        Map<String, String> mappingAbbrToDomainAbbr = new HashMap<>();

        // 调用方法
        frameworkProvider.getMappingAbbrToDomainAbbr(measures, mappingAbbrToDomainAbbr);

        // 验证结果
        assertEquals(3, mappingAbbrToDomainAbbr.size());
        assertEquals("D1", mappingAbbrToDomainAbbr.get("M1"));
        assertEquals("D2", mappingAbbrToDomainAbbr.get("M2"));
        assertEquals("D3.1", mappingAbbrToDomainAbbr.get("M3.1"));
    }

}

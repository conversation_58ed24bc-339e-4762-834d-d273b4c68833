package com.learninggenie.api.provider.impl;

import com.google.cloud.translate.*;
import com.learninggenie.common.data.enums.translate.TranslationTextFormat;
import org.apache.commons.codec.digest.DigestUtils;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.io.IOException;
import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.mockito.Mockito.*;

/**
 * TranslateProviderImpl 测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class TranslateProviderImplTest {
    @InjectMocks
    private TranslateProviderImpl translateProvider;

    @Mock
    private Translate translate;

    /**
     * 测试翻译方法
     * case 1: 验证正常文本进行翻译，传入源文本格式，应返回翻译后的文本，且使用传入的源文本格式
     * case 2: 验证正常文本进行翻译，不传入源文本格式，应返回翻译后的文本，且默认使用 html 格式
     */
    @Test
    public void textTranslateValidInputReturnsTranslatedText() throws InvocationTargetException, InstantiationException, IllegalAccessException, NoSuchMethodException {
        // 参数准备
        String content = "Hello";

        String translatedText = "你好";
        String sourceLanguage = "en";
        String model = "base";
        Constructor<?> privateConstructor = Translation.class.getDeclaredConstructor(String.class, String.class, String.class);
        privateConstructor.setAccessible(true);
        Translation translation = (Translation) privateConstructor.newInstance(translatedText, sourceLanguage, model);

        // 验证 case 1 的情况
        // 方法模拟
        when(translate.translate(eq(content), any(), any())).thenReturn(translation);
        // 调用测试方法
        String result = translateProvider.translate(content, TranslationTextFormat.TEXT, "zh");
        // 结果验证
        assertEquals("你好", result); // 返回翻译后的文本
        verify(translate, times(1)).translate(eq(content), eq(Translate.TranslateOption.format("text")), any());

        // 验证 case 2 的情况
        // 方法模拟
        when(translate.translate(eq(content), any(), any())).thenReturn(translation);
        // 调用测试方法
        result = translateProvider.translate(content, null, "zh");
        // 结果验证
        assertEquals("你好", result); // 验证不传入文本格式时，返回翻译后的文本
        verify(translate, times(1)).translate(eq(content), eq(Translate.TranslateOption.format("html")), any()); // 验证默认使用 html 格式
    }

    /**
     * 测试翻译方法
     * case: 验证空文本进行翻译或者目标语言为空，都应返回 null
     */
    @Test
    public void testTranslateEmptyInputReturnsNull() throws InvocationTargetException, InstantiationException, IllegalAccessException, NoSuchMethodException {
        // 验证空文本和目标语言为空均为空时，返回 null
        // 参数准备
        String content = "";
        String targetLanguage = "";
        // 调用测试方法
        String result = translateProvider.translate(content, TranslationTextFormat.HTML, targetLanguage);
        // 结果验证
        assertNull(result); // 验证空文本时，返回 null
        verify(translate, times(0)).translate(anyString(), any(), any()); // 验证不调用翻译方法

        // 验证目标语言为空时，返回 null
        // 参数准备
        content = "Hello";
        targetLanguage = "";
        // 调用测试方法
        result = translateProvider.translate(content, TranslationTextFormat.HTML, targetLanguage);
        // 结果验证
        assertNull(result); // 验证目标语言为空时，返回 null
        verify(translate, times(0)).translate(anyString(), any(), any()); // 验证不调用翻译方法

        // 验证源文本格式为空时，返回 null
        // 参数准备
        content = "";
        targetLanguage = "zh";
        // 调用测试方法
        result = translateProvider.translate(content, TranslationTextFormat.HTML, targetLanguage);
        // 结果验证
        assertNull(result); // 验证源文本为空时，返回 null
        verify(translate, times(0)).translate(anyString(), any(), any()); // 验证不调用翻译方法
    }

    /**
     * 测试翻译方法
     * case: 验证翻译异常时，应再次尝试翻译，且使用分割后的目标语言
     */
    @Test
    public void testTranslateExceptionRetriesWithModifiedLanguageCode() throws NoSuchMethodException, InvocationTargetException, InstantiationException, IllegalAccessException {
        // 参数准备
        String content = "Hello";
        String translatedText = "你好";
        String targetLanguage = "zh-CN";
        String splitTargetLanguage = "zh";
        String sourceLanguage = "en";
        String model = "base";
        Constructor<?> privateConstructor = Translation.class.getDeclaredConstructor(String.class, String.class, String.class);
        privateConstructor.setAccessible(true);
        Translation translation = (Translation) privateConstructor.newInstance(translatedText, sourceLanguage, model);

        // 方法模拟
        when(translate.translate(eq(content), eq(Translate.TranslateOption.format("html")), eq(Translate.TranslateOption.targetLanguage(targetLanguage)))).thenThrow(new TranslateException(new IOException("TranslateException")));
        when(translate.translate(eq(content), eq(Translate.TranslateOption.format("html")), eq(Translate.TranslateOption.targetLanguage(splitTargetLanguage)))).thenReturn(translation);

        // 调用测试方法
        String result = translateProvider.translate(content, TranslationTextFormat.HTML, targetLanguage);

        // 结果验证
        assertEquals("你好", result);
        verify(translate, times(1)).translate(eq(content), eq(Translate.TranslateOption.format("html")), eq(Translate.TranslateOption.targetLanguage(targetLanguage)));
        verify(translate, times(1)).translate(eq(content), eq(Translate.TranslateOption.format("html")), eq(Translate.TranslateOption.targetLanguage(splitTargetLanguage)));
    }

    /**
     * 测试生成翻译缓存键方法
     * case: 验证 originalFormat 为 HTML 或为空时，它不应将该格式包含在缓存键中
     */
    @Test
    public void testGenerateTranslateCacheKeyWithHtmlFormat() {
        // 数据准备
        String original = "Hello, world!";
        String expectedCacheKey = DigestUtils.md5Hex(original);
        // 调用测试方法
        String actualCacheKey = translateProvider.generateTranslateCacheKey(original, TranslationTextFormat.HTML);

        // 结果验证
        assertEquals(expectedCacheKey, actualCacheKey); // 验证 originalFormat 为 HTML 时，不包含该格式

        // 调用测试方法
        actualCacheKey = translateProvider.generateTranslateCacheKey(original, null);

        // 结果验证
        assertEquals(expectedCacheKey, actualCacheKey); // 验证 originalFormat 为空时，不包含该格式
    }

    /**
     * 测试生成翻译缓存键方法
     * case: 验证 originalFormat 不为 HTML 时，它将格式包含在缓存键中
     */
    @Test
    public void testGenerateTranslateCacheKeyWithNonHtmlFormat() {
        // 数据准备
        String original = "Hello, world!";
        TranslationTextFormat textFormat = TranslationTextFormat.TEXT;
        // 调用测试方法
        String expectedCacheKey = DigestUtils.md5Hex(textFormat.getFormat() + original);

        // 结果验证
        String actualCacheKey = translateProvider.generateTranslateCacheKey(original, textFormat);
        assertEquals(expectedCacheKey, actualCacheKey);
    }

    /**
     * 测试生成翻译缓存键方法
     * case 1: 验证 original 为空字符串时，它生成的键应该和直接调用 md5Hex 生成的键一样
     * case 2: 验证 original 为 null 时，调用测试方法和直接调用 md5Hex 生成的键都抛出空指针异常
     */
    @Test
    public void testGenerateTranslateCacheKeyWithEmptyOriginal() {
        // 数据准备
        String original = "";
        String expectedCacheKey = DigestUtils.md5Hex(original);
        // 调用测试方法
        String actualCacheKey = translateProvider.generateTranslateCacheKey(original, null);

        // 结果验证
        assertEquals(expectedCacheKey, actualCacheKey); // 验证 original 为空时，生成的键应该和直接调用 md5Hex 生成的键一样

        // 数据准备
        String finalOriginal = null;
        NullPointerException md5Exception = assertThrows(NullPointerException.class, () -> {
            DigestUtils.md5Hex(finalOriginal);
        });
        // 调用测试方法
        NullPointerException translateException = assertThrows(NullPointerException.class, () -> {
            translateProvider.generateTranslateCacheKey(null, null);
        });
        // 结果验证
        assertAll("Both exceptions should be of NullPointerException type",
                () -> assertEquals(NullPointerException.class, md5Exception.getClass()),
                () -> assertEquals(NullPointerException.class, translateException.getClass())
        ); // 验证 original 为空时，调用测试方法和直接调用 md5Hex 生成的键都抛出空指针异常
    }
}

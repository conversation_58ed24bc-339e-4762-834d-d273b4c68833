package com.learninggenie.api.provider.impl;

import com.google.common.collect.Lists;
import com.learninggenie.api.model.AuthTypeEnum;
import com.learninggenie.api.model.SystemTypeEnum;
import com.learninggenie.api.model.importdata.ImportChild;
import com.learninggenie.api.model.importdata.ImportStructure;
import com.learninggenie.api.model.importdata.ImportTeacher;
import com.learninggenie.api.model.user.CreateUserRequest;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.service.EmailService;
import com.learninggenie.api.service.ImportDataService;
import com.learninggenie.api.service.ImportService;
import com.learninggenie.api.service.UserService;
import com.learninggenie.common.data.dao.CenterDao;
import com.learninggenie.common.data.dao.GroupDao;
import com.learninggenie.common.data.dao.ImportThirdAuthDao;
import com.learninggenie.common.data.dao.StudentDao;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.entity.CenterEntity;
import com.learninggenie.common.data.entity.ThirdAuthEntity;
import com.learninggenie.common.data.entity.UserMetaDataEntity;
import com.learninggenie.common.data.enums.UserMetaKey;
import com.learninggenie.common.data.enums.api.ErrorType;
import com.learninggenie.common.data.enums.api.RosterType;
import com.learninggenie.common.data.enums.api.SyncStatus;
import com.learninggenie.common.data.enums.api.VerificationStatus;
import com.learninggenie.common.data.enums.importLog.ImportMark;
import com.learninggenie.common.data.model.UserModel;
import com.learninggenie.common.data.model.roster.mhs.VerificationResult;
import com.learninggenie.common.data.model.roster.one.*;
import com.learninggenie.common.exception.LearningGenieRuntimeException;
import com.learninggenie.common.sync.OneRosterService;
import com.learninggenie.common.utils.ResourceUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedConstruction;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.mockConstruction;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * 名单导入服务测试
 */
@RunWith(MockitoJUnitRunner.class)
public class OneRosterProviderImplTest {

    /**
     * Web 地址注入变量名称
     */
    private static final String WEB_SERVICE_FIELD_NAME = "webService";

    /**
     * Web 地址注入变量值
     */
    private static final String WEB_SERVICE_FIELD_VALUE = "web.learning-genie.com";

    @InjectMocks
    private OneRosterProviderImpl rosterProvider;

    @Mock
    private ImportThirdAuthDao importThirdAuthDao;

    @Mock
    private UserDaoImpl userDao;

    @Mock
    private UserProvider userProvider;

    @Mock
    private CenterDao centerDao;

    @Mock
    private GroupDao groupDao;

    @Mock
    private StudentDao studentDao;

    @Mock
    private ImportService importService;

    @Mock
    private EmailService emailService;

    @Mock
    private ImportDataService importDataService;

    @Mock
    private UserService usersevice;

    /**
     * 测试更新认证结果
     */
    @Test
    public void testUpdateVerificationResult() {
        // 空数据
        ThirdAuthEntity thirdAuthEntity = new ThirdAuthEntity(); // 认证信息
        VerificationResult verificationResult = new VerificationResult(); // 认证结果
        // 调用
        rosterProvider.updateVerificationResult(thirdAuthEntity, false, verificationResult);

        // 设置 ID
        thirdAuthEntity.setId("01");
        // 忽略基本信息错误
        rosterProvider.updateVerificationResult(thirdAuthEntity, true, verificationResult);

        // 重置调用状态
        Mockito.clearInvocations(importThirdAuthDao);
        // 验证状态为完成
        thirdAuthEntity.setVerificationResult(VerificationStatus.COMPLETE.toString());
        // 未到可用时间
        verificationResult.setUnavailable(true);
        // 调用
        rosterProvider.updateVerificationResult(thirdAuthEntity, false, verificationResult);
        // 验证调用更新方法
        verify(importThirdAuthDao, times(1)).updateVerification(any());

        // 重置调用状态
        Mockito.clearInvocations(importThirdAuthDao);
        // 验证状态为验证中
        thirdAuthEntity.setVerificationStatus(VerificationStatus.BASE_VERIFICATION.toString());
        // 调用
        rosterProvider.updateVerificationResult(thirdAuthEntity, false, verificationResult);

        // 验证调用更新方法
        verify(importThirdAuthDao, times(1)).updateVerification(any());
    }

    /**
     * 测试验证方法，没有认证信息的情况
     */
    @Test
    public void testVerifyWithoutAuthData() {
        // 空数据
        rosterProvider.verify(null, false, false, false);

        // 认证信息
        ThirdAuthEntity thirdAuthEntity = new ThirdAuthEntity();
        // 非 ClassLink 跳过
        thirdAuthEntity.setSystemType(SystemTypeEnum.ONE_ROSTER.toString());
        VerificationResult result = rosterProvider.verify(thirdAuthEntity, false, false, false);
        // 不调用查询认证信息
        verify(importThirdAuthDao, times(0)).getThirdAuthById(any());
        assertNull(result.getErrorType());

        // ClassLink 类型
        thirdAuthEntity.setSystemType(SystemTypeEnum.CLASS_LINK.toString());
        // 没有认证信息
        result = rosterProvider.verify(thirdAuthEntity, false, false, false);
        // 调用查询认证信息
        assertEquals(ErrorType.AUTH_INFO_INVALID.toString(), result.getErrorType());
    }

    /**
     * 测试验证方法，认证信息错误的情况
     */
    @Test
    public void testVerifyAuthError() {
        // 认证信息
        ThirdAuthEntity thirdAuthEntity = new ThirdAuthEntity();
        thirdAuthEntity.setSystemType(SystemTypeEnum.CLASS_LINK.toString()); // ClassLink 类型
        thirdAuthEntity.setExtendData("{}"); // 认证信息
        thirdAuthEntity.setAuthType(AuthTypeEnum.OAUTH1.toString()); // 认证类型
        when(importThirdAuthDao.getThirdAuthById(any())).thenReturn(thirdAuthEntity);
        try (MockedConstruction<OneRosterService> oneRosterServiceMockedConstruction = mockConstruction(OneRosterService.class, (mock, context) -> {
            when(mock.checkAuthInfo()).thenReturn(true); // 测试认证信息
            when(mock.getSchools(anyInt(), anyInt(), any())).thenThrow(new LearningGenieRuntimeException(""));
        })) {
            // 执行
            VerificationResult result = rosterProvider.verify(thirdAuthEntity, false, false, false);
            // 不会生成重复小孩 Excel
            assertEquals(ErrorType.AUTH_INFO_INVALID.toString(), result.getErrorType());
        }
    }

    /**
     * 测试验证方法，认证信息错误的情况
     */
    @Test
    public void testVerifyMissingDemographicsPermissionError() {
        // 认证信息
        ThirdAuthEntity thirdAuthEntity = new ThirdAuthEntity();
        thirdAuthEntity.setSystemType(SystemTypeEnum.CLASS_LINK.toString()); // ClassLink 类型
        thirdAuthEntity.setExtendData("{}"); // 认证信息
        thirdAuthEntity.setAuthType(AuthTypeEnum.OAUTH1.toString()); // 认证类型
        when(importThirdAuthDao.getThirdAuthById(any())).thenReturn(thirdAuthEntity);
        try (MockedConstruction<OneRosterService> oneRosterServiceMockedConstruction = mockConstruction(OneRosterService.class, (mock, context) -> {
            when(mock.checkAuthInfo()).thenReturn(true); // 测试认证信息
            List<com.learninggenie.common.data.model.roster.one.UserModel> students = Collections.singletonList(new com.learninggenie.common.data.model.roster.one.UserModel());
            when(mock.getStudents(anyInt(), anyInt(), any())).thenReturn(students); // 模拟学生列表
            when(mock.getDemographicsByStudent(any())).thenThrow(new LearningGenieRuntimeException("")); // 模拟获取属性异常
        })) {
            // 执行
            VerificationResult result = rosterProvider.verify(thirdAuthEntity, false, false, false);
            // 不会生成重复小孩 Excel
            assertEquals(ErrorType.MISSING_PERMISSION.toString(), result.getErrorType());
        }
    }

    /**
     * 测试 ClassLink 导入元数据 Key 替换为 OneRoster
     */
    @Test
    public void testSyncRosterDataClassLinkEmptyData() throws IOException {
        // 导入数据，空学校
        ImportStructure importStructure = new ImportStructure();
        importStructure.setAgencyId("agency-001"); // 机构 ID
        // 同步类型
        RosterType rosterType = RosterType.CLASS_LINK;

        // 管理员列表
        List<UserModel> admins = new ArrayList<>();
        UserModel admin = new UserModel();
        admin.setId("user-001"); // 用户 ID
        admins.add(admin);
        // 模拟查询管理员
        when(userDao.getAgencyOwnerByAgencyId(any())).thenReturn(admins);

        // 调用方法
        rosterProvider.syncRosterData(importStructure, rosterType);

        // 空数据，不会进入导入逻辑
        verify(centerDao, times(0)).getFirstCenterTimeZoneByAgencyId(any());
        verify(importService, times(0)).sftpSyncFinishEmails(any(), any(), any());
    }

    /**
     * 测试发送验证结果邮件，没有错误
     */
    @Test
    public void testSendVerificationResultNoError() {
        // 空数据
        rosterProvider.sendVerificationResult(null, null, null);
        // 不发送邮件
        verify(emailService, times(0)).sendAsync(any());

        // 验证结果，没有错误
        VerificationResult verificationResult = new VerificationResult();
        // 执行
        rosterProvider.sendVerificationResult(null, verificationResult, null);
        // 不发送邮件
        verify(emailService, times(0)).sendAsync(any());
    }

    /**
     * 测试发送验证结果邮件，认证失败
     */
    @Test
    public void testSendVerificationResultAuthError() {
        // 认证信息
        ThirdAuthEntity authEntity = new ThirdAuthEntity();
        authEntity.setEmail("<EMAIL>");
        // 验证结果
        VerificationResult verificationResult = new VerificationResult();
        verificationResult.setAuthError(true); // 认证失败
        // 查询用户信息，为空时邮件中不显示用户信息
        when(userDao.getUserByEmail(any())).thenReturn(null);
        // 模拟获取邮件模板
        MockedStatic<ResourceUtil> resourceUtil = mockStatic(ResourceUtil.class);
        resourceUtil.when(() -> ResourceUtil.getResourceAsString(anyString())).thenReturn("template-001");
        // 设置 Web 地址
        ReflectionTestUtils.setField(rosterProvider, WEB_SERVICE_FIELD_NAME,  WEB_SERVICE_FIELD_VALUE);
        // 执行
        rosterProvider.sendVerificationResult(authEntity, verificationResult, null);
        resourceUtil.close();
        // 验证发送邮件
        verify(emailService, times(1)).sendAsync(any());
    }

    /**
     * 测试发送验证结果邮件，禁用错误
     */
    @Test
    public void testSendVerificationResultDisabledError() {
        // 认证信息
        ThirdAuthEntity authEntity = new ThirdAuthEntity();
        authEntity.setEmail("<EMAIL>");
        // 验证结果
        VerificationResult verificationResult = new VerificationResult();
        verificationResult.setDisabled(true); // APP 被禁用
        // 查询用户信息
        when(userDao.getUserByEmail(any())).thenReturn(new UserModel());
        // 模拟获取邮件模板
        MockedStatic<ResourceUtil> resourceUtil = mockStatic(ResourceUtil.class);
        resourceUtil.when(() -> ResourceUtil.getResourceAsString(anyString())).thenReturn("template-002");
        // 设置 Web 地址
        ReflectionTestUtils.setField(rosterProvider, WEB_SERVICE_FIELD_NAME,  WEB_SERVICE_FIELD_VALUE);
        // 执行
        rosterProvider.sendVerificationResult(authEntity, verificationResult, null);
        resourceUtil.close();
        // 验证发送邮件
        verify(emailService, times(1)).sendAsync(any());
    }

    /**
     * 测试发送验证结果邮件，缺少属性权限
     */
    @Test
    public void testSendVerificationResultMissingDemographicsPermissionError() {
        // 认证信息
        ThirdAuthEntity authEntity = new ThirdAuthEntity();
        authEntity.setEmail("<EMAIL>");
        // 验证结果
        VerificationResult verificationResult = new VerificationResult();
        verificationResult.setMissingDemographicsPermission(true); // 缺少属性权限
        // 查询用户信息
        when(userDao.getUserByEmail(any())).thenReturn(new UserModel());
        // 模拟获取邮件模板
        MockedStatic<ResourceUtil> resourceUtil = mockStatic(ResourceUtil.class);
        resourceUtil.when(() -> ResourceUtil.getResourceAsString(anyString())).thenReturn("template-003");
        // 设置 Web 地址
        ReflectionTestUtils.setField(rosterProvider, WEB_SERVICE_FIELD_NAME,  WEB_SERVICE_FIELD_VALUE);
        // 执行
        rosterProvider.sendVerificationResult(authEntity, verificationResult, null);
        resourceUtil.close();
        // 验证发送邮件
        verify(emailService, times(1)).sendAsync(any());
    }

    /**
     * 测试发送验证结果邮件，缺少数据
     */
    @Test
    public void testSendVerificationResultMissingDataPermissionError() {
        // 认证信息
        ThirdAuthEntity authEntity = new ThirdAuthEntity();
        authEntity.setEmail("<EMAIL>");
        // 验证结果
        VerificationResult verificationResult = new VerificationResult();
        verificationResult.setMissingDataPermission(true); // 缺少数据
        // 查询用户信息
        when(userDao.getUserByEmail(any())).thenReturn(new UserModel());
        // 模拟获取邮件模板
        MockedStatic<ResourceUtil> resourceUtil = mockStatic(ResourceUtil.class);
        resourceUtil.when(() -> ResourceUtil.getResourceAsString(anyString())).thenReturn("template-004");
        // 设置 Web 地址
        ReflectionTestUtils.setField(rosterProvider, WEB_SERVICE_FIELD_NAME,  WEB_SERVICE_FIELD_VALUE);
        // 执行
        rosterProvider.sendVerificationResult(authEntity, verificationResult, null);
        resourceUtil.close();
        // 验证发送邮件
        verify(emailService, times(1)).sendAsync(any());
    }

    /**
     * 测试映射同步数据，没有数据的情况
     */
    @Test
    public void testMapObjectIdNoData() {
        // 机构 ID
        String agencyId = UUID.randomUUID().toString();
        // 同步认证信息
        ThirdAuthEntity thirdAuthEntity = new ThirdAuthEntity();
        thirdAuthEntity.setExtendData("{}"); // 认证信息
        thirdAuthEntity.setAuthType(AuthTypeEnum.OAUTH1.toString()); // 认证类型
        when(importThirdAuthDao.getThirdAuthById(any())).thenReturn(thirdAuthEntity);

        try (MockedConstruction<OneRosterService> oneRosterServiceMockedConstruction = mockConstruction(OneRosterService.class, (mock, context) -> {
            when(mock.checkAuthInfo()).thenReturn(true); // 测试认证信息
        })) {
            // 执行
            rosterProvider.mapObjectId(agencyId, thirdAuthEntity);
            // 不会生成重复小孩 Excel
            verify(importDataService, times(0)).generateDuplicateExcel(any());
        }
    }

    /**
     * 测试没有数据的同步学生入学日期
     */
    @Test
    public void testMapEnrollmentDate() {
        // 准备数据
        ImportChild child = new ImportChild();
        EnrollmentModel enrollmentModel = new EnrollmentModel();
        List<EnrollmentModel> enrollmentModels = new ArrayList<>();
        enrollmentModels.add(enrollmentModel);

        // 执行测试方法
        rosterProvider.mapEnrollmentDate(child, "1234",enrollmentModels);

        // 验证
        assertNull(child.getEnrollmentDate());
    }

    /**
     * 测试没有数据的同步学生入学日期
     */
    @Test
    public void testMapEnrollmentDate1() {
        // 准备数据
        ImportChild child = new ImportChild();

        // 执行测试方法
        rosterProvider.mapEnrollmentDate(child, "1234", null);

        // 验证
        assertNull(child.getEnrollmentDate());
    }

    /**
     * 测试有数据同步学生入学日期
     */
    @Test
    public void testMapEnrollmentDate2() {
        // 准备数据
        // 模拟学生数据
        ImportChild child = new ImportChild();
        child.setSourcedId("1234");
        // 模拟学生注册信息数据
        EnrollmentModel enrollmentModel = new EnrollmentModel();
        // 模拟学生数据
        com.learninggenie.common.data.model.roster.one.UserModel userModel = new com.learninggenie.common.data.model.roster.one.UserModel();
        userModel.setSourcedId("1234");
        enrollmentModel.setUser(userModel);
        // 模拟班级数据
        com.learninggenie.common.data.model.roster.one.ClassModel classModel = new com.learninggenie.common.data.model.roster.one.ClassModel();
        classModel.setSourcedId("1234");
        enrollmentModel.setClazz(classModel);
        enrollmentModel.setBeginDate("2020-01-01");
        List<EnrollmentModel> enrollmentModels = new ArrayList<>();
        enrollmentModels.add(enrollmentModel);

        // 执行测试方法
        rosterProvider.mapEnrollmentDate(child, "1234",enrollmentModels);

        // 验证
        assertEquals("01/01/2020", child.getEnrollmentDate());
    }

    /**
     * 测试没有数据的同步学生入学日期
     */
    @Test
    public void testMapEnrollmentTermDate() {
        // 准备数据
        ImportChild child = new ImportChild();

        // 执行测试方法
        rosterProvider.mapEnrollmentTermDate(child, null,null,"");

        // 验证
        assertNull(child.getEnrollmentDate());
    }

    /**
     * 测试有数据的同步学生入学日期
     */
    @Test
    public void testMapEnrollmentTermDate2() {
        // 准备数据
        ImportChild child = new ImportChild();
        // 模拟 Term 数据
        TermsModel termsModel = new TermsModel();
        termsModel.setSourcedId("1234");
        List<TermsModel> termsModels = new ArrayList<>();
        termsModels.add(termsModel);
        // 模拟 AcademicSession 数据
        AcademicSessionModel academicSessionModel = new AcademicSessionModel();
        academicSessionModel.setSourcedId("1234");
        String dateString = "2023-08-22";
        String endString = "2099-09-22";
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date date = dateFormat.parse(dateString);
            Date endDate = dateFormat.parse(endString);
            academicSessionModel.setStartDate(date);
            academicSessionModel.setEndDate(endDate);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        List<AcademicSessionModel> academicSessionModels = new ArrayList<>();
        academicSessionModels.add(academicSessionModel);
        String timeZone = "UTC";
        when(centerDao.getFirstCenterTimeZoneByAgencyId(anyString())).thenReturn(timeZone);

        // 执行测试方法
        rosterProvider.mapEnrollmentTermDate(child, termsModels,academicSessionModels,"1234");

        // 验证
        assertEquals("08/22/2023", child.getEnrollmentDate());
    }

    /**
     * 测试没有数据的同步学生家长信息
     */
    @Test
    public void testEnrollmentBatchParent() {
        // 准备数据
        ImportChild child = new ImportChild();

        // 执行测试方法
        rosterProvider.enrollmentBatchParent(child, null, null);

        // 验证
        assertEquals(0, child.getParents().size());
    }

    /**
     * 测试没有从属关系 ID 的同步学生家长信息
     */
    @Test
    public void testEnrollmentBatchParent2() {
        // 准备数据
        ImportChild child = new ImportChild();
        // 模拟从属关系数据
        AgentModel agentModel = new AgentModel();
        List<AgentModel> agentModels = new ArrayList<>();
        agentModels.add(agentModel);
        com.learninggenie.common.data.model.roster.one.UserModel userModel = new com.learninggenie.common.data.model.roster.one.UserModel();
        List<com.learninggenie.common.data.model.roster.one.UserModel> userModels = new ArrayList<>();
        userModels.add(userModel);

        // 执行测试方法
        rosterProvider.enrollmentBatchParent(child, agentModels, userModels);

        // 验证
        assertEquals(0, child.getParents().size());
    }

    /**
     * 测试有从属关系 ID 的同步学生家长信息
     */
    @Test
    public void testEnrollmentBatchParent3() {
        // 准备数据
        ImportChild child = new ImportChild();
        // 模拟从属关系数据
        AgentModel agentModel = new AgentModel();
        agentModel.setSourcedId("1234");
        List<AgentModel> agentModels = new ArrayList<>();
        agentModels.add(agentModel);
        // 模拟学生数据
        com.learninggenie.common.data.model.roster.one.UserModel userModel = new com.learninggenie.common.data.model.roster.one.UserModel();
        userModel.setSourcedId("1234");
        userModel.setGivenName("givenName");
        userModel.setMiddleName("middleName");
        userModel.setFamilyName("familyName");
        userModel.setEmail("email");
        List<com.learninggenie.common.data.model.roster.one.UserModel> userModels = new ArrayList<>();
        userModels.add(userModel);

        // 执行测试方法
        rosterProvider.enrollmentBatchParent(child, agentModels, userModels);

        // 验证
        assertEquals(1, child.getParents().size());
        assertEquals("1234", child.getParents().get(0).getSourceId());
    }

    /**
     * 测试有从属关系 ID 但是不匹配的同步学生家长信息
     */
    @Test
    public void testEnrollmentBatchParent4() {
        // 准备数据
        ImportChild child = new ImportChild();
        // 模拟从属关系数据
        AgentModel agentModel = new AgentModel();
        agentModel.setSourcedId("1234");
        List<AgentModel> agentModels = new ArrayList<>();
        agentModels.add(agentModel);
        // 模拟学生数据
        com.learninggenie.common.data.model.roster.one.UserModel userModel = new com.learninggenie.common.data.model.roster.one.UserModel();
        userModel.setSourcedId("2345");
        userModel.setGivenName("givenName");
        userModel.setMiddleName("middleName");
        userModel.setFamilyName("familyName");
        userModel.setEmail("email");
        List<com.learninggenie.common.data.model.roster.one.UserModel> userModels = new ArrayList<>();
        userModels.add(userModel);

        // 执行测试方法
        rosterProvider.enrollmentBatchParent(child, agentModels, userModels);

        // 验证
        assertEquals(0, child.getParents().size());
    }

    /**
     * 测试主办老师为空的情况同步学生主办老师信息
     */
    @Test
    public void testMapEnrollmentTeacher() {
        // 准备数据
        // 模拟学生数据
        ImportChild child = new ImportChild();

        // 执行测试方法
        rosterProvider.mapEnrollmentTeacher(child, null, null);

        // 验证
        assertNull(child.getEnrollmentDate());

    }

    /**
     * 测试没有匹配到主办老师信息的情况同步学生主办老师信息
     */
    @Test
    public void testMapEnrollmentTeacher1() {
        // 准备数据
        // 模拟学生数据
        ImportChild child = new ImportChild();
        // 模拟主办老师数据
        List<com.learninggenie.common.data.model.roster.one.EnrollmentModel> teacherModels = new ArrayList<>();
        com.learninggenie.common.data.model.roster.one.EnrollmentModel teacherModel = new com.learninggenie.common.data.model.roster.one.EnrollmentModel();
        com.learninggenie.common.data.model.roster.one.UserModel userModel = new com.learninggenie.common.data.model.roster.one.UserModel();
        userModel.setSourcedId("1234");
        teacherModel.setRole("teacher");
        teacherModel.setSourcedId("1234");
        teacherModel.setUser(userModel);
        teacherModels.add(teacherModel);
        // 包含主办老师所有信息的数据
        List<com.learninggenie.common.data.model.roster.one.UserModel> teachers = new ArrayList<>();
        com.learninggenie.common.data.model.roster.one.UserModel teacher = new com.learninggenie.common.data.model.roster.one.UserModel();
        teacher.setSourcedId("2345");
        teachers.add(teacher);

        // 执行测试方法
        rosterProvider.mapEnrollmentTeacher(child, teacherModels, teachers);

        // 验证
        assertNull(child.getEnrollmentDate());

    }

    /**
     * 测试匹配到主办老师信息的情况同步学生主办老师信息
     */
    @Test
    public void testMapEnrollmentTeacher2() {
        // 准备数据
        // 模拟学生数据
        ImportChild child = new ImportChild();
        // 模拟主办老师数据
        List<com.learninggenie.common.data.model.roster.one.EnrollmentModel> teacherModels = new ArrayList<>();
        com.learninggenie.common.data.model.roster.one.EnrollmentModel teacherModel = new com.learninggenie.common.data.model.roster.one.EnrollmentModel();
        com.learninggenie.common.data.model.roster.one.UserModel userModel = new com.learninggenie.common.data.model.roster.one.UserModel();
        userModel.setSourcedId("2345");
        teacherModel.setRole("teacher");
        teacherModel.setSourcedId("1234");
        teacherModel.setUser(userModel);
        teacherModels.add(teacherModel);
        // 包含主办老师所有信息的数据
        List<com.learninggenie.common.data.model.roster.one.UserModel> teachers = new ArrayList<>();
        com.learninggenie.common.data.model.roster.one.UserModel teacher = new com.learninggenie.common.data.model.roster.one.UserModel();
        teacher.setSourcedId("2345");
        teacher.setEmail("email");
        teachers.add(teacher);

        // 执行测试方法
        rosterProvider.mapEnrollmentTeacher(child, teacherModels, teachers);

        // 验证
        Map<String, List<String>> extended = child.getExtended();
        assertEquals("email.bak", extended.get("Teacher").get(0));

    }

    /**
     * 测试同步花名册数据
     *
     * @throws IOException IOException
     */
    @Test
    public void testSyncRosterData() throws IOException {
        // 导入数据，空学校
        ImportStructure importStructure = new ImportStructure();
        importStructure.setAgencyId("agency-001"); // 机构 ID
        // 老师列表
        ArrayList<ImportTeacher> updatedTeachers = new ArrayList<>();
        // 循环添加老师信息， 由于要和下方的未添加的老师做匹配。所以这里需要选择多添加一个老师
        for (int i = 0; i < SyncStatus.values().length + 1; i++) {
            // 创建要 Mock 的老师数据
            ImportTeacher importTeacher = new ImportTeacher();
            // 设置密码
            importTeacher.setPasswordHash("Password");
            // 设置在那些班级中任教
            importTeacher.setGroupNames(Lists.newArrayList());
            // 设置老师的名字
            importTeacher.setDisplayName("Teacher Name");
            // 设置导入的类型
            importTeacher.setType(ImportMark.NEW_TEACHER);
            // 设置邀请状态为为邀请
            importTeacher.setInvite(false);
            // 设置任教的班级 id 的集合
            importTeacher.setGroupIds(Lists.newArrayList());
            // 设置老师的 id
            String teacherId = UUID.randomUUID().toString();
            importTeacher.setId(teacherId);
            // 设置用户的 用户名
            importTeacher.setUserName("UserName");
            // 设置用户的 First Name
            importTeacher.setFirstName("FirstName");
            // 设置用户的 Last Name
            importTeacher.setLastName("LastName");
            // 设置用户的 Email
            importTeacher.setEmail("Teacher" + i + "@Email.com");
            // 设置用户的 Phone
            importTeacher.setPhone("");
            // 设置用户的 Password
            importTeacher.setPassword("Password");
            // 设置用户的 邀请 id
            importTeacher.setInvitationId("");
            // 设置哪个用户创建的
            importTeacher.setCreateUserId("");
            // 设置用户的 Source Id
            importTeacher.setSourcedId("SourceId" + i);
            // 设置用户的 最后修改时间
            importTeacher.setDateLastModified(new Date());
            // 设置用户的 Sourced 状态
            if (i < SyncStatus.values().length) {
                importTeacher.setSourcedStatus(SyncStatus.values()[i].toString());
            } else {
                importTeacher.setSourcedStatus(SyncStatus.INACTIVE.toString());
            }
            // 设置用户的 Sourced Group Ids
            importTeacher.setSourcedGroupIds(Lists.newArrayList());

            // 将老师数据添加到老师列表中
            updatedTeachers.add(importTeacher);
        }
        importStructure.setUpdatedTeachers(updatedTeachers); // 更新的老师
        // 同步类型
        RosterType rosterType = RosterType.ONE_ROSTER;

        // 管理员列表
        List<UserModel> admins = new ArrayList<>();
        UserModel admin = new UserModel();
        admin.setId("user-001"); // 用户 ID
        admins.add(admin);
        // 模拟查询管理员
        when(userDao.getAgencyOwnerByAgencyId(any())).thenReturn(admins);

        // 模拟查询学校
        ArrayList<CenterEntity> centerEntities = new ArrayList<>();
        // 模拟学校数据
        CenterEntity centerEntity = new CenterEntity();
        // 设置学校的 Source Id
        centerEntity.setMetaValue("Source Id");

        // 要被移除的学校
        CenterEntity removeCenter = new CenterEntity();
        // 设置学校 id
        removeCenter.setId("removeCenterId");
        // 设置学校的 Source Id
        removeCenter.setMetaValue("");

        centerEntities.add(centerEntity);
        centerEntities.add(removeCenter);
        when(centerDao.getAllWithMetadataByAgencyId(anyString(), anyString())).thenReturn(centerEntities);

        // mock 查询学校的老师
        ArrayList<UserModel> userModels = new ArrayList<>();
        // 循环添加老师信息
        for (int i = 0; i < SyncStatus.values().length; i++) {
            // 定义用户 model
            UserModel userModel = new UserModel();
            // 设置 id
            userModel.setId("user-" + i);
            // 设置用户的 Email
            userModel.setEmail("Teacher" + i + "@Email.com");
            // 设置用户的 Source Id
            userModel.setMetaValue("SourceId" + i);

            userModels.add(userModel);
        }
        // 添加一个存在在数据库中，但是不存在在第三方的老师
        UserModel removeTeacher = new UserModel();
        // 设置用户的 Email
        removeTeacher.setEmail("<EMAIL>");
        // 设置用户的 Source Id
        removeTeacher.setMetaValue("SourceId");

        userModels.add(removeTeacher);

        // 添加一个不存在在数据库中，但是存在在第三方的老师
        UserModel addTeacher = new UserModel();
        // 设置用户的 Email
        addTeacher.setEmail("Teacher" + SyncStatus.values().length + "@Email.com");
        // 设置用户的 Source Id
        addTeacher.setMetaValue(null);

        userModels.add(addTeacher);
        when(userDao.getTeacherWithMetaByAgency(anyString(), anyString())).thenReturn(userModels);
        // 调用方法
        rosterProvider.syncRosterData(importStructure, rosterType);


        // 验证正确的删除和创建了老师
        verify(usersevice, times(0)).createStaff(any(CreateUserRequest.class), anyString(), isNull());
        verify(usersevice, times(1)).deleteUser(anyString(), anyString());
        verify(importService, times(0)).sftpSyncFinishEmails(any(), any(), any());
    }


    /**
     * 测试从 Map.Entry 获取对应的数据,并且将数据转化为数据库可以保存的 UserMetaDataEntity
     */
    @Test
    public void testConvertUserMetaDataEntityHandler() {
        // 创建测试数据
        String userMetadataValue = "userMetadataValue";
        String userId = "userId";

        // 定义一个 Mock 的 UserModel
        UserModel userModel = new UserModel();
        // 设置属性值
        // 设置 Id
        userModel.setId(userId);
        // 定义一个 Map.Entry,作为要处理的测试参数
        Map.Entry<String, UserModel> needConvertUserModelEntry = new AbstractMap.SimpleEntry<>(userMetadataValue, userModel);

        // 调用被测试的方法
        UserMetaDataEntity convertedUserMetaDataEntity = rosterProvider.convertUserMetaDataEntityHandler(needConvertUserModelEntry);

        // 验证结果是否符合预期
        // 断言 userId 是期望值
        assertEquals(userId, convertedUserMetaDataEntity.getUser().getId());
        // 断言 metaKey 是 SOURCE_ID
        assertEquals(UserMetaKey.SOURCE_ID.toString(), convertedUserMetaDataEntity.getMetaKey());
        // 断言 metaValue 是 userMetadataValue
        assertEquals(userMetadataValue, convertedUserMetaDataEntity.getMetaValue());
    }

    /**
     * 测试获取 classLink 小孩的附加属性
     */
    @Test
    public void testMapEnrollmentAttr() {
        // 准备数据
        ImportChild child = new ImportChild();
        DemographicsModel demographicsModel = new DemographicsModel();
        demographicsModel.setSourcedId("1234"); // 设置 SourceId
        demographicsModel.setBirthDate("2020-01-01"); // 设置出生日期
        demographicsModel.setSex("female"); // 设置性别
        demographicsModel.setAmericanIndianOrAlaskaNative("false"); // 设置美洲印第安人或阿拉斯加原住民
        demographicsModel.setAsian("false"); // 设置亚洲
        demographicsModel.setBlackOrAfricanAmerican("false"); // 设置黑人或非洲裔美国人
        demographicsModel.setNativeHawaiianOrOtherPacificIslander("false"); // 设置夏威夷原住民或其他太平洋岛民
        demographicsModel.setWhite("false"); // 设置白人
        demographicsModel.setDemographicRaceTwoOrMoreRaces("false"); // 设置两种或两种以上种族
        demographicsModel.setHispanicOrLatinoEthnicity("true"); // 设置西班牙裔或拉丁裔
        ClassLinkMetaData metaData = new ClassLinkMetaData(); // 附加属性
        metaData.setHomeLanguage("English"); // 设置语言
        metaData.setDisabilityStatus("Yes"); // 设置残疾状态
        demographicsModel.setMetadata(metaData);

        // 执行测试方法
        rosterProvider.mapEnrollmentAttr(child, demographicsModel, null);

        // 验证
        Map<String, List<String>> extended = child.getExtended();
        // 验证语言
        assertEquals("English", extended.get("Language").get(0));
        // 验证残疾状态
        assertEquals("Yes", extended.get("IEP/IFSP").get(0));

    }
}

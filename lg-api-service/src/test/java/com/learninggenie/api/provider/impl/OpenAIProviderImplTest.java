package com.learninggenie.api.provider.impl;


import com.learninggenie.api.provider.PromptProvider;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.common.ai.listener.StreamResultListener;
import com.learninggenie.common.ai.service.OpenAIService;
import com.learninggenie.common.data.entity.prompt.PromptEntity;
import com.learninggenie.common.data.entity.prompt.PromptUsageRecordEntity;
import com.learninggenie.common.data.enums.prompt.CreateSource;
import com.learninggenie.common.data.enums.prompt.UseType;
import com.learninggenie.common.data.model.openai.completion.chat.ChatCompletionRequest;
import com.learninggenie.common.data.model.openai.completion.chat.ChatCompletionResult;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;


import java.util.concurrent.ExecutorService;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.Silent.class)
public class OpenAIProviderImplTest {

    @InjectMocks
    private OpenAIProviderImpl openAIProviderImpl;

    @Mock
    private ExecutorService executorService;

    @Mock
    private OpenAIService openAIService;

    @Mock
    private PromptProvider promptProvider;

    @Mock
    private UserProvider userProvider;

    /**
     * 测试调用 ChatCompletion 接口，并将数据推送到 SSE Emitter
     *
     */
    @Test
    public void mockCreateStreamChatCompletion2() {
       // 模拟入参
       String prompt = "prompt";
       PromptEntity promptEntity = new PromptEntity();
       SseEmitter emitter = new SseEmitter();
       String additionalData = "additionalData";
        // 模拟 userProvider.getCurrentUserId() 方法返回值
        when(userProvider.getCurrentUserId()).thenReturn("mockUserId");
       // 调用方法
       openAIProviderImpl.createStreamChatCompletion(prompt, promptEntity, emitter, additionalData);
       // 验证方法是否被调用
       verify(executorService, times(1)).execute(any(Runnable.class));
    }

    /**
     * 测试调用 ChatCompletion 接口，并将数据推送到 SSE Emitter
     *
     */
    @Test
    public void mockCreateStreamChatCompletion3() throws Exception {
        // 设置模拟行为
        doAnswer(invocationOnMock -> {
            // 获取任务执行器
            Runnable runnable = invocationOnMock.getArgument(0);
            // 执行任务
            runnable.run();
            return null;
        }).when(executorService).execute(any(Runnable.class));
        // 模拟入参
        String prompt = "prompt";
        PromptEntity promptEntity = new PromptEntity();
        SseEmitter emitter = new SseEmitter();
        String additionalData = "additionalData";

        // 模拟依赖对象

        ChatCompletionResult completionResult = mock(ChatCompletionResult.class);
        PromptUsageRecordEntity usageRecordEntity = mock(PromptUsageRecordEntity.class);


        when(openAIService.createStreamChatCompletion(any(), any())).thenReturn(completionResult);

        // 模拟 promptProvider.createPromptUsageRecord 方法返回值
        when(promptProvider.createPromptUsageRecord(any(), any(), any(), any(), any(), anyString())).thenReturn(usageRecordEntity);

        // 模拟 userProvider.getCurrentUserId() 方法返回值
        when(userProvider.getCurrentUserId()).thenReturn("mockUserId");

        // 执行方法
        openAIProviderImpl.createStreamChatCompletion(prompt, promptEntity, emitter, CreateSource.MANUAL, UseType.NORMAL, additionalData);

        // 验证开始请求方法是否被调用
        verify(openAIService).createStreamChatCompletion(any(ChatCompletionRequest.class), any(StreamResultListener.class));
    }

    /**
     * 测试 ChatCompletion 接口
     */
    @Test
    public void mockCreateChatCompletion() {
        // 模拟入参
        String prompt = "prompt";
        PromptEntity promptEntity = new PromptEntity();
        String additionalData = "additionalData";
        // 调用方法
        openAIProviderImpl.createChatCompletion(prompt, promptEntity, CreateSource.AUTO,UseType.EVALUATE, additionalData);
        // 验证方法是否被调用
        verify(openAIService, times(1)).createChatCompletion(any(ChatCompletionRequest.class));
    }
}

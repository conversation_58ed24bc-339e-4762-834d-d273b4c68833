package com.learninggenie.api.provider.impl;

import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.service.PeriodService;
import com.learninggenie.common.cache.CacheService;
import com.learninggenie.common.data.dao.GroupDao;
import com.learninggenie.common.data.dao.impl.CenterMapper;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.entity.GroupEntity;
import com.learninggenie.common.data.entity.GroupMetaDataEntity;
import com.learninggenie.common.data.entity.UserEntity;
import com.learninggenie.common.data.enums.UserRole;
import com.learninggenie.common.data.model.*;
import com.learninggenie.common.data.repository.UserRepository;
import com.learninggenie.common.filesystem.FileSystem;
import com.learninggenie.common.score.RatingService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * User Provider 测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class UserProviderImplTest {

    /**
     * 黑名单邮箱
     */
    private static final String BLACKLIST_EMAIL = "<EMAIL>";

    /**
     * 黑名单国家
     */
    private static final String BLACKLIST_COUNTRY = "Nigeria";

    /**
     * 非黑名单邮箱
     */
    private static final String NOT_BLACKLIST_EMAIL = "<EMAIL>";

    /**
     * 非黑名单国家
     */
    private static final String NOT_BLACKLIST_COUNTRY = "America";

    /**
     * 非黑名单时区
     */
    private static final String NOT_BLACKLIST_TIMEZONE = "America/Los_Angeles";

    /**
     * 黑名单时区
     */
    private static final String BLACKLIST_TIMEZONE = "Africa/Lagos";

    @InjectMocks
    private UserProviderImpl userProvider;


    @Mock
    private UserRepository userRepository;
    @Mock
    private UserDaoImpl userDao;
    @Mock
    private PeriodService periodService;
    @Mock
    private RatingService ratingService;
    @Mock
    private CenterMapper centerMapper;
    @Mock
    private GroupDao groupDao;
    @Mock
    private FileSystem fileSystem;
    @Mock
    private CacheService cacheService;
    /**
     * 测试邮箱是否可用
     */
    @Test
    public void testIsEmailAvailable() {
        // 空数据
        Boolean available = userProvider.isEmailAvailable(null);
        Assert.assertNull(available);

        // 邮箱格式错误
        available = userProvider.isEmailAvailable("test");
        Assert.assertNull(available);

        // 黑名单邮箱
        available = userProvider.isEmailAvailable(BLACKLIST_EMAIL);
        Assert.assertFalse(available);

        // 非黑名单邮箱
        available = userProvider.isEmailAvailable(NOT_BLACKLIST_EMAIL);
        Assert.assertTrue(available);
    }

    /**
     * 测试邮箱是否可用（黑名单邮箱）
     */
    @Test(expected = BusinessException.class)
    public void testCheckEmailAvailableWithBlackList() {
        // 空数据
        userProvider.checkEmailAvailable(null);

        // 黑名单邮箱
        userProvider.checkEmailAvailable(BLACKLIST_EMAIL);
    }


    /**
     * 测试国家是否可用
     */
    @Test
    public void testIsCountryAvailable() {
        // 空数据
        Boolean available = userProvider.isCountryAvailable(null);
        Assert.assertNull(available);

        // 黑名单国家
        available = userProvider.isCountryAvailable(BLACKLIST_COUNTRY);
        Assert.assertFalse(available);

        // 非黑名单国家
        available = userProvider.isCountryAvailable(NOT_BLACKLIST_COUNTRY);
        Assert.assertTrue(available);
    }

    /**
     * 测试国家是否可用（黑名单国家）
     */
    @Test(expected = BusinessException.class)
    public void testCheckCountryAvailableWithBlackList() {
        // 空数据
        userProvider.checkCountryAvailable(null);

        // 黑名单国家
        userProvider.checkCountryAvailable(BLACKLIST_COUNTRY);
    }

    /**
     * 测试检查用户邮箱是否注册
     */
    @Test(expected = BusinessException.class)
    public void testCheckUserEmailIsNotRegisterWithBlackList() {
        // 黑名单国家
        userProvider.checkUserEmailIsNotRegister(BLACKLIST_EMAIL);
    }

    /**
     * 测试时区是否可用
     */
    @Test
    public void testIsTimezoneAvailable() {
        // 空数据
        Boolean available = userProvider.isTimezoneAvailable(null);
        Assert.assertNull(available);

        // 黑名单时区
        available = userProvider.isTimezoneAvailable(BLACKLIST_TIMEZONE);
        Assert.assertFalse(available);

        // 非黑名单时区
        available = userProvider.isTimezoneAvailable(NOT_BLACKLIST_TIMEZONE);
        Assert.assertTrue(available);
    }

    /**
     * 测试时区是否可用（黑名单时区）
     */
    @Test(expected = BusinessException.class)
    public void testCheckTimezoneAvailableWithBlackList() {
        // 空数据
        userProvider.checkTimezoneAvailable(null);

        // 黑名单时区
        userProvider.checkTimezoneAvailable(BLACKLIST_TIMEZONE);
    }

    /**
     * 测试获取管理员机构列表
     */
    @Test
    public void testGetAgenciesIncludeDeletedByAdminUser() {
        // 测试空数据的情况
        userProvider.getAgenciesIncludeDeletedByUser(null);

        // 用户信息
        UserEntity user = new UserEntity();
        user.setRole(UserRole.AGENCY_ADMIN.toString()); // 设置管理员角色

        // 查询的机构列表
        List<AgencyModel> agencies = new ArrayList<>();
        AgencyModel agency = new AgencyModel();
        agencies.add(agency);
        // 模拟查询机构返回数据
        when(userDao.getAgenciesByAgencyAdminIncludeDeleted(any())).thenReturn(agencies);

        // 执行查询
        List<AgencyModel> results = userProvider.getAgenciesIncludeDeletedByUser(user);

        // 验证查询结果
        Assert.assertEquals(1, results.size());
    }

    /**
     * 测试获取园长机构列表
     */
    @Test
    public void testGetAgenciesIncludeDeletedBySiteAdminUser() {
        // 用户信息
        UserEntity user = new UserEntity();
        user.setRole(UserRole.SITE_ADMIN.toString()); // 设置园长角色

        // 查询的机构列表
        List<AgencyModel> agencies = new ArrayList<>();
        AgencyModel agency = new AgencyModel();
        agencies.add(agency);
        // 模拟查询机构返回数据
        when(userDao.getAgenciesBySiteAdminIncludeDeleted(any())).thenReturn(agencies);

        // 执行查询
        List<AgencyModel> results = userProvider.getAgenciesIncludeDeletedByUser(user);

        // 验证查询结果
        Assert.assertEquals(1, results.size());
    }

    /**
     * 测试获取老师机构列表
     */
    @Test
    public void testGetAgenciesIncludeDeletedByTeacherUser() {
        // 用户信息
        UserEntity user = new UserEntity();
        user.setRole(UserRole.COLLABORATOR.toString()); // 设置老师角色

        // 查询的机构列表
        List<AgencyModel> agencies = new ArrayList<>();
        AgencyModel agency = new AgencyModel();
        agencies.add(agency);
        // 模拟查询机构返回数据
        when(userDao.getAgenciesByTeacherIncludeDeleted(any())).thenReturn(agencies);

        // 执行查询
        List<AgencyModel> results = userProvider.getAgenciesIncludeDeletedByUser(user);

        // 验证查询结果
        Assert.assertEquals(1, results.size());
    }

    /**
     * 测试获取家长机构列表
     */
    @Test
    public void testGetAgenciesIncludeDeletedByParentUser() {
        // 用户信息
        UserEntity user = new UserEntity();
        user.setRole(UserRole.PARENT.toString()); // 设置管理员角色

        // 查询的机构列表
        List<AgencyModel> agencies = new ArrayList<>();
        AgencyModel agency = new AgencyModel();
        agencies.add(agency);
        // 模拟查询机构返回数据
        when(userDao.getAgenciesByParentIncludeDeleted(any())).thenReturn(agencies);

        // 执行查询
        List<AgencyModel> results = userProvider.getAgenciesIncludeDeletedByUser(user);

        // 验证查询结果
        Assert.assertEquals(1, results.size());
    }

    @Test
    public void testGetCenterGroupByUserIdAndAgencyOwner () {
        String userId = "1";
        boolean isAllGroup = true;
        UserEntity userEntity = new UserEntity();
        userEntity.setId("1");
        userEntity.setIsDeleted(false);
        userEntity.setRole(UserRole.AGENCY_OWNER.toString());

        List<CenterGroupModel> centerGroupList = new ArrayList<>();
        CenterGroupModel centerGroupModel = new CenterGroupModel();
        centerGroupModel.setGroupId("1");
        centerGroupModel.setGroupName("GroupName");
        centerGroupModel.setCenterId("");
        centerGroupModel.setCenterName("CenterName");

        centerGroupList.add(centerGroupModel);

        List<AgencyModel> agencyModelList = new ArrayList<>();
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("1");
        agencyModel.setName("Agency1");

        agencyModelList.add(agencyModel);

        List<GroupMetaDataEntity> groupMetaDataList = new ArrayList<>();
        GroupMetaDataEntity groupMetaDataEntity = new GroupMetaDataEntity();
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId("1");
        groupEntity.setName("GroupName");
        groupMetaDataEntity.setGroup(groupEntity);
        groupMetaDataList.add(groupMetaDataEntity);

        when(userRepository.findById(userId)).thenReturn(Optional.of(userEntity));
        when(userDao.getCenterGroupByAgencyUser(userId)).thenReturn(centerGroupList);
        when(centerMapper.getAgencyByCenterId(anyString())).thenReturn(agencyModelList);
        when(groupDao.getMetaDataByGroupIdsAndKey(anyList(), anyString())).thenReturn(groupMetaDataList);

        userProvider.getCenterGroupByUserId(userId, isAllGroup);

        verify(userRepository).findById(userId);
        verify(userDao).getCenterGroupByAgencyUser(userId);
        verify(centerMapper).getAgencyByCenterId(anyString());
        verify(groupDao).getMetaDataByGroupIdsAndKey(anyList(), anyString());
    }

    @Test
    public void testGetCenterGroupByUserIdAndSiteAdmin () {
        String userId = "1";
        boolean isAllGroup = true;
        UserEntity userEntity = new UserEntity();
        userEntity.setId("1");
        userEntity.setIsDeleted(false);
        userEntity.setRole(UserRole.SITE_ADMIN.toString());

        List<CenterGroupModel> centerGroupList = new ArrayList<>();
        CenterGroupModel centerGroupModel = new CenterGroupModel();
        centerGroupModel.setGroupId("1");
        centerGroupModel.setGroupName("GroupName");
        centerGroupModel.setCenterId("");
        centerGroupModel.setCenterName("CenterName");

        centerGroupList.add(centerGroupModel);

        List<AgencyModel> agencyModelList = new ArrayList<>();
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("1");
        agencyModel.setName("Agency1");

        agencyModelList.add(agencyModel);

        List<GroupMetaDataEntity> groupMetaDataList = new ArrayList<>();
        GroupMetaDataEntity groupMetaDataEntity = new GroupMetaDataEntity();
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId("1");
        groupEntity.setName("GroupName");
        groupMetaDataEntity.setGroup(groupEntity);
        groupMetaDataList.add(groupMetaDataEntity);

        when(userRepository.findById(userId)).thenReturn(Optional.of(userEntity));
        when(userDao.getCenterGroupBySiteAdmin(userId)).thenReturn(centerGroupList);
        when(centerMapper.getAgencyByCenterId(anyString())).thenReturn(agencyModelList);
        when(groupDao.getMetaDataByGroupIdsAndKey(anyList(), anyString())).thenReturn(groupMetaDataList);

        userProvider.getCenterGroupByUserId(userId, isAllGroup);

        verify(userRepository).findById(userId);
        verify(userDao).getCenterGroupBySiteAdmin(userId);
        verify(centerMapper).getAgencyByCenterId(anyString());
        verify(groupDao).getMetaDataByGroupIdsAndKey(anyList(), anyString());
    }

    @Test
    public void testGetCenterGroupByUserIdAndTeacher () {
        String userId = "1";
        boolean isAllGroup = true;
        UserEntity userEntity = new UserEntity();
        userEntity.setId("1");
        userEntity.setIsDeleted(false);
        userEntity.setRole(UserRole.COLLABORATOR.toString());

        List<CenterGroupModel> centerGroupList = new ArrayList<>();
        CenterGroupModel centerGroupModel = new CenterGroupModel();
        centerGroupModel.setGroupId("1");
        centerGroupModel.setGroupName("GroupName");
        centerGroupModel.setCenterId("");
        centerGroupModel.setCenterName("CenterName");

        centerGroupList.add(centerGroupModel);

        List<AgencyModel> agencyModelList = new ArrayList<>();
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("1");
        agencyModel.setName("Agency1");

        agencyModelList.add(agencyModel);

        List<GroupMetaDataEntity> groupMetaDataList = new ArrayList<>();
        GroupMetaDataEntity groupMetaDataEntity = new GroupMetaDataEntity();
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId("1");
        groupEntity.setName("GroupName");
        groupMetaDataEntity.setGroup(groupEntity);
        groupMetaDataList.add(groupMetaDataEntity);

        List<com.learninggenie.common.data.entity.GroupEntity> groups = new ArrayList<>();
        groups.add(groupEntity);

        when(userRepository.findById(userId)).thenReturn(Optional.of(userEntity));
        when(userDao.getCenterGroupByTeacherId(userId)).thenReturn(centerGroupList);
        when(centerMapper.getAgencyByCenterId(anyString())).thenReturn(agencyModelList);
        when(groupDao.getMetaDataByGroupIdsAndKey(anyList(), anyString())).thenReturn(groupMetaDataList);
        when(groupDao.getAllByCenterId(anyString())).thenReturn(groups);

        userProvider.getCenterGroupByUserId(userId, isAllGroup);

        verify(userRepository).findById(userId);
        verify(userDao).getCenterGroupByTeacherId(userId);
        verify(centerMapper).getAgencyByCenterId(anyString());
        verify(groupDao).getMetaDataByGroupIdsAndKey(anyList(), anyString());
    }
    @Test
    public void testGetCenterGroupByUserIdAgencyOwner() {
        // 模拟数据
        String userId = "123";
        boolean allGroups = false;
        UserEntity user = new UserEntity();
        user.setRole(String.valueOf(UserRole.AGENCY_OWNER));
        when(userRepository.findById(userId)).thenReturn(Optional.of(user));
        // 创建两个CenterGroupModel对象，并设置属性值
        CenterGroupModel obj1 = new CenterGroupModel();
        obj1.setCenterId("center1");
        obj1.setCenterName("Center 1");
        obj1.setGroupId("group1");
        obj1.setGroupName("Group 1");
        obj1.setPeriodFrom(new Date());
        obj1.setPeriodTo(new Date());
        obj1.setPeriodAlias("Period Alias 1");
        obj1.setGroupIsDeleted(false);
        obj1.setGroupIsInactive(true);
        obj1.setCenterDomainId("centerDomain1");
        obj1.setCenterDomainName("Center Domain 1");
        obj1.setCenterDomainIsMultiType(true);
        obj1.setCenterDomainType("Type 1");
        obj1.setCenterDomainTypeDisplay("Type 1 Display");
        obj1.setGroupDomainId("groupDomain1");
        obj1.setGroupDomainName("Group Domain 1");
        obj1.setGroupDomainIsMultiType(false);
        obj1.setGroupDomainType("Type 2");
        obj1.setGroupDomainTypeDisplay("Type 2 Display");
        obj1.setFrameworkId("framework1");
        obj1.setFrameworkName("Framework 1");
        obj1.setPeriodGroupId("periodGroup1");

        // 创建List集合并将两个对象添加到集合中
        List<CenterGroupModel> list = new ArrayList<>();
        list.add(obj1);
        when(userDao.getCenterGroupByAgencyUser(userId)).thenReturn(list);

        // 周期数据
        List<PeriodsGroupEntity> periodsGroupEntityList = new ArrayList<>();
        PeriodsGroupEntity periodsGroupEntity = new PeriodsGroupEntity();
        periodsGroupEntity.setId("periodGroup1");
        periodsGroupEntity.setSchoolYear("2019-2020");
        periodsGroupEntity.setType("type1");

        periodsGroupEntityList.add(periodsGroupEntity);
        when(periodService.getPeriodsGroupsByIds(anyList())).thenReturn(periodsGroupEntityList);

        // rating service ratingService.isITFramework
        when(ratingService.isITFramework(anyString())).thenReturn(true);
        // ratingService.isPSFramework(centerGroup.getFrameworkId())
        when(ratingService.isPSFramework(anyString())).thenReturn(false);

        // centerMapper.getAgencyByCenterId
        List<AgencyModel> agencyModelList = new ArrayList<>();
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("agency1");
        agencyModel.setName("Agency 1");
        agencyModel.setState("State 1");
        agencyModel.setAgencyLogo(new Media());
        agencyModelList.add(agencyModel);
        when(centerMapper.getAgencyByCenterId(anyString())).thenReturn(agencyModelList);
        List<CenterModel> expectedCenterModelList = new ArrayList<>();

        // groupDao.getMetaDataByGroupIdsAndKey
        List<GroupMetaDataEntity> groupMetaDataList = new ArrayList<>();
        GroupMetaDataEntity groupMetaDataEntity = new GroupMetaDataEntity();
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId("group1");
        groupMetaDataEntity.setGroup(groupEntity);
        groupMetaDataEntity.setId("groupMetaData1");
        groupMetaDataList.add(groupMetaDataEntity);
        when(groupDao.getMetaDataByGroupIdsAndKey(anyList(), anyString())).thenReturn(groupMetaDataList);

        // 调用方法
        List<CenterModel> result = userProvider.getCenterGroupByUserId(userId, allGroups);

        // 验证结果
        assertNotNull(result);
        Assert.assertEquals(1, result.size());
        Assert.assertEquals("center1", result.get(0).getId());
        Assert.assertEquals("Center 1", result.get(0).getName());
        Assert.assertEquals("agency1", result.get(0).getAgencyId());
        Assert.assertEquals("Agency 1", result.get(0).getAgencyName());
        Assert.assertEquals("group1", result.get(0).getGroups().get(0).getId());
        verify(userRepository, times(1)).findById(userId);
        verify(userDao, times(1)).getCenterGroupByAgencyUser(userId);
    }

    /**
     * 测试检查操作状态方法
     * case: 验证在导入操作进行中，应抛出操作唯一性异常
     */
    @Test
    public void testCheckOperateStatusWhenOperateInProgress() {
        // 参数准备
        String userId = "userId001"; // 当前用户 ID
        List<AgencyModel> agencies = new ArrayList<>();
        AgencyModel agencyModel = new AgencyModel(); // 创建当前机构对象
        agencyModel.setId("agencyId001");
        agencies.add(agencyModel);
        UserEntity user = new UserEntity(); // 创建当前用户对象
        user.setId(userId);
        user.setRole(String.valueOf(UserRole.AGENCY_OWNER));
        // 方法模拟
        when(userRepository.findById(userId)).thenReturn(Optional.of(user));
        when(userDao.getAgencyByAgencyAdminId(userId)).thenReturn(agencies);
        when(cacheService.exist("AGENCY_UNIQUE_OPERATE" + "-" + agencyModel.getId().trim().toUpperCase())).thenReturn(false);
        when(cacheService.exist("AGENCY_UNIQUE_OPERATE_IMPORT_EXECUTE" + "-" + agencyModel.getId().trim().toUpperCase())).thenReturn(true);

        // 调用测试方法
        BusinessException exception = org.junit.jupiter.api.Assertions.assertThrows(
                BusinessException.class,
                () -> userProvider.checkOperateStatus(userId)
        );
        // 验证结果
        Assert.assertEquals("OPERATE_IS_UNIQUE", exception.getErrorCode().getCode().toUpperCase()); // 验证错误码
    }

    /**
     * 测试转换学校班级方法
     */
    @Test
    public void testConvertCenterGroup() {
        // 准备测试数据
        List<CenterGroupModel> centerGroupList = new ArrayList<>();
        CenterGroupModel centerGroupModel = new CenterGroupModel();
        centerGroupModel.setCenterId("1");
        centerGroupModel.setCenterName("Center 1");
        centerGroupModel.setAgencyId("AG-001"); // 设置 AgencyId
        centerGroupList.add(centerGroupModel);

        // 调用被测试的方法
        List<CenterModel> result = userProvider.convertCenterGroup("OWNER", centerGroupList, false);

        // 断言结果是否符合预期
        assertEquals(1, result.size()); // 检查结果列表的大小为1，因为只有一个 CenterGroupModel 对象

        // 检查 AgencyId 是否被设置到 CenterModel 中
        assertEquals("AG-001", result.get(0).getAgencyId());
    }

}

package com.learninggenie.api.provider.impl;

import com.amazonaws.services.lambda.AWSLambdaAsyncClient;
import com.amazonaws.services.lambda.model.InvokeRequest;
import com.amazonaws.services.lambda.model.InvokeResult;
import com.amazonaws.services.sqs.AmazonSQSClient;
import com.amazonaws.services.stepfunctions.AWSStepFunctionsAsyncClient;
import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.exception.ErrorCode;
import com.learninggenie.common.cache.CacheService;
import com.learninggenie.common.data.dao.AgencyDao;
import com.learninggenie.common.data.entity.AgencyEntity;
import com.learninggenie.common.data.entity.MediaEntity;
import com.learninggenie.common.data.model.CreateMediaWithThumbnailRequest;
import com.learninggenie.common.region.RegionService;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;

import static org.mockito.Mockito.*;

public class RemoteProviderImplTest {
    /**
     * Method under test: {@link RemoteProviderImpl#callVideoCompressService(CreateMediaWithThumbnailRequest, MediaEntity)}
     */
    @Test
    public void testCallVideoCompressService() {
        UserProviderImpl userProviderImpl = mock(UserProviderImpl.class);
        when(userProviderImpl.getCurrentAgencyId()).thenReturn("42");
        RemoteProviderImpl remoteProviderImpl = new RemoteProviderImpl();
        ReflectionTestUtils.setField(remoteProviderImpl, "userProvider", userProviderImpl);
        ReflectionTestUtils.setField(remoteProviderImpl, "stepFunctionsAsyncClient",
                mock(AWSStepFunctionsAsyncClient.class));
        ReflectionTestUtils.setField(remoteProviderImpl, "sqsClient", mock(AmazonSQSClient.class));
        ReflectionTestUtils.setField(remoteProviderImpl, "restTemplate", mock(RestTemplate.class));
        ReflectionTestUtils.setField(remoteProviderImpl, "regionService", mock(RegionService.class));
        AWSLambdaAsyncClient awsLambdaAsyncClient = mock(AWSLambdaAsyncClient.class);
        when(awsLambdaAsyncClient.invokeAsync((com.amazonaws.services.lambda.model.InvokeRequest) any()))
                .thenReturn(new CompletableFuture<>());
        ReflectionTestUtils.setField(remoteProviderImpl, "lambdaAsyncClient", awsLambdaAsyncClient);
        ReflectionTestUtils.setField(remoteProviderImpl, "cacheService", mock(CacheService.class));

        AgencyEntity agencyEntity = new AgencyEntity();
        agencyEntity.setCenterId("42");
        agencyEntity.setCenterModels(new ArrayList<>());
        LocalDateTime atStartOfDayResult = LocalDate.of(1970, 1, 1).atStartOfDay();
        agencyEntity.setCreateAtUtc(Date.from(atStartOfDayResult.atZone(ZoneId.of("UTC")).toInstant()));
        agencyEntity.setDeleted(true);
        agencyEntity.setEnrollmentId("42");
        agencyEntity.setHidden(true);
        agencyEntity.setId("42");
        agencyEntity.setIsDeleted(true);
        agencyEntity.setLogoMediaId("42");
        agencyEntity.setName("Name");
        agencyEntity.setPartitionKey(1);
        agencyEntity.setState("MD");
        LocalDateTime atStartOfDayResult1 = LocalDate.of(1970, 1, 1).atStartOfDay();
        agencyEntity.setUpdateAtUtc(Date.from(atStartOfDayResult1.atZone(ZoneId.of("UTC")).toInstant()));
        agencyEntity.setUsers(new HashSet<>());
        AgencyDao agencyDao = mock(AgencyDao.class);
        when(agencyDao.getById((String) any())).thenReturn(agencyEntity);
        ReflectionTestUtils.setField(remoteProviderImpl, "agencyDao", agencyDao);

        CreateMediaWithThumbnailRequest createMediaWithThumbnailRequest = new CreateMediaWithThumbnailRequest();
        createMediaWithThumbnailRequest.setAgencyId("42");
        createMediaWithThumbnailRequest.setAnnexType("Annex Type");
        createMediaWithThumbnailRequest.setBase64_file("Base64 file");
        createMediaWithThumbnailRequest.setBase64_snapshot_file("Base64 snapshot file");
        createMediaWithThumbnailRequest.setDuration(10.0f);
        createMediaWithThumbnailRequest.setFileName("foo.txt");
        createMediaWithThumbnailRequest.setHeight(1);
        createMediaWithThumbnailRequest.setKey("Key");
        createMediaWithThumbnailRequest.setLocalId("42");
        createMediaWithThumbnailRequest.setMediaSource("Media Source");
        createMediaWithThumbnailRequest.setPrivateFile(true);
        createMediaWithThumbnailRequest.setSize(3L);
        createMediaWithThumbnailRequest.setType("Type");
        createMediaWithThumbnailRequest.setWidth(1);

        MediaEntity mediaEntity = new MediaEntity();
        mediaEntity.setAnnexType("Annex Type");
        mediaEntity.setCompressed(true);
        LocalDateTime atStartOfDayResult2 = LocalDate.of(1970, 1, 1).atStartOfDay();
        mediaEntity.setCreateAtUtc(Date.from(atStartOfDayResult2.atZone(ZoneId.of("UTC")).toInstant()));
        mediaEntity.setDynamicData("Dynamic Data");
        mediaEntity.setEventId("42");
        mediaEntity.setFileName("foo.txt");
        mediaEntity.setFileType("File Type");
        mediaEntity.setHaveMedium(true);
        mediaEntity.setHaveSmall(true);
        mediaEntity.setHeight(1);
        mediaEntity.setId("42");
        mediaEntity.setMediumImgPath("Medium Img Path");
        mediaEntity.setMimeType("Mime Type");
        mediaEntity.setPrivateFile(true);
        mediaEntity.setProcessedImgPath("Processed Img Path");
        mediaEntity.setRelativePath("Relative Path");
        mediaEntity.setSize(3L);
        mediaEntity.setSmallImgPath("Small Img Path");
        mediaEntity.setSnapshotPath("Snapshot Path");
        mediaEntity.setVoiceTime("Voice Time");
        mediaEntity.setWeb(true);
        mediaEntity.setWidth(1);
        remoteProviderImpl.callVideoCompressService(createMediaWithThumbnailRequest, mediaEntity);
        verify(userProviderImpl).getCurrentAgencyId();
        verify(awsLambdaAsyncClient).invokeAsync((com.amazonaws.services.lambda.model.InvokeRequest) any());
        verify(agencyDao).getById((String) any());
    }

    /**
     * Method under test: {@link RemoteProviderImpl#callVideoCompressService(CreateMediaWithThumbnailRequest, MediaEntity)}
     */
    @Test
    public void testCallVideoCompressService2() {
        UserProviderImpl userProviderImpl = mock(UserProviderImpl.class);
        when(userProviderImpl.getCurrentAgencyId()).thenReturn("42");
        RemoteProviderImpl remoteProviderImpl = new RemoteProviderImpl();
        ReflectionTestUtils.setField(remoteProviderImpl, "userProvider", userProviderImpl);
        ReflectionTestUtils.setField(remoteProviderImpl, "stepFunctionsAsyncClient",
                mock(AWSStepFunctionsAsyncClient.class));
        ReflectionTestUtils.setField(remoteProviderImpl, "sqsClient", mock(AmazonSQSClient.class));
        ReflectionTestUtils.setField(remoteProviderImpl, "restTemplate", mock(RestTemplate.class));
        ReflectionTestUtils.setField(remoteProviderImpl, "regionService", mock(RegionService.class));
        AWSLambdaAsyncClient awsLambdaAsyncClient = mock(AWSLambdaAsyncClient.class);
        when(awsLambdaAsyncClient.invokeAsync((com.amazonaws.services.lambda.model.InvokeRequest) any()))
                .thenThrow(new BusinessException(ErrorCode.INTERNAL_SERVER_ERROR));
        ReflectionTestUtils.setField(remoteProviderImpl, "lambdaAsyncClient", awsLambdaAsyncClient);
        ReflectionTestUtils.setField(remoteProviderImpl, "cacheService", mock(CacheService.class));

        AgencyEntity agencyEntity = new AgencyEntity();
        agencyEntity.setCenterId("42");
        agencyEntity.setCenterModels(new ArrayList<>());
        LocalDateTime atStartOfDayResult = LocalDate.of(1970, 1, 1).atStartOfDay();
        agencyEntity.setCreateAtUtc(Date.from(atStartOfDayResult.atZone(ZoneId.of("UTC")).toInstant()));
        agencyEntity.setDeleted(true);
        agencyEntity.setEnrollmentId("42");
        agencyEntity.setHidden(true);
        agencyEntity.setId("42");
        agencyEntity.setIsDeleted(true);
        agencyEntity.setLogoMediaId("42");
        agencyEntity.setName("Name");
        agencyEntity.setPartitionKey(1);
        agencyEntity.setState("MD");
        LocalDateTime atStartOfDayResult1 = LocalDate.of(1970, 1, 1).atStartOfDay();
        agencyEntity.setUpdateAtUtc(Date.from(atStartOfDayResult1.atZone(ZoneId.of("UTC")).toInstant()));
        agencyEntity.setUsers(new HashSet<>());
        AgencyDao agencyDao = mock(AgencyDao.class);
        when(agencyDao.getById((String) any())).thenReturn(agencyEntity);
        ReflectionTestUtils.setField(remoteProviderImpl, "agencyDao", agencyDao);

        CreateMediaWithThumbnailRequest createMediaWithThumbnailRequest = new CreateMediaWithThumbnailRequest();
        createMediaWithThumbnailRequest.setAgencyId("42");
        createMediaWithThumbnailRequest.setAnnexType("Annex Type");
        createMediaWithThumbnailRequest.setBase64_file("Base64 file");
        createMediaWithThumbnailRequest.setBase64_snapshot_file("Base64 snapshot file");
        createMediaWithThumbnailRequest.setDuration(10.0f);
        createMediaWithThumbnailRequest.setFileName("foo.txt");
        createMediaWithThumbnailRequest.setHeight(1);
        createMediaWithThumbnailRequest.setKey("Key");
        createMediaWithThumbnailRequest.setLocalId("42");
        createMediaWithThumbnailRequest.setMediaSource("Media Source");
        createMediaWithThumbnailRequest.setPrivateFile(true);
        createMediaWithThumbnailRequest.setSize(3L);
        createMediaWithThumbnailRequest.setType("Type");
        createMediaWithThumbnailRequest.setWidth(1);

        MediaEntity mediaEntity = new MediaEntity();
        mediaEntity.setAnnexType("Annex Type");
        mediaEntity.setCompressed(true);
        LocalDateTime atStartOfDayResult2 = LocalDate.of(1970, 1, 1).atStartOfDay();
        mediaEntity.setCreateAtUtc(Date.from(atStartOfDayResult2.atZone(ZoneId.of("UTC")).toInstant()));
        mediaEntity.setDynamicData("Dynamic Data");
        mediaEntity.setEventId("42");
        mediaEntity.setFileName("foo.txt");
        mediaEntity.setFileType("File Type");
        mediaEntity.setHaveMedium(true);
        mediaEntity.setHaveSmall(true);
        mediaEntity.setHeight(1);
        mediaEntity.setId("42");
        mediaEntity.setMediumImgPath("Medium Img Path");
        mediaEntity.setMimeType("Mime Type");
        mediaEntity.setPrivateFile(true);
        mediaEntity.setProcessedImgPath("Processed Img Path");
        mediaEntity.setRelativePath("Relative Path");
        mediaEntity.setSize(3L);
        mediaEntity.setSmallImgPath("Small Img Path");
        mediaEntity.setSnapshotPath("Snapshot Path");
        mediaEntity.setVoiceTime("Voice Time");
        mediaEntity.setWeb(true);
        mediaEntity.setWidth(1);
        remoteProviderImpl.callVideoCompressService(createMediaWithThumbnailRequest, mediaEntity);
        verify(userProviderImpl).getCurrentAgencyId();
        verify(awsLambdaAsyncClient, atLeast(1)).invokeAsync((com.amazonaws.services.lambda.model.InvokeRequest) any());
        verify(agencyDao).getById((String) any());
    }

    @Test
    public void testCallVideoCompressService3() {
        UserProviderImpl userProviderImpl = mock(UserProviderImpl.class);
        when(userProviderImpl.getCurrentAgencyId()).thenReturn("42");
        RemoteProviderImpl remoteProviderImpl = new RemoteProviderImpl();
        ReflectionTestUtils.setField(remoteProviderImpl, "userProvider", userProviderImpl);
        ReflectionTestUtils.setField(remoteProviderImpl, "stepFunctionsAsyncClient",
                mock(AWSStepFunctionsAsyncClient.class));
        ReflectionTestUtils.setField(remoteProviderImpl, "sqsClient", mock(AmazonSQSClient.class));
        ReflectionTestUtils.setField(remoteProviderImpl, "restTemplate", mock(RestTemplate.class));
        ReflectionTestUtils.setField(remoteProviderImpl, "regionService", mock(RegionService.class));
        AWSLambdaAsyncClient awsLambdaAsyncClient = mock(AWSLambdaAsyncClient.class);
        when(awsLambdaAsyncClient.invokeAsync((com.amazonaws.services.lambda.model.InvokeRequest) any()))
                .thenThrow(new BusinessException(ErrorCode.INTERNAL_SERVER_ERROR));
        ReflectionTestUtils.setField(remoteProviderImpl, "lambdaAsyncClient", awsLambdaAsyncClient);
        ReflectionTestUtils.setField(remoteProviderImpl, "cacheService", mock(CacheService.class));

        AgencyEntity agencyEntity = new AgencyEntity();
        agencyEntity.setCenterId("42");
        agencyEntity.setCenterModels(new ArrayList<>());
        LocalDateTime atStartOfDayResult = LocalDate.of(1970, 1, 1).atStartOfDay();
        agencyEntity.setCreateAtUtc(Date.from(atStartOfDayResult.atZone(ZoneId.of("UTC")).toInstant()));
        agencyEntity.setDeleted(true);
        agencyEntity.setEnrollmentId("42");
        agencyEntity.setHidden(true);
        agencyEntity.setId("42");
        agencyEntity.setIsDeleted(true);
        agencyEntity.setLogoMediaId("42");
        agencyEntity.setName("Name");
        agencyEntity.setPartitionKey(1);
        agencyEntity.setState("MD");
        LocalDateTime atStartOfDayResult1 = LocalDate.of(1970, 1, 1).atStartOfDay();
        agencyEntity.setUpdateAtUtc(Date.from(atStartOfDayResult1.atZone(ZoneId.of("UTC")).toInstant()));
        agencyEntity.setUsers(new HashSet<>());
        AgencyDao agencyDao = mock(AgencyDao.class);
        when(agencyDao.getById((String) any())).thenReturn(agencyEntity);
        ReflectionTestUtils.setField(remoteProviderImpl, "agencyDao", agencyDao);

        CreateMediaWithThumbnailRequest createMediaWithThumbnailRequest = new CreateMediaWithThumbnailRequest();
        createMediaWithThumbnailRequest.setAgencyId("42");
        createMediaWithThumbnailRequest.setAnnexType("Annex Type");
        createMediaWithThumbnailRequest.setBase64_file("Base64 file");
        createMediaWithThumbnailRequest.setBase64_snapshot_file("Base64 snapshot file");
        createMediaWithThumbnailRequest.setDuration(40.0f);
        createMediaWithThumbnailRequest.setFileName("foo.txt");
        createMediaWithThumbnailRequest.setHeight(1280);
        createMediaWithThumbnailRequest.setKey("Key");
        createMediaWithThumbnailRequest.setLocalId("42");
        createMediaWithThumbnailRequest.setMediaSource("Media Source");
        createMediaWithThumbnailRequest.setPrivateFile(true);
        createMediaWithThumbnailRequest.setSize(3L);
        createMediaWithThumbnailRequest.setType("Type");
        createMediaWithThumbnailRequest.setWidth(1920);

        MediaEntity mediaEntity = new MediaEntity();
        mediaEntity.setAnnexType("Annex Type");
        mediaEntity.setCompressed(true);
        LocalDateTime atStartOfDayResult2 = LocalDate.of(1970, 1, 1).atStartOfDay();
        mediaEntity.setCreateAtUtc(Date.from(atStartOfDayResult2.atZone(ZoneId.of("UTC")).toInstant()));
        mediaEntity.setDynamicData("Dynamic Data");
        mediaEntity.setEventId("42");
        mediaEntity.setFileName("foo.txt");
        mediaEntity.setFileType("File Type");
        mediaEntity.setHaveMedium(true);
        mediaEntity.setHaveSmall(true);
        mediaEntity.setHeight(1);
        mediaEntity.setId("42");
        mediaEntity.setMediumImgPath("Medium Img Path");
        mediaEntity.setMimeType("Mime Type");
        mediaEntity.setPrivateFile(true);
        mediaEntity.setProcessedImgPath("Processed Img Path");
        mediaEntity.setRelativePath("Relative Path");
        mediaEntity.setSize(3L);
        mediaEntity.setSmallImgPath("Small Img Path");
        mediaEntity.setSnapshotPath("Snapshot Path");
        mediaEntity.setVoiceTime("Voice Time");
        mediaEntity.setWeb(false);
        mediaEntity.setWidth(1280);
        remoteProviderImpl.callVideoCompressService(createMediaWithThumbnailRequest, mediaEntity);
        verify(userProviderImpl).getCurrentAgencyId();
        verify(awsLambdaAsyncClient, atMost(0)).invokeAsync((com.amazonaws.services.lambda.model.InvokeRequest) any());
        verify(agencyDao).getById((String) any());
    }

    @Test
    public void testCallExportSftpServer () {
        RemoteProviderImpl remoteProviderImpl = new RemoteProviderImpl();
        remoteProviderImpl.callExportSftpServer();;
    }

    /**
     * 测试 {@link RemoteProviderImpl#callPdfService(String, List)} 方法
     * case: 正常调用
     */
    @Test
    public void testCallPdfService() {
        RemoteProviderImpl remoteProvider = Mockito.spy(new RemoteProviderImpl()); // 模拟 remote provider
        AWSLambdaAsyncClient lambdaAsyncClient = Mockito.mock(AWSLambdaAsyncClient.class); // 模拟 lambda client
        Future<InvokeResult> mockFuture = Mockito.mock(Future.class); // 模拟 future
        Mockito.when(lambdaAsyncClient.invokeAsync(Mockito.any(InvokeRequest.class))).thenReturn(mockFuture); // 模拟 lambda client 的 invokeAsync 方法
        ReflectionTestUtils.setField(remoteProvider, "lambdaAsyncClient", lambdaAsyncClient); // 设置 lambda client
        ReflectionTestUtils.setField(remoteProvider, "pdfServerUrl", "pdfServerUrl"); // 设置 pdf server url
        ReflectionTestUtils.setField(remoteProvider, "regionService", mock(RegionService.class)); // 设置 region service

        String jobId = "123"; // 设置 jobId
        List<String> cmd = Arrays.asList("command1", "command2"); // 设置 cmd
        remoteProvider.callPdfService(jobId, cmd); // 调用方法

        Mockito.verify(lambdaAsyncClient).invokeAsync(Mockito.any(InvokeRequest.class)); // 验证 lambda client 是否调用 invokeAsync 方法
    }

    /**
     * 测试 {@link RemoteProviderImpl#callPdfService(String, List)} 方法
     * case: 中国区正常调用
     */
    @Test
    public void testCallPdfService2() {
        RemoteProviderImpl remoteProvider = Mockito.spy(new RemoteProviderImpl()); // 模拟 remote provider
        RestTemplate restTemplate = mock(RestTemplate.class); // 模拟 rest template
        ReflectionTestUtils.setField(remoteProvider, "restTemplate", restTemplate); // 设置 rest template
        ReflectionTestUtils.setField(remoteProvider, "pdfServerUrl", "pdfServerUrl"); // 设置 pdf server url
        RegionService regionService = mock(RegionService.class); // 模拟 region service
        when(regionService.isChina()).thenReturn(true); // 设置 region service 的 isChina 方法返回 true
        ReflectionTestUtils.setField(remoteProvider, "regionService", regionService); // 设置 region service

        String jobId = "123"; // 设置 jobId
        List<String> cmd = Arrays.asList("command1", "command2"); // 设置 cmd
        remoteProvider.callPdfService(jobId, cmd); // 调用方法

        Mockito.verify(restTemplate).exchange(anyString(), any(HttpMethod.class), any(HttpEntity.class), any(Class.class)); // 验证 rest template 是否调用 exchange 方法
    }

    /**
     * 测试 {@link RemoteProviderImpl#callPdfService(String, List, String)} 方法
     * case: 正常调用
     */
    @Test
    public void testCallPdfService3() {
        RemoteProviderImpl remoteProvider = Mockito.spy(new RemoteProviderImpl()); // 模拟 remote provider
        AWSLambdaAsyncClient lambdaAsyncClient = Mockito.mock(AWSLambdaAsyncClient.class); // 模拟 lambda client
        Future<InvokeResult> mockFuture = Mockito.mock(Future.class); // 模拟 future
        Mockito.when(lambdaAsyncClient.invokeAsync(Mockito.any(InvokeRequest.class))).thenReturn(mockFuture); // 模拟 lambda client 的 invokeAsync 方法
        ReflectionTestUtils.setField(remoteProvider, "lambdaAsyncClient", lambdaAsyncClient); // 设置 lambda client
        ReflectionTestUtils.setField(remoteProvider, "pdfServerUrl", "pdfServerUrl"); // 设置 pdf server url
        ReflectionTestUtils.setField(remoteProvider, "regionService", mock(RegionService.class)); // 设置 region service

        String jobId = "123"; // 设置 jobId
        List<String> cmd = Arrays.asList("command1", "command2"); // 设置 cmd
        remoteProvider.callPdfService(jobId, cmd, "password"); // 调用方法

        Mockito.verify(lambdaAsyncClient).invokeAsync(Mockito.any(InvokeRequest.class)); // 验证 lambda client 是否调用 invokeAsync 方法
    }
    /**
     * 测试 {@link RemoteProviderImpl#callPdfService(String, List, String)} 方法
     * case: 中国区正常调用
     */
    @Test
    public void testCallPdfService4() {
        RemoteProviderImpl remoteProvider = Mockito.spy(new RemoteProviderImpl()); // 模拟 remote provider
        RestTemplate restTemplate = mock(RestTemplate.class); // 模拟 rest template
        ReflectionTestUtils.setField(remoteProvider, "restTemplate", restTemplate); // 设置 rest template
        ReflectionTestUtils.setField(remoteProvider, "pdfServerUrl", "pdfServerUrl"); // 设置 pdf server url
        RegionService regionService = mock(RegionService.class); // 模拟 region service
        when(regionService.isChina()).thenReturn(true); // 设置 region service 的 isChina 方法返回 true
        ReflectionTestUtils.setField(remoteProvider, "regionService", regionService); // 设置 region service

        String jobId = "123"; // 设置 jobId
        List<String> cmd = Arrays.asList("command1", "command2"); // 设置 cmd
        remoteProvider.callPdfService(jobId, cmd, "password"); // 调用方法

        Mockito.verify(restTemplate).exchange(anyString(), any(HttpMethod.class), any(HttpEntity.class), any(Class.class)); // 验证 rest template 是否调用 exchange 方法
    }

}


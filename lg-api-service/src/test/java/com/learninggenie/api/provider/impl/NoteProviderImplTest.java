package com.learninggenie.api.provider.impl;

import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.model.note.NoteModel;
import com.learninggenie.api.provider.NoteProvider;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.service.PortfolioService;
import com.learninggenie.common.data.dao.*;
import com.learninggenie.common.data.dao.medias.MediaEntityDao;
import com.learninggenie.common.data.dao.notes.NoteLearningStoryDao;
import com.learninggenie.common.data.entity.ActionEntity;
import com.learninggenie.common.data.entity.NoteEntity;
import com.learninggenie.common.data.entity.notes.NoteLearningStoryEntity;
import com.learninggenie.common.data.model.DomainEntity;
import com.learninggenie.common.data.model.NoteMetaDataModel;
import com.learninggenie.common.data.model.ScoreTemplateEntity;
import com.learninggenie.common.data.model.note.*;
import com.learninggenie.common.filesystem.FileSystem;
import com.learninggenie.common.region.RegionService;
import com.learninggenie.common.utils.StringUtil;
import com.learninggenie.common.utils.TimeUtil;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * Created by w on 2017/6/8.
 */
@RunWith(MockitoJUnitRunner.class)
public class NoteProviderImplTest {
    @Mock
    private NoteDao noteDao;

    @Mock
    private DomainDao domainDao;

    @Mock
    private StudentDao studentDao;

    @Mock
    private TagDao tagDao;

    @Mock
    private RegionService regionService;

    @Mock
    private MediaDao mediaDao;

    @Mock
    private AgencyDao agencyDao;

    @Mock
    private LessonDao lessonDao;

    @Mock
    private UserProvider userProvider;

    @Mock
    private FileSystem fileSystem;

    @Mock
    private GroupDao groupDao;

    @Mock
    private MetaDao metaDao;

    @Mock
    private MediaEntityDao mediaEntityDao;

    @Mock
    private PortfolioService portfolioService;

    @Mock
    private NoteLearningStoryDao noteLearningStoryDao;

    @InjectMocks
    private NoteProvider noteProvider = new NoteProviderImpl();

    @Ignore
    @Test
    public void testGroupNotes() throws Exception {
        String groupId = "g001";
        String fromDate = "12/12/1111";
        String toDate = "12/12/1211";
        List<NoteEntity> noteEntities = new ArrayList<>();
        NoteEntity noteEntity = new NoteEntity();
        noteEntity.setId("n001");
        noteEntities.add(noteEntity);

        assertFalse(StringUtil.isEmptyOrBlank(groupId) || StringUtil.isEmptyOrBlank(fromDate) || StringUtil.isEmptyOrBlank(toDate));
        assertFalse(!TimeUtil.isValidDate(fromDate) || !TimeUtil.isValidDate(toDate));

        when(noteDao.getGroupNotes("Normal",groupId, fromDate, toDate)).thenReturn(noteEntities);

        noteProvider.getGroupNotes(groupId,fromDate,toDate, new ArrayList<String>(), new ArrayList<String>());

        verify(noteDao, times(1)).getGroupNotes(anyString(),anyString(), anyString(), anyString());

    }

    /**
     * 测试getGroupNotes 按时间段
     * 没有groupId,出现param_error
     */
    @Test(expected = BusinessException.class)
    public void testGetGroupNotes_WithoutGroupId() {
        String groupId = "";
        String fromDate = "2016-01-01";
        String toDate = "2016-02-02";
        List<String> domainIds = new ArrayList<>();
        List<String> tagIds = new ArrayList<>();

        noteProvider.getGroupNotes(groupId, fromDate, toDate, domainIds, tagIds);
    }

    /**
     * 测试getGroupNotes 按时间段
     * 没有fromDate,出现param_error
     */
    @Test(expected = BusinessException.class)
    public void testGetGroupNotes_WithoutFromDate() {
        String groupId = "g001";
        String fromDate = "";
        String toDate = "2016-02-01";
        List<String> domainIds = new ArrayList<>();
        List<String> tagIds = new ArrayList<>();

        noteProvider.getGroupNotes(groupId, fromDate, toDate, domainIds, tagIds);
    }

    /**
     * 测试getGroupNotes 按时间段
     * 没有toDate,出现param_error
     */
    @Test(expected = BusinessException.class)
    public void testGetGroupNotes_WithoutToDate() {
        String groupId = "g001";
        String fromDate = "2016-01-01";
        String toDate = "";
        List<String> domainIds = new ArrayList<>();
        List<String> tagIds = new ArrayList<>();

        noteProvider.getGroupNotes(groupId, fromDate, toDate, domainIds, tagIds);
    }

    /**
     * 测试getGroupNotes 按时间段
     * 没有fromDate出现日期无效日期
     */
    @Test(expected = BusinessException.class)
    public void testGetGroupNotes_FromDateFormatError() {
        String groupId = "g001";
        String fromDate = "2016-0c1-sd01";
        String toDate = "2016-01-01";
        List<String> domainIds = new ArrayList<>();
        List<String> tagIds = new ArrayList<>();

        noteProvider.getGroupNotes(groupId, fromDate, toDate, domainIds, tagIds);
    }

    /**
     * 测试getGroupNotes 按时间段
     * 没有toDate出现日期无效日期
     */
    @Test(expected = BusinessException.class)
    public void testGetGroupNotes_ToDateFormatError() {
        String groupId = "g001";
        String fromDate = "2016-01-01";
        String toDate = "2016";
        List<String> domainIds = new ArrayList<>();
        List<String> tagIds = new ArrayList<>();

        noteProvider.getGroupNotes(groupId, fromDate, toDate, domainIds, tagIds);
    }

    /**
     * 测试getGroupNotes 按时间段
     *本case包含一个note ，此note包含1个domain，1个media，1个tag
     */
    @Test
    @Ignore
    public void testGetGroupNotes() {
        String groupId = "g001";
        String fromDate = "2016-01-01";
        String toDate = "2016-01-02";
        List<String> domainIds = new ArrayList<>();
        List<String> tagIds = new ArrayList<>();

        NoteEntity note = new NoteEntity();
        note.setId("n001");
        note.setPayload("oh baby");
        note.setIsDeleted(false);
        note.setType("Normal");

        List<NoteEntity> noteEntities = new ArrayList<>();
        noteEntities.add(note);

        com.learninggenie.common.data.model.DomainEntity groupDomain = new com.learninggenie.common.data.model.DomainEntity();
        groupDomain.setId("gd001");
        groupDomain.setName("gd");

        com.learninggenie.common.data.model.DomainEntity childDomain = new com.learninggenie.common.data.model.DomainEntity();
        childDomain.setId("cd001");
        childDomain.setName("cd");

        List<DomainEntity> childDomains = new ArrayList<>();
        childDomains.add(childDomain);

        List<NoteDomainModel> noteDomainModels = new ArrayList<>();
        NoteDomainModel noteDomainModel = new NoteDomainModel();
        noteDomainModel.setId("d001");
        noteDomainModel.setNoteId("n001");
        noteDomainModels.add(noteDomainModel);

        List<NoteTagModel> noteTagModels = new ArrayList<>();
        NoteTagModel noteTagModel = new NoteTagModel();
        noteTagModel.setId("t001");
        noteTagModel.setNoteId("n001");
        noteTagModels.add(noteTagModel);

        List<ActionEntity> actionEntities = new ArrayList<>();
        ActionEntity actionEntity = new ActionEntity();
        actionEntity.setObjectId("n001");
        actionEntity.setId("a001");
        actionEntity.setAction("Create");
        actionEntity.setByUser("u001");
        actionEntity.setRole("AGENCY_OWNER");
        actionEntities.add(actionEntity);

        List<NoteMediaModel> noteMediaModels = new ArrayList<>();
        NoteMediaModel noteMediaModel = new NoteMediaModel();
        noteMediaModel.setId("m001");
        noteMediaModel.setNoteId("n001");
        noteMediaModels.add(noteMediaModel);

        when(noteDao.getGroupNotes("Normal", groupId, fromDate, toDate)).thenReturn(noteEntities);
        when(domainDao.getDomainByGroupId(groupId)).thenReturn(groupDomain);
        when(domainDao.getChildDomains(groupId)).thenReturn(childDomains);
        when(domainDao.getDomainByNoteIds(anyList())).thenReturn(noteDomainModels);
        when(tagDao.getTagByNoteIds(anyString())).thenReturn(noteTagModels);
        when(noteDao.getActionByNoteIds(anyList())).thenReturn(actionEntities);
        when(mediaDao.getMediaByNoteIds(anyString())).thenReturn(noteMediaModels);
        when(regionService.isChina()).thenReturn(false);
        List<NoteModel> noteModels = noteProvider.getGroupNotes(groupId, fromDate, toDate, domainIds, tagIds);

        verify(noteDao, times(1)).getGroupNotes(anyString(), anyString(), anyString(), anyString());
        verify(domainDao, times(1)).getDomainByGroupId(anyString());
        verify(domainDao, times(1)).getChildDomains(anyString());
        verify(domainDao, times(1)).getDomainByNoteIds(anyList());
        verify(tagDao, times(1)).getTagByNoteIds(anyString());
        verify(noteDao, times(1)).getActionByNoteIds(anyList());
        verify(mediaDao, times(1)).getMediaByNoteIds(anyString());

        Assert.assertEquals("n001", noteModels.get(0).getId());
        Assert.assertEquals("oh baby", noteModels.get(0).getPayload());
        Assert.assertEquals("d001", noteModels.get(0).getDomains().get(0).getId());
        Assert.assertEquals("t001", noteModels.get(0).getTags().get(0).getId());
        Assert.assertEquals("Create",noteModels.get(0).getActions().get(0).getAction());
        Assert.assertEquals("m001", noteModels.get(0).getMedias().get(0).getId());

    }

    /**
     * 测试getGroupNotes 按时间段
     *查询出的note为空，返回空数组
     */
    @Ignore
    @Test
    public void testGetGroupNotes_NoNotes() {
        String groupId = "g001";
        String fromDate = "2016-01-01";
        String toDate = "2016-01-02";
        List<String> domainIds = new ArrayList<>();
        List<String> tagIds = new ArrayList<>();

        NoteEntity note = new NoteEntity();
        note.setId("n001");
        note.setPayload("oh baby");
        note.setIsDeleted(false);
        note.setType("Normal");

        List<NoteEntity> noteEntities = new ArrayList<>();

        com.learninggenie.common.data.model.DomainEntity groupDomain = new com.learninggenie.common.data.model.DomainEntity();
        groupDomain.setId("gd001");
        groupDomain.setName("gd");

        com.learninggenie.common.data.model.DomainEntity childDomain = new com.learninggenie.common.data.model.DomainEntity();
        childDomain.setId("cd001");
        childDomain.setName("cd");

        List<DomainEntity> childDomains = new ArrayList<>();
        childDomains.add(childDomain);

        List<NoteDomainModel> noteDomainModels = new ArrayList<>();
        NoteDomainModel noteDomainModel = new NoteDomainModel();
        noteDomainModel.setId("d001");
        noteDomainModel.setNoteId("n001");
        noteDomainModels.add(noteDomainModel);

        List<NoteTagModel> noteTagModels = new ArrayList<>();
        NoteTagModel noteTagModel = new NoteTagModel();
        noteTagModel.setId("t001");
        noteTagModel.setNoteId("n001");
        noteTagModels.add(noteTagModel);

        List<ActionEntity> actionEntities = new ArrayList<>();
        ActionEntity actionEntity = new ActionEntity();
        actionEntity.setObjectId("n001");
        actionEntity.setId("a001");
        actionEntity.setAction("Create");
        actionEntity.setByUser("u001");
        actionEntities.add(actionEntity);

        List<NoteMediaModel> noteMediaModels = new ArrayList<>();
        NoteMediaModel noteMediaModel = new NoteMediaModel();
        noteMediaModel.setId("m001");
        noteMediaModel.setNoteId("n001");
        noteMediaModels.add(noteMediaModel);

        when(noteDao.getGroupNotes("Normal", groupId, fromDate, toDate)).thenReturn(noteEntities);
        when(domainDao.getDomainByGroupId(groupId)).thenReturn(groupDomain);
        when(domainDao.getChildDomains(groupId)).thenReturn(childDomains);
        when(domainDao.getDomainByNoteIds(anyList())).thenReturn(noteDomainModels);
        when(tagDao.getTagByNoteIds(anyString())).thenReturn(noteTagModels);
        when(noteDao.getActionByNoteIds(anyList())).thenReturn(actionEntities);
        when(mediaDao.getMediaByNoteIds(anyString())).thenReturn(noteMediaModels);

        List<NoteModel> noteModels = noteProvider.getGroupNotes(groupId, fromDate, toDate, domainIds, tagIds);

        assertTrue(noteModels.isEmpty());
        verify(noteDao, times(1)).getGroupNotes(anyString(), anyString(), anyString(), anyString());
        verify(domainDao, times(0)).getDomainByGroupId(anyString());
        verify(domainDao, times(0)).getChildDomains(anyString());
        verify(domainDao, times(0)).getDomainByNoteIds(anyList());
        verify(tagDao, times(0)).getTagByNoteIds(anyString());
        verify(noteDao, times(0)).getActionByNoteIds(anyList());
        verify(mediaDao, times(0)).getMediaByNoteIds(anyString());

    }

    /**
     * 测试getGroupNotes 按时间段
     *groupDomain为空
     */
    @Test
    @Ignore
    public void testGetGroupNotes_GroupDomainNull() {
        String groupId = "g001";
        String fromDate = "2016-01-01";
        String toDate = "2016-01-02";
        List<String> domainIds = new ArrayList<>();
        List<String> tagIds = new ArrayList<>();

        NoteEntity note = new NoteEntity();
        note.setId("n001");
        note.setPayload("oh baby");
        note.setIsDeleted(false);
        note.setType("Normal");

        List<NoteEntity> noteEntities = new ArrayList<>();
        noteEntities.add(note);

        com.learninggenie.common.data.model.DomainEntity groupDomain = new com.learninggenie.common.data.model.DomainEntity();

        com.learninggenie.common.data.model.DomainEntity childDomain = new com.learninggenie.common.data.model.DomainEntity();
        childDomain.setId("cd001");
        childDomain.setName("cd");

        List<DomainEntity> childDomains = new ArrayList<>();
        childDomains.add(childDomain);

        List<NoteDomainModel> noteDomainModels = new ArrayList<>();
        NoteDomainModel noteDomainModel = new NoteDomainModel();
        noteDomainModel.setId("d001");
        noteDomainModel.setNoteId("n001");
        noteDomainModels.add(noteDomainModel);

        List<NoteTagModel> noteTagModels = new ArrayList<>();
        NoteTagModel noteTagModel = new NoteTagModel();
        noteTagModel.setId("t001");
        noteTagModel.setNoteId("n001");
        noteTagModels.add(noteTagModel);

        List<ActionEntity> actionEntities = new ArrayList<>();
        ActionEntity actionEntity = new ActionEntity();
        actionEntity.setObjectId("n001");
        actionEntity.setId("a001");
        actionEntity.setAction("Create");
        actionEntity.setByUser("u001");
        actionEntity.setRole("AGENCY_OWNER");
        actionEntities.add(actionEntity);

        List<NoteMediaModel> noteMediaModels = new ArrayList<>();
        NoteMediaModel noteMediaModel = new NoteMediaModel();
        noteMediaModel.setId("m001");
        noteMediaModel.setNoteId("n001");
        noteMediaModels.add(noteMediaModel);

        when(noteDao.getGroupNotes("Normal", groupId, fromDate, toDate)).thenReturn(noteEntities);
        when(domainDao.getDomainByGroupId(groupId)).thenReturn(groupDomain);
        when(domainDao.getChildDomains(groupId)).thenReturn(childDomains);
        when(domainDao.getDomainByNoteIds(anyList())).thenReturn(noteDomainModels);
        when(tagDao.getTagByNoteIds(anyString())).thenReturn(noteTagModels);
        when(noteDao.getActionByNoteIds(anyList())).thenReturn(actionEntities);
        when(mediaDao.getMediaByNoteIds(anyString())).thenReturn(noteMediaModels);
        when(regionService.isChina()).thenReturn(false);

        List<NoteModel> noteModels = noteProvider.getGroupNotes(groupId, fromDate, toDate, domainIds, tagIds);

        verify(noteDao, times(1)).getGroupNotes(anyString(), anyString(), anyString(), anyString());
        verify(domainDao, times(1)).getDomainByGroupId(anyString());
        verify(domainDao, times(0)).getChildDomains(anyString());
        verify(domainDao, times(1)).getDomainByNoteIds(anyList());
        verify(tagDao, times(1)).getTagByNoteIds(anyString());
        verify(noteDao, times(1)).getActionByNoteIds(anyList());
        verify(mediaDao, times(1)).getMediaByNoteIds(anyString());

        Assert.assertEquals("n001", noteModels.get(0).getId());
        Assert.assertEquals("oh baby", noteModels.get(0).getPayload());
        Assert.assertEquals("d001", noteModels.get(0).getDomains().get(0).getId());
        Assert.assertEquals("t001", noteModels.get(0).getTags().get(0).getId());
        Assert.assertEquals("Create",noteModels.get(0).getActions().get(0).getAction());
        Assert.assertEquals("m001", noteModels.get(0).getMedias().get(0).getId());

    }

    /**
     * 测试getGroupNotes 按时间段
     *domainIds不为空,domainIds不包含数据库查到的domain
     */
    @Ignore
    @Test
    public void testGetGroupNotes_HasDomainIds() {
        String groupId = "g001";
        String fromDate = "2016-01-01";
        String toDate = "2016-01-02";
        List<String> domainIds = new ArrayList<>();
        List<String> tagIds = new ArrayList<>();

        domainIds.add("d002");

        NoteEntity note = new NoteEntity();
        note.setId("n001");
        note.setPayload("oh baby");
        note.setIsDeleted(false);
        note.setType("Normal");

        List<NoteEntity> noteEntities = new ArrayList<>();
        noteEntities.add(note);

        com.learninggenie.common.data.model.DomainEntity groupDomain = new com.learninggenie.common.data.model.DomainEntity();
        groupDomain.setId("gd001");
        groupDomain.setName("gd");

        com.learninggenie.common.data.model.DomainEntity childDomain = new com.learninggenie.common.data.model.DomainEntity();
        childDomain.setId("cd001");
        childDomain.setName("cd");

        List<DomainEntity> childDomains = new ArrayList<>();
        childDomains.add(childDomain);

        List<NoteDomainModel> noteDomainModels = new ArrayList<>();
        NoteDomainModel noteDomainModel = new NoteDomainModel();
        noteDomainModel.setId("d001");
        noteDomainModel.setNoteId("n001");
        noteDomainModels.add(noteDomainModel);

        List<NoteTagModel> noteTagModels = new ArrayList<>();
        NoteTagModel noteTagModel = new NoteTagModel();
        noteTagModel.setId("t001");
        noteTagModel.setNoteId("n001");
        noteTagModels.add(noteTagModel);

        List<ActionEntity> actionEntities = new ArrayList<>();
        ActionEntity actionEntity = new ActionEntity();
        actionEntity.setObjectId("n001");
        actionEntity.setId("a001");
        actionEntity.setAction("Create");
        actionEntity.setByUser("u001");
        actionEntities.add(actionEntity);

        List<NoteMediaModel> noteMediaModels = new ArrayList<>();
        NoteMediaModel noteMediaModel = new NoteMediaModel();
        noteMediaModel.setId("m001");
        noteMediaModel.setNoteId("n001");
        noteMediaModels.add(noteMediaModel);

        when(noteDao.getGroupNotes("Normal", groupId, fromDate, toDate)).thenReturn(noteEntities);
        when(domainDao.getDomainByGroupId(groupId)).thenReturn(groupDomain);
        when(domainDao.getChildDomains(groupId)).thenReturn(childDomains);
        when(domainDao.getDomainByNoteIds(anyList())).thenReturn(noteDomainModels);
        when(tagDao.getTagByNoteIds(anyString())).thenReturn(noteTagModels);
        when(noteDao.getActionByNoteIds(anyList())).thenReturn(actionEntities);
        when(mediaDao.getMediaByNoteIds(anyString())).thenReturn(noteMediaModels);

        List<NoteModel> noteModels = noteProvider.getGroupNotes(groupId, fromDate, toDate, domainIds, tagIds);

        verify(noteDao, times(1)).getGroupNotes(anyString(), anyString(), anyString(), anyString());
        verify(domainDao, times(1)).getDomainByGroupId(anyString());
        verify(domainDao, times(1)).getChildDomains(anyString());
        verify(domainDao, times(1)).getDomainByNoteIds(anyList());
        verify(tagDao, times(1)).getTagByNoteIds(anyString());
        verify(noteDao, times(1)).getActionByNoteIds(anyList());
        verify(mediaDao, times(1)).getMediaByNoteIds(anyString());

        Assert.assertTrue(noteModels.isEmpty());

    }

    /**
     * 测试getGroupNotes 按时间段
     *tagIds不为空,tagIds不包含数据库查到的tag
     */
    @Ignore
    @Test
    public void testGetGroupNotes_HasTagIds() {
        String groupId = "g001";
        String fromDate = "2016-01-01";
        String toDate = "2016-01-02";
        List<String> domainIds = new ArrayList<>();
        List<String> tagIds = new ArrayList<>();

        tagIds.add("t002");

        NoteEntity note = new NoteEntity();
        note.setId("n001");
        note.setPayload("oh baby");
        note.setIsDeleted(false);
        note.setType("Normal");

        List<NoteEntity> noteEntities = new ArrayList<>();
        noteEntities.add(note);

        com.learninggenie.common.data.model.DomainEntity groupDomain = new com.learninggenie.common.data.model.DomainEntity();
        groupDomain.setId("gd001");
        groupDomain.setName("gd");

        com.learninggenie.common.data.model.DomainEntity childDomain = new com.learninggenie.common.data.model.DomainEntity();
        childDomain.setId("cd001");
        childDomain.setName("cd");

        List<DomainEntity> childDomains = new ArrayList<>();
        childDomains.add(childDomain);

        List<NoteDomainModel> noteDomainModels = new ArrayList<>();
        NoteDomainModel noteDomainModel = new NoteDomainModel();
        noteDomainModel.setId("d001");
        noteDomainModel.setNoteId("n001");
        noteDomainModels.add(noteDomainModel);

        List<NoteTagModel> noteTagModels = new ArrayList<>();
        NoteTagModel noteTagModel = new NoteTagModel();
        noteTagModel.setId("t001");
        noteTagModel.setNoteId("n001");
        noteTagModels.add(noteTagModel);

        List<ActionEntity> actionEntities = new ArrayList<>();
        ActionEntity actionEntity = new ActionEntity();
        actionEntity.setObjectId("n001");
        actionEntity.setId("a001");
        actionEntity.setAction("Create");
        actionEntity.setByUser("u001");
        actionEntities.add(actionEntity);

        List<NoteMediaModel> noteMediaModels = new ArrayList<>();
        NoteMediaModel noteMediaModel = new NoteMediaModel();
        noteMediaModel.setId("m001");
        noteMediaModel.setNoteId("n001");
        noteMediaModels.add(noteMediaModel);

        when(noteDao.getGroupNotes("Normal", groupId, fromDate, toDate)).thenReturn(noteEntities);
        when(domainDao.getDomainByGroupId(groupId)).thenReturn(groupDomain);
        when(domainDao.getChildDomains(groupId)).thenReturn(childDomains);
        when(domainDao.getDomainByNoteIds(anyList())).thenReturn(noteDomainModels);
        when(tagDao.getTagByNoteIds(anyString())).thenReturn(noteTagModels);
        when(noteDao.getActionByNoteIds(anyList())).thenReturn(actionEntities);
        when(mediaDao.getMediaByNoteIds(anyString())).thenReturn(noteMediaModels);

        List<NoteModel> noteModels = noteProvider.getGroupNotes(groupId, fromDate, toDate, domainIds, tagIds);

        verify(noteDao, times(1)).getGroupNotes(anyString(), anyString(), anyString(), anyString());
        verify(domainDao, times(1)).getDomainByGroupId(anyString());
        verify(domainDao, times(1)).getChildDomains(anyString());
        verify(domainDao, times(1)).getDomainByNoteIds(anyList());
        verify(tagDao, times(1)).getTagByNoteIds(anyString());
        verify(noteDao, times(1)).getActionByNoteIds(anyList());
        verify(mediaDao, times(1)).getMediaByNoteIds(anyString());

        Assert.assertTrue(noteModels.isEmpty());

    }

    /**
     * 测试getGroupNotes 按时间段
     *domainIds不为空,domainIds包含数据库查到的domain
     */
    @Test
    @Ignore
    public void testGetGroupNotes_HasDomainIdsIncludeDomainIds() {
        String groupId = "g001";
        String fromDate = "2016-01-01";
        String toDate = "2016-01-02";
        List<String> domainIds = new ArrayList<>();
        List<String> tagIds = new ArrayList<>();

        domainIds.add("d001");
        domainIds.add("d002");

        NoteEntity note = new NoteEntity();
        note.setId("n001");
        note.setPayload("oh baby");
        note.setIsDeleted(false);
        note.setType("Normal");

        List<NoteEntity> noteEntities = new ArrayList<>();
        noteEntities.add(note);

        com.learninggenie.common.data.model.DomainEntity groupDomain = new com.learninggenie.common.data.model.DomainEntity();
        groupDomain.setId("gd001");
        groupDomain.setName("gd");

        com.learninggenie.common.data.model.DomainEntity childDomain = new com.learninggenie.common.data.model.DomainEntity();
        childDomain.setId("cd001");
        childDomain.setName("cd");

        List<DomainEntity> childDomains = new ArrayList<>();
        childDomains.add(childDomain);

        List<NoteDomainModel> noteDomainModels = new ArrayList<>();
        NoteDomainModel noteDomainModel = new NoteDomainModel();
        noteDomainModel.setId("d001");
        noteDomainModel.setNoteId("n001");
        noteDomainModels.add(noteDomainModel);

        List<NoteTagModel> noteTagModels = new ArrayList<>();
        NoteTagModel noteTagModel = new NoteTagModel();
        noteTagModel.setId("t001");
        noteTagModel.setNoteId("n001");
        noteTagModels.add(noteTagModel);

        List<ActionEntity> actionEntities = new ArrayList<>();
        ActionEntity actionEntity = new ActionEntity();
        actionEntity.setObjectId("n001");
        actionEntity.setId("a001");
        actionEntity.setAction("Create");
        actionEntity.setByUser("u001");
        actionEntity.setRole("AGENCY_OWNER");
        actionEntities.add(actionEntity);

        List<NoteMediaModel> noteMediaModels = new ArrayList<>();
        NoteMediaModel noteMediaModel = new NoteMediaModel();
        noteMediaModel.setId("m001");
        noteMediaModel.setNoteId("n001");
        noteMediaModels.add(noteMediaModel);

        when(noteDao.getGroupNotes("Normal", groupId, fromDate, toDate)).thenReturn(noteEntities);
        when(domainDao.getDomainByGroupId(groupId)).thenReturn(groupDomain);
        when(domainDao.getChildDomains(groupId)).thenReturn(childDomains);
        when(domainDao.getDomainByNoteIds(anyList())).thenReturn(noteDomainModels);
        when(tagDao.getTagByNoteIds(anyString())).thenReturn(noteTagModels);
        when(noteDao.getActionByNoteIds(anyList())).thenReturn(actionEntities);
        when(mediaDao.getMediaByNoteIds(anyString())).thenReturn(noteMediaModels);
        when(regionService.isChina()).thenReturn(false);

        List<NoteModel> noteModels = noteProvider.getGroupNotes(groupId, fromDate, toDate, domainIds, tagIds);

        verify(noteDao, times(1)).getGroupNotes(anyString(), anyString(), anyString(), anyString());
        verify(domainDao, times(1)).getDomainByGroupId(anyString());
        verify(domainDao, times(1)).getChildDomains(anyString());
        verify(domainDao, times(1)).getDomainByNoteIds(anyList());
        verify(tagDao, times(1)).getTagByNoteIds(anyString());
        verify(noteDao, times(1)).getActionByNoteIds(anyList());
        verify(mediaDao, times(1)).getMediaByNoteIds(anyString());

        Assert.assertEquals("n001", noteModels.get(0).getId());
        Assert.assertEquals("oh baby", noteModels.get(0).getPayload());
        Assert.assertEquals("d001", noteModels.get(0).getDomains().get(0).getId());
        Assert.assertEquals("t001", noteModels.get(0).getTags().get(0).getId());
        Assert.assertEquals("Create",noteModels.get(0).getActions().get(0).getAction());
        Assert.assertEquals("m001", noteModels.get(0).getMedias().get(0).getId());

    }

    /**
     * 测试getGroupNotes 按时间段
     *domainIds不为空,domainIds包含数据库查到的domain
     */
    @Ignore
    @Test
    public void testGetGroupNotes_HasTagIdsIncludeTagIds() {
        String groupId = "g001";
        String fromDate = "2016-01-01";
        String toDate = "2016-01-02";
        List<String> domainIds = new ArrayList<>();
        List<String> tagIds = new ArrayList<>();

        tagIds.add("t001");
        tagIds.add("t002");

        NoteEntity note = new NoteEntity();
        note.setId("n001");
        note.setPayload("oh baby");
        note.setIsDeleted(false);
        note.setType("Normal");

        List<NoteEntity> noteEntities = new ArrayList<>();
        noteEntities.add(note);

        com.learninggenie.common.data.model.DomainEntity groupDomain = new com.learninggenie.common.data.model.DomainEntity();
        groupDomain.setId("gd001");
        groupDomain.setName("gd");

        com.learninggenie.common.data.model.DomainEntity childDomain = new com.learninggenie.common.data.model.DomainEntity();
        childDomain.setId("cd001");
        childDomain.setName("cd");

        List<DomainEntity> childDomains = new ArrayList<>();
        childDomains.add(childDomain);

        List<NoteDomainModel> noteDomainModels = new ArrayList<>();
        NoteDomainModel noteDomainModel = new NoteDomainModel();
        noteDomainModel.setId("d001");
        noteDomainModel.setNoteId("n001");
        noteDomainModels.add(noteDomainModel);

        List<NoteTagModel> noteTagModels = new ArrayList<>();
        NoteTagModel noteTagModel = new NoteTagModel();
        noteTagModel.setId("t001");
        noteTagModel.setNoteId("n001");
        noteTagModels.add(noteTagModel);

        List<ActionEntity> actionEntities = new ArrayList<>();
        ActionEntity actionEntity = new ActionEntity();
        actionEntity.setObjectId("n001");
        actionEntity.setId("a001");
        actionEntity.setAction("Create");
        actionEntity.setByUser("u001");
        actionEntity.setRole("AGENCY_OWNER");
        actionEntities.add(actionEntity);

        List<NoteMediaModel> noteMediaModels = new ArrayList<>();
        NoteMediaModel noteMediaModel = new NoteMediaModel();
        noteMediaModel.setId("m001");
        noteMediaModel.setNoteId("n001");
        noteMediaModels.add(noteMediaModel);

        when(noteDao.getGroupNotes("Normal", groupId, fromDate, toDate)).thenReturn(noteEntities);
        when(domainDao.getDomainByGroupId(groupId)).thenReturn(groupDomain);
        when(domainDao.getChildDomains(groupId)).thenReturn(childDomains);
        when(domainDao.getDomainByNoteIds(anyList())).thenReturn(noteDomainModels);
        when(tagDao.getTagByNoteIds(anyString())).thenReturn(noteTagModels);
        when(noteDao.getActionByNoteIds(anyList())).thenReturn(actionEntities);
        when(mediaDao.getMediaByNoteIds(anyString())).thenReturn(noteMediaModels);
        when(regionService.isChina()).thenReturn(false);
        List<NoteModel> noteModels = noteProvider.getGroupNotes(groupId, fromDate, toDate, domainIds, tagIds);

        verify(noteDao, times(1)).getGroupNotes(anyString(), anyString(), anyString(), anyString());
        verify(domainDao, times(1)).getDomainByGroupId(anyString());
        verify(domainDao, times(1)).getChildDomains(anyString());
        verify(domainDao, times(1)).getDomainByNoteIds(anyList());
        verify(tagDao, times(1)).getTagByNoteIds(anyString());
        verify(noteDao, times(1)).getActionByNoteIds(anyList());
        verify(mediaDao, times(1)).getMediaByNoteIds(anyString());

        Assert.assertEquals("n001", noteModels.get(0).getId());
        Assert.assertEquals("oh baby", noteModels.get(0).getPayload());
        Assert.assertEquals("d001", noteModels.get(0).getDomains().get(0).getId());
        Assert.assertEquals("t001", noteModels.get(0).getTags().get(0).getId());
        Assert.assertEquals("Create",noteModels.get(0).getActions().get(0).getAction());
        Assert.assertEquals("m001", noteModels.get(0).getMedias().get(0).getId());

    }

    /**
     * 测试getCenterNotes 按时间段
     * 没有centerId,出现param_error
     */
    @Test(expected = BusinessException.class)
    public void testGetCenterNotes_WithoutCenterId() {
        String centerId = "";
        String fromDate = "2016-01-01";
        String toDate = "2016-02-02";
        List<String> domainIds = new ArrayList<>();
        List<String> tagIds = new ArrayList<>();

        noteProvider.getCenterNotes(centerId, fromDate, toDate, domainIds, tagIds);
    }

    /**
     * 测试getCenterNotes 按时间段
     * 没有centerId,出现param_error
     */
    @Test(expected = BusinessException.class)
    public void testGetCenterNotes_WithoutFromDate() {
        String centerId = "c001";
        String fromDate = "";
        String toDate = "2016-02-02";
        List<String> domainIds = new ArrayList<>();
        List<String> tagIds = new ArrayList<>();

        noteProvider.getCenterNotes(centerId, fromDate, toDate, domainIds, tagIds);
    }

    /**
     * 测试getCenterNotes 按时间段
     * 没有centerId,出现param_error
     */
    @Test(expected = BusinessException.class)
    public void testGetCenterNotes_WithoutToDate() {
        String centerId = "c001";
        String fromDate = "2016-01-01";
        String toDate = "";
        List<String> domainIds = new ArrayList<>();
        List<String> tagIds = new ArrayList<>();

        noteProvider.getCenterNotes(centerId, fromDate, toDate, domainIds, tagIds);
    }

    /**
     * 测试getCenterNotes 按时间段
     * 没有fromDate出现日期无效日期
     */
    @Test(expected = BusinessException.class)
    public void testGetCenterNotes_FromDateFormatError() {
        String centerId = "c001";
        String fromDate = "2016-0c1-sd01";
        String toDate = "2016-01-01";
        List<String> domainIds = new ArrayList<>();
        List<String> tagIds = new ArrayList<>();

        noteProvider.getCenterNotes(centerId, fromDate, toDate, domainIds, tagIds);
    }

    /**
     * 测试getCenterNotes 按时间段
     * 没有toDate出现日期无效日期
     */
    @Test(expected = BusinessException.class)
    public void testGetCenterNotes_ToDateFormatError() {
        String groupId = "c001";
        String fromDate = "2016-01-01";
        String toDate = "2016";
        List<String> domainIds = new ArrayList<>();
        List<String> tagIds = new ArrayList<>();

        noteProvider.getCenterNotes(groupId, fromDate, toDate, domainIds, tagIds);
    }

    /**
     * 测试getCenterNotes 按时间段
     *本case包含一个note ，此note包含1个domain，1个media，1个tag
     */
    @Test
    @Ignore
    public void testGetCenterNotes() {
        String centerId = "c001";
        String fromDate = "2016-01-01";
        String toDate = "2016-01-02";
        List<String> domainIds = new ArrayList<>();
        List<String> tagIds = new ArrayList<>();

        NoteEntity note = new NoteEntity();
        note.setId("n001");
        note.setPayload("oh baby");
        note.setIsDeleted(false);
        note.setType("Normal");

        List<NoteEntity> noteEntities = new ArrayList<>();
        noteEntities.add(note);

        com.learninggenie.common.data.model.DomainEntity centerDomain = new com.learninggenie.common.data.model.DomainEntity();
        centerDomain.setId("cd001");
        centerDomain.setName("cd");

        com.learninggenie.common.data.model.DomainEntity childDomain = new com.learninggenie.common.data.model.DomainEntity();
        childDomain.setId("cd001");
        childDomain.setName("cd");

        List<DomainEntity> childDomains = new ArrayList<>();
        childDomains.add(childDomain);

        List<NoteDomainModel> noteDomainModels = new ArrayList<>();
        NoteDomainModel noteDomainModel = new NoteDomainModel();
        noteDomainModel.setId("d001");
        noteDomainModel.setNoteId("n001");
        noteDomainModels.add(noteDomainModel);

        List<NoteTagModel> noteTagModels = new ArrayList<>();
        NoteTagModel noteTagModel = new NoteTagModel();
        noteTagModel.setId("t001");
        noteTagModel.setNoteId("n001");
        noteTagModels.add(noteTagModel);

        List<ActionEntity> actionEntities = new ArrayList<>();
        ActionEntity actionEntity = new ActionEntity();
        actionEntity.setObjectId("n001");
        actionEntity.setId("a001");
        actionEntity.setAction("Create");
        actionEntity.setByUser("u001");
        actionEntity.setRole("AGENCY_OWNER");
        actionEntities.add(actionEntity);

        List<NoteMediaModel> noteMediaModels = new ArrayList<>();
        NoteMediaModel noteMediaModel = new NoteMediaModel();
        noteMediaModel.setId("m001");
        noteMediaModel.setNoteId("n001");
        noteMediaModels.add(noteMediaModel);

        when(noteDao.getCenterNotes("Normal", centerId, fromDate, toDate)).thenReturn(noteEntities);
        when(domainDao.getDomainByCenterId(centerId)).thenReturn(centerDomain);
        when(domainDao.getChildDomains(centerDomain.getId())).thenReturn(childDomains);
        when(domainDao.getDomainByNoteIds(anyList())).thenReturn(noteDomainModels);
        when(tagDao.getTagByNoteIds(anyString())).thenReturn(noteTagModels);
        when(noteDao.getActionByNoteIds(anyList())).thenReturn(actionEntities);
        when(mediaDao.getMediaByNoteIds(anyString())).thenReturn(noteMediaModels);
        when(regionService.isChina()).thenReturn(false);

        List<NoteModel> noteModels = noteProvider.getCenterNotes(centerId, fromDate, toDate, domainIds, tagIds);

        verify(noteDao, times(1)).getCenterNotes(anyString(), anyString(), anyString(), anyString());
        verify(domainDao, times(1)).getDomainByCenterId(anyString());
        verify(domainDao, times(1)).getChildDomains(anyString());
        verify(domainDao, times(1)).getDomainByNoteIds(anyList());
        verify(tagDao, times(1)).getTagByNoteIds(anyString());
        verify(noteDao, times(1)).getActionByNoteIds(anyList());
        verify(mediaDao, times(1)).getMediaByNoteIds(anyString());

        Assert.assertEquals("n001", noteModels.get(0).getId());
        Assert.assertEquals("oh baby", noteModels.get(0).getPayload());
        Assert.assertEquals("d001", noteModels.get(0).getDomains().get(0).getId());
        Assert.assertEquals("t001", noteModels.get(0).getTags().get(0).getId());
        Assert.assertEquals("Create",noteModels.get(0).getActions().get(0).getAction());
        Assert.assertEquals("m001", noteModels.get(0).getMedias().get(0).getId());

    }

    /**
     * 测试getCenterNotes 按时间段
     *case:查出的domainEntities为空
     */
    @Ignore
    @Test
    public void testGetCenterNotes_CenterDomainNull() {
        String centerId = "c001";
        String fromDate = "2016-01-01";
        String toDate = "2016-01-02";
        List<String> domainIds = new ArrayList<>();
        List<String> tagIds = new ArrayList<>();

        NoteEntity note = new NoteEntity();
        note.setId("n001");
        note.setPayload("oh baby");
        note.setIsDeleted(false);
        note.setType("Normal");

        List<NoteEntity> noteEntities = new ArrayList<>();

        com.learninggenie.common.data.model.DomainEntity centerDomain = new com.learninggenie.common.data.model.DomainEntity();
        centerDomain.setId("cd001");
        centerDomain.setName("cd");

        com.learninggenie.common.data.model.DomainEntity childDomain = new com.learninggenie.common.data.model.DomainEntity();
        childDomain.setId("cd001");
        childDomain.setName("cd");

        List<DomainEntity> childDomains = new ArrayList<>();
        childDomains.add(childDomain);

        List<NoteDomainModel> noteDomainModels = new ArrayList<>();
        NoteDomainModel noteDomainModel = new NoteDomainModel();
        noteDomainModel.setId("d001");
        noteDomainModel.setNoteId("n001");
        noteDomainModels.add(noteDomainModel);

        List<NoteTagModel> noteTagModels = new ArrayList<>();
        NoteTagModel noteTagModel = new NoteTagModel();
        noteTagModel.setId("t001");
        noteTagModel.setNoteId("n001");
        noteTagModels.add(noteTagModel);

        List<ActionEntity> actionEntities = new ArrayList<>();
        ActionEntity actionEntity = new ActionEntity();
        actionEntity.setObjectId("n001");
        actionEntity.setId("a001");
        actionEntity.setAction("Create");
        actionEntity.setByUser("u001");
        actionEntities.add(actionEntity);

        List<NoteMediaModel> noteMediaModels = new ArrayList<>();
        NoteMediaModel noteMediaModel = new NoteMediaModel();
        noteMediaModel.setId("m001");
        noteMediaModel.setNoteId("n001");
        noteMediaModels.add(noteMediaModel);

        when(noteDao.getCenterNotes("Normal", centerId, fromDate, toDate)).thenReturn(noteEntities);
        when(domainDao.getDomainByCenterId(centerId)).thenReturn(centerDomain);
        when(domainDao.getChildDomains(centerDomain.getId())).thenReturn(childDomains);
        when(domainDao.getDomainByNoteIds(anyList())).thenReturn(noteDomainModels);
        when(tagDao.getTagByNoteIds(anyString())).thenReturn(noteTagModels);
        when(noteDao.getActionByNoteIds(anyList())).thenReturn(actionEntities);
        when(mediaDao.getMediaByNoteIds(anyString())).thenReturn(noteMediaModels);

        noteProvider.getCenterNotes(centerId, fromDate, toDate, domainIds, tagIds);

        verify(noteDao, times(1)).getCenterNotes(anyString(), anyString(), anyString(), anyString());
        verify(domainDao, times(0)).getDomainByCenterId(anyString());
        verify(domainDao, times(0)).getChildDomains(anyString());
        verify(domainDao, times(0)).getDomainByNoteIds(anyList());
        verify(tagDao, times(0)).getTagByNoteIds(anyString());
        verify(noteDao, times(0)).getActionByNoteIds(anyList());
        verify(mediaDao, times(0)).getMediaByNoteIds(anyString());
    }

    /**
     * 测试getCenterNotes 按时间段
     *domainIds不为空,domainIds不包含数据库查到的domain
     */
    @Ignore
    @Test
    public void testGetCenterNotes_HasDomainIds() {
        String centerId = "c001";
        String fromDate = "2016-01-01";
        String toDate = "2016-01-02";
        List<String> domainIds = new ArrayList<>();
        List<String> tagIds = new ArrayList<>();

        domainIds.add("d002");

        NoteEntity note = new NoteEntity();
        note.setId("n001");
        note.setPayload("oh baby");
        note.setIsDeleted(false);
        note.setType("Normal");

        List<NoteEntity> noteEntities = new ArrayList<>();
        noteEntities.add(note);

        com.learninggenie.common.data.model.DomainEntity centerDomain = new com.learninggenie.common.data.model.DomainEntity();
        centerDomain.setId("cd001");
        centerDomain.setName("cd");

        com.learninggenie.common.data.model.DomainEntity childDomain = new com.learninggenie.common.data.model.DomainEntity();
        childDomain.setId("chd001");
        childDomain.setName("cd");

        List<DomainEntity> childDomains = new ArrayList<>();
        childDomains.add(childDomain);

        List<NoteDomainModel> noteDomainModels = new ArrayList<>();
        NoteDomainModel noteDomainModel = new NoteDomainModel();
        noteDomainModel.setId("d001");
        noteDomainModel.setNoteId("n001");
        noteDomainModels.add(noteDomainModel);

        List<NoteTagModel> noteTagModels = new ArrayList<>();
        NoteTagModel noteTagModel = new NoteTagModel();
        noteTagModel.setId("t001");
        noteTagModel.setNoteId("n001");
        noteTagModels.add(noteTagModel);

        List<ActionEntity> actionEntities = new ArrayList<>();
        ActionEntity actionEntity = new ActionEntity();
        actionEntity.setObjectId("n001");
        actionEntity.setId("a001");
        actionEntity.setAction("Create");
        actionEntity.setByUser("u001");
        actionEntities.add(actionEntity);

        List<NoteMediaModel> noteMediaModels = new ArrayList<>();
        NoteMediaModel noteMediaModel = new NoteMediaModel();
        noteMediaModel.setId("m001");
        noteMediaModel.setNoteId("n001");
        noteMediaModels.add(noteMediaModel);

        when(noteDao.getCenterNotes("Normal", centerId, fromDate, toDate)).thenReturn(noteEntities);
        when(domainDao.getDomainByCenterId(centerId)).thenReturn(centerDomain);
        when(domainDao.getChildDomains(centerId)).thenReturn(childDomains);
        when(domainDao.getDomainByNoteIds(anyList())).thenReturn(noteDomainModels);
        when(tagDao.getTagByNoteIds(anyString())).thenReturn(noteTagModels);
        when(noteDao.getActionByNoteIds(anyList())).thenReturn(actionEntities);
        when(mediaDao.getMediaByNoteIds(anyString())).thenReturn(noteMediaModels);

        List<NoteModel> noteModels = noteProvider.getCenterNotes(centerId, fromDate, toDate, domainIds, tagIds);

        verify(noteDao, times(1)).getCenterNotes(anyString(), anyString(), anyString(), anyString());
        verify(domainDao, times(1)).getDomainByCenterId(anyString());
        verify(domainDao, times(1)).getChildDomains(anyString());
        verify(domainDao, times(1)).getDomainByNoteIds(anyList());
        verify(tagDao, times(1)).getTagByNoteIds(anyString());
        verify(noteDao, times(1)).getActionByNoteIds(anyList());
        verify(mediaDao, times(1)).getMediaByNoteIds(anyString());

        Assert.assertTrue(noteModels.isEmpty());

    }

    /**
     * 测试getCenterNotes 按时间段
     *tagIds不为空,tagIds不包含数据库查到的tag
     */
    @Ignore
    @Test
    public void testGetCenterNotes_HasTagIds() {
        String groupId = "g001";
        String fromDate = "2016-01-01";
        String toDate = "2016-01-02";
        List<String> domainIds = new ArrayList<>();
        List<String> tagIds = new ArrayList<>();

        tagIds.add("t002");

        NoteEntity note = new NoteEntity();
        note.setId("n001");
        note.setPayload("oh baby");
        note.setIsDeleted(false);
        note.setType("Normal");

        List<NoteEntity> noteEntities = new ArrayList<>();
        noteEntities.add(note);

        com.learninggenie.common.data.model.DomainEntity groupDomain = new com.learninggenie.common.data.model.DomainEntity();
        groupDomain.setId("gd001");
        groupDomain.setName("gd");

        com.learninggenie.common.data.model.DomainEntity childDomain = new com.learninggenie.common.data.model.DomainEntity();
        childDomain.setId("cd001");
        childDomain.setName("cd");

        List<DomainEntity> childDomains = new ArrayList<>();
        childDomains.add(childDomain);

        List<NoteDomainModel> noteDomainModels = new ArrayList<>();
        NoteDomainModel noteDomainModel = new NoteDomainModel();
        noteDomainModel.setId("d001");
        noteDomainModel.setNoteId("n001");
        noteDomainModels.add(noteDomainModel);

        List<NoteTagModel> noteTagModels = new ArrayList<>();
        NoteTagModel noteTagModel = new NoteTagModel();
        noteTagModel.setId("t001");
        noteTagModel.setNoteId("n001");
        noteTagModels.add(noteTagModel);

        List<ActionEntity> actionEntities = new ArrayList<>();
        ActionEntity actionEntity = new ActionEntity();
        actionEntity.setObjectId("n001");
        actionEntity.setId("a001");
        actionEntity.setAction("Create");
        actionEntity.setByUser("u001");
        actionEntities.add(actionEntity);

        List<NoteMediaModel> noteMediaModels = new ArrayList<>();
        NoteMediaModel noteMediaModel = new NoteMediaModel();
        noteMediaModel.setId("m001");
        noteMediaModel.setNoteId("n001");
        noteMediaModels.add(noteMediaModel);

        when(noteDao.getGroupNotes("Normal", groupId, fromDate, toDate)).thenReturn(noteEntities);
        when(domainDao.getDomainByGroupId(groupId)).thenReturn(groupDomain);
        when(domainDao.getChildDomains(groupId)).thenReturn(childDomains);
        when(domainDao.getDomainByNoteIds(any())).thenReturn(noteDomainModels);
        when(tagDao.getTagByNoteIds(anyString())).thenReturn(noteTagModels);
        when(noteDao.getActionByNoteIds(any())).thenReturn(actionEntities);
        when(mediaDao.getMediaByNoteIds(anyString())).thenReturn(noteMediaModels);

        List<NoteModel> noteModels = noteProvider.getGroupNotes(groupId, fromDate, toDate, domainIds, tagIds);

        verify(noteDao, times(1)).getGroupNotes(anyString(), anyString(), anyString(), anyString());
        verify(domainDao, times(1)).getDomainByGroupId(anyString());
        verify(domainDao, times(1)).getChildDomains(anyString());
        verify(domainDao, times(1)).getDomainByNoteIds(anyList());
        verify(tagDao, times(1)).getTagByNoteIds(anyString());
        verify(noteDao, times(1)).getActionByNoteIds(anyList());
        verify(mediaDao, times(1)).getMediaByNoteIds(anyString());

        Assert.assertTrue(noteModels.isEmpty());

    }

    /**
     * 测试getCenterNotes 按时间段
     *domainIds不为空,domainIds包含数据库查到的domain
     */
    @Test
    @Ignore
    public void testGetCenterNotes_HasDomainIdsIncludeDomainIds() {
        String centerId = "c001";
        String fromDate = "2016-01-01";
        String toDate = "2016-01-02";
        List<String> domainIds = new ArrayList<>();
        List<String> tagIds = new ArrayList<>();

        domainIds.add("d001");
        domainIds.add("d002");

        NoteEntity note = new NoteEntity();
        note.setId("n001");
        note.setPayload("oh baby");
        note.setIsDeleted(false);
        note.setType("Normal");

        List<NoteEntity> noteEntities = new ArrayList<>();
        noteEntities.add(note);

        com.learninggenie.common.data.model.DomainEntity centerDomain = new com.learninggenie.common.data.model.DomainEntity();
        centerDomain.setId("cd001");
        centerDomain.setName("cd");

        com.learninggenie.common.data.model.DomainEntity childDomain = new com.learninggenie.common.data.model.DomainEntity();
        childDomain.setId("chd001");
        childDomain.setName("chd");

        List<DomainEntity> childDomains = new ArrayList<>();
        childDomains.add(childDomain);

        List<NoteDomainModel> noteDomainModels = new ArrayList<>();
        NoteDomainModel noteDomainModel = new NoteDomainModel();
        noteDomainModel.setId("d001");
        noteDomainModel.setNoteId("n001");
        noteDomainModels.add(noteDomainModel);

        List<NoteTagModel> noteTagModels = new ArrayList<>();
        NoteTagModel noteTagModel = new NoteTagModel();
        noteTagModel.setId("t001");
        noteTagModel.setNoteId("n001");
        noteTagModels.add(noteTagModel);

        List<ActionEntity> actionEntities = new ArrayList<>();
        ActionEntity actionEntity = new ActionEntity();
        actionEntity.setObjectId("n001");
        actionEntity.setId("a001");
        actionEntity.setAction("Create");
        actionEntity.setByUser("u001");
        actionEntity.setRole("AGENCY_OWNER");
        actionEntities.add(actionEntity);

        List<NoteMediaModel> noteMediaModels = new ArrayList<>();
        NoteMediaModel noteMediaModel = new NoteMediaModel();
        noteMediaModel.setId("m001");
        noteMediaModel.setNoteId("n001");
        noteMediaModels.add(noteMediaModel);

        when(noteDao.getCenterNotes("Normal", centerId, fromDate, toDate)).thenReturn(noteEntities);
        when(domainDao.getDomainByCenterId(centerId)).thenReturn(centerDomain);
        when(domainDao.getChildDomains(centerId)).thenReturn(childDomains);
        when(domainDao.getDomainByNoteIds(anyList())).thenReturn(noteDomainModels);
        when(tagDao.getTagByNoteIds(anyString())).thenReturn(noteTagModels);
        when(noteDao.getActionByNoteIds(anyList())).thenReturn(actionEntities);
        when(mediaDao.getMediaByNoteIds(anyString())).thenReturn(noteMediaModels);
        when(regionService.isChina()).thenReturn(false);

        List<NoteModel> noteModels = noteProvider.getCenterNotes(centerId, fromDate, toDate, domainIds, tagIds);

        verify(noteDao, times(1)).getCenterNotes(anyString(), anyString(), anyString(), anyString());
        verify(domainDao, times(1)).getDomainByCenterId(anyString());
        verify(domainDao, times(1)).getChildDomains(anyString());
        verify(domainDao, times(1)).getDomainByNoteIds(anyList());
        verify(tagDao, times(1)).getTagByNoteIds(anyString());
        verify(noteDao, times(1)).getActionByNoteIds(anyList());
        verify(mediaDao, times(1)).getMediaByNoteIds(anyString());

        Assert.assertEquals("n001", noteModels.get(0).getId());
        Assert.assertEquals("oh baby", noteModels.get(0).getPayload());
        Assert.assertEquals("d001", noteModels.get(0).getDomains().get(0).getId());
        Assert.assertEquals("t001", noteModels.get(0).getTags().get(0).getId());
        Assert.assertEquals("Create",noteModels.get(0).getActions().get(0).getAction());
        Assert.assertEquals("m001", noteModels.get(0).getMedias().get(0).getId());

    }

    /**
     * 测试getCenterNotes 按时间段
     *domainIds不为空,domainIds包含数据库查到的domain
     */
    @Test
    @Ignore
    public void testGetCenterNotes_HasTagIdsIncludeTagIds() {
        String centerId = "c001";
        String fromDate = "2016-01-01";
        String toDate = "2016-01-02";
        List<String> domainIds = new ArrayList<>();
        List<String> tagIds = new ArrayList<>();

        tagIds.add("t001");
        tagIds.add("t002");

        NoteEntity note = new NoteEntity();
        note.setId("n001");
        note.setPayload("oh baby");
        note.setIsDeleted(false);
        note.setType("Normal");

        List<NoteEntity> noteEntities = new ArrayList<>();
        noteEntities.add(note);

        com.learninggenie.common.data.model.DomainEntity centerDomain = new com.learninggenie.common.data.model.DomainEntity();
        centerDomain.setId("cd001");
        centerDomain.setName("cd");

        com.learninggenie.common.data.model.DomainEntity childDomain = new com.learninggenie.common.data.model.DomainEntity();
        childDomain.setId("chd001");
        childDomain.setName("chd");

        List<DomainEntity> childDomains = new ArrayList<>();
        childDomains.add(childDomain);

        List<NoteDomainModel> noteDomainModels = new ArrayList<>();
        NoteDomainModel noteDomainModel = new NoteDomainModel();
        noteDomainModel.setId("d001");
        noteDomainModel.setNoteId("n001");
        noteDomainModels.add(noteDomainModel);

        List<NoteTagModel> noteTagModels = new ArrayList<>();
        NoteTagModel noteTagModel = new NoteTagModel();
        noteTagModel.setId("t001");
        noteTagModel.setNoteId("n001");
        noteTagModels.add(noteTagModel);

        List<ActionEntity> actionEntities = new ArrayList<>();
        ActionEntity actionEntity = new ActionEntity();
        actionEntity.setObjectId("n001");
        actionEntity.setId("a001");
        actionEntity.setAction("Create");
        actionEntity.setByUser("u001");
        actionEntity.setRole("AGENCY_OWNER");
        actionEntities.add(actionEntity);

        List<NoteMediaModel> noteMediaModels = new ArrayList<>();
        NoteMediaModel noteMediaModel = new NoteMediaModel();
        noteMediaModel.setId("m001");
        noteMediaModel.setNoteId("n001");
        noteMediaModels.add(noteMediaModel);
        when(regionService.isChina()).thenReturn(false);

        when(noteDao.getCenterNotes("Normal", centerId, fromDate, toDate)).thenReturn(noteEntities);
        when(domainDao.getDomainByCenterId(centerId)).thenReturn(centerDomain);
        when(domainDao.getChildDomains(centerId)).thenReturn(childDomains);
        when(domainDao.getDomainByNoteIds(anyList())).thenReturn(noteDomainModels);
        when(tagDao.getTagByNoteIds(anyString())).thenReturn(noteTagModels);
        when(noteDao.getActionByNoteIds(anyList())).thenReturn(actionEntities);
        when(mediaDao.getMediaByNoteIds(anyString())).thenReturn(noteMediaModels);

        List<NoteModel> noteModels = noteProvider.getCenterNotes(centerId, fromDate, toDate, domainIds, tagIds);

        verify(noteDao, times(1)).getCenterNotes(anyString(), anyString(), anyString(), anyString());
        verify(domainDao, times(1)).getDomainByCenterId(anyString());
        verify(domainDao, times(1)).getChildDomains(anyString());
        verify(domainDao, times(1)).getDomainByNoteIds(anyList());
        verify(tagDao, times(1)).getTagByNoteIds(anyString());
        verify(noteDao, times(1)).getActionByNoteIds(anyList());
        verify(mediaDao, times(1)).getMediaByNoteIds(anyString());

        Assert.assertEquals("n001", noteModels.get(0).getId());
        Assert.assertEquals("oh baby", noteModels.get(0).getPayload());
        Assert.assertEquals("d001", noteModels.get(0).getDomains().get(0).getId());
        Assert.assertEquals("t001", noteModels.get(0).getTags().get(0).getId());
        Assert.assertEquals("Create",noteModels.get(0).getActions().get(0).getAction());
        Assert.assertEquals("m001", noteModels.get(0).getMedias().get(0).getId());

    }

    /**
     * 获取指定小孩Note信息
     *  case： 正常情况
     */
    @Test
    public void testGetNote_child() {
        String childId = "123";
        List<NoteEntity> noteEntities = new ArrayList<>();
        NoteEntity noteEntity = new NoteEntity();
        String noteId = "note-id";
        noteEntity.setId(noteId);
        noteEntity.setOneNote("one-note-id");
        noteEntities.add(noteEntity);
        List<String> domainIds = new ArrayList<>();
        List<String> tagIds = new ArrayList<>();
        boolean sort = true;

        String domainId = "domain-id";
        DomainEntity groupDomain = new DomainEntity();
        groupDomain.setId(domainId);
        lenient().when(domainDao.getFrameworkByEnrollmentId(childId)).thenReturn(groupDomain);
        lenient().when(studentDao.getChildFramework(childId)).thenReturn(groupDomain);

        String childDomainId = "child-domain-id";
        List<DomainEntity> childDomains = new ArrayList<>();
        DomainEntity childDomain = new DomainEntity();
        childDomain.setId(childDomainId);
        childDomains.add(childDomain);
        lenient().when(domainDao.getChildDomains(domainId)).thenReturn(childDomains);

        List<NoteEnrollmentModel> noteEnrollmentModels = new ArrayList<>();
        NoteEnrollmentModel noteEnrollmentModel = new NoteEnrollmentModel();
        noteEnrollmentModel.setId("note-enrollment-id");
        noteEnrollmentModel.setNoteId(noteId);
        noteEnrollmentModel.setFirstName("a");
        noteEnrollmentModel.setLastName("b");
        noteEnrollmentModel.setDisplayName("a b");
        noteEnrollmentModel.setOneNote("one-note-id");
        noteEnrollmentModels.add(noteEnrollmentModel);
        when(studentDao.getEnrollmentByOneNotes(anyList())).thenReturn(noteEnrollmentModels);

        List<NoteDomainModel> noteDomainModels = new ArrayList<>();
        NoteDomainModel noteDomainModel = new NoteDomainModel();
        noteDomainModel.setId("note-domain-id");
        noteDomainModel.setNoteId(noteId);
        noteDomainModel.setAbbreviation("ATL-REG1");
        noteDomainModels.add(noteDomainModel);
        when(domainDao.getDomainByNoteIds(anyList(), anyString())).thenReturn(noteDomainModels);

        List<NoteTagModel> noteTagModels = new ArrayList<>();
        NoteTagModel noteTagModel = new NoteTagModel();
        noteTagModel.setNoteId(noteId);
        noteTagModel.setValue("Tag1");
        noteTagModels.add(noteTagModel);
        when(tagDao.getTagByNoteIds(anyString())).thenReturn(noteTagModels);

        List<NoteLessonModel> noteLessonModels = new ArrayList<>();
        NoteLessonModel noteLessonModel = new NoteLessonModel();
        noteLessonModel.setNoteId(noteId);
        noteLessonModel.setName("Lesson1");
        noteLessonModels.add(noteLessonModel);
        when(lessonDao.getLessonByNoteIds(anyString())).thenReturn(noteLessonModels);

        List<ActionEntity> actionEntities = new ArrayList<>();
        ActionEntity actionEntity = new ActionEntity();
        actionEntity.setObjectId(noteId);
        actionEntity.setObjectType("CREATE");
        actionEntity.setRole("AGENCY_OWNER");
        actionEntities.add(actionEntity);
        when(noteDao.getActionByNoteIds(anyList())).thenReturn(actionEntities);

        List<NoteMediaModel> noteMediaModels = new ArrayList<>();
        NoteMediaModel noteMediaModel = new NoteMediaModel();
        noteMediaModel.setNoteId(noteId);
        noteMediaModel.setRelativePath("/path");
        noteMediaModel.setFileType("jpg");
        noteMediaModels.add(noteMediaModel);
        when(mediaDao.getMediaByNoteIds(anyString())).thenReturn(noteMediaModels);

        String userId = "user-id";
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        com.learninggenie.common.data.entity.UserEntity user = new com.learninggenie.common.data.entity.UserEntity();
        user.setId(userId);
        user.setRole("agency_owner");
        when(userProvider.checkUser(userId)).thenReturn(user);

        List<NoteMetaDataModel> noteMetaDataModels = new ArrayList<>();
        NoteMetaDataModel noteMetaDataModel = new NoteMetaDataModel();
        noteMetaDataModel.setNoteId(noteId);
        noteMetaDataModel.setKey("key");
        noteMetaDataModel.setValue("value");
        noteMetaDataModels.add(noteMetaDataModel);
        when(noteDao.getNoteMetadataByNoteIds(anyList())).thenReturn(noteMetaDataModels);

        List<com.learninggenie.common.data.model.note.NoteCommentModel> noteCommentModels = new ArrayList<>();
        com.learninggenie.common.data.model.note.NoteCommentModel noteCommentModel = new com.learninggenie.common.data.model.note.NoteCommentModel();
        noteCommentModel.setNoteId(noteId);
        noteCommentModel.setContent("Good");
        noteCommentModels.add(noteCommentModel);
        when(noteDao.getNoteCommentByNoteIds(anyString())).thenReturn(noteCommentModels);
        when(regionService.isChina()).thenReturn(false);

        ScoreTemplateEntity scoreTemplateEntity = new ScoreTemplateEntity();
        when(portfolioService.getScoreTemplate(groupDomain.getId())).thenReturn(scoreTemplateEntity);
        when(fileSystem.getPublicUrl(anyString())).thenReturn("/path");

        List<NoteLearningStoryEntity> storyEntityList = new ArrayList<>();
        when(noteLearningStoryDao.getNoteLearningStoryByNoteIds(anyList())).thenReturn(storyEntityList);

        List<NoteModel> noteModels = noteProvider.getNotes(childId, noteEntities, null, domainIds, tagIds, sort);
        assertEquals(1, noteModels.size());

        NoteModel noteModel = noteModels.get(0);
        assertEquals(1, noteModel.getChildren().size());
        assertEquals(1, noteModel.getDomains().size());
        assertEquals(1, noteModel.getTags().size());
        assertEquals(1, noteModel.getLessons().size());
        assertEquals(1, noteModel.getActions().size());
        assertEquals(1, noteModel.getMedias().size());
        assertEquals(1, noteModel.getProps().size());
        assertEquals(1, noteModel.getComments().size());
        assertTrue(noteModel.isCurrentStandard());
    }

    /**
     * 获取指定小孩Note信息
     *  case： 没有数据，返回空集合
     */
    @Test
    public void testGetNote_childEmpty() {
        String childId = "123";
        List<NoteEntity> noteEntities = new ArrayList<>();
        List<String> domainIds = new ArrayList<>();
        List<String> tagIds = new ArrayList<>();
        boolean sort = true;

        List<NoteModel> noteModels = noteProvider.getNotes(childId, noteEntities, null, domainIds, tagIds, sort);
        assertEquals(0, noteModels.size());
    }

    /**
     * 获取指定小孩Note信息
     *  case： 过滤domain
     */
    @Ignore
    @Test
    public void testGetNote_childFilterDomain() {
        String childId = "123";
        List<NoteEntity> noteEntities = new ArrayList<>();
        NoteEntity noteEntity = new NoteEntity();
        String noteId = "note-id";
        noteEntity.setId(noteId);
        noteEntities.add(noteEntity);
        List<String> domainIds = new ArrayList<>();
        domainIds.add("filter-note-domain-id");
        List<String> tagIds = new ArrayList<>();
        boolean sort = true;

        String domainId = "domain-id";
        DomainEntity groupDomain = new DomainEntity();
        groupDomain.setId(domainId);
        when(domainDao.getFrameworkByEnrollmentId(childId)).thenReturn(groupDomain);

        String childDomainId = "child-domain-id";
        List<DomainEntity> childDomains = new ArrayList<>();
        DomainEntity childDomain = new DomainEntity();
        childDomain.setId(childDomainId);
        childDomains.add(childDomain);
        when(domainDao.getChildDomains(domainId)).thenReturn(childDomains);

        List<NoteEnrollmentModel> noteEnrollmentModels = new ArrayList<>();
        NoteEnrollmentModel noteEnrollmentModel = new NoteEnrollmentModel();
        noteEnrollmentModel.setNoteId(noteId);
        noteEnrollmentModel.setFirstName("a");
        noteEnrollmentModel.setLastName("b");
        noteEnrollmentModel.setDisplayName("a b");
        noteEnrollmentModels.add(noteEnrollmentModel);
        when(studentDao.getEnrollmentByNoteIds(anyString())).thenReturn(noteEnrollmentModels);

        List<NoteDomainModel> noteDomainModels = new ArrayList<>();
        NoteDomainModel noteDomainModel = new NoteDomainModel();
        noteDomainModel.setNoteId(noteId);
        noteDomainModel.setAbbreviation("ATL-REG1");
        String noteDomainId = "note-domain-id";
        noteDomainModel.setId(noteDomainId);
        noteDomainModels.add(noteDomainModel);
        when(domainDao.getDomainByNoteIds(anyList())).thenReturn(noteDomainModels);

        List<NoteTagModel> noteTagModels = new ArrayList<>();
        NoteTagModel noteTagModel = new NoteTagModel();
        noteTagModel.setNoteId(noteId);
        noteTagModel.setValue("Tag1");
        noteTagModels.add(noteTagModel);
        when(tagDao.getTagByNoteIds(anyString())).thenReturn(noteTagModels);

        List<NoteLessonModel> noteLessonModels = new ArrayList<>();
        NoteLessonModel noteLessonModel = new NoteLessonModel();
        noteLessonModel.setNoteId(noteId);
        noteLessonModel.setName("Lesson1");
        noteLessonModels.add(noteLessonModel);
        when(lessonDao.getLessonByNoteIds(anyString())).thenReturn(noteLessonModels);

        List<ActionEntity> actionEntities = new ArrayList<>();
        ActionEntity actionEntity = new ActionEntity();
        actionEntity.setObjectId(noteId);
        actionEntity.setObjectType("CREATE");
        actionEntities.add(actionEntity);
        when(noteDao.getActionByNoteIds(anyList())).thenReturn(actionEntities);

        List<NoteMediaModel> noteMediaModels = new ArrayList<>();
        NoteMediaModel noteMediaModel = new NoteMediaModel();
        noteMediaModel.setNoteId(noteId);
        noteMediaModel.setRelativePath("/path");
        noteMediaModels.add(noteMediaModel);
        when(mediaDao.getMediaByNoteIds(anyString())).thenReturn(noteMediaModels);

        String userId = "user-id";
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        com.learninggenie.common.data.entity.UserEntity user = new com.learninggenie.common.data.entity.UserEntity();
        user.setId(userId);
        user.setRole("agency_owner");
        when(userProvider.checkUser(userId)).thenReturn(user);

        List<NoteMetaDataModel> noteMetaDataModels = new ArrayList<>();
        NoteMetaDataModel noteMetaDataModel = new NoteMetaDataModel();
        noteMetaDataModel.setNoteId(noteId);
        noteMetaDataModel.setKey("key");
        noteMetaDataModel.setValue("value");
        noteMetaDataModels.add(noteMetaDataModel);
        when(noteDao.getNoteMetadataByNoteIds(anyList())).thenReturn(noteMetaDataModels);

        List<com.learninggenie.common.data.model.note.NoteCommentModel> noteCommentModels = new ArrayList<>();
        com.learninggenie.common.data.model.note.NoteCommentModel noteCommentModel = new com.learninggenie.common.data.model.note.NoteCommentModel();
        noteCommentModel.setNoteId(noteId);
        noteCommentModel.setContent("Good");
        noteCommentModels.add(noteCommentModel);
        when(noteDao.getNoteCommentByNoteIds(anyString())).thenReturn(noteCommentModels);

        when(fileSystem.getPublicUrl(anyString())).thenReturn("/path");

        List<NoteModel> noteModels = noteProvider.getNotes(childId, noteEntities, null, domainIds, tagIds, sort);
        assertEquals(0, noteModels.size());
    }

    /**
     * 获取指定小孩Note信息
     *  case： 过滤tag
     */
    @Ignore
    @Test
    public void testGetNote_childFilterTag() {
        String childId = "123";
        List<NoteEntity> noteEntities = new ArrayList<>();
        NoteEntity noteEntity = new NoteEntity();
        String noteId = "note-id";
        noteEntity.setId(noteId);
        noteEntities.add(noteEntity);
        List<String> domainIds = new ArrayList<>();
        List<String> tagIds = new ArrayList<>();
        tagIds.add("filter-tag-id");
        boolean sort = true;

        String domainId = "domain-id";
        DomainEntity groupDomain = new DomainEntity();
        groupDomain.setId(domainId);
        when(domainDao.getFrameworkByEnrollmentId(childId)).thenReturn(groupDomain);

        String childDomainId = "child-domain-id";
        List<DomainEntity> childDomains = new ArrayList<>();
        DomainEntity childDomain = new DomainEntity();
        childDomain.setId(childDomainId);
        childDomains.add(childDomain);
        when(domainDao.getChildDomains(domainId)).thenReturn(childDomains);

        List<NoteEnrollmentModel> noteEnrollmentModels = new ArrayList<>();
        NoteEnrollmentModel noteEnrollmentModel = new NoteEnrollmentModel();
        noteEnrollmentModel.setNoteId(noteId);
        noteEnrollmentModel.setFirstName("a");
        noteEnrollmentModel.setLastName("b");
        noteEnrollmentModel.setDisplayName("a b");
        noteEnrollmentModels.add(noteEnrollmentModel);
        when(studentDao.getEnrollmentByNoteIds(anyString())).thenReturn(noteEnrollmentModels);

        List<NoteDomainModel> noteDomainModels = new ArrayList<>();
        NoteDomainModel noteDomainModel = new NoteDomainModel();
        noteDomainModel.setNoteId(noteId);
        noteDomainModel.setAbbreviation("ATL-REG1");
        String noteDomainId = "note-domain-id";
        noteDomainModel.setId(noteDomainId);
        noteDomainModels.add(noteDomainModel);
        when(domainDao.getDomainByNoteIds(anyList())).thenReturn(noteDomainModels);

        List<NoteTagModel> noteTagModels = new ArrayList<>();
        NoteTagModel noteTagModel = new NoteTagModel();
        noteTagModel.setNoteId(noteId);
        String tagId = "note-tag-id";
        noteTagModel.setId(tagId);
        noteTagModel.setValue("Tag1");
        noteTagModels.add(noteTagModel);
        when(tagDao.getTagByNoteIds(anyString())).thenReturn(noteTagModels);

        List<NoteLessonModel> noteLessonModels = new ArrayList<>();
        NoteLessonModel noteLessonModel = new NoteLessonModel();
        noteLessonModel.setNoteId(noteId);
        noteLessonModel.setName("Lesson1");
        noteLessonModels.add(noteLessonModel);
        when(lessonDao.getLessonByNoteIds(anyString())).thenReturn(noteLessonModels);

        List<ActionEntity> actionEntities = new ArrayList<>();
        ActionEntity actionEntity = new ActionEntity();
        actionEntity.setObjectId(noteId);
        actionEntity.setObjectType("CREATE");
        actionEntities.add(actionEntity);
        when(noteDao.getActionByNoteIds(anyList())).thenReturn(actionEntities);

        List<NoteMediaModel> noteMediaModels = new ArrayList<>();
        NoteMediaModel noteMediaModel = new NoteMediaModel();
        noteMediaModel.setNoteId(noteId);
        noteMediaModel.setRelativePath("/path");
        noteMediaModels.add(noteMediaModel);
        when(mediaDao.getMediaByNoteIds(anyString())).thenReturn(noteMediaModels);

        String userId = "user-id";
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        com.learninggenie.common.data.entity.UserEntity user = new com.learninggenie.common.data.entity.UserEntity();
        user.setId(userId);
        user.setRole("agency_owner");
        when(userProvider.checkUser(userId)).thenReturn(user);

        List<NoteMetaDataModel> noteMetaDataModels = new ArrayList<>();
        NoteMetaDataModel noteMetaDataModel = new NoteMetaDataModel();
        noteMetaDataModel.setNoteId(noteId);
        noteMetaDataModel.setKey("key");
        noteMetaDataModel.setValue("value");
        noteMetaDataModels.add(noteMetaDataModel);
        when(noteDao.getNoteMetadataByNoteIds(anyList())).thenReturn(noteMetaDataModels);

        List<com.learninggenie.common.data.model.note.NoteCommentModel> noteCommentModels = new ArrayList<>();
        com.learninggenie.common.data.model.note.NoteCommentModel noteCommentModel = new com.learninggenie.common.data.model.note.NoteCommentModel();
        noteCommentModel.setNoteId(noteId);
        noteCommentModel.setContent("Good");
        noteCommentModels.add(noteCommentModel);
        when(noteDao.getNoteCommentByNoteIds(anyString())).thenReturn(noteCommentModels);

        when(fileSystem.getPublicUrl(anyString())).thenReturn("/path");

        List<NoteModel> noteModels = noteProvider.getNotes(childId, noteEntities, null, domainIds, tagIds, sort);
        assertEquals(0, noteModels.size());
    }

    /**
     * 获取指定老师添加的Note信息
     *  case: 正常情况
     */
    @Ignore
    @Test
    public void testGetNote_noChild() {
        String childId = null;
        List<NoteEntity> noteEntities = new ArrayList<>();
        NoteEntity noteEntity = new NoteEntity();
        String noteId = "note-id";
        noteEntity.setId(noteId);
        noteEntities.add(noteEntity);
        List<String> domainIds = new ArrayList<>();
        List<String> tagIds = new ArrayList<>();
        boolean sort = true;

        String domainId = "domain-id";
        DomainEntity groupDomain = new DomainEntity();
        groupDomain.setId(domainId);
        when(domainDao.getFrameworkByEnrollmentId(childId)).thenReturn(groupDomain);

        String childDomainId = "child-domain-id";
        List<DomainEntity> childDomains = new ArrayList<>();
        DomainEntity childDomain = new DomainEntity();
        childDomain.setId(childDomainId);
        childDomains.add(childDomain);
        when(domainDao.getChildDomains(domainId)).thenReturn(childDomains);

        List<NoteEnrollmentModel> noteEnrollmentModels = new ArrayList<>();
        NoteEnrollmentModel noteEnrollmentModel = new NoteEnrollmentModel();
        noteEnrollmentModel.setNoteId(noteId);
        noteEnrollmentModel.setFirstName("a");
        noteEnrollmentModel.setLastName("b");
        noteEnrollmentModel.setDisplayName("a b");
        noteEnrollmentModels.add(noteEnrollmentModel);
        when(studentDao.getEnrollmentByNoteIds(anyString())).thenReturn(noteEnrollmentModels);

        List<NoteDomainModel> noteDomainModels = new ArrayList<>();
        NoteDomainModel noteDomainModel = new NoteDomainModel();
        noteDomainModel.setNoteId(noteId);
        noteDomainModel.setAbbreviation("ATL-REG1");
        noteDomainModels.add(noteDomainModel);
        when(domainDao.getDomainByNoteIds(anyList())).thenReturn(noteDomainModels);

        List<NoteTagModel> noteTagModels = new ArrayList<>();
        NoteTagModel noteTagModel = new NoteTagModel();
        noteTagModel.setNoteId(noteId);
        noteTagModel.setValue("Tag1");
        noteTagModels.add(noteTagModel);
        when(tagDao.getTagByNoteIds(anyString())).thenReturn(noteTagModels);

        List<NoteLessonModel> noteLessonModels = new ArrayList<>();
        NoteLessonModel noteLessonModel = new NoteLessonModel();
        noteLessonModel.setNoteId(noteId);
        noteLessonModel.setName("Lesson1");
        noteLessonModels.add(noteLessonModel);
        when(lessonDao.getLessonByNoteIds(anyString())).thenReturn(noteLessonModels);

        List<ActionEntity> actionEntities = new ArrayList<>();
        ActionEntity actionEntity = new ActionEntity();
        actionEntity.setObjectId(noteId);
        actionEntity.setObjectType("CREATE");
        actionEntities.add(actionEntity);
        when(noteDao.getActionByNoteIds(anyList())).thenReturn(actionEntities);

        List<NoteMediaModel> noteMediaModels = new ArrayList<>();
        NoteMediaModel noteMediaModel = new NoteMediaModel();
        noteMediaModel.setNoteId(noteId);
        noteMediaModel.setRelativePath("/path");
        noteMediaModels.add(noteMediaModel);
        when(mediaDao.getMediaByNoteIds(anyString())).thenReturn(noteMediaModels);

        String userId = "user-id";
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        com.learninggenie.common.data.entity.UserEntity user = new com.learninggenie.common.data.entity.UserEntity();
        user.setId(userId);
        user.setRole("agency_owner");
        when(userProvider.checkUser(userId)).thenReturn(user);

        List<NoteMetaDataModel> noteMetaDataModels = new ArrayList<>();
        NoteMetaDataModel noteMetaDataModel = new NoteMetaDataModel();
        noteMetaDataModel.setNoteId(noteId);
        noteMetaDataModel.setKey("key");
        noteMetaDataModel.setValue("value");
        noteMetaDataModels.add(noteMetaDataModel);
        when(noteDao.getNoteMetadataByNoteIds(anyList())).thenReturn(noteMetaDataModels);
        when(regionService.isChina()).thenReturn(false);

        List<com.learninggenie.common.data.model.note.NoteCommentModel> noteCommentModels = new ArrayList<>();
        com.learninggenie.common.data.model.note.NoteCommentModel noteCommentModel = new com.learninggenie.common.data.model.note.NoteCommentModel();
        noteCommentModel.setNoteId(noteId);
        noteCommentModel.setContent("Good");
        noteCommentModels.add(noteCommentModel);
        when(noteDao.getNoteCommentByNoteIds(anyString())).thenReturn(noteCommentModels);

        when(fileSystem.getPublicUrl(anyString())).thenReturn("/path");

        List<NoteModel> noteModels = noteProvider.getNotes(childId, noteEntities, null, domainIds, tagIds, sort);
        assertEquals(1, noteModels.size());

        NoteModel noteModel = noteModels.get(0);
        assertEquals(1, noteModel.getChildren().size());
        assertEquals(1, noteModel.getDomains().size());
        assertEquals(1, noteModel.getTags().size());
        assertEquals(1, noteModel.getLessons().size());
        assertEquals(1, noteModel.getActions().size());
        assertEquals(1, noteModel.getMedias().size());
        assertEquals(1, noteModel.getProps().size());
        assertEquals(1, noteModel.getComments().size());
        assertTrue(noteModel.isCurrentStandard());
    }

    /**
     * 获取指定老师添加的Note信息
     *  case：不通过该方法排序
     */
    @Ignore
    @Test
    public void testGetNote_noChildnoSort() {
        String childId = null;
        List<NoteEntity> noteEntities = new ArrayList<>();
        NoteEntity noteEntity = new NoteEntity();
        String noteId = "note-id";
        noteEntity.setId(noteId);
        NoteEntity noteEntity2 = new NoteEntity();
        String noteId2 = "note-id2";
        noteEntity2.setId(noteId2);
        noteEntities.add(noteEntity2);
        noteEntities.add(noteEntity);
        List<String> domainIds = new ArrayList<>();
        List<String> tagIds = new ArrayList<>();
        boolean sort = false;

        String domainId = "domain-id";
        DomainEntity groupDomain = new DomainEntity();
        groupDomain.setId(domainId);
        when(domainDao.getFrameworkByEnrollmentId(childId)).thenReturn(groupDomain);

        String childDomainId = "child-domain-id";
        List<DomainEntity> childDomains = new ArrayList<>();
        DomainEntity childDomain = new DomainEntity();
        childDomain.setId(childDomainId);
        childDomains.add(childDomain);
        when(domainDao.getChildDomains(domainId)).thenReturn(childDomains);

        List<NoteEnrollmentModel> noteEnrollmentModels = new ArrayList<>();
        NoteEnrollmentModel noteEnrollmentModel = new NoteEnrollmentModel();
        noteEnrollmentModel.setNoteId(noteId);
        noteEnrollmentModel.setFirstName("a");
        noteEnrollmentModel.setLastName("b");
        noteEnrollmentModel.setDisplayName("a b");
        noteEnrollmentModels.add(noteEnrollmentModel);
        when(studentDao.getEnrollmentByNoteIds(anyString())).thenReturn(noteEnrollmentModels);

        List<NoteDomainModel> noteDomainModels = new ArrayList<>();
        NoteDomainModel noteDomainModel = new NoteDomainModel();
        noteDomainModel.setNoteId(noteId);
        noteDomainModel.setAbbreviation("ATL-REG1");
        noteDomainModels.add(noteDomainModel);
        when(domainDao.getDomainByNoteIds(anyList())).thenReturn(noteDomainModels);

        List<NoteTagModel> noteTagModels = new ArrayList<>();
        NoteTagModel noteTagModel = new NoteTagModel();
        noteTagModel.setNoteId(noteId);
        noteTagModel.setValue("Tag1");
        noteTagModels.add(noteTagModel);
        when(tagDao.getTagByNoteIds(anyString())).thenReturn(noteTagModels);

        List<NoteLessonModel> noteLessonModels = new ArrayList<>();
        NoteLessonModel noteLessonModel = new NoteLessonModel();
        noteLessonModel.setNoteId(noteId);
        noteLessonModel.setName("Lesson1");
        noteLessonModels.add(noteLessonModel);
        when(lessonDao.getLessonByNoteIds(anyString())).thenReturn(noteLessonModels);

        List<ActionEntity> actionEntities = new ArrayList<>();
        ActionEntity actionEntity = new ActionEntity();
        actionEntity.setObjectId(noteId);
        actionEntity.setObjectType("CREATE");
        actionEntities.add(actionEntity);
        when(noteDao.getActionByNoteIds(anyList())).thenReturn(actionEntities);

        List<NoteMediaModel> noteMediaModels = new ArrayList<>();
        NoteMediaModel noteMediaModel = new NoteMediaModel();
        noteMediaModel.setNoteId(noteId);
        noteMediaModel.setRelativePath("/path");
        noteMediaModels.add(noteMediaModel);
        when(mediaDao.getMediaByNoteIds(anyString())).thenReturn(noteMediaModels);

        String userId = "user-id";
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        com.learninggenie.common.data.entity.UserEntity user = new com.learninggenie.common.data.entity.UserEntity();
        user.setId(userId);
        user.setRole("agency_owner");
        when(userProvider.checkUser(userId)).thenReturn(user);

        List<NoteMetaDataModel> noteMetaDataModels = new ArrayList<>();
        NoteMetaDataModel noteMetaDataModel = new NoteMetaDataModel();
        noteMetaDataModel.setNoteId(noteId);
        noteMetaDataModel.setKey("key");
        noteMetaDataModel.setValue("value");
        noteMetaDataModels.add(noteMetaDataModel);
        when(noteDao.getNoteMetadataByNoteIds(anyList())).thenReturn(noteMetaDataModels);

        List<com.learninggenie.common.data.model.note.NoteCommentModel> noteCommentModels = new ArrayList<>();
        com.learninggenie.common.data.model.note.NoteCommentModel noteCommentModel = new com.learninggenie.common.data.model.note.NoteCommentModel();
        noteCommentModel.setNoteId(noteId);
        noteCommentModel.setContent("Good");
        noteCommentModels.add(noteCommentModel);
        when(noteDao.getNoteCommentByNoteIds(anyString())).thenReturn(noteCommentModels);
        when(regionService.isChina()).thenReturn(false);

        when(fileSystem.getPublicUrl(anyString())).thenReturn("/path");
        List<NoteModel> noteModels = noteProvider.getNotes(childId, noteEntities, null, domainIds, tagIds, sort);
        assertEquals(2, noteModels.size());

        NoteModel noteModel = noteModels.get(0);
        assertEquals(noteId2, noteModel.getId());
        NoteModel noteModel2 = noteModels.get(1);
        assertEquals(noteId, noteModel2.getId());
        assertEquals(1, noteModel2.getChildren().size());
        assertEquals(1, noteModel2.getDomains().size());
        assertEquals(1, noteModel2.getTags().size());
        assertEquals(1, noteModel2.getLessons().size());
        assertEquals(1, noteModel2.getActions().size());
        assertEquals(1, noteModel2.getMedias().size());
        assertEquals(1, noteModel2.getProps().size());
        assertEquals(1, noteModel2.getComments().size());
        assertTrue(noteModel2.isCurrentStandard());
    }

    /**
     * 获取指定老师添加的Note信息
     *  case: 正常情况
     */
    @Ignore
    @Test
    public void testGetNeedQualityFilterNotes_normal() {
        String childId = null;
        List<NoteEntity> noteEntities = new ArrayList<>();
        NoteEntity noteEntity = new NoteEntity();
        String noteId = "note-id";
        noteEntity.setId(noteId);
        noteEntity.setLastScore(3);
        noteEntities.add(noteEntity);
        List<String> domainIds = new ArrayList<>();
        List<String> tagIds = new ArrayList<>();
        boolean sort = true;

        String domainId = "domain-id";
        DomainEntity groupDomain = new DomainEntity();
        groupDomain.setId(domainId);
        when(domainDao.getFrameworkByEnrollmentId(childId)).thenReturn(groupDomain);

        String childDomainId = "child-domain-id";
        List<DomainEntity> childDomains = new ArrayList<>();
        DomainEntity childDomain = new DomainEntity();
        childDomain.setId(childDomainId);
        childDomains.add(childDomain);
        when(domainDao.getChildDomains(domainId)).thenReturn(childDomains);

        List<NoteEnrollmentModel> noteEnrollmentModels = new ArrayList<>();
        NoteEnrollmentModel noteEnrollmentModel = new NoteEnrollmentModel();
        noteEnrollmentModel.setNoteId(noteId);
        noteEnrollmentModel.setFirstName("a");
        noteEnrollmentModel.setLastName("b");
        noteEnrollmentModel.setDisplayName("a b");
        noteEnrollmentModels.add(noteEnrollmentModel);
        when(studentDao.getEnrollmentByNoteIds(anyString())).thenReturn(noteEnrollmentModels);

        List<NoteDomainModel> noteDomainModels = new ArrayList<>();
        NoteDomainModel noteDomainModel = new NoteDomainModel();
        noteDomainModel.setNoteId(noteId);
        noteDomainModel.setAbbreviation("ATL-REG1");
        noteDomainModels.add(noteDomainModel);
        when(domainDao.getDomainByNoteIds(anyList())).thenReturn(noteDomainModels);

        List<NoteTagModel> noteTagModels = new ArrayList<>();
        NoteTagModel noteTagModel = new NoteTagModel();
        noteTagModel.setNoteId(noteId);
        noteTagModel.setValue("Tag1");
        noteTagModels.add(noteTagModel);
        when(tagDao.getTagByNoteIds(anyString())).thenReturn(noteTagModels);

        List<NoteLessonModel> noteLessonModels = new ArrayList<>();
        NoteLessonModel noteLessonModel = new NoteLessonModel();
        noteLessonModel.setNoteId(noteId);
        noteLessonModel.setName("Lesson1");
        noteLessonModels.add(noteLessonModel);
        when(lessonDao.getLessonByNoteIds(anyString())).thenReturn(noteLessonModels);

        List<ActionEntity> actionEntities = new ArrayList<>();
        ActionEntity actionEntity = new ActionEntity();
        actionEntity.setObjectId(noteId);
        actionEntity.setObjectType("CREATE");
        actionEntities.add(actionEntity);
        when(noteDao.getActionByNoteIds(anyList())).thenReturn(actionEntities);

        List<NoteMediaModel> noteMediaModels = new ArrayList<>();
        NoteMediaModel noteMediaModel = new NoteMediaModel();
        noteMediaModel.setNoteId(noteId);
        noteMediaModel.setRelativePath("/path");
        noteMediaModels.add(noteMediaModel);
        when(mediaDao.getMediaByNoteIds(anyString())).thenReturn(noteMediaModels);

        String userId = "user-id";
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        com.learninggenie.common.data.entity.UserEntity user = new com.learninggenie.common.data.entity.UserEntity();
        user.setId(userId);
        user.setRole("agency_owner");
        when(userProvider.checkUser(userId)).thenReturn(user);

        List<NoteMetaDataModel> noteMetaDataModels = new ArrayList<>();
        NoteMetaDataModel noteMetaDataModel = new NoteMetaDataModel();
        noteMetaDataModel.setNoteId(noteId);
        noteMetaDataModel.setKey("key");
        noteMetaDataModel.setValue("value");
        noteMetaDataModels.add(noteMetaDataModel);
        when(noteDao.getNoteMetadataByNoteIds(anyList())).thenReturn(noteMetaDataModels);

        List<com.learninggenie.common.data.model.note.NoteCommentModel> noteCommentModels = new ArrayList<>();
        com.learninggenie.common.data.model.note.NoteCommentModel noteCommentModel = new com.learninggenie.common.data.model.note.NoteCommentModel();
        noteCommentModel.setNoteId(noteId);
        noteCommentModel.setContent("Good");
        noteCommentModels.add(noteCommentModel);
        when(noteDao.getNoteCommentByNoteIds(anyString())).thenReturn(noteCommentModels);

        when(fileSystem.getPublicUrl(anyString())).thenReturn("/path");

        List<NoteModel> noteModels = noteProvider.getNotes(childId, noteEntities, null, domainIds, tagIds, sort);
        assertEquals(1, noteModels.size());

        NoteModel noteModel = noteModels.get(0);
        assertEquals(1, noteModel.getChildren().size());
        assertEquals(1, noteModel.getDomains().size());
        assertEquals(1, noteModel.getTags().size());
        assertEquals(1, noteModel.getLessons().size());
        assertEquals(1, noteModel.getActions().size());
        assertEquals(1, noteModel.getMedias().size());
        assertEquals(1, noteModel.getProps().size());
        assertEquals(1, noteModel.getComments().size());
        assertEquals(3, noteModel.getLastScore());
        assertTrue(noteModel.isCurrentStandard());
    }

    /**
     * 测试组装学习故事数据
     */
    @Test
    public void testAddLearningStoryToNote() {
        String noteId = "NOTE_ID";
        Map<String, NoteModel> noteMap = new HashMap<>();
        NoteModel noteModel = new NoteModel();
        noteModel.setId(noteId);
        noteMap.put(noteId, noteModel);

        List<NoteLearningStoryEntity> learningStoryEntityList = new ArrayList<>();
        NoteLearningStoryEntity learningStoryEntity = new NoteLearningStoryEntity();
        learningStoryEntity.setTitle("title");
        learningStoryEntity.setContent("content");
        learningStoryEntity.setSortIndex(0);
        learningStoryEntity.setNoteId(noteId);
        learningStoryEntityList.add(learningStoryEntity);
//        when(userProvider.getAgencyOpenDefaultOpen(anyString(), anyString())).thenReturn(false);
        noteProvider.addLearningStoryToNote(noteMap, learningStoryEntityList);
    }

    /**
     * 测试移除获取学习故事数据
     */
    @Test
    public void testRemoveLearningStoryNote() {
        // 1. 测试移除学习故事数据
        String noteId = "NOTE_ID";
        List<NoteModel> noteModels = new ArrayList<>();
        NoteModel noteModel = new NoteModel();
        noteModel.setId(noteId);
        noteModel.setNoteType("LEARNING_STORY");
        NoteModel noteModel1 = new NoteModel();
        noteModel1.setId("NOTE_ID1");
        noteModel1.setNoteType("");
        noteModels.add(noteModel);
        noteModels.add(noteModel1);
        noteProvider.removeLearningStoryNote(noteModels);
        assertEquals(1, noteModels.size());
    }
}
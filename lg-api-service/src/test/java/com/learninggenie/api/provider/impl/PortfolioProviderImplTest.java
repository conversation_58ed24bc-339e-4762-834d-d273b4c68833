package com.learninggenie.api.provider.impl;

import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.provider.PortfolioProvider;
import com.learninggenie.common.data.dao.NoteDao;
import com.learninggenie.common.data.enums.Alias;
import com.learninggenie.common.utils.TimeUtil;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PortfolioProviderImplTest {

    @InjectMocks
    private PortfolioProvider portfolioProvider = new PortfolioProviderImpl();

    @Mock
    private NoteDao noteDao;

    /**
     * 判断添加的周期能否覆盖老周期
     *  case: 可以覆盖，新加周期范围大于老周期
     */
    @Test
    public void testUnableOverwrite_canOverwrite1() {
        Date newFromDate = TimeUtil.parseDate("2017-01-01");
        Date newToDate = TimeUtil.parseDate("2017-06-06");
        Date overlapFromDate = TimeUtil.parseDate("2017-01-12");
        Date overlapToDate = TimeUtil.parseDate("2017-05-23");
        Date localNow = TimeUtil.parseDate("2017-01-11");
        String childId = "child-id";

        assertFalse(portfolioProvider.unableOverwrite(newFromDate, newToDate, overlapFromDate, overlapToDate, localNow, childId));
    }

    /**
     * 判断添加的周期能否覆盖老周期
     *  case: 可以覆盖，新加周期from时间小于老周期，to时间小于老周期，并且新加的to时间在今天之后
     */
    @Test
    public void testUnableOverwrite_canOverwrite2() {
        Date newFromDate = TimeUtil.parseDate("2017-02-02");
        Date newToDate = TimeUtil.parseDate("2017-06-06");
        Date overlapFromDate = TimeUtil.parseDate("2017-03-13");
        Date overlapToDate = TimeUtil.parseDate("2017-07-23");
        Date localNow = TimeUtil.parseDate("2017-06-01");
        String childId = "child-id";

        assertFalse(portfolioProvider.unableOverwrite(newFromDate, newToDate, overlapFromDate, overlapToDate, localNow, childId));
    }

    /**
     * 判断添加的周期能否覆盖老周期
     *  case: 可以覆盖，新加周期from时间大于老周期，to时间大于老周期，并且老的from时间在今天之后
     */
    @Test
    public void testUnableOverwrite_canOverwrite3() {
        Date newFromDate = TimeUtil.parseDate("2017-03-15");
        Date newToDate = TimeUtil.parseDate("2017-08-06");
        Date overlapFromDate = TimeUtil.parseDate("2017-03-13");
        Date overlapToDate = TimeUtil.parseDate("2017-07-23");
        Date localNow = TimeUtil.parseDate("2017-03-01");
        String childId = "child-id";

        assertFalse(portfolioProvider.unableOverwrite(newFromDate, newToDate, overlapFromDate, overlapToDate, localNow, childId));
    }

    /**
     * 判断添加的周期能否覆盖老周期
     *  case: 可以覆盖，新加周期from时间大于老周期，to时间小于老周期，并且老的from时间在今天之后
     */
    @Test
    public void testUnableOverwrite_canOverwrite4() {
        Date newFromDate = TimeUtil.parseDate("2017-03-15");
        Date newToDate = TimeUtil.parseDate("2017-07-06");
        Date overlapFromDate = TimeUtil.parseDate("2017-03-13");
        Date overlapToDate = TimeUtil.parseDate("2017-07-23");
        Date localNow = TimeUtil.parseDate("2017-03-01");
        String childId = "child-id";

        assertFalse(portfolioProvider.unableOverwrite(newFromDate, newToDate, overlapFromDate, overlapToDate, localNow, childId));
    }

    /**
     * 判断添加的周期能否覆盖老周期
     *  case: 不能覆盖，新加周期from时间小于老周期，to时间小于老周期，并且新加的to时间在今天之前
     */
    @Test
    public void testUnableOverwrite_cannotOverwrite1() {
        Date newFromDate = TimeUtil.parseDate("2017-02-02");
        Date newToDate = TimeUtil.parseDate("2017-06-06");
        Date overlapFromDate = TimeUtil.parseDate("2017-03-13");
        Date overlapToDate = TimeUtil.parseDate("2017-07-23");
        Date localNow = TimeUtil.parseDate("2017-06-08");
        String childId = "child-id";

        when(noteDao.getNoteCountByChild(eq(childId), eq("06/07/2017"), eq("07/23/2017"))).thenReturn(10);

        assertTrue(portfolioProvider.unableOverwrite(newFromDate, newToDate, overlapFromDate, overlapToDate, localNow, childId));
    }

    /**
     * 判断添加的周期能否覆盖老周期
     *  case: 不能覆盖，新加周期from时间大于老周期，to时间大于老周期，并且老的from时间在今天之前
     */
    @Test
    public void testUnableOverwrite_cannotOverwrite2() {
        Date newFromDate = TimeUtil.parseDate("2017-03-15");
        Date newToDate = TimeUtil.parseDate("2017-08-06");
        Date overlapFromDate = TimeUtil.parseDate("2017-03-13");
        Date overlapToDate = TimeUtil.parseDate("2017-07-23");
        Date localNow = TimeUtil.parseDate("2017-03-20");
        String childId = "child-id";

        when(noteDao.getNoteCountByChild(eq(childId), eq("03/13/2017"), eq("03/14/2017"))).thenReturn(10);

        assertTrue(portfolioProvider.unableOverwrite(newFromDate, newToDate, overlapFromDate, overlapToDate, localNow, childId));
    }

    /**
     * 判断添加的周期能否覆盖老周期
     *  case: 不能覆盖，新加周期from时间大于老周期，to时间小于老周期，并且老的from时间在今天之前
     */
    @Test
    public void testUnableOverwrite_cannotOverwrite3() {
        Date newFromDate = TimeUtil.parseDate("2017-03-15");
        Date newToDate = TimeUtil.parseDate("2017-07-06");
        Date overlapFromDate = TimeUtil.parseDate("2017-03-13");
        Date overlapToDate = TimeUtil.parseDate("2017-07-23");
        Date localNow = TimeUtil.parseDate("2017-03-20");
        String childId = "child-id";

        when(noteDao.getNoteCountByChild(eq(childId), eq("03/13/2017"), eq("03/14/2017"))).thenReturn(10);

        assertTrue(portfolioProvider.unableOverwrite(newFromDate, newToDate, overlapFromDate, overlapToDate, localNow, childId));
    }

    @Test
    public void testCheckPeriodRange_normal() {
        portfolioProvider.checkPeriodRange(TimeUtil.parse("07/01/2018", TimeUtil.format3), TimeUtil.parse("09/01/2018", TimeUtil.format3), Alias.A20182019FALL);
        portfolioProvider.checkPeriodRange(TimeUtil.parse("07/02/2018", TimeUtil.format3), TimeUtil.parse("08/31/2019", TimeUtil.format3), Alias.A20182019FALL);
        portfolioProvider.checkPeriodRange(TimeUtil.parse("07/01/2018", TimeUtil.format3), TimeUtil.parse("08/31/2019", TimeUtil.format3), Alias.A20182019FALL);
        portfolioProvider.checkPeriodRange(TimeUtil.parse("02/01/2019", TimeUtil.format3), TimeUtil.parse("04/21/2019", TimeUtil.format3), Alias.A20182019FALL);
    }

    @Ignore
    @Test(expected = BusinessException.class)
    public void testCheckPeriodRange_exception1() {
        portfolioProvider.checkPeriodRange(TimeUtil.parse("07/01/2018", TimeUtil.format3), TimeUtil.parse("09/02/2018", TimeUtil.format3), Alias.A20172018SUMMER);
    }

    @Test(expected = BusinessException.class)
    public void testCheckPeriodRange_exception2() {
        portfolioProvider.checkPeriodRange(TimeUtil.parse("07/02/2018", TimeUtil.format3), TimeUtil.parse("12/01/2018", TimeUtil.format3), Alias.A20172018TIME1);
    }

    @Test(expected = BusinessException.class)
    public void testCheckPeriodRange_exception3() {
        portfolioProvider.checkPeriodRange(TimeUtil.parse("07/02/2018", TimeUtil.format3), TimeUtil.parse("12/01/2019", TimeUtil.format3), Alias.A20172018TIME1);
    }
}

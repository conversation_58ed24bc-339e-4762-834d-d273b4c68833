package com.learninggenie.api.provider.impl;

import com.amazonaws.services.lambda.AWSLambdaAsyncClient;
import com.amazonaws.services.lambda.model.InvokeRequest;
import com.amazonaws.services.lambda.model.InvokeResult;
import com.amazonaws.services.sqs.AmazonSQSClient;
import com.learninggenie.common.data.model.dashboard.ChatLogModel;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;

import static org.mockito.Mockito.*;

/**
 * Remote Provider 的实现测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class NewRemoteProviderImplTest {
    @InjectMocks
    private RemoteProviderImpl remoteProvider;

    @Mock
    private AWSLambdaAsyncClient lambdaAsyncClient;

    @Mock
    private AmazonSQSClient sqsClient;

    @Mock
    private UserProviderImpl userProvider;

    /**
     * 测试调用创建聊天记录服务
     * case: 调用服务
     */
    @Test
    public void callCreateChatLogService() {
        // 模拟测试数据
        ChatLogModel chatLogModel = new ChatLogModel(); // 创建聊天记录
        chatLogModel.setId("chatLogId001"); // 设置聊天记录 Id
        chatLogModel.setSenderId("senderId001"); // 设置发送者 Id
        chatLogModel.setSenderRole("PARENT"); // 设置发送者角色
        chatLogModel.setMessageId("messageId001"); // 设置消息 Id
        chatLogModel.setMessageGroupId("messageGroupId001"); // 设置群组 Id
        chatLogModel.setEnrollmentId("enrollmentId001"); // 设置小孩 Id
        chatLogModel.setSendAtUtc(new Date()); // 设置发送时间
        chatLogModel.setGroupId("groupId001"); // 设置班级 Id
        chatLogModel.setCenterId("centerId001"); // 设置学校 Id
        chatLogModel.setAgencyId("agencyId001"); // 设置机构 Id
        Future<InvokeResult> future = new CompletableFuture<>(); // 创建异步结果
        when(lambdaAsyncClient.invokeAsync(any(InvokeRequest.class))).thenReturn(future); //模拟测试方法

        // 调用测试方法
        remoteProvider.callCreateChatLogService(chatLogModel);

        // 验证测试结果
        verify(lambdaAsyncClient).invokeAsync(any(InvokeRequest.class));
    }

    /**
     * 测试调用生成课程任务服务
     */
    @Test
    public void testCallGenerateLessonTaskService() {
        String taskId = "taskId001";
        when(userProvider.getCurrentUserId()).thenReturn("userId");
        remoteProvider.callGenerateLessonTaskService(taskId);
        verify(sqsClient, times(1)).sendMessage(any());
    }

    /**
     * 测试调用改编课程任务服务
     */
    @Test
    public void testCallAdaptLessonTaskService() {
        String taskId = "taskId001";
        when(userProvider.getCurrentUserId()).thenReturn("userId");
        remoteProvider.callAdaptLessonTaskService(taskId);
        verify(sqsClient, times(1)).sendMessage(any());
    }
}


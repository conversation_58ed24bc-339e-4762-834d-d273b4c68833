package com.learninggenie.api.provider.impl;

import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.entity.UserEntity;
import com.learninggenie.common.data.model.UserModel;
import com.learninggenie.common.report.LGSnapshot;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.*;

/**
 * Snapshot Provider 测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class SnapshotProviderImplTest {

    @InjectMocks
    private SnapshotProviderImpl snapshotProvider;

    @Mock
    private UserDaoImpl userDao;

    @Test
    public void testUpdateTeacher () {
        String groupId = "1";
        LGSnapshot.ClassSnapshot classSnapshot = LGSnapshot.ClassSnapshot.newBuilder()
                .setId("1").setName("groupName").setCreatedAt(1L).setUpdatedAt(1L)
                .build();
        List<UserEntity> groupTeachers = new ArrayList<>();
        UserEntity teacher1 = new UserEntity();
        teacher1.setId("1");
        groupTeachers.add(teacher1);

        List<UserModel> users = new ArrayList<>();
        UserModel userModel = new UserModel();
        userModel.setId("1");
        userModel.setDisplayName("name");
        userModel.setEmail("<EMAIL>");

        users.add(userModel);
        when(userDao.getTeacherByGroupId(anyString())).thenReturn(groupTeachers);
        when(userDao.getUsersByUserIds(anyList())).thenReturn(users);

        snapshotProvider.updateTeacher(groupId, classSnapshot);

        verify(userDao, times(1)).getTeacherByGroupId(anyString());
        verify(userDao, times(1)).getUsersByUserIds(anyList());
    }


}

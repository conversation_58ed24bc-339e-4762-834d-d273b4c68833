package com.learninggenie.api.provider.impl;

import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import com.learninggenie.api.model.AuthTypeEnum;
import com.learninggenie.api.model.importdata.ImportChild;
import com.learninggenie.api.model.importdata.ImportParent;
import com.learninggenie.common.data.dao.CenterDao;
import com.learninggenie.common.data.dao.GroupDao;
import com.learninggenie.common.data.entity.CenterEntity;
import com.learninggenie.common.data.entity.CenterMetaDataEntity;
import com.learninggenie.common.data.entity.GroupEntity;
import com.learninggenie.common.data.entity.GroupMetaDataEntity;
import com.learninggenie.common.data.entity.ThirdAuthEntity;
import com.learninggenie.common.data.model.MyHeadStartPDEPRefBody;
import com.learninggenie.common.data.model.myHeadStart.Center;
import com.learninggenie.common.data.model.myHeadStart.Division;
import com.learninggenie.common.data.model.myHeadStart.Group;
import com.learninggenie.common.data.model.myHeadStart.MyHeadStartSyncSetting;
import com.learninggenie.common.utils.JsonUtil;
import com.learninggenie.common.utils.TimeUtil;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.learninggenie.api.provider.impl.MyHeadStartProviderImpl.*;
import static java.util.stream.Collectors.toList;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.description;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Created by hxl on 2023/07/20.
 */
@RunWith(MockitoJUnitRunner.class)
public class MyHeadStartProviderImplTest {

    @InjectMocks
    private MyHeadStartProviderImpl myHeadStartProviderImpl;

    @Mock
    private CenterDao centerDao;
    @Mock
    private GroupDao groupDao;

    /**
     * Created by hxl on 2023/07/20.
     * transform 的测试代码
     */
    @Test
    public void testTransform() {
        final JSONObject childJson = new JSONObject();
        final JSONObject keyJson = new JSONObject();
        final String personID = "PersonID";
        final String sourcedId = "123";
        keyJson.put(personID, sourcedId);
        keyJson.put("UpdateStatus", "ACTIVE");
        keyJson.put("UpdateStatusDatetime", "7/10/2022 11:30 AM");
        final String key = "key";
        childJson.put(key, keyJson);
        final String firstName = "John";
        childJson.put("FirstName", firstName);
        final String middleName = "D";
        childJson.put("MiddleInitial", middleName);
        final String lastName = "Doe";
        childJson.put("LastName", lastName);
        final JSONObject genderJson = new JSONObject();
        final String value = "value";
        genderJson.put(value, "MALE");
        childJson.put("Gender", genderJson);
        final String birthday = "01/01/2000";
        childJson.put("BirthDate", birthday);
        final JSONArray parentJsonArray = new JSONArray();
        final JSONObject parentJson = new JSONObject();
        final String parentName = "Jane";
        parentJson.put("First_Name", parentName);
        parentJson.put("Last_Name", lastName);
        parentJson.put("EMail", "<EMAIL>");
        final JSONObject parentKeyJson = new JSONObject();
        final String parentId = "456";
        parentKeyJson.put(personID, parentId);
        parentJson.put(key, parentKeyJson);
        parentJsonArray.put(parentJson);
        childJson.put("relationships", parentJsonArray);
        final JSONObject languageJson = new JSONObject();
        final String english = "ENGLISH";
        languageJson.put(value, english);
        final String language = "Language";
        childJson.put(language, languageJson);
        final JSONObject secondaryLanguageJson = new JSONObject();
        final String spanish = "SPANISH";
        secondaryLanguageJson.put(value, spanish);
        childJson.put("SecondaryLanguage", secondaryLanguageJson);
        final JSONObject raceJson = new JSONObject();
        final String white = "WHITE";
        raceJson.put(value, white);
        final String race = "Race";
        childJson.put(race, raceJson);
        final String hispanic = "Hispanic";
        childJson.put(hispanic, "Y");

        final String agencyId = "12345";
        final String systemId = "1";
        final MyHeadStartPDEPRefBody refBody = new MyHeadStartPDEPRefBody("ydinm", systemId, systemId, "aE9SqxxdCBNHEXn1pb4rQUBEbiRfyuVrFwIInFRP05pXW9632tUEekN0PBqgbZic");

        final ImportChild importChild = new ImportChild();
        importChild.setSourcedId(sourcedId);
        importChild.setExternalId(sourcedId);
        importChild.setSourcedStatus("UPDATE");
        final LocalDateTime updateStatusDatetime = LocalDateTime.parse("7/20/2023 11:30 AM", DTF);
        importChild.setDateLastModified(TimeUtil.localDateTimeToDate(updateStatusDatetime));
        importChild.addCustomizedAttribute(ROSTER_SOURCED_KEY, Collections.singletonList(sourcedId));
        importChild.addCustomizedAttribute(ROSTER_LAST_KEY, Collections.singletonList(updateStatusDatetime.format(DateTimeFormatter.ofPattern(TimeUtil.format2))));
        importChild.setFirstName(firstName);
        importChild.setMiddleName(middleName);
        importChild.setLastName(lastName);
        importChild.setDisplayName("John D Doe");
        importChild.setGender("");
        importChild.setBirthDate(birthday);
        final ImportParent importParent = new ImportParent();
        importParent.setPrimaryAdultFirstName(parentName);
        importParent.setPrimaryAdultLastName(lastName);
        importParent.setPrimaryAdultEmail("<EMAIL>");
        final List<ImportParent> parentList = new ArrayList<>();
        parentList.add(importParent);
        importChild.setParents(parentList);
        importChild.setParentIds(Collections.singletonList(parentId));
        importChild.addCustomizedAttribute(language, Arrays.asList(english, spanish));
        importChild.addCustomizedAttribute(race, Arrays.asList(white));
        importChild.addCustomizedAttribute(hispanic, Arrays.asList("Yes"));

        final String dateformatBir = "2000-01-01";
        final ImportChild result = myHeadStartProviderImpl.transform(childJson, agencyId, refBody);

        assertEquals(importChild.getSourcedId(), result.getSourcedId());
        assertEquals(importChild.getExternalId(), result.getExternalId());
        assertEquals(importChild.getSourcedStatus(), result.getSourcedStatus());
        assertEquals(importChild.getFirstName(), result.getFirstName());
        assertEquals(importChild.getMiddleName(), result.getMiddleName());
        assertEquals(importChild.getLastName(), result.getLastName());
        assertEquals(importChild.getDisplayName(), result.getDisplayName());
        assertEquals(importChild.getGender(), result.getGender());
        assertEquals(dateformatBir, result.getBirthDate());
        assertEquals(0, result.getParents().size());
        assertEquals(importChild.getParentIds().size(), result.getParentIds().size());
        assertEquals(importChild.getParentIds(), result.getParentIds());
    }

    @Test
    public void testMatchingCenterAndGroup () {
        String agencyId= "1";
        List<Division> divisions = new ArrayList<>();
        Division division = new Division();
        division.setId("1");

        List<Center> centers = new ArrayList<>();
        Center center = new Center();
        Set<String> ids = new HashSet<>();
        ids.add("1");

        center.setIds(ids);

        List<Group> groups = new ArrayList<>();
        Group group = new Group();
        LinkedList<String> groupIds = Lists.newLinkedList();
        groupIds.add("1");
        group.setIds(groupIds);
        group.setNewId("1");
        group.setName("GroupName");

        groups.add(group);

        center.setClasses(groups);
        center.setName("CenterName");
        center.setNewId("1");

        centers.add(center);

        division.setCenters(centers);

        divisions.add(division);

        // 同步认证信息
        ThirdAuthEntity thirdAuthEntity = new ThirdAuthEntity();
        thirdAuthEntity.setExtendData("{}"); // 认证信息
        thirdAuthEntity.setAuthType(AuthTypeEnum.OAUTH1.toString()); // 认证类型
        MyHeadStartSyncSetting syncSetting = new MyHeadStartSyncSetting();
        syncSetting.setDivisionId("1");
        syncSetting.setSelectCenterIds(new ArrayList<>(ids));
        syncSetting.setSelectGroupIds(groupIds);
        thirdAuthEntity.setSyncSetting(JsonUtil.toJson(syncSetting));


        List<CenterMetaDataEntity> centerMetaDataList = new ArrayList<>();
        CenterMetaDataEntity centerMetaDataEntity = new CenterMetaDataEntity();
        centerMetaDataEntity.setMetaKey(ROSTER_SOURCED_KEY);
        centerMetaDataEntity.setMetaValue("1");
        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setId("1");
        centerEntity.setTraining(false);
        centerMetaDataEntity.setCenter(centerEntity);

        centerMetaDataList.add(centerMetaDataEntity);

        List<GroupMetaDataEntity> groupMetaDataList = new ArrayList<>();
        GroupMetaDataEntity groupMetaDataEntity = new GroupMetaDataEntity ();
        groupMetaDataEntity.setMetaKey(ROSTER_SOURCED_KEY);
        groupMetaDataEntity.setMetaValue("1");
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId("1");
        groupEntity.setName("GroupName");
        groupMetaDataEntity.setGroup(groupEntity);
        groupMetaDataList.add(groupMetaDataEntity);

        List<GroupEntity> groupEntities = new ArrayList<>();
        groupEntity.setCenter(centerEntity);
        groupEntities.add(groupEntity);

        List<CenterEntity> centerEntities = new ArrayList<>();
        centerEntities.add(centerEntity);

        when(centerDao.getMetaByAgencyId(agencyId, ROSTER_SOURCED_KEY)).thenReturn(centerMetaDataList);
        when(groupDao.getMetaByAgencyId(agencyId, ROSTER_SOURCED_KEY)).thenReturn(groupMetaDataList);
        when(groupDao.getGroupByAgency(agencyId)).thenReturn(groupEntities);
        when(centerDao.getAllByAgencyId(agencyId)).thenReturn(centerEntities);



        myHeadStartProviderImpl.matchingCenterAndGroup(divisions, agencyId, thirdAuthEntity);

        verify(centerDao).getMetaByAgencyId(agencyId, ROSTER_SOURCED_KEY);
        verify(groupDao).getMetaByAgencyId(agencyId, ROSTER_SOURCED_KEY);
        verify(groupDao).getGroupByAgency(agencyId);
        verify(centerDao).getAllByAgencyId(agencyId);
    }
}

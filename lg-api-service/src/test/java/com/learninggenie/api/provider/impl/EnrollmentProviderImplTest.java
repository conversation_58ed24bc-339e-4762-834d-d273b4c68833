package com.learninggenie.api.provider.impl;

import com.learninggenie.api.model.prompt.GroupAdaptationInfo;
import com.learninggenie.common.data.dao.StudentDao;
import com.learninggenie.common.data.dao.groups.GroupEnrollmentTeamEntityDao;
import com.learninggenie.common.data.dao.groups.GroupsMetaDataEntityDao;
import com.learninggenie.common.data.entity.EnrollmentEntity;
import com.learninggenie.common.data.entity.EnrollmentMetaDataEntity;
import com.learninggenie.common.data.entity.groups.GroupsMetaDataEntity;
import com.learninggenie.common.data.enums.DrdpAttr;
import com.learninggenie.common.data.enums.GroupMetaKey;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Enrollment Provider 的实现测试类
 */
@ExtendWith(MockitoExtension.class)
class EnrollmentProviderImplTest {
    @InjectMocks
    private EnrollmentProviderImpl enrollmentProvider;

    @Mock
    private GroupEnrollmentTeamEntityDao groupEnrollmentTeamEntityDao;

    @Mock
    private GroupsMetaDataEntityDao groupsMetaDataEntityDao;


    @Mock
    private StudentDao studentDao;

    /**
     * 测试根据班级 Id 获取班级下学生的 IEP 和 ELD 属性值
     * case：传入班级 Id
     */
    @Test
    void testGetIEPAndELDAttrsByGroupIds() {
        // 模拟测试数据
        List<String> groupIds = Collections.singletonList("groupId001"); // 班级 Id
        List<EnrollmentMetaDataEntity> iepMetas = new ArrayList<>(); // IEP 属性的元数据列表
        EnrollmentMetaDataEntity iepMeta = new EnrollmentMetaDataEntity(); // IEP 属性的元数据
        iepMeta.setId("meta001"); // 设置 Id
        iepMeta.setMetaKey("IEP/IFSP"); // 设置 Key
        iepMeta.setMetaValue("true"); // 设置 Value
        EnrollmentEntity enrollmentEntity = new EnrollmentEntity(); // 创建 EnrollmentEntity 对象
        enrollmentEntity.setId("childId001"); // 设置小孩 Id
        iepMeta.setEnrollment(enrollmentEntity); // 设置 EnrollmentEntity 对象
        iepMeta.setChildId("childId001"); // 设置小孩 Id
        iepMetas.add(iepMeta); // 添加到列表中
        EnrollmentMetaDataEntity iepMeta2 = new EnrollmentMetaDataEntity(); // IEP 属性的元数据
        iepMeta2.setId("meta002"); // 设置 Id
        iepMeta2.setMetaKey("IEP/IFSP"); // 设置 Key
        iepMeta2.setMetaValue("false"); // 设置 Value
        EnrollmentEntity enrollmentEntity2 = new EnrollmentEntity(); // 创建 EnrollmentEntity 对象
        enrollmentEntity2.setId("childId002"); // 设置小孩 Id
        iepMeta2.setEnrollment(enrollmentEntity2); // 设置 EnrollmentEntity 对象
        iepMeta2.setChildId("childId002"); // 设置小孩 Id
        iepMetas.add(iepMeta2); // 添加到列表中
        List<EnrollmentMetaDataEntity> eldMetas = new ArrayList<>(); // ELD 属性的元数据列表
        EnrollmentMetaDataEntity eldMeta = new EnrollmentMetaDataEntity(); // ELD 属性的元数据
        eldMeta.setId("meta003"); // 设置 Id
        eldMeta.setMetaKey("ELD"); // 设置 Key
        eldMeta.setMetaValue("true"); // 设置 Value
        EnrollmentEntity enrollmentEntity3 = new EnrollmentEntity(); // 创建 EnrollmentEntity 对象
        enrollmentEntity3.setId("childId001"); // 设置小孩 Id
        eldMeta.setEnrollment(enrollmentEntity3); // 设置 EnrollmentEntity 对象
        eldMeta.setChildId("childId001"); // 设置小孩 Id
        eldMetas.add(eldMeta); // 添加到列表中
        EnrollmentMetaDataEntity eldMeta2 = new EnrollmentMetaDataEntity(); // ELD 属性的元数据
        eldMeta2.setId("meta004"); // 设置 Id
        eldMeta2.setMetaKey("ELD"); // 设置 Key
        eldMeta2.setMetaValue("false"); // 设置 Value
        EnrollmentEntity enrollmentEntity4 = new EnrollmentEntity(); // 创建 EnrollmentEntity 对象
        enrollmentEntity4.setId("childId002"); // 设置小孩 Id
        eldMeta2.setEnrollment(enrollmentEntity4); // 设置 EnrollmentEntity 对象
        eldMeta2.setChildId("childId002"); // 设置小孩 Id
        eldMetas.add(eldMeta2); // 添加到列表中
        eldMetas.addAll(iepMetas); // 添加到列表中
        when(studentDao.getMetaByGroupIds(groupIds, Arrays.asList(DrdpAttr.IEP.toString(), DrdpAttr.ELD.toString()))).thenReturn(eldMetas); // 模拟调用获取 ELD 属性方法

        // 调用测试方法
        Map<String, Map<String, Boolean>> attrsMap = enrollmentProvider.getIEPAndELDAttrsByGroupIds(groupIds);

        // 验证测试结果
        assertEquals(true, attrsMap.get("CHILDID001").get("IEP/IFSP")); // 验证小孩 Id 为 childId001 的小孩的 IEP 属性值
        assertEquals(true, attrsMap.get("CHILDID001").get("ELD")); // 验证小孩 Id 为 childId001 的小孩的 ELD 属性值
        assertEquals(false, attrsMap.get("CHILDID002").get("IEP/IFSP")); // 验证小孩 Id 为 childId002 的小孩的 IEP 属性值
        assertEquals(false, attrsMap.get("CHILDID002").get("ELD")); // 验证小孩 Id 为 childId002 的小孩的 ELD 属性值
    }

    /**
     * 测试根据班级 Id 获取班级下学生的 IEP 和 ELD 属性值
     * case：班级 Id 为空
     */
    @Test
    void testGetIEPAndELDAttrsByGroupIds2() {
        // 调用测试方法
        Map<String, Map<String, Boolean>> attrsMap = enrollmentProvider.getIEPAndELDAttrsByGroupIds(Collections.emptyList());

        // 验证测试结果
        assertTrue(attrsMap.isEmpty()); // 验证返回的 Map 的是空的
    }

    /**
     * 测试根据学校 Id 获取学校下学生的 IEP 和 ELD 属性值
     * case：传入学校 Id
     */
    @Test
    void testGIEPAndELDAttrsByCenterIds() {
        // 模拟测试数据
        List<String> centerIds = Collections.singletonList("centerId001"); // 班级 Id
        List<EnrollmentMetaDataEntity> iepMetas = new ArrayList<>(); // IEP 属性的元数据列表
        EnrollmentMetaDataEntity iepMeta = new EnrollmentMetaDataEntity(); // IEP 属性的元数据
        iepMeta.setId("meta001"); // 设置 Id
        iepMeta.setMetaKey("IEP/IFSP"); // 设置 Key
        iepMeta.setMetaValue("true"); // 设置 Value
        EnrollmentEntity enrollmentEntity = new EnrollmentEntity(); // 创建 EnrollmentEntity 对象
        enrollmentEntity.setId("childId001"); // 设置小孩 Id
        iepMeta.setEnrollment(enrollmentEntity); // 设置 EnrollmentEntity 对象
        iepMeta.setChildId("childId001"); // 设置小孩 Id
        iepMetas.add(iepMeta); // 添加到列表中
        EnrollmentMetaDataEntity iepMeta2 = new EnrollmentMetaDataEntity(); // IEP 属性的元数据
        iepMeta2.setId("meta002"); // 设置 Id
        iepMeta2.setMetaKey("IEP/IFSP"); // 设置 Key
        iepMeta2.setMetaValue("false"); // 设置 Value
        EnrollmentEntity enrollmentEntity2 = new EnrollmentEntity(); // 创建 EnrollmentEntity 对象
        enrollmentEntity2.setId("childId002"); // 设置小孩 Id
        iepMeta2.setEnrollment(enrollmentEntity2); // 设置 EnrollmentEntity 对象
        iepMeta2.setChildId("childId002"); // 设置小孩 Id
        iepMetas.add(iepMeta2); // 添加到列表中
        List<EnrollmentMetaDataEntity> eldMetas = new ArrayList<>(); // ELD 属性的元数据列表
        EnrollmentMetaDataEntity eldMeta = new EnrollmentMetaDataEntity(); // ELD 属性的元数据
        eldMeta.setId("meta003"); // 设置 Id
        eldMeta.setMetaKey("ELD"); // 设置 Key
        eldMeta.setMetaValue("true"); // 设置 Value
        EnrollmentEntity enrollmentEntity3 = new EnrollmentEntity(); // 创建 EnrollmentEntity 对象
        enrollmentEntity3.setId("childId001"); // 设置小孩 Id
        eldMeta.setEnrollment(enrollmentEntity3); // 设置 EnrollmentEntity 对象
        eldMeta.setChildId("childId001"); // 设置小孩 Id
        eldMetas.add(eldMeta); // 添加到列表中
        EnrollmentMetaDataEntity eldMeta2 = new EnrollmentMetaDataEntity(); // ELD 属性的元数据
        eldMeta2.setId("meta004"); // 设置 Id
        eldMeta2.setMetaKey("ELD"); // 设置 Key
        eldMeta2.setMetaValue("false"); // 设置 Value
        EnrollmentEntity enrollmentEntity4 = new EnrollmentEntity(); // 创建 EnrollmentEntity 对象
        enrollmentEntity4.setId("childId002"); // 设置小孩 Id
        eldMeta2.setEnrollment(enrollmentEntity4); // 设置 EnrollmentEntity 对象
        eldMeta2.setChildId("childId002"); // 设置小孩 Id
        eldMetas.add(eldMeta2); // 添加到列表中
        eldMetas.addAll(iepMetas); // 添加到列表中
        when(studentDao.getMetaByCenterIds(centerIds,  Arrays.asList(DrdpAttr.IEP.toString(), DrdpAttr.ELD.toString()))).thenReturn(eldMetas); // 模拟调用获取 IEP 属性方法

        // 调用测试方法
        Map<String, Map<String, Boolean>> attrsMap = enrollmentProvider.getIEPAndELDAttrsByCenterIds(centerIds);

        // 验证测试结果
        assertEquals(true, attrsMap.get("CHILDID001").get("IEP/IFSP")); // 验证小孩 Id 为 childId001 的小孩的 IEP 属性值
        assertEquals(true, attrsMap.get("CHILDID001").get("ELD")); // 验证小孩 Id 为 childId001 的小孩的 ELD 属性值
        assertEquals(false, attrsMap.get("CHILDID002").get("IEP/IFSP")); // 验证小孩 Id 为 childId002 的小孩的 IEP 属性值
        assertEquals(false, attrsMap.get("CHILDID002").get("ELD")); // 验证小孩 Id 为 childId002 的小孩的 ELD 属性值
    }

    /**
     * 测试根据学校 Id 获取学校下学生的 IEP 和 ELD 属性值
     * case：学校 Id 为空
     */
    @Test
    void testGIEPAndELDAttrsByCenterIds2() {
        // 调用测试方法
        Map<String, Map<String, Boolean>> attrsMap = enrollmentProvider.getIEPAndELDAttrsByCenterIds(Collections.emptyList());

        // 验证测试结果
        assertTrue(attrsMap.isEmpty()); // 验证返回的 Map 的是空的
    }

    /**
     * 测试方法：getGroupAdaptationInfo_HasIEPAndHasELD_ReturnsAdaptationInfo
     * 测试场景：存在 IEP 和 ELD 的情况
     * 预期结果：返回包含正确信息的 GroupAdaptationInfo 对象
     */
    @Test
    public void testGetGroupAdaptationInfo_HasIEPAndHasELD_ReturnsAdaptationInfo() {
        // 创建 GroupAdaptationInfo 对象
        GroupAdaptationInfo adaptationInfo = new GroupAdaptationInfo();

        // 创建 EnrollmentEntity 对象
        EnrollmentEntity child1 = new EnrollmentEntity();
        child1.setId("childId1");
        child1.setDisplayName("Child1");

        EnrollmentEntity child2 = new EnrollmentEntity();
        child2.setId("childId2");
        child2.setDisplayName("Child2");

        EnrollmentEntity child3 = new EnrollmentEntity();
        child3.setId("childId3");
        child3.setDisplayName("Child3");


        // 设置小孩列表
        List<EnrollmentEntity> allChildren = new ArrayList<>();
        allChildren.add(child1);
        allChildren.add(child2);
        allChildren.add(child3);


        // 创建 GroupsMetaDataEntity 对象
        GroupsMetaDataEntity groupsMetaDataEntity = new GroupsMetaDataEntity();
        groupsMetaDataEntity.setId(UUID.randomUUID().toString());

        groupsMetaDataEntity.setMetaValue("1,5"); // 设置分组大小限制

        // 设置班级限制信息
        List<GroupsMetaDataEntity> byGroupIdsAndKey = Collections.singletonList(groupsMetaDataEntity);

        // 创建 EnrollmentMetaDataEntity 对象
        EnrollmentMetaDataEntity enrollmentIEPMetaDataEntity = new EnrollmentMetaDataEntity();
        enrollmentIEPMetaDataEntity.setChildId("childId1");

        enrollmentIEPMetaDataEntity.setEnrollment(child1);
        enrollmentIEPMetaDataEntity.setMetaKey("IEP/IFSP");
        enrollmentIEPMetaDataEntity.setMetaValue("Yes");

        EnrollmentMetaDataEntity enrollmentELDMetaDataEntity = new EnrollmentMetaDataEntity();
        enrollmentELDMetaDataEntity.setChildId("childId1");

        enrollmentELDMetaDataEntity.setEnrollment(child1);
        enrollmentELDMetaDataEntity.setMetaKey("ELD");
        enrollmentELDMetaDataEntity.setMetaValue("Yes");


        EnrollmentMetaDataEntity enrollmentIEPMetaDataEntity2 = new EnrollmentMetaDataEntity();
        enrollmentIEPMetaDataEntity2.setChildId("childId2");

        enrollmentIEPMetaDataEntity2.setEnrollment(child2);
        enrollmentIEPMetaDataEntity2.setMetaKey("IEP/IFSP");
        enrollmentIEPMetaDataEntity2.setMetaValue("Yes");

        EnrollmentMetaDataEntity enrollmentELDMetaDataEntity3 = new EnrollmentMetaDataEntity();
        enrollmentELDMetaDataEntity3.setChildId("childId3");

        enrollmentELDMetaDataEntity3.setEnrollment(child3);
        enrollmentELDMetaDataEntity3.setMetaKey("ELD");
        enrollmentELDMetaDataEntity3.setMetaValue("Yes");

        // 设置小孩属性列表
        List<EnrollmentMetaDataEntity> allMetas = Arrays.asList(enrollmentIEPMetaDataEntity, enrollmentELDMetaDataEntity, enrollmentIEPMetaDataEntity2, enrollmentELDMetaDataEntity3);

        // 设置 groupsMetaDataEntityDao.getByGroupIdsAndKey 返回值
        when(groupsMetaDataEntityDao.getByGroupIdsAndKey(Collections.singletonList("validGroupId"),
                GroupMetaKey.GROUP_TEAM_SIZE_LIMIT.toString())).thenReturn(byGroupIdsAndKey);

        // 设置 studentDao.getAllByGroupId 返回值
        when(studentDao.getAllChildrenById(anyString())).thenReturn(allChildren);

        // 设置 studentDao.getChildrenAllMeta 返回值
        when(studentDao.getChildrenAllMeta(Arrays.asList("childId1", "childId2", "childId3"))).thenReturn(allMetas);

        // 调用方法
        GroupAdaptationInfo result = enrollmentProvider.getGroupAdaptationInfo("validGroupId", "validTeacherId", Collections.singletonList("validEnrollmentId"));

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getHasIEP());
        assertTrue(result.getHasELD());
        assertEquals("In my class, there are 3 children:\n" +
                "1 IEP Children:\n" +
                "Child2\n" +
                "1 ELD Children:\n" +
                "Child3\n" +
                "1 IEP and ELD Children:\n" +
                "Child1\n", result.getChildInfo());
        assertEquals("Group 1: 3 children\n", result.getGroupInfo());
        assertEquals("3", result.getGroupChild());
        assertEquals("1", result.getGroupCount());

        // 验证相关方法调用
        verify(groupsMetaDataEntityDao).getByGroupIdsAndKey(Collections.singletonList("validGroupId"),
                GroupMetaKey.GROUP_TEAM_SIZE_LIMIT.toString());
        verify(studentDao).getAllChildrenById("'validEnrollmentId'");
        verify(studentDao).getChildrenAllMeta(Arrays.asList("childId1", "childId2", "childId3"));
    }

    /**
     * 测试方法：getGroupAdaptationInfo_NoChildren_ReturnsAdaptationInfo
     * 测试场景：没有小孩的情况
     * 预期结果：返回空的 GroupAdaptationInfo 对象
     */
    @Test
    public void testGetGroupAdaptationInfo_NoChildren_ReturnsAdaptationInfo() {
        // 创建 GroupAdaptationInfo 对象
        GroupAdaptationInfo adaptationInfo = new GroupAdaptationInfo();

        // 设置班级小孩为空
        List<EnrollmentEntity> allChildren = Collections.emptyList();

        // 设置 studentDao.getAllByGroupId 返回值
        when(studentDao.getAllChildrenById(anyString())).thenReturn(allChildren);

        // 调用方法
        GroupAdaptationInfo result = enrollmentProvider.getGroupAdaptationInfo("validGroupId", "validTeacherId", Collections.singletonList("validEnrollmentId"));

        // 验证结果
        assertNotNull(result);

        // 验证相关方法调用
        verify(studentDao).getAllChildrenById("'validEnrollmentId'");
        verifyNoInteractions(groupEnrollmentTeamEntityDao);
    }

    /**
     * 测试方法：getGroupAdaptationInfo_ReturnsAdaptationInfo
     * 测试场景：正常情况，存在小孩和分组限制
     * 预期结果：返回包含正确信息的 GroupAdaptationInfo 对象
     */
    @Test
    public void testGetGroupAdaptationInfo_ReturnsAdaptationInfo() {
        // 创建 GroupAdaptationInfo 对象
        GroupAdaptationInfo adaptationInfo = new GroupAdaptationInfo();

        // 创建 EnrollmentEntity 对象
        EnrollmentEntity child1 = new EnrollmentEntity();
        child1.setId("childId1");
        child1.setDisplayName("Child1");

        // 设置小孩列表
        List<EnrollmentEntity> allChildren = new ArrayList<>();
        allChildren.add(child1);

        // 创建 GroupsMetaDataEntity 对象
        GroupsMetaDataEntity groupsMetaDataEntity = new GroupsMetaDataEntity();
        groupsMetaDataEntity.setId(UUID.randomUUID().toString());
        groupsMetaDataEntity.setMetaValue("1,5"); // 设置分组大小限制

        // 设置班级限制信息
        List<GroupsMetaDataEntity> byGroupIdsAndKey = Collections.singletonList(groupsMetaDataEntity);

        // 创建 EnrollmentMetaDataEntity 对象
        EnrollmentMetaDataEntity enrollmentIEPMetaDataEntity = new EnrollmentMetaDataEntity();
        enrollmentIEPMetaDataEntity.setChildId("childId1");

        enrollmentIEPMetaDataEntity.setEnrollment(child1);
        enrollmentIEPMetaDataEntity.setMetaKey("IEP/IFSP");
        enrollmentIEPMetaDataEntity.setMetaValue("Yes");

        EnrollmentMetaDataEntity enrollmentELDMetaDataEntity = new EnrollmentMetaDataEntity();
        enrollmentELDMetaDataEntity.setChildId("childId1");

        enrollmentELDMetaDataEntity.setEnrollment(child1);
        enrollmentELDMetaDataEntity.setMetaKey("ELD");
        enrollmentELDMetaDataEntity.setMetaValue("Yes");

        // 设置小孩属性列表
        List<EnrollmentMetaDataEntity> allMetas = Arrays.asList(enrollmentIEPMetaDataEntity, enrollmentELDMetaDataEntity);

        // 设置 groupsMetaDataEntityDao.getByGroupIdsAndKey 返回值
        when(groupsMetaDataEntityDao.getByGroupIdsAndKey(Collections.singletonList("validGroupId"),
                GroupMetaKey.GROUP_TEAM_SIZE_LIMIT.toString())).thenReturn(byGroupIdsAndKey);

        // 设置 studentDao.getAllByGroupId 返回值
        when(studentDao.getAllByGroupId("validGroupId")).thenReturn(allChildren);

        // 设置 studentDao.getChildrenAllMeta 返回值
        when(studentDao.getChildrenAllMeta(Collections.singletonList("childId1"))).thenReturn(allMetas);

        // 调用方法
        GroupAdaptationInfo result = enrollmentProvider.getGroupAdaptationInfo("validGroupId");

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getHasIEP());
        assertTrue(result.getHasELD());
        assertEquals("In my class, there are 1 children:\n" +
                "1 IEP and ELD Children:\n" +
                "Child1\n", result.getChildInfo());
        assertEquals("1", result.getGroupChild());
        assertEquals("1", result.getGroupCount());

        // 验证相关方法调用
        verify(groupsMetaDataEntityDao).getByGroupIdsAndKey(Collections.singletonList("validGroupId"),
                GroupMetaKey.GROUP_TEAM_SIZE_LIMIT.toString());
        verify(studentDao).getAllByGroupId("validGroupId");
        verify(studentDao).getChildrenAllMeta(Collections.singletonList("childId1"));
    }

    /**
     * 测试儿童分组
     */
    @Test
    public void testGroupChildren() {
        // 设置输入参数
        int totalChildren = 10;
        int customerMinGroupSize = 2;
        int customerMaxGroupSize = 4;

        // 调用被测试方法
        List<Integer> result = enrollmentProvider.groupChildren(totalChildren, customerMinGroupSize, customerMaxGroupSize);

        // 验证返回结果是否符合预期
        assertEquals(totalChildren, result.stream().mapToInt(Integer::intValue).sum());
        assertTrue(result.size() > 0);
    }

    /**
     * 测试儿童分组 只能一组的时候
     */
    @Test
    public void testGroupChildren_Only_One_Group() {
        // 设置输入参数
        int totalChildren = 10;
        int customerMinGroupSize = 6;
        int customerMaxGroupSize = 7;

        // 调用被测试方法
        List<Integer> result = enrollmentProvider.groupChildren(totalChildren, customerMinGroupSize, customerMaxGroupSize);

        // 验证返回结果是否符合预期
        assertEquals(totalChildren, result.stream().mapToInt(Integer::intValue).sum());
        assertTrue(result.size() > 0);
    }

}
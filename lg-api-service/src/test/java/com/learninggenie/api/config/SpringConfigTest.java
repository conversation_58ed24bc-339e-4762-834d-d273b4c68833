package com.learninggenie.api.config;

import com.learninggenie.api.security.DotNetPasswordEncoder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.*;
import org.springframework.context.support.PropertySourcesPlaceholderConfigurer;
import org.springframework.core.env.Environment;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.client.RestTemplate;

@Configuration
@EnableTransactionManagement
@PropertySource({"classpath:service.properties"})
@Import({DotNetPasswordEncoder.class})
@ComponentScan(basePackages = {"com.learninggenie.api.service",
        "com.learninggenie.api.provider",
        "com.learninggenie.api.dao",
        "com.learninggenie.api.client",
        "com.learninggenie.email.common",
        "com.learninggenie.api.notification",
        "com.learninggenie.common"})
public class SpringConfigTest {
    @Autowired
    Environment env;
    @Autowired
    SimpleClientHttpRequestFactory httpClientFactory;

    @Bean
    public static PropertySourcesPlaceholderConfigurer propertySourcesPlaceholderConfigurer() {
        return new PropertySourcesPlaceholderConfigurer();
    }

    @Bean(name = "httpClientFactory")
    public SimpleClientHttpRequestFactory getSimpleClientHttpRequestFactory() {
        SimpleClientHttpRequestFactory scrf = new SimpleClientHttpRequestFactory();
        scrf.setConnectTimeout(8000);
        scrf.setReadTimeout(8000);
        return scrf;
    }

    @Bean(name = "restTemplate")
    @DependsOn("httpClientFactory")
    public RestTemplate getRestTemplate() {
        return new RestTemplate(httpClientFactory);
    }
}

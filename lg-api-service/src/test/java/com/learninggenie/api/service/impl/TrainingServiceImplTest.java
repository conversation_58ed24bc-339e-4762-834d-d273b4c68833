package com.learninggenie.api.service.impl;

import com.learninggenie.api.model.VimeoModel;
import com.learninggenie.api.model.training.CertificateModel;
import com.learninggenie.api.model.training.CertificatesResponse;
import com.learninggenie.api.model.training.TrainingMediasResponse;
import com.learninggenie.api.model.training.TrainingModuleModel;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.common.cache.CacheModel;
import com.learninggenie.common.cache.CacheService;
import com.learninggenie.common.constant.CacheKey;
import com.learninggenie.common.data.dao.*;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.dao.training.TrainingCertificateDao;
import com.learninggenie.common.data.dao.training.TrainingResourceDao;
import com.learninggenie.common.data.entity.AgencyMetaDataEntity;
import com.learninggenie.common.data.entity.TrainingCertificateEntity;
import com.learninggenie.common.data.entity.TrainingMediaEntity;
import com.learninggenie.common.data.entity.UserEntity;
import com.learninggenie.common.data.enums.AgencyMetaKey;
import com.learninggenie.common.data.enums.AppMetaKey;
import com.learninggenie.common.data.enums.UserRole;
import com.learninggenie.common.data.enums.training.TrainingModules;
import com.learninggenie.common.data.enums.training.TrainingTypes;
import com.learninggenie.common.data.model.AgencyModel;
import com.learninggenie.common.data.model.ResultPojo;
import com.learninggenie.common.data.model.UserModel;
import com.learninggenie.common.utils.JsonUtil;
import com.learninggenie.common.utils.RestApiUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TrainingServiceImplTest {

    @InjectMocks
    TrainingServiceImpl trainingService;

    @Mock
    private UserProvider userProvider;

    @Mock
    private UserDaoImpl userDao;

    @Mock
    private AgencyDao agencyDao;

    @Mock
    private MetaDao metaDao;

    @Mock
    private TrainingResourceDao trainingResourceDao;

    @Mock
    private MediaBookDao mediaBookDao;

    @Mock
    private TrainingCertificateDao trainingCertificateDao;

    @Mock
    private MediaDao mediaDao;

    @Mock
    private FormDao formDao;

    @Mock
    private CacheService cacheService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }


    /**
     * 测试获取培训视频详情
     * Case: 视频信息没有缓存
     */
    @Test
    public void getVimeoVideoDetail() {
        // 数据准备
        // 视频ID
        String singleVideo = "v1";
        List<VimeoModel> videos = new ArrayList<>();
        VimeoModel vimeoModel = new VimeoModel();
        vimeoModel.setVideoId(singleVideo);
        videos.add(vimeoModel);
        // 数据模拟
        HashMap<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer e9c38a22439cb8acb8e90d7c5ebe2131");
        ResultPojo resultPojo = new ResultPojo();
        resultPojo.setStatus(200);
        resultPojo.setData(JsonUtil.toJson(vimeoModel));
        when(cacheService.get(CacheKey.TRAINING_VIDEO + singleVideo)).thenReturn(null); // 模拟缓存中没有视频信息
        try(MockedStatic<RestApiUtil> restApiUtil = Mockito.mockStatic(RestApiUtil.class)){
            restApiUtil.when(() -> RestApiUtil.get(Mockito.anyString(), any(), Mockito.anyInt())).thenReturn(resultPojo);
            // 调用方法
            List<VimeoModel> vimeoVideoDetail = trainingService.getVimeoVideoDetail(singleVideo);
            // 验证结果
            assertEquals(vimeoVideoDetail.get(0).getVideoId(), singleVideo);
        }
    }

    /**
     * 测试获取培训视频详情
     * Case: 视频信息有缓存
     */
    @Test
    public void getVimeoVideoDetailWithCache() {
        // 数据准备
        // 视频ID
        String singleVideo = "v1";
        VimeoModel vimeoModel = new VimeoModel();
        vimeoModel.setVideoId(singleVideo);
        // 数据模拟
        CacheModel cacheModel = new CacheModel();
        cacheModel.setValue(JsonUtil.toJson(vimeoModel));
        when(cacheService.get(CacheKey.TRAINING_VIDEO + singleVideo)).thenReturn(cacheModel); // 模拟缓存中没有视频信息

        // 调用方法
        List<VimeoModel> vimeoVideoDetail = trainingService.getVimeoVideoDetail(singleVideo);
        // 验证结果,缓存中的视频信息与需要获取的视频 ID 一致
        assertEquals(vimeoVideoDetail.get(0).getVideoId(), singleVideo);
    }
    /**
     * 测试获取培训视频列表
     * Case: 视频信息有缓存
     */
    @Test
    public void testGetMediaList() {
        // 用户 ID
        String userId = UUID.randomUUID().toString();

        // 模拟用户信息
        UserEntity userEntity = new UserEntity();
        userEntity.setRole(UserRole.AGENCY_OWNER.toString()); // 角色
        // 模拟检查用户
        when(userProvider.checkUser(userId)).thenReturn(userEntity);
        // 模拟用户信息
        UserModel userModel = new UserModel();
        // 模拟获取用户信息
        when(userDao.getUserById(userId)).thenReturn(userModel);

        // 模拟机构信息
        AgencyModel agencyModel = new AgencyModel();
        // 模拟获取机构信息
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);

        // 模拟培训课程模块开关
        List<AgencyMetaDataEntity> metaDataList = new ArrayList<>();
        // RLP 基础课程
        AgencyMetaDataEntity rlpBasicMetadata = new AgencyMetaDataEntity();
        rlpBasicMetadata.setMetaKey(AgencyMetaKey.TRAINING_MODULE_RLP_BASIC.toString()); // 开关键值
        rlpBasicMetadata.setMetaValue(Boolean.TRUE.toString()); // 开关状态
        metaDataList.add(rlpBasicMetadata);
        // RLP 高级课程
        AgencyMetaDataEntity rlpAdvancedMetadata = new AgencyMetaDataEntity();
        rlpAdvancedMetadata.setMetaKey(AgencyMetaKey.TRAINING_MODULE_RLP_ADVANCED_I.toString()); // 开关键值
        rlpAdvancedMetadata.setMetaValue(Boolean.TRUE.toString()); // 开关状态
        metaDataList.add(rlpAdvancedMetadata);
        // 模拟查询机构 Metadata
        when(agencyDao.getAllMetaData(any())).thenReturn(metaDataList);

        // 模拟培训模块
        List<TrainingModuleModel> moduleModels = new ArrayList<>();
        // RLP 模块
        TrainingModuleModel rlpModule = new TrainingModuleModel();
        rlpModule.setName(TrainingModules.RLP.getModuleName());
        moduleModels.add(rlpModule);
        // 模拟查询模块
        when(metaDao.getAppMeta(any())).thenReturn(JsonUtil.toJson(moduleModels));

        // 模拟根据模块查询视频 ID 列表
        when(trainingResourceDao.getMediaIdsByModuleAndTypes(anyString(), any())).thenReturn(new ArrayList<>());

        // 模拟视频信息
        List<TrainingMediaEntity> mediaEntities = new ArrayList<>();
        TrainingMediaEntity mediaEntity = new TrainingMediaEntity();
        mediaEntity.setVideoId("test-video1-id"); // 视频 ID
        mediaEntities.add(mediaEntity);
        // 模拟根据 ID 获取视频信息
        when(trainingResourceDao.getTrainingMediasByIds(any())).thenReturn(mediaEntities);

        // 模拟获取观看记录，无观看记录的情况
        when(mediaBookDao.getWatchRecordsByUserIdAndShareIds(anyString(), any())).thenReturn(new ArrayList<>());
        // 模拟获取证书，无证书的情况
        when(trainingCertificateDao.getCertificateList(anyString(), any())).thenReturn(new ArrayList<>());
        when(cacheService.get(CacheKey.TRAINING_VIDEO + "test-video1-id")).thenReturn(null); // 模拟缓存中没有视频信息

        // 模拟视频详情结果数据
        ResultPojo resultPojo = new ResultPojo();
        resultPojo.setStatus(200);
        resultPojo.setData(JsonUtil.toJson(new VimeoModel()));
        try (MockedStatic<RestApiUtil> restApiUtil = Mockito.mockStatic(RestApiUtil.class)) {
            // 模拟获取视频详情
            restApiUtil.when(() -> RestApiUtil.get(anyString(), any(), anyInt())).thenReturn(resultPojo);

            // 调用方法
            List<TrainingMediasResponse> mediaList = trainingService.getMediaList(userId);

            // 验证结果
            assertEquals(1, mediaList.size()); // 模块数量为 1
            assertEquals(TrainingModules.RLP.getModuleName(), mediaList.get(0).getModuleName()); // 验证模块名称
        }
    }

    /**
     * 测试获取培训视频列表
     * Case: 视频信息没有缓存
     */
    @Test
    public void testGetMediaListWithCache() {
        // 用户 ID
        String userId = UUID.randomUUID().toString();

        // 模拟用户信息
        UserEntity userEntity = new UserEntity();
        userEntity.setRole(UserRole.AGENCY_OWNER.toString()); // 角色
        // 模拟检查用户
        when(userProvider.checkUser(userId)).thenReturn(userEntity);
        // 模拟用户信息
        UserModel userModel = new UserModel();
        // 模拟获取用户信息
        when(userDao.getUserById(userId)).thenReturn(userModel);

        // 模拟机构信息
        AgencyModel agencyModel = new AgencyModel();
        // 模拟获取机构信息
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);

        // 模拟培训课程模块开关
        List<AgencyMetaDataEntity> metaDataList = new ArrayList<>();
        // RLP 基础课程
        AgencyMetaDataEntity rlpBasicMetadata = new AgencyMetaDataEntity();
        rlpBasicMetadata.setMetaKey(AgencyMetaKey.TRAINING_MODULE_RLP_BASIC.toString()); // 开关键值
        rlpBasicMetadata.setMetaValue(Boolean.TRUE.toString()); // 开关状态
        metaDataList.add(rlpBasicMetadata);
        // RLP 高级课程
        AgencyMetaDataEntity rlpAdvancedMetadata = new AgencyMetaDataEntity();
        rlpAdvancedMetadata.setMetaKey(AgencyMetaKey.TRAINING_MODULE_RLP_ADVANCED_I.toString()); // 开关键值
        rlpAdvancedMetadata.setMetaValue(Boolean.TRUE.toString()); // 开关状态
        metaDataList.add(rlpAdvancedMetadata);
        // 模拟查询机构 Metadata
        when(agencyDao.getAllMetaData(any())).thenReturn(metaDataList);

        // 模拟培训模块
        List<TrainingModuleModel> moduleModels = new ArrayList<>();
        // RLP 模块
        TrainingModuleModel rlpModule = new TrainingModuleModel();
        rlpModule.setName(TrainingModules.RLP.getModuleName());
        moduleModels.add(rlpModule);
        // 模拟查询模块
        when(metaDao.getAppMeta(any())).thenReturn(JsonUtil.toJson(moduleModels));

        // 模拟根据模块查询视频 ID 列表
        when(trainingResourceDao.getMediaIdsByModuleAndTypes(anyString(), any())).thenReturn(new ArrayList<>());

        // 模拟视频信息
        List<TrainingMediaEntity> mediaEntities = new ArrayList<>();
        TrainingMediaEntity mediaEntity = new TrainingMediaEntity();
        mediaEntity.setVideoId("test-video1-id"); // 视频 ID
        mediaEntities.add(mediaEntity);
        // 模拟根据 ID 获取视频信息
        when(trainingResourceDao.getTrainingMediasByIds(any())).thenReturn(mediaEntities);

        // 模拟获取观看记录，无观看记录的情况
        when(mediaBookDao.getWatchRecordsByUserIdAndShareIds(anyString(), any())).thenReturn(new ArrayList<>());
        // 模拟获取证书，无证书的情况
        when(trainingCertificateDao.getCertificateList(anyString(), any())).thenReturn(new ArrayList<>());
        // 模拟视频详情结果数据
        ResultPojo resultPojo = new ResultPojo();
        resultPojo.setStatus(200);
        resultPojo.setData(JsonUtil.toJson(new VimeoModel()));
        CacheModel cacheModel = new CacheModel();
        cacheModel.setValue(JsonUtil.toJson(resultPojo));
        when(cacheService.get(CacheKey.TRAINING_VIDEO + "test-video1-id")).thenReturn(cacheModel); // 模拟缓存存在视频信息

        // 调用方法
        List<TrainingMediasResponse> mediaList = trainingService.getMediaList(userId);

        // 验证结果
        assertEquals(1, mediaList.size()); // 模块数量为 1
        assertEquals(TrainingModules.RLP.getModuleName(), mediaList.get(0).getModuleName()); // 验证模块名称
    }

    /**
     * 获取用户培训课程模块
     * Case: 家庭助手角色，只能获取家园互动、In-Kind、DHC 三个模块
     */
    @Test
    public void testSetTrainingModulesForFamilyServiceRole() {
        // 数据模拟
        UserEntity userEntity = new UserEntity(); // 用户信息
        userEntity.setId("userId01");
        userEntity.setRole(UserRole.FAMILY_SERVICE.toString());
        AgencyModel agencyModel = new AgencyModel(); // 机构信息
        agencyModel.setId("agencyId01");
        List<AgencyMetaDataEntity> allMetaData = new ArrayList<>(); // 机构开通培训课程 Metadata
        AgencyMetaDataEntity metaDataEntity1 = new AgencyMetaDataEntity();
        metaDataEntity1.setMetaKey(AgencyMetaKey.TRAINING_MODULE_FE_BASIC.toString()); // FE 基础课程
        metaDataEntity1.setMetaValue(Boolean.TRUE.toString());
        allMetaData.add(metaDataEntity1);
        AgencyMetaDataEntity metaDataEntity2 = new AgencyMetaDataEntity();
        metaDataEntity2.setMetaKey(AgencyMetaKey.TRAINING_MODULE_IN_KIND_BASIC.toString()); // In-Kind 基础课程
        metaDataEntity2.setMetaValue(Boolean.TRUE.toString());
        allMetaData.add(metaDataEntity2);
        AgencyMetaDataEntity metaDataEntity3 = new AgencyMetaDataEntity();
        metaDataEntity3.setMetaKey(AgencyMetaKey.TRAINING_MODULE_DHC_BASIC.toString()); // DHC 基础课程
        metaDataEntity3.setMetaValue(Boolean.TRUE.toString());
        allMetaData.add(metaDataEntity3);
        String teacherTrainingModules = "[{\"name\":\"Portfolio Assessment\",\"description\":\"Learn how to observe and implement intentional, meaningful observation notes. Learn how to analyze and review assessment progress reports to make data driven decisions.\"},{\"name\":\"Family Engagement\",\"description\":\"Learn how to successfully engage your families through Learning Genie and make data driven decisions.\"},{\"name\":\"In-Kind\",\"description\":\"Learn how to assign In-Kind activities, and review In-Kind submissions from families and volunteers. Download reports and review the In-Kind dashboard to make data driven decisions.\"},{\"name\":\"DHC/Attendance\",\"description\":\"Learn how to ensure child's health and safety with Learning Genie's DHC feature. How to sign in/out children, and download to view child's attendance reports and daily health card statistics.\"},{\"name\":\"Reflective Planning\",\"description\":\"Learn how to design developmentally appropriate lesson plans, weekly schedules based on children's insights, data-driven strategies, following a cyclical approach of observation, reflection, and adjustment to enhance children's learning and development.\"},{\"name\":\"Webinar\",\"description\":\"Learn how to optimize the use of Learning Genie to enhance your efficiency, allowing you to dedicate more time to children's learning.\"}]";

        // 方法模拟
        when(userProvider.getAgencyByUserId("userId01")).thenReturn(agencyModel); // 模拟获取机构
        when(agencyDao.getAllMetaData("agencyId01")).thenReturn(allMetaData); // 模拟获取机构 Metadata
        when(metaDao.getAppMeta(AppMetaKey.TRAINING_MEDIA_MODULE_TEACHER.toString())).thenReturn(teacherTrainingModules);  // 模拟获取教师培训课程模块

        // 调用方法
        List<TrainingModuleModel> trainingModuleModels = trainingService.setTrainingModule(userEntity, false);

        // 验证结果
        assertTrue(trainingModuleModels.stream().anyMatch(module -> module.getName().equals(TrainingModules.FAMILY_ENGAGEMENT.getModuleName()))); // 验证家园互动模块已开通
        assertTrue(trainingModuleModels.stream().anyMatch(module -> module.getName().equals(TrainingModules.IN_KIND.getModuleName()))); // 验证 In-Kind 模块已开通
        assertTrue(trainingModuleModels.stream().anyMatch(module -> module.getName().equals(TrainingModules.DHC.getModuleName()))); // 验证 DHC 模块已开通
    }

    /**
     * 获取用户培训课程模块
     * Case: 管理员角色，获取所有模块
     */
    @Test
    public void testSetTrainingModulesForAdminRole() {
        // 数据模拟
        UserEntity userEntity = new UserEntity(); // 用户信息
        userEntity.setId("userId01");
        userEntity.setRole(UserRole.AGENCY_ADMIN.toString());
        AgencyModel agencyModel = new AgencyModel(); // 机构信息
        agencyModel.setId("agencyId01");
        List<AgencyMetaDataEntity> allMetaData = new ArrayList<>(); // 机构开通培训课程 Metadata
        AgencyMetaDataEntity metaDataEntity1 = new AgencyMetaDataEntity();
        metaDataEntity1.setMetaKey(AgencyMetaKey.TRAINING_MODULE_FE_BASIC.toString()); // FE 基础课程
        metaDataEntity1.setMetaValue(Boolean.TRUE.toString());
        allMetaData.add(metaDataEntity1);
        AgencyMetaDataEntity metaDataEntity2 = new AgencyMetaDataEntity();
        metaDataEntity2.setMetaKey(AgencyMetaKey.TRAINING_MODULE_IN_KIND_BASIC.toString()); // In-Kind 基础课程
        metaDataEntity2.setMetaValue(Boolean.TRUE.toString());
        allMetaData.add(metaDataEntity2);
        AgencyMetaDataEntity metaDataEntity3 = new AgencyMetaDataEntity();
        metaDataEntity3.setMetaKey(AgencyMetaKey.TRAINING_MODULE_DHC_BASIC.toString()); // DHC 基础课程
        metaDataEntity3.setMetaValue(Boolean.TRUE.toString());
        allMetaData.add(metaDataEntity3);
        AgencyMetaDataEntity metaDataEntity4 = new AgencyMetaDataEntity();
        metaDataEntity4.setMetaKey(AgencyMetaKey.TRAINING_MODULE_PA_BASIC.toString()); // PA 基础课程
        metaDataEntity4.setMetaValue(Boolean.TRUE.toString());
        allMetaData.add(metaDataEntity4);
        AgencyMetaDataEntity metaDataEntity5 = new AgencyMetaDataEntity();
        metaDataEntity5.setMetaKey(AgencyMetaKey.TRAINING_MODULE_RLP_BASIC.toString()); // RLP 基础课程
        metaDataEntity5.setMetaValue(Boolean.TRUE.toString());
        allMetaData.add(metaDataEntity5);
        AgencyMetaDataEntity metaDataEntity6 = new AgencyMetaDataEntity();
        metaDataEntity6.setMetaKey(AgencyMetaKey.TRAINING_MODULE_RLP_ADVANCED_I.toString()); // RLP 高级课程
        metaDataEntity6.setMetaValue(Boolean.TRUE.toString());
        allMetaData.add(metaDataEntity6);
        AgencyMetaDataEntity metaDataEntity7 = new AgencyMetaDataEntity();
        metaDataEntity7.setMetaKey(AgencyMetaKey.TRAINING_MODULE_PA_BASIC.toString()); // PA 高级课程
        metaDataEntity7.setMetaValue(Boolean.TRUE.toString());
        allMetaData.add(metaDataEntity7);
        AgencyMetaDataEntity metaDataEntity8 = new AgencyMetaDataEntity();
        metaDataEntity8.setMetaKey(AgencyMetaKey.TRAINING_MODULE_DHC_ADVANCED_I.toString()); // DHC 高级课程
        metaDataEntity8.setMetaValue(Boolean.TRUE.toString());
        allMetaData.add(metaDataEntity8);
        AgencyMetaDataEntity metaDataEntity9 = new AgencyMetaDataEntity();
        metaDataEntity9.setMetaKey(AgencyMetaKey.TRAINING_MODULE_IN_KIND_ADVANCED_I.toString()); // In-Kind 高级课程
        metaDataEntity9.setMetaValue(Boolean.TRUE.toString());
        allMetaData.add(metaDataEntity9);
        AgencyMetaDataEntity metaDataEntity10 = new AgencyMetaDataEntity();
        metaDataEntity10.setMetaKey(AgencyMetaKey.TRAINING_MODULE_FE_ADVANCED_I.toString()); // FE 高级课程
        metaDataEntity10.setMetaValue(Boolean.TRUE.toString());
        allMetaData.add(metaDataEntity10);
        //
        String teacherTrainingModules = "[{\"name\":\"Portfolio Assessment\",\"description\":\"Learn how to observe and implement intentional, meaningful observation notes. Learn how to analyze and review assessment progress reports to make data driven decisions.\"},{\"name\":\"Family Engagement\",\"description\":\"Learn how to successfully engage your families through Learning Genie and make data driven decisions.\"},{\"name\":\"In-Kind\",\"description\":\"Learn how to setup In-Kind, assign In-Kind activities, and review In-Kind submissions from families and volunteers. Download reports and review the In-Kind dashboard to make data driven decisions.\"},{\"name\":\"DHC/Attendance\",\"description\":\"Learn how to ensure child's health and safety with Learning Genie's DHC feature. How to sign in/out children, and download to view child's attendance reports and daily health card statistics.\"},{\"name\":\"Reflective Planning\",\"description\":\"Learn how to design developmentally appropriate lesson plans, weekly schedules based on children's insights, data-driven strategies, following a cyclical approach of observation, reflection, and adjustment to enhance children's learning and development.\"},{\"name\":\"Webinars\",\"description\":\"Learn how to optimize the use of Learning Genie to enhance your efficiency, allowing you to dedicate more time to children's learning.\"}]";

        // 方法模拟
        when(userProvider.getAgencyByUserId("userId01")).thenReturn(agencyModel); // 模拟获取机构
        when(agencyDao.getAllMetaData("agencyId01")).thenReturn(allMetaData); // 模拟获取机构 Metadata
        when(metaDao.getAppMeta(AppMetaKey.TRAINING_MEDIA_MODULE_ADMIN.toString())).thenReturn(teacherTrainingModules);  // 模拟获取教师培训课程模块

        // 调用方法
        List<TrainingModuleModel> trainingModuleModels = trainingService.setTrainingModule(userEntity, false);

        // 验证结果
        assertTrue(trainingModuleModels.stream().anyMatch(module -> module.getName().equals(TrainingModules.FAMILY_ENGAGEMENT.getModuleName()))); // 验证家园互动模块已开通
        assertTrue(trainingModuleModels.stream().anyMatch(module -> module.getName().equals(TrainingModules.IN_KIND.getModuleName()))); // 验证 In-Kind 模块已开通
        assertTrue(trainingModuleModels.stream().anyMatch(module -> module.getName().equals(TrainingModules.DHC.getModuleName()))); // 验证 DHC 模块已开通
        assertTrue(trainingModuleModels.stream().anyMatch(module -> module.getName().equals(TrainingModules.PORTFOLIO_ASSESSMENT.getModuleName()))); // 验证 Reflective Planning 模块已开通
        assertTrue(trainingModuleModels.stream().anyMatch(module -> module.getName().equals(TrainingModules.RLP.getModuleName()))); // 验证 RLP 模块已开通
    }

    /**
     * 测试获取培训课程模块
     * Case: 包括 Webinar 模块
     */
    @Test
    public void testSetTrainingModulesWithWebinar() {
        // 数据模拟
        UserEntity userEntity = new UserEntity(); // 用户信息
        userEntity.setId("userId01");
        userEntity.setRole(UserRole.FAMILY_SERVICE.toString());
        AgencyModel agencyModel = new AgencyModel(); // 机构信息
        agencyModel.setId("agencyId01");
        List<AgencyMetaDataEntity> allMetaData = new ArrayList<>(); // 机构开通培训课程 Metadata
        AgencyMetaDataEntity metaDataEntity1 = new AgencyMetaDataEntity();
        metaDataEntity1.setMetaKey(AgencyMetaKey.TRAINING_MODULE_FE_BASIC.toString()); // FE 基础课程
        metaDataEntity1.setMetaValue(Boolean.TRUE.toString());
        allMetaData.add(metaDataEntity1);
        String teacherTrainingModules = "[{\"name\":\"Portfolio Assessment\",\"description\":\"Learn how to observe and implement intentional, meaningful observation notes. Learn how to analyze and review assessment progress reports to make data driven decisions.\"},{\"name\":\"Family Engagement\",\"description\":\"Learn how to successfully engage your families through Learning Genie and make data driven decisions.\"},{\"name\":\"In-Kind\",\"description\":\"Learn how to assign In-Kind activities, and review In-Kind submissions from families and volunteers. Download reports and review the In-Kind dashboard to make data driven decisions.\"},{\"name\":\"DHC/Attendance\",\"description\":\"Learn how to ensure child's health and safety with Learning Genie's DHC feature. How to sign in/out children, and download to view child's attendance reports and daily health card statistics.\"},{\"name\":\"Reflective Planning\",\"description\":\"Learn how to design developmentally appropriate lesson plans, weekly schedules based on children's insights, data-driven strategies, following a cyclical approach of observation, reflection, and adjustment to enhance children's learning and development.\"},{\"name\":\"Webinars\",\"description\":\"Learn how to optimize the use of Learning Genie to enhance your efficiency, allowing you to dedicate more time to children's learning.\"}]";

        // 方法模拟
        when(userProvider.getAgencyByUserId("userId01")).thenReturn(agencyModel); // 模拟获取机构
        when(agencyDao.getAllMetaData("agencyId01")).thenReturn(allMetaData); // 模拟获取机构 Metadata
        when(metaDao.getAppMeta(AppMetaKey.TRAINING_MEDIA_MODULE_TEACHER.toString())).thenReturn(teacherTrainingModules);  // 模拟获取教师培训课程模块

        // 调用方法
        List<TrainingModuleModel> trainingModuleModels = trainingService.setTrainingModule(userEntity, true);

        // 验证结果
        assertTrue(trainingModuleModels.stream().anyMatch(module -> module.getName().equals(TrainingModules.WEBINARS.getModuleName()))); // 验证 Webinar 模块已开通
    }

    /**
     * 测试获取证书列表
     */
    @Test
    public void testGetCertificateList() {
        // 用户 ID
        String userId = UUID.randomUUID().toString();

        // 模拟用户信息
        UserEntity userEntity = new UserEntity();
        userEntity.setRole(UserRole.AGENCY_OWNER.toString()); // 角色
        // 模拟获取用户
        when(userProvider.getUser(userId)).thenReturn(userEntity);

        // 模拟培训课程模块开关
        List<AgencyMetaDataEntity> metaDataList = new ArrayList<>();
        // RLP 基础课程
        AgencyMetaDataEntity rlpBasicMetadata = new AgencyMetaDataEntity();
        rlpBasicMetadata.setMetaKey(AgencyMetaKey.TRAINING_MODULE_RLP_BASIC.toString()); // 开关键值
        rlpBasicMetadata.setMetaValue(Boolean.TRUE.toString()); // 开关状态
        metaDataList.add(rlpBasicMetadata);
        // RLP 高级课程
        AgencyMetaDataEntity rlpAdvancedMetadata = new AgencyMetaDataEntity();
        rlpAdvancedMetadata.setMetaKey(AgencyMetaKey.TRAINING_MODULE_RLP_ADVANCED_I.toString()); // 开关键值
        rlpAdvancedMetadata.setMetaValue(Boolean.TRUE.toString()); // 开关状态
        metaDataList.add(rlpAdvancedMetadata);
        // 模拟查询机构 Metadata
        when(agencyDao.getAllMetaData(any())).thenReturn(metaDataList);

        // 模拟培训模块
        List<TrainingModuleModel> moduleModels = new ArrayList<>();
        // RLP 模块
        TrainingModuleModel rlpModule = new TrainingModuleModel();
        rlpModule.setName(TrainingModules.RLP.getModuleName());
        moduleModels.add(rlpModule);
        // 模拟查询模块
        when(metaDao.getAppMeta(any())).thenReturn(JsonUtil.toJson(moduleModels));

        // 模拟根据模块查询视频 ID 列表
        when(trainingResourceDao.getMediaIdsByModuleAndTypesWithDeleted(anyString(), any())).thenReturn(new ArrayList<>());

        // 删除的视频 ID
        String deletedMediaId = "deleted-media-1-id";
        // 模拟证书信息
        List<TrainingCertificateEntity> certificateList = new ArrayList<>();
        // 模拟证书 1 信息
        TrainingCertificateEntity certificate1 = new TrainingCertificateEntity();
        certificate1.setName("test-certificate-1"); // 证书名称
        certificate1.setTrainingMediaId(deletedMediaId); // 视频 ID
        certificateList.add(certificate1);
        // 模拟证书 2 信息
        TrainingCertificateEntity certificate2 = new TrainingCertificateEntity();
        certificate2.setName("test-certificate-2"); // 证书名称
        certificate2.setTrainingMediaId("media-2-id"); // 视频 ID
        certificateList.add(certificate2);
        // 模拟获取证书
        when(trainingCertificateDao.getCertificateList(anyString(), any())).thenReturn(certificateList);

        // 模拟用户信息
        UserModel user = new UserModel();
        user.setId(userId); // 用户 ID
        // 模拟获取用户信息
        when(userDao.getUserById(any())).thenReturn(user);

        // 模拟视频信息
        List<TrainingMediaEntity> trainingMediaEntities = new ArrayList<>();
        // 模拟视频 1
        TrainingMediaEntity mediaEntity1 = new TrainingMediaEntity();
        mediaEntity1.setId(deletedMediaId); // 视频 ID
        mediaEntity1.setVideoId("media-1-video-id");
        mediaEntity1.setType(TrainingTypes.BASIC.getType()); // 视频类型
        mediaEntity1.setDeleted(true); // 是否删除
        trainingMediaEntities.add(mediaEntity1);
        // 模拟视频 2
        TrainingMediaEntity mediaEntity2 = new TrainingMediaEntity();
        mediaEntity2.setId(certificate2.getTrainingMediaId()); // 视频 ID
        mediaEntity2.setVideoId("media-2-video-id");
        mediaEntity2.setType(TrainingTypes.ADVANCED_I.getType()); // 视频类型
        trainingMediaEntities.add(mediaEntity2);
        // 模拟获取视频
        when(trainingResourceDao.getTrainingMediasByIdsWithDeleted(any())).thenReturn(trainingMediaEntities);

        // 模拟获取时区
        when(userProvider.getTimezoneOffsetNum()).thenReturn(-7);
        when(cacheService.get(CacheKey.TRAINING_VIDEO + "media-1-video-id")).thenReturn(null); // 模拟缓存中没有视频信息
        when(cacheService.get(CacheKey.TRAINING_VIDEO + "media-2-video-id")).thenReturn(null); // 模拟缓存中没有视频信息


        // 模拟视频详情结果数据
        ResultPojo resultPojo = new ResultPojo();
        resultPojo.setStatus(200);
        // 模拟视频信息
        VimeoModel vimeoModel = new VimeoModel();
        vimeoModel.setDuration("121"); // 视频时长
        vimeoModel.setVideoId(mediaEntity1.getVideoId()); // 视频 ID
        resultPojo.setData(JsonUtil.toJson(vimeoModel));
        try (MockedStatic<RestApiUtil> restApiUtil = Mockito.mockStatic(RestApiUtil.class)) {
            // 模拟获取视频详情
            restApiUtil.when(() -> RestApiUtil.get(anyString(), any(), anyInt())).thenReturn(resultPojo);

            // 调用方法
            CertificatesResponse certificatesResponse = trainingService.getCertificateList(userId);
            List<CertificateModel> certificates = certificatesResponse.getCertificates();

            // 验证结果
            assertEquals(2, certificates.size()); // 证书数量为 2
            assertTrue(certificates.get(0).isArchived()); // 第一个证书为归档
            assertFalse(certificates.get(1).isArchived()); // 第二个证书不是归档
        }
    }

}

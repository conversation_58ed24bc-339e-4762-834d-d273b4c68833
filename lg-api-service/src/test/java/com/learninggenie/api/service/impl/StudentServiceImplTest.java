package com.learninggenie.api.service.impl;

import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.model.GetChildClassIdAvailableResponse;
import com.learninggenie.api.model.RatingPeriod;
import com.learninggenie.api.model.group.InvitationRequest;
import com.learninggenie.api.model.student.*;
import com.learninggenie.api.provider.*;
import com.learninggenie.api.security.AuthUserDetails;
import com.learninggenie.api.service.CommonService;
import com.learninggenie.api.service.EmailService;
import com.learninggenie.api.service.PeriodService;
import com.learninggenie.common.cache.CacheService;
import com.learninggenie.common.comm.CommService;
import com.learninggenie.common.communication.CommunicationService;
import com.learninggenie.common.data.dao.*;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.dto.EnrollmentDTO;
import com.learninggenie.common.data.entity.CenterEntity;
import com.learninggenie.common.data.entity.DomainEntity;
import com.learninggenie.common.data.entity.GroupEntity;
import com.learninggenie.common.data.entity.UserEntity;
import com.learninggenie.common.data.entity.*;
import com.learninggenie.common.data.enums.*;
import com.learninggenie.common.data.model.NoteEntity;
import com.learninggenie.common.data.model.*;
import com.learninggenie.common.data.model.framework.GroupFrameworkStatsModel;
import com.learninggenie.common.data.model.note.AutoGenerateNote;
import com.learninggenie.common.data.repository.EnrollmentRepository;
import com.learninggenie.common.data.repository.GroupPeriodRepository;
import com.learninggenie.common.data.repository.GroupRepository;
import com.learninggenie.common.filesystem.FileSystem;
import com.learninggenie.common.framwork.FrameworkProvider;
import com.learninggenie.common.region.RegionService;
import com.learninggenie.common.report.AnalysisService;
import com.learninggenie.common.report.ChildAttrGroup;
import com.learninggenie.common.score.RatingService;
import com.learninggenie.common.utils.RateUtil;
import com.learninggenie.common.utils.StringUtil;
import com.learninggenie.common.utils.TimeUtil;
import com.learninggenie.common.weekly.WeeklyService;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;
import org.springframework.test.util.ReflectionTestUtils;
import org.mockito.quality.Strictness;

import java.lang.reflect.Method;
import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class StudentServiceImplTest {
    @Mock
    private NoteDao noteDao;

    @Mock
    private ScoreDao scoreDao;

    @Mock
    private GroupDao groupDao;

    @Mock
    private CenterDao centerDao;

    @Mock
    private PortfolioDao portfolioDao;

    @Mock
    private com.learninggenie.common.messaging.EmailService emailService;

    @Mock
    private CommonService commonService;

    @Mock
    private EnrollmentRepository enrollmentRepository;

    @InjectMocks
    private StudentServiceImpl studentService;

    @Mock
    private StudentDao studentDao;

    @Mock
    private UserProvider userProvider;

    @Mock
    private GroupRepository groupRepository;

    @Mock
    private AgencyDao agencyDao;

    @Mock
    private CenterProvider centerProvider;

    @Mock
    private UserDaoImpl userDao;

    @Mock
    private EmailService emailServiceImpl;

    @Mock
    private FileSystem fileSystem;

    @Mock
    private RatingService ratingService;

    @Mock
    private EnrollmentProvider enrollmentProvider;

    @Mock
    private MediaProvider mediaProvider;

    @Mock
    private DomainDao domainDao;

    @Mock
    private AnalysisService analysisService;

    @Mock
    private GroupProvider groupProvider;

    @Mock
    private WeeklyService weeklyService;

    @Mock
    private UsersFileDao usersFileDao;

    @Mock
    private CommunicationService communicationService;

    @Mock
    private GroupPeriodRepository groupPeriodRepository;

    @Mock
    private CommService commService;

    @Mock
    private RegionService regionService;

    @Mock
    private CacheService redisCacheServiceImpl;

    @Mock
    private InvitationDao invitationDao;

    @Mock
    private InvitationsEnrollmentInvitationDao invitationsEnrollmentInvitationDao;

    @Mock
    private PeriodsGroupDao periodsGroupDao;

    @Mock
    private PeriodService periodService;

    @Mock
    private EnrollmentDao enrollmentDao;

    @Mock
    private EventActionDao eventActionDao;

    @Mock
    private DashboardDao dashboardDao;

    @Mock
    private ImportErrorDao importErrorDao;

    @Mock
    private FrameworkProvider frameworkProvider;

    /**
     * 按日期获取学生的评分记录，并计算最高分
     * 秦浩然 创建于2012/02/22
     */
    @Test
    public void testGetScoresByDate() {
        EnrollmentEntity student = new EnrollmentEntity();
        student.setId("1");
        GroupEntity group = new GroupEntity();
        group.setId("1");
        student.setGroup(group);
        when(enrollmentRepository.findById("1")).thenReturn(Optional.ofNullable(student));

        com.learninggenie.common.data.model.GroupEntry g = new com.learninggenie.common.data.model.GroupEntry();
        //g.setDomainId("1");
        when(groupDao.getGroup("1")).thenReturn(g);

        List<StudentScoreEntity> scores = new ArrayList<>();
        StudentScoreEntity score1 = new StudentScoreEntity();
        score1.setId("1");
        score1.setLevelId("1");
        score1.setNoteId("1");
        scores.add(score1);
        StudentScoreEntity score2 = new StudentScoreEntity();
        score2.setId("2");
        score2.setLevelId("2");
        score2.setNoteId("1");
        scores.add(score2);
        when(scoreDao.get("1")).thenReturn(scores);

        List<NoteEntity> notes = new ArrayList<>();
        NoteEntity note1 = new NoteEntity();
        note1.setId("1");
        notes.add(note1);
        NoteEntity note2 = new NoteEntity();
        note2.setId("2");
        notes.add(note2);
        when(noteDao.getDomainNotes(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(notes);

//        ScoreTemplateEntity scoreTemplate = new ScoreTemplateEntity();
//        when(portfolioDao.loadScoreTemplate(Mockito.anyString())).thenReturn(scoreTemplate);

        LevelEntity level = new LevelEntity();
        level.setId("1");
        when(ratingService.getBestScore(anyList(), anyList())).thenReturn(level);

        List<StudentScoreEntity> result = studentService.getScoresByDate("1", "2016/01/01", "2016/02/02");
        for (StudentScoreEntity score : result) {
            assertEquals("1", score.getLevelId());
        }
    }

    /**
     * 测试自定义属性上移排序
     */
    @Test
    public void testSortAttr_Up() {
        EnrollmentAttrEntity enrollmentAttrEntity = new EnrollmentAttrEntity();
        enrollmentAttrEntity.setOrder(3);
        enrollmentAttrEntity.setAgencyId("67BA523B-9447-4237-B6FE-00579DBC5910");

        when(studentDao.getAttr(anyString())).thenReturn(enrollmentAttrEntity);

        studentService.sortAttr("FBFCFE73-5643-E611-8E4A-E09467EB0A90", 1);

        verify(studentDao, times(0)).updateAttrOrderDown(enrollmentAttrEntity.getOrder(), 1, enrollmentAttrEntity.getAgencyId());
        verify(studentDao, times(1)).updateAttrOrderUp(enrollmentAttrEntity.getOrder(), 1, enrollmentAttrEntity.getAgencyId());
        verify(studentDao, times(1)).updateAttrOrder("FBFCFE73-5643-E611-8E4A-E09467EB0A90", 1);
    }

    /**
     * 测试自定义属性下移排序
     */
    @Test
    public void testSortAttr_Down() {
        EnrollmentAttrEntity enrollmentAttrEntity = new EnrollmentAttrEntity();
        enrollmentAttrEntity.setOrder(2);
        enrollmentAttrEntity.setAgencyId("67BA523B-9447-4237-B6FE-00579DBC5910");

        when(studentDao.getAttr(anyString())).thenReturn(enrollmentAttrEntity);

        studentService.sortAttr("FB84AF45-5643-E611-8E4A-E09467EB0A90", 4);

        verify(studentDao, times(0)).updateAttrOrderUp(enrollmentAttrEntity.getOrder(), 4, enrollmentAttrEntity.getAgencyId());
        verify(studentDao, times(1)).updateAttrOrderDown(enrollmentAttrEntity.getOrder(), 4, enrollmentAttrEntity.getAgencyId());
        verify(studentDao, times(1)).updateAttrOrder("FB84AF45-5643-E611-8E4A-E09467EB0A90", 4);
    }

    @Ignore
    @Test
    public void testDeletePeriod() throws Exception {
        final List<RatingPeriodEntity> ratingPeriodEntityList = new ArrayList<>();
        RatingPeriodEntity ratingPeriodEntity1 = new RatingPeriodEntity();
        ratingPeriodEntity1.setId("p1");
        ratingPeriodEntity1.setToAtLocal(new Date("11/11/1111"));
        final RatingPeriodEntity ratingPeriodEntity2 = new RatingPeriodEntity();
        ratingPeriodEntity2.setId("p2");
        ratingPeriodEntity2.setToAtLocal(new Date("11/12/1111"));
        RatingPeriodEntity ratingPeriodEntity3 = new RatingPeriodEntity();
        ratingPeriodEntity3.setId("p3");
        ratingPeriodEntity3.setToAtLocal(new Date("11/13/1111"));
        ratingPeriodEntity3.setActived(true);
        ratingPeriodEntityList.add(ratingPeriodEntity1);
        ratingPeriodEntityList.add(ratingPeriodEntity2);
        ratingPeriodEntityList.add(ratingPeriodEntity3);

        RatingPeriodEntity ratingPeriodEntityS = new RatingPeriodEntity();
        ratingPeriodEntityS.setId("123");

        when(studentDao.getStudentAllPeriodByPeriodId(anyString())).thenReturn(ratingPeriodEntityList);

        doAnswer(new Answer() {
            @Override
            public List<RatingPeriodEntity> answer(InvocationOnMock invocationOnMock) throws Throwable {
                ratingPeriodEntityList.remove(2);
                return ratingPeriodEntityList;
            }
        }).when(studentDao).deletePeriod(anyString());

        when(studentDao.getCurrentPeriod(anyString())).thenReturn(ratingPeriodEntityS);

        doAnswer(new Answer() {
            @Override
            public String answer(InvocationOnMock invocationOnMock) throws Throwable {
                return "success";
            }
        }).when(studentDao).inActiveCurrentPeriod(anyString());

        doAnswer(new Answer() {
            @Override
            public String answer(InvocationOnMock invocationOnMock) throws Throwable {
                ratingPeriodEntity2.setActived(true);
                return "success";
            }
        }).when(studentDao).activePeriod(anyString());

        studentService.deletePeriod("periodId", true);

        //假装做了删除,以上集合和实体类都有值，中间没有出现任何异常
        assertEquals(2, ratingPeriodEntityList.size());
        //第二个为激活
        assertTrue(ratingPeriodEntity2.isActived() == true);
    }

    private AddStudentAttrsRequest getAddStudentAttrsRequest() {
        AddStudentAttrsRequest request = new AddStudentAttrsRequest();
        request.setAll(false);
        List<String> childIds = new ArrayList<>();
        childIds.add("08D78B7F-23A5-4A43-B00F-0002B64B1AA7");
        List<String> values = new ArrayList<>();
        values.add("12");
        List<StudentAttrWithValues> attrWithValues = new ArrayList<>();
        StudentAttrWithValues attrWithValue = new StudentAttrWithValues();
        attrWithValue.setName("08D78B7");
        attrWithValue.setValues(values);
        attrWithValues.add(attrWithValue);
        request.setChildIds(childIds);

        StudentsAttrValuesRequest attrValuesRequest = new StudentsAttrValuesRequest();
        attrValuesRequest.setName("08D78B7");
        attrValuesRequest.setTextboxValue("08D78B7");

        List<StudentsAttrValuesRequest> attrValuesRequests = new ArrayList<>();
        attrValuesRequests.add(attrValuesRequest);

        StudentsAttrRequest attrRequest = new StudentsAttrRequest();
        attrRequest.setName("08D78B7");
        attrRequest.setValues(attrValuesRequests);

        List<StudentsAttrRequest> attrRequests = new ArrayList<>();
        attrRequests.add(attrRequest);

        request.setAttrs(attrRequests);
        return request;
    }

    /**
     * 为学生添加自定义属性测试
     * 属性类型为单选，并且学生已经存在该属性值
     */
    @Ignore
    @Test
    public void testAddStudentAttrs_SingleExist() {
        AddStudentAttrsRequest request = this.getAddStudentAttrsRequest();

        EnrollmentAttrEntity attrEntity = new EnrollmentAttrEntity();
        when(studentDao.getAttr(anyString())).thenReturn(attrEntity);
        EnrollmentAttrTypeEntity attrTypeEntity = new EnrollmentAttrTypeEntity();
        attrTypeEntity.setValue(EnrollmentAttrType.CROSS_AGENCY_IDENTIFIER.toString());
        when(studentDao.getAttrType(anyString())).thenReturn(attrTypeEntity);
        when(enrollmentProvider.checkEnrollment(anyString())).thenReturn(new EnrollmentEntity());

        List<EnrollmentMetaDataEntity> studentAttrEntities = new ArrayList<>();
        EnrollmentMetaDataEntity enrollmentMetaDataEntity = new EnrollmentMetaDataEntity();
        enrollmentMetaDataEntity.setMetaValue("12");
        enrollmentMetaDataEntity.setMetaKey("");
        studentAttrEntities.add(enrollmentMetaDataEntity);
        when(studentDao.getMetas(anyString(), anyString())).thenReturn(studentAttrEntities);

        //CROSS_AGENCY_IDENTIFIER类型时，判断唯一性
        UserEntity user = new UserEntity();
        user.setRole(UserRole.AGENCY_ADMIN.toString());
        when(userProvider.checkUser(anyString())).thenReturn(user);
        AgencyModel agency = new AgencyModel();
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agency);
        when(studentDao.existCrossAgencyIdentifier(anyString(), anyString())).thenReturn(false);
        studentService.batchAddStudentAttrs(request, "111");
        verify(studentDao, times(0)).createOrUpdateMeta(anyString(), anyString(), anyString());
        verify(studentDao, times(0)).createMeta(anyString(), anyString(), anyString());
        verify(studentDao, times(0)).deleteMeta(anyString(), anyString(), anyString());
    }

    /**
     * 为学生添加自定义属性测试
     * 属性类型为单选，并且学生不存在该属性值
     */
    @Ignore
    @Test
    public void testAddStudentAttrs_SingleNotExist() {
        AddStudentAttrsRequest request = this.getAddStudentAttrsRequest();

        EnrollmentAttrEntity attrEntity = new EnrollmentAttrEntity();
        when(studentDao.getAttr(anyString())).thenReturn(attrEntity);
        EnrollmentAttrTypeEntity attrTypeEntity = new EnrollmentAttrTypeEntity();
        attrTypeEntity.setValue(EnrollmentAttrType.CROSS_AGENCY_IDENTIFIER.toString());
        when(studentDao.getAttrType(anyString())).thenReturn(attrTypeEntity);
        when(enrollmentProvider.checkEnrollment(anyString())).thenReturn(new EnrollmentEntity());

        List<EnrollmentMetaDataEntity> studentAttrEntities = new ArrayList<>();
        EnrollmentMetaDataEntity enrollmentMetaDataEntity = new EnrollmentMetaDataEntity();
        enrollmentMetaDataEntity.setMetaValue("1");
        enrollmentMetaDataEntity.setMetaKey("");
        studentAttrEntities.add(enrollmentMetaDataEntity);
        when(studentDao.getMetas(anyString(), anyString())).thenReturn(studentAttrEntities);

        //CROSS_AGENCY_IDENTIFIER类型时，判断唯一性
        UserEntity user = new UserEntity();
        user.setRole(UserRole.AGENCY_ADMIN.toString());
        when(userProvider.checkUser(anyString())).thenReturn(user);
        AgencyModel agency = new AgencyModel();
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agency);
        when(studentDao.existCrossAgencyIdentifier(anyString(), anyString())).thenReturn(false);
        studentService.batchAddStudentAttrs(request, "11");
        verify(studentDao, times(1)).createOrUpdateMeta(anyString(), anyString(), anyString());
        verify(studentDao, times(0)).createMeta(anyString(), anyString(), anyString());
        verify(studentDao, times(0)).deleteMeta(anyString(), anyString(), anyString());
    }

    /**
     * 为学生添加自定义属性测试
     * 属性类型为多，并且学生已存在该属性值
     */
    @Ignore
    @Test
    public void testAddStudentAttrs_MultipleExist() {
        AddStudentAttrsRequest request = this.getAddStudentAttrsRequest();

        EnrollmentAttrEntity attrEntity = new EnrollmentAttrEntity();
        when(studentDao.getAttr(anyString())).thenReturn(attrEntity);
        EnrollmentAttrTypeEntity attrTypeEntity = new EnrollmentAttrTypeEntity();
        attrTypeEntity.setValue(EnrollmentAttrType.MULTIPLE_CHOICES.toString());
        when(studentDao.getAttrType(anyString())).thenReturn(attrTypeEntity);
        when(enrollmentProvider.checkEnrollment(anyString())).thenReturn(new EnrollmentEntity());

        List<EnrollmentMetaDataEntity> studentAttrEntities = new ArrayList<>();
        EnrollmentMetaDataEntity enrollmentMetaDataEntity = new EnrollmentMetaDataEntity();
        enrollmentMetaDataEntity.setMetaValue("12");
        enrollmentMetaDataEntity.setMetaKey("");
        studentAttrEntities.add(enrollmentMetaDataEntity);
        when(studentDao.getMetas(anyString(), anyString())).thenReturn(studentAttrEntities);

        //CROSS_AGENCY_IDENTIFIER类型时，判断唯一性
        AgencyEntity agency = new AgencyEntity();
        when(agencyDao.getByAgencyAdmin(anyString())).thenReturn(agency);
        when(studentDao.existCrossAgencyIdentifier(anyString(), anyString())).thenReturn(false);
        studentService.batchAddStudentAttrs(request, "");
        verify(studentDao, times(0)).createOrUpdateMeta(anyString(), anyString(), anyString());
        verify(studentDao, times(0)).createMeta(anyString(), anyString(), anyString());
        verify(studentDao, times(0)).deleteMeta(anyString(), anyString(), anyString());
    }

    /**
     * 为学生添加自定义属性测试
     * 属性类型为多，并且学生不存在该属性值
     */
    @Ignore
    @Test
    public void testAddStudentAttrs_MultipleNotExist() {
        AddStudentAttrsRequest request = this.getAddStudentAttrsRequest();

        EnrollmentAttrEntity attrEntity = new EnrollmentAttrEntity();
        when(studentDao.getAttr(anyString())).thenReturn(attrEntity);
        EnrollmentAttrTypeEntity attrTypeEntity = new EnrollmentAttrTypeEntity();
        attrTypeEntity.setValue(EnrollmentAttrType.MULTIPLE_CHOICES.toString());
        when(studentDao.getAttrType(anyString())).thenReturn(attrTypeEntity);
        when(enrollmentProvider.checkEnrollment(anyString())).thenReturn(new EnrollmentEntity());

        List<EnrollmentMetaDataEntity> studentAttrEntities = new ArrayList<>();
        EnrollmentMetaDataEntity enrollmentMetaDataEntity = new EnrollmentMetaDataEntity();
        enrollmentMetaDataEntity.setMetaValue("11");
        enrollmentMetaDataEntity.setMetaKey("");
        studentAttrEntities.add(enrollmentMetaDataEntity);
        when(studentDao.getMetas(anyString(), anyString())).thenReturn(studentAttrEntities);

        //CROSS_AGENCY_IDENTIFIER类型时，判断唯一性
        AgencyEntity agency = new AgencyEntity();
        when(agencyDao.getByAgencyAdmin(anyString())).thenReturn(agency);
        when(studentDao.existCrossAgencyIdentifier(anyString(), anyString())).thenReturn(false);
        studentService.batchAddStudentAttrs(request, "");
        verify(studentDao, times(0)).createOrUpdateMeta(anyString(), anyString(), anyString());
        verify(studentDao, times(1)).createMeta(anyString(), anyString(), anyString());
        verify(studentDao, times(0)).deleteMeta(anyString(), anyString(), anyString());
    }

    /**
     * 为学生添加自定义属性测试
     * 属性类型是CROSS_AGENCY_IDENTIFIER，插入了重复的值
     */
    @Ignore
    @Test(expected = BusinessException.class)
    public void testAddStudentAttrs_IdentifierExist() {
        AddStudentAttrsRequest request = this.getAddStudentAttrsRequest();

        EnrollmentAttrEntity attrEntity = new EnrollmentAttrEntity();
        when(studentDao.getAttr(anyString())).thenReturn(attrEntity);
        EnrollmentAttrTypeEntity attrTypeEntity = new EnrollmentAttrTypeEntity();
        attrTypeEntity.setValue(EnrollmentAttrType.CROSS_AGENCY_IDENTIFIER.toString());
        when(studentDao.getAttrType(anyString())).thenReturn(attrTypeEntity);
        when(enrollmentProvider.checkEnrollment(anyString())).thenReturn(new EnrollmentEntity());

        List<EnrollmentMetaDataEntity> studentAttrEntities = new ArrayList<>();
        EnrollmentMetaDataEntity enrollmentMetaDataEntity = new EnrollmentMetaDataEntity();
        enrollmentMetaDataEntity.setMetaValue("11");
        enrollmentMetaDataEntity.setMetaKey(EnrollmentAttrType.CROSS_AGENCY_IDENTIFIER.toString());
        studentAttrEntities.add(enrollmentMetaDataEntity);
        when(studentDao.getMetas(anyString(), anyString())).thenReturn(studentAttrEntities);

        when(enrollmentProvider.getChildAttrByGroupId(anyString(), anyBoolean())).thenReturn(new ArrayList<StudentAttr>());

        //CROSS_AGENCY_IDENTIFIER类型时，判断唯一性
        UserEntity user = new UserEntity();
        user.setRole(UserRole.AGENCY_ADMIN.toString());
        when(userProvider.checkUser(anyString())).thenReturn(user);
        AgencyModel agency = new AgencyModel();
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agency);
        when(studentDao.existCrossAgencyIdentifier(anyString(), anyString())).thenReturn(true);
        studentService.batchAddStudentAttrs(request, "111");
    }

    /**
     * 孩子转班级
     * case： 老师没有权限进行转班级操作，抛异常
     * zjj 2016.7.25
     */
    @Ignore
    @Test(expected = BusinessException.class)
    public void testChangeChildGroupByTeacher() {
        String userId = "u001";
        String chilId = "child001";
        String fromGroupId = "g001";
        String toGroupId = "g002";
        EnrollmentEntity enrollment = new EnrollmentEntity();
        enrollment.setId(chilId);
        enrollment.setDisplayName("child001");
        Mockito.when(enrollmentRepository.findById(chilId)).thenReturn(Optional.of(enrollment));
        //学生当前所在的班级
        GroupEntity fromGroup = new GroupEntity();
        fromGroup.setId(fromGroupId);
        fromGroup.setName("g001");
        enrollment.setGroup(fromGroup);
        //学生要转到的班级
        GroupEntity toGroup = new GroupEntity();
        toGroup.setId(toGroupId);
        toGroup.setName("g002");
        Mockito.when(groupRepository.findById(toGroupId)).thenReturn(Optional.of(toGroup));

        UserEntity user = new UserEntity();
        user.setId("u001");
        user.setRole(UserRole.COLLABORATOR.toString());
        Mockito.when(userProvider.checkUser(userId)).thenReturn(user);
        studentService.changeChildGroup(chilId, toGroupId, userId, true, false, false, false);
    }

    /**
     * 孩子转班级
     * case： 要转到的班级没找到 抛异常
     * zjj 2016.7.25
     */
    @Test(expected = BusinessException.class)
    public void testChangeChildGroupGroupNotFound() {
        String userId = "u001";
        String chilId = "child001";
        String fromGroupId = "g001";
        String toGroupId = "g002";
        EnrollmentEntity enrollment = new EnrollmentEntity();
        enrollment.setId(chilId);
        enrollment.setDisplayName("child001");
        Mockito.when(enrollmentRepository.findById(chilId)).thenReturn(Optional.of(enrollment));
        //学生当前所在的班级
        GroupEntity fromGroup = new GroupEntity();
        fromGroup.setId(fromGroupId);
        fromGroup.setName("g001");
        enrollment.setGroup(fromGroup);
        Mockito.when(groupRepository.findById(toGroupId)).thenReturn(Optional.empty());
        studentService.changeChildGroup(chilId, toGroupId, userId, true, false, false, false);
    }

    @Ignore
    @Test
    public void testChangeChildGroup() {
        String userId = "u001";
        String childId = "child001";
        String fromGroupId = "g001";
        String toGroupId = "g002";
        EnrollmentEntity enrollment = new EnrollmentEntity();
        enrollment.setId(childId);
        enrollment.setDisplayName("child001");
        Mockito.when(enrollmentRepository.findById(childId)).thenReturn(Optional.of(enrollment));
        //学生当前所在的班级
        GroupEntity fromGroup = new GroupEntity();
        fromGroup.setId(fromGroupId);
        fromGroup.setName("g001");
        enrollment.setGroup(fromGroup);
        //学生要转到的班级
        GroupEntity toGroup = new GroupEntity();
        toGroup.setId(toGroupId);
        toGroup.setName("g002");
        Mockito.when(groupRepository.findById(toGroupId)).thenReturn(Optional.of(toGroup));
        UserEntity user = new UserEntity();
        user.setId("u001");
        user.setRole(UserRole.AGENCY_ADMIN.toString());
        Mockito.when(userProvider.checkUser(userId)).thenReturn(user);
        when(studentDao.updateVersion(anyString(), any(Date.class))).thenReturn(1);
        studentService.changeChildGroup(childId, toGroupId, userId, true, false, false, false);
        Mockito.verify(studentDao, times(1)).getChildNotes(childId);
        Mockito.verify(studentDao, times(1)).getChildNoteMetaData(childId);
        Mockito.verify(studentDao, times(1)).getNoteTag(childId);
        Mockito.verify(studentDao, times(1)).getNoteDomain(childId);
        Mockito.verify(studentDao, times(1)).getNoteMedia(childId);
        Mockito.verify(studentDao, times(1)).insertChildNote((List<NoteEntry>) any(NoteEntry.class));
        Mockito.verify(studentDao, times(1)).deleteRelationChildNote(childId);
    }

    /**
     * 创建学生属性
     * case： 用户不是agency owner/agency admin  抛异常
     * zjj 2016.7.26
     */
    @Test(expected = BusinessException.class)
    public void testCreateAttrUserUNAUTHORIZED() {
        String userId = "u001";
        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setRole(UserRole.SITE_ADMIN.toString());
        Mockito.when(userProvider.checkUser(userId)).thenReturn(user);
        CreateAttrRequest createAttrRequest = new CreateAttrRequest();
        studentService.createAttr(createAttrRequest, userId);
    }

    /**
     * 创建学生属性
     * case： 要创建的学生属性没有找到 抛异常
     * zjj 2016.7.26
     */
    @Test(expected = BusinessException.class)
    public void testCreateAttrAttrTypeNotFound() {
        String userId = "u001";
        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setRole(UserRole.AGENCY_ADMIN.toString());
        Mockito.when(userProvider.checkUser(userId)).thenReturn(user);
        CreateAttrRequest createAttrRequest = new CreateAttrRequest();
        createAttrRequest.setName("attrName");
        createAttrRequest.setTypeId("typeId");
        Mockito.when(studentDao.existAttrType(anyString())).thenReturn(false);
        studentService.createAttr(createAttrRequest, userId);
    }

    /**
     * 创建学生属性
     * case：要创建的学生属性已存在 抛异常
     * zjj 2016.7.26
     */
    @Test(expected = BusinessException.class)
    public void testCreateAttrAttrExist() {
        String userId = "u001";
        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setRole(UserRole.AGENCY_ADMIN.toString());
        Mockito.when(userProvider.checkUser(userId)).thenReturn(user);
        CreateAttrRequest createAttrRequest = new CreateAttrRequest();
        createAttrRequest.setName("attrName");
        createAttrRequest.setTypeId("typeId");
        Mockito.when(studentDao.existAttrType(anyString())).thenReturn(true);
        AgencyEntity agencyEntity = new AgencyEntity();
        agencyEntity.setId("a001");
        agencyEntity.setName("a001");
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("a001");
        agencyModel.setName("a001");
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
//        Mockito.when(agencyDao.getByAgencyAdmin(userId)).thenReturn(agencyEntity);
        Mockito.when(studentDao.existAttr(createAttrRequest.getName(), agencyEntity.getId())).thenReturn(true);
        studentService.createAttr(createAttrRequest, userId);
    }

    /**
     * 创建学生的属性
     * case: 正常创建
     * zjj 2016.7.26
     */
    @Test
    public void testCreateAttr() {
        String userId = "u001";
        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setRole(UserRole.AGENCY_ADMIN.toString());
        Mockito.when(userProvider.checkUser(userId)).thenReturn(user);
        CreateAttrRequest createAttrRequest = new CreateAttrRequest();
        createAttrRequest.setName("attrName");
        createAttrRequest.setTypeId("typeId");
        Mockito.when(studentDao.existAttrType(anyString())).thenReturn(true);
        AgencyEntity agencyEntity = new AgencyEntity();
        agencyEntity.setId("a001");
        agencyEntity.setName("a001");
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("a001");
        agencyModel.setName("a001");
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
//        Mockito.when(agencyDao.getByAgencyAdmin(userId)).thenReturn(agencyEntity);
        Mockito.when(studentDao.existAttr(createAttrRequest.getName(), agencyEntity.getId())).thenReturn(false);
        Mockito.when(studentDao.getAttrMaxOrder(agencyEntity.getId())).thenReturn(1);
        EnrollmentAttrEntity entity = new EnrollmentAttrEntity();
        entity.setId("attr001");
        entity.setName("attrName");
        entity.setTypeId("attrTypeId");
        Mockito.when(studentDao.createAttrWithValue(any(EnrollmentAttrEntity.class))).thenReturn(entity);
        EnrollmentAttrTypeEntity typeEntity = new EnrollmentAttrTypeEntity();
        typeEntity.setId("typeId");
        typeEntity.setName("typeName");
        typeEntity.setValue(EnrollmentAttrType.SINGLE_CHOICE.toString());
//        Mockito.when(studentDao.getAttrType(createAttrRequest.getTypeId())).thenReturn(typeEntity);
        studentService.createAttr(createAttrRequest, userId);
        Mockito.verify(studentDao, times(1)).createAttrWithValue(Mockito.any(EnrollmentAttrEntity.class));
        //Mockito.verify(studentDao,times(1)).getAttrTypeValue(Mockito.anyString());
    }

    /**
     * 删除学生属性
     */
    @Test
    public void testDeleteAttr() {
        String userId = "u001";
        String attrId = "attr001";
        studentService.deleteAttr(userId, attrId);
        Mockito.verify(studentDao, times(1)).deleteAttr(attrId);
    }

    /**
     * 根据用户id获取agency所有的属性
     * case：agency没有找到
     * zjj 2016.8.31
     */
    @Test
    public void testGetAttrs_agency_Not_Fountd() {
        String userId = "u001";
        String childId = "";
        //用户所属的agency
        AgencyModel agency = new AgencyModel();
        agency.setId("a001");
        agency.setName("a001");
        Mockito.when(userProvider.getAgencyByUserId(userId)).thenReturn(null);
        List<ChildAttrGroup> attrGroups = studentService.getAttrs(userId, null, childId, "", "", "");
        assertEquals(0, attrGroups.size());
    }

    /**
     * 根据用户的id获取agency的所有属性
     * 正常获取
     * zjj 2016.8.31
     */
    @Ignore
    @Test
    public void testGetAttrs() {
        String userId = "u001";
        String childId = "";
        //用户所属的agency
        AgencyModel agency = new AgencyModel();
        agency.setId("a001");
        agency.setName("a001");
        Mockito.when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        List<EnrollmentAttrEntity> enrollmentAttrEntityList = new ArrayList<>();
        //agency的所有属性
        EnrollmentAttrEntity enrollmentAttrEntity1 = new EnrollmentAttrEntity();
        enrollmentAttrEntity1.setId("attr001");
        enrollmentAttrEntity1.setName("attr001");
        enrollmentAttrEntity1.setDescription("desc001");
        enrollmentAttrEntity1.setRequired(true);
        enrollmentAttrEntity1.setTypeId("type001");
        enrollmentAttrEntity1.setTypeName("type001");
        enrollmentAttrEntity1.setTypeValue("type001");
        enrollmentAttrEntity1.setOrder(1);
        EnrollmentAttrEntity enrollmentAttrEntity2 = new EnrollmentAttrEntity();
        enrollmentAttrEntity2.setId("attr002");
        enrollmentAttrEntity2.setName("attr002");
        enrollmentAttrEntity2.setDescription("desc002");
        enrollmentAttrEntity2.setRequired(false);
        enrollmentAttrEntity2.setTypeId("type002");
        enrollmentAttrEntity2.setTypeName("type002");
        enrollmentAttrEntity2.setTypeValue("type002");
        enrollmentAttrEntity2.setOrder(1);
        enrollmentAttrEntityList.add(enrollmentAttrEntity1);
        enrollmentAttrEntityList.add(enrollmentAttrEntity2);
        Mockito.when(studentDao.getAttrs(anyString())).thenReturn(enrollmentAttrEntityList);
        List<ChildAttrGroup> studentAttrGroups = studentService.getAttrs(userId, null, childId, "", "", "");
        Assert.assertEquals(1, studentAttrGroups.size());
        Assert.assertEquals("attr001", studentAttrGroups.get(0).getAttrs().get(0).getId());
        Assert.assertEquals("attr002", studentAttrGroups.get(0).getAttrs().get(1).getId());
        Assert.assertEquals(false, studentAttrGroups.get(0).getAttrs().get(1).isRequired());
    }

    /**
     * 获取所有的属性类型
     * zjj 2016.8.31
     */
    @Test
    public void testGetAttrTypes() {
        List<EnrollmentAttrTypeEntity> enrollmentAttrTypeEntities = new ArrayList<>();
        EnrollmentAttrTypeEntity attrTypeEntity1 = new EnrollmentAttrTypeEntity();
        attrTypeEntity1.setId("type001");
        attrTypeEntity1.setName("name001");
        attrTypeEntity1.setValue("value001");
        attrTypeEntity1.setDescription("desc001");
        EnrollmentAttrTypeEntity attrTypeEntity2 = new EnrollmentAttrTypeEntity();
        attrTypeEntity2.setId("type002");
        attrTypeEntity2.setName("name002");
        attrTypeEntity2.setValue("value002");
        attrTypeEntity2.setDescription("desc001");
        enrollmentAttrTypeEntities.add(attrTypeEntity1);
        enrollmentAttrTypeEntities.add(attrTypeEntity2);
        Mockito.when(studentDao.getAttrTypes()).thenReturn(enrollmentAttrTypeEntities);
        List<StudentAttrType> studentAttrTypes = studentService.getAttrTypes();
        Assert.assertEquals(2, studentAttrTypes.size());
        Assert.assertEquals("type001", studentAttrTypes.get(0).getId());
        Assert.assertEquals("type002", studentAttrTypes.get(1).getId());
    }

    /**
     * 获取孩子API
     * case: 分页，页数不正确
     * zjj 2016.9.1
     */
    @Test(expected = BusinessException.class)
    public void testGetChildren_PageNumError() {
        String userId = "u001";
        String centerId = "c001";
        int pageSize = 2;
        int pageNum = 0;
        studentService.getChildren(userId, "", centerId, null, pageSize, pageNum, null, null, null, null, false, false);
    }

    /**
     * 获取孩子API
     * case:根据学校的Id获取孩子
     * zjj 2016.9.1
     */
    @Ignore
    @Test
    public void testGetChildren_ByCenterId() {
        String userId = "u001";
        String centerId = "c001";
        String sort = SortType.DISPLAYNAME.toString();
        String order = "Desc";
        int pageSize = 2;
        int pageNum = 1;
        //mock当前登录的用户
        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setRole(UserRole.AGENCY_ADMIN.toString());
        when(userProvider.checkUser(userId)).thenReturn(user);
        //mock当前用户所属的agency
        AgencyModel agency = new AgencyModel();
        agency.setId("agency001");
        agency.setName("agency001");
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        EnrollmentModel model = new EnrollmentModel();
        model.setId("c001");
        model.setFirstName("fName1");
        model.setLastName("lName1");
        model.setAvatarUrl("123.jpg");
        EnrollmentModel mode2 = new EnrollmentModel();
        mode2.setId("c002");
        mode2.setFirstName("fName2");
        mode2.setLastName("lName2");
        List<EnrollmentModel> enrollmentModelList = new ArrayList<>();
        enrollmentModelList.add(model);
        enrollmentModelList.add(mode2);
        when(studentDao.getChildrenByCenterId(anyString(), anyInt(), anyInt(), anyString(), anyList())).thenReturn(enrollmentModelList);
        when(studentDao.getChildrenCountByCenterId(anyString(), anyList())).thenReturn(enrollmentModelList.size());
        List<StudentAttrEntity> attrs = new ArrayList<>();
        StudentAttrEntity attr1 = new StudentAttrEntity();
        attr1.setAttrId("attr1");
        attr1.setEnrollmentId("c001");
        attr1.setAttrName("attrName");
        attr1.setAttrValue("attrValue");
        StudentAttrEntity attr2 = new StudentAttrEntity();
        attr2.setAttrId("attr2");
        attr2.setEnrollmentId("c001");
        attr2.setAttrName("attrName2");
        attr2.setAttrValue("attrValue2");
        attrs.add(attr1);
        attrs.add(attr2);
        when(studentDao.getAttrsByChildIds(anyList())).thenReturn(attrs);
        when(studentDao.getMetaValueByMetaKey(anyString())).thenReturn("[]");
        when(enrollmentProvider.getChildAttrByGroupId(anyString(), anyBoolean())).thenReturn(new ArrayList<StudentAttr>());

        List<EnrollmentAttrEntity> allAttrs = new ArrayList<>();
        EnrollmentAttrEntity all1 = new EnrollmentAttrEntity();
        all1.setName("attrName1");
        EnrollmentAttrEntity all2 = new EnrollmentAttrEntity();
        all1.setName("attrName2");
        allAttrs.add(all1);
        allAttrs.add(all2);
        when(studentDao.getAttrs(anyString())).thenReturn(allAttrs);
        when(domainDao.getFrameworkByEnrollmentId(anyString())).thenReturn(null);

        StudentResponseModel responseModel = studentService.getChildren(userId, "", centerId, null, pageSize, pageNum, null, sort, order, null, false, false);
        Assert.assertEquals(2, responseModel.getTotal());
        Assert.assertEquals("C001", responseModel.getResults().get(0).getId());
        Assert.assertEquals(6, responseModel.getResults().get(0).getAttrs().size());
        Assert.assertEquals(6, responseModel.getResults().get(1).getAttrs().size());
    }

    /**
     * 获取孩子API
     * case:根据班级的Id获取孩子
     * zjj 2016.9.1
     */
    @Ignore
    @Test
    public void testGetChildren_ByGroupId() {
        String userId = "u001";
        String groupId = "c001";
        String sort = SortType.GENDER.toString();
        String order = "Desc";
        int pageSize = 2;
        int pageNum = 1;
        //mock当前登录的用户
        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setRole(UserRole.AGENCY_ADMIN.toString());
        when(userProvider.checkUser(userId)).thenReturn(user);
        //mock当前用户所属的agency
        AgencyModel agency = new AgencyModel();
        agency.setId("agency001");
        agency.setName("agency001");
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        EnrollmentModel model = new EnrollmentModel();
        model.setId("c001");
        model.setFirstName("fName1");
        model.setLastName("lName1");
        model.setAvatarUrl("123.jpg");
        EnrollmentModel mode2 = new EnrollmentModel();
        mode2.setId("c002");
        mode2.setFirstName("fName2");
        mode2.setLastName("lName2");
        List<EnrollmentModel> enrollmentModelList = new ArrayList<>();
        enrollmentModelList.add(model);
        enrollmentModelList.add(mode2);
        when(studentDao.getChildrenByGroupId(anyString(), anyInt(), anyInt(), anyString(), anyList(), false)).thenReturn(enrollmentModelList);
        when(studentDao.getChildrenCountByGroupId(anyString(), anyList())).thenReturn(enrollmentModelList.size());
        List<StudentAttrEntity> attrs = new ArrayList<>();
        StudentAttrEntity attr1 = new StudentAttrEntity();
        attr1.setAttrId("attr1");
        attr1.setEnrollmentId("c001");
        attr1.setAttrName("attrName");
        attr1.setAttrValue("attrValue");
        StudentAttrEntity attr2 = new StudentAttrEntity();
        attr2.setAttrId("attr2");
        attr2.setEnrollmentId("c001");
        attr2.setAttrName("attrName2");
        attr2.setAttrValue("attrValue2");
        attrs.add(attr1);
        attrs.add(attr2);
        when(studentDao.getAttrsByChildIds(anyList())).thenReturn(attrs);
        when(studentDao.getMetaValueByMetaKey(anyString())).thenReturn("[]");
        when(enrollmentProvider.getChildAttrByGroupId(anyString(), anyBoolean())).thenReturn(new ArrayList<StudentAttr>());

        List<EnrollmentAttrEntity> allAttrs = new ArrayList<>();
        EnrollmentAttrEntity all1 = new EnrollmentAttrEntity();
        all1.setName("attrName1");
        EnrollmentAttrEntity all2 = new EnrollmentAttrEntity();
        all1.setName("attrName2");
        allAttrs.add(all1);
        allAttrs.add(all2);
        when(studentDao.getAttrs(anyString())).thenReturn(allAttrs);
        when(domainDao.getFrameworkByEnrollmentId(anyString())).thenReturn(null);

        StudentResponseModel responseModel = studentService.getChildren(userId, "", null, groupId, pageSize, pageNum, null, sort, order, null, false ,false);
        Assert.assertEquals(2, responseModel.getTotal());
        Assert.assertEquals("C001", responseModel.getResults().get(0).getId());
        Assert.assertEquals(6, responseModel.getResults().get(0).getAttrs().size());
        Assert.assertEquals(6, responseModel.getResults().get(1).getAttrs().size());
    }

    /**
     * 获取孩子API
     * case:根据用户的Id获取孩子
     * zjj 2016.9.1
     */
    @Ignore
    @Test
    public void testGetChildren_ByUserId() {
        String userId = "u001";
        String sort = SortType.FIRSTNAME.toString();
        String order = "Desc";
//        String search = "aaaaa";
        int pageSize = 2;
        int pageNum = 1;
        //mock当前登录的用户
        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setRole(UserRole.AGENCY_ADMIN.toString());
        when(userProvider.checkUser(userId)).thenReturn(user);
        //mock当前用户所属的agency
        AgencyModel agency = new AgencyModel();
        agency.setId("agency001");
        agency.setName("agency001");
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        EnrollmentModel model = new EnrollmentModel();
        model.setId("c001");
        model.setFirstName("fName1");
        model.setLastName("lName1");
        model.setAvatarUrl("123.jpg");
        EnrollmentModel mode2 = new EnrollmentModel();
        mode2.setId("c002");
        mode2.setFirstName("fName2");
        mode2.setLastName("lName2");
        List<EnrollmentModel> enrollmentModelList = new ArrayList<>();
        enrollmentModelList.add(model);
        enrollmentModelList.add(mode2);
        when(studentDao.getChildrenByUserId(any(UserEntity.class), anyInt(), anyInt(), anyString(), anyString(), anyList())).thenReturn(enrollmentModelList);
        when(studentDao.getChildrenCountByUserId(any(UserEntity.class), anyString(), anyString(), anyList())).thenReturn(enrollmentModelList.size());
        List<StudentAttrEntity> attrs = new ArrayList<>();
        StudentAttrEntity attr1 = new StudentAttrEntity();
        attr1.setAttrId("attr1");
        attr1.setEnrollmentId("c001");
        attr1.setAttrName("attrName1");
        attr1.setAttrValue("attrValue");
        StudentAttrEntity attr2 = new StudentAttrEntity();
        attr2.setAttrId("attr2");
        attr2.setEnrollmentId("c001");
        attr2.setAttrName("attrName2");
        attr2.setAttrValue("attrValue2");
        attrs.add(attr1);
        attrs.add(attr2);
        when(studentDao.getAttrsByChildIds(anyList())).thenReturn(attrs);
        when(studentDao.getMetaValueByMetaKey(anyString())).thenReturn("[]");
        when(enrollmentProvider.getChildAttrByGroupId(anyString(), anyBoolean())).thenReturn(new ArrayList<StudentAttr>());

        List<EnrollmentAttrEntity> allAttrs = new ArrayList<>();
        EnrollmentAttrEntity all1 = new EnrollmentAttrEntity();
        all1.setName("attrName1");
        EnrollmentAttrEntity all2 = new EnrollmentAttrEntity();
        all1.setName("attrName2");
        allAttrs.add(all1);
        allAttrs.add(all2);
        when(studentDao.getAttrs(anyString())).thenReturn(allAttrs);
        when(domainDao.getFrameworkByEnrollmentId(anyString())).thenReturn(null);

        StudentResponseModel responseModel = studentService.getChildren(userId, "", null, null, pageSize, pageNum, null, sort, order, null, false, false);
        Assert.assertEquals(2, responseModel.getTotal());
        Assert.assertEquals("C001", responseModel.getResults().get(0).getId());
        Assert.assertEquals(6, responseModel.getResults().get(0).getAttrs().size());
        Assert.assertEquals(6, responseModel.getResults().get(1).getAttrs().size());
    }

    /**
     * 测试获取Merge学生的信息
     */
    @Test
    public void testGetMergeChildInfo() {
        // 班级
        GroupEntity group = new GroupEntity();
        group.setId("g001");
        group.setName("G 001");
        // 学校
        CenterEntity center = new CenterEntity();
        center.setId("center001");
        center.setName("Center 001");
        group.setCenter(center);

        EnrollmentEntity child1 = new EnrollmentEntity();
        child1.setId("c001");
        child1.setDisplayName("c 001");
        child1.setGroup(group);
        // 头像
        MediaEntity avatar = new MediaEntity();
        avatar.setId("a001");
        avatar.setRelativePath("/upload/a001.jpg");
        child1.setAvatarMedia(avatar);

        EnrollmentEntity child2 = new EnrollmentEntity();
        child2.setId("c002");
        child2.setDisplayName("c 002");
        child2.setGroup(group);

        when(fileSystem.getPublicUrl("/upload/a001.jpg")).thenReturn("http://test.com/upoload/a001.jpg");
        when(studentDao.getChildWithGroupCenter("c001")).thenReturn(child1);
        when(studentDao.getChildWithGroupCenter("c002")).thenReturn(child2);
        when(studentDao.getNoteCount("c001")).thenReturn(10);
        when(studentDao.getNoteCount("c002")).thenReturn(0);

        List<MergeChildInfo> result = studentService.getMergeChildInfo(Arrays.asList("c001", "c002"));
        assertEquals(2, result.size());
        assertEquals("c001", result.get(0).getChildId());
        assertEquals("c 001", result.get(0).getChildName());
        assertEquals("http://test.com/upoload/a001.jpg", result.get(0).getChildAvatarUrl());
        assertEquals("center001", result.get(0).getCenterId());
        assertEquals("Center 001", result.get(0).getCenterName());
        assertEquals("g001", result.get(0).getGroupId());
        assertEquals("G 001", result.get(0).getGroupName());
        assertEquals(10, result.get(0).getNoteCount());

        assertEquals("c002", result.get(1).getChildId());
        assertEquals("c 002", result.get(1).getChildName());
        assertEquals("center001", result.get(1).getCenterId());
        assertEquals("Center 001", result.get(1).getCenterName());
        assertEquals("g001", result.get(1).getGroupId());
        assertEquals("G 001", result.get(1).getGroupName());
        assertEquals(0, result.get(1).getNoteCount());
    }

    /**
     * 根据学生的名字获取需要合并的学生
     * zjj 2016.8.31
     */
    @Test
    public void testGetMergeChildInfoByName() {
        String childName = "cName001";
        //所有名字为cName001的学生
        List<EnrollmentEntity> enrollmentEntityList = new ArrayList<>();
        EnrollmentEntity child1 = new EnrollmentEntity();
        child1.setId("c001");
        child1.setDisplayName(childName);
        MediaEntity media = new MediaEntity();
        media.setId("media001");
        media.setRelativePath("001.jpg");
        child1.setAvatarMedia(media);
        Mockito.when(studentDao.getNoteCount("c001")).thenReturn(5);
        enrollmentEntityList.add(child1);
        EnrollmentEntity child2 = new EnrollmentEntity();
        child2.setId("c002");
        child2.setDisplayName(childName);
        Mockito.when(studentDao.getNoteCount("c002")).thenReturn(8);
        enrollmentEntityList.add(child2);
        Mockito.when(studentDao.getChildsByName(childName)).thenReturn(enrollmentEntityList);
        List<MergeChildInfo> mergeChildInfoList = studentService.getMergeChildInfoByName(childName);
        Assert.assertEquals(2, mergeChildInfoList.size());
        Assert.assertEquals("c001", mergeChildInfoList.get(0).getChildId());
        Assert.assertEquals("c002", mergeChildInfoList.get(1).getChildId());
        Assert.assertEquals(5, mergeChildInfoList.get(0).getNoteCount());
        Assert.assertEquals(8, mergeChildInfoList.get(1).getNoteCount());
    }

    /**
     * 测试inactive小孩
     */
    @Test(expected = BusinessException.class)
    public void testInactiveChildIdNull() {
        studentService.inActiveChild("", "");
    }

    @Test(expected = BusinessException.class)
    public void testInactiveChildNotFoundChild() {
//        when(studentDao.getEnrollment(Mockito.anyString())).thenReturn(null);
        studentService.inActiveChild("", "");
    }

    @Test(expected = BusinessException.class)
    public void testInactiveChildArchiveError() {
        EnrollmentModel enrollment = new EnrollmentModel();
        enrollment.setArchivedGroupId("asdf");
        enrollment.setArchiveAtUtc(TimeUtil.getUtcNowStr());
//        when(studentDao.getEnrollment(Mockito.anyString())).thenReturn(enrollment);
        studentService.inActiveChild("asdf", "asdf");
    }

    @Test(expected = BusinessException.class)
    public void testInactiveChildGroupNotFound() {
//        EnrollmentModel enrollment = new EnrollmentModel();
//        when(studentDao.getEnrollment(anyString())).thenReturn(enrollment);
//        when(groupDao.getGroup(anyString())).thenReturn(null);
        studentService.inActiveChild("asdf", "asdf");
    }

    @Ignore
    @Test
    public void testInactiveChildExistInactiveClass() throws Exception {
        EnrollmentModel enrollmentModel = new EnrollmentModel();
        when(studentDao.getEnrollment(anyString())).thenReturn(enrollmentModel);

        com.learninggenie.common.data.model.GroupEntity groupEntity = new com.learninggenie.common.data.model.GroupEntity();
        com.learninggenie.common.data.model.CenterEntity centerEntity = new com.learninggenie.common.data.model.CenterEntity();
        groupEntity.setCenter(centerEntity);
        when(groupDao.getGroupWithCenter(anyString())).thenReturn(groupEntity);

        com.learninggenie.common.data.model.GroupEntity inactiveGroup = new com.learninggenie.common.data.model.GroupEntity();
        inactiveGroup.setId("qwe");
        when(groupDao.getInactiveGroup(anyString())).thenReturn(inactiveGroup);

        String childId = "child001";
        String fromGroupId = "g001";
        String toGroupId = "g002";
        EnrollmentEntity enrollment = new EnrollmentEntity();
        enrollment.setId(childId);
        enrollment.setDisplayName("child001");
        when(enrollmentRepository.findById(anyString()).orElse(null)).thenReturn(enrollment);
        //学生当前所在的班级
        GroupEntity fromGroup = new GroupEntity();
        fromGroup.setId(fromGroupId);
        fromGroup.setName("g001");
        enrollment.setGroup(fromGroup);
        //学生要转到的班级
        GroupEntity toGroup = new GroupEntity();
        toGroup.setId(toGroupId);
        toGroup.setName("g002");
        toGroup.setInactive(true);
        when(groupRepository.findById(anyString()).orElse(null)).thenReturn(toGroup);
        UserEntity user = new UserEntity();
        user.setId("u001");
        user.setRole(UserRole.AGENCY_ADMIN.toString());
        when(userProvider.checkUser(anyString())).thenReturn(user);
        when(studentDao.updateVersion(anyString(), any(Date.class))).thenReturn(1);

        studentService.inActiveChild("asdf", "asdf");

        verify(groupDao, times(0)).insertGroup(any(com.learninggenie.common.data.model.GroupEntity.class), anyString());
        verify(studentDao, times(1)).setSourceGroup(anyString(), anyString());
        verify(studentDao, times(1)).getChildNotes(anyString());
        verify(studentDao, times(1)).getChildNoteMetaData(anyString());
        verify(studentDao, times(1)).getNoteTag(anyString());
        verify(studentDao, times(1)).getNoteDomain(anyString());
        verify(studentDao, times(1)).getNoteMedia(anyString());
        verify(studentDao, times(1)).insertChildNote(anyList());
        verify(studentDao, times(1)).deleteRelationChildNote(anyString());
    }

    @Ignore
    @Test
    public void testInactiveChildNoExistInactiveClass() throws Exception {
        EnrollmentModel enrollmentModel = new EnrollmentModel();
        when(studentDao.getEnrollment(anyString())).thenReturn(enrollmentModel);

        com.learninggenie.common.data.model.GroupEntity groupEntity = new com.learninggenie.common.data.model.GroupEntity();
        com.learninggenie.common.data.model.CenterEntity centerEntity = new com.learninggenie.common.data.model.CenterEntity();
        groupEntity.setCenter(centerEntity);
        when(groupDao.getGroupWithCenter(anyString())).thenReturn(groupEntity);

        when(groupDao.getInactiveGroup(anyString())).thenReturn(null);

        String childId = "child001";
        String fromGroupId = "g001";
        String toGroupId = "g002";
        EnrollmentEntity enrollment = new EnrollmentEntity();
        enrollment.setId(childId);
        enrollment.setDisplayName("child001");
        when(enrollmentRepository.findById(anyString())).thenReturn(Optional.of(enrollment));
        //学生当前所在的班级
        GroupEntity fromGroup = new GroupEntity();
        fromGroup.setId(fromGroupId);
        fromGroup.setName("g001");
        enrollment.setGroup(fromGroup);
        //学生要转到的班级
        GroupEntity toGroup = new GroupEntity();
        toGroup.setId(toGroupId);
        toGroup.setName("g002");
        toGroup.setInactive(true);
        when(groupRepository.findById(anyString())).thenReturn(Optional.of(toGroup));
        UserEntity user = new UserEntity();
        user.setId("u001");
        user.setRole(UserRole.AGENCY_ADMIN.toString());
        when(userProvider.checkUser(anyString())).thenReturn(user);
        when(studentDao.updateVersion(anyString(), any(Date.class))).thenReturn(1);

        studentService.inActiveChild("asdf", "asdf");

        verify(groupDao, times(1)).insertGroup(any(com.learninggenie.common.data.model.GroupEntity.class), anyString());
        verify(studentDao, times(1)).setSourceGroup(anyString(), anyString());
        verify(studentDao, times(1)).getChildNotes(anyString());
        verify(studentDao, times(1)).getChildNoteMetaData(anyString());
        verify(studentDao, times(1)).getNoteTag(anyString());
        verify(studentDao, times(1)).getNoteDomain(anyString());
        verify(studentDao, times(1)).getNoteMedia(anyString());
        verify(studentDao, times(1)).insertChildNote(anyList());
        verify(studentDao, times(1)).deleteRelationChildNote(anyString());
    }

    @Ignore
    @Test
    public void testInactiveChildAlreadyInactive() throws Exception {
        EnrollmentModel enrollmentModel = new EnrollmentModel();
        enrollmentModel.setGroupId("qwe");
        when(studentDao.getEnrollment(anyString())).thenReturn(enrollmentModel);

        com.learninggenie.common.data.model.GroupEntity groupEntity = new com.learninggenie.common.data.model.GroupEntity();
        com.learninggenie.common.data.model.CenterEntity centerEntity = new com.learninggenie.common.data.model.CenterEntity();
        groupEntity.setCenter(centerEntity);
        when(groupDao.getGroupWithCenter(anyString())).thenReturn(groupEntity);

        com.learninggenie.common.data.model.GroupEntity inactiveGroup = new com.learninggenie.common.data.model.GroupEntity();
        inactiveGroup.setId("qwe");
        when(groupDao.getInactiveGroup(anyString())).thenReturn(inactiveGroup);

        AgencyModel agency = new AgencyModel();
        agency.setId("A123");
        when(userProvider.getAgencyByUserId(Mockito.anyString())).thenReturn(agency);

        UserEntity user = new UserEntity();
        user.setId("U123");
        user.setRole("AGENCY_OWNER");
        when(userProvider.checkUser(Mockito.anyString())).thenReturn(user);

        studentService.inActiveChild("asdf", "asdf");

        verify(groupDao, times(0)).insertGroup(any(com.learninggenie.common.data.model.GroupEntity.class), anyString());
        verify(studentDao, times(0)).setSourceGroup(anyString(), anyString());
    }

    /**
     *
     * 编辑孩子
     * case : 如果孩子名字存在, 但是是孩子本身, 正常编辑
     */
    @Ignore
    @Test
    public void testEditChildName_SameNameAsChildSelf() {
        String groupId = "G123";
        String agencyId = "A123";
        String userId = "U123";
        String childId = "E123";

        UserEntity user = new UserEntity();
        user.setRole("AGENCY_OWNER");
        user.setId(userId);
        when(userProvider.checkUser(userId)).thenReturn(user);

        UpdateStudentRequest studentRequest = new UpdateStudentRequest();
        studentRequest.setFirstName("Child");
        studentRequest.setMiddleName("");
        studentRequest.setLastName("Name");
        studentRequest.setBirthDate("06/19/2017");
        studentRequest.setGroupId(groupId);

        AgencyModel agency = new AgencyModel();
        agency.setId(agencyId);

        EnrollmentEntity enrollmentEntity = new EnrollmentEntity();
        enrollmentEntity.setId(childId);
        GroupEntity group = new GroupEntity();
        group.setId(groupId);
        group.setName("G123");
        group.getEnrollments().add(enrollmentEntity);
        CenterEntity center = new CenterEntity();
        center.setName("C123");
        center.getGroups().add(group);
        group.setCenter(center);
        enrollmentEntity.setGroup(group);

        when(groupProvider.checkInactive(groupId)).thenReturn(new GroupEntity());
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        when(studentDao.getChildIdByGroupIdAndName(anyString(), anyString(), anyString(), anyBoolean())).thenReturn(childId);
        when(enrollmentRepository.findById(childId).orElse(null)).thenReturn(enrollmentEntity);

        studentService.updateChild(childId, studentRequest, userId);
        verify(enrollmentRepository, times(1)).findById(anyString());
    }

    /**
     * 测试设置 ELD 属性值
     * case: 小孩属于导入的情况，没有选择语言，却是 ELD 小孩
     * @throws Exception
     */
    @Test
    public void testSetELDAttrForImport() throws Exception {
        // 反射获取 StudentServiceImpl
        Class<?> clazz = Class.forName("com.learninggenie.api.service.impl.StudentServiceImpl");
        // 获取 setELDAttr 方法
        Method setELDAttr = clazz.getDeclaredMethod("setELDAttr", List.class);
        // 设置可访问私有方法
        setELDAttr.setAccessible(true);
        // 设置参数
        List<StudentsAttrRequest> attrs = new ArrayList<>();
        StudentsAttrRequest languageAttr = new StudentsAttrRequest();
        languageAttr.setName("language");
        StudentsAttrRequest eldAttr = new StudentsAttrRequest();
        eldAttr.setName("ELD");
        List<StudentsAttrValuesRequest> values = new ArrayList<>();
        StudentsAttrValuesRequest eldValue = new StudentsAttrValuesRequest();
        eldValue.setName("Yes");
        values.add(eldValue);
        eldAttr.setValues(values);
        attrs.add(languageAttr);
        attrs.add(eldAttr);

        // 反射调用方法
        Object invoke = setELDAttr.invoke(clazz.newInstance(), attrs);
        List<StudentsAttrRequest> attrRequests = (List<StudentsAttrRequest>) invoke;

        // 断言
        assertEquals(attrRequests.size(), 1);
        assertEquals(attrRequests.get(0).getName(), "language");
    }

    /**
     * 测试设置 ELD 属性值
     * case: 小孩属于正常手动添加的情况，未选择语言，ELD 的属性值应为 No
     */
    @Test
    public void testSetELDAttrNormalLogicOneLanguage() throws Exception {
        // 反射获取 StudentServiceImpl
        Class<?> clazz = Class.forName("com.learninggenie.api.service.impl.StudentServiceImpl");
        // 获取 setELDAttr 方法
        Method setELDAttr = clazz.getDeclaredMethod("setELDAttr", List.class);
        // 设置可访问私有方法
        setELDAttr.setAccessible(true);
        // 设置参数
        List<StudentsAttrRequest> attrs = new ArrayList<>();
        StudentsAttrRequest languageAttr = new StudentsAttrRequest();
        languageAttr.setName("language");
        List<StudentsAttrValuesRequest> languageValues = new ArrayList<>();
        StudentsAttrValuesRequest languageValue = new StudentsAttrValuesRequest();
        languageValue.setName("English");
        languageValues.add(languageValue);
        languageAttr.setValues(languageValues);
        StudentsAttrRequest eldAttr = new StudentsAttrRequest();
        eldAttr.setName("ELD");
        List<StudentsAttrValuesRequest> values = new ArrayList<>();
        StudentsAttrValuesRequest eldValue = new StudentsAttrValuesRequest();
        eldValue.setName("No");
        values.add(eldValue);
        eldAttr.setValues(values);
        attrs.add(languageAttr);
        attrs.add(eldAttr);

        // 反射调用方法
        Object invoke = setELDAttr.invoke(clazz.newInstance(), attrs);
        List<StudentsAttrRequest> attrRequests = (List<StudentsAttrRequest>) invoke;

        // 断言
        assertEquals(attrRequests.size(), 2);
        assertEquals(attrRequests.get(0).getName(), "language");
        assertEquals(attrRequests.get(0).getValues().get(0).getName(), "English");
        assertEquals(attrRequests.get(1).getName(), "ELD");
        assertEquals(attrRequests.get(1).getValues().get(0).getName(), "No");
    }

    /**
     * 测试设置 ELD 属性值
     * case: 小孩属于正常手动添加的情况，选择多种语言，ELD 的属性值应为 Yes
     */
    @Test
    public void testSetELDAttrNormalLogicMoreLanguage() throws Exception {
        // 反射获取 StudentServiceImpl
        Class<?> clazz = Class.forName("com.learninggenie.api.service.impl.StudentServiceImpl");
        // 获取 setELDAttr 方法
        Method setELDAttr = clazz.getDeclaredMethod("setELDAttr", List.class);
        // 设置可访问私有方法
        setELDAttr.setAccessible(true);
        // 设置参数
        List<StudentsAttrRequest> attrs = new ArrayList<>();
        StudentsAttrRequest languageAttr = new StudentsAttrRequest();
        languageAttr.setName("language");
        List<StudentsAttrValuesRequest> languageValues = new ArrayList<>();
        StudentsAttrValuesRequest englishValue = new StudentsAttrValuesRequest();
        englishValue.setName("English");
        languageValues.add(englishValue);
        StudentsAttrValuesRequest JapaneseValue = new StudentsAttrValuesRequest();
        JapaneseValue.setName("Japanese");
        languageValues.add(JapaneseValue);
        languageAttr.setValues(languageValues);
        StudentsAttrRequest eldAttr = new StudentsAttrRequest();
        eldAttr.setName("ELD");
        List<StudentsAttrValuesRequest> values = new ArrayList<>();
        StudentsAttrValuesRequest eldValue = new StudentsAttrValuesRequest();
        eldValue.setName("Yes");
        values.add(eldValue);
        eldAttr.setValues(values);
        attrs.add(languageAttr);
        attrs.add(eldAttr);

        // 反射调用方法
        Object invoke = setELDAttr.invoke(clazz.newInstance(), attrs);
        List<StudentsAttrRequest> attrRequests = (List<StudentsAttrRequest>) invoke;

        // 断言
        assertEquals(attrRequests.size(), 2);
        assertEquals(attrRequests.get(0).getValues().size(), 2);
        assertEquals(attrRequests.get(0).getName(), "language");
        assertEquals(attrRequests.get(0).getValues().get(0).getName(), "English");
        assertEquals(attrRequests.get(0).getValues().get(1).getName(), "Japanese");
        assertEquals(attrRequests.get(1).getName(), "ELD");
        assertEquals(attrRequests.get(1).getValues().get(0).getName(), "Yes");
    }

    @Ignore
    @Test
    public void testGenerateChildren_detailExcel() {
        String userId = "U123";
        String agencyId = "A123";

        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setRole("AGENCY_OWNER");
        Map<String, String> attrMap = new HashMap<>();
        attrMap.put("1", "First Name");
        attrMap.put("2", "Last Name");
        attrMap.put("3", "Gender");
        attrMap.put("4", "Class Name");
        attrMap.put("5", "Center Name");
        attrMap.put("6", "Active Period");
        attrMap.put("7", "Active Alias");
        attrMap.put("8", "Framework Name");
        List<EnrollmentAttrEntity> allAttrs = new ArrayList<>();
        for (int i = 1; i < 9; i++) {
            EnrollmentAttrEntity attr = new EnrollmentAttrEntity();
            attr.setTypeName(attrMap.get(i + ""));
            attr.setName(attrMap.get(i + ""));
            attr.setTypeValue("Sena");
            allAttrs.add(attr);
        }
        List<EnrollmentModel> enrollmentModelList = new ArrayList<>();
        EnrollmentModel enrollment = new EnrollmentModel();
        enrollment.setId("E123");
        enrollment.setEnrollmentDate("2016-06-07");
        enrollment.setGroupId("G123");
        enrollmentModelList.add(enrollment);

        String key = "[{\"attrs\":[{\"name\":\"Statewide Student Identifier\",\"drdpName\":\"ssid\",\"valueList\":[],\"description\":\"Statewide Student Identifier (10-digit SSID)\",\"\n" +
                "order\":\"0\",\"typeValue\":\"TEXT_FIELD\",\"required\":false,\"max\":10,\"regex\":\"^[0-9]{10}$\",\"errMsg\":\"ssid must be 10 numeric digits.\"},{\"name\":\"External ID\",\"drdpName\":\"icode\",\"valueList\":[],\"description\":\"Agency/District Student Identifier (Agency/district or CASEMIS ID). The Agency/District Student Identifier can be the same as the Statewide Student Identifier.\",\"\n" +
                "order\":\"3\",\"refer\":\"Statewide Student Identifier\",\"typeValue\":\"TEXT_FIELD\",\"required\":true,\"max\":50},{\"name\":\"Hispanic\",\"drdpName\":\"hispanic\",\"valueList\":[{\"name\":\"Yes\",\"value\":\"1\"},{\"name\":\"No\",\"value\":\"\"}],\"description\":\"What is this child's ethnicity? Check one.\",\"\n" +
                "order\":\"51\",\"typeValue\":\"SINGLE_CHOICE\",\"required\":false},{\"name\":\"Race\",\"valueList\":[{\"name\":\"American Indian or Alaska Native\",\"value\":\"1\",\"asAttr\":\"Nativeamerican\",\"drdpName\":\"nativeamerican\"},{\"name\":\"Asian Indian\",\"value\":\"1\",\"asAttr\":\"Asian Indian\",\"drdpName\":\"asianindian\"},{\"name\":\"Black or African American\",\"value\":\"1\",\"asAttr\":\"Africanamerican\",\"drdpName\":\"africanamerican\"},{\"name\":\"Cambodian\",\"value\":\"1\",\"asAttr\":\"Cambodian\",\"drdpName\":\"cambodian\"},{\"name\":\"Chinese\",\"value\":\"1\",\"asAttr\":\"Chinese\",\"drdpName\":\"chinese\"},{\"name\":\"Filipino\",\"value\":\"1\",\"asAttr\":\"Filipino\",\"drdpName\":\"filipino\"},{\"name\":\"Guamanian\",\"value\":\"1\",\"asAttr\":\"Guamanian\",\"drdpName\":\"guamanian\"},{\"name\":\"Hawaiian\",\"value\":\"1\",\"asAttr\":\"Hawaiian\",\"drdpName\":\"hawaiian\"},{\"name\":\"Hmong\",\"value\":\"1\",\"asAttr\":\"Hmong\",\"drdpName\":\"hmong\"},{\"name\":\"Japanese\",\"value\":\"1\",\"asAttr\":\"Japanese\",\"drdpName\":\"japanese\"},{\"name\":\"Korean\",\"value\":\"1\",\"asAttr\":\"Korean\",\"drdpName\":\"korean\"},{\"name\":\"Laotian\",\"value\":\"1\",\"asAttr\":\"Laotian\",\"drdpName\":\"laotian\"},{\"name\":\"Other Asian\",\"value\":\"1\",\"asAttr\":\"Other Asian\",\"drdpName\":\"otherasian\"},{\"name\":\"Other Pacific Islander\",\"value\":\"1\",\"asAttr\":\"Pacificislander\",\"drdpName\":\"pacificislander\"},{\"name\":\"Samoan\",\"value\":\"1\",\"asAttr\":\"Samoan\",\"drdpName\":\"samoan\"},{\"name\":\"Tahitian\",\"value\":\"1\",\"asAttr\":\"Tahitian\",\"drdpName\":\"tahitian\"},{\"name\":\"Vietnamese\",\"value\":\"1\",\"asAttr\":\"Vietnamese\",\"drdpName\":\"vietnamese\"},{\"name\":\"White\",\"value\":\"1\",\"asAttr\":\"White\",\"drdpName\":\"white\"},{\"name\":\"Intentionally Left Blank\",\"value\":\"1\",\"asAttr\":\"Ethnicityleftblank\",\"drdpName\":\"ethnicityleftblank\"}],\"description\":\"What is this child's race? Mark one or more races to indicate what this person considers himself/herself to be. (Check up to three)\",\"\n" +
                "order\":\"54\",\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":true,\"max\":3},{\"name\":\"Primary\",\"drdpName\":\"primary\",\"valueList\":[{\"name\":\"Yes\",\"value\":\"1\"},{\"name\":\"No\",\"value\":\"0\",\"textbox\":{\"desc\":\"Please describe your relationship\",\"show\":true,\"asAttr\":\"Primarydescribe\",\"drdpName\":\"primarydescribe\",\"max\":100,\"required\":true}}],\"description\":\"Are you the primary teacher working with this child?\",\"\n" +
                "order\":\"57\",\"typeValue\":\"SINGLE_CHOICE\",\"required\":true},{\"name\":\"Generalassistance\",\"drdpName\":\"generalassistance\",\"valueList\":[{\"name\":\"Yes\",\"value\":\"1\",\"textbox\":{\"desc\":\"Please describe their role/relation\",\"show\":true,\"asAttr\":\"Assistancedescribe\",\"drdpName\":\"assistancedescribe\",\"max\":100,\"required\":true}},{\"name\":\"No\",\"value\":\"0\"}],\"description\":\"Did another adult assist you with assessing this child?\",\"\n" +
                "order\":\"60\",\"typeValue\":\"SINGLE_CHOICE\",\"required\":true},{\"name\":\"Language\",\"valueList\":[{\"no\":\"0\",\"name\":\"English\",\"value\":\"1\",\"asAttr\":\"Homelanguageenglish\",\"drdpName\":\"homelanguageenglish\"},{\"no\":\"1\",\"name\":\"Spanish\",\"value\":\"1\",\"asAttr\":\"Homelanguagespanish\",\"drdpName\":\"homelanguagespanish\"},{\"no\":\"2\",\"name\":\"Vietnamese\",\"value\":\"02\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"3\",\"name\":\"Cantonese\",\"value\":\"03\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"4\",\"name\":\"Korean\",\"value\":\"04\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"5\",\"name\":\"Filipino (Pilipino or Tagalog)\",\"value\":\"05\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"6\",\"name\":\"Portuguese\",\"value\":\"06\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"7\",\"name\":\"Mandarin (Putonghua)\",\"value\":\"07\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"10\",\"name\":\"Japanese\",\"value\":\"08\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"11\",\"name\":\"Khmer (Cambodian)\",\"value\":\"09\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"12\",\"name\":\"Lao\",\"value\":\"10\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"13\",\"name\":\"Arabic\",\"value\":\"11\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"14\",\"name\":\"Armenian\",\"value\":\"12\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"15\",\"name\":\"Burmese\",\"value\":\"13\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"16\",\"name\":\"Dutch\",\"value\":\"15\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"17\",\"name\":\"Farsi\",\"value\":\"16\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"18\",\"name\":\"French\",\"value\":\"17\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"19\",\"name\":\"German\",\"value\":\"18\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"20\",\"name\":\"Greek\",\"value\":\"19\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"21\",\"name\":\"Chamorro (Guamanian)\",\"value\":\"20\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"22\",\"name\":\"Hebrew\",\"value\":\"21\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"23\",\"name\":\"Hindi\",\"value\":\"22\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"24\",\"name\":\"Hmong\",\"value\":\"23\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"25\",\"name\":\"Hungarian\",\"value\":\"24\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"26\",\"name\":\"Ilocano\",\"value\":\"25\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"27\",\"name\":\"Indonesian\",\"value\":\"26\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"28\",\"name\":\"Italian\",\"value\":\"27\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"29\",\"name\":\"Punjabi\",\"value\":\"28\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"30\",\"name\":\"Russian\",\"value\":\"29\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"31\",\"name\":\"Samoan\",\"value\":\"30\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"32\",\"name\":\"Thai\",\"value\":\"32\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"33\",\"name\":\"Turkish\",\"value\":\"33\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"34\",\"name\":\"Tongan\",\"value\":\"34\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"35\",\"name\":\"Urdu\",\"value\":\"35\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"36\",\"name\":\"Cebuano (Visayan)\",\"value\":\"36\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"37\",\"name\":\"Sign Language\",\"value\":\"37\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"38\",\"name\":\"Ukranian\",\"value\":\"38\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"39\",\"name\":\"Chaozhou (Chiuchow)\",\"value\":\"39\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"40\",\"name\":\"Pashto\",\"value\":\"40\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"41\",\"name\":\"Polish\",\"value\":\"41\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"42\",\"name\":\"Assyrian\",\"value\":\"42\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"43\",\"name\":\"Gujarati\",\"value\":\"43\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"44\",\"name\":\"Mien (Yao)\",\"value\":\"44\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"45\",\"name\":\"Rumanian\",\"value\":\"45\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"46\",\"name\":\"Taiwanese\",\"value\":\"46\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"47\",\"name\":\"Lahu\",\"value\":\"47\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"48\",\"name\":\"Marhallese\",\"value\":\"48\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"49\",\"name\":\"Mixteco\",\"value\":\"49\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"50\",\"name\":\"Khmu\",\"value\":\"50\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"51\",\"name\":\"Kurdish (Kurdi, Kurmanji)\",\"value\":\"51\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"52\",\"name\":\"Serbo-Croatian (Bosnian, Croatian, Serbian)\",\"value\":\"52\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"53\",\"name\":\"Toishanese\",\"value\":\"53\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"54\",\"name\":\"Chaldean\",\"value\":\"54\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"55\",\"name\":\"Albanian\",\"value\":\"56\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"56\",\"name\":\"Tigrinya\",\"value\":\"57\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"57\",\"name\":\"Somali\",\"value\":\"60\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"58\",\"name\":\"Bengali\",\"value\":\"61\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"59\",\"name\":\"Telugu\",\"value\":\"62\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"60\",\"name\":\"Tamil\",\"value\":\"63\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"61\",\"name\":\"Marathi\",\"value\":\"64\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"62\",\"name\":\"Kannada\",\"value\":\"65\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"},{\"no\":\"99\",\"name\":\"Other non-English\",\"value\":\"99\",\"asAttr\":\"Otherhomelanguage\",\"drdpName\":\"homelanguageother\"}],\"description\":\"Child's home language(s)?(Check all that apply)\",\"\n" +
                "order\":\"63\",\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":true},{\"name\":\"ELD\",\"drdpName\":\"otherthanenglish\",\"valueList\":[{\"name\":\"Yes\",\"value\":\"1\",\"isDigit\":true},{\"name\":\"No\",\"value\":\"\"}],\"description\":\"Is a language other than English spoken in the child's home? If yes, a language other than English is spoken in the child's home, the ELD measures must be completed.\",\"\n" +
                "order\":\"64\",\"typeValue\":\"SINGLE_CHOICE\",\"required\":true},{\"name\":\"Bilingual\",\"drdpName\":\"\",\"valueList\":[{\"name\":\"Yes\",\"value\":\"1\",\"isDigit\":true},{\"name\":\"No\",\"value\":\"\"}],\"description\":\"Is this a bilingual kindergarten class where the curriculum provides opportunities for learning and development of Spanish?\",\"\n" +
                "order\":\"65\",\"typeValue\":\"SINGLE_CHOICE\",\"required\":false},{\"name\":\"Speak Language\",\"valueList\":[{\"no\":\"0\",\"name\":\"English\",\"value\":\"1\",\"asAttr\":\"Speaklanguageenglish\",\"drdpName\":\"speaklanguageenglish\"},{\"no\":\"1\",\"name\":\"Spanish\",\"value\":\"1\",\"asAttr\":\"Speaklanguagespanish\",\"drdpName\":\"speaklanguagespanish\"},{\"no\":\"2\",\"name\":\"Vietnamese\",\"value\":\"02\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"3\",\"name\":\"Cantonese\",\"value\":\"03\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"4\",\"name\":\"Korean\",\"value\":\"04\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"5\",\"name\":\"Filipino (Pilipino or Tagalog)\",\"value\":\"05\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"6\",\"name\":\"Portuguese\",\"value\":\"06\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"7\",\"name\":\"Mandarin (Putonghua)\",\"value\":\"07\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"10\",\"name\":\"Japanese\",\"value\":\"08\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"11\",\"name\":\"Khmer (Cambodian)\",\"value\":\"09\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"12\",\"name\":\"Lao\",\"value\":\"10\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"13\",\"name\":\"Arabic\",\"value\":\"11\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"14\",\"name\":\"Armenian\",\"value\":\"12\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"15\",\"name\":\"Burmese\",\"value\":\"13\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"16\",\"name\":\"Dutch\",\"value\":\"15\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"17\",\"name\":\"Farsi\",\"value\":\"16\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"18\",\"name\":\"French\",\"value\":\"17\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"19\",\"name\":\"German\",\"value\":\"18\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"20\",\"name\":\"Greek\",\"value\":\"19\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"21\",\"name\":\"Chamorro (Guamanian)\",\"value\":\"20\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"22\",\"name\":\"Hebrew\",\"value\":\"21\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"23\",\"name\":\"Hindi\",\"value\":\"22\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"24\",\"name\":\"Hmong\",\"value\":\"23\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"25\",\"name\":\"Hungarian\",\"value\":\"24\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"26\",\"name\":\"Ilocano\",\"value\":\"25\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"27\",\"name\":\"Indonesian\",\"value\":\"26\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"28\",\"name\":\"Italian\",\"value\":\"27\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"29\",\"name\":\"Punjabi\",\"value\":\"28\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"30\",\"name\":\"Russian\",\"value\":\"29\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"31\",\"name\":\"Samoan\",\"value\":\"30\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"32\",\"name\":\"Thai\",\"value\":\"32\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"33\",\"name\":\"Turkish\",\"value\":\"33\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"34\",\"name\":\"Tongan\",\"value\":\"34\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"35\",\"name\":\"Urdu\",\"value\":\"35\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"36\",\"name\":\"Cebuano (Visayan)\",\"value\":\"36\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"37\",\"name\":\"Sign Language\",\"value\":\"37\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"38\",\"name\":\"Ukranian\",\"value\":\"38\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"39\",\"name\":\"Chaozhou (Chiuchow)\",\"value\":\"39\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"40\",\"name\":\"Pashto\",\"value\":\"40\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"41\",\"name\":\"Polish\",\"value\":\"41\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"42\",\"name\":\"Assyrian\",\"value\":\"42\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"43\",\"name\":\"Gujarati\",\"value\":\"43\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"44\",\"name\":\"Mien (Yao)\",\"value\":\"44\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"45\",\"name\":\"Rumanian\",\"value\":\"45\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"46\",\"name\":\"Taiwanese\",\"value\":\"46\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"47\",\"name\":\"Lahu\",\"value\":\"47\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"48\",\"name\":\"Marhallese\",\"value\":\"48\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"49\",\"name\":\"Mixteco\",\"value\":\"49\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"50\",\"name\":\"Khmu\",\"value\":\"50\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"51\",\"name\":\"Kurdish (Kurdi, Kurmanji)\",\"value\":\"51\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"52\",\"name\":\"Serbo-Croatian (Bosnian, Croatian, Serbian)\",\"value\":\"52\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"53\",\"name\":\"Toishanese\",\"value\":\"53\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"54\",\"name\":\"Chaldean\",\"value\":\"54\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"55\",\"name\":\"Albanian\",\"value\":\"56\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"56\",\"name\":\"Tigrinya\",\"value\":\"57\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"57\",\"name\":\"Somali\",\"value\":\"60\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"58\",\"name\":\"Bengali\",\"value\":\"61\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"59\",\"name\":\"Telugu\",\"value\":\"62\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"60\",\"name\":\"Tamil\",\"value\":\"63\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"61\",\"name\":\"Marathi\",\"value\":\"64\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"62\",\"name\":\"Kannada\",\"value\":\"65\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"},{\"no\":\"99\",\"name\":\"Other non-English\",\"value\":\"99\",\"asAttr\":\"Otherlanguagespeak\",\"drdpName\":\"speaklanguageother\"}],\"description\":\"What language(s) do you speak with this child? (Check all that apply)\",\"\n" +
                "order\":\"66\",\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":true},{\"name\":\"Class Language\",\"valueList\":[{\"no\":\"0\",\"name\":\"English\",\"value\":\"1\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageenglish\"},{\"no\":\"1\",\"name\":\"Spanish\",\"value\":\"1\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguagespanish\"},{\"no\":\"2\",\"name\":\"Vietnamese\",\"value\":\"02\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"3\",\"name\":\"Cantonese\",\"value\":\"03\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"4\",\"name\":\"Korean\",\"value\":\"04\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"5\",\"name\":\"Filipino (Pilipino or Tagalog)\",\"value\":\"05\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"6\",\"name\":\"Portuguese\",\"value\":\"06\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"7\",\"name\":\"Mandarin (Putonghua)\",\"value\":\"07\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"10\",\"name\":\"Japanese\",\"value\":\"08\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"11\",\"name\":\"Khmer (Cambodian)\",\"value\":\"09\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"12\",\"name\":\"Lao\",\"value\":\"10\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"13\",\"name\":\"Arabic\",\"value\":\"11\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"14\",\"name\":\"Armenian\",\"value\":\"12\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"15\",\"name\":\"Burmese\",\"value\":\"13\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"16\",\"name\":\"Dutch\",\"value\":\"15\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"17\",\"name\":\"Farsi\",\"value\":\"16\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"18\",\"name\":\"French\",\"value\":\"17\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"19\",\"name\":\"German\",\"value\":\"18\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"20\",\"name\":\"Greek\",\"value\":\"19\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"21\",\"name\":\"Chamorro (Guamanian)\",\"value\":\"20\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"22\",\"name\":\"Hebrew\",\"value\":\"21\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"23\",\"name\":\"Hindi\",\"value\":\"22\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"24\",\"name\":\"Hmong\",\"value\":\"23\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"25\",\"name\":\"Hungarian\",\"value\":\"24\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"26\",\"name\":\"Ilocano\",\"value\":\"25\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"27\",\"name\":\"Indonesian\",\"value\":\"26\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"28\",\"name\":\"Italian\",\"value\":\"27\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"29\",\"name\":\"Punjabi\",\"value\":\"28\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"30\",\"name\":\"Russian\",\"value\":\"29\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"31\",\"name\":\"Samoan\",\"value\":\"30\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"32\",\"name\":\"Thai\",\"value\":\"32\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"33\",\"name\":\"Turkish\",\"value\":\"33\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"34\",\"name\":\"Tongan\",\"value\":\"34\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"35\",\"name\":\"Urdu\",\"value\":\"35\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"36\",\"name\":\"Cebuano (Visayan)\",\"value\":\"36\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"37\",\"name\":\"Sign Language\",\"value\":\"37\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"38\",\"name\":\"Ukranian\",\"value\":\"38\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"39\",\"name\":\"Chaozhou (Chiuchow)\",\"value\":\"39\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"40\",\"name\":\"Pashto\",\"value\":\"40\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"41\",\"name\":\"Polish\",\"value\":\"41\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"42\",\"name\":\"Assyrian\",\"value\":\"42\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"43\",\"name\":\"Gujarati\",\"value\":\"43\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"44\",\"name\":\"Mien (Yao)\",\"value\":\"44\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"45\",\"name\":\"Rumanian\",\"value\":\"45\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"46\",\"name\":\"Taiwanese\",\"value\":\"46\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"47\",\"name\":\"Lahu\",\"value\":\"47\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"48\",\"name\":\"Marhallese\",\"value\":\"48\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"49\",\"name\":\"Mixteco\",\"value\":\"49\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"50\",\"name\":\"Khmu\",\"value\":\"50\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"51\",\"name\":\"Kurdish (Kurdi, Kurmanji)\",\"value\":\"51\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"52\",\"name\":\"Serbo-Croatian (Bosnian, Croatian, Serbian)\",\"value\":\"52\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"53\",\"name\":\"Toishanese\",\"value\":\"53\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"54\",\"name\":\"Chaldean\",\"value\":\"54\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"55\",\"name\":\"Albanian\",\"value\":\"56\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"56\",\"name\":\"Tigrinya\",\"value\":\"57\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"57\",\"name\":\"Somali\",\"value\":\"60\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"58\",\"name\":\"Bengali\",\"value\":\"61\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"59\",\"name\":\"Telugu\",\"value\":\"62\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"60\",\"name\":\"Tamil\",\"value\":\"63\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"61\",\"name\":\"Marathi\",\"value\":\"64\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"62\",\"name\":\"Kannada\",\"value\":\"65\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"},{\"no\":\"99\",\"name\":\"Other non-English\",\"value\":\"99\",\"asAttr\":\"OtherClasslanguage\",\"drdpName\":\"classlanguageother\"}],\"description\":\"What is the language of instruction in the classroom? (Check all that apply)\",\"\n" +
                "order\":\"67\",\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":true},{\"name\":\"Assistquestion\",\"drdpName\":\"assistquestion\",\"valueList\":[{\"name\":\"Yes\",\"value\":\"1\",\"textbox\":{\"desc\":\"Please describe their role/relation\",\"show\":true,\"asAttr\":\"Assistance\",\"drdpName\":\"assistance\",\"max\":100,\"required\":true}},{\"name\":\"No\",\"value\":\"0\"},{\"name\":\"Not applicable\",\"value\":\"2\",\"desc\":\"I understand and use the child's home language\"}],\"description\":\"Did someone who understands and uses a child's home language assist you with completing the observation?\",\"\n" +
                "order\":\"69\",\"typeValue\":\"SINGLE_CHOICE\",\"required\":true},{\"name\":\"Program Name\",\"valueList\":[{\"name\":\"Full day Kindergarten/Transitional Kindergarten\",\"value\":\"1\",\"asAttr\":\"Enrol_fullday\",\"drdpName\":\"enrol_fullday\"},{\"name\":\"Half or Part day Kindergarten/Transitional Kindergarten\",\"value\":\"1\",\"asAttr\":\"Enrol_halfday\",\"drdpName\":\"enrol_halfday\"},{\"name\":\"State Infant/Toddler Program\",\"value\":\"1\",\"asAttr\":\"Enrol_stateprogram\",\"drdpName\":\"enrol_stateprogram\"},{\"name\":\"Migrant\",\"value\":\"1\",\"asAttr\":\"Migrant\",\"drdpName\":\"enrol_migrant\"},{\"name\":\"Head Start\",\"value\":\"1\",\"asAttr\":\"Enrol_headstart\",\"drdpName\":\"enrol_headstart\"},{\"name\":\"First 5\",\"value\":\"1\",\"asAttr\":\"Enrol_firstfive\",\"drdpName\":\"enrol_firstfive\"},{\"name\":\"Early Head Start\",\"value\":\"1\",\"asAttr\":\"Enrol_earlyheadstart\",\"drdpName\":\"enrol_earlyheadstart\"},{\"name\":\"Title 1\",\"value\":\"1\",\"asAttr\":\"Enrol_title1\",\"drdpName\":\"enrol_title1\"},{\"name\":\"Child Care Center\",\"value\":\"1\",\"asAttr\":\"Enrol_generalchildcare\",\"drdpName\":\"enrol_generalchildcare\"},{\"name\":\"Family Child Care Home\",\"value\":\"1\",\"asAttr\":\"Enrol_familychildcare\",\"drdpName\":\"enrol_familychildcare\"},{\"name\":\"Tribal Head Start\",\"value\":\"1\",\"asAttr\":\"Enrol_tribalheadstart\",\"drdpName\":\"enrol_tribalheadstart\"},{\"name\":\"Other\",\"value\":\"1\",\"asAttr\":\"Enrol_other\",\"drdpName\":\"enrol_other\"}],\"description\":\"Child is enrolled in (Check all that apply)\",\"\n" +
                "order\":\"72\",\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false},{\"name\":\"Enrol_subsidized\",\"drdpName\":\"enrol_subsidized\",\"valueList\":[{\"name\":\"Yes\",\"value\":\"1\"},{\"name\":\"No\",\"value\":\"0\"},{\"name\":\"Don't know\",\"value\":\"2\"}],\"description\":\"Child's tuition fees are\",\"\n" +
                "order\":\"73\",\"typeValue\":\"SINGLE_CHOICE\",\"required\":true},{\"no\":\"10\",\"name\":\"Teacher\",\"drdpName\":\"teacher\",\"description\":\"Teacher\",\"valueList\":[{\"name\":\"Other\",\"value\":\"0\",\"ignore\":true,\"textbox\":{\"desc\":\"Please fill in teacher email\",\"show\":true,\"asAttr\":\"Teacher\",\"drdpName\":\"teacher\",\"max\":100,\"required\":true,\"regex\":\"\\\\w[-\\\\w.+]*@([A-Za-z0-9][-A-Za-z0-9]+\\\\.)+[A-Za-z]{2,14}\",\"errMsg\":\"This field must be Email.\"}}],\"\n" +
                "order\":\"75\",\"typeValue\":\"SINGLE_CHOICE\",\"required\":true},{\"name\":\"IEP/IFSP\",\"drdpName\":\"ieporifsp\",\"valueList\":[{\"name\":\"Yes\",\"value\":\"1\"},{\"name\":\"No\",\"value\":\"0\"},{\"name\":\"Don't know\",\"value\":\"2\"}],\"description\":\"Does this child have an Individualized Education Program (IEP) or an Individualized Family Service Plan (IFSP)?\",\"\n" +
                "order\":\"78\",\"typeValue\":\"SINGLE_CHOICE\",\"required\":true},{\"name\":\"Notes\",\"drdpName\":\"notes\",\"valueList\":[],\"description\":\"Notes for Rating Period.\",\"\n" +
                "order\":\"81\",\"typeValue\":\"TEXT_FIELD\",\"required\":false},{\"no\":\"18\",\"name\":\"District Of Service\",\"drdpName\":\"speced_districtofservice\",\"description\":\"District Of Service\",\"valueList\":[],\"\n" +
                "order\":\"84\",\"typeValue\":\"TEXT_FIELD\",\"max\":100,\"required\":false},{\"name\":\"Speced_eligibilty\",\"drdpName\":\"speced_eligibilty\",\"valueList\":[{\"name\":\"Autism\",\"value\":\"1\"},{\"name\":\"Multiple Disability\",\"value\":\"2\"},{\"name\":\"Deaf-Blindness\",\"value\":\"3\"},{\"name\":\"Orthopedic Impairment\",\"value\":\"4\"},{\"name\":\"Deafness\",\"value\":\"5\"},{\"name\":\"Other Health Impairment\",\"value\":\"6\"},{\"name\":\"Emotional Disturbance\",\"value\":\"7\"},{\"name\":\"Specific Learning Disability\",\"value\":\"8\"},{\"name\":\"Established Medical Disability\",\"value\":\"9\"},{\"name\":\"Speech or Language Impairment\",\"value\":\"10\"},{\"name\":\"Hard of Hearing\",\"value\":\"11\"},{\"name\":\"Traumatic Brain Injury\",\"value\":\"12\"},{\"name\":\"Intellectual Disability\",\"value\":\"13\"},{\"name\":\"Visual Impairment\",\"value\":\"14\"}],\"description\":\"Special education eligibility (Check one)\",\"\n" +
                "order\":\"87\",\"typeValue\":\"SINGLE_CHOICE\",\"required\":false},{\"name\":\"Adaptations\",\"valueList\":[{\"no\":\"1\",\"name\":\"Augmentative or alternative communication system\",\"value\":\"1\",\"asAttr\":\"Adapt_augmentoraltcomm\",\"drdpName\":\"adapt_augmentoraltcomm\"},{\"no\":\"2\",\"name\":\"Alternative mode for written language\",\"value\":\"1\",\"asAttr\":\"Adapt_altwrittenlanguage\",\"drdpName\":\"adapt_altwrittenlanguage\"},{\"no\":\"3\",\"name\":\"Visual Support\",\"value\":\"1\",\"asAttr\":\"Adapt_visualsupport\",\"drdpName\":\"adapt_visualsupport\"},{\"no\":\"4\",\"name\":\"Assistive equipment or device\",\"value\":\"1\",\"asAttr\":\"Adapt_assistiveequipment\",\"drdpName\":\"adapt_assistiveequipment\"},{\"no\":\"5\",\"name\":\"Functional positioning\",\"value\":\"1\",\"asAttr\":\"Adapt_functionalposition\",\"drdpName\":\"adapt_functionalposition\"},{\"no\":\"6\",\"name\":\"Sensory Support\",\"value\":\"1\",\"asAttr\":\"Adapt_sensorysupport\",\"drdpName\":\"adapt_sensorysupport\"},{\"no\":\"7\",\"name\":\"Alternative response mode\",\"value\":\"1\",\"asAttr\":\"Adapt_altresponsemode\",\"drdpName\":\"adapt_altresponsemode\"},{\"no\":\"8\",\"name\":\"None\",\"value\":\"1\",\"asAttr\":\"Adapt_none\",\"drdpName\":\"adapt_none\"}],\"description\":\"Adaptations (Check all that apply)\",\"\n" +
                "order\":\"90\",\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false},{\"name\":\"Accommodation\",\"drdpName\":\"accommodation\",\"valueList\":[{\"name\":\"Yes\",\"value\":\"1\",\"textbox\":{\"desc\":\"describe\",\"show\":true,\"asAttr\":\"accommodationdescribe\",\"drdpName\":\"accommodationdescribe\",\"max\":4000,\"required\":true}},{\"name\":\"No\",\"value\":\"0\"},{\"name\":\"Don't know\",\"value\":\"2\"}],\"description\":\"Accommodations/modifications?\",\"\n" +
                "order\":\"93\",\"typeValue\":\"SINGLE_CHOICE\",\"required\":false}]}]";

        DomainEntity domain = new DomainEntity();
        domain.setId("A5845474-BDCE-E411-AF66-02C72B94B99B");

        AgencyModel agency = new AgencyModel();
        agency.setId(agencyId);

        when(studentDao.getChildrenByUserId(any(UserEntity.class), anyInt(), anyInt(), anyString(), anyString(), anyList())).thenReturn(enrollmentModelList);
        when(studentDao.getAttrs(agency.getId())).thenReturn(allAttrs);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        when(userProvider.checkUser(userId)).thenReturn(user);
        when(studentDao.getMetaValueByMetaKey(DRDP.DRDP_ATTR_ALL.toString())).thenReturn(key);
        when(domainDao.getDomainParentIdByGroupId(Mockito.anyString())).thenReturn("A5845474-BDCE-E411-AF66-02C72B94B99B");
        when(domainDao.getParentIdByChildId(Mockito.anyString())).thenReturn("A5845474-BDCE-E411-AF66-02C72B94B99B");
        List<StudentResponse> studentResponses = studentService.getStudentResponses(enrollmentModelList, allAttrs, agency, "zh-cn");

        Assert.assertTrue(studentResponses.size() == 1);
        Assert.assertTrue(studentResponses.get(0).getAttrs().size() == 12);
        Assert.assertTrue(studentResponses.get(0).getParentFrameworkId().equalsIgnoreCase("A5845474-BDCE-E411-AF66-02C72B94B99B"));
    }

    /**
     * 设置孩子默认的周期
     * case : 孩子所在班级不能评分
     * 结果 : 不进行创建孩子周期
     */
    @Test
    @Ignore
    public void testSetRatingPeriod_GroupNotHasScoreTemplate() {
        String childId = "E123";
        String domainId = "D123";
        String groupId = "G123";

        com.learninggenie.common.data.entity.DomainEntity domain = new com.learninggenie.common.data.entity.DomainEntity();
        domain.setId(domainId);

        EnrollmentEntity enrollment = new EnrollmentEntity();
        enrollment.setId(childId);

        GroupEntity group = new GroupEntity();
        group.setId(groupId);
        group.setDomain(domain);
        enrollment.setGroup(group);

        when(groupRepository.getByIdAndIsDeletedFalse(anyString())).thenReturn(group);
        when(studentDao.getChildWithGroupCenter(childId)).thenReturn(enrollment);
        when(groupDao.getGroupByChildId(childId)).thenReturn(group);
        when(weeklyService.hasScoreTemplate(domain.getId())).thenReturn(false);

        RatingPeriod period = studentService.setRatingPeriodV2(childId, null, false, null);
        Assert.assertEquals(period, null);
        verify(groupDao, times(0)).getActivePeriod(anyString());
    }

    /**
     * 设置孩子默认的周期
     * case : 孩子所在班级能评分, 孩子没入学日期, 班级有周期
     * 结果 : 孩子周期应为班级周期
     */
    @Test
    public void testSetRatingPeriod_GroupCanScoreAndHasAlias_ChildNoEnrollmentDate() {
        String childId = "E123";
        String domainId = "D123";
        String groupId = "G123";

        com.learninggenie.common.data.entity.DomainEntity domain = new com.learninggenie.common.data.entity.DomainEntity();
        domain.setId(domainId);

        EnrollmentEntity enrollment = new EnrollmentEntity();
        enrollment.setId(childId);

        CenterEntity center = new CenterEntity();
        center.setId("C123");
        center.setName("CN123");
        center.setCenterTimeZone("America/Los_Angeles");

        GroupEntity group = new GroupEntity();
        group.setId(groupId);
        group.setDomain(domain);
        group.setCenter(center);
        enrollment.setGroup(group);
        group.getEnrollments().add(enrollment);

        GroupPeriodEntity groupPeriod = new GroupPeriodEntity();
        groupPeriod.setId("P123");
        groupPeriod.setAlias("P123");
        groupPeriod.setFromAtLocal(TimeUtil.parseDate("06/12/2017"));
        groupPeriod.setToAtLocal(TimeUtil.parseDate("08/24/2018"));

        RatingPeriodEntity periodEntity = new RatingPeriodEntity();
        periodEntity.setId("P123");
        RatingPeriodEntity returnPeriod = new RatingPeriodEntity();
        returnPeriod.setId("R123");
        returnPeriod.setActived(true);
        returnPeriod.setEnrollmentId(childId);
        returnPeriod.setFromAtLocal(groupPeriod.getFromAtLocal());
        returnPeriod.setToAtLocal(groupPeriod.getToAtLocal());
        returnPeriod.setAlias("P123");
        returnPeriod.setCreateAtUtc(TimeUtil.parseDate("06/12/2017"));
        returnPeriod.setUpdateAtUtc(TimeUtil.parseDate("08/24/2018"));

        List<GroupPeriodEntity> periodEntities = new ArrayList<>();
        periodEntities.add(groupPeriod);
        group.setCurrentPeriod(groupPeriod);

        // 机构数据
        AgencyEntity agency = new AgencyEntity();
        agency.setId("A00001");

        // 机构元数据
        AgencyMetaDataEntity agencyMeta = new AgencyMetaDataEntity();
        agencyMeta.setMetaValue("true");

        when(agencyDao.getAgencyByChildId(anyString())).thenReturn(agency);
        when(agencyDao.getMeta(agency.getId(), AgencyMetaKey.AUTO_GENERATE_NOTE_OPEN.toString())).thenReturn(agencyMeta);
//        when(groupPeriodRepository.getByGroupId(anyString())).thenReturn(periodEntities);
        when(studentDao.getChildWithGroupCenter(childId)).thenReturn(enrollment);
        when(groupDao.getGroupByChildId(childId)).thenReturn(group);
//        when(weeklyService.hasScoreTemplate(domain.getId())).thenReturn(true);
//        when(groupDao.getActivePeriod(groupId)).thenReturn(groupPeriod);
        when(studentDao.insertNewPeriod(any(RatingPeriodEntity.class))).thenReturn(periodEntity);
        when(studentDao.getPeriodById(periodEntity.getId())).thenReturn(returnPeriod);
        when(groupRepository.getByIdAndIsDeletedFalse(anyString())).thenReturn(group);

//        com.learninggenie.common.data.model.DomainEntity framework = mock(com.learninggenie.common.data.model.DomainEntity.class);
//        ScoreTemplateEntity scoreTemplate = mock(ScoreTemplateEntity.class);
//        when(scoreTemplate.getDomainLevelsJson()).thenReturn("[]");
//        when(portfolioDao.loadScoreTemplate(anyString())).thenReturn(scoreTemplate);

        RatingPeriod period = studentService.setRatingPeriodV2(childId, null, false, null);
        Assert.assertEquals(period.getAlias(), "P123");
        verify(studentDao, times(0)).inActiveCurrentPeriod(anyString());
    }

    /**
     * 设置孩子默认的周期
     * case : 孩子所在班级能评分, 孩子有入学日期, 班级有周期
     * 结果 : 孩子周期应该根据入学日期创建
     */
    @Test
    public void testSetRatingPeriod_ChildHasEnrollmentDate() {
        String childId = "E123";
        String domainId = "D123";
        String groupId = "G123";

        com.learninggenie.common.data.entity.DomainEntity domain = new com.learninggenie.common.data.entity.DomainEntity();
        domain.setId(domainId);

        EnrollmentEntity enrollment = new EnrollmentEntity();
        enrollment.setId(childId);
        enrollment.setEnrollmentDate(TimeUtil.parseDate("08/24/2017"));

        CenterEntity center = new CenterEntity();
        center.setId("C123");
        center.setName("CN123");
        center.setCenterTimeZone("America/Los_Angeles");

        GroupEntity group = new GroupEntity();
        group.setId(groupId);
        group.setDomain(domain);
        group.setCenter(center);
        enrollment.setGroup(group);
        group.getEnrollments().add(enrollment);

        GroupPeriodEntity groupPeriod = new GroupPeriodEntity();
        groupPeriod.setId("P123");
        groupPeriod.setAlias("P123");
        groupPeriod.setFromAtLocal(TimeUtil.parseDate("06/12/2017"));
        groupPeriod.setToAtLocal(TimeUtil.parseDate("08/24/2018"));

        group.setCurrentPeriod(groupPeriod);

        RatingPeriodEntity periodEntity = new RatingPeriodEntity();
        periodEntity.setId("P123");
        RatingPeriodEntity returnPeriod = new RatingPeriodEntity();
        returnPeriod.setId("R123");
        returnPeriod.setActived(true);
        returnPeriod.setEnrollmentId(childId);
        returnPeriod.setFromAtLocal(groupPeriod.getFromAtLocal());
        returnPeriod.setToAtLocal(groupPeriod.getToAtLocal());
        returnPeriod.setAlias("2016-2017 Fall");
        returnPeriod.setCreateAtUtc(TimeUtil.parseDate("06/12/2017"));
        returnPeriod.setUpdateAtUtc(TimeUtil.parseDate("08/24/2018"));

        List<GroupPeriodEntity> periodEntities = new ArrayList<>();
        periodEntities.add(groupPeriod);

        // 机构数据
        AgencyEntity agency = new AgencyEntity();
        agency.setId("A00001");

        // 机构元数据
        AgencyMetaDataEntity agencyMeta = new AgencyMetaDataEntity();
        agencyMeta.setMetaValue("true");

        when(agencyDao.getAgencyByChildId(childId)).thenReturn(agency);
        when(agencyDao.getMeta(agency.getId(), AgencyMetaKey.AUTO_GENERATE_NOTE_OPEN.toString())).thenReturn(agencyMeta);
//        when(groupPeriodRepository.getByGroupId(anyString())).thenReturn(periodEntities);
        when(studentDao.getChildWithGroupCenter(childId)).thenReturn(enrollment);
        when(groupDao.getGroupByChildId(childId)).thenReturn(group);
//        when(weeklyService.hasScoreTemplate(domain.getId())).thenReturn(true);
//        when(groupDao.getActivePeriod(groupId)).thenReturn(null);
        when(studentDao.insertNewPeriod(any(RatingPeriodEntity.class))).thenReturn(periodEntity);
        when(studentDao.getPeriodById(periodEntity.getId())).thenReturn(returnPeriod);
        when(groupRepository.getByIdAndIsDeletedFalse(anyString())).thenReturn(group);

//        com.learninggenie.common.data.model.DomainEntity framework = mock(com.learninggenie.common.data.model.DomainEntity.class);
//        ScoreTemplateEntity scoreTemplate = mock(ScoreTemplateEntity.class);
//        when(scoreTemplate.getDomainLevelsJson()).thenReturn("[]");
//        when(portfolioDao.loadScoreTemplate(anyString())).thenReturn(scoreTemplate);

        RatingPeriod period = studentService.setRatingPeriodV2(childId, null, false, null);
        Assert.assertEquals(period.getAlias(), "2016-2017 Fall");
        verify(studentDao, times(0)).inActiveCurrentPeriod(anyString());
    }


    /**
     * 批量restore学生，以班级为单位
     */
    @Ignore
    @Test
    public void testBatchActiveChild_group() {
        StudentSearch request = new StudentSearch();
        request.setAll(true);
        request.setGroupId("g001");

        EnrollmentModel enrollment = new EnrollmentModel();
        enrollment.setId("e001");
        enrollment.setDisplayName("kitty");
        enrollment.setSourceGroupId("g001");

        List<EnrollmentModel> enrollmentModels = new ArrayList<>();
        enrollmentModels.add(enrollment);

        UserEntity user = new UserEntity();
        user.setId("u001");
        user.setUserName("uuu");

        AgencyModel agency = new AgencyModel();
        agency.setId("a001");
        agency.setName("aaa");

        EnrollmentEntity enrollmentEntity = new EnrollmentEntity();
        enrollmentEntity.setId("e001");
        enrollmentEntity.setDisplayName("kitty");

        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId("g001");

        enrollmentEntity.setGroup(groupEntity);

        when(studentDao.getChildrenByGroupId("g001", request.getSearch())).thenReturn(enrollmentModels);
        when(userProvider.checkUser("u001")).thenReturn(user);
        when(userProvider.getAgencyByUserId("u001")).thenReturn(agency);
        when(studentDao.getEnrollment("e001")).thenReturn(enrollment);
        when(enrollmentRepository.findById("e001")).thenReturn(Optional.ofNullable(enrollmentEntity));
        when(groupRepository.findById("g001")).thenReturn(Optional.ofNullable(groupEntity));

        studentService.batchActiveChild(request, "u001");

        verify(studentDao, times(1)).getChildrenByGroupId("g001", request.getSearch());
        verify(userProvider, times(3)).checkUser("u001");
        verify(userProvider, times(2)).getAgencyByUserId("u001");
        verify(studentDao, times(1)).getEnrollment("e001");
        verify(enrollmentRepository, times(1)).findById("e001").orElse(null);
        verify(groupRepository, times(1)).findById("g001").orElse(null);
    }

    /**
     * 批量restore学生，以学校为单位
     */
    @Ignore
    @Test
    public void testBatchActiveChild_center() {
        StudentSearch request = new StudentSearch();
        request.setAll(true);
        request.setCenterId("c001");

        EnrollmentModel enrollment = new EnrollmentModel();
        enrollment.setId("e001");
        enrollment.setDisplayName("kitty");
        enrollment.setSourceGroupId("g001");

        List<EnrollmentModel> enrollmentModels = new ArrayList<>();
        enrollmentModels.add(enrollment);

        UserEntity user = new UserEntity();
        user.setId("u001");
        user.setUserName("uuu");

        AgencyModel agency = new AgencyModel();
        agency.setId("a001");
        agency.setName("aaa");

        EnrollmentEntity enrollmentEntity = new EnrollmentEntity();
        enrollmentEntity.setId("e001");
        enrollmentEntity.setDisplayName("kitty");

        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId("g001");

        enrollmentEntity.setGroup(groupEntity);

        when(studentDao.getChildrenByCenterId("c001", request.getSearch())).thenReturn(enrollmentModels);
        when(userProvider.checkUser("u001")).thenReturn(user);
        when(userProvider.getAgencyByUserId("u001")).thenReturn(agency);
        when(studentDao.getEnrollment("e001")).thenReturn(enrollment);
        when(enrollmentRepository.findById("e001")).thenReturn(Optional.ofNullable(enrollmentEntity));
        when(groupRepository.findById("g001")).thenReturn(Optional.ofNullable(groupEntity));
        when(regionService.isChina()).thenReturn(false);

        studentService.batchActiveChild(request, "u001");

        verify(studentDao, times(1)).getChildrenByCenterId("c001", request.getSearch());
        verify(userProvider, times(3)).checkUser("u001");
        verify(userProvider, times(2)).getAgencyByUserId("u001");
        verify(studentDao, times(1)).getEnrollment("e001");
        verify(enrollmentRepository, times(1)).findById("e001").orElse(null);
        verify(groupRepository, times(1)).findById("g001").orElse(null);
    }

    /**
     * 测试若是家长是待邀请状态，则家长登录时，不返回与之对应的小孩信息
     */
    @Test
    public void testGetEnrollmentsByParentId() {
        // 准备数据
        String childId = UUID.randomUUID().toString(); // 小孩 Id
        String parentId = UUID.randomUUID().toString(); // 家长 Id
        String groupId = UUID.randomUUID().toString(); // 班级 Id
        String stageId = UUID.randomUUID().toString(); // 阶段 Id
        List<EnrollmentEntity> enrollmentEntities = new ArrayList<>(); // 学生列表
        EnrollmentEntity enrollmentEntity = new EnrollmentEntity(); // 学生实体
        GroupEntity group = new GroupEntity(); // 班级实体
        group.setId(groupId); // 设置班级 Id
        GroupStageEntity stage = new GroupStageEntity(); // 班级年龄实体
        stage.setId(stageId); // 设置年龄 Id
        group.setStage(stage); // 设置年龄
        MediaEntity enrollmentAvatar = new MediaEntity(); // 头像实体
        String avatarPath = "path"; // 头像相对路径
        enrollmentAvatar.setRelativePath(avatarPath); // 设置头像相对路径
        CenterEntity center = new CenterEntity(); // 学校实体
        center.setId(UUID.randomUUID().toString()); // 设置学校 Id
        group.setCenter(center); // 设置学校
        enrollmentEntity.setAvatarMedia(enrollmentAvatar); // 设置头像
        enrollmentEntity.setGroup(group); // 设置班级
        enrollmentEntity.setInactive(true); // 设置学生状态
        enrollmentEntity.setId(childId); // 设置学生 Id
        enrollmentEntities.add(enrollmentEntity);
        // 模拟学生列表
        when(studentDao.getEnrollmentsByParentId(parentId)).thenReturn(enrollmentEntities);
        // 家长和小孩关系列表
        List<EnrollmentDTO> allInvitations = new ArrayList<>();
        // 家长和小孩关系
        EnrollmentDTO enrollmentDTO = new EnrollmentDTO();
        enrollmentDTO.setState("0"); // 设置为待邀请状态
        allInvitations.add(enrollmentDTO);
        // 模拟家长和小孩关系列表
        when(enrollmentDao.getByChildAndUserWithDeleted(parentId, childId)).thenReturn(allInvitations);

        // 调用方法
        List<ParentEnrollmentResponse> responses = studentService.getEnrollmentsByParentId(parentId);

        // 验证家长为未邀请状态，则不返回相关的小孩信息·
        assertEquals(responses.size(), 0);
    }

    @Test
    public void testUpdateInvitation() {
        String invitationId = UUID.randomUUID().toString();
        InvitationRequest request = new InvitationRequest();
        request.setEmail("<EMAIL>");
        String userAvatarMediaId="123123";
        request.setUserAvatarMediaId("123");
        request.setUserDisplayName("xiaohei");
        EnrollmentInvitationEntity model = new EnrollmentInvitationEntity();
        when(enrollmentProvider.checkInvitation(invitationId)).thenReturn(model);
        MediaEntity mediaEntity = new MediaEntity();
        mediaEntity.setId(userAvatarMediaId);
//        when(mediaProvider.checkMedia(userAvatarMediaId)).thenReturn(mediaEntity);
        studentService.updateInvitation(invitationId,request);
        assertEquals(userAvatarMediaId, mediaEntity.getId());
    }

    /**
     * Case:孩子Ids为空
     * 结果:抛异常
     */
    @Test(expected = BusinessException.class)
    public void testSendInvitations_childIdsIsEmpty() {
        List<String> child_ids = new ArrayList<>();
        String userId = "u001";
        GenerateParentsCode request = new GenerateParentsCode(child_ids,userId);
        studentService.sendInvitations(request);
    }

    /**
     * Case:孩子无所属班级
     * 结果:抛异常
     */
    @Test(expected = BusinessException.class)
    public void testSendInvitataion_childGroupIsEmpty() {
        List<String> child_ids = new ArrayList<>();
        child_ids.add("c001");
        String userId = "u001";
        GenerateParentsCode request = new GenerateParentsCode(child_ids,userId);
        //enrollment
        EnrollmentEntity enrollment = new EnrollmentEntity();
        enrollment.setGroup(null);
        when(enrollmentProvider.checkEnrollment("c001")).thenReturn(enrollment);
        studentService.sendInvitations(request);
    }

    /**
     * Case:孩子班级无所属学校
     * 结果:抛异常
     */
    @Test(expected = BusinessException.class)
    public void testSendInvitation_childCenterIsEmpty() {
        List<String> child_ids = new ArrayList<>();
        child_ids.add("c001");
        String userId = "u001";
        GenerateParentsCode request = new GenerateParentsCode(child_ids,userId);
        //group
        GroupEntity group = new GroupEntity();
        group.setId("g001");
        //enrollment
        EnrollmentEntity enrollment = new EnrollmentEntity();
        enrollment.setGroup(group);

        when(enrollmentProvider.checkEnrollment("c001")).thenReturn(enrollment);
        when(centerDao.getCentersByGroupIds(Arrays.asList(group.getId()))).thenReturn(null);
        studentService.sendInvitations(request);
    }

    /**
     * Case:Grantee用户不能邀请家长
     * 结果:(抛异常)
     */
    @Test(expected = BusinessException.class)
    public void testSendInvitation_granteeCanNotInvite() {
        List<String> child_ids = new ArrayList<>();
        GenerateParentsCode request = new GenerateParentsCode(child_ids,"u001");
        //userAgency
        Set<AgencyEntity> userAgencies = new HashSet<>();
        AgencyEntity agencyEntity = new AgencyEntity();
        agencyEntity.setId("ua001");
        userAgencies.add(agencyEntity);
        //user
        UserEntity user = new UserEntity();
        user.setId("u001");
        user.setType("GRANTEE");
        user.setAgencies(userAgencies);
        request.setUser_id("u001");
        //childIds
        child_ids.add("c001");
        request.setEnrollment_ids(child_ids);
        //agecny
        AgencyEntity childAgency = new AgencyEntity();
        childAgency.setId("ga001");
        //center
        CenterEntity center = new CenterEntity();
        center.setId("c001");
        List<CenterEntity> centers = new ArrayList<>();
        centers.add(center);
        //group
        GroupEntity group = new GroupEntity();
        group.setId("g001");
        //enrollment
        EnrollmentEntity enrollment = new EnrollmentEntity();
        enrollment.setGroup(group);

        when(enrollmentProvider.checkEnrollment(request.getEnrollment_ids().get(0))).thenReturn(enrollment);
        when(centerDao.getCentersByGroupIds(Arrays.asList(group.getId()))).thenReturn(centers);
        when(userProvider.checkUser(request.getUser_id())).thenReturn(user);
        when(agencyDao.getByCenterId(center.getId())).thenReturn(childAgency);
        studentService.sendInvitations(request);
    }

    /**
     * Case:开启学院模式的学校不能发送邀请码
     * 结果:抛异常
     */
    @Test(expected = BusinessException.class)
    public void testSendInvitation_centerAcamedyOpenCanNotInvite() {
        List<String> child_ids = new ArrayList<>();
        GenerateParentsCode request = new GenerateParentsCode(child_ids,"u001");
        //user
        UserEntity user = new UserEntity();
        user.setId("u001");
        request.setUser_id("u001");
        //childIds
        request.setEnrollment_ids(child_ids);
        //center
        CenterEntity center = new CenterEntity();
        center.setId("c001");
        List<CenterEntity> centers = new ArrayList<>();
        centers.add(center);
        //group
        GroupEntity group = new GroupEntity();
        group.setId("g001");
        group.setName("g001name");
        //enrollment
        EnrollmentEntity enrollment = new EnrollmentEntity();
        enrollment.setGroup(group);

//        when(enrollmentRepository.findById("e001")).thenReturn(Optional.ofNullable.thenReturn(enrollment);
//        when(centerDao.getCentersByGroupIds(Arrays.asList(group.getId()))).thenReturn(centers);
//        when(userProvider.checkUser(request.getUser_id())).thenReturn(user);
//        when(centerProvider.checkAcamedyOpen(center.getId())).thenReturn(true);
        studentService.sendInvitations(request);
    }

    /**
     * Case:正常发送家长邀请码
     */
    @Test
    public void testSendInvitations_childIsNotExist(){
        String enrollmentId = "e001";
        String userId = "u001";
        String email = "<EMAIL>";
        String code = "GH56S22";
        //group
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId("g001");
        groupEntity.setName("g001Name");
        //enrollmemt
        EnrollmentEntity enrollment = new EnrollmentEntity();
        enrollment.setId(enrollmentId);
        enrollment.setDisplayName("edname001");
        enrollment.setGroup(groupEntity);
        //teacher
        UserEntity teacher = new UserEntity();
        teacher.setId(userId);
        teacher.setEmail(email);
        //profile
        UserProfileEntity userProfileEntity = new UserProfileEntity();
        userProfileEntity.setDisplayName("td001");
        teacher.setProfile(userProfileEntity);
        //center
        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setId("c001");
        centerEntity.setName("c001Name");

        List<CenterEntity> centerEntities = new ArrayList<>();
        centerEntities.add(centerEntity);
        ReflectionTestUtils.setField(studentService,"emailTemplateVersion","v1");

        when(enrollmentProvider.checkEnrollment(enrollmentId)).thenReturn(enrollment);
        when(userProvider.checkUser(userId)).thenReturn(teacher);
        when(centerDao.getCentersByGroupIds(Arrays.asList(groupEntity.getId()))).thenReturn(centerEntities);

        studentService.sendInvitations(enrollmentId,userId,email,code);
        //检查孩子是否存在或被删除
        verify(enrollmentProvider,times(1)).checkEnrollment(anyString());
        //检查用户是否存在或被删除
        verify(userProvider,times(1)).checkUser(anyString());
        //获取孩子班级
        verify(centerDao,times(1)).getCentersByGroupIds(anyList());
        //发送邮件
        verify(emailService,times(1)).sendAsync(any());
    }

    /**
     * Case:验证家长的邀请码,验证码不存在
     */
    @Test(expected = BusinessException.class)
    public void testValidateInvitationCode(){
        when(invitationDao.checkEnrollmentInvitationEntity(anyString())).thenReturn(null);
        when(invitationsEnrollmentInvitationDao.getByPwdNotDelete(anyString())).thenReturn(new ArrayList<>());
        String code = "c001";
        studentService.validateInvitationCode(code);
    }

    @Ignore
    @Test
    public void testCheckChildTransfer_it_to_ps() {
        // 准备数据
        // 学生ID
        String enrollmentId = "e001";
        // 小孩信息
        EnrollmentEntity enrollmentEntity = new EnrollmentEntity();
        enrollmentEntity.setId(enrollmentId);
        enrollmentEntity.setEnrollmentDate(new Date("05/01/2022"));
        List<EnrollmentEntity> enrollmentEntities = new ArrayList<>();
        enrollmentEntities.add(enrollmentEntity);
        // 转入班级ID
        String targetGroupId = "targetGroup001";
        // 原班级ID
        String sourceGroupId = "sourceGroup001";
        // 入学时间
        String entryDate = "05/01/2022";
        // when(ReflectionTestUtils.invokeMethod(studentService, "getSchoolYear", enrollmentId)).thenReturn("2021-2022");
        // 转入班级
        com.learninggenie.common.data.model.GroupEntity targetGroup = new com.learninggenie.common.data.model.GroupEntity();
        targetGroup.setId(targetGroupId);
        targetGroup.setPeriodGroupId("targetGroupPeriodGroup001");
        // 转入学校
        com.learninggenie.common.data.model.CenterEntity targetCenter = new com.learninggenie.common.data.model.CenterEntity();
        targetCenter.setId("targetCenter001");
        // 转入框架
        com.learninggenie.common.data.model.DomainEntity targetDomain = new com.learninggenie.common.data.model.DomainEntity();
        targetDomain.setId("targetDomain001");
        targetDomain.setName("ps");
        targetGroup.setDomain(targetDomain);
        // 原班级
        com.learninggenie.common.data.model.GroupEntity sourceGroup = new com.learninggenie.common.data.model.GroupEntity();
        sourceGroup.setId(sourceGroupId);
        sourceGroup.setPeriodGroupId("sourceGroupPeriodGroup001");
        // 原学校
        com.learninggenie.common.data.model.CenterEntity sourceCenter = new com.learninggenie.common.data.model.CenterEntity();
        sourceCenter.setId("sourceCenter001");
        sourceGroup.setCenter(sourceCenter);
        // 原框架
        com.learninggenie.common.data.model.DomainEntity sourceDomain = new com.learninggenie.common.data.model.DomainEntity();
        sourceDomain.setId("sourceDomain001");
        sourceDomain.setName("it");
        sourceGroup.setDomain(sourceDomain);
        // 框架列表
        List<com.learninggenie.common.data.model.DomainEntity> domainList = new ArrayList<>();
        domainList.add(sourceDomain);
        domainList.add(targetDomain);
        // 目标班级周期组
        com.learninggenie.common.data.model.PeriodsGroupEntity periodsGroupEntity = new com.learninggenie.common.data.model.PeriodsGroupEntity();
        periodsGroupEntity.setPeriodGroupType("season");
        // 小孩评分周期
        List<RatingPeriodEntity> ratingPeriodList = new ArrayList<>();
        RatingPeriodEntity ratingPeriodEntity1 = new RatingPeriodEntity();
        ratingPeriodEntity1.setId("p1");
        ratingPeriodEntity1.setToAtLocal(new Date("04/01/2022"));
        ratingPeriodEntity1.setToAtLocal(new Date(" 05/30/2022"));
        ratingPeriodEntity1.setActived(true);
        RatingPeriodEntity ratingPeriodEntity2 = new RatingPeriodEntity();
        ratingPeriodEntity2.setId("p2");
        ratingPeriodEntity2.setToAtLocal(new Date("05/31/2022"));
        ratingPeriodEntity2.setToAtLocal(new Date("08/31/2022"));
        RatingPeriodEntity ratingPeriodEntity3 = new RatingPeriodEntity();
        ratingPeriodEntity3.setId("p3");
        ratingPeriodEntity3.setToAtLocal(new Date("09/01/2022"));
        ratingPeriodEntity3.setToAtLocal(new Date("11/30/2022"));
        ratingPeriodList.add(ratingPeriodEntity1);
        ratingPeriodList.add(ratingPeriodEntity2);
        ratingPeriodList.add(ratingPeriodEntity3);

        // mock 数据
        // 获取转入班级
        when(groupDao.getGroupBySchoolYear(targetGroupId, "2021-2022")).thenReturn(targetGroup);
        // 获取转班记录
        when(studentDao.getMeta(enrollmentId, "TRANSFER_METADATA")).thenReturn(null);
        // 获取小孩信息（附带学校班级信息）
        when(studentDao.getChildWithGroupCenter(enrollmentId)).thenReturn(enrollmentEntity);
        // 获取原班级ID
        when(studentDao.getGroupIdByChildId(enrollmentId)).thenReturn(sourceGroupId);
        // 获取原班级
        when(groupDao.getGroupCurrentSchoolYearPeriodById(sourceGroupId)).thenReturn(sourceGroup);
        // 批量查询框架
        when(domainDao.getDomains(anyString())).thenReturn(domainList);
        // 获取原班级周期组
        when(periodsGroupDao.getById(targetGroup.getPeriodGroupId())).thenReturn(periodsGroupEntity);
        when(studentDao.getAllPeriods(enrollmentId)).thenReturn(ratingPeriodList);
        // 班级框架(it -> ps)
        when(ratingService.isPSFramework(sourceDomain.getId())).thenReturn(false);
        when(ratingService.isPSFramework(targetDomain.getId())).thenReturn(true);
        when(ratingService.isITFramework(sourceDomain.getId())).thenReturn(true);
              when(ratingService.isITFramework(targetDomain.getId())).thenReturn(false);
        when(groupDao.getGroupById(targetGroupId)).thenReturn(targetGroup);
        // 获取更新后的周期

        when(periodService.updateEnrollmentPeriods(enrollmentEntities, targetGroup.getPeriodGroupId(), new ArrayList<>(), false, false, true)).thenReturn(ratingPeriodList);

        // 执行测试
        studentService.checkChildTransfer(enrollmentId, targetGroupId, entryDate, false, false, false, false);
    }

    @Test
    public void testGetChildClassIdAvailable () {
        EnrollmentModel enrollment = new EnrollmentModel();
        enrollment.setId("1");
        enrollment.setSourceGroupId("1");

        AuthUserDetails user = new AuthUserDetails();
        user.setUsername("1");

        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("1");

        GroupMetaDataEntity sourceIdMeta = new GroupMetaDataEntity();
        sourceIdMeta.setMetaKey("SOURCE_ID");
        sourceIdMeta.setMetaValue("1");

        List<GroupEntity> groups = new ArrayList<>();

        when(studentDao.getEnrollment(anyString())).thenReturn(enrollment);
        when(userProvider.getCurrentUser()).thenReturn(user);
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agencyModel);
        when(groupDao.getMeta(anyString(), anyString())).thenReturn(sourceIdMeta);
        when(groupDao.getGroupsByMetaAndAgency(anyString(), anyString(), anyString())).thenReturn(groups);

        GetChildClassIdAvailableResponse childClassIdAvailable = studentService.getChildClassIdAvailable(anyString());

        Assert.assertTrue(childClassIdAvailable.isAvailable());
    }

    /**
     * 测试 convertEnrollments 方法
     */
    @Test
    public void testConvertEnrollments() {

        // 数据准备
        String enrollmentId = "E00001";
        String frameworkId = "F00001";
        ReflectionTestUtils.setField(studentService, "childDefaultAvatarUrl", "childDefaultAvatarUrl");

        List<EnrollmentModel> enrollmentModelList = new ArrayList<>();
        EnrollmentModel enrollmentModel = new EnrollmentModel();
        enrollmentModel.setId(enrollmentId);
        enrollmentModel.setFirstName("test");
        enrollmentModel.setLastName("test");
        enrollmentModel.setDisplayName("test");
        enrollmentModel.setAvatarMediaId("");
        enrollmentModel.setCenterId("C00001");
        enrollmentModel.setGroupId("G00001");
        enrollmentModel.setCenterName("test");
        enrollmentModel.setBirthDate("2000-01-02");
        enrollmentModel.setGroupName("test");
        enrollmentModel.setLastGroupName("test");
        enrollmentModel.setLastGroupName("test");
        enrollmentModel.setGender("gender");
        enrollmentModel.setEnrollmentDate("2000-01-03");
        enrollmentModel.setWithdrawnDate("2000-01-04");
        enrollmentModel.setLocked(false);
        enrollmentModel.setCurrentAlias("test");
        enrollmentModel.setFrameworkId(frameworkId);
        enrollmentModel.setFirstName("test");
        enrollmentModel.setInactive(false);
        enrollmentModel.setPrivatePhoto(false);
        enrollmentModel.setEmail("<EMAIL>");
        enrollmentModel.setPhoneNumber("123456789");
        enrollmentModelList.add(enrollmentModel);
        List<StudentAttrEntity> attrs = new ArrayList<>();
        StudentAttrEntity attr1 = new StudentAttrEntity();
        attr1.setEnrollmentId(enrollmentId);
        attr1.setAttrName("IEP/IFSP");
        attr1.setAttrValue("true");
        StudentAttrEntity attr2 = new StudentAttrEntity();
        attr2.setEnrollmentId(enrollmentId);
        attr2.setAttrName("ELD");
        attr2.setAttrValue("true");

        // mock 为当前小孩添加 language 属性
        StudentAttrEntity attr3 = new StudentAttrEntity();
        attr3.setEnrollmentId(enrollmentId);
        attr3.setAttrName(DrdpAttr.LANGUAGE.toString());
        attr3.setAttrValue("Spanish");
        attrs.add(attr1);
        attrs.add(attr2);
        attrs.add(attr3);

        List<EnrollmentAttrEntity> allAttrs = new ArrayList<>();
        AgencyModel agencyModel = new AgencyModel();
        Boolean isDRDPtech = true;
        List<ChildAttrGroup> publicAttrs = new ArrayList<>();

        Map<String, Map<String, Boolean>> childIdToIepAndEldMap = new HashMap<>();
        Map<String, Boolean> eldMap = new HashMap<>();
        eldMap.put("ELD", true);
        eldMap.put("IEP/IFSP", true);
        childIdToIepAndEldMap.put(enrollmentId, eldMap);

        ReflectionTestUtils.setField(studentService, "idPSE2015", frameworkId);
        ReflectionTestUtils.setField(studentService, "idITE2015", frameworkId);
        // 模拟接口
//        UserEntity userEntity = new UserEntity();
//        userEntity.setId("U001");
//        when(userProvider.checkUser(userProvider.getCurrentUserId())).thenReturn(userEntity);
        when(agencyDao.isDRDPtech(any())).thenReturn(isDRDPtech);
//        when(userProvider.isGranteeTeacher(userEntity)).thenReturn(false);
        when(analysisService.getAttrGroups(DRDP.DRDP_TECH_ATTR_ALL.toString(), null)).thenReturn(publicAttrs);
        studentService.convertEnrollments(enrollmentModelList, attrs, allAttrs, true, agencyModel, null);

        // 验证结果
        verify(agencyDao, times(1)).isDRDPtech(any());
    }

    /**
     * 测试批量更新小孩属性方法
     */
    @Test
    public void testBatchCreateOrUpdateEnrollmentMeta() {
        // 数据准备
        String enrollmentId = "E00001";
        String groupId = "G00001";
        List<String> groupIds = new ArrayList<>();
        groupIds.add(groupId);
        // 更新邮箱信息
        String email = "<EMAIL>";
        List<String> childIds = new ArrayList<>();
        childIds.add(enrollmentId);
        // 设置班级数据
        List<com.learninggenie.common.data.entity.GroupEntity> groups = new ArrayList<>();
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId(groupId);
        groupEntity.setName("test");
        groups.add(groupEntity);

        List<ChildEntity> children = new ArrayList<>();
        ChildEntity childEntity = new ChildEntity();
        childEntity.setName("123");
        childEntity.setCreated("");
        childEntity.setId(enrollmentId);
        com.learninggenie.common.data.model.GroupEntity group = new com.learninggenie.common.data.model.GroupEntity();
        group.setId(groupId);
        childEntity.setGroup(group);
        children.add(childEntity);
        // 设置小孩 id
        List<String> child01Ids = new ArrayList<>();
        child01Ids.add("123");
        // 设置小孩属性
        List<StudentAttrEntity> studentAttrEntities = new ArrayList<>();
        StudentAttrEntity studentAttrEntity = new StudentAttrEntity();
        studentAttrEntity.setAttrName(ChildMetaKey.Teacher.toString());
        studentAttrEntity.setAttrValue("<EMAIL>");
        studentAttrEntity.setEnrollmentId(enrollmentId);
        studentAttrEntities.add(studentAttrEntity);
        // mock 数据
        Mockito.when(groupDao.getGroupByChildIds(StringUtil.convertIdsToString(childIds))).thenReturn(groups);
        String groupIdStr = "(" + StringUtil.convertIdsToString(groupIds) + ")";
        Mockito.when(groupDao.getChildrenByGroups(groupIdStr)).thenReturn(children);
        when(groupDao.getGroupByChildIds(StringUtil.convertIdsToString(childIds))).thenReturn(groups);
        when(studentDao.getAttrsByChildIdsAndMetaKey(childIds, ChildMetaKey.Teacher.toString())).thenReturn(studentAttrEntities);
        studentService.batchCreateOrUpdateEnrollmentMeta(childIds, ChildMetaKey.Teacher.toString(), email);
        // 验证结果
        verify(studentDao,times(1)).batchUpdateMetaData(ChildMetaKey.Teacher.toString(), email, childIds);
    }

    /**
     * 测试设置小孩继承班级属性方法
     */
    @Test
    public void testSetChildTeacherAttr() {
        // 数据准备
        String childId = "C00001";
        String childId2 = "C00002";
        String groupId = "G00001";
        // 设置小孩 id
        List<String> childIds = new ArrayList<>();
        childIds.add(childId2);
        // 设置小孩属性
        List<StudentAttrEntity> studentAttrEntities = new ArrayList<>();
        StudentAttrEntity studentAttrEntity = new StudentAttrEntity();
        studentAttrEntity.setAttrName(ChildMetaKey.Teacher.toString());
        studentAttrEntity.setAttrValue("<EMAIL>");
        studentAttrEntity.setEnrollmentId(childId2);
        studentAttrEntities.add(studentAttrEntity);
        // 设置小孩数据
        List<EnrollmentModel> students = new ArrayList<>();
        EnrollmentModel enrollmentModel = new EnrollmentModel();
        enrollmentModel.setId(childId2);
        enrollmentModel.setGroupId(groupId);
        students.add(enrollmentModel);
        // mock 数据
        when(studentDao.getChildrenByGroup(groupId)).thenReturn(students);
        when(studentDao.getAttrsByChildIdsAndMetaKey(childIds, ChildMetaKey.Teacher.toString())).thenReturn(studentAttrEntities);
        studentService.setChildTeacherAttr(groupId, childId);
        // 验证结果
        verify(studentDao,times(1)).createOrUpdateMeta(childId, ChildMetaKey.Teacher.toString(), studentAttrEntities.get(0).getAttrValue());
    }

    @Test
    public void testSetAvatar() {
        // Arrange
        String expectedAvatarMediaId = "avatar123";
        String expectedChildId = "child123";
        UpdateStudentRequest request = new UpdateStudentRequest();
        request.setAvatarMediaId(expectedAvatarMediaId);
        request.setId(expectedChildId);

        // Act
        studentService.setAvatar(request);

        // Assert
        verify(enrollmentDao).updateAvatar(expectedChildId, expectedAvatarMediaId);
    }

    /**
     * 测试设置小孩主班老师是否改变
     */
    @Test
    public void testCheckRecordTeacherChange() {

        // 数据准备
        String childId = "C00001";
        String teacherEmail01 = "email01";
        String teacherEmail02 = "email02";
        List<StudentsAttrRequest> attrs = new ArrayList<>();
        // 设置小孩属性
        StudentsAttrRequest studentsAttrRequest = new StudentsAttrRequest();
        studentsAttrRequest.setName(ChildMetaKey.Teacher.toString());
        List<StudentsAttrValuesRequest> values = new ArrayList<>();
        StudentsAttrValuesRequest studentsAttrValuesRequest = new StudentsAttrValuesRequest();
        studentsAttrValuesRequest.setName(teacherEmail01);
        values.add(studentsAttrValuesRequest);
        studentsAttrRequest.setValues(values);
        attrs.add(studentsAttrRequest);

        List<StudentAttrEntity> studentAttrEntities = new ArrayList<>();
        StudentAttrEntity attrEntity = new StudentAttrEntity();
        attrEntity.setAttrName(ChildMetaKey.Teacher.toString());
        attrEntity.setAttrValue(teacherEmail02);
        studentAttrEntities.add(attrEntity);
        // 模拟请求
        when(studentDao.getAttrsByChildIdAndMetaKey(childId, ChildMetaKey.Teacher.toString())).thenReturn(studentAttrEntities);
        boolean changed = studentService.checkRecordTeacherChange(childId, attrs);

        // 验证结果
        Assert.assertTrue(changed);
    }

    /**
     * 测试批量设置主班老师
     */
    @Test
    public void testBatchCreateOrUpdateEnrollmentTeacher() {
        // 数据准备
        String userId = "U00001";
        String childId = "C00001";
        String groupId = "G00001";
        String teacherEmail = "email01";
        boolean isUpdate = true;
        boolean isBatchAdd = false;
        List<String> childIds = new ArrayList<>();
        childIds.add(childId);
        EnrollmentEntity enrollment = new EnrollmentEntity();
        enrollment.setId(childId);
        enrollment.setDisplayName("child001");
        GroupEntity group = new GroupEntity();
        group.setId(groupId);
        enrollment.setGroup(group);

        AgencyModel agency = new AgencyModel();
        agency.setId("A00001");

        List<com.learninggenie.common.data.model.GroupEntity> groupEntities = new ArrayList<>();
        com.learninggenie.common.data.model.GroupEntity groupEntity = new com.learninggenie.common.data.model.GroupEntity();
        groupEntity.setId(groupId);
        groupEntities.add(groupEntity);

        when(enrollmentRepository.findById(anyString())).thenReturn(Optional.of(enrollment));
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.isGranteeTeacher(userId)).thenReturn(true);
        when(groupDao.getGroupByAgencyId(agency.getId())).thenReturn(groupEntities);
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agency);
        studentService.batchCreateOrUpdateEnrollmentTeacher(childIds, groupId, teacherEmail, isUpdate, isBatchAdd);
    }

    /**
     * 测试自动生成/删除单个小孩特定类型 Note 数据
     */
    @Test
    public void testAutoGenerateAndDeleteNotes() {
        // 数据准备
        String studentId = "S00001";
        List<StudentsAttrRequest> attrs = new ArrayList<>();
        // 设置小孩 IEP 属性
        StudentsAttrRequest IEPYesAttr = new StudentsAttrRequest();
        IEPYesAttr.setName("IEP");
        List<StudentsAttrValuesRequest> IEPYesValues = new ArrayList<>();
        StudentsAttrValuesRequest IEPYesValue = new StudentsAttrValuesRequest();
        IEPYesValue.setName("YES");
        IEPYesValues.add(IEPYesValue);
        IEPYesAttr.setValues(IEPYesValues);
        attrs.add(IEPYesAttr);

        // 设置小孩 ELD 属性
        StudentsAttrRequest ELDAttr = new StudentsAttrRequest();
        ELDAttr.setName("ELD");
        List<StudentsAttrValuesRequest> ELDValues = new ArrayList<>();
        StudentsAttrValuesRequest ELDValue = new StudentsAttrValuesRequest();
        ELDValue.setName("NO");
        ELDValues.add(ELDValue);
        ELDAttr.setValues(ELDValues);
        attrs.add(ELDAttr);

        // 设置小孩 BILINGUAL 属性
        StudentsAttrRequest BILINGUALAttr = new StudentsAttrRequest();
        BILINGUALAttr.setName("BILINGUAL");
        List<StudentsAttrValuesRequest> BILINGUALValues = new ArrayList<>();
        StudentsAttrValuesRequest BILINGUALValue = new StudentsAttrValuesRequest();
        BILINGUALValue.setName("NO");
        BILINGUALValues.add(BILINGUALValue);
        BILINGUALAttr.setValues(BILINGUALValues);
        attrs.add(BILINGUALAttr);

        // 设置周期数据
        RatingPeriodEntity period = new RatingPeriodEntity();
        period.setActived(true);
        period.setFromAtLocal(TimeUtil.parse(new Date(), TimeUtil.format2));
        period.setToAtLocal(TimeUtil.parse(new Date(), TimeUtil.format2));
        period.setAlias("Spring");
        period.setActived(true);

        // 设置用户
        UserEntity user = new UserEntity();
        user.setId("U00001");

        // 小孩自动生成类型的 Note 数据
        List<AutoGenerateNote> autoNotes = new ArrayList<>();
        AutoGenerateNote note = new AutoGenerateNote();
        note.setChildId(studentId);
        // PSC_NOT_IEP 类型的 Note 数据
        com.learninggenie.common.data.entity.NoteEntity PscIEPNote = new com.learninggenie.common.data.entity.NoteEntity();
        PscIEPNote.setId("N00001");
        PscIEPNote.setAutoGenerateType("PSC_NOT_IEP");
        note.setNoteEntity(PscIEPNote);
        autoNotes.add(note);
        note.setNoteEntity(PscIEPNote);

        // PSF_NOT_IEP 类型的 Note 数据
        com.learninggenie.common.data.entity.NoteEntity PsfIEPNote = new com.learninggenie.common.data.entity.NoteEntity();
        PsfIEPNote.setId("N00002");
        PsfIEPNote.setAutoGenerateType("PSF_NOT_IEP");

        // PS_NOT_ELD 类型的 Note 数据
        com.learninggenie.common.data.entity.NoteEntity PsELDNote = new com.learninggenie.common.data.entity.NoteEntity();
        PsELDNote.setId("N00003");
        PsELDNote.setAutoGenerateType("PS_NOT_ELD");

        // K_NOT_ELD 类型的 Note 数据
        com.learninggenie.common.data.entity.NoteEntity KELDNote = new com.learninggenie.common.data.entity.NoteEntity();
        KELDNote.setId("N00004");
        KELDNote.setAutoGenerateType("K_NOT_ELD");

        // K_NOT_BILINGUAL 类型的 Note 数据
        com.learninggenie.common.data.entity.NoteEntity KBNote = new com.learninggenie.common.data.entity.NoteEntity();
        KBNote.setId("N00005");
        KBNote.setAutoGenerateType("K_NOT_BILINGUAL");

        // 以上不同类型的 Note 数据列表
        List<com.learninggenie.common.data.entity.NoteEntity> notes = new ArrayList<>();
        notes.add(PscIEPNote);
        notes.add(PsfIEPNote);
        notes.add(PsELDNote);
        notes.add(KELDNote);
        notes.add(KBNote);

        String groupId = "G00001"; // 班级 Id
        String frameworkId = RateUtil.FRAMEWORK_PS_ID; // PS 框架 Id

        // 评分模板数据
        ScoreTemplateEntity scoreTemplateEntry = new ScoreTemplateEntity();
        scoreTemplateEntry.setDomainLevelsJson("[{\"domainId\":\"measureId\",\"measure\":\"ATL-REG1\",\"measureName\":\"Phonological Awareness\",\"levels\":[{\"id\":\"77F413F8-1EC3-4B70-B6E6-2F153CD936EE\",\"name\":\" Exploring Middle\",\"type\":\"radio\",\"sortIndex\":\"3\",\"value\":\"4\",\"tip\":\"Attends to sounds or elements of language\"},{\"id\":\"976C1402-59B1-4B21-8F16-EAB81A329989\",\"name\":\" Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Demonstrates awareness of variations in sounds\"},{\"id\":\"CFC998E8-3D19-44DB-BE98-664395648330\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Engages actively in play with sounds in words or rhymes, or sings simple songs, or\\nrepeats simple nursery rhymes\"},{\"id\":\"405BA949-F8FF-4CB0-A384-766174446ADF\",\"name\":\"Building Middle\",\"type\":\"radio\",\"sortIndex\":\"6\",\"value\":\"7\",\"tip\":\"Demonstrates awareness of larger units of language (e.g., words, syllables)\"},{\"id\":\"790D37D5-FF13-4DCD-84A3-5847C81323B2\",\"name\":\"Building Later\",\"type\":\"radio\",\"sortIndex\":\"7\",\"value\":\"8\",\"tip\":\"Blends larger units of language (e.g., compound words and syllables) with or without the support of pictures or objects;and segments larger units of language (e.g.\\ncompound words and syllables) with or without the support of pictures or objects\"},{\"id\":\"B16025FA-6DEF-47B4-808E-FAD1C0748308\",\"name\":\"UR (Unable to Rate)\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"},{\"id\":\"63BBC32F-686E-4859-81C5-463C366CD4F7\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"12\",\"value\":\"e\"},{\"id\":\"E72D58A8-167D-4068-8D25-216A174AAEE0\",\"name\":\"Integrating Earlier\",\"type\":\"radio\",\"sortIndex\":\"8\",\"value\":\"9\",\"tip\":\"Segments larger units of language (e.g., compound words and syllables) with or without the support of pictures or objectsBlends smaller units of language (e.g., onsetsand rimes), with or without the support of pictures or objects; and Segments smaller units of language (e.g., onsets and rimes), with or without the support of pictures or objects\"},{\"id\":\"F2A37092-00A9-4592-A182-625B59DEA07B\",\"name\":\"Not yet\",\"type\":\"radio\",\"sortIndex\":\"21\",\"value\":\"n\"}]}]");
        scoreTemplateEntry.setLevelsJson("");
        scoreTemplateEntry.setPortfolioId(frameworkId);

        List<StudentScoreEntity> studentScoreEntityList = new ArrayList<>();
        // 小孩测评点数据
        StudentScoreEntity studentScoreEntity = new StudentScoreEntity();
        studentScoreEntity.setDomainId("10");
        studentScoreEntityList.add(studentScoreEntity);

        // 小孩生成/删除 Note 相关数据
        StudentAutoGenerateAndDeleteNote studentData = new StudentAutoGenerateAndDeleteNote();
        studentData.setPeriod(period); // 设置周期
        studentData.setFrameworkId(RateUtil.FRAMEWORK_PS_ID); // 设置框架 Id
        studentData.setNoteEntities(notes); // 设置 Note 数据

        // 学校
        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setCenterTimeZone("America/Los_Angeles");

        // 机构数据
        AgencyEntity agency = new AgencyEntity();
        agency.setId("A00001");

        // 机构元数据
        AgencyMetaDataEntity agencyMeta = new AgencyMetaDataEntity();
        agencyMeta.setMetaValue("true");

        when(agencyDao.getAgencyByChildId(studentId)).thenReturn(agency);
        when(agencyDao.getMeta(agency.getId(), AgencyMetaKey.AUTO_GENERATE_NOTE_OPEN.toString())).thenReturn(agencyMeta);
        when(studentDao.getCurrentPeriod(studentId)).thenReturn(period);
        when(studentDao.getChildFramework(studentId)).thenReturn(new com.learninggenie.common.data.model.DomainEntity());
        when(studentDao.getGroupIdByChildId(studentId)).thenReturn(groupId);
        when(groupDao.getGroupDomainId(groupId)).thenReturn(frameworkId);
        when(studentDao.isChildLocked(studentId, period.getAlias(), frameworkId)).thenReturn(false);
        when(userProvider.checkUser(anyString())).thenReturn(user);
        when(noteDao.getAutoGeneratedNotesByChildId(studentId)).thenReturn(autoNotes);
        when(centerDao.getByChildId(studentId)).thenReturn(centerEntity);
        when(portfolioDao.loadScoreTemplate(frameworkId)).thenReturn(scoreTemplateEntry);
        when(ratingService.isPSFramework(frameworkId)).thenReturn(true);
        when(ratingService.isKFramework(frameworkId)).thenReturn(true);
        when(scoreDao.gerChildScore(eq(studentId), anyString(), anyString())).thenReturn(studentScoreEntityList);

        // Act
        studentService.autoGenerateAndDeleteNotes(studentId, attrs, null, user.getId(), null);
        studentService.autoGenerateAndDeleteNotes(studentId, attrs, null, user, studentData, false);

        // 验证结果
        verify(ratingService, times(2)).isPSFramework(eq(frameworkId)); // 验证执行两次判断是否为 PS 框架方法
        verify(scoreDao, times(2)).gerChildScore(eq(studentId), anyString(), anyString()); // 验证获取两次小孩评分数据
    }

    /**
     * 测试自动生成/删除班级下小孩特定类型的 Note 数据
     */
    @Test
    public void testAutoGenerateAndDeleteNotesByGroup() {
        // 数据准备
        String studentId = "S0001";
        String groupId = "G0001"; // 班级 Id
        String userId = "U0001"; // 用户 Id

        // 设置小孩数据
        List<StudentAutoGenerateAndDeleteNote> activeNotLockChildren = new ArrayList<>();
        StudentAutoGenerateAndDeleteNote firstStudentData = new StudentAutoGenerateAndDeleteNote();
        firstStudentData.setStudentId("S0001");
        // 设置周期数据
        RatingPeriodEntity period = new RatingPeriodEntity();
        period.setFromAtLocal(new Date());
        period.setToAtLocal(new Date());
        firstStudentData.setPeriod(period);
        com.learninggenie.common.data.entity.NoteEntity firstNote = new com.learninggenie.common.data.entity.NoteEntity();
        firstNote.setId("N0001");
        // 设置 Note 数据
        List<com.learninggenie.common.data.entity.NoteEntity> firstNotes = new ArrayList<>();
        firstNotes.add(firstNote);
        firstStudentData.setNoteEntities(firstNotes);
        firstNote.setAutoGenerateType("K_NOT_BILINGUAL");
        activeNotLockChildren.add(firstStudentData);

        // 存在自动生成 Note 的小孩数据
        List<StudentAutoGenerateAndDeleteNote> withAutoGenerateNoteChildren = new ArrayList<>();
        StudentAutoGenerateAndDeleteNote secondStudentData = new StudentAutoGenerateAndDeleteNote();
        secondStudentData.setStudentId("S0002");
        // 设置 Note 数据
        com.learninggenie.common.data.entity.NoteEntity secondNote = new com.learninggenie.common.data.entity.NoteEntity();
        secondNote.setId("N0002");
        secondNote.setAutoGenerateType("K_NOT_BILINGUAL");
        List<com.learninggenie.common.data.entity.NoteEntity> secondNotes = new ArrayList<>();
        secondNotes.add(secondNote);
        firstStudentData.setNoteEntities(secondNotes);
        secondStudentData.setPeriod(period);
        secondStudentData.setNoteEntities(secondNotes);
        withAutoGenerateNoteChildren.add(firstStudentData);
        withAutoGenerateNoteChildren.add(secondStudentData);

        // 小孩测评点数据
        List<StudentScoreEntity> scores = new ArrayList<>();
        // 设置小孩 K 框架下的测评点数据
        StudentScoreEntity KScore = new StudentScoreEntity();
        KScore.setId("S0001");
        KScore.setNoteId("N0001");
        KScore.setLevelId("L0001");
        KScore.setDomainId(RateUtil.FRAMEWORK_K_ID);
        // 设置小孩 PS 框架下的测评点数据
        StudentScoreEntity PsScore = new StudentScoreEntity();
        PsScore.setId("S0002");
        PsScore.setLevelId("L0002");
        PsScore.setNoteId("N0002");
        PsScore.setDomainId(RateUtil.FRAMEWORK_PS_ID);
        scores.add(KScore);
        scores.add(PsScore);

        // 小孩评分模板数据
        ScoreTemplateEntity scoreTemplateEntry = new ScoreTemplateEntity();
        scoreTemplateEntry.setDomainLevelsJson("[{\"domainId\":\"measureId\",\"measure\":\"ATL-REG1\",\"measureName\":\"Phonological Awareness\",\"levels\":[{\"id\":\"77F413F8-1EC3-4B70-B6E6-2F153CD936EE\",\"name\":\" Exploring Middle\",\"type\":\"radio\",\"sortIndex\":\"3\",\"value\":\"4\",\"tip\":\"Attends to sounds or elements of language\"},{\"id\":\"976C1402-59B1-4B21-8F16-EAB81A329989\",\"name\":\" Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Demonstrates awareness of variations in sounds\"},{\"id\":\"CFC998E8-3D19-44DB-BE98-664395648330\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Engages actively in play with sounds in words or rhymes, or sings simple songs, or\\nrepeats simple nursery rhymes\"},{\"id\":\"405BA949-F8FF-4CB0-A384-766174446ADF\",\"name\":\"Building Middle\",\"type\":\"radio\",\"sortIndex\":\"6\",\"value\":\"7\",\"tip\":\"Demonstrates awareness of larger units of language (e.g., words, syllables)\"},{\"id\":\"790D37D5-FF13-4DCD-84A3-5847C81323B2\",\"name\":\"Building Later\",\"type\":\"radio\",\"sortIndex\":\"7\",\"value\":\"8\",\"tip\":\"Blends larger units of language (e.g., compound words and syllables) with or without the support of pictures or objects;and segments larger units of language (e.g.\\ncompound words and syllables) with or without the support of pictures or objects\"},{\"id\":\"B16025FA-6DEF-47B4-808E-FAD1C0748308\",\"name\":\"UR (Unable to Rate)\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"},{\"id\":\"63BBC32F-686E-4859-81C5-463C366CD4F7\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"12\",\"value\":\"e\"},{\"id\":\"E72D58A8-167D-4068-8D25-216A174AAEE0\",\"name\":\"Integrating Earlier\",\"type\":\"radio\",\"sortIndex\":\"8\",\"value\":\"9\",\"tip\":\"Segments larger units of language (e.g., compound words and syllables) with or without the support of pictures or objectsBlends smaller units of language (e.g., onsetsand rimes), with or without the support of pictures or objects; and Segments smaller units of language (e.g., onsets and rimes), with or without the support of pictures or objects\"},{\"id\":\"F2A37092-00A9-4592-A182-625B59DEA07B\",\"name\":\"Not yet\",\"type\":\"radio\",\"sortIndex\":\"21\",\"value\":\"n\"}]}]");
        scoreTemplateEntry.setLevelsJson("");
        scoreTemplateEntry.setPortfolioId(RateUtil.FRAMEWORK_K_ID);

        List<EnrollmentMetaDataEntity> metas = new ArrayList<>();
        // 小孩 IEP 属性数据
        EnrollmentMetaDataEntity IEPMeta = new EnrollmentMetaDataEntity();
        IEPMeta.setChildId("S0001");
        IEPMeta.setMetaKey("IEP/IFSP");
        IEPMeta.setMetaValue("NO");
        // 小孩 ELD 属性数据
        EnrollmentMetaDataEntity ELDMeta = new EnrollmentMetaDataEntity();
        ELDMeta.setChildId("S0002");
        ELDMeta.setMetaKey("ELD");
        ELDMeta.setMetaValue("NO");
        metas.add(IEPMeta);
        metas.add(ELDMeta);

        // 学校
        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setCenterTimeZone("America/Los_Angeles");

        // 机构数据
        AgencyEntity agency = new AgencyEntity();
        agency.setId("A00001");

        // 机构元数据
        AgencyMetaDataEntity agencyMeta = new AgencyMetaDataEntity();
        agencyMeta.setMetaValue("true");

        when(agencyDao.getByGroupId(groupId)).thenReturn(agency);
        when(agencyDao.getMeta(agency.getId(), AgencyMetaKey.AUTO_GENERATE_NOTE_OPEN.toString())).thenReturn(agencyMeta);
        when(studentDao.getActiveNotLockChildByGroupId(groupId)).thenReturn(activeNotLockChildren);
        when(studentDao.getAutoGeneratedNotesByChildIds(anyList())).thenReturn(withAutoGenerateNoteChildren);
        when(scoreDao.getStudentScoresByNoteIds(anyList())).thenReturn(scores);
        when(centerDao.getCenterByGroupId(groupId)).thenReturn(centerEntity);
        when(studentDao.getMetasByChildIdsAndKeys(anyList(), anyList())).thenReturn(metas);
        when(portfolioDao.loadScoreTemplate(anyString())).thenReturn(scoreTemplateEntry);

        // Act
        studentService.autoGenerateAndDeleteNotesByGroup(groupId, userId);

        // 验证结果
        verify(studentDao, times(1)).getActiveNotLockChildByGroupId(groupId); // 验证执行一次获取班级下所有处于活跃周期且未锁定的小孩
        verify(portfolioDao, times(1)).loadScoreTemplate(anyString()); // 验证执行一次获取评分模板数据
    }

    /**
     * 测试是否需要显示批量离校提示方法
     */
    @Test
    public void testNeedShowBatchInactiveTipByImportRecord() {
        String agencyId = "agencyId";
        // 获取当前时间
        Date utcNow = TimeUtil.getUtcNow();

        // 模拟上年导入的 1 年前的情况
        Date lastYear = TimeUtil.addYears(utcNow, -1);
        SyncHistoryEntity lastImportHistory = mock(SyncHistoryEntity.class);
        when(lastImportHistory.getImportTime()).thenReturn(lastYear);
        when(importErrorDao.getLastImportHistoryExcludeUndoSuccess(anyString())).thenReturn(lastImportHistory);
        // 应该显示批量离校提示
        assertTrue(studentService.needShowBatchInactiveTipByImportRecord(agencyId));

        // 模拟上年导入 2 年前的情况
        Date lastYearTooFar = TimeUtil.addYears(utcNow, -2);
        when(lastImportHistory.getImportTime()).thenReturn(lastYearTooFar);
        // 不应该显示批量离校提示
        assertFalse(studentService.needShowBatchInactiveTipByImportRecord(agencyId));

        // 模拟 1.5 年前导入的情况
        Date oneAndHalfYearsAgo = TimeUtil.addMonths(utcNow, -18);
        when(lastImportHistory.getImportTime()).thenReturn(oneAndHalfYearsAgo);
        // 应该显示批量离校提示
        assertTrue(studentService.needShowBatchInactiveTipByImportRecord(agencyId));

        // 模拟导入日期为 当年 7 月 1 日的情况（根据 utcNow）
        Date julyFirst = TimeUtil.parseDate(TimeUtil.format(utcNow, "yyyy") + "-07-01");
        when(lastImportHistory.getImportTime()).thenReturn(julyFirst);
        // 不应该显示批量离校提示
        assertFalse(studentService.needShowBatchInactiveTipByImportRecord(agencyId));

        // 模拟导入日期为 2024 年 9 月 15 日的情况
        Date septemberFifteenth = TimeUtil.parseDate(TimeUtil.format(utcNow, "yyyy") + "-09-15");
        when(lastImportHistory.getImportTime()).thenReturn(septemberFifteenth);
        // 不应该显示批量离校提示
        assertFalse(studentService.needShowBatchInactiveTipByImportRecord(agencyId));
    }

    /**
     * 测试 checkNeedUpdateFramework 方法
     */
    @Test
    public void testCheckNeedUpdateFramework() {
        // 构造 EnrollmentModel
        EnrollmentModel enrollment = new EnrollmentModel();
        enrollment.setFrameworkId("F1");

        // 构造 GroupEntity 和 DomainEntity
        com.learninggenie.common.data.model.GroupEntity group = new com.learninggenie.common.data.model.GroupEntity();
        group.setId("G1");
        com.learninggenie.common.data.model.DomainEntity domain = new com.learninggenie.common.data.model.DomainEntity();
        domain.setId("F2");
        group.setDomain(domain);

        // 1. 班级和小孩框架有一个为空
        enrollment.setFrameworkId("");
        assertFalse(studentService.checkNeedUpdateFramework(enrollment, group));
        enrollment.setFrameworkId("F1");
        group.getDomain().setId("");
        assertFalse(studentService.checkNeedUpdateFramework(enrollment, group));
        group.getDomain().setId("F2");

        // 2. 多框架班级且班级有多个小孩框架，且包含小孩框架
        List<GroupFrameworkStatsModel> usedFrameworks = new ArrayList<>();
        GroupFrameworkStatsModel stats1 = new GroupFrameworkStatsModel();
        stats1.setFrameworkId("F1");
        usedFrameworks.add(stats1);
        GroupFrameworkStatsModel stats2 = new GroupFrameworkStatsModel();
        stats2.setFrameworkId("F2");
        usedFrameworks.add(stats2);
        when(studentDao.getUsedFrameworksByGroup("G1")).thenReturn(usedFrameworks);
        when(frameworkProvider.isGroupOpenMultiFramework("G1", null)).thenReturn(true);
        when(userProvider.getCurrentAgencyId()).thenReturn(null);
        assertFalse(studentService.checkNeedUpdateFramework(enrollment, group));

        // 2.1 多框架班级且班级有多个小孩框架，不包含小孩框架
        enrollment.setFrameworkId("F3");
        assertTrue(studentService.checkNeedUpdateFramework(enrollment, group));

        // 2.2 多框架班级，usedFrameworkIds 为空
        usedFrameworks = new ArrayList<>();
        when(studentDao.getUsedFrameworksByGroup("G1")).thenReturn(usedFrameworks);
        assertTrue(studentService.checkNeedUpdateFramework(enrollment, group));

        // 2.3 多框架班级，usedFrameworkIds 有重复
        usedFrameworks = new ArrayList<>();
        stats1 = new GroupFrameworkStatsModel();
        stats1.setFrameworkId("F1");
        usedFrameworks.add(stats1);
        stats2 = new GroupFrameworkStatsModel();
        stats2.setFrameworkId("F1");
        usedFrameworks.add(stats2);
        when(studentDao.getUsedFrameworksByGroup("G1")).thenReturn(usedFrameworks);
        enrollment.setFrameworkId("F1");
        assertFalse(studentService.checkNeedUpdateFramework(enrollment, group));
        enrollment.setFrameworkId("F3");
        assertTrue(studentService.checkNeedUpdateFramework(enrollment, group));

        // 3. 多框架班级但班级只有一个小孩框架，domainId 精确判断
        usedFrameworks = new ArrayList<>();
        stats1 = new GroupFrameworkStatsModel();
        stats1.setFrameworkId("ITC");
        usedFrameworks.add(stats1);
        when(studentDao.getUsedFrameworksByGroup("G1")).thenReturn(usedFrameworks);
        when(frameworkProvider.isGroupOpenMultiFramework("G1", null)).thenReturn(true);
        group.getDomain().setId("ITC");
        // a. 班级唯一小孩框架和小孩当前框架一致
        enrollment.setFrameworkId("ITC");
        assertFalse(studentService.checkNeedUpdateFramework(enrollment, group));
        // b. 主框架和小孩框架同属 IT 但 domainId 不同
        enrollment.setFrameworkId("ITE");
        when(ratingService.isITFramework("ITC")).thenReturn(true);
        when(ratingService.isITFramework("ITE")).thenReturn(true);
        assertTrue(studentService.checkNeedUpdateFramework(enrollment, group));
        // c. 主框架和小孩框架同属 PS 但 domainId 不同
        group.getDomain().setId("PSC");
        enrollment.setFrameworkId("PSF");
        when(ratingService.isPSFramework("PSC")).thenReturn(true);
        when(ratingService.isPSFramework("PSF")).thenReturn(true);
        assertTrue(studentService.checkNeedUpdateFramework(enrollment, group));
        // d. IT/PS 并存允许
        group.getDomain().setId("ITC");
        enrollment.setFrameworkId("PSF");
        when(ratingService.isITFramework("ITC")).thenReturn(true);
        when(ratingService.isPSFramework("PSF")).thenReturn(true);
        assertFalse(studentService.checkNeedUpdateFramework(enrollment, group));
        group.getDomain().setId("PSC");
        enrollment.setFrameworkId("ITC");
        when(ratingService.isPSFramework("PSC")).thenReturn(true);
        when(ratingService.isITFramework("ITC")).thenReturn(true);
        assertFalse(studentService.checkNeedUpdateFramework(enrollment, group));

        // 4. 多框架班级但班级还没有小孩框架
        usedFrameworks = new ArrayList<>();
        when(studentDao.getUsedFrameworksByGroup("G1")).thenReturn(usedFrameworks);
        when(frameworkProvider.isGroupOpenMultiFramework("G1", null)).thenReturn(true);
        group.getDomain().setId("ITC");
        enrollment.setFrameworkId("ITE");
        when(ratingService.isITFramework("ITC")).thenReturn(true);
        when(ratingService.isITFramework("ITE")).thenReturn(true);
        assertTrue(studentService.checkNeedUpdateFramework(enrollment, group));
        group.getDomain().setId("PSC");
        enrollment.setFrameworkId("PSF");
        when(ratingService.isPSFramework("PSC")).thenReturn(true);
//        when(ratingService.isPSFramework("PSF")).thenReturn(true);
        assertTrue(studentService.checkNeedUpdateFramework(enrollment, group));
        // 其它情况
        group.getDomain().setId("K1");
        enrollment.setFrameworkId("K2");
        when(ratingService.isITFramework("K1")).thenReturn(false);
        when(ratingService.isPSFramework("K1")).thenReturn(false);
        assertTrue(studentService.checkNeedUpdateFramework(enrollment, group));

        // 5. 单框架班级
        when(frameworkProvider.isGroupOpenMultiFramework("G1", null)).thenReturn(false);
        group.getDomain().setId("F2");
        enrollment.setFrameworkId("F2");
        assertFalse(studentService.checkNeedUpdateFramework(enrollment, group));
        enrollment.setFrameworkId("F7");
        assertTrue(studentService.checkNeedUpdateFramework(enrollment, group));

        // 6. 边界：主框架不在 usedFrameworkIds 里
        usedFrameworks = new ArrayList<>();
        stats1 = new GroupFrameworkStatsModel();
        stats1.setFrameworkId("ITC");
        usedFrameworks.add(stats1);
        when(studentDao.getUsedFrameworksByGroup("G1")).thenReturn(usedFrameworks);
        group.getDomain().setId("ITE");
        enrollment.setFrameworkId("ITE");
        when(frameworkProvider.isGroupOpenMultiFramework("G1", null)).thenReturn(true);
        when(ratingService.isITFramework("ITE")).thenReturn(true);
        when(ratingService.isITFramework("ITC")).thenReturn(true);
        assertTrue(studentService.checkNeedUpdateFramework(enrollment, group));
    }

    /**
     * 测试 updateStudentFramework 方法
     */
    @Test
    public void testUpdateStudentFramework() {
        // 基础数据
        EnrollmentModel enrollment = new EnrollmentModel();
        enrollment.setId("child1");
        enrollment.setFrameworkId("ITC");
        com.learninggenie.common.data.model.GroupEntity group = new com.learninggenie.common.data.model.GroupEntity();
        group.setId("G1");
        com.learninggenie.common.data.model.DomainEntity domain = new com.learninggenie.common.data.model.DomainEntity();
        domain.setId("ITC");
        group.setDomain(domain);
        when(userProvider.getCurrentAgencyId()).thenReturn(null);
        when(userProvider.getTimezoneOffsetNum()).thenReturn(0);

        List<RatingPeriodEntity> ratingPeriods = new ArrayList<>();

        // 1. 多框架班级且班级有多个小孩框架，usedFrameworkIds 包含 childFrameworkId
        List<GroupFrameworkStatsModel> usedFrameworks = new ArrayList<>();
        GroupFrameworkStatsModel stats1 = new GroupFrameworkStatsModel();
        stats1.setFrameworkId("ITC");
        usedFrameworks.add(stats1);
        GroupFrameworkStatsModel stats2 = new GroupFrameworkStatsModel();
        stats2.setFrameworkId("PSC");
        usedFrameworks.add(stats2);
        when(studentDao.getUsedFrameworksByGroup("G1")).thenReturn(usedFrameworks);
        when(frameworkProvider.isGroupOpenMultiFramework("G1", null)).thenReturn(true);
        when(ratingService.isITFramework("ITC")).thenReturn(true);
        when(ratingService.isPSFramework("PSC")).thenReturn(true);
        when(studentDao.getCurrentPeriod("child1")).thenReturn(null);
        studentService.updateStudentFramework(enrollment, group, ratingPeriods);

        // 2. 多框架班级且班级有多个小孩框架，usedFrameworkIds 不包含 childFrameworkId，优先找同年龄段
        enrollment.setFrameworkId("ITE");
        when(ratingService.isITFramework("ITE")).thenReturn(true);
        studentService.updateStudentFramework(enrollment, group, ratingPeriods);

        // 3. 多框架班级只有一个小孩框架，domainId 精确判断
        usedFrameworks = new ArrayList<>();
        stats1 = new GroupFrameworkStatsModel();
        stats1.setFrameworkId("PSC");
        usedFrameworks.add(stats1);
        when(studentDao.getUsedFrameworksByGroup("G1")).thenReturn(usedFrameworks);
        group.getDomain().setId("PSC");
        enrollment.setFrameworkId("PSF");
        when(ratingService.isPSFramework("PSC")).thenReturn(true);
        when(ratingService.isPSFramework("PSF")).thenReturn(true);
        studentService.updateStudentFramework(enrollment, group, ratingPeriods);

        // 4. 多框架班级没有小孩框架，主框架和小孩框架同属 IT/PS 但 domainId 不同
        usedFrameworks = new ArrayList<>();
        when(studentDao.getUsedFrameworksByGroup("G1")).thenReturn(usedFrameworks);
        group.getDomain().setId("ITC");
        enrollment.setFrameworkId("ITE");
        when(ratingService.isITFramework("ITC")).thenReturn(true);
//        when(ratingService.isITFramework("ITE")).thenReturn(true);
        studentService.updateStudentFramework(enrollment, group, ratingPeriods);

        // 5. 单框架班级，主框架和小孩框架一致/不一致
        when(frameworkProvider.isGroupOpenMultiFramework("G1", null)).thenReturn(false);
        group.getDomain().setId("ITC");
        enrollment.setFrameworkId("ITC");
        studentService.updateStudentFramework(enrollment, group, ratingPeriods);
        enrollment.setFrameworkId("PSC");
        when(ratingService.isPSFramework("PSC")).thenReturn(true);
        studentService.updateStudentFramework(enrollment, group, ratingPeriods);

        // 6. PS->IT 且当前及未来学年有PS锁定，抛出异常
        group.getDomain().setId("ITC");
        enrollment.setFrameworkId("PSC");
        when(ratingService.isITFramework("ITC")).thenReturn(true);
        when(ratingService.isPSFramework("PSC")).thenReturn(true);

        // mock 当前周期
        RatingPeriodEntity currentPeriod = new RatingPeriodEntity();
        currentPeriod.setAlias("2023-2024 Fall");
        when(studentDao.getCurrentPeriod("child1")).thenReturn(currentPeriod);

        // mock 所有周期
        List<RatingPeriodEntity> periods = new ArrayList<>();
        RatingPeriodEntity psPeriod = new RatingPeriodEntity();
        psPeriod.setAlias("2023-2024 Fall");
        psPeriod.setDomainId("PSC");
        psPeriod.setFromAtLocal(new java.util.Date());
        psPeriod.setToAtLocal(new java.util.Date(System.currentTimeMillis() + 1000000));
        periods.add(psPeriod);
        when(studentDao.queryAllReportingPeriodsByStudentId("child1")).thenReturn(periods);
        when(studentDao.isChildLocked("child1", "2023-2024 Fall", "PSC")).thenReturn(true);
        when(ratingService.isPSFramework("PSC")).thenReturn(true);

        assertThrows(BusinessException.class, () -> {
            // 打印关键条件
            System.out.println("groupFrameworkId=" + group.getDomain().getId());
            System.out.println("childFrameworkId=" + enrollment.getFrameworkId());
            System.out.println("isITFramework=" + ratingService.isITFramework(group.getDomain().getId()));
            System.out.println("isPSFramework=" + ratingService.isPSFramework(enrollment.getFrameworkId()));
            System.out.println("isChildLocked=" + studentDao.isChildLocked("child1", "2023-2024 Fall", "PSC"));
            studentService.updateStudentFramework(enrollment, group, periods);
        });

        // assertThrows(BusinessException.class, () -> studentService.updateStudentFramework(enrollment, group, true, periods));

        // ----------- 宽松断言部分 -----------
        // 只断言被调用过，不关心参数
        verify(studentDao, atLeastOnce()).updateChildFramework(anyString(), anyString());
    }
}

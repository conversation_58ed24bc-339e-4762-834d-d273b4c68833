//package com.learninggenie.api.service.impl;
//
//import com.learninggenie.common.data.dao.MediaDao;
//import com.learninggenie.common.data.model.MediaRequest;
//import com.learninggenie.common.filesystem.FileSystem;
//import com.learninggenie.common.filesystem.LocalFileSystemImpl;
//import com.learninggenie.common.utils.FileUtil;
//import org.junit.Assert;
//import org.junit.Ignore;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.mockito.runners.MockitoJUnitRunner;
//import org.springframework.core.env.Environment;
//import org.springframework.jdbc.core.JdbcTemplate;
//import org.springframework.mock.web.MockMultipartFile;
//import org.springframework.web.context.ContextLoader;
//import org.springframework.web.multipart.MultipartFile;
//import org.springframework.web.multipart.MultipartHttpServletRequest;
//import org.springframework.web.multipart.commons.CommonsMultipartResolver;
//import sun.misc.BASE64Decoder;
//
//import javax.imageio.ImageIO;
//import javax.servlet.http.HttpServletRequest;
//import java.awt.image.BufferedImage;
//import java.io.*;
//import java.nio.ByteBuffer;
//import java.nio.channels.FileChannel;
//import java.util.UUID;
//
//import static org.mockito.Matchers.any;
//import static org.mockito.Mockito.mock;
//import static org.mockito.Mockito.times;
//import static org.mockito.Mockito.verify;
//import static org.powermock.api.mockito.PowerMockito.when;
//
///**
// * Created by miao on 6/13/2016.
// */
//@RunWith(MockitoJUnitRunner.class)
//public class LocalFileSystemImplTest {
//    @Mock
//    private JdbcTemplate jdbcTemplate;
//    @Mock
//    Environment env;
//    @Mock
//    private CommonsMultipartResolver commonsMultipartResolver;
//    @Mock
//    private MediaDao mediaDao;
//    @InjectMocks
//    private LocalFileSystemImpl fileSystem;
//    private String abcTxt = "dHPKx3Rlc3Q=";
//    private String jpgStr = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVQIW2P8+vXrfwAJpgPg8gE+iwAAAABJRU5ErkJggg==";
//    @Test
//    public void fileUpload() throws Exception {
//        File sourceFile = File.createTempFile("abcdefg", ".txt");
//        File detFile = File.createTempFile("target", "path");
//        String s3Server = detFile.getParent() + File.separator;
//        String s3Bucket = "upLoad";
//        String localPath = s3Server + s3Bucket;
//        File localFile = new File(localPath);
//        ByteBuffer buf = new BASE64Decoder().decodeBufferToByteBuffer(abcTxt);
//        FileChannel outChannel = new RandomAccessFile(sourceFile, "rw").getChannel();
//        outChannel.write(buf);
//        outChannel.close();
//        buf.clear();
//        HttpServletRequest request = mock(HttpServletRequest.class);
//        MultipartHttpServletRequest multipartRequest = mock(MultipartHttpServletRequest.class);
//        FileInputStream fis = new FileInputStream(sourceFile);
//        MultipartFile file = new MockMultipartFile("file", sourceFile.getName(), "text/plain", fis);
//        when(multipartRequest.getContentType()).thenReturn("multipart/form-data;boundary=acebdf13572468");
//        when(multipartRequest.getFile("file")).thenReturn(file);
//        when(env.getProperty("s3.server")).thenReturn(s3Server);
//        when(env.getProperty("s3.bucket")).thenReturn(s3Bucket);
//        when(request.getMethod()).thenReturn("Post");
//        Mockito.when(commonsMultipartResolver.resolveMultipart(Mockito.anyObject())).thenReturn(multipartRequest);
//        when(commonsMultipartResolver.isMultipart(Mockito.anyObject())).thenReturn(true);
//        fileSystem.fileUpload(request,commonsMultipartResolver, "", false);
//        sourceFile.delete();
//        detFile.delete();
//        localFile.delete();
//    }
//
//    @Test
//    public void fileUploadBase64() throws Exception {
//        MediaRequest request = mock(MediaRequest.class);
//        File detFile = File.createTempFile("target", "path");
//        String s3Server = detFile.getParent() + File.separator;
//        String s3Bucket = "upLoad";
//        String localPath = s3Server + s3Bucket;
//        File localFile = new File(localPath);
//        when(env.getProperty("s3.server")).thenReturn(s3Server);
//        when(env.getProperty("s3.bucket")).thenReturn(s3Bucket);
//        when(request.getType()).thenReturn("txt");
//        when(request.getBase64_file()).thenReturn(abcTxt);
//        when(request.getBase64_snapshot_file()).thenReturn(jpgStr);
//        when(request.getType()).thenReturn("txt");
//        when(request.getType()).thenReturn("txt");
//        fileSystem.fileUploadBase(request);
//        detFile.delete();
//        localFile.delete();
//    }
//
//    @Test
//    public void testFileUpload() throws Exception {
//        HttpServletRequest request = mock(HttpServletRequest.class);
//        when(request.getParameter("type")).thenReturn("jpg");
//        when(request.getParameter("fileName")).thenReturn("abc.jpg");
//        when(request.getParameter("annexType")).thenReturn("annex");
//        MultipartHttpServletRequest multipartRequest = mock(MultipartHttpServletRequest.class);
//        MultipartFile file = mock(MultipartFile.class);
//        when(env.getProperty("s3.server")).thenReturn("test");
//        when(env.getProperty("s3.bucket")).thenReturn("test");
//        when(commonsMultipartResolver.isMultipart(Mockito.anyObject())).thenReturn(true);
//        when(commonsMultipartResolver.resolveMultipart(request)).thenReturn(multipartRequest);
//        when(multipartRequest.getFile("file")).thenReturn(file);
//        when(file.getOriginalFilename()).thenReturn("abc.jpg");
//        BufferedImage image = mock(BufferedImage.class);
//        File imgFile=File.createTempFile("abcdefg","abc.jpg");
//        ByteBuffer buf = new BASE64Decoder().decodeBufferToByteBuffer(jpgStr);
//        FileChannel outChannel = new RandomAccessFile(imgFile, "rw").getChannel();
//        outChannel.write(buf);
//        outChannel.close();
//        buf.clear();
//        InputStream in=new FileInputStream(imgFile);
//        when(file.getInputStream()).thenReturn(new FileInputStream(imgFile));
//        fileSystem.fileUpload(request, commonsMultipartResolver,"", false);
//        verify(file, times(1)).transferTo(any(File.class));
//        Assert.assertEquals(image != null,true);
//    }
//
//}
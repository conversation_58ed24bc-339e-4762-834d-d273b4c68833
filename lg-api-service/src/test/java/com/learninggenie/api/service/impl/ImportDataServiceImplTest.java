package com.learninggenie.api.service.impl;

import com.amazonaws.services.lambda.model.InvokeResult;
import com.google.api.client.util.Lists;
import com.google.gson.reflect.TypeToken;
import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.exception.ErrorCode;
import com.learninggenie.api.model.*;
import com.learninggenie.api.model.importdata.*;
import com.learninggenie.api.model.importdata.clever.GetCleverAuthorizeInfoResponse;
import com.learninggenie.api.model.importdata.clever.LinkCleverRosteringRequest;
import com.learninggenie.api.model.importdata.clever.LinkCleverRosteringResponse;
import com.learninggenie.api.model.importdata.mhs.TestConnectionResponse;
import com.learninggenie.api.model.undo.ImportHistoriesResponse;
import com.learninggenie.api.model.undo.ImportHistoryResponse;
import com.learninggenie.api.provider.PeriodProvider;
import com.learninggenie.api.provider.RemoteProvider;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.provider.impl.CleverProviderImpl;
import com.learninggenie.api.provider.impl.MyHeadStartProviderImpl;
import com.learninggenie.api.provider.impl.OneRosterProviderImpl;
import com.learninggenie.api.security.AuthUserDetails;
import com.learninggenie.api.security.DotNetPasswordEncoder;
import com.learninggenie.api.service.AccountService;
import com.learninggenie.api.service.CommonService;
import com.learninggenie.api.service.ImportService;
import com.learninggenie.api.service.PeriodService;
import com.learninggenie.api.util.ImportUtil;
import com.learninggenie.common.cache.CacheModel;
import com.learninggenie.common.cache.CacheService;
import com.learninggenie.common.data.dao.*;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.entity.CenterEntity;
import com.learninggenie.common.data.entity.UserEntity;
import com.learninggenie.common.data.entity.*;
import com.learninggenie.common.data.enums.*;
import com.learninggenie.common.data.enums.api.ErrorType;
import com.learninggenie.common.data.enums.api.MHSApiCode;
import com.learninggenie.common.data.enums.api.VerificationStatus;
import com.learninggenie.common.data.enums.importLog.ImportMark;
import com.learninggenie.common.data.mapper.dynamo.AgencyMetadataMapper;
import com.learninggenie.common.data.model.GroupEntity;
import com.learninggenie.common.data.model.*;
import com.learninggenie.common.data.model.classlink.GetTokenResponse;
import com.learninggenie.common.data.model.roster.ApiVerificationModel;
import com.learninggenie.common.data.model.roster.mhs.VerificationResult;
import com.learninggenie.common.data.model.roster.one.SyncSettingModel;
import com.learninggenie.common.data.repository.*;
import com.learninggenie.common.exception.LearningGenieRuntimeException;
import com.learninggenie.common.filesystem.FileSystem;
import com.learninggenie.common.messaging.EmailService;
import com.learninggenie.common.region.RegionService;
import com.learninggenie.common.report.AnalysisService;
import com.learninggenie.common.score.RatingService;
import com.learninggenie.common.sync.clever.CleverAppClient;
import com.learninggenie.common.sync.clever.CleverDataClient;
import com.learninggenie.common.sync.clever.data.*;
import com.learninggenie.common.utils.JsonUtil;
import com.learninggenie.common.utils.RestApiUtil;
import com.learninggenie.common.utils.StringUtil;
import com.learninggenie.common.utils.TimeUtil;
import com.learninggenie.common.utils.pdf.wk.WKHelper;
import org.apache.poi.ss.usermodel.Workbook;
import org.json.JSONObject;
import org.junit.Assert;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.function.Executable;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.stubbing.Answer;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static com.learninggenie.common.data.enums.ImportChildStatus.NORMAL;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ImportDataServiceImplTest {
    @InjectMocks
    ImportDataServiceImpl importDataService;

    @Mock
    private CommonService commonService;

    @Mock
    private UserProvider userProvider;

    @Mock
    private com.learninggenie.common.data.dao.contents.GroupDao contentsGroupDao;

    @Mock
    private ImportThirdAuthDao importThirdAuthDao;

    @Mock
    private CleverAppClient cleverAppClient;

    @Mock
    private CleverProviderImpl cleverProvider;

    @Mock
    private MyHeadStartProviderImpl myHeadStartProvider;

    @Mock
    private CacheService redisCacheService;

    @Mock
    private FileSystem fileSystem;

    @Mock
    private AgencyDao agencyDao;

    @Mock
    private CenterDao centerDao;

    @Mock
    private GroupDao groupDao;

    @Mock
    private StudentDao studentDao;

    @Mock
    private AnalysisService analysisService;

    @Mock
    private EmailService emailService;

    @Mock
    private ReportDao reportDao;

    @Mock
    private RemoteProvider remoteProvider;

    @Mock
    private OneRosterProviderImpl oneRosterProvider;

    @Mock
    private CacheService cacheService;

    @Mock
    private CenterMetadataRepository centerMetadataRepository;

    @Mock
    private UserDaoImpl userDao;

    @Mock
    private InvitationsEnrollmentInvitationDao invitationsEnrollmentInvitationDao;

    @Mock
    private UserRepository userRepository;

    @Mock
    private SyncParseDataRepository syncParseDataEntityRepository;

    @Mock
    private RegionService regionService;

    @Mock
    private EnrollmentMetadataRepository enrollmentMetadataRepository;

    @Mock
    private EnrollmentRepository enrollmentRepository;

    @Mock
    private ImportService importService;

    @Mock
    private PeriodsGroupDao periodsGroupDao;

    @Mock
    private PeriodService periodService;

    @Mock
    private RecordDao recordDao;

    @Mock
    private NoteDao noteDao;

    @Mock
    private PeriodProvider periodProvider;

    @Mock
    private RatingService ratingService;

    @Mock
    private ImportErrorDao importErrorDao;

    @Mock
    private UsersMetaDataDao usersMetaDataDao;

    @Mock
    private AgencyMetadataMapper agencyMetadataMapper;

    @Mock
    private  DotNetPasswordEncoder dotNetPasswordEncoder;

    @Mock
    private AccountService accountService;

    @Test
    void testMyHeadStart() {
        // 测试连接成功
        ImportHeadStartServiceRequest request = new ImportHeadStartServiceRequest();
        request.setAppShortName("Stage17");
        request.setSystemId("1");
        request.setTpsId("3001");
        request.setMasterPassword("_96AWyXkUIQ5mFyxLkkwdNiNTMvEKWmIBy9eKrpXQXhxRPDB1_zi5XyQemVkt0JR");

        ResultPojo resultPojo = new ResultPojo();
        resultPojo.setStatus(200);
        resultPojo.setData("{\"PDEPVersion\":\"6.0\",\"flag\":\"1\",\"UserId\":\"-1\",\"data\":[{\"APIRefID\":\"48\",\"records\":[{\"code\":\"1\",\"description\":\"English\"}],\"ReturnCode\":\"100\"}],\"ReturnCode\":\"100\"}");
        try (MockedStatic<RestApiUtil> restApiUtil = Mockito.mockStatic(RestApiUtil.class)) {
            restApiUtil.when(() -> RestApiUtil.get(Mockito.anyString(), Mockito.any(), Mockito.anyBoolean())).thenReturn(resultPojo);
//            ResultPojo result = importDataService.testMyHeadStart(request);
//            Assertions.assertEquals(200, result.getStatus());
        }

        // 测试连接失败（appShortName 错误）
        ImportHeadStartServiceRequest request2 = new ImportHeadStartServiceRequest();
        request2.setAppShortName("Stage171");
        request2.setSystemId("1");
        request2.setTpsId("3001");
        request2.setMasterPassword("_96AWyXkUIQ5mFyxLkkwdNiNTMvEKWmIBy9eKrpXQXhxRPDB1_zi5XyQemVkt0JR");

        ResultPojo resultPojo2 = new ResultPojo();
        resultPojo2.setStatus(200);
        resultPojo2.setData("{\"PDEPVersion\":\"6.0\",\"flag\":\"1\",\"ReturnCode\":\"4\"}");
        try (MockedStatic<RestApiUtil> restApiUtil = Mockito.mockStatic(RestApiUtil.class)) {
            restApiUtil.when(() -> RestApiUtil.get(Mockito.anyString(), Mockito.any(), Mockito.anyBoolean())).thenReturn(resultPojo2);
//            ResultPojo result = importDataService.testMyHeadStart(request2);
//            Assertions.assertAll(() -> Assertions.assertEquals(500, result.getStatus()),
//                    () -> Assertions.assertEquals("4", result.getErrorMsg()));
        }

        // 测试连接失败（systemId 错误）
        ImportHeadStartServiceRequest request3 = new ImportHeadStartServiceRequest();
        request3.setAppShortName("Stage17");
        request3.setSystemId("11");
        request3.setTpsId("3001");
        request3.setMasterPassword("_96AWyXkUIQ5mFyxLkkwdNiNTMvEKWmIBy9eKrpXQXhxRPDB1_zi5XyQemVkt0JR");

        ResultPojo resultPojo3 = new ResultPojo();
        resultPojo3.setStatus(200);
        resultPojo3.setData("{\"PDEPVersion\":\"6.0\",\"flag\":\"1\",\"ReturnCode\":\"5\"}");
        try (MockedStatic<RestApiUtil> restApiUtil = Mockito.mockStatic(RestApiUtil.class)) {
            restApiUtil.when(() -> RestApiUtil.get(Mockito.anyString(), Mockito.any(), Mockito.anyBoolean())).thenReturn(resultPojo3);
//            ResultPojo result = importDataService.testMyHeadStart(request3);
//            Assertions.assertAll(() -> Assertions.assertEquals(500, result.getStatus()),
//                    () -> Assertions.assertEquals("5", result.getErrorMsg()));
        }


        // 测试连接失败（TpsId 错误）
        ImportHeadStartServiceRequest request4 = new ImportHeadStartServiceRequest();
        request4.setAppShortName("Stage17");
        request4.setSystemId("1");
        request4.setTpsId("30011");
        request4.setMasterPassword("_96AWyXkUIQ5mFyxLkkwdNiNTMvEKWmIBy9eKrpXQXhxRPDB1_zi5XyQemVkt0JR");

        ResultPojo resultPojo4 = new ResultPojo();
        resultPojo4.setStatus(200);
        resultPojo4.setData("{\"PDEPVersion\":\"6.0\",\"flag\":\"1\",\"ReturnCode\":\"17\"}");
        try (MockedStatic<RestApiUtil> restApiUtil = Mockito.mockStatic(RestApiUtil.class)) {
            restApiUtil.when(() -> RestApiUtil.get(Mockito.anyString(), Mockito.any(), Mockito.anyBoolean())).thenReturn(resultPojo4);
//            ResultPojo result = importDataService.testMyHeadStart(request4);
//            Assertions.assertAll(() -> Assertions.assertEquals(500, result.getStatus()),
//                    () -> Assertions.assertEquals("17", result.getErrorMsg()));
        }

        // 测试连接失败（TpsId 错误）
        ImportHeadStartServiceRequest request5 = new ImportHeadStartServiceRequest();
        request5.setAppShortName("Stage17");
        request5.setSystemId("1");
        request5.setTpsId("3001");
        request5.setMasterPassword("_96AWyXkUIQ5mFyxLkkwdNiNTMvEKWmIBy9eKrpXQXhxRPDB1_zi5XyQemVkt0JR1");

        ResultPojo resultPojo5 = new ResultPojo();
        resultPojo5.setStatus(200);
        resultPojo5.setData("{\"PDEPVersion\":\"6.0\",\"flag\":\"1\",\"ReturnCode\":\"6\"}");
        try (MockedStatic<RestApiUtil> restApiUtil = Mockito.mockStatic(RestApiUtil.class)) {
            restApiUtil.when(() -> RestApiUtil.get(Mockito.anyString(), Mockito.any(), Mockito.anyBoolean())).thenReturn(resultPojo5);
//            ResultPojo result = importDataService.testMyHeadStart(request5);
//            Assertions.assertAll(() -> Assertions.assertEquals(500, result.getStatus()),
//                    () -> Assertions.assertEquals("6", result.getErrorMsg()));
        }
    }

    @Test
    public void testTestMyHeadStart_first() {
        // 请求信息
        ImportHeadStartServiceRequest request = new ImportHeadStartServiceRequest();
        request.setAppShortName("test");
        request.setTpsId("1");
        request.setSystemId("1");
        request.setMasterPassword("123");
        // 用户机构信息
        String agencyId = UUID.randomUUID().toString();
        AuthUserDetails user = new AuthUserDetails();
        user.setAgencyId(agencyId);
        when(userProvider.checkCurrentUser()).thenReturn(user);

        // 认证信息
        when(importThirdAuthDao.getThirdAuthByAgencyIdAndSystemTypeAndAuth(anyString(), anyString(), anyString())).thenReturn(null);

        // 验证结果
        VerificationResult verificationResult = new VerificationResult();
        when(myHeadStartProvider.verify(any(), anyBoolean(), anyBoolean(), anyBoolean())).thenReturn(verificationResult);

        // 执行方法
        TestConnectionResponse response = importDataService.testMyHeadStart(request);

        // 保存认证记录
        verify(importThirdAuthDao, times(1)).insertThirdAuth(any());
        // 缓存数据
        verify(myHeadStartProvider, times(1)).ready(anyString(), any());
        // 不会查询班级缓存信息
        verify(importThirdAuthDao, times(0)).getThirdAuthSchoolCache(anyString());
        // 验证结果状态
        assertEquals(VerificationStatus.GROUP_PENDING.toString(), response.getNextStep());
    }

    @Test
    public void testTestMyHeadStart_newUnavailable() {
        // 请求信息
        ImportHeadStartServiceRequest request = new ImportHeadStartServiceRequest();
        request.setAppShortName("test");
        request.setTpsId("1");
        request.setSystemId("1");
        request.setMasterPassword("123");
        // 用户机构信息
        String agencyId = UUID.randomUUID().toString();
        AuthUserDetails user = new AuthUserDetails();
        user.setAgencyId(agencyId);
        when(userProvider.checkCurrentUser()).thenReturn(user);

        // 认证信息
        ThirdAuthEntity auth = new ThirdAuthEntity();
        auth.setId(UUID.randomUUID().toString());
        auth.setExtendData(JsonUtil.toJson(request));
        when(importThirdAuthDao.getThirdAuthByAgencyIdAndSystemTypeAndAuth(anyString(), anyString(), anyString())).thenReturn(auth);

        // 验证结果
        VerificationResult verificationResult = new VerificationResult();
        verificationResult.setUnavailable(true);
        when(myHeadStartProvider.verify(any(), anyBoolean(), anyBoolean(), anyBoolean())).thenReturn(verificationResult);

        // 执行方法
        TestConnectionResponse response = importDataService.testMyHeadStart(request);

        // 不会新增认证记录
        verify(importThirdAuthDao, times(0)).insertThirdAuth(any());
        // 更新认证记录
        verify(importThirdAuthDao, times(1)).updateThirdAuth(any());
        // 缓存数据
        verify(myHeadStartProvider, times(0)).ready(anyString(), any());
        // 不会查询班级缓存信息
        verify(importThirdAuthDao, times(0)).getThirdAuthSchoolCache(anyString());
        // 验证结果状态
        assertEquals(VerificationStatus.API_PENDING.toString(), response.getNextStep());
    }

    @Test
    public void testTestMyHeadStart_newBaseError() {
        // 请求信息
        ImportHeadStartServiceRequest request = new ImportHeadStartServiceRequest();
        request.setAppShortName("test");
        request.setTpsId("1");
        request.setSystemId("1");
        request.setMasterPassword("123");
        // 用户机构信息
        String agencyId = UUID.randomUUID().toString();
        AuthUserDetails user = new AuthUserDetails();
        user.setAgencyId(agencyId);
        when(userProvider.checkCurrentUser()).thenReturn(user);

        // 认证信息
        ThirdAuthEntity auth = new ThirdAuthEntity();
        auth.setId(UUID.randomUUID().toString());
        auth.setExtendData(JsonUtil.toJson(request));
        when(importThirdAuthDao.getThirdAuthByAgencyIdAndSystemTypeAndAuth(anyString(), anyString(), anyString())).thenReturn(auth);

        // 验证结果
        VerificationResult verificationResult = new VerificationResult();
        verificationResult.setError(ErrorType.TPS_ID_ERROR);
        when(myHeadStartProvider.verify(any(), anyBoolean(), anyBoolean(), anyBoolean())).thenReturn(verificationResult);

        // 执行方法
        TestConnectionResponse response = importDataService.testMyHeadStart(request);

        // 不会新增认证记录
        verify(importThirdAuthDao, times(0)).insertThirdAuth(any());
        // 更新认证记录
        verify(importThirdAuthDao, times(1)).updateThirdAuth(any());
        // 缓存数据
        verify(myHeadStartProvider, times(0)).ready(anyString(), any());
        // 不会查询班级缓存信息
        verify(importThirdAuthDao, times(0)).getThirdAuthSchoolCache(anyString());
        // 验证结果状态
        assertEquals(VerificationStatus.BASE_VERIFICATION.toString(), response.getNextStep());
    }

    @Test
    public void testTestMyHeadStart_newApiError() {
        // 请求信息
        ImportHeadStartServiceRequest request = new ImportHeadStartServiceRequest();
        request.setAppShortName("test");
        request.setTpsId("1");
        request.setSystemId("1");
        request.setMasterPassword("123");
        // 用户机构信息
        String agencyId = UUID.randomUUID().toString();
        AuthUserDetails user = new AuthUserDetails();
        user.setAgencyId(agencyId);
        when(userProvider.checkCurrentUser()).thenReturn(user);

        // 认证信息
        ThirdAuthEntity auth = new ThirdAuthEntity();
        auth.setId(UUID.randomUUID().toString());
        auth.setExtendData(JsonUtil.toJson(request));
        when(importThirdAuthDao.getThirdAuthByAgencyIdAndSystemTypeAndAuth(anyString(), anyString(), anyString())).thenReturn(auth);

        // 验证结果
        VerificationResult verificationResult = new VerificationResult();
        List<ApiVerificationModel> verificationModelList = new ArrayList<>();
        verificationModelList.add(new ApiVerificationModel(MHSApiCode.AGENCY.getCode(), MHSApiCode.AGENCY.getDesc(), false));
        verificationModelList.add(new ApiVerificationModel(MHSApiCode.CHILD.getCode(), MHSApiCode.CHILD.getDesc(), true));
        verificationModelList.add(new ApiVerificationModel(MHSApiCode.CLASS.getCode(), MHSApiCode.CLASS.getDesc(), false));
        verificationResult.setApiVerificationResults(verificationModelList);
        when(myHeadStartProvider.verify(any(), anyBoolean(), anyBoolean(), anyBoolean())).thenReturn(verificationResult);

        // 执行方法
        TestConnectionResponse response = importDataService.testMyHeadStart(request);

        // 不会新增认证记录
        verify(importThirdAuthDao, times(0)).insertThirdAuth(any());
        // 更新认证记录
        verify(importThirdAuthDao, times(1)).updateThirdAuth(any());
        // 缓存数据
        verify(myHeadStartProvider, times(0)).ready(anyString(), any());
        // 不会查询班级缓存信息
        verify(importThirdAuthDao, times(0)).getThirdAuthSchoolCache(anyString());
        // 验证结果状态
        assertEquals(VerificationStatus.API_VERIFICATION.toString(), response.getNextStep());
        assertEquals(3, response.getApiVerificationResults().size());
    }

    @Test
    public void testGetMyHeadStartSetting_noData() {
        // 用户信息
        String userId = UUID.randomUUID().toString();
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        // 机构信息
        AgencyModel agency = new AgencyModel();
        agency.setId(UUID.randomUUID().toString());
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agency);

        // 查询认证信息
        when(importThirdAuthDao.getThirdAuthByAgencyIdAndSystemTypeAndAuth(anyString(), anyString(), anyString())).thenReturn(null);

        // 执行方法
        MyHeadStartSettingModel myHeadStartSetting = importDataService.getMyHeadStartSetting();

        // 验证结果
        assertEquals(ThirdSyncAuthStatus.NOT_READY.toString(), myHeadStartSetting.getStatus());
        assertEquals(VerificationStatus.NEW.toString(), myHeadStartSetting.getVerificationStatus());
    }

    @Test
    public void testGetMyHeadStartSetting_hasDataNoVerificationStatus() {
        // 用户信息
        String userId = UUID.randomUUID().toString();
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        // 机构信息
        AgencyModel agency = new AgencyModel();
        agency.setId(UUID.randomUUID().toString());
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agency);

        // 查询认证信息
        ThirdAuthEntity thirdAuthEntity = new ThirdAuthEntity();
        thirdAuthEntity.setStatus(ThirdSyncAuthStatus.SUCCESS.toString());
        thirdAuthEntity.setExtendData(JsonUtil.toJson(new MyHeadStartSettingModel()));
        when(importThirdAuthDao.getThirdAuthByAgencyIdAndSystemTypeAndAuth(anyString(), anyString(), anyString())).thenReturn(thirdAuthEntity);

        // 执行方法
        MyHeadStartSettingModel myHeadStartSetting = importDataService.getMyHeadStartSetting();

        // 验证结果
        assertEquals(ThirdSyncAuthStatus.SUCCESS.toString(), myHeadStartSetting.getStatus());
        assertEquals(VerificationStatus.COMPLETE.toString(), myHeadStartSetting.getVerificationStatus());
    }

    @Test
    public void testGenerateErrorMsgExcel() throws IOException {
        // 用户信息
        UserEntity user = new UserEntity();
        String userId = UUID.randomUUID().toString();
        user.setRole(UserRole.AGENCY_OWNER.toString());
        user.setId(userId);
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.checkUser(userId)).thenReturn(user);
        // 机构信息
        AgencyModel agency = new AgencyModel();
        agency.setId(UUID.randomUUID().toString());
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agency);

        // 插入 redis 数据
        String errorMsg = "[{\n" +
                "            \"centerName\": \"import_error_site_too_long*&erewarwer男\", \n" +
                "            \"className\": \"import1_error_class_too_long$%#@长班级名\", \n" +
                "            \"childName\": \"error57 57\", \n" +
                "            \"errors\": [\n" +
                "                \"Unsupported date format. 2021 11 03\"\n" +
                "            ], \n" +
                "            \"row\": 58, \n" +
                "            \"columns\": [\n" +
                "                11\n" +
                "            ]\n" +
                "        }]";
        CacheModel errorMsgModel = new CacheModel();
        errorMsgModel.setValue(errorMsg);
        when(redisCacheService.get(anyString())).thenReturn(errorMsgModel);

        //
        when(fileSystem.uploadExcel(any(Workbook.class), anyString())).thenReturn(UUID.randomUUID().toString());
        // 执行方法
        DownFileResponse excel = importDataService.generateErrorMsgExcel();

        // 验证结果
        Assertions.assertNotNull(excel.getUrl());
    }

    @Test
    public void testParseOtherData_v2() throws Exception {
        // 用户信息
        UserEntity user = new UserEntity();
        String userId = UUID.randomUUID().toString();
        user.setRole(UserRole.AGENCY_OWNER.toString());
        user.setId(userId);
        when(userProvider.checkUser(userId)).thenReturn(user);
        // 机构信息
        AgencyModel agency = new AgencyModel();
        agency.setId(UUID.randomUUID().toString());
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agency);

        // 对数据进行校验
        ParseOtherDataRequest request = new ParseOtherDataRequest();
        request.setSettingSftp(true);

        // 封装 mappedColumn
        MappedColumn mappedColumn = new MappedColumn("First Name", "ChildName (First)", 0);
        MappedColumn mappedColumn1 = new MappedColumn("Middle Name", "ChildName (Middle)", 1);
        MappedColumn mappedColumn2 = new MappedColumn("Last Name", "ChildName (Last)", 2);
        MappedColumn mappedColumn3 = new MappedColumn("Date of Birth", "Birthday", 3);
        MappedColumn mappedColumn4 = new MappedColumn("Class Name", "Class Name", 4);
        MappedColumn mappedColumn5 = new MappedColumn("Center Name", "Site Name", 5);
        MappedColumn mappedColumn6 = new MappedColumn("Site ID", "Site Id", 6);
        MappedColumn mappedColumn7 = new MappedColumn("Gender", "Gender", 7);
        MappedColumn mappedColumn8 = new MappedColumn("Enrollment Date", "Entry Date", 8);
        MappedColumn mappedColumn9 = new MappedColumn("Parent1 Name", "Primary Adult First Name", 10);
        MappedColumn mappedColumn10 = new MappedColumn("Parent1 Email", "Primary Adult Email", 11);
        MappedColumn mappedColumn11 = new MappedColumn("Statewide Student Identifier", "Statewide Student Identifier", 14);
        MappedColumn mappedColumn12 = new MappedColumn("External ID", "External ID", 15);
        MappedColumn mappedColumn13 = new MappedColumn("Hispanic", "Hispanic", 16);
        MappedColumn mappedColumn14 = new MappedColumn("Race", "Race", 17);
        MappedColumn mappedColumn15 = new MappedColumn("Language", "Language", 18);
        MappedColumn mappedColumn16 = new MappedColumn("ELD", "ELD", 19);
        MappedColumn mappedColumn17 = new MappedColumn("Program Name", "Program Name", 20);
        MappedColumn mappedColumn18 = new MappedColumn("IEP/IFSP", "IEP/IFSP", 21);
        MappedColumn mappedColumn19 = new MappedColumn("Teacher First Name", "Teacher First Name", 22);
        MappedColumn mappedColumn20 = new MappedColumn("Teacher Last Name", "Teacher Last Name", 23);
        MappedColumn mappedColumn21 = new MappedColumn("Teacher ID", "Teacher ID", 24);
        MappedColumn mappedColumn22 = new MappedColumn("Teacher Email", "Teacher Email", 25);

        List<MappedColumn> mappedColumns = new ArrayList<>();
        mappedColumns.add(mappedColumn);
        mappedColumns.add(mappedColumn1);
        mappedColumns.add(mappedColumn2);
        mappedColumns.add(mappedColumn3);
        mappedColumns.add(mappedColumn4);
        mappedColumns.add(mappedColumn5);
        mappedColumns.add(mappedColumn6);
        mappedColumns.add(mappedColumn7);
        mappedColumns.add(mappedColumn8);
        mappedColumns.add(mappedColumn9);
        mappedColumns.add(mappedColumn10);
        mappedColumns.add(mappedColumn11);
        mappedColumns.add(mappedColumn12);
        mappedColumns.add(mappedColumn13);
        mappedColumns.add(mappedColumn14);
        mappedColumns.add(mappedColumn15);
        mappedColumns.add(mappedColumn16);
        mappedColumns.add(mappedColumn17);
        mappedColumns.add(mappedColumn18);
        mappedColumns.add(mappedColumn19);
        mappedColumns.add(mappedColumn20);
        mappedColumns.add(mappedColumn21);
        mappedColumns.add(mappedColumn22);
        // 准备数据
        String data = "[{\"name\":\"Galaxy Kids\",\"groups\":[{\"name\":\"1B\",\"childs\":[{\"lastName\":\"Emoto\",\"parents\":[],\"middleName\":\"Ian\",\"row\":1,\"firstName\":\"Mark\",\"birthDate\":\"08/14/2015\",\"gender\":\"Male\",\"enrollmentDate\":\"08/10/2020\",\"customizedData\":{\"Statewide Student Identifier\":[\"1234567890\",\"1234567890\",\"1234567890\",\"1234567890\",\"1234567890\"],\"External ID\":[\"1234567890\",\"1234567890\",\"1234567890\",\"1234567890\",\"1234567890\"],\"Hispanic\":[\"No\",\"No\",\"No\",\"No\",\"No\"],\"Race\":[\"Japanese\",\"Japanese\",\"Japanese\",\"Japanese\",\"Japanese\"],\"Language\":[\"English\",\"English\",\"English\",\"English\",\"English\"],\"ELD\":[\"Yes\",\"Yes\",\"Yes\",\"Yes\",\"Yes\"],\"Program Name\":[\"Full day Kindergarten/Transitional Kindergarten\",\"Full day Kindergarten/Transitional Kindergarten\",\"Full day Kindergarten/Transitional Kindergarten\",\"Full day Kindergarten/Transitional Kindergarten\",\"Full day Kindergarten/Transitional Kindergarten\"],\"IEP/IFSP\":[\"Yes\",\"Yes\",\"Yes\",\"Yes\",\"Yes\"],\"Teacher\":[\"<EMAIL>\"]}},{\"lastName\":\"Marks\",\"parents\":[],\"middleName\":\"Joy\",\"row\":3,\"firstName\":\"Sophia\",\"birthDate\":\"11/15/2015\",\"gender\":\"Female\",\"enrollmentDate\":\"08/10/2020\",\"customizedData\":{\"Statewide Student Identifier\":[\"\",\"\",\"\",\"\",\"\"],\"External ID\":[\"\",\"\",\"\",\"\",\"\"],\"Hispanic\":[\"No\",\"No\",\"No\",\"No\",\"No\"],\"Race\":[\"White\",\"White\",\"White\",\"White\",\"White\"],\"Language\":[\"English\",\"English\",\"English\",\"English\",\"English\"],\"ELD\":[\"No\",\"No\",\"No\",\"No\",\"No\"],\"Program Name\":[\"Full day Kindergarten/Transitional Kindergarten\",\"Full day Kindergarten/Transitional Kindergarten\",\"Full day Kindergarten/Transitional Kindergarten\",\"Full day Kindergarten/Transitional Kindergarten\",\"Full day Kindergarten/Transitional Kindergarten\"],\"IEP/IFSP\":[\"No\",\"No\",\"No\",\"No\",\"No\"],\"Teacher\":[\"<EMAIL>\"]}}],\"teachers\":[{\"firstName\":\"Jane \",\"lastName\":\"Cooper\",\"email\":\"<EMAIL>\",\"sourcedId\":\"1234\"},{\"firstName\":\"Robert \",\"lastName\":\"Fox\",\"email\":\"<EMAIL>\",\"sourcedId\":\"1234\"}]},{\"name\":\"1A\",\"childs\":[{\"lastName\":\"Lee\",\"parents\":[{\"primaryAdultFirstName\":\"Irene  Lee\",\"primaryAdultEmail\":\"<EMAIL>\"}],\"middleName\":\"\",\"row\":2,\"firstName\":\"Rosa\",\"birthDate\":\"12/31/2016\",\"gender\":\"Male\",\"enrollmentDate\":\"08/10/2020\",\"customizedData\":{\"Statewide Student Identifier\":[\"\",\"\",\"\",\"\",\"\"],\"External ID\":[\"\",\"\",\"\",\"\",\"\"],\"Hispanic\":[\"No\",\"No\",\"No\",\"No\",\"No\"],\"Race\":[\"Korean\",\"Korean\",\"Korean\",\"Korean\",\"Korean\"],\"Language\":[\"English\",\"English\",\"English\",\"English\",\"English\"],\"ELD\":[\"No\",\"No\",\"No\",\"No\",\"No\"],\"Program Name\":[\"Head Start\",\"Head Start\",\"Head Start\",\"Head Start\",\"Head Start\"],\"IEP/IFSP\":[\"Yes\",\"Yes\",\"Yes\",\"Yes\",\"Yes\"],\"Teacher\":[\"<EMAIL>\"]}},{\"lastName\":\"Martinez\",\"parents\":[],\"middleName\":\"Alexa\",\"row\":4,\"firstName\":\"Wendy\",\"birthDate\":\"09/15/2015\",\"gender\":\"Female\",\"enrollmentDate\":\"09/15/2020\",\"customizedData\":{\"Statewide Student Identifier\":[\"\",\"\",\"\",\"\",\"\"],\"External ID\":[\"\",\"\",\"\",\"\",\"\"],\"Hispanic\":[\"Yes\",\"Yes\",\"Yes\",\"Yes\",\"Yes\"],\"Race\":[\"Other\",\"Other\",\"Other\",\"Other\",\"Other\"],\"Language\":[\"English\",\"English\",\"English\",\"English\",\"English\"],\"ELD\":[\"Yes\",\"Yes\",\"Yes\",\"Yes\",\"Yes\"],\"Program Name\":[\"Head Start\",\"Head Start\",\"Head Start\",\"Head Start\",\"Head Start\"],\"IEP/IFSP\":[\"Yes\",\"Yes\",\"Yes\",\"Yes\",\"Yes\"],\"Teacher\":[\"<EMAIL>\"]}},{\"lastName\":\"Wang\",\"parents\":[{\"primaryAdultEmail\":\"<EMAIL>\"}],\"middleName\":\"\",\"row\":5,\"firstName\":\"Jackson\",\"birthDate\":\"05/10/2016\",\"gender\":\"Male\",\"enrollmentDate\":\"08/10/2020\",\"customizedData\":{\"Statewide Student Identifier\":[\"\",\"\",\"\",\"\",\"\"],\"External ID\":[\"\",\"\",\"\",\"\",\"\"],\"Hispanic\":[\"No\",\"No\",\"No\",\"No\",\"No\"],\"Race\":[\"Chinese\",\"Chinese\",\"Chinese\",\"Chinese\",\"Chinese\"],\"Language\":[\"English\",\"English\",\"English\",\"English\",\"English\"],\"ELD\":[\"No\",\"No\",\"No\",\"No\",\"No\"],\"Program Name\":[\"Head Start\",\"Head Start\",\"Head Start\",\"Head Start\",\"Head Start\"],\"IEP/IFSP\":[\"No\",\"No\",\"No\",\"No\",\"No\"],\"Teacher\":[\"<EMAIL>\"]}}],\"teachers\":[{\"firstName\":\"Leslie \",\"lastName\":\"Alexander\",\"email\":\"<EMAIL>\",\"sourcedId\":\"1234\"},{\"firstName\":\"Jacob \",\"lastName\":\"Jones\",\"email\":\"<EMAIL>\",\"sourcedId\":\"1234\"},{\"firstName\":\"Albert \",\"lastName\":\"Flores\",\"email\":\"<EMAIL>\",\"sourcedId\":\"1234\"}]}],\"sourcedId\":\"123\"}]";
        List<Center> centers = JsonUtil.fromJsonArray(data, new TypeToken<ArrayList<Center>>() {
        }.getType());
        request.setCenters(centers);
        request.setMappedColumn(mappedColumns);

        // 其他数据
        String timeZone = "Asia/Shanghai", importId = UUID.randomUUID().toString();
        // 设置 CenterEntity
        CenterEntity center1 = new CenterEntity();
        // 为 CenterEntity 设置 Id
        center1.setId("c001");
        // 为 CenterEntity 设置 CenterName
        center1.setName("c001");
        // 另 agency id 获取 agency 中的 center
        List<CenterEntity> centerEntityList = new ArrayList<>();
        centerEntityList.add(center1);
        // 模拟获取机构下的所有 center
        when(centerDao.getAllByAgencyId(agency.getId())).thenReturn(centerEntityList);

        // 设置学校的 centerMetaData
        List<CenterMetaDataEntity> centerMetaDataList = new ArrayList<>();
        // 定义 CenterMetaDataEntity
        CenterMetaDataEntity centerMetaDataEntity = new CenterMetaDataEntity();
        // 设置 CenterMetaDataEntity 的 metaKey
        centerMetaDataEntity.setMetaKey(CenterMetaKey.SOURCE_ID.toString());
        // 设置 CenterMetaDataEntity 的 metaValue
        centerMetaDataEntity.setMetaValue("123");
        centerMetaDataEntity.setCenter(center1);

        centerMetaDataList.add(centerMetaDataEntity);
        // mock 获取学校的 centerMetaData
        when(centerDao.getMeta(anyList(), anyString())).thenReturn(centerMetaDataList);
        ArrayList<UserModel> userModels = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            // 定义 UserModel
            UserModel model = new UserModel();
            // 设置 UserModel 的 id
            model.setId(i + "234");
            // 设置 UserModel 的 source
            model.setMetaValue(i + "234");
            // 设置 UserModel 的 email
            model.setEmail("teacher" + i + "@mail.com");
            // 设置 UserModel 的 firstName
            model.setFirstName("teacher" + i);
            // 设置 UserModel 的 lastName
            model.setLastName("teacher" + i);
            // 设置 UserModel 的 agencyId
            model.setAgencyId(agency.getId());
            // 将 userModel 添加进入 机构老师集合中
            userModels.add(model);
        }
        when(userDao.getTeacherWithMetaByAgency(anyString(), anyString())).thenReturn(userModels);

        // 调用方法
        ImportDataViewModel importDataViewModel = importDataService.parseOtherData_V2(request, timeZone, userId, importId);
        Assert.assertNotNull(importDataViewModel.getErrors());
    }

    @Test
    void testSaveOneRosterSyncGroups_repeatStudent() throws IOException {
        // 存在重复小孩
//        SyncSettingModel syncSettingModel = new SyncSettingModel();
//        when(userProvider.getCurrentUserId()).thenReturn("123");
//        importDataService.saveOneRosterSyncGroups(syncSettingModel, AuthTypeEnum.OAUTH2.toString());

        // 不存在重复小孩
    }

    /**
     * 测试更新导入状态出现异常的情况
     */
    @Test
    public void testUpdateImportStatusWithSendEmailException() {
        String userId = "user"; // 用户 ID
        Status status = new Status(); // 状态

        // 机构信息
        String agencyId = "agency"; // 机构 ID
        AgencyModel agency = new AgencyModel();
        agency.setId(agencyId);
        // 模拟机构信息
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);

        // 导入数据
        AgencyMetaDataEntity importMetadata = new AgencyMetaDataEntity();
        importMetadata.setMetaValue("{}");
        // 模拟导入数据信息
        when(agencyDao.getMeta(agencyId, AgencyMetaKey.AGENCY_IMPORT.toString())).thenReturn(importMetadata);

        // 导入通知邮箱
        AgencyMetaDataEntity importEmailMetadata = new AgencyMetaDataEntity();
        importEmailMetadata.setMetaValue("<EMAIL>");
        // 模拟导入通知邮箱数据
        when(agencyDao.getMeta(agencyId, AgencyMetaKey.IMPORT_EMAIL.toString())).thenReturn(importEmailMetadata);

        // 执行方法
        importDataService.updateImportStatus(userId, status);

        // 解析出现异常，不能继续执行
        verify(agencyDao, times(0)).getMeta(agencyId, AgencyMetaKey.IMPORT_ERROR.toString());
    }

    /**
     * 测试验证 ClassLink 导入认证信息，没有错误的清空
     */
    @Test
    public void testVerifyImportAuthClassLinkWithoutError() {
        // 要测试的机构 ID 列表
        List<String> agencyIds = new ArrayList<>();
        agencyIds.add("agency-001");
        // 模拟获取开通同步的机构
        when(importThirdAuthDao.getSyncAgencyIds()).thenReturn(agencyIds);

        // 认证信息
        ThirdAuthEntity auth = new ThirdAuthEntity();
        auth.setSystemType(SystemTypeEnum.CLASS_LINK.toString());
        when(importThirdAuthDao.getCurrentActiveThirdByAgencyId(any())).thenReturn(auth);

        // 当前认证结果
        VerificationResult verificationResult = new VerificationResult();
        when(oneRosterProvider.verify(any(), anyBoolean(), anyBoolean(), anyBoolean())).thenReturn(verificationResult);

        // 执行
        importDataService.verifyImportAuth();
    }

    /**
     * 测试验证 ClassLink 导入认证信息，有错误的情况
     */
    @Test
    public void testVerifyImportAuthClassLinkWithError() {
        // 要测试的机构 ID 列表
        List<String> agencyIds = new ArrayList<>();
        agencyIds.add("agency-002");
        // 模拟获取开通同步的机构
        when(importThirdAuthDao.getSyncAgencyIds()).thenReturn(agencyIds);

        // 认证信息
        ThirdAuthEntity auth = new ThirdAuthEntity();
        auth.setSystemType(SystemTypeEnum.CLASS_LINK.toString());
        when(importThirdAuthDao.getCurrentActiveThirdByAgencyId(any())).thenReturn(auth);

        // 当前认证结果
        VerificationResult verificationResult = new VerificationResult();
        verificationResult.setAuthError(true); // 认证信息错误
        when(oneRosterProvider.verify(any(), anyBoolean(), anyBoolean(), anyBoolean())).thenReturn(verificationResult);

        // 执行
        importDataService.verifyImportAuth();
    }

    /**
     * 测试获取 ClassLink 同步预览 PDF，没有学生的情况
     */
    @Test
    public void testGetClassLinkCheckGroupPdfNoChildren() {
        // 请求信息
        ImportSyncCenterGroupRequest request = new ImportSyncCenterGroupRequest();
        // 当前用户 ID
        String userId = UUID.randomUUID().toString();
        // 模拟获取当前用户 ID
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        // 用户所在机构
        AgencyModel agency = new AgencyModel();
        agency.setId(UUID.randomUUID().toString());
        // 模拟获取用户所在机构
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        // 同步认证信息
        ThirdAuthEntity thirdAuthEntity = new ThirdAuthEntity();
        thirdAuthEntity.setSystemType(SystemTypeEnum.CLASS_LINK.toString());
        // 模拟获取同步认证信息
        when(importThirdAuthDao.getThirdAuthByAgencyIdAndSystemTypeAndAuth(any(), any(), any())).thenReturn(thirdAuthEntity);
        // 导入数据
        ImportStructure importStructure = new ImportStructure();
        // 模拟获取导入数据
        when(oneRosterProvider.mapObjectId(any(), any(), anyBoolean(), anyBoolean())).thenReturn(importStructure);
        when(oneRosterProvider.getRosterData(any(), any(), anyBoolean())).thenReturn(importStructure);

        // 执行
        BusinessException exception = Assertions.assertThrows(BusinessException.class, () -> {
            importDataService.getClassLinkCheckGroupPdf(request);
        });
        // 验证异常
        assertEquals(ErrorCode.NOT_FOUND_CHILD_DATA, exception.getErrorCode());
    }

    /**
     * 测试获取 ClassLink 同步预览 PDF，认证的情况
     */
    @Test
    public void testGetClassLinkCheckGroupPdfAuthFailed() {
        // 请求信息
        ImportSyncCenterGroupRequest request = new ImportSyncCenterGroupRequest();
        // 当前用户 ID
        String userId = UUID.randomUUID().toString();
        // 模拟获取当前用户 ID
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        // 用户所在机构
        AgencyModel agency = new AgencyModel();
        agency.setId(UUID.randomUUID().toString());
        // 模拟获取用户所在机构
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        // 同步认证信息
        ThirdAuthEntity thirdAuthEntity = new ThirdAuthEntity();
        thirdAuthEntity.setSystemType(SystemTypeEnum.CLASS_LINK.toString());
        // 模拟获取同步认证信息
        when(importThirdAuthDao.getThirdAuthByAgencyIdAndSystemTypeAndAuth(any(), any(), any())).thenReturn(thirdAuthEntity);
        // 导入数据
        ImportStructure importStructure = new ImportStructure();
        // 模拟获取导入数据
        when(oneRosterProvider.mapObjectId(any(), any(), anyBoolean(), anyBoolean())).thenThrow(new LearningGenieRuntimeException("Auth Failed!"));

        // 执行
        BusinessException exception = Assertions.assertThrows(BusinessException.class, () -> {
            importDataService.getClassLinkCheckGroupPdf(request);
        });
        // 验证异常
        assertEquals(ErrorCode.UNAUTHORIZED, exception.getErrorCode());
    }

    /**
     * 获取导入数据，没有数据的情况
     */
    @Test
    public void testGetImportDataViewModelNoData() {
        // 用户信息
        UserEntity user = new UserEntity();
        // 机构信息
        AgencyModel agency = new AgencyModel();
        // 导入数据
        ImportStructure parse = new ImportStructure();
        // 导入资源类型
        String resource = SystemTypeEnum.CLASS_LINK.toString();
        // 导入资源
        ImportResource importResource = ImportResource.ONE_ROSTER;

        // 执行
        ImportDataViewModel ret = importDataService.getImportDataViewModel(user, agency, parse, null, resource, importResource);

        // 验证
        assertEquals(0, ret.getCenters().size());
    }

    /**
     * 测试获取 ClassLink 配置信息，包含验证结果
     */
    @Test
    public void testGetClassLinkSettingWithVerificationResult() {
        // 验证结果
        String verificationStatus = VerificationStatus.COMPLETE.toString();
        // 当前用户 ID
        String userId = UUID.randomUUID().toString();
        // 模拟获取当前用户 ID
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        // 用户所在机构
        AgencyModel agency = new AgencyModel();
        agency.setId(UUID.randomUUID().toString());
        // 模拟获取用户所在机构
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        // 同步认证信息
        ThirdAuthEntity thirdAuthEntity = new ThirdAuthEntity();
        thirdAuthEntity.setSystemType(SystemTypeEnum.CLASS_LINK.toString()); // 系统类型
        thirdAuthEntity.setEmail("<EMAIL>"); // 邮箱
        thirdAuthEntity.setVerificationStatus(verificationStatus); // 认证状态
        thirdAuthEntity.setVerificationResult("{}"); // 认证结果
        thirdAuthEntity.setExtendData("{\"consumerKey\":\"4254bbb4509da6b4b9c870fd\",\"consumerSecret\":\"1c09cb8dde2311c510fd4c20\"}"); // 认证信息
        // 模拟获取同步认证信息
        when(importThirdAuthDao.getThirdAuthByAgencyIdAndSystemTypeAndAuth(any(), any(), any())).thenReturn(thirdAuthEntity);

        // 执行
        OneRosterSettingResponse classLinkSetting = importDataService.getClassLinkSetting();

        // 验证
        Assertions.assertNotNull(classLinkSetting.getImportOAuth1Info());
        assertEquals(verificationStatus, classLinkSetting.getImportOAuth1Info().getVerificationStatus());
    }

    /**
     * 测试保存 ClassLink 设置，认证失败的情况
     */
    @Test
    public void testSaveClassLinkSyncGroupsWithAuthError() {
        // 同步设置信息
        SyncSettingModel request = new SyncSettingModel();
        request.setSelectCenterIds(Collections.singletonList(UUID.randomUUID().toString())); // 选择的学校 ID
        request.setSelectGroupIds(Collections.singletonList(UUID.randomUUID().toString())); // 选择的班级 ID
        // 认证类型
        String authType = AuthTypeEnum.OAUTH1.toString();
        // API 异常
        when(userProvider.getCurrentUserId()).thenThrow(new LearningGenieRuntimeException(""));

        // 执行
        BusinessException exception = Assertions.assertThrows(BusinessException.class,
                () -> importDataService.saveClassLinkSyncGroups(request, authType));
        // 验证异常
        assertEquals(ErrorCode.UNAUTHORIZED, exception.getErrorCode());
    }

    /**
     * 测试 OneRoster 获取新班级提醒，同步类型为非 OneRoster 的情况
     */
    @Test
    public void testGetOneRosterNewGroupTips() {
        // 当前用户 ID
        String userId = UUID.randomUUID().toString();
        // 模拟获取当前用户 ID
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        // 用户所在机构
        AgencyModel agency = new AgencyModel();
        agency.setId(UUID.randomUUID().toString());
        // 模拟获取用户所在机构
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        // 是否开启 API 同步 Metadata
        AgencyMetaDataEntity apiSyncMetadata = new AgencyMetaDataEntity();
        apiSyncMetadata.setMetaValue("true"); // 开启状态
        when(agencyDao.getMeta(agency.getId(), AgencyMetaKey.API_SYNC_SERVICE.toString())).thenReturn(apiSyncMetadata);
        // 启用服务 Metadata
        AgencyMetaDataEntity enableServiceMetadata = new AgencyMetaDataEntity();
        enableServiceMetadata.setMetaValue(SystemTypeEnum.MY_HEAD_START.toString());
        when(agencyDao.getMeta(agency.getId(), AgencyMetaKey.ENABLED_SYNC_SERVICE.toString())).thenReturn(enableServiceMetadata);
        // 执行
        importDataService.getOneRosterNewGroupTips();
    }

    /**
     * 测试 ClassLink 连接成功的情况
     */
    @Test
    public void testTestClassLinkOAuth1Success() {
        ImportOneRosterOAuth1Request request = new ImportOneRosterOAuth1Request();
        request.setApiUrl("test-classlink-oauth1-api-url"); // API 地址
        request.setConsumerKey("test-classlink-oauth1-consumer-key"); // API Key
        request.setConsumerSecret("test-classlink-oauth1-consumer-secret"); // API Secret
        request.setEmails(new ArrayList<>()); // 接收邮箱列表
        // 当前用户信息
        AuthUserDetails user = new AuthUserDetails();
        user.setEmail("<EMAIL>"); // 邮箱
        user.setAgencyId(UUID.randomUUID().toString()); // 机构 ID
        when(userProvider.checkCurrentUser()).thenReturn(user);
        // 模拟认证信息，第一次认证为空
        when(importThirdAuthDao.getThirdAuthByAgencyIdAndSystemTypeAndAuth(any(), any(), any())).thenReturn(null);
        // 验证结果，没有异常
        VerificationResult verificationResult = new VerificationResult();
        when(oneRosterProvider.verify(any(), anyBoolean(), anyBoolean(), anyBoolean())).thenReturn(verificationResult);
        // 执行
        ImportTestConnectionResponse response = importDataService.testClassLinkOAuth1(request);
        // 验证
        assertEquals(200, response.getStatusCode());
    }

    /**
     * 测试获取 ClassLink 设置，认证失败的情况
     */
    @Test
    public void testGetClassLinkSettingSyncDataWithAuthError() {
        // 模拟异常
        when(userProvider.getCurrentUserId()).thenThrow(new LearningGenieRuntimeException(""));
        // 执行
        BusinessException exception = Assertions.assertThrows(BusinessException.class, () -> {
            importDataService.getClassLinkSettingSyncData(AuthTypeEnum.OAUTH1.toString());
        });
        // 验证异常
        assertEquals(ErrorCode.UNAUTHORIZED, exception.getErrorCode());
    }

    /**
     * 测试初次导入 childPlus 数据
     */
    @Test
    public void testParseChildPlusData() {
        // 模拟数据
        // 模拟 stageId
        ReflectionTestUtils.setField(importDataService, "stageId", "72516154-3B50-E411-837D-02DBFC8648CE");
        ReflectionTestUtils.setField(importDataService, "mediaServer", "media.server");
        // 用户名字
        String userName = "test-check-import-data-view-model-user-name";
        // 导入内容
        String content = "QWdlbmN5IE5hbWUsU2l0ZSBOYW1lLENsYXNzIE5hbWUsQ2hpbGROYW1lIChMYXN0KSxDaGlsZE5hbWUgKEZpcnN0KSxCaXJ0aGRheSxDaGlsZFBsdXMgSUQsSGlzcGFuaWMsR2VuZGVyIENvZGUsUmFjZSxSYWNlIEFtZXJpY2FuIEluZGlhbiBPciBBbGFza2FuIE5hdGl2ZSxSYWNlIEFzaWFuLFJhY2UgQmxhY2sgb3IgQWZyaWNhbiBBbWVyaWNhbiwsUmFjZSBIaXNwYW5pYyxSYWNlIE11bHRpLVJhY2lhbCxSYWNlIE5hdGl2ZSBIYXdhaWlhbiBvciBPdGhlciBQYWNpZmljIElzbGFuZGVyLCxSYWNlIE90aGVyLFJhY2UgT3RoZXIgRGVzY3JpcHRpb24sUmFjZSBXaGl0ZSxFbnJvbGxtZW50IFN0YXR1cyxFbnJvbGxlZCBEYXRlLElFUCBEaXNhYmlsaXR5IENvZGUgRGVzY3JpcHRpb24sUHJvZ3JhbSBOYW1lLFBhcnRpY2lwYXRpb25ZZWFyLFByaW1hcnkgTGFuZ3VhZ2UsSG9tZSBMYW5ndWFnZSxQcmltYXJ5IEFkdWx0IEZpcnN0IE5hbWUsUHJpbWFyeSBBZHVsdCBMYXN0IE5hbWUsUHJpbWFyeSBBZHVsdCBFbWFpbCxTZWNvbmRhcnkgQWR1bHQgRmlyc3QgTmFtZSxTZWNvbmRhcnkgQWR1bHQgTGFzdCBOYW1lLFNlY29uZGFyeSBBZHVsdCBFbWFpbA0KTGVhcm5pbmcgR2VuaWUgQWNhZGVteSxDYXJsc2JhZCxIUyBQTSAxLFN0dWRlbnQsT25lLDExLzE5LzIwMTUsMzcyNzIyLE5vLE0sT3RoZXIsMCwwLDAsLDAsMCwwLCwxLEhpc3BhbmljLDAsRW5yb2xsZWQsOC85LzIwMTcsLEhlYWQgU3RhcnQsMSwsRW5nbGlzaCwsLCwsLA0K";
        // timeZone
        String timeZone = "Asia/Shanghai";
        // 用户 Id
        String userId = UUID.randomUUID().toString();
        // 导入类型
        String resource = "childPlus";
        // 模拟机构数据
        AgencyModel agency = new AgencyModel();
        String agencyId = UUID.randomUUID().toString();
        agency.setId(agencyId);
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agency);
        // 模拟注册日期
        boolean needEntryDate = true;
        when(userProvider.needEntryDate(anyString())).thenReturn(needEntryDate);
        // 模拟缓存数据
        CacheModel cacheModel = new CacheModel();
        cacheModel.setKey("test-check-import-data-view-model-cache-key");
        when(cacheService.create(anyString(), anyInt())).thenReturn(cacheModel);
        // 模拟 CenterMetaDataEntity 数据
        when(centerMetadataRepository.findTop1ByMetaKeyAndMetaValue(anyString(), anyString())).thenReturn(null);
        // 模拟 metaData 数据
        when(agencyDao.getMeta(anyString(), anyString())).thenReturn(null);
        // 模拟用户数据
        com.learninggenie.common.data.entity.UserEntity user = new com.learninggenie.common.data.entity.UserEntity();
        user.setId(userId);
        user.setRole("teacher");
        when(userProvider.checkUser(anyString())).thenReturn(user);
        // 模拟 userEntity 数据
        when(userRepository.findById(anyString())).thenReturn(Optional.of(user));
        // 模拟导入前后数字
        int childrenCount = 1;
        when(studentDao.getChildrenCountByUserId(any(), anyString(), any(), anyList())).thenReturn(childrenCount);
        // 模拟导入记录
        SyncParseDataEntity savedEntity = new SyncParseDataEntity();
        savedEntity.setId(UUID.randomUUID().toString());
        when(syncParseDataEntityRepository.saveAndFlush(any(SyncParseDataEntity.class))).thenReturn(savedEntity);
        // 模拟 External 数据
        when(enrollmentMetadataRepository.findTop1ByMetaKeyAndMetaValue(anyString(), anyString())).thenReturn(null);
        // 模拟获取文件路径
        ReflectionTestUtils.setField(importDataService, "pdfBucket", "com.learning-genie.prod.pdf");
        ReflectionTestUtils.setField(importDataService, "s3Root", "https://s3.amazonaws.com");
        ReflectionTestUtils.setField(importDataService, "pdfEndpoint", "https://s3-us-west-1.amazonaws.com");
        // when(fileSystem.getPublicUrl(anyString(), anyString())).thenReturn("https://s3.amazonaws.com/com.learning-genie.prod.pdf/test.html");
        // when(fileSystem.getPublicUrl(anyString())).thenReturn("https://s3.amazonaws.com/com.learning-genie.prod.pdf/test.html");

        MockedStatic<WKHelper> wkHelperMockedStatic = mockStatic(WKHelper.class); // 创建一个 WKHelper 的模拟静态方法
        wkHelperMockedStatic.when(() -> WKHelper.create(any(), any(), any())).thenReturn(new File("filePath")); // 当调用 WKHelper 的 create 方法时，返回一个模拟文件
        wkHelperMockedStatic.when(() -> WKHelper.upload(any(), any(), any())).thenReturn("publicUrl"); // 当调用 WKHelper 的 upload 方法时，返回一个公共 URL

        // // 模拟 PdfConvertJobEntity 数据
        // InvokeResult invokeResult = new InvokeResult();
        // invokeResult.setStatusCode(200);
        // Future<InvokeResult> future = CompletableFuture.completedFuture(invokeResult);
        // when(remoteProvider.callPdfService(any(), any())).thenReturn(future);
        // 模拟 PdfConvertJobEntity 数据
        // PdfConvertJobEntity pdfJob = new PdfConvertJobEntity();
        // pdfJob.setId("pdfJobId001");
        // pdfJob.setPdfName("test.pdf");
        // pdfJob.setUrl("https://s3.amazonaws.com/com.learning-genie.prod.pdf/test.html");
        // pdfJob.setPdfUrl("https://s3.amazonaws.com/com.learning-genie.prod.pdf/test.pdf");
        // pdfJob.setStatus("SUCCEED");
        // pdfJob.setType("IMPORT_PDF");
        // pdfJob.setPdfPath("test.pdf");
        // pdfJob.setBucket("com.learning-genie.prod.pdf");
        //
        // when(reportDao.getPdfJob(any())).thenReturn(pdfJob);
        // 方法调用
        try {
            ImportDataViewModel importDataViewModel = importDataService.parseChildPlusData(userName, content, timeZone, userId, resource);
            //
            // // 验证
            // // 验证静态方法是否被调用
            // verify(remoteProvider).callPdfService(any(), any());
            // 验证导入的学校个数
            assert importDataViewModel.getCenters().size() == 0;
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            wkHelperMockedStatic.close();
        }
    }

    /**
     * 测试导入时处理孩子评分周期方法
     * case1: 孩子导入状态为 NORMAL
     */
    @Test
    void testGetChildPeriodsStatusIsNormal() {
        // 接口入参数据准备
        String updateChildString = "{\"id\":\"6B3A07DD-71C1-4BE7-BB65-412064A8C8B8\",\"groupId\":\"B5BB4F75-8607-4DF4-9405-7999F33EFB7A\",\"groupDomainId\":\"32df6b7b-a5a0-4b73-af23-193175bc537c\",\"gender\":\"MALE\",\"firstName\":\"One2\",\"lastName\":\"123\",\"displayName\":\"One2 123\",\"enrollmentDate\":\"08/21/2023\",\"birthDate\":\"11/19/2022\",\"extended\":{\"participationyear\":[\"1\"],\"Language\":[\"Other non-English\"],\"IEP/IFSP\":[\"No\"],\"Race\":[\"Intentionally Left Blank\"],\"race other\":[\"1\"],\"race hispanic\":[\"0\"],\"ELD\":[\"Yes\"],\"IMPORT_OTHER_VALUE\":[\"[{\\\"key\\\":\\\"program name\\\",\\\"value\\\":\\\"Part Day Head Start Program\\\"},{\\\"key\\\":\\\"Language\\\",\\\"value\\\":\\\"Chinese, Chinese\\\"},{\\\"key\\\":\\\"Race\\\",\\\"value\\\":\\\"Hispanic\\\"}]\"],\"Hispanic\":[\"No\"],\"race other description\":[\"Hispanic\"],\"program name\":[\"Part Day Head Start Program\"]}" +
                ",\"status\":\"NORMAL\",\"isMultiple\":false,\"importChild\":{\"id\":\"6B3A07DD-71C1-4BE7-BB65-412064A8C8B8\",\"groupId\":\"B5BB4F75-8607-4DF4-9405-7999F33EFB7A\",\"gender\":\"MALE\",\"firstName\":\"One2\",\"lastName\":\"123\",\"displayName\":\"One2 123\",\"enrollmentDate\":\"08/24/2023\",\"birthDate\":\"11/19/2022\",\"externalId\":\"\",\"extended\":{\"\":[\"\"],\"participationyear\":[\"1\"],\"childPlus_EXTERNAL_ID_C3DE2D26-FF95-439E-B76C-C83940E6D31B\":[\"\"],\"secondary adult first name\":[\"\"],\"race hispanic\":[\"0\"],\"program name\":[],\"primary adult last name\":[\"\"],\"secondary adult email\":[\"\"],\"Language\":[],\"Race\":[],\"Hispanic\":[\"No\"],\"secondary adult last name\":[\"\"],\"enrollment status\":[\"\"],\"IMPORT_OTHER_VALUE\":[\"[{\\\"key\\\":\\\"program name\\\",\\\"value\\\":\\\"Part Day Head Start Program\\\"},{\\\"key\\\":\\\"Language\\\",\\\"value\\\":\\\"Chinese, Chinese\\\"},{\\\"key\\\":\\\"Race\\\",\\\"value\\\":\\\"Hispanic\\\"}]\"],\"race other description\":[\"Hispanic\"],\"IEP/IFSP\":[\"No\"],\"race other\":[\"1\"],\"primary adult email\":[\"\"],\"primary adult first name\":[\"\"]},\"isMultiple\":false,\"importExtended\":{},\"ratingPeriods\":[],\"parents\":[],\"groupName\":\"sisson\",\"centerName\":\"test1_8.23\",\"isInactive\":false,\"isNewGroup\":false,\"deletePeriod\":[],\"isAliasSame\":true,\"loseNoteNum\":0,\"oneKeyImport\":false,\"isLock\":false,\"prePeriods\":[],\"importValues\":[{\"key\":\"program name\",\"value\":\"Part Day Head Start Program\"},{\"key\":\"Language\",\"value\":\"Chinese, Chinese\"},{\"key\":\"Race\",\"value\":\"Hispanic\"}],\"ignoreClassRoomSessionIds\":[],\"parentIds\":[]},\"inactiveGroupId\":\"74095D23-5680-485F-8D12-61AADFC5E6FD\",\"importExtended\":{\"\":[\"\"],\"participationyear\":[\"1\"],\"childPlus_EXTERNAL_ID_C3DE2D26-FF95-439E-B76C-C83940E6D31B\":[\"\"],\"secondary adult first name\":[\"\"],\"race hispanic\":[\"0\"],\"program name\":[],\"primary adult last name\":[\"\"],\"secondary adult email\":[\"\"],\"Language\":[],\"Race\":[],\"Hispanic\":[\"No\"],\"secondary adult last name\":[\"\"],\"enrollment status\":[\"\"],\"IMPORT_OTHER_VALUE\":[\"[{\\\"key\\\":\\\"program name\\\",\\\"value\\\":\\\"Part Day Head Start Program\\\"},{\\\"key\\\":\\\"Language\\\",\\\"value\\\":\\\"Chinese, Chinese\\\"},{\\\"key\\\":\\\"Race\\\",\\\"value\\\":\\\"Hispanic\\\"}]\"],\"race other description\":[\"Hispanic\"],\"IEP/IFSP\":[\"No\"],\"race other\":[\"1\"],\"primary adult email\":[\"\"],\"primary adult first name\":[\"\"]},\"ratingPeriods\":[],\"parents\":[],\"groupName\":\"sisson\",\"centerName\":\"test1_8.23\",\"periodGroupId\":\"31AE6D93-4C27-4223-B410-B835448FADE0\",\"isInactive\":false,\"isNewGroup\":false,\"deletePeriod\":[],\"isAliasSame\":true,\"importId\":\"57595362-BD33-4DB2-A703-CC6ACF9B0BB7\",\"loseNoteNum\":0,\"oneKeyImport\":true,\"isLock\":false,\"prePeriods\":[],\"importValues\":[],\"ignoreClassRoomSessionIds\":[],\"parentIds\":[],\"frameworkId\":\"32df6b7b-a5a0-4b73-af23-193175bc537c\"}";
        ImportChild child = JsonUtil.fromJson(updateChildString, ImportChild.class);
        String periodGroupId = "31AE6D93-4C27-4223-B410-B835448FADE0";
        String aliasType = "Season"; // 当前周期别名类型
        String agencyId = "C3DE2D26-FF95-439E-B76C-C83940E6D31B";
        boolean isOpenImportError = true; // 是否开启导入错误

        // 模拟数据
        PeriodsGroupEntity periodsGroup = new PeriodsGroupEntity();
        periodsGroup.setId(periodGroupId);
        periodsGroup.setPeriodGroupType(aliasType);
        periodsGroup.setType(aliasType);
        periodsGroup.setSchoolYear("2023-2024");
        Mockito.when(periodsGroupDao.getById(anyString())).thenReturn(periodsGroup); // 模拟获取周期组数据
        List<RatingPeriodEntity> ratingPeriodEntities = new ArrayList<>();
        RatingPeriodEntity ratingPeriodEntity = new RatingPeriodEntity();
        ratingPeriodEntity.setId("4895AE9A-A44D-437C-A9C1-17699017137B");
        ratingPeriodEntity.setDomainId("32df6b7b-a5a0-4b73-af23-193175bc537c");
        ratingPeriodEntity.setEnrollmentId("6B3A07DD-71C1-4BE7-BB65-412064A8C8B8");
        ratingPeriodEntity.setFromAtLocal(TimeUtil.parse("2023-08-21", "yyyy-MM-dd"));
        ratingPeriodEntity.setToAtLocal(TimeUtil.parse("2023-10-19", "yyyy-MM-dd"));
        ratingPeriodEntity.setAlias("2023-2024 Fall");
        ratingPeriodEntity.setDisplayAlias("Fall 2023");
        ratingPeriodEntity.setActived(true);
        ratingPeriodEntity.setNew(true);
        ratingPeriodEntities.add(ratingPeriodEntity);
        Mockito.when(studentDao.getEnrollmentsPeriods(anyList(), anyString())).thenReturn(ratingPeriodEntities); // 模拟获取孩子评分周期
        List<RatingPeriodEntity> newEnrollmentPeriods = new LinkedList<>();
        RatingPeriodEntity newEnrollmentPeriod = new RatingPeriodEntity();
        newEnrollmentPeriod.setId("2a6642cb-b2f4-4e49-a4d6-742a9b441dd8");
        newEnrollmentPeriod.setEnrollmentId("6B3A07DD-71C1-4BE7-BB65-412064A8C8B8");
        newEnrollmentPeriod.setAlias("2023-2024 Fall");
        newEnrollmentPeriod.setFromAtLocal(TimeUtil.parse("2023-08-24", "yyyy-MM-dd"));
        newEnrollmentPeriod.setToAtLocal(TimeUtil.parse("2023-10-22", "yyyy-MM-dd"));
        newEnrollmentPeriod.setActived(true);
        newEnrollmentPeriod.setNew(true);
        newEnrollmentPeriods.add(newEnrollmentPeriod);
        Mockito.when(periodService.updateEnrollmentPeriods(anyList(), anyString(), anyList(), anyBoolean(), anyBoolean(), anyBoolean())).thenReturn(newEnrollmentPeriods); // 模拟重新孩子评分周期

        // 调用被测试的方法
        importDataService.getChildPeriods(child, periodGroupId, new ArrayList<>(), aliasType, new ArrayList<>(), agencyId, isOpenImportError, new HashMap<>());

        // 验证
        ImportChild importChild = child.getImportChild();
        assertEquals("6B3A07DD-71C1-4BE7-BB65-412064A8C8B8", importChild.getId()); // 验证孩子 Id
        assertEquals(1, importChild.getRatingPeriods().size()); // 验证孩子生成的评分周期数量
        RatingPeriodEntity ratingPeriod = importChild.getRatingPeriods().get(0);
        assertEquals(newEnrollmentPeriod.getFromAtLocal(), ratingPeriod.getFromAtLocal()); // 验证孩子生成的评分周期开始时间
        assertEquals(newEnrollmentPeriod.getToAtLocal(), ratingPeriod.getToAtLocal()); // 验证孩子生成的评分周期结束时间
        assertEquals("Fall 2023", ratingPeriod.getDisplayAlias()); // 验证孩子生成的评分周期显示名称
        assertEquals("2023-08-24", ratingPeriod.getFromAtLocalString()); // 验证孩子生成的评分周期开始时间字符串
        assertEquals("2023-10-22", ratingPeriod.getToAtLocalString()); // 验证孩子生成的评分周期结束时间字符串
        assertEquals("32df6b7b-a5a0-4b73-af23-193175bc537c", ratingPeriod.getDomainId()); // 验证孩子生成的评分周期框架 Id
        Assertions.assertTrue(ratingPeriod.isActived()); // 验证孩子生成的评分周期是否激活
    }

    /**
     * 测试导入时处理孩子评分周期方法
     * case1: 孩子导入状态为 NEW_FROM, 正常小孩转班
     */
    @Test
    void testGetChildPeriodsStatusIsNewFrom() {
        // 接口入参数据准备
        String updateChildString = "{\"id\":\"6B3A07DD-71C1-4BE7-BB65-412064A8C8B8\",\"groupId\":\"B5BB4F75-8607-4DF4-9405-7999F33EFB7A\",\"groupDomainId\":\"32df6b7b-a5a0-4b73-af23-193175bc537c\",\"gender\":\"MALE\",\"sourceGroupId\":\"74095D23-5680-485F-8D12-61AADFC5E6FD\",\"firstName\":\"One2\",\"lastName\":\"123\",\"displayName\":\"One2 123\",\"enrollmentDate\":\"08/24/2023\",\"birthDate\":\"11/19/2022\",\"externalId\":\"\",\"extended\":{\"\":[\"\"],\"participationyear\":[\"1\"],\"childPlus_EXTERNAL_ID_C3DE2D26-FF95-439E-B76C-C83940E6D31B\":[\"\"],\"secondary adult first name\":[\"\"],\"race hispanic\":[\"0\"],\"program name\":[\"Part Day Head Start Program\"],\"primary adult last name\":[\"\"],\"secondary adult email\":[\"\"],\"Language\":[\"Other non-English\"],\"Race\":[\"Intentionally Left Blank\"],\"Hispanic\":[\"No\"],\"secondary adult last name\":[\"\"],\"enrollment status\":[\"\"],\"IMPORT_OTHER_VALUE\":[\"[{\\\"key\\\":\\\"program name\\\",\\\"value\\\":\\\"Part Day Head Start Program\\\"},{\\\"key\\\":\\\"Language\\\",\\\"value\\\":\\\"Chinese, Chinese\\\"},{\\\"key\\\":\\\"Race\\\",\\\"value\\\":\\\"Hispanic\\\"}]\"],\"race other description\":[\"Hispanic\"],\"IEP/IFSP\":[\"No\"],\"race other\":[\"1\"],\"primary adult email\":[\"\"],\"primary adult first name\":[\"\"],\"ELD\":[\"Yes\"]}" +
                ",\"status\":\"NEW_FROM\",\"isMultiple\":false,\"importChild\":{\"id\":\"6B3A07DD-71C1-4BE7-BB65-412064A8C8B8\",\"groupId\":\"74095D23-5680-485F-8D12-61AADFC5E6FD\",\"groupDomainId\":\"E9506154-3B50-E411-837D-02DBFC8648CE\",\"gender\":\"MALE\",\"sourceGroupId\":\"B5BB4F75-8607-4DF4-9405-7999F33EFB7A\",\"firstName\":\"One2\",\"lastName\":\"123\",\"displayName\":\"One2 123\",\"enrollmentDate\":\"08/24/2023\",\"birthDate\":\"11/19/2022\",\"extended\":{\"participationyear\":[\"1\"],\"Language\":[\"Other non-English\"],\"IEP/IFSP\":[\"No\"],\"Race\":[\"Intentionally Left Blank\"],\"race other\":[\"1\"],\"race hispanic\":[\"0\"],\"ELD\":[\"Yes\"],\"IMPORT_OTHER_VALUE\":[\"[{\\\"key\\\":\\\"program name\\\",\\\"value\\\":\\\"Part Day Head Start Program\\\"},{\\\"key\\\":\\\"Language\\\",\\\"value\\\":\\\"Chinese, Chinese\\\"},{\\\"key\\\":\\\"Race\\\",\\\"value\\\":\\\"Hispanic\\\"}]\"],\"Hispanic\":[\"No\"],\"race other description\":[\"Hispanic\"],\"program name\":[\"Part Day Head Start Program\"]},\"status\":\"OUT_TO\",\"isMultiple\":false,\"inactiveGroupId\":\"74095D23-5680-485F-8D12-61AADFC5E6FD\",\"importExtended\":{},\"ratingPeriods\":[],\"parents\":[],\"toGroup\":\"sisson\",\"toGroupId\":\"B5BB4F75-8607-4DF4-9405-7999F33EFB7A\",\"toCenter\":\"test1_8.23\",\"groupName\":\"Inactive Children\",\"centerName\":\"test1_8.23\",\"isInactive\":true,\"isNewGroup\":false,\"deletePeriod\":[],\"isAliasSame\":true,\"loseNoteNum\":0,\"oneKeyImport\":false,\"isLock\":false,\"prePeriods\":[],\"importValues\":[],\"ignoreClassRoomSessionIds\":[],\"parentIds\":[],\"frameworkId\":\"32DF6B7B-A5A0-4B73-AF23-193175BC537C\"},\"importExtended\":{\"participationyear\":[\"1\"],\"Language\":[\"Other non-English\"],\"IEP/IFSP\":[\"No\"],\"Race\":[\"Intentionally Left Blank\"],\"race other\":[\"1\"],\"race hispanic\":[\"0\"],\"ELD\":[\"Yes\"],\"IMPORT_OTHER_VALUE\":[\"[{\\\"key\\\":\\\"program name\\\",\\\"value\\\":\\\"Part Day Head Start Program\\\"},{\\\"key\\\":\\\"Language\\\",\\\"value\\\":\\\"Chinese, Chinese\\\"},{\\\"key\\\":\\\"Race\\\",\\\"value\\\":\\\"Hispanic\\\"}]\"],\"Hispanic\":[\"No\"],\"race other description\":[\"Hispanic\"],\"program name\":[\"Part Day Head Start Program\"]},\"ratingPeriods\":[],\"parents\":[],\"toGroup\":\"Inactive Children\",\"toGroupId\":\"74095D23-5680-485F-8D12-61AADFC5E6FD\",\"toCenter\":\"test1_8.23\",\"groupName\":\"sisson\",\"centerName\":\"test1_8.23\",\"isInactive\":false,\"isNewGroup\":false,\"deletePeriod\":[],\"isAliasSame\":true,\"importId\":\"0FB049B6-8497-45E7-A080-4312B068592A\",\"loseNoteNum\":0,\"oneKeyImport\":true,\"isLock\":false,\"prePeriods\":[],\"importValues\":[{\"key\":\"program name\",\"value\":\"Part Day Head Start Program\"},{\"key\":\"Language\",\"value\":\"Chinese, Chinese\"},{\"key\":\"Race\",\"value\":\"Hispanic\"}],\"ignoreClassRoomSessionIds\":[],\"parentIds\":[],\"frameworkId\":\"32df6b7b-a5a0-4b73-af23-193175bc537c\"}";
        ImportChild child = JsonUtil.fromJson(updateChildString, ImportChild.class);
        String periodGroupId = "320C2939-AA8F-4150-979B-D85D916A962D";
        String aliasType = "Season"; // 当前周期别名类型
        String agencyId = "C3DE2D26-FF95-439E-B76C-C83940E6D31B";
        boolean isOpenImportError = true; // 开启导入错误
        boolean isNormalChild = true; // 正常小孩

        // 模拟数据
        PeriodsGroupEntity periodsGroup = new PeriodsGroupEntity();
        periodsGroup.setId(periodGroupId);
        periodsGroup.setPeriodGroupType(aliasType);
        periodsGroup.setType(aliasType);
        periodsGroup.setSchoolYear("2023-2024");
        Mockito.when(periodsGroupDao.getById(anyString())).thenReturn(periodsGroup); // 模拟获取周期组数据
        List<RatingPeriodEntity> ratingPeriodEntities = new ArrayList<>();
        RatingPeriodEntity ratingPeriodEntity = new RatingPeriodEntity();
        ratingPeriodEntity.setId("4895AE9A-A44D-437C-A9C1-17699017137B");
        ratingPeriodEntity.setDomainId("32df6b7b-a5a0-4b73-af23-193175bc537c");
        ratingPeriodEntity.setEnrollmentId("6B3A07DD-71C1-4BE7-BB65-412064A8C8B8");
        ratingPeriodEntity.setFromAtLocal(TimeUtil.parse("2023-08-21", "yyyy-MM-dd"));
        ratingPeriodEntity.setToAtLocal(TimeUtil.parse("2023-10-19", "yyyy-MM-dd"));
        ratingPeriodEntity.setAlias("2023-2024 Fall");
        ratingPeriodEntity.setDisplayAlias("Fall 2023");
        ratingPeriodEntity.setActived(true);
        ratingPeriodEntities.add(ratingPeriodEntity);
        Mockito.when(studentDao.getEnrollmentsPeriods(anyList(), anyString())).thenReturn(ratingPeriodEntities);
        Mockito.when(periodProvider.getCurrentSchoolYearByGroup(anyString())).thenReturn("2023-2024");
        com.learninggenie.common.data.model.GroupEntity targetGroup = new GroupEntity();
        targetGroup.setPeriodGroupId(periodGroupId);
        Mockito.when(groupDao.getGroupCurrentSchoolYearPeriodById(anyString(), anyString())).thenReturn(targetGroup);
        Mockito.when(periodService.isEqualsPeriodsIgnoreCount(anyList(), anyList())).thenReturn(isNormalChild);
        Mockito.when(ratingService.isPSFramework(anyString())).thenReturn(false); // 模拟不是 PS 框架
        Mockito.doNothing().when(recordDao).batchCreateChildTransferRecord(anyList()); // 模拟批量创建孩子转移记录
        Mockito.when(noteDao.getDomainNotes(anyString(), anyString(), anyString())).thenReturn(new ArrayList<>()); // 模拟获取观察记录
        List<RatingPeriodEntity> newEnrollmentPeriods = new LinkedList<>();
        RatingPeriodEntity newEnrollmentPeriod = new RatingPeriodEntity();
        newEnrollmentPeriod.setId("2a6642cb-b2f4-4e49-a4d6-742a9b441dd8");
        newEnrollmentPeriod.setEnrollmentId("6B3A07DD-71C1-4BE7-BB65-412064A8C8B8");
        newEnrollmentPeriod.setAlias("2023-2024 Fall");
        newEnrollmentPeriod.setFromAtLocal(TimeUtil.parse("2023-08-24", "yyyy-MM-dd"));
        newEnrollmentPeriod.setToAtLocal(TimeUtil.parse("2023-10-22", "yyyy-MM-dd"));
        newEnrollmentPeriod.setActived(true);
        newEnrollmentPeriod.setNew(true);
        newEnrollmentPeriods.add(newEnrollmentPeriod);
        Mockito.when(periodService.updateEnrollmentPeriods(anyList(), anyString(), anyList(), anyBoolean(), anyBoolean(), anyBoolean())).thenReturn(newEnrollmentPeriods); // 模拟重新生成评分周期框架

        // 调用被测试的方法
        importDataService.getChildPeriods(child, periodGroupId, new ArrayList<>(), aliasType, new ArrayList<>(), agencyId, isOpenImportError, new HashMap<>());

        // 验证
        assertEquals("6B3A07DD-71C1-4BE7-BB65-412064A8C8B8", child.getId()); // 验证孩子 Id
        assertEquals(1, child.getRatingPeriods().size()); // 验证孩子评分周期框架数量
        assertEquals(1, child.getDeletePeriod().size()); // 验证孩子删除的评分周期框架数量
        RatingPeriodEntity ratingPeriod = child.getRatingPeriods().get(0);
        assertEquals(newEnrollmentPeriod.getFromAtLocal(), ratingPeriod.getFromAtLocal()); // 验证孩子生成的评分周期框架开始时间
        assertEquals(newEnrollmentPeriod.getToAtLocal(), ratingPeriod.getToAtLocal()); // 验证孩子生成的评分周期框架结束时间
        assertEquals("Fall 2023", ratingPeriod.getDisplayAlias()); // 验证孩子生成的评分周期框架名称
        assertEquals("08/24/2023", ratingPeriod.getFromAtLocalString()); // 验证孩子生成的评分周期框架开始时间字符串
        assertEquals("10/22/2023", ratingPeriod.getToAtLocalString()); // 验证孩子生成的评分周期框架结束时间字符串
        assertEquals("32df6b7b-a5a0-4b73-af23-193175bc537c", ratingPeriod.getDomainId()); // 验证孩子生成的评分周期框架 Id
        Assertions.assertTrue(ratingPeriod.isActived()); // 验证转班后的班级应该是激活状态
        Assertions.assertFalse(child.getExtended().get("TRANSFER_METADATA").isEmpty()); // 验证转班记录应该存在
    }

    /**
     * 测试孩子匹配成功后的处理
     */
    @Test
    void matchChildSucceedHandlerTest() {
        ImportChild child = new ImportChild(); // 声明导入孩子
        child.setId("1");
        Map<String, List<String>> extended = new HashMap<>();
        List<String> key1Values = new ArrayList<>();
        key1Values.add("value1");
        extended.put("key1", key1Values);
        child.setExtended(extended); // 设置孩子对应的导入孩子的自定义属性
        List<ImportParent> importParents = new ArrayList<>();
        ImportParent importParent = new ImportParent();
        importParent.setId("importParent1");
        importParents.add(importParent);
        child.setParents(importParents); // 设置孩子的家长
        ImportChild currentChild = new ImportChild(); // 声明现系统小孩
        currentChild.setId("2");
        try (MockedStatic<ImportUtil> importUtilStatic = Mockito.mockStatic(ImportUtil.class)) {
            importUtilStatic.when(() -> ImportUtil.valueHistory(Mockito.any(), Mockito.any(), Mockito.any())).thenAnswer((Answer<Void>) invocation -> null);
        }
        // 调用测试方法
        importDataService.matchChildSucceedHandler(ImportResource.CHILD_PLUS, child, currentChild);
        // 验证
        Assertions.assertEquals("2", child.getId()); // 验证孩子 Id
        Assertions.assertEquals("value1", currentChild.getImportExtended().get("key1").get(0)); // 验证孩子自定义属性
        Assertions.assertEquals("importParent1", currentChild.getParents().get(0).getId()); // 验证孩子家长
        Assertions.assertEquals(NORMAL, currentChild.getStatus()); // 验证导入小孩状态
        Assertions.assertTrue(currentChild.isMatched()); // 验证导入小孩已经被匹配
        Assertions.assertEquals(child, currentChild.getImportChild()); // 验证小孩中导入小孩是否设置正确
    }

    /**
     * 测试修改 MY_HEAD_START 导入类型,期望结果是修改为 GoEngage
     */
    @Test
    public void testChangeImportType() {
        // 模拟数据
        // 导入历史记录
        ImportHistoryResponse historyResponse = new ImportHistoryResponse();
        historyResponse.setImportType("MY_HEAD_START");

        // 调用被测试的方法
        importDataService.changeImportType(historyResponse);

        // 验证 MY_HEAD_START 导入类型修改为 GoEngage
        assertEquals("GoEngage", historyResponse.getImportType());
    }

    /**
     * 测试修改 PROMIS 导入类型,期望结果是修改为 GoEngage - Manual Import
     */
    @Test
    public void testChangeImportType1() {
        // 模拟数据
        // 导入历史记录
        ImportHistoryResponse historyResponse = new ImportHistoryResponse();
        historyResponse.setImportType("PROMIS");

        // 调用被测试的方法
        importDataService.changeImportType(historyResponse);

        // 验证 PROMIS 导入类型修改为 GoEngage - Manual Import
        assertEquals("GoEngage - Manual Import", historyResponse.getImportType());
    }

    /**
     * 测试修改 PROMIS 导入类型,期望结果是修改为 GoEngage - Manual Import
     */
    @Test
    public void testHistory() {
        // 模拟数据
        // 机构号
        String agencyId = UUID.randomUUID().toString();
        // Agency 所有的导入记录
        AgencyMetaDataEntity metaDataEntity = new AgencyMetaDataEntity();
        metaDataEntity.setAgencyId(agencyId);
        metaDataEntity.setMetaValue("true");
        when(agencyDao.getMeta(anyString(), anyString())).thenReturn(metaDataEntity);
        // 导入历史记录
        List<SyncHistoryEntity> listDB = new ArrayList<>();
        // 历史记录
        SyncHistoryEntity syncHistoryEntity = new SyncHistoryEntity();
        // 设置导入 Id
        String importerId = UUID.randomUUID().toString();
        // 设置记录 Id
        String id = UUID.randomUUID().toString();
        syncHistoryEntity.setId(id);
        syncHistoryEntity.setImportId(importerId);
        syncHistoryEntity.setImporterId(importerId);
        syncHistoryEntity.setAgencyId(agencyId);
        // 设置导入类型
        syncHistoryEntity.setImportType("PROMIS");
        listDB.add(syncHistoryEntity);
        // 模拟导入历史记录
        when(noteDao.listSyncHistoryByAgencyId(anyString(), anyBoolean())).thenReturn(listDB);
        // 导入用户信息
        List<com.learninggenie.common.data.entity.UserEntity> userEntityLis = new ArrayList<>();
        // 用户实体
        com.learninggenie.common.data.entity.UserEntity userEntity = new com.learninggenie.common.data.entity.UserEntity();
        // 用户 Id
        userEntity.setId(importerId);
        userEntityLis.add(userEntity);
        // 模拟导入用户信息
        when(userDao.getUserEntityByIds(anyList())).thenReturn(userEntityLis);
        ArrayList<MapModel> mapModels = new ArrayList<>();
        MapModel mapModel = new MapModel();
        mapModel.setKey(importerId);
        mapModel.setValue("1");
        mapModels.add(mapModel);
        // 模拟导入错误信息
        when(importErrorDao.getCountAlerts(anyList(), any())).thenReturn(mapModels);

        // 调用被测试的方法
        ImportHistoriesResponse history = importDataService.history(agencyId, true);

        // 验证本次导入的历史记录个数
        assertEquals(1, history.getImportHistories().size());
        // 验证 POMIS 导入类型修改为 GoEngage - Manual Import
        assertEquals("GoEngage - Manual Import", history.getImportHistories().get(0).getImportType());
    }

    /**
     * 测试修改 MY_HEAD_START 导入类型,期望结果是修改为 GoEngage
     */
    @Test
    public void testHistory1() {
        // 模拟数据
        // 机构号
        String agencyId = UUID.randomUUID().toString();
        // Agency 所有的导入记录
        AgencyMetaDataEntity metaDataEntity = new AgencyMetaDataEntity();
        // 机构号
        metaDataEntity.setAgencyId(agencyId);
        metaDataEntity.setMetaValue("true");
        when(agencyDao.getMeta(anyString(), anyString())).thenReturn(metaDataEntity);
        // 导入历史记录
        List<SyncHistoryEntity> listDB = new ArrayList<>();
        // 历史记录
        SyncHistoryEntity syncHistoryEntity = new SyncHistoryEntity();
        // 导入 Id
        String importerId = UUID.randomUUID().toString();
        // 记录 Id
        String id = UUID.randomUUID().toString();
        syncHistoryEntity.setId(id);
        // 设置导入 Id
        syncHistoryEntity.setImportId(importerId);
        // 设置导入人 Id
        syncHistoryEntity.setImporterId(importerId);
        // 设置机构号
        syncHistoryEntity.setAgencyId(agencyId);
        // 设置导入类型
        syncHistoryEntity.setImportType("MY_HEAD_START");
        listDB.add(syncHistoryEntity);
        // 模拟导入历史记录
        when(noteDao.listSyncHistoryByAgencyId(anyString(), anyBoolean())).thenReturn(listDB);
        // 导入用户信息
        List<com.learninggenie.common.data.entity.UserEntity> userEntityLis = new ArrayList<>();
        // 用户实体
        com.learninggenie.common.data.entity.UserEntity userEntity = new com.learninggenie.common.data.entity.UserEntity();
        userEntity.setId(importerId);
        userEntityLis.add(userEntity);
        // 模拟导入用户信息
        when(userDao.getUserEntityByIds(anyList())).thenReturn(userEntityLis);
        ArrayList<MapModel> mapModels = new ArrayList<>();
        MapModel mapModel = new MapModel();
        mapModel.setKey(importerId);
        mapModel.setValue("1");
        mapModels.add(mapModel);
        when(importErrorDao.getCountAlerts(anyList(), any())).thenReturn(mapModels);

        // 调用被测试的方法
        ImportHistoriesResponse history = importDataService.history(agencyId, true);

        // 验证本次导入的历史记录个数
        assertEquals(1, history.getImportHistories().size());
        // 验证 MY_HEAD_START 导入类型修改为 GoEngage
        assertEquals("GoEngage", history.getImportHistories().get(0).getImportType());
    }

    /**
     * 测试设置夜间导入是否邀请家长和老师（并设置上次导入是否邀请家长选项值）
     */
    @Test
    public void testSettingInvitationForTeacherAndParent() {
        // 准备测试数据
        // 当前用户 Id
        String currentUserId = "testUserId";
        // 当前机构 Id
        String currentAgencyId = "testAgencyId";
        // 当前机构名称
        String currentAgencyName = "testAgencyName";
        // 定义导入的 request
        ImportStructure request = new ImportStructure();
        // 定义导入的 center
        ArrayList<ImportCenter> centers = new ArrayList<>();
        // 定义临时密码
        String tempPassword = "TEMP_PASSWORD";
        // 循环设置导入的 center
        for (int i = 0; i < 10; i++) {
            // 定义要导入的 center
            ImportCenter center = new ImportCenter();
            // 设置 center 的属性
            // 设置学校的类型
            center.setType(ImportMark.NEW_CENTER);
            // 设置学校的 Id
            String centerId = "centerId" + i;
            center.setId(centerId);
            // 设置当前的 job Id
            center.setJobId("jobId" + i);
            // 设置学校属于哪个用户
            center.setUserId(currentUserId);
            // 设置学校隶属于哪个机构
            center.setAgencyId(currentAgencyId);
            // 设置学校的名称
            center.setName("centerName" + i);
            // 设置时区
            center.setTimeZone("Asia/Shanghai");
            // 设置发送时间
            center.setSendTime(TimeUtil.getUtcNowStr());
            // 设置创建时间
            center.setCreatedUtc(TimeUtil.getUtcNowStr());
            // 设置对应的要导入的班级
            ArrayList<ImportGroup> importGroups = Lists.newArrayList();
            // 循环创建对应的班级
            for (int j = 0; j < 10; j++) {
                // 定义要导入的班级
                ImportGroup group = new ImportGroup();
                // 设置班级的属性
                // 设置班级的周期组 Id
                group.setPeriodGroupId("periodGroupId" + j);
                // 设置班级的离校状态
                group.setInactive(false);
                // 设置班级所使用的模板
                group.setTemplate("template" + j);
                // 设置班级的导入状态
                group.setType(ImportMark.NEW_CENTER);
                // 设置班级的 Id
                group.setId("groupId" + j);
                // 设置班级所隶属的学校 Id
                group.setCenterId(centerId);
                // 设置班级的名称
                group.setName("groupName" + j);
                // 设置班级的年级组
                group.setStageId("stageId" + j);
                // 设置班级正在使用的框架
                group.setDomainId("domainId" + j);
                // 设置班级的学生
                ArrayList<ImportChild> children = Lists.newArrayList();
                // 循环创建班级的学生
                for (int k = 0; k < 10; k++) {
                    // 定义要导入的学生
                    ImportChild importChild = new ImportChild();
                    // 设置学生的属性
                    // 设置学生的 Id
                    importChild.setId("childId" + k);
                    // 设置学生的班级 Id
                    importChild.setGroupId("groupId" + j);
                    // 设置学生的班级框架 Id
                    importChild.setGroupDomainId("domainId" + j);
                    // 设置学生的性别
                    importChild.setGender(k % 2 == 0 ? "Male" : "Female");
                    // 设置学生的源班级 Id
                    importChild.setSourceGroupId("sourcedId" + j);
                    // 设置学生的姓名
                    importChild.setFirstName("firstName" + k);
                    // 设置学生的中间名
                    importChild.setMiddleName("middleName" + k);
                    // 设置学生的姓氏
                    importChild.setLastName("lastName" + k);
                    // 设置学生的全名
                    importChild.setDisplayName("displayName" + k);
                    // 设置学生的出生日期
                    String enrollmentDate = TimeUtil.format("2020-12-13", "yyyy-MM-dd");
                    importChild.setEnrollmentDate(enrollmentDate);
                    // 设置学生的离校日期
                    importChild.setWithdrawnDate(TimeUtil.format("2023-12-13", "yyyy-MM-dd"));
                    // 设置学生的创建时间
                    importChild.setCreateAtUtc(enrollmentDate);
                    // 设置学生的生日
                    importChild.setBirthDate(enrollmentDate);
                    // 设置学生的 source Id
                    importChild.setSourcedId("sourcedId" + k);
                    // 设置学生的 周期
                    importChild.setPeriod(new EnrollmentPeriodEntity());
                    // 设置学生的导入状态
                    importChild.setStatus(ImportChildStatus.NEW);
                    // 设置学生的导入的 job Id
                    importChild.setImportChild(new ImportChild());
                    // 设置学生的评分周期
                    importChild.setRatingPeriods(Lists.newArrayList());
                    // 设置学生的家长
                    importChild.setParents(Lists.newArrayList());
                    // 设置学生所在的班级名称
                    importChild.setGroupName("groupName" + j);
                    // 设置学生所在的学校名称
                    importChild.setCenterName("centerName" + i);
                    // 设置学生所使用的框架 Id
                    importChild.setFrameworkId("frameworkId" + j);

                    // 将学生添加入班级的学生列表中
                    children.add(importChild);
                }
                group.setChildren(children);
                // 设置班级的 source Id
                group.setSourcedId("sourcedId" + j);
                // 设置班级的最后一次修改时间
                group.setDateLastModified(TimeUtil.getUtcNow());
                // 设置班级的 source 状态
                group.setSourcedStatus("sourceStatus" + j);

                // 将班级添加到要导入的班级列表中
                importGroups.add(group);
            }
            center.setGroups(importGroups);
            // 获取班级 ids
            List<String> groupIds = importGroups.stream().map(ImportGroup::getId).collect(Collectors.toList());
            // 获取班级名称
            List<String> groupNames = importGroups.stream().map(ImportGroup::getName).collect(Collectors.toList());
            // 设置要导入的老师
            ArrayList<ImportTeacher> teachers = Lists.newArrayList();
            // 循环创建老师
            for (int j = 0; j < 10; j++) {
                // 定义要导入的老师
                ImportTeacher importTeacher = new ImportTeacher();
                // 设置老师的属性
                // 设置老师的密码
                importTeacher.setPasswordHash("passwordHash" + j);
                // 设置老师所任职的班级 名称
                importTeacher.setGroupNames(groupNames);
                // 设置老师的全名
                importTeacher.setDisplayName("displayName" + j);
                // 设置老师的导入状态
                importTeacher.setType(ImportMark.NEW_CENTER);
                // 设置老师的 邀请状态
                importTeacher.setInvite(j % 2 == 0);
                // 设置老师所在的班级 Id
                importTeacher.setGroupIds(groupIds);
                // 设置老师的 Id
                importTeacher.setId("teacherId" + j);
                // 设置老师的用户名
                importTeacher.setUserName("userName" + j);
                // 设置老师的姓名
                importTeacher.setFirstName("firstName" + j);
                // 设置老师的姓氏
                importTeacher.setLastName("lastName" + j);
                // 设置老师的 email
                importTeacher.setEmail("Teacher" + j + "@gmail.com");
                // 设置老师的电话
                importTeacher.setPhone("phone" + j);
                // 设置老师的密码
                importTeacher.setPassword("password" + j);
                // 设置老师的 securityStamp
                importTeacher.setSecurityStamp(TimeUtil.getUtcNowStr());
                // 设置老师的邀请 Id
                importTeacher.setInvitationId("invitationId" + j);
                // 设置老师的创建者
                importTeacher.setCreateUserId(currentUserId);
                // 设置老师的 token
                importTeacher.setToken("token" + j);
                // 设置老师的 source Id
                importTeacher.setSourcedId("sourcedId" + j);
                // 设置老师的最后一次修改时间
                importTeacher.setDateLastModified(new Date());

                // 将老师添加到要导入的老师列表中
                teachers.add(importTeacher);
            }
            center.setTeachers(teachers);
            // 设置是否是不在自己权限范围内的学校
            center.setNoRight(false);
            // 设置学校的 source Id
            center.setSourcedId("sourceId" + i);
            // 设置最后一次修改的时间
            center.setDateLastModified(TimeUtil.getUtcNow());
            // 设置当前的 source 状态
            center.setSourcedStatus(ImportMark.NEW_CENTER.toString());

            // 将学校添加到要导入的学校列表中
            centers.add(center);
        }
        // 设置要导入的学校
        request.setCenters(centers);
        // 邀请的家长和老师的 email
        List<InvitationsEnrollmentInvitationEntity> invitationEmails = null;
        // mock 方法
        // 定义要返回的数据
        // 定义学校的 metadata
        ArrayList<CenterMetaDataEntity> metaDataEntities = new ArrayList<>();
        // 循环创建学校的 metadata
        // 从 1 到 11，那么就正好有一个需要新增，一个需要删除
        for (int i = 1; i < 11; i++) {
            // 定义学校的 metadata
            CenterMetaDataEntity centerMetaDataEntity = new CenterMetaDataEntity();
            // 定义学校
            CenterEntity center = new CenterEntity();
            // 设置学校的 Id
            center.setId("centerId" + i);
            // 设置学校的名称
            center.setName("centerName" + i);
            // 将学校设置到 metadata 中
            centerMetaDataEntity.setCenter(center);
            // 设置学校的 source Id
            centerMetaDataEntity.setMetaValue("sourceId" + i);
            // 添加学校的 metadata
            metaDataEntities.add(centerMetaDataEntity);
        }
        lenient().when(centerDao.getMeta(anyList(), anyString())).thenReturn(metaDataEntities);
        // 将 metadata 转换为 CenterEntity
        List<CenterEntity> entities = metaDataEntities.stream().map(cm -> {
            // 获取 centerMeta 中的 center
            CenterEntity center = cm.getCenter();
            // 设置 metaValue
            center.setMetaValue(cm.getMetaValue());
            // 返回结果
            return center;
        }).collect(Collectors.toList());
        lenient().when(centerDao.getAllCenterWithMetaByAgencyId(anyString(), anyList())).thenReturn(entities);
        // 定义要返回的数据
        // 定义查询出来的老师的集合
        ArrayList<UserModel> userModels = new ArrayList<>();
        // 循环创建老师
        // 从 1 到 11，那么就正好有一个需要新增，一个需要删除
        for (int j = 1; j < 11; j++) {
            UserModel userModel = new UserModel();
            // 设置老师的属性
            // 设置老师的全名
            userModel.setDisplayName("displayName" + j);
            // 设置老师的 Id
            userModel.setId("teacherId" + j);
            // 设置老师的姓名
            userModel.setFirstName("firstName" + j);
            // 设置老师的姓氏
            userModel.setLastName("lastName" + j);
            // 设置老师的 email
            userModel.setEmail("Teacher" + j + "@gmail.com");
            // 设置老师的密码
            userModel.setPassword("password" + j);
            // 设置老师的 securityStamp
            userModel.setSecurityStamp(TimeUtil.getUtcNowStr());
            // 设置老师的 source Id
            userModel.setMetaValue("sourcedId" + j);
            // 添加老师到老师的集合中
            userModels.add(userModel);
        }
        lenient().when(userDao.getTeacherWithMetaByAgency(anyString(), anyString())).thenReturn(userModels);
        lenient().when(dotNetPasswordEncoder.encode(tempPassword)).thenReturn("PASSWORD");
        // Mock 通过 Email 获取 teacher 数据
        for (UserModel teacher : userModels) {
            lenient().when(userDao.getUserByEmail(eq(teacher.getEmail()))).thenReturn(teacher);
        }
        // 调用被测试方法
        importDataService.settingInvitationForTeacherAndParent(request, currentUserId, currentAgencyId, currentAgencyName, invitationEmails);
        // 断言 request 通过方法修改之后，剩余的数据是否符合预期
        // 断言学校的数量
        assertEquals(10, request.getCenters().size());
        // 断言第一个学校中的数据是否符合预期
        // 断言学校的 Id
        assertEquals("centerId0", request.getCenters().get(0).getId());
        // 断言学校的名称
        assertEquals("centerName0", request.getCenters().get(0).getName());
        // 断言学校的 source Id
        assertEquals("sourceId0", request.getCenters().get(0).getSourcedId());
        // 断言第一个学校的第一个班级信息是否符合预期
        // 断言班级的 Id
        assertEquals("groupId0", request.getCenters().get(0).getGroups().get(0).getId());
        // 断言班级的名称
        assertEquals("groupName0", request.getCenters().get(0).getGroups().get(0).getName());
        // 断言第一个学校中的老师是否符合预期
        // 断言老师的全名
        assertEquals("displayName0", request.getCenters().get(0).getTeachers().get(0).getDisplayName());
        // 断言老师的 source id
        assertEquals("sourcedId0", request.getCenters().get(0).getTeachers().get(0).getSourcedId());
    }

    /**
     * 测试按照优先级在孩子列表中匹配小孩
     */
    @Test
    void matchChildByPriorityTest() {
        // 创建测试数据
        ImportChild child = new ImportChild(); // 待匹配小孩
        child.setFirstName("Alice");
        child.setLastName("1");
        child.setBirthDate("2000-01-01");

        ImportChild child1 = new ImportChild(); // 生日不同的小孩
        child1.setFirstName("Alice");
        child1.setLastName("1");
        child1.setBirthDate("2222-01-02");
        child1.setMatched(false);
        ImportChild child2 = new ImportChild(); // 名字不同的小孩
        child2.setFirstName("Alice");
        child2.setLastName("2");
        child2.setBirthDate("2000-01-01");
        child2.setMatched(false);
        ImportChild child3 = new ImportChild(); // 已经匹配过的小孩
        child3.setFirstName("Alice");
        child3.setLastName("1");
        child3.setBirthDate("2000-01-01");
        child3.setMatched(true);
        ImportChild child4 = new ImportChild(); // 预定可以匹配成功的小孩
        child4.setFirstName("Alice");
        child4.setLastName("1");
        child4.setBirthDate("2000-01-01");
        child4.setMatched(false);
        List<ImportChild> children = new ArrayList<>();
        children.add(child1);
        children.add(child2);
        children.add(child3);
        children.add(child4);
        // 调用被测试方法
        ImportChild matchedChild = importDataService.matchChildByPriority(child, children);
        // 验证
        Assertions.assertEquals(child4, matchedChild); // 验证是否匹配成功
    }

    /**
     * 测试设置夜间导入是否邀请家长（并设置上次导入是否邀请家长选项值）
     */
    @Test
    public void testSetNightImportInviteParents() {
        // 准备测试数据
        NightImportInviteParentsRequest request = new NightImportInviteParentsRequest();
        request.setInviteParents(true);
        request.setImportId("importId");

        String currentUserId = "testUserId";
        String currentAgencyId = "testAgencyId";

        ImportMetaDataEntity metaDataEntity = new ImportMetaDataEntity();
        metaDataEntity.setId("metaDataId");
        metaDataEntity.setMetaValue("{\"invitationParents\": false}");

        ImportDataViewModel importDataViewModel = new ImportDataViewModel();
        importDataViewModel.setInvitationParent(true);
        // 设置模拟行为
        when(userProvider.getCurrentUserId()).thenReturn(currentUserId);
        when(userProvider.getCurrentAgencyId()).thenReturn(currentAgencyId);
        when(importErrorDao.getImportMetadataByMetakeyImportId(currentAgencyId, "importId", "NIGHTING"))
                .thenReturn(metaDataEntity);
        doNothing().when(importErrorDao).updateImportMetaDataValueById(any(), any());
        // 调用被测试方法
        importDataService.setNightImportInviteParents(request);
        // 验证结果
        verify(usersMetaDataDao).setMeta(eq(currentUserId), eq("LAST_IMPORT_INVITE_PARENTS_OPTION_VALUE"), eq("true")); // 验证设置导入邀请家长选项值是否正确
        importDataViewModel.setInvitationParent(true);
        verify(importErrorDao).updateImportMetaDataValueById(eq("metaDataId"), eq(JsonUtil.toJson(importDataViewModel))); // 验证更新导入数据是否正确
    }

    /***
     * 测试导入学校孩子数量的统计数据方法
     */
    @Test
    public void testUpdateImportSession() {
        // 创建三个学校 A、B、C
        ImportCenter centerA = new ImportCenter();
        centerA.setName("A");
        ImportGroup groupA = new ImportGroup();
        groupA.setName("GroupA");
        ImportChild child01 = new ImportChild();
        child01.setId("Child01");
        child01.setCenterName("A");
        child01.setToCenter("B");
        child01.setStatus(ImportChildStatus.OUT_TO);
        groupA.getChildren().add(child01);
        centerA.getGroups().add(groupA);

        ImportCenter centerB = new ImportCenter();
        ImportGroup groupB = new ImportGroup();
        groupB.setName("GroupA");
        ImportChild child02 = new ImportChild();
        child02.setId("Child01");
        child02.setCenterName("B");
        child02.setToCenter("A");
        child02.setStatus(ImportChildStatus.NEW_FROM);
        ImportChild child05 = new ImportChild();
        child05.setId("Child03");
        child05.setCenterName("B");
        child05.setToCenter("C");
        child05.setStatus(ImportChildStatus.NEW_FROM);
        groupB.getChildren().add(child02);
        groupB.getChildren().add(child05);
        centerB.setName("B");
        centerB.getGroups().add(groupB);

        ImportCenter centerC = new ImportCenter();
        ImportGroup groupC = new ImportGroup();
        groupC.setName("GroupA");
        ImportChild child03 = new ImportChild();
        child03.setId("Child03");
        child03.setCenterName("C");
        child03.setToCenter("B");
        child03.setStatus(ImportChildStatus.OUT_TO);
        groupC.getChildren().add(child03);
        centerC.setName("C");
        centerC.getGroups().add(groupC);

        // 创建 ImportStructure，包含这三个学校
        ImportStructure importStructure = new ImportStructure();
        importStructure.setCenters(Arrays.asList(centerA, centerB, centerC));

        List<ImportCenterStatisticModel> centerStatisticList = new ArrayList<>();
        ImportCenterStatisticModel centerAStatistic = new ImportCenterStatisticModel();
        centerAStatistic.setCenterName("A");
        centerStatisticList.add(centerAStatistic);
        ImportCenterStatisticModel centerBStatistic = new ImportCenterStatisticModel();
        centerBStatistic.setCenterName("B");
        centerStatisticList.add(centerBStatistic);
        ImportCenterStatisticModel centerCStatistic = new ImportCenterStatisticModel();
        centerCStatistic.setCenterName("C");
        centerStatisticList.add(centerCStatistic);

        // 创建 ImportStatisticModel，模拟学生的转入和转出情况
        ImportStatisticModel importStatisticModel = new ImportStatisticModel();
        importStatisticModel.setCenters(centerStatisticList);
        importStatisticModel.setAllIn(new ArrayList<>());
        importStatisticModel.setAllOut(new ArrayList<>());
        importStatisticModel.setCenterChildOutMap(new HashMap<>());
        importStatisticModel.setCenterChildInActiveMap(new HashMap<>());

        // 调用待测试的方法
        importDataService.updateImportSession("testAgencyId", importStatisticModel, importStructure);

        // 验证 agencyDao.setMeta 方法是否被正确调用
        verify(agencyDao, times(1)).setMeta(eq("testAgencyId"), eq(AgencyMetaKey.AGENCY_IMPORT.toString()), anyString());

        // 验证结果是否符合预期
        assertEquals(2, importStatisticModel.getAllIn().size());
        assertEquals(2, importStatisticModel.getAllOut().size());

        // 验证 A 学校的学生统计
        ImportCenterStatisticModel a = importStatisticModel.getCenters().stream().filter(item -> item.getCenterName().equals("A")).collect(Collectors.toList()).get(0);
        assertEquals(0, a.getChildBefore());
        assertEquals(0, a.getChildNew());
        assertEquals(0, a.getChildIn());
        assertEquals(0, a.getChildInactive());
        assertEquals(0, a.getChildTransfer());
        assertEquals(1, a.getChildOut());
        assertEquals(0, a.getChildSync());
        // 验证 B 学校的学生统计
        ImportCenterStatisticModel b = importStatisticModel.getCenters().stream().filter(item -> item.getCenterName().equals("B")).collect(Collectors.toList()).get(0);
        assertEquals(0, b.getChildBefore());
        assertEquals(0, b.getChildNew());
        assertEquals(2, b.getChildIn());
        assertEquals(0, b.getChildInactive());
        assertEquals(2, b.getChildTransfer());
        assertEquals(0, b.getChildOut());
        assertEquals(2, b.getChildSync());
        // 验证 C 学校的学生统计
        ImportCenterStatisticModel c = importStatisticModel.getCenters().stream().filter(item -> item.getCenterName().equals("C")).collect(Collectors.toList()).get(0);
        assertEquals(0, c.getChildBefore());
        assertEquals(0, c.getChildNew());
        assertEquals(0, c.getChildIn());
        assertEquals(0, c.getChildInactive());
        assertEquals(0, c.getChildTransfer());
        assertEquals(1, c.getChildOut());
        assertEquals(0, c.getChildSync());
    }

    /**
     * Other 导入员工发送邮件
     */
    @Test
    public void testSendEmail() {
        String adminEmail = "ADMIN_EMAIL";
        String agencyName = "AGENCY_NAME";

        // 构建 command Json 模拟参数
        String username = "USER_NAME";
        String districtName = "DISTRICT_NAME";
        String tempPassword = "TEMP_PASSWORD";
        String email = "EMAIL";
        String centerName = "CENTER_NAME";
        JSONObject json = new JSONObject();
        json.put("username", username);
        json.put("districtName", districtName);
        json.put("tempPassword", tempPassword);
        json.put("centerName", centerName);
        json.put("email", email);
        String command = "Teacher Invite:" + json + "\r\n";
        String type = "TYPE";
        String resource = "RESOURCE";
        String role = "TEACHER";
        // 构建 LambdaEmailRequset 模拟参数
        LambdaEmailRequset request = new LambdaEmailRequset();
        request.setCommand(command);
        request.setResource(resource);
        request.setRole(role);
        request.setType(type);

        // 模拟 emailTemplateVersion
        ReflectionTestUtils.setField(importDataService, "emailTemplateVersion", "v1");

        // 模拟对象行为
        when(userProvider.getTimezoneOffsetNum()).thenReturn(0);

        //执行
        importDataService.sendEmail(request, adminEmail, agencyName);
    }

    /**
     * 测试通过 SSO 回调授权码获取 Clever 学区认证信息
     */
    @Test
    public void testGetCleverAuthorizeInfo() throws UnsupportedEncodingException {
        // 模拟 Clever 返回的数据
        GetTokenResponse getTokenResponse = new GetTokenResponse();
        getTokenResponse.setAccessToken("Learning Genie");
        OauthMeResponse oauthMeResponse = new OauthMeResponse();
        oauthMeResponse.setType("user");
        oauthMeResponse.setAuthorizedBy("district");
        com.learninggenie.common.sync.clever.data.User userInfo = new com.learninggenie.common.sync.clever.data.User();
        userInfo.setDistrict("Learning Genie");
        RolesAttr rolesAttr = new RolesAttr();
        rolesAttr.setDistrictAdminAttr(new DistrictAdminAttr());
        userInfo.setRoles(rolesAttr);
        District district = new District();

        // 模拟授权成功的场景
        CleverDataClient cleverAuthClientMock = Mockito.mock(CleverDataClient.class);
        // mock 认证信息
        when(cleverAppClient.generationAccessToken(any(), any())).thenReturn(getTokenResponse);
        when(cleverAppClient.buildDataClientByToken(any())).thenReturn(cleverAuthClientMock);
        when(cleverAuthClientMock.me()).thenReturn(oauthMeResponse);
        when(cleverAppClient.buildDataClient(any())).thenReturn(cleverAuthClientMock);
        when(cleverAuthClientMock.getUserById(any())).thenReturn(userInfo);
        when(cleverAuthClientMock.getDistrict(any())).thenReturn(district);
        when(accountService.buildCleverRedirectUri("externalWindow", false)).thenReturn("redirectUri");
        // 执行测试并断言响应结果
        GetCleverAuthorizeInfoResponse response = importDataService.getCleverAuthorizeInfo("Learning Genie", "Learning Genie");
        assertFalse(StringUtil.isEmptyOrBlank(response.getAccessToken()));

        GetCleverAuthorizeInfoResponse learningGenie = importDataService.getCleverAuthorizeInfo("Learning Genie", null);
        assertEquals("Learning Genie", learningGenie.getAccessToken());

        // 模拟非 Clever 学区管理员授权的场景
        rolesAttr.setDistrictAdminAttr(null); // 设置角色为 null
        // 使用 assertThrows 来断言异常的抛出
        Executable executable = () -> importDataService.getCleverAuthorizeInfo("Learning Genie", "Learning Genie");
        // 执行测试并断言异常类型和消息
        BusinessException exception = assertThrows(BusinessException.class, executable);
        assertEquals(ErrorCode.CLEVER_DISTRICT_ROSTER_LINK_FAILED, exception.getErrorCode());

        // 模拟 Clever 认证类型不是 District 认证的场景
        oauthMeResponse.setType("teacher"); // 设置 Clever 认证类型是老师
        // 使用 assertThrows 来断言异常的抛出
        executable = () -> importDataService.getCleverAuthorizeInfo("Learning Genie", "Learning Genie");
        // 执行测试并断言异常类型和消息
        exception = assertThrows(BusinessException.class, executable);
        assertEquals(ErrorCode.REQUEST_THIRD_PART_API_FAILED, exception.getErrorCode());

        // 模拟 Clever 回调时 code 为空或者 redirectUri 为空的场景
        executable = () -> importDataService.getCleverAuthorizeInfo(null, "Learning Genie"); // code 为空
        // 执行测试并断言异常类型和消息
        exception = assertThrows(BusinessException.class, executable);
        assertEquals(ErrorCode.PARAM_ERROR, exception.getErrorCode());
    }

    /**
     * 测试通过 Clever District SSO 认证的 Token 连接 Clever -> LG Rostering
     */
    @Test
    public void testLinkCleverRostering() {
        LinkCleverRosteringRequest request = new LinkCleverRosteringRequest();
        OauthMeResponse oauthMeResponse = new OauthMeResponse();
        oauthMeResponse.setType("user");
        oauthMeResponse.setAuthorizedBy("district");
        com.learninggenie.common.sync.clever.data.User userInfo = new com.learninggenie.common.sync.clever.data.User();
        userInfo.setDistrict("Learning Genie");
        RolesAttr rolesAttr = new RolesAttr();
        rolesAttr.setDistrictAdminAttr(new DistrictAdminAttr());
        userInfo.setRoles(rolesAttr);
        District district = new District();
        ThirdAuthEntity thirdAuthEntity = new ThirdAuthEntity();
        thirdAuthEntity.setExtendData("Learning Genie");

        // mock 连接成功的数据
        CleverDataClient cleverAuthClientMock = Mockito.mock(CleverDataClient.class);
        when(cleverAppClient.buildDataClientByToken(any())).thenReturn(cleverAuthClientMock);
        when(cleverAuthClientMock.me()).thenReturn(oauthMeResponse);
        when(cleverAuthClientMock.getUserById(any())).thenReturn(userInfo);
        when(cleverAuthClientMock.getDistrict(any())).thenReturn(district);
        when(cleverAppClient.buildDataClient(any())).thenReturn(cleverAuthClientMock);
        when(importThirdAuthDao.getThirdAuthByAgencyIdAndSystemTypeAndAuth(any(), any(), any())).thenReturn(thirdAuthEntity);
        when(cleverProvider.isMappedToOtherAgency(any(), any())).thenReturn(false);

        try (MockedStatic<JsonUtil> jsonUtilMockedStatic = mockStatic(JsonUtil.class)) {
            // 创建一个 JsonUtil 的模拟静态方法
            jsonUtilMockedStatic.when(() -> JsonUtil.fromJson(any(), any())).thenReturn(new CleverSyncAuth());
            // 执行测试并断言测试结果
            LinkCleverRosteringResponse response = importDataService.linkCleverRostering(request);
            assertTrue(response.isSuccess());

            // 模拟 Clever 学区已经和其它 LG 机构绑定的场景
            when(cleverProvider.isMappedToOtherAgency(any(), any())).thenReturn(true); // 设置 Clever 学区 ID 是否和其它机构关联
            // 使用 assertThrows 来断言异常的抛出
            Executable executable = () -> importDataService.linkCleverRostering(request);
            // 执行测试并断言异常类型和消息
            BusinessException exception = assertThrows(BusinessException.class, executable);
            assertEquals(ErrorCode.CLEVER_DISTRICT_OTHER_AGENCY_MAPPING_EXISTS, exception.getErrorCode());
        }
    }

    /**
     * 测试 Clever 断开连接
     */
    @Test
    public void testUnlinkClever() {
        // 模拟第三方认证信息
        ThirdAuthEntity thirdAuthEntity = new ThirdAuthEntity();
        thirdAuthEntity.setExtendData("Learning Genie");
        // 模拟查询数据库获取第三方认证信息
        when(importThirdAuthDao.getThirdAuthByAgencyIdAndSystemTypeAndAuth(any(), any(), any())).thenReturn(thirdAuthEntity);
        MockedStatic<JsonUtil> jsonUtilMockedStatic = mockStatic(JsonUtil.class); // 创建一个 JsonUtil 的模拟静态方法
        jsonUtilMockedStatic.when(() -> JsonUtil.fromJson(any(), any())).thenReturn(new CleverSyncAuth());
        SuccessResponse response = importDataService.unlinkClever();
        assertTrue(response.isSuccess());
        jsonUtilMockedStatic.close();
    }
}

package com.learninggenie.api.service.impl;

import com.learninggenie.api.provider.RemoteProvider;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.service.ChatsDialogService;
import com.learninggenie.common.cache.CacheService;
import com.learninggenie.common.comm.CommService;
import com.learninggenie.common.comm.quickblox.model.message.ChatMessageEntity;
import com.learninggenie.common.comm.quickblox.model.message.ChatMessageHistoryWithDialogResponse;
import com.learninggenie.common.comm.quickblox.model.message.ListMessageResponse;
import com.learninggenie.common.comm.quickblox.model.message.MessageResponse;
import com.learninggenie.common.data.dao.*;
import com.learninggenie.common.data.dao.chat.ChatsDialogDao;
import com.learninggenie.common.data.dao.chat.ChatsDialogMemberDao;
import com.learninggenie.common.data.dao.im.ChatMessageDao;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.dao.users.ImProfileDao;
import com.learninggenie.common.data.dto.chat.PrivateChatParentChildDTO;
import com.learninggenie.common.data.entity.AgencyEntity;
import com.learninggenie.common.data.entity.EnrollmentChatGroupEntity;
import com.learninggenie.common.data.entity.EnrollmentEntity;
import com.learninggenie.common.data.entity.UserEntity;
import com.learninggenie.common.data.entity.chats.ChatsDialogEntity;
import com.learninggenie.common.data.entity.chats.ChatsDialogMemberEntity;
import com.learninggenie.common.data.entity.users.ImProfileEntity;
import com.learninggenie.common.data.model.EnrollmentModel;
import com.learninggenie.common.data.model.UserModel;
import com.learninggenie.common.data.model.hyphenate.MessagePushRequest;
import com.learninggenie.common.data.repository.UserEnrollmentRepository;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * 聊天记录服务测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class ChatMessageServiceImplTest {

    @InjectMocks
    private ChatMessageServiceImpl chatMessageService;

    @Mock
    private CacheService cacheService;

    @Mock
    private AgencyDao agencyDao;

    @Mock
    private ChatMessageDao chatMessageDao;

    @Mock
    private MetaDao metaDao;

    @Mock
    private CommService commService;

    @Mock
    private ImProfileDao imProfileDao;

    @Mock
    private UserDaoImpl userDao;

    @Mock
    private UserProvider userProvider;

    @Mock
    private CenterDao centerDao;

    @Mock
    private RemoteProvider remoteProvider;

    @Mock
    private StudentDao studentDao;

    @Mock
    private DashboardDao dashboardDao;

    @Mock
    private ChatsDialogDao chatsDialogDao;

    @Mock
    private ChatsDialogMemberDao chatsDialogMemberDao;

    @Mock
    private CommService commQuickbloxService;

    @Mock
    private UserEnrollmentRepository userEnrollmentRepository;

    @Mock
    private ChatsDialogService chatsDialogService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 获取指定时间之前的聊天记录列表测试
     * 测试小孩 Id 和 聊天记录个数不为空，聊天时间传参为空的情况
     */
    @Test
    public void testListChatMessages() {
        // 数据准备
        // 小孩 Id
        String childId = "f25b6175-0d57-4407-b4e2-e2afda821df7";
        // 根据小孩 Id 获取机构信息
        AgencyEntity agencyEntity = new AgencyEntity();
        // 机构 Id
        String agencyId = "f7bcd3b7-3f64-4402-981d-496b5863d217";
        agencyEntity.setId(agencyId);
        Mockito.when(agencyDao.getAgencyByChildId(childId)).thenReturn(agencyEntity);
        // 设置系统配置的初始化时间
        String startTime = "2027-07-01 00:00:00";
        when(commService.getByAgencyIdMessageStartTime(anyString())).thenReturn(startTime);

        // 调用方法
        ListMessageResponse listMessageResponse = chatMessageService.listChatMessages(childId, "1693497600000", 50);

        // 结果验证
        assert listMessageResponse.getItems().size() == 0;
    }

    /**
     * 获取指定时间之前的聊天记录列表测试
     * 测试小孩 Id 和 聊天记录个数不为空，聊天时间传参为空的情况且缓存时间小于传入时间
     */
    @Test
    public void testListChatMessages1() {
        // 数据准备
        // 小孩 Id
        String childId = "f25b6175-0d57-4407-b4e2-e2afda821df7";
        // 根据小孩 Id 获取机构信息
        AgencyEntity agencyEntity = new AgencyEntity();
        // 机构 Id
        String agencyId = "f7bcd3b7-3f64-4402-981d-496b5863d217";
        agencyEntity.setId(agencyId);
        Mockito.when(agencyDao.getAgencyByChildId(childId)).thenReturn(agencyEntity);
        // 设置系统配置的初始化时间
        String startTime = "2022-07-01 00:00:00";
        when(commService.getByAgencyIdMessageStartTime(anyString())).thenReturn(startTime);
        // 准备聊天记录的返回结果
        List<ChatMessageEntity> messageEntities = new ArrayList<>();
        ChatMessageEntity chatMessageEntity = new ChatMessageEntity();
        chatMessageEntity.setMessage("test");
        messageEntities.add(chatMessageEntity);
        Mockito.when(chatMessageDao.listByChild(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt())).thenReturn(messageEntities);

        // 调用方法
        ListMessageResponse listMessageResponse = chatMessageService.listChatMessages(childId, "1693497600000", 50);

        // 结果验证
        assert listMessageResponse.getItems().size() == 1;
    }

    /**
     * 获取指定时间之前的聊天记录列表测试
     * 测试小孩 Id 和 聊天记录个数不为空，聊天时间传参为空的情况且缓存中没有时间的情况
     */
    @Test
    public void testListChatMessages2() {
        // 数据准备
        // 小孩 Id
        String childId = "f25b6175-0d57-4407-b4e2-e2afda821df7";
        // 机构信息为空
        Mockito.when(agencyDao.getAgencyByChildId(childId)).thenReturn(null);
        // 设置系统配置的初始化时间
        String startTime = "2027-07-01 00:00:00";
        when(commService.getInitMessageStartTime()).thenReturn(startTime);

        // 调用方法
        ListMessageResponse listMessageResponse = chatMessageService.listChatMessages(childId, "1693497600000", 50);

        // 结果验证
        assert listMessageResponse.getItems().size() == 0;
    }

    /**
     * 测试生成日志记录接口
     * case: 单个小孩发送消息后，生成日志记录
     */
    @Test
    public void testPushMessage() {
        // 创建请求数据
        MessagePushRequest request = new MessagePushRequest(); // 创建请求数据
        request.setMessageId("messageId001"); // 设置消息 Id
        request.setFrom("userId001"); // 设置发送者
        request.setTo(Arrays.asList("userId002", "userId003")); // 设置接收者
        request.setMessageType("txt"); // 设置消息类型
        request.setPayload("Test payload"); // 设置消息内容
        request.setChatGroupId("chatGroupId001"); // 设置群组 Id
        request.setGroupId("groupId001"); // 设置班级 Id
        request.setChildId("childId001"); // 设置小孩 Id
        request.setLang("en-US"); // 设置语言

        ImProfileEntity imProfileEntity = new ImProfileEntity(); // 创建聊天用户信息
        imProfileEntity.setId("imProfileId001"); // 设置 Id
        imProfileEntity.setUsername("Ms.test test"); // 设置用户名
        imProfileEntity.setEmail("<EMAIL>"); // 设置邮箱
        imProfileEntity.setPassword("123456"); // 设置密码
        imProfileEntity.setUserId("userId001"); // 设置用户 Id
        imProfileEntity.setImUserId("imUserId001"); // 设置聊天用户 Id
        imProfileEntity.setAgencyId("agencyId001"); // 设置机构 Id
        when(imProfileDao.getByImUserId("userId001")).thenReturn(imProfileEntity); // 模拟获取用户聊天信息

        UserEntity sender = new UserEntity(); // 创建用户信息
        sender.setId("userId001"); // 设置用户 Id
        sender.setEmail("<EMAIL>"); // 设置邮箱
        sender.setRole("AGENCY_ADMIN"); // 设置角色
        sender.setFirstName("test"); // 设置名
        sender.setLastName("test"); // 设置姓
        when(userProvider.checkUser("userId001")).thenReturn(sender); // 模拟获取用户信息

        // 私聊信息
        List<PrivateChatParentChildDTO> privateChatParentChildList = new ArrayList<>();
        PrivateChatParentChildDTO privateChatParentChildDTO = new PrivateChatParentChildDTO();
        privateChatParentChildDTO.setAgencyId("agency_id");
        privateChatParentChildDTO.setCenterId("center_id");
        privateChatParentChildDTO.setGroupId("group_id");
        privateChatParentChildList.add(privateChatParentChildDTO);
        // when(chatsDialogService.listPrivateChatParentEnrollments("chatGroupId", "sender_id")).thenReturn(privateChatParentChildList);

        // 调用测试方法
        chatMessageService.pushMessage(request);

        // 验证是否调用 Lambda 异步生成日志记录
        verify(remoteProvider).callCreateChatLogService(any());
    }

    /**
     * 测试生成日志记录接口
     * case: 班级批量发送消息后，生成日志记录
     */
    @Test
    public void testPushMessage2() {
        // 创建请求数据
        MessagePushRequest request = new MessagePushRequest(); // 创建请求数据
        request.setMessageId("messageId001"); // 设置消息 Id
        request.setFrom("userId001"); // 设置发送者
        request.setTo(Arrays.asList("userId002", "userId003")); // 设置接收者
        request.setMessageType("txt"); // 设置消息类型
        request.setPayload("Test payload"); // 设置消息内容
        request.setChatGroupId("chatGroupId001"); // 设置群组 Id
        request.setGroupId("groupId001"); // 设置班级 Id
        request.setLang("en-US"); // 设置语言

        ImProfileEntity imProfileEntity = new ImProfileEntity(); // 创建聊天用户信息
        imProfileEntity.setId("imProfileId001"); // 设置 Id
        imProfileEntity.setUsername("Ms.test test"); // 设置用户名
        imProfileEntity.setEmail("<EMAIL>"); // 设置邮箱
        imProfileEntity.setPassword("123456"); // 设置密码
        imProfileEntity.setUserId("userId001"); // 设置用户 Id
        imProfileEntity.setImUserId("imUserId001"); // 设置聊天用户 Id
        imProfileEntity.setAgencyId("agencyId001"); // 设置机构 Id
        when(imProfileDao.getByImUserId("userId001")).thenReturn(imProfileEntity); // 模拟获取用户聊天信息

        List<ImProfileEntity> imProfiles = new ArrayList<>(); // 创建聊天用户信息列表
        ImProfileEntity imProfileEntity2 = new ImProfileEntity(); // 创建聊天用户信息
        imProfileEntity2.setId("imProfileId002"); // 设置 Id
        imProfileEntity2.setUsername("Ms.test test"); // 设置用户名
        imProfileEntity2.setEmail("<EMAIL>"); // 设置邮箱
        imProfileEntity2.setPassword("123456"); // 设置密码
        imProfileEntity2.setUserId("userId002"); // 设置用户 Id
        imProfileEntity2.setImUserId("imUserId002"); // 设置聊天用户 Id
        imProfileEntity2.setAgencyId("agencyId001"); // 设置机构 Id
        imProfiles.add(imProfileEntity2); // 添加聊天用户信息
        ImProfileEntity imProfileEntity3 = new ImProfileEntity(); // 创建聊天用户信息
        imProfileEntity3.setId("imProfileId003"); // 设置 Id
        imProfileEntity3.setUsername("Ms.test test"); // 设置用户名
        imProfileEntity3.setEmail("<EMAIL>"); // 设置邮箱
        imProfileEntity3.setPassword("123456"); // 设置密码
        imProfileEntity3.setUserId("userId003"); // 设置用户 Id
        imProfileEntity3.setImUserId("imUserId003"); // 设置聊天用户 Id
        imProfileEntity3.setAgencyId("agencyId001"); // 设置机构 Id
        imProfiles.add(imProfileEntity3); // 添加聊天用户信息
        when(imProfileDao.listByImUserIds(any())).thenReturn(imProfiles); // 模拟获取用户聊天信息列表

        List<EnrollmentModel> childrenByGroup = new ArrayList<>(); // 创建小孩信息列表
        EnrollmentModel enrollmentModel = new EnrollmentModel(); // 创建小孩信息
        enrollmentModel.setId("childId001"); // 设置小孩 Id
        enrollmentModel.setDisplayName("childName001"); // 设置小孩名字
        childrenByGroup.add(enrollmentModel); // 添加小孩信息
        EnrollmentModel enrollmentModel2 = new EnrollmentModel(); // 创建小孩信息
        enrollmentModel2.setId("childId002"); // 设置小孩 Id
        enrollmentModel2.setDisplayName("childName002"); // 设置小孩名字
        childrenByGroup.add(enrollmentModel2); // 添加小孩信息
        when(studentDao.getChildrenByGroup(any())).thenReturn(childrenByGroup); // 模拟获取班级的小孩信息列表

        UserEntity sender = new UserEntity(); // 创建用户信息
        sender.setId("userId001"); // 设置用户 Id
        sender.setEmail("<EMAIL>"); // 设置邮箱
        sender.setRole("AGENCY_ADMIN"); // 设置角色
        when(userProvider.checkUser("userId001")).thenReturn(sender); // 模拟获取用户信息

        List<EnrollmentEntity> parentChild = new ArrayList<>(); // 创建家长的小孩信息列表
        EnrollmentEntity enrollmentEntity = new EnrollmentEntity(); // 创建小孩信息
        enrollmentEntity.setId("childId001"); // 设置小孩 Id
        enrollmentEntity.setDisplayName("childName001"); // 设置小孩名字
        parentChild.add(enrollmentEntity); // 添加小孩信息
        when(studentDao.getChildByParent("USERID002")).thenReturn(parentChild); // 模拟获取家长的小孩信息列表

        List<EnrollmentEntity> parentChild2 = new ArrayList<>(); // 创建家长的小孩信息列表
        EnrollmentEntity enrollmentEntity2 = new EnrollmentEntity(); // 创建小孩信息
        enrollmentEntity2.setId("childId002"); // 设置小孩 Id
        enrollmentEntity2.setDisplayName("childName002"); // 设置小孩名字
        parentChild2.add(enrollmentEntity2); // 添加小孩信息
        when(studentDao.getChildByParent("USERID003")).thenReturn(parentChild2); // 模拟获取家长的小孩信息列表

        // 私聊信息
        List<PrivateChatParentChildDTO> privateChatParentChildList = new ArrayList<>();
        PrivateChatParentChildDTO privateChatParentChildDTO = new PrivateChatParentChildDTO();
        privateChatParentChildDTO.setAgencyId("agency_id");
        privateChatParentChildDTO.setCenterId("center_id");
        privateChatParentChildDTO.setGroupId("group_id");
        privateChatParentChildList.add(privateChatParentChildDTO);
        // when(chatsDialogService.listPrivateChatParentEnrollments("chatGroupId", "sender_id")).thenReturn(privateChatParentChildList);

        // 调用测试方法
        chatMessageService.pushMessage(request);

        // 验证是否调用 Lambda 异步生成日志记录
        verify(dashboardDao).batchSaveChatLog(any());
    }

    @Test
    public void testGetStaffToParentChats_noChatsFound() {
        String childId = "child1";
        String qbFromDate = "2023-01-01";
        String qbToDate = "2023-01-31";
        EnrollmentModel enrollment = new EnrollmentModel();
        enrollment.setDisplayName("Test Enrollment");
        List<ChatMessageHistoryWithDialogResponse> responses = new ArrayList<>();


        List<ChatMessageHistoryWithDialogResponse> result = chatMessageService.getStaffToParentChats(childId, qbFromDate, qbToDate, enrollment, responses);

        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetStaffToParentChats_chatsFound_withMembers_noMessages() {
        String childId = "child1";
        String qbFromDate = "2023-01-01";
        String qbToDate = "2023-01-31";
        EnrollmentModel enrollment = new EnrollmentModel();
        enrollment.setDisplayName("Test Enrollment");
        List<ChatMessageHistoryWithDialogResponse> responses = new ArrayList<>();

        ChatsDialogEntity chatDialog = new ChatsDialogEntity();
        chatDialog.setId("dialog1");
        chatDialog.setDialogId("dialog1");

        ChatsDialogMemberEntity member = new ChatsDialogMemberEntity();
        member.setChatsDialogId("dialog1");
        member.setUserId("user1");

        UserModel user = new UserModel();
        user.setId("user1");
        user.setFirstName("John");
        user.setLastName("Doe");
        user.setRole("staff");

        List<ChatMessageHistoryWithDialogResponse> result = chatMessageService.getStaffToParentChats(childId, qbFromDate, qbToDate, enrollment, responses);

        assertTrue(result.isEmpty());
    }

    /*@Test
    public void testGetStaffToParentChats_chatsFound_withMembers_withMessages() {
        String childId = "child1";
        String qbFromDate = "2023-01-01";
        String qbToDate = "2023-01-31";
        EnrollmentModel enrollment = new EnrollmentModel();
        enrollment.setDisplayName("Test Enrollment");
        List<ChatMessageHistoryWithDialogResponse> responses = new ArrayList<>();

        ChatsDialogEntity chatDialog = new ChatsDialogEntity();
        chatDialog.setId("dialog1");
        chatDialog.setDialogId("dialog1");

        ChatsDialogMemberEntity member = new ChatsDialogMemberEntity();
        member.setChatsDialogId("dialog1");
        member.setUserId("user1");

        UserModel user = new UserModel();
        user.setId("user1");
        user.setFirstName("John");
        user.setLastName("Doe");
        user.setRole("staff");

        MessageResponse message = new MessageResponse();
        message.setMessage("Hello");

        when(chatsDialogDao.list(any())).thenReturn(Arrays.asList(chatDialog));
        when(chatsDialogMemberDao.list(any())).thenReturn(Arrays.asList(member));
        when(userDao.getUsersByIds(any())).thenReturn(Arrays.asList(user));
        when(commQuickbloxService.listMessages(anyString(), anyLong(), anyLong())).thenReturn(Arrays.asList(message));

        List<ChatMessageHistoryWithDialogResponse> result = chatMessageService.getStaffToParentChats(childId, qbFromDate, qbToDate, enrollment, responses);

        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        assertEquals(1, result.get(0).getItems().size());
        assertEquals("Hello", result.get(0).getItems().get(0).getMessage());
    }
*/
    @Test
    public void testChatMessageHistory_invalidParams() {
        List<ChatMessageHistoryWithDialogResponse> result = chatMessageService.chatMessageHistory("", "", "");
        assertTrue(result.isEmpty());
    }

    /*@Test
    public void testChatMessageHistory_onlyDyDB() {
        String childId = "child1";
        String fromDate = "2022-04-01";
        String toDate = "2022-04-30";

        long fromTime = 1648771200000L; // 2022-04-01 00:00:00
        long toTime = 1651276800000L; // 2022-04-30 00:00:00
        long splitTime = 1651363200000L; // 2022-05-01 00:00:00

        EnrollmentModel enrollment = new EnrollmentModel();
        enrollment.setDisplayName("Test Enrollment");

        ChatMessageEntity chatMessageEntity = new ChatMessageEntity();
        chatMessageEntity.setChatDialogId("dialog1");

        when(studentDao.getEnrollment(childId)).thenReturn(enrollment);
        when(chatMessageDao.listByChild(childId, fromDate, toDate)).thenReturn(Arrays.asList(chatMessageEntity));

        List<ChatMessageHistoryWithDialogResponse> result = chatMessageService.chatMessageHistory(childId, fromDate, toDate);

        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        assertEquals("dialog1", result.get(0).getChatGroupId());
    }*/

    @Test
    public void testChatMessageHistory_onlyQB() {
        String childId = "child1";
        String fromDate = "2022-05-01";
        String toDate = "2022-05-31";

        long fromTime = 1651363200000L; // 2022-05-01 00:00:00
        long toTime = 1653955200000L; // 2022-05-31 00:00:00
        long splitTime = 1651363200000L; // 2022-05-01 00:00:00

        EnrollmentModel enrollment = new EnrollmentModel();
        enrollment.setDisplayName("Test Enrollment");

        EnrollmentChatGroupEntity chatGroupEntity = new EnrollmentChatGroupEntity();
        chatGroupEntity.setChatGroupId("group1");

        MessageResponse messageResponse = new MessageResponse();
        messageResponse.setMessage("Hello");

        when(studentDao.getEnrollment(childId)).thenReturn(enrollment);
        when(studentDao.getChatGroupById(childId, commQuickbloxService.getImVersion())).thenReturn(chatGroupEntity);

        List<ChatMessageHistoryWithDialogResponse> result = chatMessageService.chatMessageHistory(childId, fromDate, toDate);

        assertTrue(!result.isEmpty());
        assertEquals(1, result.size());
    }

    /*@Test
    public void testChatMessageHistory_crossSplitTime() {
        String childId = "child1";
        String fromDate = "2022-04-30";
        String toDate = "2022-05-02";

        long fromTime = 1651276800000L; // 2022-04-30 00:00:00
        long toTime = 1651536000000L; // 2022-05-02 00:00:00
        long splitTime = 1651363200000L; // 2022-05-01 00:00:00

        EnrollmentModel enrollment = new EnrollmentModel();
        enrollment.setDisplayName("Test Enrollment");

        ChatMessageEntity chatMessageEntity = new ChatMessageEntity();
        chatMessageEntity.setChatDialogId("dialog1");

        EnrollmentChatGroupEntity chatGroupEntity = new EnrollmentChatGroupEntity();
        chatGroupEntity.setChatGroupId("group1");

        MessageResponse messageResponse = new MessageResponse();
        messageResponse.setMessage("Hello");

        when(studentDao.getEnrollment(childId)).thenReturn(enrollment);
        when(chatMessageDao.listByChild(childId, fromDate, "04/30/2022 00:00:00")).thenReturn(Arrays.asList(chatMessageEntity));
        when(studentDao.getChatGroupById(childId, commQuickbloxService.getImVersion())).thenReturn(chatGroupEntity);

        List<ChatMessageHistoryWithDialogResponse> result = chatMessageService.chatMessageHistory(childId, fromDate, toDate);

        assertFalse(result.isEmpty());
        assertEquals(1, result.size());

        // Check DyDB response
        ChatMessageHistoryWithDialogResponse dyDbResponse = result.get(0);
        assertEquals("dialog1", dyDbResponse.getChatGroupId());

        // Check QB response
        ChatMessageHistoryWithDialogResponse qbResponse = result.get(0);
        assertEquals("dialog1", qbResponse.getChatGroupId());
        assertEquals(1, qbResponse.getItems().size());
        assertEquals(null, qbResponse.getItems().get(0).getMessage());
    }*/

    @Test
    public void testChatMessageHistory_noMessages() {
        String childId = "child1";
        String fromDate = "2022-04-01";
        String toDate = "2022-04-30";

        long fromTime = 1648771200000L; // 2022-04-01 00:00:00
        long toTime = 1651276800000L; // 2022-04-30 00:00:00
        long splitTime = 1651363200000L; // 2022-05-01 00:00:00

        EnrollmentModel enrollment = new EnrollmentModel();
        enrollment.setDisplayName("Test Enrollment");

        when(studentDao.getEnrollment(childId)).thenReturn(enrollment);
        when(chatMessageDao.listByChild(childId, fromDate, toDate)).thenReturn(Collections.emptyList());

        List<ChatMessageHistoryWithDialogResponse> result = chatMessageService.chatMessageHistory(childId, fromDate, toDate);

        assertTrue(result.isEmpty());
    }

}

package com.learninggenie.api.service.impl;

import com.google.common.collect.Lists;
import com.learninggenie.api.model.PeriodAlias;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.security.AuthUserDetails;
import com.learninggenie.common.data.dao.AgencyDao;
import com.learninggenie.common.data.dao.CenterDao;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.entity.AgencyIdentifierEntity;
import com.learninggenie.common.data.enums.Alias;
import com.learninggenie.common.data.enums.UserRole;
import com.learninggenie.common.data.enums.UserType;
import com.learninggenie.common.data.model.AgencyModel;
import com.learninggenie.common.data.model.dashboard.DashboardAliasModel;
import com.learninggenie.common.utils.TimeUtil;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.when;

/**
 * 应用程序服务impl测试
 *
 * <AUTHOR>
 * @date 2023/10/09
 */
@RunWith(MockitoJUnitRunner.class)
public class AppServiceImplTest {
    @Mock
    private UserProvider userProvider;
    @Mock
    private CenterDao centerDao;
    @Mock
    private UserDaoImpl userDao;
    @Mock
    private AgencyDao agencyDao;

    @InjectMocks
    private AppServiceImpl appService;

    /**
     * 测试获取当年所有别名
     */
    @Test
    public void testGetAllAliasInCurrentYear() {
        // Mock AuthUserDetails
        // 设置 AuthUserDetails 的属性
        AuthUserDetails authUserDetails = new AuthUserDetails();
        authUserDetails.setType("Type");
        authUserDetails.setDelayed(false);
        authUserDetails.setNonExpired(false);
        authUserDetails.setNonLocked(false);
        authUserDetails.setCredentialsNonExpired(false);
        authUserDetails.setEnable(false);
        authUserDetails.setUsername("Username");
        authUserDetails.setPassword("Version");
        authUserDetails.setAuthorities(Lists.newArrayList());
        authUserDetails.setVersion("Version");
        authUserDetails.setAgencyId("AgencyId");
        authUserDetails.setAgencyName("AgencyName");
        authUserDetails.setEmail("Email");
        authUserDetails.setRole(UserRole.AGENCY_ADMIN.toString());

        // Mock userProvider.checkCurrentUser() 方法
        when(userProvider.checkCurrentUser()).thenReturn(authUserDetails);

        // Mock AgencyModel
        // 设置 AgencyModel 的属性
        AgencyModel agencyModel = new AgencyModel();
        String agencyId = "agencyId";
        agencyModel.setId(agencyId);
        agencyModel.setName("agencyName");

        // Mock userProvider.getAgencyByUserId() 方法
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agencyModel);

        // Mock centerDao.getFirstCenterTimeZoneByAgencyId() 方法
        when(centerDao.getFirstCenterTimeZoneByAgencyId(eq(agencyId))).thenReturn("Asia/Shanghai");

        // Mock current date
        // 设置当前日期
        Date currentDate = new Date();
        try (MockedStatic<TimeUtil> timeUtil = Mockito.mockStatic(TimeUtil.class)) {
            timeUtil.when(TimeUtil::getUtcNow).thenReturn(currentDate);
            timeUtil.when(() -> TimeUtil.convertUtcToLocal(eq(currentDate), anyString())).thenReturn(currentDate);
        }

        // Mock Alias values
        // 设置别名的值
        Alias[] aliases = Alias.values();

        // Mock userDao methods
        // Mock userDao 的方法
        List<DashboardAliasModel> aliasNames = new ArrayList<>();
        int aliasNum = 1;
        for (Alias alias : aliases) {
            DashboardAliasModel aliasName = new DashboardAliasModel();
            aliasName.setAlias(alias.getName());
            aliasName.setIsActive("1");
            aliasName.setAliasNum(aliasNum++);
            aliasNames.add(aliasName);
        }

        // Mock userDao.getAliasNamesInCurrentYearByAgencyId() 方法
        when(userDao.getAliasNamesInCurrentYearByAgencyId(anyString(), anyString(), anyList(), anyList(), anyString(), anyString(), anyString(), anyBoolean())).thenReturn(aliasNames);

        // Test case
        List<PeriodAlias> aliasResult = appService.getAllAliasInCurrentYear("userId", "frameworkId", agencyId, "centerIds", "groupIds");

        // Assertion
        // 判断给定的两个参数是否相等, 判断返回的周期是不是期望的数据
        Assertions.assertEquals(Alias.list().length, aliasResult.size());
    }

    /**
     * 使用园长测试获取当年所有别名
     */
    @Test
    public void testGetAllAliasInCurrentYearWithSiteAdmin() {
        // Mock AuthUserDetails
        // 设置 AuthUserDetails 的属性
        AuthUserDetails authUserDetails = new AuthUserDetails();
        authUserDetails.setType("Type");
        authUserDetails.setDelayed(false);
        authUserDetails.setNonExpired(false);
        authUserDetails.setNonLocked(false);
        authUserDetails.setCredentialsNonExpired(false);
        authUserDetails.setEnable(false);
        authUserDetails.setUsername("Username");
        authUserDetails.setPassword("Version");
        authUserDetails.setAuthorities(Lists.newArrayList());
        authUserDetails.setVersion("Version");
        authUserDetails.setAgencyId("AgencyId");
        authUserDetails.setAgencyName("AgencyName");
        authUserDetails.setEmail("Email");
        authUserDetails.setRole(UserRole.SITE_ADMIN.toString());

        // Mock userProvider.checkCurrentUser() 方法
        when(userProvider.checkCurrentUser()).thenReturn(authUserDetails);

        // Mock AgencyModel
        // 设置 AgencyModel 的属性
        AgencyModel agencyModel = new AgencyModel();
        String agencyId = "agencyId";
        agencyModel.setId(agencyId);
        agencyModel.setName("agencyName");

        // Mock userProvider.getAgencyByUserId() 方法
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agencyModel);

        // Mock centerDao.getFirstCenterTimeZoneByAgencyId() 方法
        when(centerDao.getFirstCenterTimeZoneByAgencyId(eq(agencyId))).thenReturn("Asia/Shanghai");

        // Mock current date
        // 设置当前日期
        Date currentDate = TimeUtil.parseDate("2023-06-09 00:00:00");
        try (MockedStatic<TimeUtil> timeUtil = Mockito.mockStatic(TimeUtil.class)) {
            timeUtil.when(TimeUtil::getUtcNow).thenReturn(currentDate);
            timeUtil.when(() -> TimeUtil.convertUtcToLocal(eq(currentDate), anyString())).thenReturn(currentDate);
        }

        // Mock Alias values
        // 设置别名的值
        Alias[] aliases = Alias.values();

        // Mock userDao methods
        // Mock userDao 的方法
        List<DashboardAliasModel> aliasNames = new ArrayList<>();
        int aliasNum = 1;
        for (Alias alias : aliases) {
            DashboardAliasModel aliasName = new DashboardAliasModel();
            aliasName.setAlias(alias.getName());
            aliasName.setIsActive("1");
            aliasName.setAliasNum(aliasNum++);
            aliasNames.add(aliasName);
        }

        // Mock userDao.getAliasNamesInCurrentYearBySiteAdmin() 方法
        when(userDao.getAliasNamesInCurrentYearBySiteAdmin(anyString(), anyString(), anyList(), anyList(), anyString(), anyString(), anyString(), anyBoolean())).thenReturn(aliasNames);

        // Test case
        List<PeriodAlias> aliasResult = appService.getAllAliasInCurrentYear("userId", "frameworkId", agencyId, "centerIds", "groupIds");

        // Assertion
        // 判断给定的两个参数是否相等, 判断返回的周期是不是期望的数据
        Assertions.assertEquals(Alias.list().length, aliasResult.size());
    }


    /**
     * 测试老师获取过去年所有别名
     */
    @Test
    public void testGetAllAliasInCurrentYearWithSiteCollaborator() {
        // Mock AuthUserDetails
        // 设置 AuthUserDetails 的属性
        AuthUserDetails authUserDetails = new AuthUserDetails();
        authUserDetails.setType("Type");
        authUserDetails.setDelayed(false);
        authUserDetails.setNonExpired(false);
        authUserDetails.setNonLocked(false);
        authUserDetails.setCredentialsNonExpired(false);
        authUserDetails.setEnable(false);
        authUserDetails.setUsername("Username");
        authUserDetails.setPassword("Version");
        authUserDetails.setAuthorities(Lists.newArrayList());
        authUserDetails.setVersion("Version");
        authUserDetails.setAgencyId("AgencyId");
        authUserDetails.setAgencyName("AgencyName");
        authUserDetails.setEmail("Email");
        authUserDetails.setRole(UserRole.COLLABORATOR.toString());

        // Mock userProvider.checkCurrentUser() 方法
        when(userProvider.checkCurrentUser()).thenReturn(authUserDetails);

        // Mock AgencyModel
        // 设置 AgencyModel 的属性
        AgencyModel agencyModel = new AgencyModel();
        String agencyId = "agencyId";
        agencyModel.setId(agencyId);
        agencyModel.setName("agencyName");

        // Mock userProvider.getAgencyByUserId() 方法
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agencyModel);

        // Mock centerDao.getFirstCenterTimeZoneByAgencyId() 方法
        when(centerDao.getFirstCenterTimeZoneByAgencyId(eq(agencyId))).thenReturn("Asia/Shanghai");

        // Mock current date
        // 设置当前日期
        Date currentDate = TimeUtil.parseDate("2023-06-09 00:00:00");
        try (MockedStatic<TimeUtil> timeUtil = Mockito.mockStatic(TimeUtil.class)) {
            timeUtil.when(TimeUtil::getUtcNow).thenReturn(currentDate);
            timeUtil.when(() -> TimeUtil.convertUtcToLocal(eq(currentDate), anyString())).thenReturn(currentDate);
            // Mock Alias values
            Alias[] aliases = Alias.values();

            // Mock userDao methods
            List<DashboardAliasModel> aliasNames = new ArrayList<>();
            int aliasNum = 1;
            for (Alias alias : aliases) {
                DashboardAliasModel aliasName = new DashboardAliasModel();
                aliasName.setAlias(alias.getName());
                aliasName.setIsActive("1");
                aliasName.setAliasNum(aliasNum++);
                aliasNames.add(aliasName);
            }

            // Test case
            List<PeriodAlias> aliasResult = appService.getAllAliasInCurrentYear("userId", "frameworkId", agencyId, "centerIds", "groupIds");

            // Assertion
            // 判断给定的两个参数是否相等, 判断返回的周期是不是期望的数据
            Assertions.assertEquals(0, aliasResult.size());
        }
    }

    /**
     * 测试获取当前年份中所有别名被授予者
     */
    @Test
    public void testGetAllAliasInCurrentYearGrantee() {
        // Mock AuthUserDetails
        AuthUserDetails authUserDetails = new AuthUserDetails();
        authUserDetails.setType(UserType.GRANTEE.toString());
        authUserDetails.setDelayed(false);
        authUserDetails.setNonExpired(false);
        authUserDetails.setNonLocked(false);
        authUserDetails.setCredentialsNonExpired(false);
        authUserDetails.setEnable(false);
        authUserDetails.setUsername("Username");
        authUserDetails.setPassword("Version");
        authUserDetails.setAuthorities(Lists.newArrayList());
        authUserDetails.setVersion("Version");
        authUserDetails.setAgencyId("AgencyId");
        authUserDetails.setAgencyName("AgencyName");
        authUserDetails.setEmail("Email");
        authUserDetails.setRole(UserRole.AGENCY_ADMIN.toString());

        when(userProvider.checkCurrentUser()).thenReturn(authUserDetails);

        // Mock AgencyModel
        AgencyModel agencyModel = new AgencyModel();
        String agencyId = "agencyId";
        agencyModel.setId(agencyId);
        agencyModel.setName("agencyName");

        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agencyModel);
        when(centerDao.getFirstCenterTimeZoneByAgencyId(eq(agencyId))).thenReturn("Asia/Shanghai");

        // Mock current date
        Date currentDate = new Date();
        try (MockedStatic<TimeUtil> timeUtil = Mockito.mockStatic(TimeUtil.class)) {
            timeUtil.when(TimeUtil::getUtcNow).thenReturn(currentDate);
            timeUtil.when(() -> TimeUtil.convertUtcToLocal(eq(currentDate), anyString())).thenReturn(currentDate);
        }

        // Mock Alias values
        Alias[] aliases = Alias.values();

        // Mock userDao methods
        List<DashboardAliasModel> aliasNames = new ArrayList<>();
        int aliasNum = 1;
        for (Alias alias : aliases) {
            DashboardAliasModel aliasName = new DashboardAliasModel();
            aliasName.setAlias(alias.getName());
            aliasName.setIsActive("1");
            aliasName.setAliasNum(aliasNum++);
            aliasNames.add(aliasName);
        }

        when(userDao.getAliasNamesInCurrentYearByAgencyId(anyString(), anyString(), anyList(), anyList(), anyString(), anyString(), anyString(), anyBoolean())).thenReturn(aliasNames);

        ArrayList<AgencyIdentifierEntity> agencyIdentifierEntities = new ArrayList<>();
        AgencyIdentifierEntity agencyIdentifierEntity = new AgencyIdentifierEntity();
        agencyIdentifierEntity.setName("name1");
        agencyIdentifierEntities.add(agencyIdentifierEntity);

        AgencyIdentifierEntity agencyIdentifierEntity2 = new AgencyIdentifierEntity();
        agencyIdentifierEntity2.setName("name2");
        agencyIdentifierEntities.add(agencyIdentifierEntity2);
        when(agencyDao.getIdentifierByUserId(anyString())).thenReturn(agencyIdentifierEntities);

        when(userDao.getAliasInCurrentYearByIdentifiers(anyList(), anyString(), anyString(), anyString(), anyList(), anyList(), anyString(), anyBoolean())).thenReturn(aliasNames);
        // Test case
        List<PeriodAlias> aliasResult = appService.getAllAliasInCurrentYear("userId", "frameworkId", agencyId, "centerIds", "groupIds");

        // Assertion
        // 判断给定的两个参数是否相等, 判断返回的周期是不是期望的数据
        Assertions.assertEquals(Alias.list().length, aliasResult.size());
    }
}

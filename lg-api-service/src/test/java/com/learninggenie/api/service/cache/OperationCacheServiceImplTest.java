package com.learninggenie.api.service.cache;

import com.learninggenie.api.service.PortfolioService;
import com.learninggenie.api.service.cache.impl.OperationCacheServiceImpl;
import com.learninggenie.api.service.impl.PortfolioServiceImpl;
import com.learninggenie.common.cache.CacheService;
import com.learninggenie.common.constant.cachekey.FrameworkKey;
import com.learninggenie.common.data.dao.frameworks.FrameworkDao;
import com.learninggenie.common.data.dao.frameworks.impl.FrameworkDaoImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * OperationCacheServiceImplTest
 */
@RunWith(MockitoJUnitRunner.class)
public class OperationCacheServiceImplTest {
	@Mock
	private CacheService redisCacheServiceImpl;

	@InjectMocks
	private OperationCacheServiceImpl operationCacheService;

	@Mock
	private PortfolioService portfolioService;

	@Mock
	private FrameworkDao frameworkDao;


	@Test
	public void testDeleteAllFrameworkCache() {
		List<String> countries = new ArrayList<>();
		countries.add("country1");
		List<String> states = new ArrayList<>();
		states.add("states1");
		states.add("states2");
		states.add("states3");
		when(frameworkDao.getCountries()).thenReturn(countries);
		when(portfolioService.getStates("country1")).thenReturn(states);
		boolean result = operationCacheService.deleteAllFrameworkCache();

		assertTrue(result);
		verify(redisCacheServiceImpl).delete(FrameworkKey.FRAMEWORK_ALL_CACHE_KEY + "country1");
	}

	@Test
	public void testDeleteStateFrameworkCache_ValidState() {
		String state = "CA";
		boolean result = operationCacheService.deleteStateFrameworkCache(state);
		assertTrue(result);
		verify(redisCacheServiceImpl).delete(FrameworkKey.FRAMEWORK_STATE_CACHE_KEY + state);
	}

	@Test
	public void testDeleteStateFrameworkCache_BlankState() {
		String blankState = "";
		boolean result = operationCacheService.deleteStateFrameworkCache(blankState);
		assertFalse(result);
	}

	@Test
	public void testDeleteStateFrameworkCache_NullState() {
		String nullState = null;
		boolean result = operationCacheService.deleteStateFrameworkCache(nullState);
		assertFalse(result);
	}
}

package com.learninggenie.api.service.impl;

import com.google.common.collect.Lists;
import com.learninggenie.common.cache.CacheModel;
import com.learninggenie.common.cache.CacheService;
import com.learninggenie.common.constant.CacheKey;
import com.learninggenie.common.data.model.drdp2.AddDrdpUserRoleResponse;
import com.learninggenie.common.data.model.drdp2.ChildProficienciesModel;
import com.learninggenie.common.data.model.drdp2.CreateDrdpAgencyUserModel;
import com.learninggenie.common.data.model.drdp2.CreateDrdpClassModel;
import com.learninggenie.common.data.model.drdp2.CreateDrdpClassResponse;
import com.learninggenie.common.data.model.drdp2.CreateDrdpClassroomModel;
import com.learninggenie.common.data.model.drdp2.CreateDrdpClassroomResponse;
import com.learninggenie.common.data.model.drdp2.CreateDrdpSiteModel;
import com.learninggenie.common.data.model.drdp2.CreateDrdpSiteResponse;
import com.learninggenie.common.data.model.drdp2.CreateDrdpUserResponse;
import com.learninggenie.common.data.model.drdp2.DeleteDrdpClassroomResponse;
import com.learninggenie.common.data.model.drdp2.DeleteDrdpUserRoleResponse;
import com.learninggenie.common.data.model.drdp2.DrdpAgencyModel;
import com.learninggenie.common.data.model.drdp2.DrdpAuthModel;
import com.learninggenie.common.data.model.drdp2.DrdpBatchScoringModel;
import com.learninggenie.common.data.model.drdp2.DrdpBatchScoringResponse;
import com.learninggenie.common.data.model.drdp2.DrdpClassModel;
import com.learninggenie.common.data.model.drdp2.DrdpClassroomModel;
import com.learninggenie.common.data.model.drdp2.DrdpCountyModel;
import com.learninggenie.common.data.model.drdp2.DrdpDomainValueModel;
import com.learninggenie.common.data.model.drdp2.DrdpRoleModel;
import com.learninggenie.common.data.model.drdp2.DrdpSingleScoringResponse;
import com.learninggenie.common.data.model.drdp2.DrdpSiteModel;
import com.learninggenie.common.data.model.drdp2.DrdpStateModel;
import com.learninggenie.common.data.model.drdp2.DrdpStudentScoreModel;
import com.learninggenie.common.data.model.drdp2.DrdpTermModel;
import com.learninggenie.common.data.model.drdp2.DrdpUserModel;
import com.learninggenie.common.data.model.drdp2.GetAgencySitesResponse;
import com.learninggenie.common.data.model.drdp2.GetAgencyTermsResponse;
import com.learninggenie.common.data.model.drdp2.GetAllAgencyResponse;
import com.learninggenie.common.data.model.drdp2.GetClassroomClassesResponse;
import com.learninggenie.common.data.model.drdp2.GetDrdpBatchScoringResultResponse;
import com.learninggenie.common.data.model.drdp2.GetDrdpCountiesResponse;
import com.learninggenie.common.data.model.drdp2.GetDrdpRolesResponse;
import com.learninggenie.common.data.model.drdp2.GetDrdpStatesResponse;
import com.learninggenie.common.data.model.drdp2.GetDrdpUsersResponse;
import com.learninggenie.common.data.model.drdp2.GetSiteClassroomsResponse;
import com.learninggenie.common.data.model.drdp2.ProficiencyModel;
import com.learninggenie.common.data.model.drdp2.UpdateDrdpClassModel;
import com.learninggenie.common.data.model.drdp2.UpdateDrdpClassResponse;
import com.learninggenie.common.data.model.drdp2.UpdateDrdpClassroomModel;
import com.learninggenie.common.data.model.drdp2.UpdateDrdpClassroomResponse;
import com.learninggenie.common.data.model.drdp2.UpdateDrdpSiteModel;
import com.learninggenie.common.data.model.drdp2.UpdateDrdpSiteResponse;
import com.learninggenie.common.data.model.drdp2.UpdateDrdpUserModel;
import com.learninggenie.common.data.model.drdp2.UpdateDrdpUserResponse;
import com.learninggenie.common.sync.DrdpV2ServiceImpl;
import com.learninggenie.common.utils.JsonUtil;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.junit.Assert.assertEquals;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;

@RunWith(MockitoJUnitRunner.class)
public class DrdpV2ServiceImplTest {

    @InjectMocks
    DrdpV2ServiceImpl drdpV2Service;

    @Mock
    private CacheService cacheService;

    @Mock
    private RestTemplate restTemplate;


    @Test
    public void testLogin() {
        when(cacheService.exist(anyString())).thenReturn(true);

        DrdpAuthModel expectedAuthModel = new DrdpAuthModel();
        expectedAuthModel.setExpires(900);
        ResponseEntity<String> responseEntity = new ResponseEntity<>(JsonUtil.toJson(expectedAuthModel), HttpStatus.OK);
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class))).thenReturn(responseEntity);

        DrdpAuthModel drdpAuth = drdpV2Service.login();
        Assert.assertEquals(expectedAuthModel.getExpires(), drdpAuth.getExpires());
    }

    @Test
    public void testGetAllAgencies() {
        CacheModel cacheModel = new CacheModel();
        cacheModel.setKey(CacheKey.DRDP2_AUTH);
        DrdpAuthModel drdpAuth = new DrdpAuthModel();
        drdpAuth.setAccessToken("1");
        cacheModel.setValue(JsonUtil.toJson(drdpAuth));
        when(cacheService.get(CacheKey.DRDP2_AUTH)).thenReturn(cacheModel);


        List<DrdpAgencyModel> agencies = Lists.newArrayList();
        DrdpAgencyModel drdpAgency = new DrdpAgencyModel();
        drdpAgency.setId(1);
        agencies.add(drdpAgency);
        ResponseEntity<String> responseEntity = new ResponseEntity<>(JsonUtil.toJson(agencies), HttpStatus.OK);
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class))).thenReturn(responseEntity);

        GetAllAgencyResponse allAgencies = drdpV2Service.getAllAgencies();

        assertEquals(1, allAgencies.getAgencies().size());
    }

    @Test
    public void testAgencySite() {
        String drdpAgencyId = "1";

        CacheModel cacheModel = new CacheModel();
        cacheModel.setKey(CacheKey.DRDP2_AUTH);
        DrdpAuthModel drdpAuth = new DrdpAuthModel();
        drdpAuth.setAccessToken("1");
        cacheModel.setValue(JsonUtil.toJson(drdpAuth));
        when(cacheService.get(CacheKey.DRDP2_AUTH)).thenReturn(cacheModel);


        List<DrdpSiteModel> drdpSites = Lists.newArrayList();
        DrdpSiteModel drdpSite = new DrdpSiteModel();
        drdpSite.setId(1);
        drdpSites.add(drdpSite);
        ResponseEntity<String> responseEntity = new ResponseEntity<>(JsonUtil.toJson(drdpSites), HttpStatus.OK);
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class))).thenReturn(responseEntity);

        GetAgencySitesResponse agencySites = drdpV2Service.getAgencySites(drdpAgencyId, null, null);
        assertEquals(1, agencySites.getSites().size());
    }

    @Test
    public void testCreateAgencySite() {
        String drdpAgencyId = "1";
        CreateDrdpSiteModel createDrdpSiteModel = new CreateDrdpSiteModel();

        CacheModel cacheModel = new CacheModel();
        cacheModel.setKey(CacheKey.DRDP2_AUTH);
        DrdpAuthModel drdpAuth = new DrdpAuthModel();
        drdpAuth.setAccessToken("1");
        cacheModel.setValue(JsonUtil.toJson(drdpAuth));
        when(cacheService.get(CacheKey.DRDP2_AUTH)).thenReturn(cacheModel);


        ResponseEntity<String> responseEntity = new ResponseEntity<>("1", HttpStatus.OK);
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class))).thenReturn(responseEntity);

        CreateDrdpSiteResponse agencySite = drdpV2Service.createAgencySite(drdpAgencyId, createDrdpSiteModel);
        assertThat(agencySite.getId(), is(1));
    }

    @Test
    public void getAgencyTerms() {
        String drdpAgencyId = "1";
        String termName = "Fall 2023";

        CacheModel cacheModel = new CacheModel();
        cacheModel.setKey(CacheKey.DRDP2_AUTH);
        DrdpAuthModel drdpAuth = new DrdpAuthModel();
        drdpAuth.setAccessToken("1");
        cacheModel.setValue(JsonUtil.toJson(drdpAuth));
        when(cacheService.get(CacheKey.DRDP2_AUTH)).thenReturn(cacheModel);

        List<DrdpTermModel> drdpTerms = Lists.newArrayList();
        DrdpTermModel drdpTerm = new DrdpTermModel();
        drdpTerm.setId(1);
        drdpTerms.add(drdpTerm);

        ResponseEntity<String> responseEntity = new ResponseEntity<>(JsonUtil.toJson(drdpTerms), HttpStatus.OK);
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class))).thenReturn(responseEntity);

        GetAgencyTermsResponse agencyTerms = drdpV2Service.getAgencyTerms(drdpAgencyId, termName);

        assertThat(agencyTerms.getTerms().size(), is(1));
        assertThat(agencyTerms.getTerms().get(0).getId(), is(1));
    }

    @Test
    public void testCreateAgencyUser() {
        String drdpAgencyId = "1";
        CreateDrdpAgencyUserModel createDrdpAgencyUserModel = new CreateDrdpAgencyUserModel();

        CacheModel cacheModel = new CacheModel();
        cacheModel.setKey(CacheKey.DRDP2_AUTH);
        DrdpAuthModel drdpAuth = new DrdpAuthModel();
        drdpAuth.setAccessToken("1");
        cacheModel.setValue(JsonUtil.toJson(drdpAuth));
        when(cacheService.get(CacheKey.DRDP2_AUTH)).thenReturn(cacheModel);

        ResponseEntity<String> responseEntity = new ResponseEntity<>("1", HttpStatus.OK);
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class))).thenReturn(responseEntity);

        CreateDrdpUserResponse agencyUser = drdpV2Service.createAgencyUser(drdpAgencyId, createDrdpAgencyUserModel);

        assertThat(agencyUser.getId(), is(1));
    }

    @Test
    public void testGetClassroomClasses() {
        String classroomId = "1";
        String termId = "1";

        CacheModel cacheModel = new CacheModel();
        cacheModel.setKey(CacheKey.DRDP2_AUTH);
        DrdpAuthModel drdpAuth = new DrdpAuthModel();
        drdpAuth.setAccessToken("1");
        cacheModel.setValue(JsonUtil.toJson(drdpAuth));
        when(cacheService.get(CacheKey.DRDP2_AUTH)).thenReturn(cacheModel);

        List<DrdpClassModel> classes = Lists.newArrayList();
        DrdpClassModel drdpClass = new DrdpClassModel();
        drdpClass.setId(1);

        classes.add(drdpClass);

        ResponseEntity<String> responseEntity = new ResponseEntity<>(JsonUtil.toJson(classes), HttpStatus.OK);
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class))).thenReturn(responseEntity);

        GetClassroomClassesResponse classroomClasses = drdpV2Service.getClassroomClasses(classroomId, termId);

        assertEquals(1, classroomClasses.getClasses().size());
        assertEquals(1, (int) classroomClasses.getClasses().get(0).getId());
    }

    @Test
    public void testCreateClassroomClass() {
        String classroomId = "1";
        CreateDrdpClassModel createDrdpClassModel = new CreateDrdpClassModel();

        CacheModel cacheModel = new CacheModel();
        cacheModel.setKey(CacheKey.DRDP2_AUTH);
        DrdpAuthModel drdpAuth = new DrdpAuthModel();
        drdpAuth.setAccessToken("1");
        cacheModel.setValue(JsonUtil.toJson(drdpAuth));
        when(cacheService.get(CacheKey.DRDP2_AUTH)).thenReturn(cacheModel);

        ResponseEntity<String> responseEntity = new ResponseEntity<>("1", HttpStatus.OK);
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class))).thenReturn(responseEntity);

        CreateDrdpClassResponse classroomClass = drdpV2Service.createClassroomClass(classroomId, createDrdpClassModel);

        assertEquals(1, (int) classroomClass.getId());
    }

    @Test
    public void testUpdateClassroom() {
        String classroomId = "1";
        UpdateDrdpClassroomModel updateDrdpClassroomModel = new UpdateDrdpClassroomModel();

        CacheModel cacheModel = new CacheModel();
        cacheModel.setKey(CacheKey.DRDP2_AUTH);
        DrdpAuthModel drdpAuth = new DrdpAuthModel();
        drdpAuth.setAccessToken("1");
        cacheModel.setValue(JsonUtil.toJson(drdpAuth));
        when(cacheService.get(CacheKey.DRDP2_AUTH)).thenReturn(cacheModel);

        ResponseEntity<String> responseEntity = new ResponseEntity<>("1", HttpStatus.OK);
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class))).thenReturn(responseEntity);

        UpdateDrdpClassroomResponse updateDrdpClassroomResponse = drdpV2Service.updateClassroom(classroomId, updateDrdpClassroomModel);

        assertEquals(1, (int) updateDrdpClassroomResponse.getId());
    }

    @Test
    public void testUpdateClass() {
        String classId = "1";
        UpdateDrdpClassModel updateDrdpClassModel = new UpdateDrdpClassModel();

        CacheModel cacheModel = new CacheModel();
        cacheModel.setKey(CacheKey.DRDP2_AUTH);
        DrdpAuthModel drdpAuth = new DrdpAuthModel();
        drdpAuth.setAccessToken("1");
        cacheModel.setValue(JsonUtil.toJson(drdpAuth));
        when(cacheService.get(CacheKey.DRDP2_AUTH)).thenReturn(cacheModel);

        ResponseEntity<String> responseEntity = new ResponseEntity<>("1", HttpStatus.OK);
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class))).thenReturn(responseEntity);

        UpdateDrdpClassResponse updateDrdpClassResponse = drdpV2Service.updateClass(classId, updateDrdpClassModel);

        assertEquals(1, (int) updateDrdpClassResponse.getId());
    }

    @Test
    public void testGetSiteClassrooms() {
        String drdpSiteId = "1";

        CacheModel cacheModel = new CacheModel();
        cacheModel.setKey(CacheKey.DRDP2_AUTH);
        DrdpAuthModel drdpAuth = new DrdpAuthModel();
        drdpAuth.setAccessToken("1");
        cacheModel.setValue(JsonUtil.toJson(drdpAuth));
        when(cacheService.get(CacheKey.DRDP2_AUTH)).thenReturn(cacheModel);

        List<DrdpClassroomModel> classrooms = Lists.newArrayList();
        DrdpClassroomModel drdpClassroom = new DrdpClassroomModel();
        drdpClassroom.setId(1);

        classrooms.add(drdpClassroom);
        ResponseEntity<String> responseEntity = new ResponseEntity<>(JsonUtil.toJson(classrooms), HttpStatus.OK);
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class))).thenReturn(responseEntity);

        GetSiteClassroomsResponse siteClassrooms = drdpV2Service.getSiteClassrooms(drdpSiteId, null, null);

        assertEquals(1, siteClassrooms.getClassrooms().size());
        assertEquals(1, (int) siteClassrooms.getClassrooms().get(0).getId());
    }

    @Test
    public void testCreateSiteClassroom() {
        String drdpSiteId = "1";
        CreateDrdpClassroomModel createDrdpClassroomModel = new CreateDrdpClassroomModel();

        CacheModel cacheModel = new CacheModel();
        cacheModel.setKey(CacheKey.DRDP2_AUTH);
        DrdpAuthModel drdpAuth = new DrdpAuthModel();
        drdpAuth.setAccessToken("1");
        cacheModel.setValue(JsonUtil.toJson(drdpAuth));
        when(cacheService.get(CacheKey.DRDP2_AUTH)).thenReturn(cacheModel);

        ResponseEntity<String> responseEntity = new ResponseEntity<>("1", HttpStatus.OK);
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class))).thenReturn(responseEntity);

        CreateDrdpClassroomResponse siteClassroom = drdpV2Service.createSiteClassroom(drdpSiteId, createDrdpClassroomModel);

        assertEquals(1, (int) siteClassroom.getId());
    }

    @Test
    public void updateDrdpSite() {
        String drdpSiteId = "1";
        UpdateDrdpSiteModel updateDrdpSiteModel = new UpdateDrdpSiteModel();

        CacheModel cacheModel = new CacheModel();
        cacheModel.setKey(CacheKey.DRDP2_AUTH);
        DrdpAuthModel drdpAuth = new DrdpAuthModel();
        drdpAuth.setAccessToken("1");
        cacheModel.setValue(JsonUtil.toJson(drdpAuth));
        when(cacheService.get(CacheKey.DRDP2_AUTH)).thenReturn(cacheModel);

        ResponseEntity<String> responseEntity = new ResponseEntity<>("1", HttpStatus.OK);
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class))).thenReturn(responseEntity);

        UpdateDrdpSiteResponse updateDrdpSiteResponse = drdpV2Service.updateDrdpSite(drdpSiteId, updateDrdpSiteModel);

        assertEquals(1, (int) updateDrdpSiteResponse.getId());
    }

    @Test
    public void testGetRoles() {
        CacheModel cacheModel = new CacheModel();
        cacheModel.setKey(CacheKey.DRDP2_AUTH);
        DrdpAuthModel drdpAuth = new DrdpAuthModel();
        drdpAuth.setAccessToken("1");
        cacheModel.setValue(JsonUtil.toJson(drdpAuth));
        when(cacheService.get(CacheKey.DRDP2_AUTH)).thenReturn(cacheModel);

        List<DrdpRoleModel> roles = Lists.newArrayList();
        DrdpRoleModel role = new DrdpRoleModel();
        role.setId(1);

        roles.add(role);
        ResponseEntity<String> responseEntity = new ResponseEntity<>(JsonUtil.toJson(roles), HttpStatus.OK);
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class))).thenReturn(responseEntity);

        GetDrdpRolesResponse drdpRoles = drdpV2Service.getRoles();

        assertEquals(1, drdpRoles.getRoles().size());
        assertEquals(1, (int) drdpRoles.getRoles().get(0).getId());
    }

    @Test
    public void testDeleteUserRole() {
        String drdpUserId = "1";
        String drdpRoleId = "1";

        CacheModel cacheModel = new CacheModel();
        cacheModel.setKey(CacheKey.DRDP2_AUTH);
        DrdpAuthModel drdpAuth = new DrdpAuthModel();
        drdpAuth.setAccessToken("1");
        cacheModel.setValue(JsonUtil.toJson(drdpAuth));
        when(cacheService.get(CacheKey.DRDP2_AUTH)).thenReturn(cacheModel);

        ResponseEntity<String> responseEntity = new ResponseEntity<>(drdpUserId, HttpStatus.OK);
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class))).thenReturn(responseEntity);

        DeleteDrdpUserRoleResponse deleteDrdpUserRoleResponse = drdpV2Service.deleteUserRole(drdpUserId, drdpRoleId);

        assertEquals(1, (int) deleteDrdpUserRoleResponse.getId());
    }

    @Test
    public void testAddUserRole() {
        String drdpUserId = "1";
        String drdpRoleId = "1";

        CacheModel cacheModel = new CacheModel();
        cacheModel.setKey(CacheKey.DRDP2_AUTH);
        DrdpAuthModel drdpAuth = new DrdpAuthModel();
        drdpAuth.setAccessToken("1");
        cacheModel.setValue(JsonUtil.toJson(drdpAuth));
        when(cacheService.get(CacheKey.DRDP2_AUTH)).thenReturn(cacheModel);

        ResponseEntity<String> responseEntity = new ResponseEntity<>(drdpUserId, HttpStatus.OK);
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class))).thenReturn(responseEntity);

        AddDrdpUserRoleResponse addDrdpUserRoleResponse = drdpV2Service.addUserRole(drdpUserId, drdpRoleId);

        assertEquals(1, (int) addDrdpUserRoleResponse.getId());
    }

    @Test
    public void testUpdateUser() {
        String drdpUserId = "1";
        UpdateDrdpUserModel updateDrdpUserModel = new UpdateDrdpUserModel();

        CacheModel cacheModel = new CacheModel();
        cacheModel.setKey(CacheKey.DRDP2_AUTH);
        DrdpAuthModel drdpAuth = new DrdpAuthModel();
        drdpAuth.setAccessToken("1");
        cacheModel.setValue(JsonUtil.toJson(drdpAuth));
        when(cacheService.get(CacheKey.DRDP2_AUTH)).thenReturn(cacheModel);

        ResponseEntity<String> responseEntity = new ResponseEntity<>(drdpUserId, HttpStatus.OK);
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class))).thenReturn(responseEntity);

        UpdateDrdpUserResponse updateDrdpUserResponse = drdpV2Service.updateUser(drdpUserId, updateDrdpUserModel);

        assertEquals(1, (int) updateDrdpUserResponse.getId());
    }

    @Test
    public void testGetUsers() {
        CacheModel cacheModel = new CacheModel();
        cacheModel.setKey(CacheKey.DRDP2_AUTH);
        DrdpAuthModel drdpAuth = new DrdpAuthModel();
        drdpAuth.setAccessToken("1");
        cacheModel.setValue(JsonUtil.toJson(drdpAuth));
        when(cacheService.get(CacheKey.DRDP2_AUTH)).thenReturn(cacheModel);

        List<DrdpUserModel> users = Lists.newArrayList();
        DrdpUserModel user = new DrdpUserModel();
        user.setId(1);

        users.add(user);

        ResponseEntity<String> responseEntity = new ResponseEntity<>(JsonUtil.toJson(users), HttpStatus.OK);
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class))).thenReturn(responseEntity);

        GetDrdpUsersResponse usersResponse = drdpV2Service.getUsers(null, null);

        assertEquals(1, usersResponse.getUsers().size());
        assertEquals(1, (int) usersResponse.getUsers().get(0).getId());
    }

    @Test
    public void testBatchScoring() {

        DrdpBatchScoringModel drdpBatchScoringModel = new DrdpBatchScoringModel();
        drdpBatchScoringModel.setRatings(new ArrayList<>());
        // 模拟返回的用户信息
        DrdpAuthModel drdpAuthModel = new DrdpAuthModel();
        drdpAuthModel.setAccessToken("1");

        CacheModel cacheModel = new CacheModel();
        cacheModel.setKey(CacheKey.DRDP2_AUTH);
        cacheModel.setValue(JsonUtil.toJson(drdpAuthModel));

        when(cacheService.get(CacheKey.DRDP2_AUTH)).thenReturn(cacheModel);

        ResponseEntity<String> responseEntity = new ResponseEntity<>("1", HttpStatus.OK);
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class))).thenReturn(responseEntity);

        DrdpBatchScoringResponse drdpBatchScoringResponse = drdpV2Service.batchScoring(drdpBatchScoringModel, false);

        // 校验返回结果
        assertEquals(1, (int) drdpBatchScoringResponse.getId());
    }

    @Test
    public void testBatchScoring_withoutRetry() {

        DrdpBatchScoringModel drdpBatchScoringModel = new DrdpBatchScoringModel();
        drdpBatchScoringModel.setRatings(new ArrayList<>());
        // 模拟返回的用户信息
        DrdpAuthModel drdpAuthModel = new DrdpAuthModel();
        drdpAuthModel.setAccessToken("1");

        CacheModel cacheModel = new CacheModel();
        cacheModel.setKey(CacheKey.DRDP2_AUTH);
        cacheModel.setValue(JsonUtil.toJson(drdpAuthModel));

        when(cacheService.get(CacheKey.DRDP2_AUTH)).thenReturn(cacheModel);

        ResponseEntity<String> responseEntity = new ResponseEntity<>("1", HttpStatus.OK);
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class))).thenReturn(responseEntity);

        DrdpBatchScoringResponse drdpBatchScoringResponse = drdpV2Service.batchScoring(drdpBatchScoringModel, false);

        // 校验返回结果
        assertEquals(1, (int) drdpBatchScoringResponse.getId());
    }

    @Test
    public void testGetBatchScoringResult() {

        DrdpAuthModel drdpAuthModel = new DrdpAuthModel();
        drdpAuthModel.setAccessToken("1");

        CacheModel cacheModel = new CacheModel();
        cacheModel.setKey(CacheKey.DRDP2_AUTH);
        cacheModel.setValue(JsonUtil.toJson(drdpAuthModel));

        when(cacheService.get(CacheKey.DRDP2_AUTH)).thenReturn(cacheModel);

        DrdpStudentScoreModel drdpBatchScoringModel = new DrdpStudentScoreModel();

        DrdpDomainValueModel drdpDomainValueModel = new DrdpDomainValueModel();
        drdpDomainValueModel.setDomain("a");
        drdpDomainValueModel.setValue(0.0);
        List<DrdpDomainValueModel> proficiencies = Collections.singletonList(drdpDomainValueModel);
        drdpBatchScoringModel.setProficiencies(proficiencies);

        List<DrdpStudentScoreModel> studentScores = Collections.singletonList(drdpBatchScoringModel);

        GetDrdpBatchScoringResultResponse getDrdpBatchScoringResultResponse = new GetDrdpBatchScoringResultResponse();
        getDrdpBatchScoringResultResponse.setStudentScores(studentScores);

        ResponseEntity<String> responseEntity = new ResponseEntity<>(JsonUtil.toJson(studentScores), HttpStatus.OK);
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class))).thenReturn(responseEntity);

        String batchId = "1";
        GetDrdpBatchScoringResultResponse batchScoringResult = drdpV2Service.getBatchScoringResult(batchId);

        List<DrdpStudentScoreModel> batchScoringResultStudentScores = batchScoringResult.getStudentScores();
        assertEquals(1, batchScoringResultStudentScores.size());
        assertEquals(1, batchScoringResultStudentScores.get(0).getProficiencies().size());
        assertEquals("a", batchScoringResultStudentScores.get(0).getProficiencies().get(0).getDomain());
        assertEquals(0.0, batchScoringResultStudentScores.get(0).getProficiencies().get(0).getValue(), 0.0001);


    }

    @Test
    public void testGetBatchScoringResult_returnError() {
        DrdpAuthModel drdpAuthModel = new DrdpAuthModel();
        drdpAuthModel.setAccessToken("1");

        CacheModel cacheModel = new CacheModel();
        cacheModel.setKey(CacheKey.DRDP2_AUTH);
        cacheModel.setValue(JsonUtil.toJson(drdpAuthModel));

        when(cacheService.get(CacheKey.DRDP2_AUTH)).thenReturn(cacheModel);

        GetDrdpBatchScoringResultResponse getDrdpBatchScoringResultResponse = new GetDrdpBatchScoringResultResponse();
        getDrdpBatchScoringResultResponse.setStatus(401);

        ResponseEntity<String> responseEntity = new ResponseEntity<>(JsonUtil.toJson(getDrdpBatchScoringResultResponse), HttpStatus.INTERNAL_SERVER_ERROR);
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class))).thenReturn(responseEntity);

        String batchId = "1";
        GetDrdpBatchScoringResultResponse batchScoringResult = drdpV2Service.getBatchScoringResult(batchId);

        assertEquals(401, (int) batchScoringResult.getStatus());
    }

    @Test
    public void testGetStates() {
        CacheModel cacheModel = new CacheModel();
        cacheModel.setKey(CacheKey.DRDP2_AUTH);
        DrdpAuthModel drdpAuth = new DrdpAuthModel();
        drdpAuth.setAccessToken("1");
        cacheModel.setValue(JsonUtil.toJson(drdpAuth));
        when(cacheService.get(CacheKey.DRDP2_AUTH)).thenReturn(cacheModel);

        List<DrdpStateModel> states = Lists.newArrayList();
        DrdpStateModel state = new DrdpStateModel();
        state.setId(1);

        states.add(state);

        ResponseEntity<String> responseEntity = new ResponseEntity<>(JsonUtil.toJson(states), HttpStatus.OK);
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class))).thenReturn(responseEntity);

        GetDrdpStatesResponse statesResponse = drdpV2Service.getStates(null);

        assertEquals(1, statesResponse.getStates().size());
        assertEquals(1, (int) statesResponse.getStates().get(0).getId());
    }

    @Test
    public void testGetCounties() {
        CacheModel cacheModel = new CacheModel();
        cacheModel.setKey(CacheKey.DRDP2_AUTH);
        DrdpAuthModel drdpAuth = new DrdpAuthModel();
        drdpAuth.setAccessToken("1");
        cacheModel.setValue(JsonUtil.toJson(drdpAuth));
        when(cacheService.get(CacheKey.DRDP2_AUTH)).thenReturn(cacheModel);

        List<DrdpCountyModel> counties = Lists.newArrayList();
        DrdpCountyModel county = new DrdpCountyModel();
        county.setId(1);

        counties.add(county);

        ResponseEntity<String> responseEntity = new ResponseEntity<>(JsonUtil.toJson(counties), HttpStatus.OK);
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class))).thenReturn(responseEntity);

        GetDrdpCountiesResponse countiesResponse = drdpV2Service.getCounties(null, null);

        assertEquals(1, countiesResponse.getCounties().size());
        assertEquals(1, (int) countiesResponse.getCounties().get(0).getId());
    }

    @Test
    public void testUpdateHeaderToken() {
        DrdpAuthModel expectedAuthModel = new DrdpAuthModel();
        String token = UUID.randomUUID().toString();
        expectedAuthModel.setExpires(900);
        expectedAuthModel.setAccessToken(token);

        ResponseEntity<String> responseEntity = new ResponseEntity<>(JsonUtil.toJson(expectedAuthModel), HttpStatus.OK);
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class))).thenReturn(responseEntity);

        Map<String, String> headerToken = drdpV2Service.updateHeaderToken();

        assertEquals("Bearer " + token, headerToken.get("Authorization"));
    }

    @Test
    public void testSingleScoring() {

        DrdpAuthModel drdpAuthModel = new DrdpAuthModel();
        drdpAuthModel.setAccessToken("1");

        CacheModel cacheModel = new CacheModel();
        cacheModel.setKey(CacheKey.DRDP2_AUTH);
        cacheModel.setValue(JsonUtil.toJson(drdpAuthModel));

        when(cacheService.get(CacheKey.DRDP2_AUTH)).thenReturn(cacheModel);

        // 填充api请求的返回结果
        ChildProficienciesModel childProficienciesModel = new ChildProficienciesModel();
        childProficienciesModel.setChildId("1");
        ProficiencyModel proficiencyModel = new ProficiencyModel();
        proficiencyModel.setDomain("a");
        proficiencyModel.setValue(0.0);
        List<ProficiencyModel> proficiencies = Collections.singletonList(proficiencyModel);
        childProficienciesModel.setProficiencies(proficiencies);
        List<ChildProficienciesModel> childProficiencies = Collections.singletonList(childProficienciesModel);

        // 设置api请求的返回结果
        ResponseEntity<String> responseEntity = new ResponseEntity<>(JsonUtil.toJson(childProficiencies), HttpStatus.OK);
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class))).thenReturn(responseEntity);

        DrdpSingleScoringResponse singleScoringResponse = drdpV2Service.singleScoring(null);

        // 对结果进行断言判断
        List<ChildProficienciesModel> singleScoringResponseChildProficiencies = singleScoringResponse.getChildProficiencies();
        assertEquals(1, singleScoringResponseChildProficiencies.size());
        assertEquals("1",singleScoringResponseChildProficiencies.get(0).getChildId());
        assertEquals(1,singleScoringResponseChildProficiencies.get(0).getProficiencies().size());
        assertEquals("a",singleScoringResponseChildProficiencies.get(0).getProficiencies().get(0).getDomain());
        assertEquals(0.0,singleScoringResponseChildProficiencies.get(0).getProficiencies().get(0).getValue(), 0.0001);
    }

    @Test
    public void testDeleteClassroom() {
        DrdpAuthModel drdpAuthModel = new DrdpAuthModel();
        drdpAuthModel.setAccessToken("1");

        CacheModel cacheModel = new CacheModel();
        cacheModel.setKey(CacheKey.DRDP2_AUTH);
        cacheModel.setValue(JsonUtil.toJson(drdpAuthModel));

        when(cacheService.get(CacheKey.DRDP2_AUTH)).thenReturn(cacheModel);

        ResponseEntity<String> responseEntity = new ResponseEntity<>("", HttpStatus.OK);
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class))).thenReturn(responseEntity);
        DeleteDrdpClassroomResponse response = drdpV2Service.deleteClassroom("1");

        assertEquals(200, (int) response.getStatus());
    }

}

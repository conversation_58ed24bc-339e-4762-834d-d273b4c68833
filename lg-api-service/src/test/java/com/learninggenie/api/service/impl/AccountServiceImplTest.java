package com.learninggenie.api.service.impl;

import com.learninggenie.api.config.WeChatConfig;
import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.exception.ErrorCode;
import com.learninggenie.api.model.*;
import com.learninggenie.api.model.thirdlogin.BindThirdPartRequest;
import com.learninggenie.api.model.thirdlogin.ThirdPartUserInfoResponse;
import com.learninggenie.api.model.thirdlogin.UnbindThirdPartRequest;
import com.learninggenie.api.model.thirdlogin.UserThirdPartBindListResponse;
import com.learninggenie.api.model.user.AccountCreateOwnerRequest;
import com.learninggenie.api.model.user.LoginRequest;
import com.learninggenie.api.model.user.RegisterUserRequest;
import com.learninggenie.api.model.wechat.BindChildRequest;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.security.DotNetPasswordEncoder;
import com.learninggenie.api.service.*;
import com.learninggenie.common.cache.CacheService;
import com.learninggenie.common.data.dao.*;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.dao.users.ImProfileDao;
import com.learninggenie.common.data.entity.*;
import com.learninggenie.common.data.enums.AgencyMetaKey;
import com.learninggenie.common.data.enums.AppMetaKey;
import com.learninggenie.common.data.enums.AppUpdateStatusKey;
import com.learninggenie.common.data.enums.UserMetaKey;
import com.learninggenie.common.data.mapper.NoteMapper;
import com.learninggenie.common.data.model.AgencyModel;
import com.learninggenie.common.data.model.DeviceModel;
import com.learninggenie.common.data.model.UsersMetaDataEntity;
import com.learninggenie.common.data.repository.AppSettingRepository;
import com.learninggenie.common.data.repository.UserMetaDataRepository;
import com.learninggenie.common.data.repository.UserRepository;
import com.learninggenie.common.data.utils.DBUtil;
import com.learninggenie.common.invitation.InvitationProvider;
import com.learninggenie.common.region.RegionService;
import com.learninggenie.common.utils.MSG;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.env.Environment;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

import static org.mockito.Mockito.*;


/**
 * Created by  on 2017/2/24.
 */
@RunWith(MockitoJUnitRunner.class)
public class AccountServiceImplTest {
    @InjectMocks
    private AccountServiceImpl accountService;
    @Mock
    private UserProvider userProvider;
    @Mock
    private UserRepository userRepository;
    @Mock
    private AccountDao accountDao;
    @Mock
    private AgencyDao agencyDao;
    @Mock
    private UserDaoImpl userDao;
    @Mock
    private CenterDao centerDao;
    @Mock
    private GroupService groupService;
    @Mock
    private EmailService emailService;
    @Mock
    private ExperienceCodeDao experienceCodeDao;
    @Mock
    private DotNetPasswordEncoder dotNetPasswordEncoder;
    @Mock
    private RestTemplate restTemplate;
    @Mock
    private StudentDao studentDao;
    @Mock
    private RegionService regionService;
    @Mock
    private InvitationProvider invitationProvider;
    @Mock
    private SocialService socialService;
    @Mock
    private SocialUserDao socialUserDao;
    @Mock
    private WeChatConfig weChatConfig;
    @Mock
    private CacheService cacheService;
    @Mock
    private UsersMetaDataDao usersMetaDataDao;

    @Mock
    private InvitationsEnrollmentInvitationDao invitationsEnrollmentInvitationDao;
    @Mock
    private AppService appService;

    @Mock
    private AgencyService agencyService;
    @Mock
    private Environment env;
    @Mock
    private ReleaseNotificationService releaseNotificationService;
    @Mock
    private ImProfileDao imProfileDao;
    @Mock
    private GroupDao groupDao;
    @Mock
    private AppSettingRepository appSettingRepository;
    @Mock
    private AuthService authService;
    @Mock
    private LoginLogDao loginLogDao;
    @Mock
    private InvitationsEnrollmentInvitationService enrollmentInvitationService;
    // 不可以mock重载的方法，所以要把接口mock出来
    @Mock
    private AccountService accountServiceInterface;
    @Mock
    private NoteDao noteDao;
    @Mock
    private MetaDao metaDao;
    @Mock
    private PeriodService periodService;
    @Mock
    @Qualifier("shardingJdbcTemplate")
    private JdbcTemplate shardingTemplate;

    @Mock
    private UserMetaDataRepository metaDataRepository;

    @Mock
    private com.learninggenie.common.messaging.EmailService  messageService;


    @Test(expected = BusinessException.class)
    public void testCreateDeviceAdressIsNull() {
        String type = "IP";
        String userId = "u001";
        DeviceLimitRequest request = new DeviceLimitRequest();
        request.setType(type);

        accountService.createDevice(userId, request);
    }

    @Test(expected = BusinessException.class)
    public void testCreateDeviceUserRoleNoSame() {
        String type = "IP";
        String userId = "u001";
        DeviceLimitRequest request = new DeviceLimitRequest();
        request.setAddress("***************");
        request.setType(type);

        UserEntity userEntity = new UserEntity();
        userEntity.setRole("NO_AGENCY_ADMIN");
        when(userProvider.checkUser(anyString())).thenReturn(userEntity);

        accountService.createDevice(userId, request);
    }

    @Ignore
    @Test
    public void testCreateDevice() {
        String type = "IP";
        String userId = "u001";
        DeviceLimitRequest request = new DeviceLimitRequest();
        request.setAddress("***************");
        request.setType(type);

        UserEntity userEntity = new UserEntity();
        userEntity.setRole("AGENCY_ADMIN");
        when(userProvider.checkUser(anyString())).thenReturn(userEntity);

        AgencyModel agency = new AgencyModel();
        agency.setId("1F3F8540-58EF-E611-ABF5-90FBA6071C06");
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agency);

        when(accountDao.getDevicesByAddress(anyString(), anyString())).thenReturn(null);

        accountService.createDevice(userId, request);
    }

    @Test
    public void testDeleteDevice() {
        UserEntity userEntity = new UserEntity();
        userEntity.setRole("AGENCY_ADMIN");
        when(userProvider.checkUser(anyString())).thenReturn(userEntity);
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("123");
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agencyModel);

        List<String> ids = new ArrayList<>();
        ids.add("i100");

        String userId = "u100";
        accountService.deleteDevice(userId, ids);

    }

    @Ignore
    @Test
    public void testUpdateDevice() {
        String type = "IP";
        String userId = "u001";
        DeviceLimitRequest request = new DeviceLimitRequest();
        request.setAddress("***************");
        request.setType(type);
        request.setId(userId);

        UserEntity userEntity = new UserEntity();
        userEntity.setRole("AGENCY_ADMIN");
        when(userProvider.checkUser(anyString())).thenReturn(userEntity);

        DeviceModel device = new DeviceModel();
        when(accountDao.getDeviceById(anyString())).thenReturn(device);

        AgencyModel agency = new AgencyModel();
        agency.setId("1F3F8540-58EF-E611-ABF5-90FBA6071C06");
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agency);

        accountService.updateDevice(userId, request);
    }

    @Test(expected = BusinessException.class)
    public void testGetDeviceUserIdIsNull() {
        String type = "IP";
        String userId = "";
        UserEntity user = new UserEntity();
        user.setRole("666a");
        when(userProvider.checkUser(anyString())).thenReturn(user);
        accountService.getDevice(userId, type, null);
    }

    @Test
    public void testGetDeviceAgencyIsNull() {
        String type = "IP";
        String userId = "";
        UserEntity user = new UserEntity();
        user.setRole("AGENCY_ADMIN");
        when(userProvider.checkUser(anyString())).thenReturn(user);

        AgencyModel agency = new AgencyModel();
        agency.setId("666");
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agency);
        accountService.getDevice(userId, type, null);
    }

    @Test(expected = BusinessException.class)
    public void testSetDeviceOpenNotUse1Or0() {
        String userId = "";
        String id = "";
        int open = 2;

        accountService.setDeviceOpen(userId, id, open);
    }

    @Test
    public void testGetDeviceLimitOpen() {
        String userId = "u100";

        AgencyModel agency = new AgencyModel();
        agency.setId("666");
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agency);

        AgencyMetaDataEntity open = new AgencyMetaDataEntity();
        open.setMetaValue("1");
        when(agencyDao.getMeta(anyString(), anyString())).thenReturn(open);

        accountService.getDeviceLimitOpen(userId);
    }


    @Test
    public void testSetDeviceLimitOpen() {
        String userId = "u100";
        int open = 1;

        UserEntity userEntity = new UserEntity();
        userEntity.setRole("AGENCY_ADMIN");
        when(userProvider.checkUser(anyString())).thenReturn(userEntity);

        AgencyModel agency = new AgencyModel();
        agency.setId("666");
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agency);

        List<DeviceModel> deviceModels = new ArrayList<>();
        DeviceModel deviceModel = new DeviceModel();
        deviceModel.setId("********");
        deviceModels.add(deviceModel);
        when(accountDao.getInactiveDevices(anyString())).thenReturn(deviceModels);
        accountService.setDeviceLimitOpen(userId, open);
    }

    @Test
    public void testSetDeviceOpen() {
        String userId = "";
        String id = "i100";
        int open = 1;

        UserEntity userEntity = new UserEntity();
        userEntity.setRole("AGENCY_ADMIN");
        when(userProvider.checkUser(anyString())).thenReturn(userEntity);

        accountService.setDeviceOpen(userId, id, open);
    }

    /**
     * Case : 当设备地址是空
     * 结果 : 关闭总开关   (已经删除)
     */
    @Ignore
    @Test
    public void deleteDevice_thenNoDevice_closeLimit() {
        String userId = "U123";
        String agencyId = "A123";

        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setRole("AGENCY_OWNER");

        AgencyModel agency = new AgencyModel();
        agency.setId(agencyId);

        List<String> ids = new ArrayList<>();
        ids.add("D123");
        ids.add("D1234");

        DeviceModel device = new DeviceModel();
        device.setId("D123");

        List<DeviceModel> deviceModels = new ArrayList<>();

        when(userProvider.checkUser(userId)).thenReturn(user);
        when(userProvider.getAgencyByUserId(user.getId())).thenReturn(agency);
        when(accountDao.getDeviceById("D1234")).thenReturn(device);  //这里通过ID只能查到一个, 所以删除只会执行一次
        when(accountDao.getInactiveDevices(agencyId)).thenReturn(deviceModels);

        accountService.deleteDevice(userId, ids);

        verify(accountDao, times(1)).deleteDevice(anyString());
        verify(agencyDao, times(1)).setMeta(agencyId, "DEVICE_LIMIT_OPEN", "0");
    }

    /**
     * Case : 没有活动的设备, 设置设备开关打开
     * 结果 : 抛异常
     */
    @Test(expected = BusinessException.class)
    public void setDeviceLimitOpen_NoDevice() {
        String userId = "U123";
        String agencyId = "A123";

        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setRole("AGENCY_OWNER");

        AgencyModel agency = new AgencyModel();
        agency.setId(agencyId);

        List<DeviceModel> deviceModels = new ArrayList<>();

        when(userProvider.checkUser(userId)).thenReturn(user);
        when(userProvider.getAgencyByUserId(user.getId())).thenReturn(agency);
        when(accountDao.getInactiveDevices(agencyId)).thenReturn(deviceModels);

        accountService.setDeviceLimitOpen(userId, 1);
    }

    /**
     * Case:注册邮箱为空格
     * 结果:抛异常
     */
    @Test(expected = BusinessException.class)
    public void registerOwner_emailIsBlankTest() {
        String email = " ";
        String password = "123";
        AccountCreateOwnerRequest request = new AccountCreateOwnerRequest();
        request.setEmail(email);
        request.setPassword(password);
        HttpServletRequest header = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        accountService.registerOwner(request, header, response);
    }

    /**
     * Case:注册密码为空格
     * 结果:抛异常
     */
    @Test(expected = BusinessException.class)
    public void registerOwner_passwordIsBlankTest() {
        String password = " ";
        String email = "<EMAIL>";
        AccountCreateOwnerRequest request = new AccountCreateOwnerRequest();
        request.setEmail(email);
        request.setPassword(password);
        HttpServletRequest header = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        accountService.registerOwner(request, header, response);
    }

    /**
     * Case:注册邮箱为空
     * 结果:抛异常
     */
    @Test(expected = BusinessException.class)
    public void registerOwner_emailIsNullTest() {
        String email = null;
        String password = "123456";
        AccountCreateOwnerRequest request = new AccountCreateOwnerRequest();
        request.setEmail(email);
        request.setPassword(password);
        HttpServletRequest header = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        accountService.registerOwner(request, header, response);
    }

    /**
     * Case:注册密码为空
     * 结果:抛异常
     */
    @Test(expected = BusinessException.class)
    public void registerOwner_passwordIsNullTest() {
        String password = null;
        String email = "<EMAIL>";
        AccountCreateOwnerRequest request = new AccountCreateOwnerRequest();
        request.setPassword(password);
        request.setEmail(email);
        HttpServletRequest header = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        accountService.registerOwner(request, header, response);
    }

    /**
     * Case:邮箱已被注册
     * 结果:抛异常
     */
    @Test(expected = BusinessException.class)
    public void registerOwner_checkUserTest() {
        String email = "<EMAIL>";
        String password = "123456";
        AccountCreateOwnerRequest request = new AccountCreateOwnerRequest();
        request.setEmail(email);
        request.setPassword(password);
        UserEntity userEntity = new UserEntity();
        userEntity.setEmail(email);
        HttpServletRequest header = new MockHttpServletRequest();
        ReflectionTestUtils.setField(accountService, "emailTemplateVersion", "v1");
        when(userProvider.addEmailProjectSuffix(request.getEmail())).thenReturn(request.getEmail());
        when(userRepository.getTop1ByEmailAndIsDeletedFalse(request.getEmail())).thenReturn(userEntity);
        when(userProvider.getCurrentProject()).thenReturn("LEARNING-GENIE");
        HttpServletResponse response = new MockHttpServletResponse();
        accountService.registerOwner(request, header, response);
    }

    /**
     * Case:邀请码不为空时
     */
    @Ignore
    @Test
    public void registerOwner_invitationTokenIsNotNull() {
        String email = "<EMAIL>";
        String password = "123456";
        String invitationToken = "123";
        AccountCreateOwnerRequest request = new AccountCreateOwnerRequest();
        request.setEmail(email);
        request.setPassword(password);
        request.setInvitationToken(invitationToken);
        HttpServletRequest header = new MockHttpServletRequest();
        when(userRepository.getTop1ByEmailAndIsDeletedFalse(request.getEmail())).thenReturn(null);
        HttpServletResponse response = new MockHttpServletResponse();
        accountService.registerOwner(request, header, response);
        verify(userDao, times(1)).createUser(Mockito.any(UserEntity.class));
        verify(userDao, times(1)).createUserProfile(Mockito.any(UserProfileEntity.class));
        //邀请码不为空，不会创建agency和center
        verify(agencyDao, times(0)).createAgency(Mockito.any(AgencyEntity.class));
        verify(agencyDao, times(0)).createAgencyUser(anyString(), anyString());
        verify(centerDao, times(0)).createAgencyCenter(anyString(), anyString());
        verify(centerDao, times(0)).createCenter(Mockito.any(com.learninggenie.common.data.model.CenterEntity.class));
        verify(groupService, times(1)).getGroupStages(anyString());
    }

    /**
     * Case:邀请码为空时
     */
    @Ignore
    @Test
    public void registerOwner_invitationTokenIsNull() throws IOException {
        String email = "<EMAIL>";
        String password = "123456";
        AccountCreateOwnerRequest request = new AccountCreateOwnerRequest();
        request.setEmail(email);
        request.setPassword(password);
        HttpServletRequest header = new MockHttpServletRequest();
        when(userRepository.getTop1ByEmailAndIsDeletedFalse(request.getEmail())).thenReturn(null);
        HttpServletResponse response = new MockHttpServletResponse();
        accountService.registerOwner(request, header, response);
        verify(userDao, times(1)).createUser(Mockito.any(UserEntity.class));
        verify(userDao, times(1)).createUserProfile(Mockito.any(UserProfileEntity.class));
        //邀请码为空，创建agency和center
        verify(agencyDao, times(1)).createAgency(Mockito.any(AgencyEntity.class));
        verify(agencyDao, times(1)).createAgencyUser(anyString(), anyString());
        verify(centerDao, times(1)).createAgencyCenter(anyString(), anyString());
        verify(centerDao, times(1)).createCenter(Mockito.any(com.learninggenie.common.data.model.CenterEntity.class));
        verify(emailService, times(1)).sendRemindNewRegisterEmail(anyString(), anyString(), anyString(), anyString(), anyString());
        verify(groupService, times(1)).getGroupStages(anyString());
    }

    /**
     * Case:体验码为空
     * 结果:抛异常
     */
    @Test(expected = BusinessException.class)
    public void registerOwner_experienceCodeIsNull() {
        String email = "<EMAIL>";
        String password = "123456";
        String experienceCode = null;
        ReflectionTestUtils.setField(accountService, "experienceCodeEnabled", "true");
        AccountCreateOwnerRequest request = new AccountCreateOwnerRequest();
        request.setEmail(email);
        request.setPassword(password);
        request.setExperienceCode(experienceCode);
        HttpServletRequest header = new MockHttpServletRequest();
        ReflectionTestUtils.setField(accountService, "emailTemplateVersion", "v1");
        when(userProvider.addEmailProjectSuffix(request.getEmail())).thenReturn(request.getEmail());
        when(userRepository.getTop1ByEmailAndIsDeletedFalse(request.getEmail())).thenReturn(null);
        when(userProvider.getCurrentProject()).thenReturn("LEARNING-GENIE");
        HttpServletResponse response = new MockHttpServletResponse();
        accountService.registerOwner(request, header, response);
    }

    /**
     * Case：体验码不存在
     * 结果：抛异常
     */
    @Test(expected = BusinessException.class)
    public void registerOwner_experienceNotExist() {
        String email = "<EMAIL>";
        String password = "123456";
        String experienceCode = "123";
        ReflectionTestUtils.setField(accountService, "experienceCodeEnabled", "true");
        AccountCreateOwnerRequest request = new AccountCreateOwnerRequest();
        request.setEmail(email);
        request.setPassword(password);
        request.setExperienceCode(experienceCode);
        HttpServletRequest header = new MockHttpServletRequest();
        List<UserEntity> applyUsers = new ArrayList<>();
        ReflectionTestUtils.setField(accountService, "emailTemplateVersion", "v1");
        when(userProvider.addEmailProjectSuffix(request.getEmail())).thenReturn(request.getEmail());
        when(userRepository.getTop1ByEmailAndIsDeletedFalse(request.getEmail())).thenReturn(null);
        when(userProvider.getCurrentProject()).thenReturn("LEARNING-GENIE");
        //体验码未被使用但不存在
        //when(experienceCodeDao.getByCode(request.getExperienceCode())).thenReturn(null);
        //when(userDao.getApplyUsers(anyString())).thenReturn(applyUsers);
        HttpServletResponse response = new MockHttpServletResponse();
        accountService.registerOwner(request, header, response);
    }

    /**
     * Case：体验码已使用
     * 结果：抛异常
     */
    @Test(expected = BusinessException.class)
    public void registerOwner_experienceIsUsed() {
        String email = "<EMAIL>";
        String password = "123456";
        String experienceCode = "123";
        ReflectionTestUtils.setField(accountService, "experienceCodeEnabled", "true");
        AccountCreateOwnerRequest request = new AccountCreateOwnerRequest();
        request.setEmail(email);
        request.setPassword(password);
        request.setExperienceCode(experienceCode);
        HttpServletRequest header = new MockHttpServletRequest();
        UserEntity userEntity = new UserEntity();
        ExperienceCodeEntity experienceCodeEntity = new ExperienceCodeEntity();
        List<UserEntity> applyUsers = new ArrayList<>();
        applyUsers.add(userEntity);
        ReflectionTestUtils.setField(accountService, "emailTemplateVersion", "v1");
        when(userProvider.addEmailProjectSuffix(request.getEmail())).thenReturn(request.getEmail());
        when(userRepository.getTop1ByEmailAndIsDeletedFalse(request.getEmail())).thenReturn(null);
        when(userProvider.getCurrentProject()).thenReturn("LEARNING-GENIE");
        //体验码存在但是已被使用
        //when(experienceCodeDao.getByCode(request.getExperienceCode())).thenReturn(experienceCodeEntity);
        //when(userDao.getApplyUsers(anyString())).thenReturn(applyUsers);
        HttpServletResponse response = new MockHttpServletResponse();
        accountService.registerOwner(request, header, response);
    }

    /**
     * Case:正常发送重置密码链接
     * TODO: yujun
     */
    @Test
    public void testSendResetPasswordEmail() {
        String email = "<EMAIL>";
        UserEntity user = new UserEntity();
        user.setId("u001");
        user.setSecurityStamp("8e7c63b9-784b-486c-9a90-6ee7fba61f72");
        user.setEmail(email);
        ReflectionTestUtils.setField(accountService, "resetPwdUrl", "http://admin.learning-genie.com/reset_password");

        when(userProvider.addEmailProjectSuffix(email)).thenReturn(email);
        when(userProvider.checkUserByEmail(email)).thenReturn(user);

        accountService.sendResetPasswordEmail(email);

        verify(userProvider, times(1)).checkUserByEmail(email);
        verify(emailService, times(1)).sendResetPasswordEmail(Mockito.anyString(), Mockito.anyString(), Mockito.any());
    }

    @Test
    public void testGetParentalInvitationInfo() {
        String invitationToken = "1";
        List<EnrollmentEntity> students = new ArrayList<>();
        EnrollmentEntity student = new EnrollmentEntity();
        student.setFirstName("李小明");
        students.add(student);
        student.setConfirmed(false);
        student.setVerifyNum(1);
        when(studentDao.getEnrollmentByInvitationToken(invitationToken)).thenReturn(students);

        accountService.validateParentalInvitation(invitationToken);

        verify(studentDao, times(1)).getEnrollmentByInvitationToken(invitationToken);
    }

    @Test
    public void testCheckParentalInvitationChildInfo() {
        RegisterUserRequest request = new RegisterUserRequest();
        request.setInvitationToken("1");
        request.setFirstName("李小明");
        List<EnrollmentEntity> students = new ArrayList<>();
        EnrollmentEntity student = new EnrollmentEntity();
        student.setFirstName("李小明");
        student.setVerifyNum(0);
        student.setInvitationId("1");
        student.setConfirmed(false);
        students.add(student);
        when(studentDao.getEnrollmentByInvitationToken(request.getInvitationToken())).thenReturn(students);
        when(regionService.isChina()).thenReturn(true);

        accountService.validateParentalInvitationChildInfo(request);

        verify(invitationProvider, times(0)).updateInvationVerifyNum(student.getVerifyNum() + 1, student.getInvitationId());
        verify(studentDao, times(1)).getEnrollmentByInvitationToken(request.getInvitationToken());
    }

    private BindChildRequest prepareBindUserRequest() {
        BindChildRequest bindUserRequest = new BindChildRequest();
        bindUserRequest.setBindType("WE_CHAT");
        bindUserRequest.setEmail("e001");
        bindUserRequest.setDisplayName("dn001");
        bindUserRequest.setInvitationToken("it001");
        bindUserRequest.setPassword("pw01");
        bindUserRequest.setUnionId("ui001");
        return bindUserRequest;
    }

    /**
     * Case: 测试微信回调方法
     */
//    @Test
//    public void testWeChatCallback_NotBind(){
//        String code ="c001";
//        HttpServletRequest httpServletRequest=mock(HttpServletRequest.class);
//        when(httpServletRequest.getHeader(anyString())).thenReturn("EDUCATOR");
//        SocialLoginResponse<WeChatUserInfoResponse> socialLoginResponse= new SocialLoginResponse<>();
//        LoginResponse loginResponse=new LoginResponse();
//        loginResponse.setUserId("u001");
//        socialLoginResponse.setLoginResponse(loginResponse);
//        socialLoginResponse.setBind(false);
//        when(socialService.weChatLogin(code,"EDUCATOR")).thenReturn(socialLoginResponse);
//        socialLoginResponse=accountService.weChatCallback("c001","t001","GOOGLE",httpServletRequest);
//        Assert.assertEquals(false,socialLoginResponse.getBind());
//    }
    /**
     * Case : 测试第三方账号绑定用户方法
     */

    @Test
    public void testCreateTempPassword_HasMeta() {
        ReflectionTestUtils.setField(accountService, "tempPasswordKey", "789");
        String userName = "<EMAIL>";
        String userId = "123";
        String mId = "456";
        UserEntity user = new UserEntity();
        user.setId(userId);
        UsersMetaDataEntity meta = new UsersMetaDataEntity();
        meta.setId(mId);
        List<UsersMetaDataEntity> metas = new ArrayList<>();
        metas.add(meta);
        when(userProvider.checkUserByEmail(userName)).thenReturn(user);
        when(usersMetaDataDao.getMetas(UserMetaKey.TEMP_PASSWORD.toString(), userId)).thenReturn(metas);

        String pwd = accountService.createTempPassword("789", userName);

        verify(usersMetaDataDao, times(1)).getMetas(UserMetaKey.TEMP_PASSWORD.toString(), userId);
        verify(usersMetaDataDao, times(0)).insertMeta(eq(UserMetaKey.TEMP_PASSWORD.toString()), anyString(), eq(userId));
        verify(usersMetaDataDao, times(1)).updateMetaById(eq(mId), Mockito.anyString());
        Assert.assertNotEquals(pwd, "");
        Assert.assertNotEquals(pwd, null);
    }

    @Test
    public void testCreateTempPassword_NoMeta() {
        ReflectionTestUtils.setField(accountService, "tempPasswordKey", "789");
        String userName = "<EMAIL>";
        String userId = "123";
        String mId = "456";
        UserEntity user = new UserEntity();
        user.setId(userId);
        List<UsersMetaDataEntity> metas = new ArrayList<>();
        when(userProvider.checkUserByEmail(userName)).thenReturn(user);
        when(usersMetaDataDao.getMetas(UserMetaKey.TEMP_PASSWORD.toString(), userId)).thenReturn(metas);

        String pwd = accountService.createTempPassword("789", userName);

        verify(usersMetaDataDao, times(1)).getMetas(UserMetaKey.TEMP_PASSWORD.toString(), userId);
        verify(usersMetaDataDao, times(1)).insertMeta(eq(UserMetaKey.TEMP_PASSWORD.toString()), Mockito.anyString(), eq(userId));
        verify(usersMetaDataDao, times(0)).updateMetaById(eq(mId), Mockito.anyString());
        Assert.assertNotEquals(pwd, "");
        Assert.assertNotEquals(pwd, null);
    }

    /**
     * 三方登录回调
     * case: 三方账号未绑定，返回三方账号
     */
    @Test
    public void testThirdPartLoginCallback_unbind() throws Exception {
        // 数据准备
        String token = "token";
        String code = "code";
        String appType = "Parents";
        String client = "GOOGLE";
        String email = "email";
        HttpServletRequest header = new MockHttpServletRequest();
        SocialLoginResponse<ThirdPartUserInfoResponse> response = new SocialLoginResponse<>();
        response.setBind(false);
        ThirdPartUserInfoResponse thirdPartUserInfoResponse = new ThirdPartUserInfoResponse();
        thirdPartUserInfoResponse.setEmail(email);
        response.setSocialUserInfo(thirdPartUserInfoResponse);

        // 数据模拟
        when(socialService.googleLogin(token, null)).thenReturn(response);
        HttpServletResponse httpServletResponse = new MockHttpServletResponse();
        // 调用
        SocialLoginResponse<ThirdPartUserInfoResponse> callbackResponse = accountService.thirdLoginCallback(token, code, client, appType, false, null, null, null, header, httpServletResponse);

        // 验证
        Assert.assertEquals(callbackResponse.getBind(), false);
        Assert.assertEquals(callbackResponse.getSocialUserInfo().getEmail(), email);
    }

    /**
     * 三方登录回调
     * case: 三方账号已绑定，返回登录信息
     */
    @Test
    public void testThirdPartLoginCallback_binding() throws Exception {
        // 数据准备
        String token = "token";
        String code = "code";
        String client = "GOOGLE";
        String appType = "Parents";
        String email = "<EMAIL>";
        String userId = "userId";
        HttpServletRequest header = new MockHttpServletRequest();
        SocialLoginResponse<ThirdPartUserInfoResponse> response = new SocialLoginResponse<>();
        response.setUserId(userId);
        response.setBind(true);
        ThirdPartUserInfoResponse thirdPartUserInfoResponse = new ThirdPartUserInfoResponse();
        thirdPartUserInfoResponse.setEmail(email);
        response.setSocialUserInfo(thirdPartUserInfoResponse);
        UserEntity user = new UserEntity();
        user.setEmail(email);
        user.setId(userId);
        user.setRole("parent");
        AppUpgradeStatusResponse appUpgradeStatusResponse = new AppUpgradeStatusResponse();
        appUpgradeStatusResponse.setType(AppUpdateStatusKey.NO_UPDATE.toString());
        AppSettingEntity appSettingEntity = new AppSettingEntity();
        appSettingEntity.setPerferCredits(1);

        AgencyEntity agency = new AgencyEntity();
        agency.setId("agencyId01");
        agency.setEducationStage("EARLY_LEARNING");

        // 数据模拟
        when(socialService.googleLogin(token, null)).thenReturn(response);
        when(userProvider.checkUserByEmail(email)).thenReturn(user);
        when(userProvider.checkUser(userId)).thenReturn(user);
        when(appService.getAppUpgradeStatus(null, null, null, userId, null)).thenReturn(appUpgradeStatusResponse);
        when(agencyService.getCommStatus(userId)).thenReturn(new Status());
        when(appSettingRepository.findAll()).thenReturn(Collections.singletonList(appSettingEntity));
        when(enrollmentInvitationService.getByEmail(email)).thenReturn(new ArrayList<>());
        when(userProvider.addEmailProjectSuffix(email)).thenReturn(email);
        when(userProvider.removeEmailProjectSuffix(email)).thenReturn(email);
        HttpServletResponse httpServletResponse = new MockHttpServletResponse();

        // 调用
        SocialLoginResponse<ThirdPartUserInfoResponse> callbackResponse = accountService.thirdLoginCallback(token, code, client, appType, false, null, null, null, header, httpServletResponse);

        // 验证
        Assert.assertEquals(callbackResponse.getBind(), true);
        Assert.assertEquals(callbackResponse.getLoginResponse().getUserId(), userId);
    }

    /**
     * 测试用户三方账号已绑定情况
     */
    @Test
    public void testThirdPartBind_userBinding() {
        // 参数
        String userId = "u001";
        String userEmail = "<EMAIL>";
        String socialType = "APPLE";
        String socialId = "socialId";

        // 模拟请求参数
        BindThirdPartRequest bindRequest = new BindThirdPartRequest();
        bindRequest.setUserId(userId);
        bindRequest.setThirdPartUserId(socialId);
        bindRequest.setThirdPartType(socialType);
        bindRequest.setEmail(userEmail);
        bindRequest.setThirdPartEmail(userEmail);
        bindRequest.setAppType("Parents");

        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setRole("Parents");

        HttpServletRequest requestHeader = new MockHttpServletRequest();

        // 数据模拟
        when(socialService.findUserIdByTypeAndSocialId(socialId, socialType)).thenReturn(userId);
        when(userProvider.checkUser(userId)).thenReturn(user);
        HttpServletResponse response = new MockHttpServletResponse();
        // 调用测试
        try {
            accountService.thirdPartBind(bindRequest, requestHeader, response);
        } catch (Exception e) {
            // 验证结果
            String errorCode = ((BusinessException) e).getErrorCode().getCode().toUpperCase();
            Assert.assertEquals("BINDING_FAILED", errorCode);
        }

    }

    /**
     * 测试用户绑定
     */
    @Test
    public void testThirdPartBind() {
        // 参数
        String userId = "u001";
        String userEmail = "<EMAIL>";
        String thirdPartUserEmail = "<EMAIL>";
        String socialType = "APPLE";
        String socialId = "socialId";
        String pwd = "pwd";

        // 数据准备
        BindThirdPartRequest bindRequest = new BindThirdPartRequest();
        bindRequest.setUserId(userId);
        bindRequest.setThirdPartUserId(socialId);
        bindRequest.setThirdPartType(socialType);
        bindRequest.setEmail(userEmail);
        bindRequest.setPassword(pwd);
        bindRequest.setThirdPartEmail(thirdPartUserEmail);
        bindRequest.setAppType("Parents");

        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setRole("parent");

        LoginRequest loginRequest = new LoginRequest();
        loginRequest.setEmail(userEmail);
        loginRequest.setPassword(pwd);

        AgencyEntity agency = new AgencyEntity();
        agency.setId("agencyId01");
        agency.setEducationStage("EARLY_LEARNING");

        HttpServletRequest requestHeader = new MockHttpServletRequest();

        AppUpgradeStatusResponse appUpgradeStatusResponse = new AppUpgradeStatusResponse();
        appUpgradeStatusResponse.setType(AppUpdateStatusKey.NO_UPDATE.toString());

        LoginResponse response = new LoginResponse();
        response.setUserId(userId);

        AppSettingEntity appSettingEntity = new AppSettingEntity();
        appSettingEntity.setPerferCredits(1);

        // 模拟数据
        when(socialService.findUserIdByTypeAndSocialId(socialId, socialType)).thenReturn(null);
        when(userProvider.checkUserByEmail(userEmail)).thenReturn(user);
        when(userProvider.checkUser(userId)).thenReturn(user);
        when(socialUserDao.userThirdPartBinding(userId, socialType)).thenReturn(false);
        when(dotNetPasswordEncoder.matches(pwd, user.getPasswordHash())).thenReturn(true);
        when(appService.getAppUpgradeStatus(null, null, null, userId, null)).thenReturn(appUpgradeStatusResponse);
        when(agencyService.getCommStatus(userId)).thenReturn(new Status());
        when(appSettingRepository.findAll()).thenReturn(Collections.singletonList(appSettingEntity));
        when(userProvider.addEmailProjectSuffix(bindRequest.getEmail())).thenReturn(bindRequest.getEmail());
        when(metaDataRepository.findTop1ByMetaKeyAndUserId(MetaDataKey.STRIPE_CUSTOMER_ID.toString(), userId)).thenReturn(null);
        HttpServletResponse httpServletResponse = new MockHttpServletResponse();

        // 调用方法
        SocialLoginResponse<ThirdPartUserInfoResponse> socialLoginResponse = accountService.thirdPartBind(bindRequest, requestHeader, httpServletResponse);

        // 验证结果
        Assert.assertEquals(socialLoginResponse.getBind(), true);
        Assert.assertEquals(socialLoginResponse.getLoginResponse().getUserId(), userId);
    }


    /**
     * 测试用户解绑
     */
    @Test
    public void testThirdPartUnBind() {
        // 参数
        String userId = "u001";
        String pwd = "pwd";
        String socialType = "APPLE";
        String socialId = "socialId";

        // 数据准备
        UnbindThirdPartRequest unbindThirdPartRequest = new UnbindThirdPartRequest();
        unbindThirdPartRequest.setThirdPartType(socialType);
        unbindThirdPartRequest.setSocialId(socialId);
        unbindThirdPartRequest.setUserId(userId);
        unbindThirdPartRequest.setPassword(pwd);

        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setRole("parent");

        // 数据模拟
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.checkUser(userId)).thenReturn(user);
        when(dotNetPasswordEncoder.matches(pwd, user.getPasswordHash())).thenReturn(true);
        when(socialService.unbindThirdPart(socialId, socialType)).thenReturn(true);

        // 调用测试
        boolean res = accountService.unbindThirdPart(unbindThirdPartRequest);

        // 验证结果
        Assert.assertEquals(res, true);
    }

    /**
     * 获取用户绑定列表测试
     */
    @Test
    public void testGetUserThirdPartBindList() {
        // 参数
        String userId = "u001";
        String socialType = "APPLE";
        String socialId = "socialId";

        // 数据准备
        SocialUserEntity socialUserEntity = new SocialUserEntity();
        socialUserEntity.setUserId(userId);
        socialUserEntity.setSocialId(socialId);
        socialUserEntity.setType(socialType);

        List<SocialUserEntity> bindList = Collections.singletonList(socialUserEntity);

        // 数据模拟
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(socialUserDao.getUserThirdPartBindList(userId)).thenReturn(bindList);

        // 调用
        UserThirdPartBindListResponse userThirdPartBindList = accountService.getUserThirdPartBindList();

        // 验证
        Assert.assertEquals(userThirdPartBindList.getSocialUserList().get(0).getUserId(), userId);
        Assert.assertEquals(userThirdPartBindList.getSocialUserList().get(0).getSocialId(), socialId);
        Assert.assertEquals(userThirdPartBindList.getSocialUserList().get(0).getType(), socialType);
    }

    /**
     * case: role 为 Teacher.
     */
    @Test
    public void testGetApprovalOpenForTeacher() {
        // 数据准备
        String role = "COLLABORATOR";
        String agencyId = "test-agencyId";
        UserEntity userEntity = new UserEntity();
        userEntity.setId("test-id");
        userEntity.setRole(role);
        // sql
        lenient().when(shardingTemplate.query(NoteMapper.SQL_GET_SITE_TEACHER_IDS,
                new String[]{userEntity.getId(), AgencyMetaKey.APPROVAL_OPEN.toString()},
                (rs, i) -> DBUtil.getString(rs, "Id"))).thenReturn(new ArrayList<>());
        // 测试
        final int count = noteDao.openApprovalCountByCenterIds(userEntity.getId());
        lenient().when(accountService.getApprovalOpen(userEntity, agencyId)).thenReturn(true);
        final boolean result = accountService.getApprovalOpen(userEntity, agencyId);
        Assert.assertEquals(0, count);
        Assert.assertEquals(true, result);
    }

    /**
     * case: role为 siteAdmin.
     */
    @Test
    public void testGetApprovalOpenForSiteAdmin() {
        // 数据准备
        String role = "SITE_ADMIN";
        String agencyId = "test-agencyId";
        UserEntity userEntity = new UserEntity();
        userEntity.setId("test-id");
        userEntity.setRole(role);
        // 测试
        lenient().when(noteDao.openApprovalCountByCenterIds(userEntity.getId())).thenReturn(0);
        final boolean result = accountService.getApprovalOpen(userEntity, agencyId);
        Assert.assertEquals(false, result);
    }

    /**
     * case: 除去Teacher Or siteAdmin 角色的其他情况.
     */
    @Test
    public void testGetApprovalOpenForOther() {
        // 数据准备
        String role = "AGENCY_OWNER";
        String agencyId = "test-agencyId";
        UserEntity userEntity = new UserEntity();
        userEntity.setId("test-id");
        userEntity.setRole(role);
        // 测试
        lenient().when(noteDao.openApprovalCountByCenterIds(userEntity.getId())).thenReturn(0);
        final boolean result = accountService.getApprovalOpen(userEntity, agencyId);
        Assert.assertEquals(false, result);
    }

    /**
     * 测试登录恢复账号
     */
    @Test(expected = BusinessException.class)
    public void testLoginRestoreAccount() {
        // 登录请求信息
        LoginRequest request = new LoginRequest();
        request.setEmail("<EMAIL>"); // 登录邮箱
        request.setPassword("test-login-restore-account-password"); // 登录密码

        // 用户信息
        UserEntity user = new UserEntity();
        // 模拟查询用户
        when(userDao.getUserByEmailIncludeDeleted(request.getEmail())).thenReturn(user);

        // 删除的机构列表
        List<AgencyModel> deletedAgencies = new ArrayList<>();
        AgencyModel agency = new AgencyModel();
        agency.setId(UUID.randomUUID().toString()); // 机构 ID
        agency.setDeleted(true); // 机构已删除
        deletedAgencies.add(agency);
        // 模拟查询机构
        when(userProvider.getAgenciesIncludeDeletedByUser(any())).thenReturn(deletedAgencies);

        // 用户不存在的情况，不执行后面逻辑
        when(userProvider.checkUserByEmail(request.getEmail()))
                .thenThrow(new BusinessException(ErrorCode.USER_NOT_FOUND, MSG.t("NO_USER")));

        when(userProvider.addEmailProjectSuffix(request.getEmail())).thenReturn(request.getEmail());
        HttpServletResponse httpServletResponse = new MockHttpServletResponse();

        // 调用测试
        accountService.login(request, null, httpServletResponse);

        // 恢复的机构 ID 列表
        List<String> restoreAgencyIds = Collections.singletonList(agency.getId());
        // 验证恢复方法执行
        verify(agencyDao, times(1)).restoreAgencies(restoreAgencyIds);
        verify(agencyDao, times(1)).restoreAgencyOwners(restoreAgencyIds);
    }

    /**
     * 测试用户获取 AB 测试开关
     */
    @Test
    public void testGetABTestOpen() {
        // 数据模拟
        when(metaDao.getAppMeta(AppMetaKey.ALL_AGENCY_GENERATE_DEFAULT_PERIOD_OPEN.toString())).thenReturn("true");
        ABTestOpenResponse abTestResponse = accountService.getABTestOpenResponse();
        Assert.assertEquals(abTestResponse.getAbTestOpenModel().isT1(), false);
        Assert.assertEquals(abTestResponse.getAbTestFeatureOpenModel().isCreateDefaultPeriodOpen(), true);

        // 用户 Id
        String userId = "U001";
        // 机构 Id
        String agencyId = "A001";
        // 用户信息
        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setRole("AGENCY_OWNER");

        AgencyMetaDataEntity agencyMetaDataEntity = new AgencyMetaDataEntity();
        agencyMetaDataEntity.setMetaValue("true");
        agencyMetaDataEntity.setAgencyId(agencyId);

        AgencyMetaDataEntity agencyMetaDataEntity1 = new AgencyMetaDataEntity();
        agencyMetaDataEntity1.setMetaValue("false");
        agencyMetaDataEntity1.setAgencyId(agencyId);

        // 机构信息
        AgencyModel agency = new AgencyModel();
        agency.setId(agencyId);
        lenient().when(userProvider.getCurrentUserId()).thenReturn(userId);
        lenient().when(userProvider.checkUser(userId)).thenReturn(user);
        lenient().when(userProvider.getAgencyByUserId(anyString())).thenReturn(agency);
        when(metaDao.getAppMeta(AppMetaKey.ALL_AGENCY_GENERATE_DEFAULT_PERIOD_OPEN.toString())).thenReturn("false");
        when(agencyDao.getMeta(agencyId, AgencyMetaKey.GENERATE_DEFAULT_PERIOD_OPEN.toString())).thenReturn(agencyMetaDataEntity1);
        ABTestOpenResponse abTestResponse1 = accountService.getABTestOpenResponse();
        Assert.assertEquals(abTestResponse1.getAbTestOpenModel().isT1(), false);
        Assert.assertEquals(abTestResponse1.getAbTestFeatureOpenModel().isCreateDefaultPeriodOpen(), false);

        when(metaDao.getAppMeta(AppMetaKey.ALL_AGENCY_GENERATE_DEFAULT_PERIOD_OPEN.toString())).thenReturn("false");
        when(agencyDao.getMeta(agencyId, AgencyMetaKey.GENERATE_DEFAULT_PERIOD_OPEN.toString())).thenReturn(agencyMetaDataEntity);
        ABTestOpenResponse abTestResponse2 = accountService.getABTestOpenResponse();
        Assert.assertEquals(abTestResponse2.getAbTestOpenModel().isT1(), true);
        Assert.assertEquals(abTestResponse2.getAbTestFeatureOpenModel().isCreateDefaultPeriodOpen(), true);

        when(metaDao.getAppMeta(AppMetaKey.ALL_AGENCY_GENERATE_DEFAULT_PERIOD_OPEN.toString())).thenReturn("false");
        when(agencyDao.getMeta(agencyId, AgencyMetaKey.GENERATE_DEFAULT_PERIOD_OPEN.toString())).thenReturn(null);
        ABTestOpenResponse abTestResponse3 = accountService.getABTestOpenResponse();
        Assert.assertEquals(abTestResponse3.getAbTestOpenModel().isT1(), false);
        Assert.assertEquals(abTestResponse3.getAbTestFeatureOpenModel().isCreateDefaultPeriodOpen(), false);
    }

    /**
     * 测试新自测用户是否生成周期
     */
    @Test
    public void testIsCreateDefaultRatingPeriod() {
        // 机构 id
        String agencyId = "cc088004-87ce-4eb3-a71f-753e1c36582c";
        // 机构 Id
        String agencyId1 = "********-f512-471c-8915-57ec1562a2ad";
        // 数据模拟
        when(metaDao.getAppMeta(AppMetaKey.ALL_AGENCY_GENERATE_DEFAULT_PERIOD_OPEN.toString())).thenReturn("false");
        boolean isCreateDefaultRatingPeriod = accountService.isCreateDefaultRatingPeriod(agencyId);
        Assert.assertEquals(isCreateDefaultRatingPeriod, true);

        boolean isCreateDefaultRatingPeriod1 = accountService.isCreateDefaultRatingPeriod(agencyId1);
        Assert.assertEquals(isCreateDefaultRatingPeriod1, false);

        when(metaDao.getAppMeta(AppMetaKey.ALL_AGENCY_GENERATE_DEFAULT_PERIOD_OPEN.toString())).thenReturn("true");
        boolean isCreateDefaultRatingPeriod2 = accountService.isCreateDefaultRatingPeriod(agencyId1);
        Assert.assertEquals(isCreateDefaultRatingPeriod2, true);
    }

    /**
     * 测试创建机构默认周期
     */
    @Test
    public void testCreateAgencyDemoRatingPeriod() {
        // 机构 Id
        String agencyId = "A001";
        String userId = "U001";
        // 机构信息
        AgencyModel agency = new AgencyModel();
        agency.setId(agencyId);

        SchoolYearsResponse schoolYearsResponse = new SchoolYearsResponse();
        List<SchoolYearModel> schoolYearModels = new ArrayList<>();
        SchoolYearModel schoolYearModel = new SchoolYearModel();
        schoolYearModel.setSchoolYear("current");
        schoolYearModel.setValue("2018-2019");
        schoolYearModels.add(schoolYearModel);
        schoolYearsResponse.setUnSettedSchoolYears(schoolYearModels);

        // 数据模拟
//        when(periodService.getSchoolYears(agencyId)).thenReturn(schoolYearsResponse);

        // 调用测试
        accountService.createAgencyDemoRatingPeriod(agencyId, userId);
        verify(agencyDao, times(1)).createMeta(agencyId, AgencyMetaKey.GENERATE_DEFAULT_PERIOD_OPEN.toString(), "true");;
    }

}

package com.learninggenie.api.service.impl;

import com.learninggenie.api.dao.impl.PhotoBookOrderMapper;
import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.model.Album;
import com.learninggenie.api.model.AlbumPageAction;
import com.learninggenie.api.provider.AlbumProvider;
import com.learninggenie.api.provider.CenterProvider;
import com.learninggenie.api.provider.EnrollmentProvider;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.service.CommonService;
import com.learninggenie.common.data.dao.CenterDao;
import com.learninggenie.common.data.dao.CentersMetaDataDao;
import com.learninggenie.common.data.dao.DistrictDao;
import com.learninggenie.common.data.dao.GroupDao;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.entity.*;
import com.learninggenie.common.data.enums.UserRole;
import com.learninggenie.common.data.repository.*;
import com.learninggenie.common.filesystem.FileSystem;
import com.learninggenie.common.utils.TimeUtil;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class AlbumServiceImplTest {
    @Mock
    private CentersMetaDataDao centersMetaDataDao;
    @Mock
    private UserRepository userRepository;
    @Mock
    private UserEnrollmentRepository userChildRelationRepository;
    @Mock
    private UserProvider userProvider;
    @Mock
    private UserDaoImpl userDao;
    @Mock
    private AlbumProvider albumProvider;
    @Mock
    private GroupDao groupDao;
    @Mock
    private CenterDao centerDao;
    @Mock
    private DistrictDao districtDao;
    @Mock
    private AlbumPageActionRepository albumPageActionRepository;
    @Mock
    private AlbumPageRepository pageRepository;
    @Mock
    private EnrollmentRepository enrollmentRepository;
    @Mock
    private FileSystem fileSystem;
    @Mock
    private AlbumRepository albumRepository;
    @Mock
    private CenterProvider centerProvider;
    @Mock
    private CommonService commonService;
    @Mock
    private AlbumSpecRepository specRepository;
    @Mock
    private AppMetadataRepository appMetadataRepository;
    @Mock
    private EnrollmentProvider enrollmentProvider;
    @Mock
    private PhotoBookOrderMapper photoBookOrderMapper;
    @InjectMocks
    private AlbumServiceImpl albumService;

    private Answer commonAnswer = new Answer() {
        @Override
        public Object answer(InvocationOnMock invocation) throws Throwable {
            return null;
        }
    };

    /**
     * 测试打开学校相册开关
     * 如果该学校之前没有设置过相册开关，应创建一条新记录，并设置为打开
     * 秦浩然 创建于2016/02/28
     */
    @Test
    public void testCenterAlbumOpen_WhenFirstSet() {
        //假设该学校之前没有设置过相册开关
        when(centersMetaDataDao.getCenter(anyString(), anyString())).thenReturn(Collections.EMPTY_LIST);

        albumService.centerAlbumOpen("true", "c001");
        //确保正确保存了新记录
        ArgumentCaptor<String> arg1 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> arg2 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> arg3 = ArgumentCaptor.forClass(String.class);
        verify(centersMetaDataDao, times(1)).insertMetavalue(arg1.capture(),arg2.capture(),arg3.capture());
        assertEquals("ALBUM_OPEN_FLAG", arg1.getValue());
        assertEquals("true", arg2.getValue());
        assertEquals("c001",arg3.getValue());
    }

    /**
     * 测试打开学校相册开关
     * 如果该学校之前设置过相册开关，应直接更新旧记录，不再创建新纪录
     * 秦浩然 创建于2016/02/28
     */
    @Test
    public void testCenterAlbumOpen_WhenHasSet() {
        //构造学校的metadata
        List<CenterMetaDataEntity> metas = new ArrayList<>();
        CenterMetaDataEntity meta1 = new CenterMetaDataEntity();
        meta1.setMetaKey("ALBUM_OPEN_FLAG");
        meta1.setMetaValue("false");
        metas.add(meta1);

        when(centersMetaDataDao.getCenter(anyString(), anyString())).thenReturn(metas);

        albumService.centerAlbumOpen("true", "c001");
        //确保更新的内容是正确的
        ArgumentCaptor<String> arg1 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> arg2 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> arg3 = ArgumentCaptor.forClass(String.class);
        verify(centersMetaDataDao, times(1)).updateMetaValue(arg1.capture(), arg2.capture(), arg3.capture());
        assertEquals("ALBUM_OPEN_FLAG", arg1.getValue());
        assertEquals("true", arg2.getValue());
        assertEquals("c001",arg3.getValue());

        //确保没有插入新记录
        verify(centersMetaDataDao, times(0)).insertMetavalue(anyString(), anyString(), anyString());
    }

    /**
     * 测试删除相册
     * param String id 要删除的相册id
     * 如果相册未找到，不进行任何操作
     * 秦浩然 创建于2016/02/28
     */
    @Test
    public void testDeleteAlbum_WhenAlbumNotFound(){
//        when(albumRepository.getOne(anyString())).thenReturn(null);
        //确保没有调用保存方法
        verify(albumRepository, times(0)).save(any(AlbumEntity.class));
    }

    /**
     * 测试删除相册
     * param String id 要删除的相册id
     * 如果相册已删除，不进行任何操作
     * 秦浩然 创建于2016/02/28
     */
    @Test
    public void testDeleteAlbum_WhenAlbumHasDeleted(){
        //构造一个相册
        AlbumEntity album = new AlbumEntity();
        album.setId("a001");
        album.setIsDeleted(true);

        when(albumRepository.getOne(anyString())).thenReturn(album);

        albumService.deleteAlbum("a001");
        //确保没有更新数据
        assertTrue(album.isDeleted());
        verify(albumRepository, times(0)).save(any(AlbumEntity.class));
    }

    /**
     * 测试删除相册
     * param String id 要删除的相册id
     * 秦浩然 创建于2016/02/28
     */
    @Test
    public void testDeleteAlbum(){
        //构造一个相册
        AlbumEntity album = new AlbumEntity();
        album.setId("a001");
        album.setIsDeleted(false);
        when(photoBookOrderMapper.query(anyString())).thenReturn(new ArrayList<AlbumOrderEntity>());
        when(albumRepository.getOne(anyString())).thenReturn(album);

        albumService.deleteAlbum("a001");
        //确保数据被正确更新到数据库
        ArgumentCaptor<AlbumEntity> albumEntityArgumentCaptor = ArgumentCaptor.forClass(AlbumEntity.class);
        verify(albumRepository, times(1)).save(albumEntityArgumentCaptor.capture());
        assertTrue(albumEntityArgumentCaptor.getValue().isDeleted());
    }

    /**
     * 测试获取相册 用户不存在 报异常
     * TODO:修复
     */
    @Ignore
    @Test(expected = BusinessException.class)
    public void testGetAlbumWithException(){
        //构造传入的参数
        String id="id1";
        String userId="userId1";
        //模拟家长用户
        UserEntity userEntity=new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole(UserRole.PARENT.toString());
        userEntity.setIsDeleted(true);
        when(userRepository.getOne(anyString())).thenReturn(userEntity);
        albumService.getAlbum(id,userId);
    }
    /**
     * 家长用户 获取相册  相册能找到
     */
    @Ignore
    @Test
    public void testGetAlbumOfParent(){
        String id="id1";
        String userId="userId1";
        //模拟家长用户
        UserEntity userEntity=new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole(UserRole.PARENT.toString());
        userEntity.setIsDeleted(false);
        when(userRepository.getOne(anyString())).thenReturn(userEntity);

        //模拟相册
        AlbumEntity album=new AlbumEntity();
        album.setId(id);
        //模拟学生
        EnrollmentEntity enrollmentEntity=new EnrollmentEntity();
        enrollmentEntity.setId("childId1");
        album.setEnrollment(enrollmentEntity);
        when(albumRepository.findOneWithSpecAndPages(anyString())).thenReturn(album);
        UserEnrollmentEntity relation=new UserEnrollmentEntity();
        relation.setEnrollmentId("childId1");
        relation.setUserId("userId1");
        when(userChildRelationRepository.getTop1ByUserIdAndEnrollmentIdAndIsDeletedFalse(anyString(),anyString())).thenReturn(relation);
        albumService.getAlbum(id,userId);
        assertEquals(id,album.getId());
    }

    /**
     * 家长用户 获取相册  相册找不到
     * TODO:修复
     */
    @Ignore
    @Test(expected = BusinessException.class)
    public void testGetAlbumOfParentNotFound(){
        String id="id1";
        String userId="userId1";
        //模拟家长用户
        UserEntity userEntity=new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole(UserRole.PARENT.toString());
        userEntity.setIsDeleted(false);
        when(userRepository.getOne(anyString())).thenReturn(userEntity);

        //模拟相册
        AlbumEntity album=new AlbumEntity();
        album.setId(id);
        //模拟学生
        EnrollmentEntity enrollmentEntity=new EnrollmentEntity();
        enrollmentEntity.setId("childId1");
        album.setEnrollment(enrollmentEntity);
        albumService.getAlbum(id,userId);
    }
    /**
     * 家长用户 获取相册  家长和孩子没关系
     * TODO:修复
     */
    @Ignore
    @Test(expected = BusinessException.class)
    public void testGetAlbumOfParentNoRelation(){
        String id="id1";
        String userId="userId1";
        //模拟家长用户
        UserEntity userEntity=new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole(UserRole.PARENT.toString());
        userEntity.setIsDeleted(false);
        when(userRepository.getOne(anyString())).thenReturn(userEntity);

        //模拟相册
        AlbumEntity album=new AlbumEntity();
        album.setId(id);
        //模拟学生
        EnrollmentEntity enrollmentEntity=new EnrollmentEntity();
        enrollmentEntity.setId("childId1");
        album.setEnrollment(enrollmentEntity);
        when(albumRepository.findOneWithSpecAndPages(anyString())).thenReturn(album);
        when(userChildRelationRepository.getTop1ByUserIdAndEnrollmentIdAndIsDeletedFalse(anyString(),anyString())).thenReturn(null);
        albumService.getAlbum(id,userId);
    }

    /**
     * 测试获取相册
     *
     * 如果相册处于可编辑状态（未提交），则自动追加新notes
     *
     * 秦浩然 创建于2016/03/23
     */
    @Ignore
    @Test
    public void testGetAlbum_AutoAppend() {
        UserEntity user = new UserEntity();//user
        user.setId("u001");
        AlbumEntity album = new AlbumEntity();//album
        album.setId("a001");
        album.setStatus("DRAFT");
        album.setStartDate(TimeUtil.parse("2016/01/01", "yyyy/MM/dd"));
        album.setEndDate(TimeUtil.parse("2016/03/01", "yyyy/MM/dd"));
        album.setLastNoteCreateAtLocal(TimeUtil.parse("2016/02/12 13:25:20", "yyyy/MM/dd HH:mm:ss"));
        AlbumSpecEntity spec = new AlbumSpecEntity();//spec
        spec.setId("s001");
        AlbumPageTemplateEntity template = new AlbumPageTemplateEntity();//template
        template.setId("t001");
        template.setWidePictureNum(1);
        template.setSquarePictureNum(0);
        template.setTallPictureNum(0);
        template.setTextNum(1);
        template.setType("CONTENT_PAGE");
        template.setHtmlTemplate("");
        template.setSpec(spec);
        spec.getPageTemplates().add(template);
        album.setSpec(spec);
        AlbumPageEntity page1 = new AlbumPageEntity();//page1
        page1.setId("p001");
        page1.setPageNum(0);
        page1.setType("COVER_PAGE");
        album.getPages().add(page1);
        AlbumPageEntity page2 = new AlbumPageEntity();//page2
        page2.setId("p002");
        page2.setPageNum(1);
        page2.setType("CONTENT_PAGE");
        album.getPages().add(page2);
        AlbumPageEntity page3 = new AlbumPageEntity();//page3
        page3.setId("p003");
        page3.setPageNum(2);
        page3.setType("END_PAGE");
        album.getPages().add(page3);
        AlbumPageEntity page4 = new AlbumPageEntity();//page4
        page4.setId("p004");
        page4.setPageNum(3);
        page4.setType("BACK_COVER_PAGE");
        album.getPages().add(page4);
        EnrollmentEntity child = new EnrollmentEntity();//child
        child.setId("c001");
        NoteEntity note1 = new NoteEntity();//note1
        note1.setId("n001");
        note1.setCreateAtLocal(TimeUtil.getNow());
        NoteMediaEntity media1 = new NoteMediaEntity();
        media1.setId("m001");
        media1.setWidth(100);
        media1.setHeight(20);
        media1.setRelativePath("abc.jpg");
        note1.getMedias().add(media1);
        child.getNotes().add(note1);
        GroupEntity group = new GroupEntity();
        group.setId("g001");
        child.setGroup(group);
        CenterEntity center = new CenterEntity();
        center.setId("center001");
        group.setCenter(center);
        album.setEnrollment(child);

        when(userProvider.checkUser("u001")).thenReturn(user);
        when(albumProvider.checkAlbum("a001")).thenReturn(album);
        doAnswer(commonAnswer).when(albumProvider).checkPermission(album, user);
        when(enrollmentRepository.getNotesWithPicturesExcludeStartDate(eq("c001"), any(Date.class), any(Date.class))).thenReturn(child);
        when(fileSystem.getPublicUrl(anyString())).thenReturn("http://abc.jpg");
        when(albumRepository.save(album)).thenReturn(album);
        when(albumPageActionRepository.getUndoCount("a001")).thenReturn(1);
        when(albumPageActionRepository.getRedoCount("a001")).thenReturn(1);
        when(centerProvider.getOpenValue("center001", "ALBUM_TEXT_OPEN_FLAG")).thenReturn(true);

        Album result = albumService.getAlbum("a001", "u001");
        assertEquals(5, result.getPages().size());// 因为多了一条note，所以多了一页
        assertEquals(5, album.getPages().size());
        assertEquals(3, page3.getPageNum());// 尾页页码应该从2变成3
        assertEquals(4, page4.getPageNum());// 封底页码应该从3变成4
    }

    /**
     * 测试相册undo功能
     *
     * 测试undo创建操作：
     * 1.应删除创建的相册页并更新页码
     * 2.应把该操作移到redo列表
     *
     * 秦浩然 创建于2016/03/22
     */
    @Test
    public void testUndo_CreateAction() {
        UserEntity user = new UserEntity();
        AlbumEntity album = new AlbumEntity();
        AlbumSpecEntity spec = new AlbumSpecEntity();
        album.setSpec(spec);
        album.setStatus("DRAFT");
        List<AlbumPageActionEntity> actions = new ArrayList<>();
        AlbumPageActionEntity action1 = new AlbumPageActionEntity();
        action1.setAction("CREATE");
        action1.setStackType("UNDO");
        action1.setData("{\"id\":\"p001\",\"pageNum\":1}");
        actions.add(action1);

        when(userProvider.checkUser("u001")).thenReturn(user);
        when(albumProvider.checkAlbum("a001")).thenReturn(album);
        doAnswer(commonAnswer).when(albumProvider).checkPermission(album, user);
        when(albumPageActionRepository.getUndoActions("a001")).thenReturn(actions);
//        doAnswer(commonAnswer).when(pageRepository).delete("p001");
        doAnswer(commonAnswer).when(pageRepository).nativeUpdatePageNumAfterDelete(1, "a001");
        when(albumPageActionRepository.save(action1)).thenReturn(action1);

        AlbumPageAction result = albumService.undo("a001", "u001");
        verify(pageRepository, times(1)).nativeDelete("p001");//确保删除了该页
        verify(pageRepository, times(1)).nativeUpdatePageNumAfterDelete(1, "a001");//确保更新了页码
        assertEquals(action1.getStackType(), "REDO");//该操作应该被移到了REDO列表
        verify(albumPageActionRepository, times(1)).save(action1);//确保保存了更改
        //验证返回结果
        assertEquals(result.getAction(), "CREATE");
        assertEquals(result.getPage().getId(), "p001");
    }

    /**
     * 测试相册undo功能
     *
     * 测试undo删除操作：
     * 1.应创建删除的相册页并更新页码
     * 2.应把该操作移到redo列表
     *
     * 秦浩然 创建于2016/03/22
     */
    @Test
    public void testUndo_DeleteAction() {
        UserEntity user = new UserEntity();
        AlbumEntity album = new AlbumEntity();
        AlbumSpecEntity spec = new AlbumSpecEntity();
        album.setSpec(spec);
        album.setStatus("DRAFT");
        List<AlbumPageActionEntity> actions = new ArrayList<>();
        AlbumPageActionEntity action1 = new AlbumPageActionEntity();
        action1.setAction("DELETE");
        action1.setStackType("UNDO");
        action1.setData("{\"id\":\"p001\",\"pageNum\":1}");
        actions.add(action1);

        when(userProvider.checkUser("u001")).thenReturn(user);
        when(albumProvider.checkAlbum("a001")).thenReturn(album);
        doAnswer(commonAnswer).when(albumProvider).checkPermission(album, user);
        when(albumPageActionRepository.getUndoActions("a001")).thenReturn(actions);
        doAnswer(commonAnswer).when(pageRepository).save(any(AlbumPageEntity.class));
        doAnswer(commonAnswer).when(pageRepository).nativeUpdatePageNumAfterInsert(1, "a001");
        when(albumPageActionRepository.save(action1)).thenReturn(action1);

        AlbumPageAction result = albumService.undo("a001", "u001");
        verify(pageRepository, times(1)).save(any(AlbumPageEntity.class));//确保创建了该页
        verify(pageRepository, times(1)).nativeUpdatePageNumAfterInsert(1, "a001");//确保更新了页码
        assertEquals(action1.getStackType(), "REDO");//该操作应该被移到了REDO列表
        verify(albumPageActionRepository, times(1)).save(action1);//确保保存了更改
        //验证返回结果
        assertEquals(result.getAction(), "DELETE");
        assertEquals(result.getPage().getId(), "p001");
    }

    /**
     * 测试相册undo功能
     *
     * 测试undo更新操作：
     * 1.应还原相册页内容
     * 2.应把该操作移到redo列表
     *
     * 秦浩然 创建于2016/03/22
     */
    @Test
    public void testUndo_UpdateAction() {
        UserEntity user = new UserEntity();
        AlbumEntity album = new AlbumEntity();
        AlbumSpecEntity spec = new AlbumSpecEntity();
        album.setSpec(spec);
        album.setStatus("DRAFT");
        List<AlbumPageActionEntity> actions = new ArrayList<>();
        AlbumPageActionEntity action1 = new AlbumPageActionEntity();
        action1.setAction("UPDATE");
        action1.setStackType("UNDO");
        action1.setData("{\"id\":\"p001\",\"pageNum\":1,\"html\":\"qwe\"}");
        actions.add(action1);
        AlbumPageEntity page = new AlbumPageEntity();
        page.setHtml("abc");

        when(userProvider.checkUser("u001")).thenReturn(user);
        when(albumProvider.checkAlbum("a001")).thenReturn(album);
        doAnswer(commonAnswer).when(albumProvider).checkPermission(album, user);
        when(albumPageActionRepository.getUndoActions("a001")).thenReturn(actions);
        when(pageRepository.findById(anyString())).thenReturn(Optional.of(page));
        doAnswer(commonAnswer).when(pageRepository).save(any(AlbumPageEntity.class));
//        doAnswer(commonAnswer).when(pageRepository).nativeUpdatePageNumAfterInsert(1, "a001");
        when(albumPageActionRepository.save(action1)).thenReturn(action1);

        AlbumPageAction result = albumService.undo("a001", "u001");
        assertEquals(page.getHtml(), "qwe");// 确保更新了该页内容
        verify(pageRepository, times(1)).save(page);//确保保存了更新
        assertEquals(action1.getStackType(), "REDO");//该操作应该被移到了REDO列表
        verify(albumPageActionRepository, times(1)).save(action1);//确保保存了更改
        //验证返回结果
        assertEquals(result.getAction(), "UPDATE");
        assertEquals(result.getPage().getId(), "p001");
    }

    /**
     * 测试相册redo功能
     *
     * 测试redo创建操作：
     * 1.应创建删除的相册页并更新页码
     * 2.应把该操作移到undo列表
     *
     * 秦浩然 创建于2016/03/22
     */
    @Test
    public void testRedo_CreateAction() {
        UserEntity user = new UserEntity();
        AlbumEntity album = new AlbumEntity();
        AlbumSpecEntity spec = new AlbumSpecEntity();
        album.setSpec(spec);
        album.setStatus("DRAFT");
        List<AlbumPageActionEntity> actions = new ArrayList<>();
        AlbumPageActionEntity action1 = new AlbumPageActionEntity();
        action1.setAction("CREATE");
        action1.setStackType("REDO");
        action1.setData("{\"id\":\"p001\",\"pageNum\":1}");
        actions.add(action1);

        when(userProvider.checkUser("u001")).thenReturn(user);
        when(albumProvider.checkAlbum("a001")).thenReturn(album);
        doAnswer(commonAnswer).when(albumProvider).checkPermission(album, user);
        when(albumPageActionRepository.getRedoActions("a001")).thenReturn(actions);
        doAnswer(commonAnswer).when(pageRepository).save(any(AlbumPageEntity.class));
        doAnswer(commonAnswer).when(pageRepository).nativeUpdatePageNumAfterInsert(1, "a001");
        when(albumPageActionRepository.save(action1)).thenReturn(action1);

        AlbumPageAction result = albumService.redo("a001", "u001");
        verify(pageRepository, times(1)).save(any(AlbumPageEntity.class));//确保创建了该页
        verify(pageRepository, times(1)).nativeUpdatePageNumAfterInsert(1, "a001");//确保更新了页码
        assertEquals(action1.getStackType(), "UNDO");//该操作应该被移到了undo列表
        verify(albumPageActionRepository, times(1)).save(action1);//确保保存了更改
        //验证返回结果
        assertEquals(result.getAction(), "CREATE");
        assertEquals(result.getPage().getId(), "p001");
    }

    /**
     * 测试相册redo功能
     *
     * 测试redo删除操作：
     * 1.应删除创建的相册页并更新页码
     * 2.应把该操作移到undo列表
     *
     * 秦浩然 创建于2016/03/22
     */
    @Test
    public void testRedo_DeleteAction() {
        UserEntity user = new UserEntity();
        AlbumEntity album = new AlbumEntity();
        AlbumSpecEntity spec = new AlbumSpecEntity();
        album.setSpec(spec);
        album.setStatus("DRAFT");
        List<AlbumPageActionEntity> actions = new ArrayList<>();
        AlbumPageActionEntity action1 = new AlbumPageActionEntity();
        action1.setAction("DELETE");
        action1.setStackType("REDO");
        action1.setData("{\"id\":\"p001\",\"pageNum\":1}");
        actions.add(action1);

        when(userProvider.checkUser("u001")).thenReturn(user);
        when(albumProvider.checkAlbum("a001")).thenReturn(album);
        doAnswer(commonAnswer).when(albumProvider).checkPermission(album, user);
        when(albumPageActionRepository.getRedoActions("a001")).thenReturn(actions);
//        doAnswer(commonAnswer).when(pageRepository).delete("p001");
        doAnswer(commonAnswer).when(pageRepository).nativeUpdatePageNumAfterDelete(1, "a001");
        when(albumPageActionRepository.save(action1)).thenReturn(action1);

        AlbumPageAction result = albumService.redo("a001", "u001");
        verify(pageRepository, times(1)).nativeDelete("p001");//确保删除了该页
        verify(pageRepository, times(1)).nativeUpdatePageNumAfterDelete(1, "a001");//确保更新了页码
        assertEquals(action1.getStackType(), "UNDO");//该操作应该被移到了undo列表
        verify(albumPageActionRepository, times(1)).save(action1);//确保保存了更改
        //验证返回结果
        assertEquals(result.getAction(), "DELETE");
        assertEquals(result.getPage().getId(), "p001");
    }

    /**
     * 测试相册redo功能
     *
     * 测试redo更新操作：
     * 1.应还原相册页内容
     * 2.应把该操作移到undo列表
     *
     * 秦浩然 创建于2016/03/22
     */
    @Test
    public void testRedo_UpdateAction() {
        UserEntity user = new UserEntity();
        AlbumEntity album = new AlbumEntity();
        AlbumSpecEntity spec = new AlbumSpecEntity();
        album.setSpec(spec);
        album.setStatus("DRAFT");
        List<AlbumPageActionEntity> actions = new ArrayList<>();
        AlbumPageActionEntity action1 = new AlbumPageActionEntity();
        action1.setAction("UPDATE");
        action1.setStackType("REDO");
        action1.setData("{\"id\":\"p001\",\"pageNum\":1,\"html\":\"qwe\"}");
        actions.add(action1);
        AlbumPageEntity page = new AlbumPageEntity();
        page.setHtml("abc");

        when(userProvider.checkUser("u001")).thenReturn(user);
        when(albumProvider.checkAlbum("a001")).thenReturn(album);
        doAnswer(commonAnswer).when(albumProvider).checkPermission(album, user);
        when(albumPageActionRepository.getRedoActions("a001")).thenReturn(actions);
        when(pageRepository.findById(anyString())).thenReturn(Optional.of(page));
        doAnswer(commonAnswer).when(pageRepository).save(any(AlbumPageEntity.class));
//        doAnswer(commonAnswer).when(pageRepository).nativeUpdatePageNumAfterInsert(1, "a001");
        when(albumPageActionRepository.save(action1)).thenReturn(action1);

        AlbumPageAction result = albumService.redo("a001", "u001");
        assertEquals(page.getHtml(), "qwe");// 确保更新了该页内容
        verify(pageRepository, times(1)).save(page);//确保保存了更新
        assertEquals(action1.getStackType(), "UNDO");//该操作应该被移到了UNDO列表
        verify(albumPageActionRepository, times(1)).save(action1);//确保保存了更改
        //验证返回结果
        assertEquals(result.getAction(), "UPDATE");
        assertEquals(result.getPage().getId(), "p001");
    }

    /**
     * 测试生成相册
     *
     * 如果参数 force 为 false，并且有未提交的相册，则抛出异常
     *
     * 秦浩然 创建于 2016/04/15
     */
    @Test(expected = BusinessException.class)
    public void testGenerateAlbum_NotForce() {
        String dateFormat = "MM/dd/yyyy";
        EnrollmentEntity student = new EnrollmentEntity();
        student.setId("s001");
        UserEntity user = new UserEntity();
        user.setId("u001");
        List<AlbumEntity> drafts = new ArrayList<>();
        AlbumEntity draft1 = new AlbumEntity();
        draft1.setId("d001");
        drafts.add(draft1);

        when(enrollmentProvider.checkEnrollment("s001")).thenReturn(student);
        when(userProvider.checkUser("u001")).thenReturn(user);
        when(albumRepository.getDraftAlbums("s001")).thenReturn(drafts);

        albumService.generateAlbum("s001", "a001", TimeUtil.parse("01/01/2016", dateFormat), TimeUtil.parse("06/01/2016", dateFormat), "u001", false, TimeUtil.getNow(), false, "");
    }

    /**
     * 测试生成相册
     *
     * 如果参数 force 为 false，并且有未提交的相册，则删除未提交的相册并创建新的相册
     *
     * 秦浩然 创建于 2016/04/15
     */
    @Ignore // TODO: 补完
    @Test(expected = BusinessException.class)
    public void testGenerateAlbum_Force() {
        String dateFormat = "MM/dd/yyyy";
        CenterEntity center1 = new CenterEntity(); // center1
        center1.setId("center1");
        center1.setName("Center 1");
        GroupEntity group1 = new GroupEntity(); // group1
        group1.setId("group1");
        group1.setName("Group 1");
        group1.setCenter(center1);
        center1.getGroups().add(group1);
        UserEntity user1 = new UserEntity(); // user1
        user1.setId("u001");

        List<AlbumEntity> drafts = new ArrayList<>(); // draft list
        AlbumEntity draft1 = new AlbumEntity(); // draft1
        draft1.setId("d001");
        drafts.add(draft1);

        EnrollmentEntity child1 = new EnrollmentEntity(); // child
        child1.setId("c001");
        child1.setFirstName("Wa");
        child1.setLastName("Ha");
        child1.setGroup(group1);
        group1.getEnrollments().add(child1);
        NoteEntity note1 = new NoteEntity(); // note1
        note1.setId("n001");
        note1.setPayload("How are you ?");
        NoteMediaEntity photo1 = new NoteMediaEntity(); // photo1
        photo1.setId("p001");
        photo1.setRelativePath("p001.jpg");
        photo1.setWidth(100);
        photo1.setHeight(300);
        note1.getMedias().add(photo1);
        child1.getNotes().add(note1);
        NoteEntity note2 = new NoteEntity(); // note2
        note2.setId("n002");
        note2.setPayload("What's your name ?");
        NoteMediaEntity photo2 = new NoteMediaEntity(); // photo2
        photo2.setId("p002");
        photo2.setRelativePath("p002.jpg");
        photo2.setWidth(201);
        photo2.setHeight(210);
        note2.getMedias().add(photo2);
        child1.getNotes().add(note2);

        AlbumPageTemplateEntity template1 = new AlbumPageTemplateEntity(); // template1
        template1.setId("t001");
        template1.setHtmlTemplate("<div></div>");

        when(userProvider.checkUser("u001")).thenReturn(user1);
        when(albumRepository.getDraftAlbums("c001")).thenReturn(drafts);
        doAnswer(commonAnswer).when(albumRepository).deleteDraftAlbums("c001");
        when(commonService.getCenterLogoUrl(any(MediaEntity.class))).thenReturn("center_logo.jpg");
        when(albumProvider.checkTemplate("t001")).thenReturn(template1);

        albumService.generateAlbum("c001", "a001", TimeUtil.parse("01/01/2016", dateFormat), TimeUtil.parse("06/01/2016", dateFormat), "u001", false, TimeUtil.getNow(), false, "");

        verify(albumRepository, times(1)).deleteDraftAlbums("c001");
    }

}

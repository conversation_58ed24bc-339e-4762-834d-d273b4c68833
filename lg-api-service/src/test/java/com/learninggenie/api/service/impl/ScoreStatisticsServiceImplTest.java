package com.learninggenie.api.service.impl;

import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.service.PortfolioService;
import com.learninggenie.common.data.dao.*;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.entity.CenterEntity;
import com.learninggenie.common.data.entity.GroupEntity;
import com.learninggenie.common.data.entity.*;
import com.learninggenie.common.data.enums.ExcelType;
import com.learninggenie.common.data.model.NoteEntity;
import com.learninggenie.common.data.model.*;
import com.learninggenie.common.data.repository.CenterDistrictRepository;
import com.learninggenie.common.data.repository.CenterRepository;
import com.learninggenie.common.data.repository.EnrollmentMetadataRepository;
import com.learninggenie.common.data.repository.UserRepository;
import com.learninggenie.common.messaging.EmailModel;
import com.learninggenie.common.report.AnalysisService;
import com.learninggenie.common.report.CheckLockResult;
import com.learninggenie.common.score.RatingService;
import junit.framework.Assert;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.internal.verification.Times;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.isA;

@RunWith(MockitoJUnitRunner.class)
public class ScoreStatisticsServiceImplTest {
    @Mock
    private UserRepository userRepository;
    @Mock
    private ScoreDao scoreDao;
    @Mock
    private PortfolioService portfolioService;
    @Mock
    private NoteDao noteDao;
    @Mock
    private CenterDistrictRepository centerDistrictRepository;
    @Mock
    private GroupDao groupDao;
    @Mock
    private DomainDao domainDao;
    @Mock
    private EnrollmentMetadataRepository enrollmentMetadataRepository;
    @Mock
    private CommonServiceImpl commonService;
    @Mock
    private com.learninggenie.common.messaging.EmailService emailService;
    @Mock
    private UserDaoImpl userDao;
    @Mock
    private CenterRepository centerRepository;
    @Mock
    private StudentDao studentDao;
    @InjectMocks
    private ScoreStatisticsServiceImpl scoreStatisticsService;
    @Mock
    private UserProvider userProvider;
    @Mock
    private AnalysisService analysisService;
    @Mock
    private RatingService ratingService;

    /**
     * 获取excel信息
     * 传入参数
     * userId，用户id
     * start,开始时间
     * end,结束时间
     * <p/>
     * 根据userId获取“userEntity”实体类
     * 如果userEntity的Role不是MOWER则添加用户的学校信息
     * 如果是MOWER则根据MOWER的区域抓取区域学校，前提该MOWER有区域
     * <p/>
     * 最终会得到一个学校实体
     * 然后根据学校中的domainId分类
     * <!--括号中为实现逻辑-->
     * （如果该学校某个班级的某个学生的domainId和“idIT2015”中的数据一样itEnrollmentEntities中增加该学生信息
     * 如果该学校某个班级的某个学生的domainId和“idPS2015”中的数据一样psEnrollmentEntities中增加该学生信息
     * 如果该学校某个班级的某个学生的domainId和“idK2015”中的数据一样kEnrollmentEntities中增加该学生信息
     * 如果该学校某个班级的某个学生的domainId和“idSA2012”中的数据一样saEnrollmentEntities中增加该学生信息）
     * <p/>
     * <p/>
     * 开始获取评分模版
     * 用集合记录成绩
     * <p/>
     * <!--括号中为实现逻辑-->
     * （如果itEnrollmentEntities有数据则获取该评分模版下学生的成绩，用集合记录成绩
     * 如果psEnrollmentEntities有数据则获取该评分模版下学生的成绩，用集合记录成绩
     * 如果kEnrollmentEntities有数据则获取该评分模版下学生的成绩，用集合记录成绩
     * 如果saEnrollmentEntities有数据则获取该评分模版下学生的成绩，用集合记录成绩）
     * <p/>
     * 最后以流的形式返回学生信息，以及成绩
     */
    @Test
    public void testGetDRDPExcel() throws Exception {
        com.learninggenie.common.data.entity.UserEntity userEntity = new com.learninggenie.common.data.entity.UserEntity();

        List<CenterWithIdName> centers = new ArrayList<>();
        CenterWithIdName center001 = new CenterWithIdName();
        center001.setId("c001");
        center001.setName("Center 001");
        centers.add(center001);

        //设置用户角色为mower
        userEntity.setRole("Agency_Owner");
        //创建区域实体类
        DistrictEntity districtEntity = new DistrictEntity();
        //添加区域实体类
        userEntity.setDistrict(districtEntity);
        Set<CenterEntity> centerEntitySet = new HashSet<>();
        userEntity.setCenters(centerEntitySet);
        CenterDistrictEntity centerDistrictEntity = new CenterDistrictEntity();
        CenterEntity center = new CenterEntity();
        center.setIsDeleted(false);
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setIsDeleted(false);
        Set<GroupEntity> groupEntitySet = new HashSet<>();
        groupEntitySet.add(groupEntity);
        center.setGroups(groupEntitySet);
        com.learninggenie.common.data.entity.DomainEntity domainEntity = new com.learninggenie.common.data.entity.DomainEntity();
        domainEntity.setId("123");
        groupEntity.setDomain(domainEntity);
        EnrollmentEntity enrollmentEntity = new EnrollmentEntity();
        enrollmentEntity.setIsDeleted(false);
        Set<EnrollmentEntity> enrollmentEntitySet = new HashSet<>();
        enrollmentEntitySet.add(enrollmentEntity);
        groupEntity.setEnrollments(enrollmentEntitySet);
        centerDistrictEntity.setCenter(center);

//        Mockito.when(userDao.getCenterIdNameByAgencyAdmin(anyString())).thenReturn(centers);
//        Mockito.when(centerRepository.findById(anyString()).orElse(null)).thenReturn(center);

        //返回用户实体类
//        Mockito.when(userRepository.findById(anyString()).orElse(null)).thenReturn(userEntity);
        List<CenterDistrictEntity> centerDistrictEntities = new ArrayList<>();
        centerDistrictEntities.add(centerDistrictEntity);
        centerDistrictEntities.add(centerDistrictEntity);
        centerDistrictEntities.add(centerDistrictEntity);
        centerDistrictEntities.add(centerDistrictEntity);
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setName("aa");
//        Mockito.when(userProvider.getAgencyByUserId(anyString())).thenReturn(agencyModel);
        //返回学校区域集合
//        Mockito.when(centerDistrictRepository.findByDistrictIdAndIsDeletedFalse(anyString())).thenReturn(centerDistrictEntities);
        //ReflectionTestUtils.setField(scoreStatisticsService, "idIT2015", "123");
        //测试地区不为null
//        OutputStream out = scoreStatisticsService.getDRDPExcel("", "", "");
//        Assert.assertTrue(out != null);
    }

    /**
     * 根据班级id返回班级实体
     */
    @Test
    public void testGetGroup() throws Exception {
        GroupEntry gpe = new GroupEntry();
        gpe.setId("123");
        gpe.setName("qqq");
        //返回班级实体
        Mockito.when(groupDao.getGroup(anyString())).thenReturn(gpe);
        GroupEntry gp1 = scoreStatisticsService.getGroup("123");
        Assert.assertTrue(gp1 != null);
        Assert.assertEquals("123", gp1.getId());
        Assert.assertEquals("qqq", gp1.getName());
    }

    /**
     * 根据domainId获取domainId
     */
    @Test
    public void testGetDomain() throws Exception {
        com.learninggenie.common.data.model.DomainEntity domainEntity = new com.learninggenie.common.data.model.DomainEntity();
        domainEntity.setId("123");
        //获取domain实体
        Mockito.when(domainDao.getDomain(anyString())).thenReturn(domainEntity);
        com.learninggenie.common.data.model.DomainEntity domainEntity1 = scoreStatisticsService.getDomain("1123");
        Assert.assertTrue(domainEntity1 != null);
        Assert.assertEquals("123", domainEntity1.getId());
    }

    /**
     * 加载excel信息
     * 传递参数
     * groupId，班级id
     * start，开始时间
     * end，结束时间
     * type，传递类型
     * inputStream，文件流
     * ignoreGroupName，是否忽略班级名字
     * ignoreNotMatch，是否忽略是否匹配
     * <p/>
     * 先根据班级id获取学生列表
     * 再根据班级id获取班级列表
     * 最后根据班级列表获取domainId
     * 根据domainId看属于哪种类型，进行分类
     * 根据分类获取评分title
     */
    @Ignore
    @Test
    public void testWriteExcelByGroup() throws Exception {
        List<ChildEntity> children = new ArrayList<>();
        ChildEntity childEntity = new ChildEntity();
        com.learninggenie.common.data.model.GroupEntity ge = new com.learninggenie.common.data.model.GroupEntity();
        ge.setName("456");
        childEntity.setGroup(ge);
        childEntity.setFirstName("h");
        childEntity.setLastName("j");
        children.add(childEntity);
        //根据班级id获取班级学生实体
        Mockito.when(groupDao.getChilds(anyString())).thenReturn(children);
        GroupEntry group = new GroupEntry();
        group.setDomainId("123");
        //根据班级获取班级实体
        Mockito.when(groupDao.getGroup(anyString())).thenReturn(group);
        com.learninggenie.common.data.model.DomainEntity domainEntity = new com.learninggenie.common.data.model.DomainEntity();
        domainEntity.setId("123");
        domainEntity.setName("DRDPPS-2015");
        //根据班级的domainId
        Mockito.when(domainDao.getDomain(anyString())).thenReturn(domainEntity);
        ReflectionTestUtils.setField(scoreStatisticsService, "idIT2015", "123");
        ScoreTemplateEntity scoreTemplateEntry = new ScoreTemplateEntity();
        scoreTemplateEntry.setDomainLevelsJson("");
        scoreTemplateEntry.setLevelsJson("[{\"id\":\"C16B22DF-09A1-4657-8ED4-509E3310C781\",\"name\":\"Emerging\",\"type\":\"radio\",\"sortIndex\":\"\",\"value\":\"1\"},{\"id\":\"7D80B5EA-5BA6-48A3-814E-A953845B71D1\",\"name\":\"Exploring\",\"type\":\"radio\",\"sortIndex\":\"\",\"value\":\"2\"},{\"id\":\"C5F7C569-3ABB-42F4-B208-DD278F8911ED\",\"name\":\"Extending\",\"type\":\"radio\",\"sortIndex\":\"\",\"value\":\"3\"}]");
        scoreTemplateEntry.setPortfolioId("111");
        //没有评分评分模版则返回为null
        Mockito.when(portfolioService.getScoreTemplate(anyString())).thenReturn(null);
        List<StudentScoreEntity> studentScoreEntityList = new ArrayList<>();
        Mockito.when(scoreDao.get(anyString())).thenReturn(studentScoreEntityList);
        List<com.learninggenie.common.data.model.NoteEntity> notes = new ArrayList<>();
        Mockito.when(noteDao.getNotes(anyString(), anyString(), anyString(), Mockito.any(SimpleDateFormat.class))).thenReturn(notes);
        InputStream in = new FileInputStream("src/test/resources/template.xls");
        Map<String, Object> map = scoreStatisticsService.writeExcelByGroup("", "07/13/2015", "01/13/2016", "", in, true, true);
        Assert.assertTrue(map != null);
    }

    @Ignore
    @Test
    public void testGetDRDPExcel1() throws Exception {
        com.learninggenie.common.data.entity.UserEntity userEntity = new com.learninggenie.common.data.entity.UserEntity();
        //设置用户角色为非mower
        userEntity.setRole("Site_Admin");

        List<CenterWithIdName> centers = new ArrayList<>();
        CenterWithIdName center001 = new CenterWithIdName();
        center001.setId("c001");
        center001.setName("Center 001");
        centers.add(center001);
        //学校集合
        Set<CenterEntity> centerEntitySet = new HashSet<>();
        userEntity.setCenters(centerEntitySet);

        //建立学校实体
        CenterEntity center = new CenterEntity();
        center.setIsDeleted(false);

        //班级实体
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setName("c1");
        groupEntity.setIsDeleted(false);

        //班级集合set
        Set<GroupEntity> groupEntitySet = new HashSet<>();
        groupEntitySet.add(groupEntity);
        center.setName("center1");
        center.setGroups(groupEntitySet);
        centerEntitySet.add(center);

        //domain实体
        com.learninggenie.common.data.entity.DomainEntity domainEntity = new com.learninggenie.common.data.entity.DomainEntity();
        domainEntity.setId("123");
        groupEntity.setDomain(domainEntity);

        //学生实体
        EnrollmentEntity enrollmentEntity = new EnrollmentEntity();
        enrollmentEntity.setIsDeleted(false);
        enrollmentEntity.setGroup(groupEntity);

        groupEntity.setCenter(center);

        //学生集合
        Set<EnrollmentEntity> enrollmentEntitySet = new HashSet<>();
        enrollmentEntitySet.add(enrollmentEntity);
        groupEntity.setEnrollments(enrollmentEntitySet);
        //返回用户实体类
        Mockito.when(userRepository.findById(anyString()).orElse(null)).thenReturn(userEntity);
        ReflectionTestUtils.setField(scoreStatisticsService, "idIT2015", "123");

        Mockito.when(userDao.getCenterIdNameBySiteAdminId(anyString())).thenReturn(centers);
        Mockito.when(centerRepository.findById(anyString()).orElse(null)).thenReturn(center);

        EnrollmentMetaDataEntity enrollmentMetaDataEntity = new EnrollmentMetaDataEntity();
        enrollmentMetaDataEntity.setMetaValue("1");
        Mockito.when(enrollmentMetadataRepository.findTop1ByMetaKeyAndEnrollmentId(anyString(), anyString())).thenReturn(enrollmentMetaDataEntity);

        List<com.learninggenie.common.data.model.DomainEntity> domainEntityList = new ArrayList<>();
        com.learninggenie.common.data.model.DomainEntity domainEntity1 = new com.learninggenie.common.data.model.DomainEntity();
        domainEntity1.setId("E363164F-BDCE-E411-AF66-02C72B94B99B");
        domainEntity1.setSortIndex(1);
        com.learninggenie.common.data.model.DomainEntity domainEntity2 = new com.learninggenie.common.data.model.DomainEntity();
        domainEntity2.setId("E463164F-BDCE-E411-AF66-02C72B94B99B");
        domainEntity2.setSortIndex(2);
        domainEntityList.add(domainEntity1);
        domainEntityList.add(domainEntity2);
        Mockito.when(domainDao.getAllChildDomains(anyString())).thenReturn(domainEntityList);

        ScoreTemplateEntity scoreTemplateEntry = new ScoreTemplateEntity();
        scoreTemplateEntry.setPortfolioId("p1");
        //scoreTemplateEntry.setDomainLevelsJson("[{\"domainId\":\"E363164F-BDCE-E411-AF66-02C72B94B99B\",\"measure\":\"ATL–REG1\",\"measureName\":\"Attention Maintenance\",\"levels\":[{\"id\":\"A7BF0C28-B115-4A48-A087-2E9D19FBE4DB\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Shifts attention frequently from one person or thing to another\"},{\"id\":\"1B8B7FBD-A13D-4B79-B474-F35FB479F9DF\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"tip\":\"Maintains attention, on own or with adult support, during brief activities\"},{\"id\":\"5AF52F6D-FA16-4EA5-84AB-6B0B6EB9AD03\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Maintains attention, with adult support, during activities that last for extended periods of time\"},{\"id\":\"1F997653-3111-4AA3-8444-C7333332F025\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Maintains attention on own during activities that last for extended periods of time\"},{\"id\":\"0F8D73D2-491E-41EE-B942-4E39BEFB0373\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\"},{\"id\":\"4F9F07FB-C23A-4781-92F5-526B3F4892FA\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"},{\"id\":\"9136A78A-71AB-4D27-B1FE-1FF83291F7E3\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\"}]},{\"domainId\":\"E463164F-BDCE-E411-AF66-02C72B94B99B\",\"measure\":\"ATL–REG2\",\"measureName\":\"Self-Comforting\",\"levels\":[{\"id\":\"9136A78A-71AB-4D27-B1FE-1FF83291F7E3\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"tip\":\"Responds to internal or external stimulation in basic ways\"},{\"id\":\"A7BF0C28-B115-4A48-A087-2E9D19FBE4DB\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Engages in behaviors that have previously worked to soothe self\"},{\"id\":\"1B8B7FBD-A13D-4B79-B474-F35FB479F9DF\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"tip\":\"Comforts self by seeking a familiar adult or a special thing\"},{\"id\":\"5AF52F6D-FA16-4EA5-84AB-6B0B6EB9AD03\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Comforts self in different ways, based on the situation\"},{\"id\":\"1F997653-3111-4AA3-8444-C7333332F025\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Anticipates need for comfort and prepares self by asking questions, getting a special thing, or in other ways\"},{\"id\":\"0F8D73D2-491E-41EE-B942-4E39BEFB0373\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\"},{\"id\":\"4F9F07FB-C23A-4781-92F5-526B3F4892FA\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"}]},{\"domainId\":\"E763164F-BDCE-E411-AF66-02C72B94B99B\",\"measure\":\"ATL–REG4\",\"measureName\":\"Curiosity and Initiative in Learning\",\"levels\":[{\"id\":\"9136A78A-71AB-4D27-B1FE-1FF83291F7E3\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"tip\":\"Responds to people, things, or sounds\"},{\"id\":\"A7BF0C28-B115-4A48-A087-2E9D19FBE4DB\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Notices new or unexpected characteristics or actions of people or things\"},{\"id\":\"1B8B7FBD-A13D-4B79-B474-F35FB479F9DF\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"tip\":\"Explores people or things in the immediate environment\"},{\"id\":\"5AF52F6D-FA16-4EA5-84AB-6B0B6EB9AD03\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Explores new ways to use familiar things, including simple trial and error\"},{\"id\":\"1F997653-3111-4AA3-8444-C7333332F025\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Explores through simple observations, manipulations, or asking simple questions\"},{\"id\":\"0F8D73D2-491E-41EE-B942-4E39BEFB0373\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\"},{\"id\":\"4F9F07FB-C23A-4781-92F5-526B3F4892FA\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"}]},{\"domainId\":\"FE63164F-BDCE-E411-AF66-02C72B94B99B\",\"measure\":\"ATL–REG5\",\"measureName\":\"Self-Control of Feelings and Behavior\",\"levels\":[{\"id\":\"9136A78A-71AB-4D27-B1FE-1FF83291F7E3\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"tip\":\"Calms when comforted by an adult\"},{\"id\":\"A7BF0C28-B115-4A48-A087-2E9D19FBE4DB\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Seeks a familiar adult when distressed, and responds when physically comforted by a familiar adult\"},{\"id\":\"1B8B7FBD-A13D-4B79-B474-F35FB479F9DF\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"tip\":\"Calms self when a familiar adult initiates contact, moves close, or offers a special thing\"},{\"id\":\"5AF52F6D-FA16-4EA5-84AB-6B0B6EB9AD03\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Relies on communication or guidance from a familiar adult to regulate emotional or behavioral reactions in moderately stressful situations\"},{\"id\":\"1F997653-3111-4AA3-8444-C7333332F025\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Demonstrates capacity to regulate emotional or behavioral reactions in some moderately stressful situations,occasionally needing adult support\"},{\"id\":\"0F8D73D2-491E-41EE-B942-4E39BEFB0373\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\"},{\"id\":\"4F9F07FB-C23A-4781-92F5-526B3F4892FA\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"}]},{\"domainId\":\"EA63164F-BDCE-E411-AF66-02C72B94B99B\",\"measure\":\"COG1\",\"measureName\":\"Spatial Relationships\",\"levels\":[{\"id\":\"9136A78A-71AB-4D27-B1FE-1FF83291F7E3\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"tip\":\"Moves body parts in basic ways\"},{\"id\":\"A7BF0C28-B115-4A48-A087-2E9D19FBE4DB\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Attends or responds as objects, people, or own body move through space \"},{\"id\":\"1B8B7FBD-A13D-4B79-B474-F35FB479F9DF\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"tip\":\"Explores how self or objects fit in or fill up different spaces\"},{\"id\":\"5AF52F6D-FA16-4EA5-84AB-6B0B6EB9AD03\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Explores spatial relationships (e.g., distance, position, direction), or movement of self or objects through space, trying a variety of possibilities\"},{\"id\":\"1F997653-3111-4AA3-8444-C7333332F025\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Takes into account spatial relationships (e.g., distance, position, direction) and physical properties (e.g., size, shape) when exploring possibilites of fitting objects together or moving through space\"},{\"id\":\"0F8D73D2-491E-41EE-B942-4E39BEFB0373\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\"},{\"id\":\"4F9F07FB-C23A-4781-92F5-526B3F4892FA\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"}]},{\"domainId\":\"EB63164F-BDCE-E411-AF66-02C72B94B99B\",\"measure\":\"ATL–REG3\",\"measureName\":\"Imitation\",\"levels\":[{\"id\":\"9136A78A-71AB-4D27-B1FE-1FF83291F7E3\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"tip\":\"Responds to facial expressions or vocalizations in basic ways\"},{\"id\":\"A7BF0C28-B115-4A48-A087-2E9D19FBE4DB\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Imitates approximations of single simple actions or sounds when interacting with others\"},{\"id\":\"1B8B7FBD-A13D-4B79-B474-F35FB479F9DF\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"tip\":\"Imitates actions, or Repeats familiar words or gestures by others when interacting with them\"},{\"id\":\"5AF52F6D-FA16-4EA5-84AB-6B0B6EB9AD03\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Imitates a few actions, or Repeats familiar actions or words experienced at an earlier time\"},{\"id\":\"1F997653-3111-4AA3-8444-C7333332F025\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Imitates multiple steps of others’ actions, or Repeats phrases,experienced at an earlier time\"},{\"id\":\"0F8D73D2-491E-41EE-B942-4E39BEFB0373\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\"},{\"id\":\"4F9F07FB-C23A-4781-92F5-526B3F4892FA\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"}]},{\"domainId\":\"0064164F-BDCE-E411-AF66-02C72B94B99B\",\"measure\":\"COG8\",\"measureName\":\"Cause and Effect\",\"levels\":[{\"id\":\"9136A78A-71AB-4D27-B1FE-1FF83291F7E3\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"tip\":\"Responds or shows anticipatory excitement to people, objects, or actions\"},{\"id\":\"A7BF0C28-B115-4A48-A087-2E9D19FBE4DB\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Repeats actions that have effects\"},{\"id\":\"1B8B7FBD-A13D-4B79-B474-F35FB479F9DF\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"tip\":\"Tries out different behaviors to cause effects\"},{\"id\":\"5AF52F6D-FA16-4EA5-84AB-6B0B6EB9AD03\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Searches for possible causes of actions, events, or behaviors\"},{\"id\":\"1F997653-3111-4AA3-8444-C7333332F025\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Acts on objects to cause a specifc result\"},{\"id\":\"0F8D73D2-491E-41EE-B942-4E39BEFB0373\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\"},{\"id\":\"4F9F07FB-C23A-4781-92F5-526B3F4892FA\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"}]},{\"domainId\":\"FF63164F-BDCE-E411-AF66-02C72B94B99B\",\"measure\":\"COG2\",\"measureName\":\"Classification\",\"levels\":[{\"id\":\"9136A78A-71AB-4D27-B1FE-1FF83291F7E3\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"tip\":\"Attends to people, objects, or events\"},{\"id\":\"A7BF0C28-B115-4A48-A087-2E9D19FBE4DB\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Interacts differently with familiar people and objects than with unfamiliar people and \\\\nobjects\"},{\"id\":\"1B8B7FBD-A13D-4B79-B474-F35FB479F9DF\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"tip\":\"Associates a person or object with another person or object, based on a similarity or relationship between them\"},{\"id\":\"5AF52F6D-FA16-4EA5-84AB-6B0B6EB9AD03\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Selects some objects that are similar from a collection of objects\"},{\"id\":\"1F997653-3111-4AA3-8444-C7333332F025\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Sorts objects into two groups based on one attribute, but not always accurately\"},{\"id\":\"0F8D73D2-491E-41EE-B942-4E39BEFB0373\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\"},{\"id\":\"4F9F07FB-C23A-4781-92F5-526B3F4892FA\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"}]},{\"domainId\":\"EC63164F-BDCE-E411-AF66-02C72B94B99B\",\"measure\":\"COG3\",\"measureName\":\"Number Sense of Quantity\",\"levels\":[{\"id\":\"9136A78A-71AB-4D27-B1FE-1FF83291F7E3\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"tip\":\"Responds to people or objects in basic ways\"},{\"id\":\"A7BF0C28-B115-4A48-A087-2E9D19FBE4DB\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Responds to changes in the number of objects observed or interacted with\"},{\"id\":\"1B8B7FBD-A13D-4B79-B474-F35FB479F9DF\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"tip\":\"Demonstrates awareness of quantity\"},{\"id\":\"5AF52F6D-FA16-4EA5-84AB-6B0B6EB9AD03\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Uses number names, but not always correctly, in situations related to number or quantity\"},{\"id\":\"1F997653-3111-4AA3-8444-C7333332F025\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Identifies small quantities without counting, up to three\"},{\"id\":\"0F8D73D2-491E-41EE-B942-4E39BEFB0373\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\"},{\"id\":\"4F9F07FB-C23A-4781-92F5-526B3F4892FA\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"}]},{\"domainId\":\"FA63164F-BDCE-E411-AF66-02C72B94B99B\",\"measure\":\"LLD1\",\"measureName\":\"Understanding of Language (Receptive)\",\"levels\":[{\"id\":\"9136A78A-71AB-4D27-B1FE-1FF83291F7E3\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"tip\":\"Responds to voices, sounds, gestures, or facial expressions in basic ways\"},{\"id\":\"A7BF0C28-B115-4A48-A087-2E9D19FBE4DB\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Responds to voices, gestures, or facial expressions in a variety of ways (e.g., gaze aversion, vocalization, movements)\"},{\"id\":\"1B8B7FBD-A13D-4B79-B474-F35FB479F9DF\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"tip\":\"Recognizes a few frequently used words or gestures in familiar situations\"},{\"id\":\"D0ACC52D-6FF5-4E4A-A344-D9108E9BEF49\",\"name\":\"Exploring  Middle\",\"type\":\"radio\",\"sortIndex\":\"3\",\"value\":\"4\",\"tip\":\"Shows understanding of a variety of single words\"},{\"id\":\"5AF52F6D-FA16-4EA5-84AB-6B0B6EB9AD03\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Shows understanding of frequently used simple phrases or sentences\"},{\"id\":\"1F997653-3111-4AA3-8444-C7333332F025\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Shows understanding of a wide variety of phrases or sentences\"},{\"id\":\"0F8D73D2-491E-41EE-B942-4E39BEFB0373\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\"},{\"id\":\"4F9F07FB-C23A-4781-92F5-526B3F4892FA\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"}]},{\"domainId\":\"F863164F-BDCE-E411-AF66-02C72B94B99B\",\"measure\":\"LLD2\",\"measureName\":\"Responsiveness to Language\",\"levels\":[{\"id\":\"9136A78A-71AB-4D27-B1FE-1FF83291F7E3\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"tip\":\"Responds to voices, sounds, gestures, or facial expressions in basic ways\"},{\"id\":\"A7BF0C28-B115-4A48-A087-2E9D19FBE4DB\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Responds to voices, gestures, or facial expressions in a variety of ways (e.g., eye gaze, gaze aversion, vocalization, movements)\"},{\"id\":\"1B8B7FBD-A13D-4B79-B474-F35FB479F9DF\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"tip\":\"Responds to a few frequently used words or gestures in familiar situations\"},{\"id\":\"5AF52F6D-FA16-4EA5-84AB-6B0B6EB9AD03\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Responds to simple comments that relate to a present situation\"},{\"id\":\"1F997653-3111-4AA3-8444-C7333332F025\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Responds to one-step requests or questions that involve a familiar activity or routine\"},{\"id\":\"0F8D73D2-491E-41EE-B942-4E39BEFB0373\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\"},{\"id\":\"4F9F07FB-C23A-4781-92F5-526B3F4892FA\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"}]},{\"domainId\":\"F963164F-BDCE-E411-AF66-02C72B94B99B\",\"measure\":\"LLD3\",\"measureName\":\"Communication and Use of Language (Expressive)\",\"levels\":[{\"id\":\"9136A78A-71AB-4D27-B1FE-1FF83291F7E3\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"tip\":\"Makes sounds spontaneously\"},{\"id\":\"A7BF0C28-B115-4A48-A087-2E9D19FBE4DB\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Uses sounds, gestures, or facial expressions to communicate\"},{\"id\":\"1B8B7FBD-A13D-4B79-B474-F35FB479F9DF\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"tip\":\"Uses a few “first words,” word-like sounds, or gestures to communicate\"},{\"id\":\"D0ACC52D-6FF5-4E4A-A344-D9108E9BEF49\",\"name\":\"Exploring  Middle\",\"type\":\"radio\",\"sortIndex\":\"3\",\"value\":\"4\",\"tip\":\"Uses a variety of single words to communicate\"},{\"id\":\"5AF52F6D-FA16-4EA5-84AB-6B0B6EB9AD03\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Uses two words together to communicate\"},{\"id\":\"1F997653-3111-4AA3-8444-C7333332F025\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Uses short phrases or sentences of more than two words to communicate\"},{\"id\":\"0F8D73D2-491E-41EE-B942-4E39BEFB0373\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\"},{\"id\":\"4F9F07FB-C23A-4781-92F5-526B3F4892FA\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"}]},{\"domainId\":\"FC63164F-BDCE-E411-AF66-02C72B94B99B\",\"measure\":\"LLD4\",\"measureName\":\"Reciprocal Communication and Conversation\",\"levels\":[{\"id\":\"9136A78A-71AB-4D27-B1FE-1FF83291F7E3\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"tip\":\"Responds to sounds or movements of others in basic ways\"},{\"id\":\"A7BF0C28-B115-4A48-A087-2E9D19FBE4DB\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Responds to or seeks contact with familiar adults, using vocalizations, gestures, or facial expressions during interactions\"},{\"id\":\"1B8B7FBD-A13D-4B79-B474-F35FB479F9DF\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"tip\":\"Engages in brief back-and-forth communication with a familiar adult, using word approximations, vocalizations, gestures, or facial expressions\"},{\"id\":\"D0ACC52D-6FF5-4E4A-A344-D9108E9BEF49\",\"name\":\"Exploring  Middle\",\"type\":\"radio\",\"sortIndex\":\"3\",\"value\":\"4\",\"tip\":\"Engages in brief back-and-forth communication with a familiar adult, using simple words or conventional gestures to communicate meaning \"},{\"id\":\"5AF52F6D-FA16-4EA5-84AB-6B0B6EB9AD03\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Engages in brief back-and-forth communication, combining words to communicate meaning\"},{\"id\":\"1F997653-3111-4AA3-8444-C7333332F025\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Engages in brief back-and-forth communication, using short phrases and sentences\"},{\"id\":\"0F8D73D2-491E-41EE-B942-4E39BEFB0373\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\"},{\"id\":\"4F9F07FB-C23A-4781-92F5-526B3F4892FA\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"}]},{\"domainId\":\"FB63164F-BDCE-E411-AF66-02C72B94B99B\",\"measure\":\"LLD5\",\"measureName\":\"Interest in Literacy\",\"levels\":[{\"id\":\"9136A78A-71AB-4D27-B1FE-1FF83291F7E3\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"tip\":\"Attends or responds to people or things in basic ways\"},{\"id\":\"A7BF0C28-B115-4A48-A087-2E9D19FBE4DB\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Plays with books;and Responds to other literacy activities\"},{\"id\":\"1B8B7FBD-A13D-4B79-B474-F35FB479F9DF\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"tip\":\"Attends briefly to a familiar adult reading books, singing songs, or saying rhymes\"},{\"id\":\"5AF52F6D-FA16-4EA5-84AB-6B0B6EB9AD03\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Looks at books on own briefly,or Chooses to join reading, singing, or rhyming activities led by an adult\"},{\"id\":\"1F997653-3111-4AA3-8444-C7333332F025\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Looks at books page by page,or Participates, from beginning to end, in listening to stories,singing songs,or playing rhyming games,when supported by an adult\"},{\"id\":\"0F8D73D2-491E-41EE-B942-4E39BEFB0373\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\"},{\"id\":\"4F9F07FB-C23A-4781-92F5-526B3F4892FA\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"}]},{\"domainId\":\"F363164F-BDCE-E411-AF66-02C72B94B99B\",\"measure\":\"PD-HLTH1\",\"measureName\":\"Perceptual-Motor Skills and Movement Concepts\",\"levels\":[{\"id\":\"9136A78A-71AB-4D27-B1FE-1FF83291F7E3\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"tip\":\"Responds to sensory information or input (e.g., visual, auditory, tactile) with basic movements of body parts\"},{\"id\":\"A7BF0C28-B115-4A48-A087-2E9D19FBE4DB\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Responds to sensory information by moving body or limbs to reach for or move toward \\\\npeople or objects\"},{\"id\":\"1B8B7FBD-A13D-4B79-B474-F35FB479F9DF\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"tip\":\"Uses sensory information to control body while exploring people, objects, or changes in the physical environment\"},{\"id\":\"5AF52F6D-FA16-4EA5-84AB-6B0B6EB9AD03\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Demonstrates awareness of major body parts by exploring their movement potential\"},{\"id\":\"1F997653-3111-4AA3-8444-C7333332F025\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Tries different ways to coordinate movements of large or small body parts\"},{\"id\":\"0F8D73D2-491E-41EE-B942-4E39BEFB0373\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\"},{\"id\":\"4F9F07FB-C23A-4781-92F5-526B3F4892FA\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"}]},{\"domainId\":\"F563164F-BDCE-E411-AF66-02C72B94B99B\",\"measure\":\"PD-HLTH2\",\"measureName\":\"Gross Locomotor Movement Skills\",\"levels\":[{\"id\":\"9136A78A-71AB-4D27-B1FE-1FF83291F7E3\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"tip\":\"Moves in basic and often involuntary ways\"},{\"id\":\"A7BF0C28-B115-4A48-A087-2E9D19FBE4DB\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Moves two or more body parts together, often with intention\"},{\"id\":\"1B8B7FBD-A13D-4B79-B474-F35FB479F9DF\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"tip\":\"Coordinates movements of body parts to move whole body, such as creeping, crawling, or scooting on bottom\"},{\"id\":\"D0ACC52D-6FF5-4E4A-A344-D9108E9BEF49\",\"name\":\"Exploring  Middle\",\"type\":\"radio\",\"sortIndex\":\"3\",\"value\":\"4\",\"tip\":\"Coordinates movement of whole body while upright, using support\"},{\"id\":\"1F997653-3111-4AA3-8444-C7333332F025\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Attempts to coordinate movements, in an upright position, that momentarily move whole body off the ground\"},{\"id\":\"5AF52F6D-FA16-4EA5-84AB-6B0B6EB9AD03\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Coordinates basic movements in an upright position without using support\"},{\"id\":\"0F8D73D2-491E-41EE-B942-4E39BEFB0373\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\"},{\"id\":\"4F9F07FB-C23A-4781-92F5-526B3F4892FA\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"}]},{\"domainId\":\"F263164F-BDCE-E411-AF66-02C72B94B99B\",\"measure\":\"PD-HLTH3\",\"measureName\":\"Gross Motor Manipulative Skills\",\"levels\":[{\"id\":\"9136A78A-71AB-4D27-B1FE-1FF83291F7E3\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"tip\":\"Moves in basic and often involuntary ways\"},{\"id\":\"A7BF0C28-B115-4A48-A087-2E9D19FBE4DB\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Uses arms, legs, or body to move toward or reach for people or objects\"},{\"id\":\"1B8B7FBD-A13D-4B79-B474-F35FB479F9DF\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"tip\":\"Uses arms, legs, or body to engage in simple, repeated actions on objects\"},{\"id\":\"D0ACC52D-6FF5-4E4A-A344-D9108E9BEF49\",\"name\":\"Exploring  Middle\",\"type\":\"radio\",\"sortIndex\":\"3\",\"value\":\"4\",\"tip\":\"Uses arms, legs, or body in various ways to manipulate objects, while in positions such as sitting, moving on all fours, or upright, using support\"},{\"id\":\"5AF52F6D-FA16-4EA5-84AB-6B0B6EB9AD03\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Manipulates objects, using one or more body parts, with limited stability\"},{\"id\":\"1F997653-3111-4AA3-8444-C7333332F025\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Manipulates objects, using one or more body parts, with stability but limited coordination\"},{\"id\":\"0F8D73D2-491E-41EE-B942-4E39BEFB0373\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\"},{\"id\":\"4F9F07FB-C23A-4781-92F5-526B3F4892FA\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"}]},{\"domainId\":\"F063164F-BDCE-E411-AF66-02C72B94B99B\",\"measure\":\"PD-HLTH4\",\"measureName\":\"Fine Motor Manipulative Skills\",\"levels\":[{\"id\":\"9136A78A-71AB-4D27-B1FE-1FF83291F7E3\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"tip\":\"Moves arms or hands in basic ways\"},{\"id\":\"A7BF0C28-B115-4A48-A087-2E9D19FBE4DB\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Uses arms or hands to make contact with objects in the environment\"},{\"id\":\"1B8B7FBD-A13D-4B79-B474-F35FB479F9DF\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"tip\":\"Grasps objects with entire hand\"},{\"id\":\"D0ACC52D-6FF5-4E4A-A344-D9108E9BEF49\",\"name\":\"Exploring  Middle\",\"type\":\"radio\",\"sortIndex\":\"3\",\"value\":\"4\",\"tip\":\"Grasps objects with fingers and thumb\"},{\"id\":\"5AF52F6D-FA16-4EA5-84AB-6B0B6EB9AD03\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Explores ways to use one hand, or to use both hands doing the same movements, to manipulate objects\"},{\"id\":\"1F997653-3111-4AA3-8444-C7333332F025\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Manipulates objects with one hand while stabilizing the objects with other hand or with another part of body \"},{\"id\":\"0F8D73D2-491E-41EE-B942-4E39BEFB0373\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\"},{\"id\":\"4F9F07FB-C23A-4781-92F5-526B3F4892FA\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"}]},{\"domainId\":\"EF63164F-BDCE-E411-AF66-02C72B94B99B\",\"measure\":\"PD-HLTH5\",\"measureName\":\"Safety\",\"levels\":[{\"id\":\"9136A78A-71AB-4D27-B1FE-1FF83291F7E3\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"tip\":\"Reacts to unpleasant stimulation or events in basic ways\"},{\"id\":\"A7BF0C28-B115-4A48-A087-2E9D19FBE4DB\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Responds to situations that make child feel unsafe\"},{\"id\":\"1B8B7FBD-A13D-4B79-B474-F35FB479F9DF\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"tip\":\"Seeks to make contact with familiar adult\"},{\"id\":\"5AF52F6D-FA16-4EA5-84AB-6B0B6EB9AD03\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Follows adults’ guidance about basic safety practices\"},{\"id\":\"1F997653-3111-4AA3-8444-C7333332F025\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Follows basic safety practices, with close adult supervision\"},{\"id\":\"0F8D73D2-491E-41EE-B942-4E39BEFB0373\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\"},{\"id\":\"4F9F07FB-C23A-4781-92F5-526B3F4892FA\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"}]},{\"domainId\":\"EE63164F-BDCE-E411-AF66-02C72B94B99B\",\"measure\":\"PD-HLTH6\",\"measureName\":\"Personal Care Routines: Hygiene\",\"levels\":[{\"id\":\"9136A78A-71AB-4D27-B1FE-1FF83291F7E3\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"tip\":\"Responds in basic ways during personal care routines that involve hygiene\"},{\"id\":\"A7BF0C28-B115-4A48-A087-2E9D19FBE4DB\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Responds in ways that demonstrate awareness of a hygiene routine\"},{\"id\":\"1B8B7FBD-A13D-4B79-B474-F35FB479F9DF\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"tip\":\"Anticipates one or two steps of a hygiene routine\"},{\"id\":\"5AF52F6D-FA16-4EA5-84AB-6B0B6EB9AD03\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Participates in own hygiene routines, with an adult\"},{\"id\":\"1F997653-3111-4AA3-8444-C7333332F025\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Carries out some steps of own hygiene routines, with specific adult guidance or demonstration\"},{\"id\":\"0F8D73D2-491E-41EE-B942-4E39BEFB0373\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\"},{\"id\":\"4F9F07FB-C23A-4781-92F5-526B3F4892FA\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"}]},{\"domainId\":\"F463164F-BDCE-E411-AF66-02C72B94B99B\",\"measure\":\"PD-HLTH7\",\"measureName\":\"Personal Care Routines: Feeding\",\"levels\":[{\"id\":\"9136A78A-71AB-4D27-B1FE-1FF83291F7E3\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"tip\":\"Responds in basic ways during feeding\"},{\"id\":\"A7BF0C28-B115-4A48-A087-2E9D19FBE4DB\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Shows interest in participating in the process of being fed\"},{\"id\":\"1B8B7FBD-A13D-4B79-B474-F35FB479F9DF\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"tip\":\"Feeds self some finger food items\"},{\"id\":\"5AF52F6D-FA16-4EA5-84AB-6B0B6EB9AD03\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Feeds self some foods when using utensil(s) or an open cup, sometimes needing help\"},{\"id\":\"1F997653-3111-4AA3-8444-C7333332F025\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Feeds self on own, using utensils or an open cup\"},{\"id\":\"0F8D73D2-491E-41EE-B942-4E39BEFB0373\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\"},{\"id\":\"4F9F07FB-C23A-4781-92F5-526B3F4892FA\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"}]},{\"domainId\":\"F163164F-BDCE-E411-AF66-02C72B94B99B\",\"measure\":\"PD-HLTH8\",\"measureName\":\"Personal Care Routines: Dressing\",\"levels\":[{\"id\":\"9136A78A-71AB-4D27-B1FE-1FF83291F7E3\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"tip\":\"Responds in basic ways during dressing\"},{\"id\":\"A7BF0C28-B115-4A48-A087-2E9D19FBE4DB\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Responds in ways that demonstrate awareness of a dressing routine\"},{\"id\":\"1B8B7FBD-A13D-4B79-B474-F35FB479F9DF\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"tip\":\"Anticipates one or two steps of a dressing routine\"},{\"id\":\"5AF52F6D-FA16-4EA5-84AB-6B0B6EB9AD03\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Participates with adult in dressing self\"},{\"id\":\"1F997653-3111-4AA3-8444-C7333332F025\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Puts on clothing that is simple to manipulate, sometimes with adult assistance\"},{\"id\":\"0F8D73D2-491E-41EE-B942-4E39BEFB0373\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\"},{\"id\":\"4F9F07FB-C23A-4781-92F5-526B3F4892FA\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"}]},{\"domainId\":\"FD63164F-BDCE-E411-AF66-02C72B94B99B\",\"measure\":\"SED1\",\"measureName\":\"Identity of Self in Relation to Others\",\"levels\":[{\"id\":\"9136A78A-71AB-4D27-B1FE-1FF83291F7E3\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"tip\":\"Responds in basic ways to others\"},{\"id\":\"A7BF0C28-B115-4A48-A087-2E9D19FBE4DB\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Uses senses to explore self and others\"},{\"id\":\"1B8B7FBD-A13D-4B79-B474-F35FB479F9DF\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"tip\":\"Recognizes self and familiar people\"},{\"id\":\"5AF52F6D-FA16-4EA5-84AB-6B0B6EB9AD03\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Communicates own name and names of familiar people (e.g., “dada,” “mama,” “grandma,” or sibling's name)\"},{\"id\":\"1F997653-3111-4AA3-8444-C7333332F025\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Expresses simple ideas about self and connection to others\\\\n\"},{\"id\":\"0F8D73D2-491E-41EE-B942-4E39BEFB0373\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\"},{\"id\":\"4F9F07FB-C23A-4781-92F5-526B3F4892FA\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"}]},{\"domainId\":\"E863164F-BDCE-E411-AF66-02C72B94B99B\",\"measure\":\"SED2\",\"measureName\":\"Social and Emotional Understanding\",\"levels\":[{\"id\":\"9136A78A-71AB-4D27-B1FE-1FF83291F7E3\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"tip\":\"Responds to faces, voices, or actions of other people\"},{\"id\":\"A7BF0C28-B115-4A48-A087-2E9D19FBE4DB\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Shows awareness of what to expect from familiar people by responding to or anticipating their actions\"},{\"id\":\"1B8B7FBD-A13D-4B79-B474-F35FB479F9DF\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"tip\":\"Adjusts behavior in response to emotional expressions of familiar people, especially in novel or uncertain situations\"},{\"id\":\"5AF52F6D-FA16-4EA5-84AB-6B0B6EB9AD03\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Adjusts behavior in response to emotional expressions of people who are less familiar\"},{\"id\":\"1F997653-3111-4AA3-8444-C7333332F025\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Identifies own or others’ feelings\"},{\"id\":\"0F8D73D2-491E-41EE-B942-4E39BEFB0373\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\"},{\"id\":\"4F9F07FB-C23A-4781-92F5-526B3F4892FA\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"}]},{\"domainId\":\"E663164F-BDCE-E411-AF66-02C72B94B99B\",\"measure\":\"SED3\",\"measureName\":\"Relationships and Social Interactions with Familiar Adults\",\"levels\":[{\"id\":\"9136A78A-71AB-4D27-B1FE-1FF83291F7E3\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"tip\":\"Responds to faces, voices, or actions of familiar people\"},{\"id\":\"A7BF0C28-B115-4A48-A087-2E9D19FBE4DB\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Shows a preference for familiar adults and tries to interact with them\"},{\"id\":\"1B8B7FBD-A13D-4B79-B474-F35FB479F9DF\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"tip\":\"Interacts in simple ways with familiar adults and tries to maintain the interactions\"},{\"id\":\"5AF52F6D-FA16-4EA5-84AB-6B0B6EB9AD03\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Initiates activities with familiar adults;and Seeks out assistance or support from familiar \\\\nadults\"},{\"id\":\"1F997653-3111-4AA3-8444-C7333332F025\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Engages in extended interactions with familiar adults in a variety of situations (e.g., sharing ideas or experiences,solving simple problems)\"},{\"id\":\"0F8D73D2-491E-41EE-B942-4E39BEFB0373\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\"},{\"id\":\"4F9F07FB-C23A-4781-92F5-526B3F4892FA\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"}]},{\"domainId\":\"0164164F-BDCE-E411-AF66-02C72B94B99B\",\"measure\":\"SED4\",\"measureName\":\"Relationships and Social Interactions with Peers\",\"levels\":[{\"id\":\"9136A78A-71AB-4D27-B1FE-1FF83291F7E3\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"tip\":\"Shows awareness of other people, including children\"},{\"id\":\"A7BF0C28-B115-4A48-A087-2E9D19FBE4DB\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Shows interest in other children\"},{\"id\":\"1B8B7FBD-A13D-4B79-B474-F35FB479F9DF\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"tip\":\"Plays alongside other children, rarely interacting with them\"},{\"id\":\"5AF52F6D-FA16-4EA5-84AB-6B0B6EB9AD03\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Interacts in simple ways with familiar peers as they play side by side\"},{\"id\":\"1F997653-3111-4AA3-8444-C7333332F025\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Participates in brief episodes of cooperative play with one or two peers, especially those with whom child regularly plays\"},{\"id\":\"0F8D73D2-491E-41EE-B942-4E39BEFB0373\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\"},{\"id\":\"4F9F07FB-C23A-4781-92F5-526B3F4892FA\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"}]},{\"domainId\":\"F663164F-BDCE-E411-AF66-02C72B94B99B\",\"measure\":\"SED5\",\"measureName\":\"Symbolic and Sociodramatic Play\",\"levels\":[{\"id\":\"9136A78A-71AB-4D27-B1FE-1FF83291F7E3\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"tip\":\"Responds to people or objects in basic ways\"},{\"id\":\"A7BF0C28-B115-4A48-A087-2E9D19FBE4DB\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Explores people and objects in a variety of ways\"},{\"id\":\"1B8B7FBD-A13D-4B79-B474-F35FB479F9DF\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"tip\":\"Uses or combines objects in functional or meaningful ways\"},{\"id\":\"1F997653-3111-4AA3-8444-C7333332F025\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Engages in pretendplay sequences\"},{\"id\":\"5AF52F6D-FA16-4EA5-84AB-6B0B6EB9AD03\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Pretends that an object represents another object or serves a different purpose\"},{\"id\":\"0F8D73D2-491E-41EE-B942-4E39BEFB0373\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\"},{\"id\":\"4F9F07FB-C23A-4781-92F5-526B3F4892FA\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"}]},{\"domainId\":\"EB63165F-BDCE-E411-AF66-02C72B94B91B\",\"measure\":\"COG9\",\"measureName\":\"Inquiry Through Observation and Investigation\",\"levels\":[{\"id\":\"9136A78A-71AB-4D27-B1FE-1FF83291F7E3\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"tip\":\"Responds to people, things, or sounds\"},{\"id\":\"A7BF0C28-B115-4A48-A087-2E9D19FBE4DB\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Attends to responses of objects and people that result from own actions\"},{\"id\":\"1B8B7FBD-A13D-4B79-B474-F35FB479F9DF\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"tip\":\"Shows interest in people or things in the environment\"},{\"id\":\"5AF52F6D-FA16-4EA5-84AB-6B0B6EB9AD03\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Engages in simple purposeful explorations of familiar objects in the environment\"},{\"id\":\"1F997653-3111-4AA3-8444-C7333332F025\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Engages in sustained explorations\"},{\"id\":\"0F8D73D2-491E-41EE-B942-4E39BEFB0373\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\"},{\"id\":\"4F9F07FB-C23A-4781-92F5-526B3F4892FA\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"}]},{\"domainId\":\"EB63164F-BDCE-E412-AF66-02C72B94B92B\",\"measure\":\"COG11\",\"measureName\":\"Knowledge of the Natural World\",\"levels\":[{\"id\":\"1B8B7FBD-A13D-4B79-B474-F35FB479F9DF\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"tip\":\"Shows interest in the characteristics of living or nonliving things in the environment\"},{\"id\":\"A7BF0C28-B115-4A48-A087-2E9D19FBE4DB\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Interacts with objects or people \"},{\"id\":\"9136A78A-71AB-4D27-B1FE-1FF83291F7E3\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"tip\":\"Attends to people, objects, or events\"},{\"id\":\"5AF52F6D-FA16-4EA5-84AB-6B0B6EB9AD03\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Explores how objects in the natural world will behave or function\"},{\"id\":\"1F997653-3111-4AA3-8444-C7333332F025\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Identifies basic characteristics of living things, earth materials, or events in the environment (e.g.,how they look,feel ,sound,or behave)\"},{\"id\":\"4F9F07FB-C23A-4781-92F5-526B3F4892FA\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"},{\"id\":\"0F8D73D2-491E-41EE-B942-4E39BEFB0373\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\"}]}]");
        //scoreTemplateEntry.setLevelsJson("[{\"id\":\"9136A78A-71AB-4D27-B1FE-1FF83291F7E3\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\"},{\"id\":\"A7BF0C28-B115-4A48-A087-2E9D19FBE4DB\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\"},{\"id\":\"1B8B7FBD-A13D-4B79-B474-F35FB479F9DF\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\"},{\"id\":\"D0ACC52D-6FF5-4E4A-A344-D9108E9BEF49\",\"name\":\"Exploring  Middle\",\"type\":\"radio\",\"sortIndex\":\"3\",\"value\":\"4\"},{\"id\":\"5AF52F6D-FA16-4EA5-84AB-6B0B6EB9AD03\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\"},{\"id\":\"1F997653-3111-4AA3-8444-C7333332F025\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\"},{\"id\":\"3F4B3D6C-503F-4C67-BF95-305780D611A8\",\"name\":\"Building Middle\",\"type\":\"text\",\"sortIndex\":\"6\",\"value\":\"7\"},{\"id\":\"A88AA042-F142-4BC8-B4D9-3C290E9B8542\",\"name\":\"Building Later\",\"type\":\"text\",\"sortIndex\":\"7\",\"value\":\"8\"},{\"id\":\"3A1234A3-EC10-4122-95CD-F5A5D6DBF91C\",\"name\":\"Integrating  Earlier\",\"type\":\"text\",\"sortIndex\":\"8\",\"value\":\"9\"},{\"id\":\"0F8D73D2-491E-41EE-B942-4E39BEFB0373\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\"},{\"id\":\"4F9F07FB-C23A-4781-92F5-526B3F4892FA\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"}]");
        scoreTemplateEntry.setLevelsJson("[{\"id\":\"9136A78A-71AB-4D27-B1FE-1FF83291F7E3\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\"}]");
        scoreTemplateEntry.setDomainLevelsJson("[{\"domainId\":\"E363164F-BDCE-E411-AF66-02C72B94B99B\",\"measure\":\"ATL–REG1\",\"measureName\":\"Attention Maintenance\",\"levels\":[{\"id\":\"A7BF0C28-B115-4A48-A087-2E9D19FBE4DB\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Shifts attention frequently from one person or thing to another\"},{\"id\":\"1B8B7FBD-A13D-4B79-B474-F35FB479F9DF\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"tip\":\"Maintains attention, on own or with adult support, during brief activities\"},{\"id\":\"5AF52F6D-FA16-4EA5-84AB-6B0B6EB9AD03\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Maintains attention, with adult support, during activities that last for extended periods of time\"},{\"id\":\"1F997653-3111-4AA3-8444-C7333332F025\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Maintains attention on own during activities that last for extended periods of time\"},{\"id\":\"0F8D73D2-491E-41EE-B942-4E39BEFB0373\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\"},{\"id\":\"4F9F07FB-C23A-4781-92F5-526B3F4892FA\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"},{\"id\":\"9136A78A-71AB-4D27-B1FE-1FF83291F7E3\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\"}]},{\"domainId\":\"E463164F-BDCE-E411-AF66-02C72B94B99B\",\"measure\":\"ATL–REG2\",\"measureName\":\"Self-Comforting\",\"levels\":[{\"id\":\"9136A78A-71AB-4D27-B1FE-1FF83291F7E3\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"tip\":\"Responds to internal or external stimulation in basic ways\"},{\"id\":\"A7BF0C28-B115-4A48-A087-2E9D19FBE4DB\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Engages in behaviors that have previously worked to soothe self\"},{\"id\":\"1B8B7FBD-A13D-4B79-B474-F35FB479F9DF\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"tip\":\"Comforts self by seeking a familiar adult or a special thing\"},{\"id\":\"5AF52F6D-FA16-4EA5-84AB-6B0B6EB9AD03\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Comforts self in different ways, based on the situation\"},{\"id\":\"1F997653-3111-4AA3-8444-C7333332F025\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Anticipates need for comfort and prepares self by asking questions, getting a special thing, or in other ways\"},{\"id\":\"0F8D73D2-491E-41EE-B942-4E39BEFB0373\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\"},{\"id\":\"4F9F07FB-C23A-4781-92F5-526B3F4892FA\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"}]}]");
        Mockito.when(portfolioService.getScoreTemplate(anyString())).thenReturn(scoreTemplateEntry);
        LevelEntity level = new LevelEntity();
        level.setName("Conditional Measure");
        level.setValue("1");
        level.setId("level1");
        Mockito.when(ratingService.getBestScore(Mockito.anyList(), Mockito.anyList())).thenReturn(level);

        List<StudentScoreEntity> studentScoreEntityList=new ArrayList<>();
        StudentScoreEntity studentScoreEntity=new StudentScoreEntity();
        studentScoreEntity.setDomainId("E363164F-BDCE-E411-AF66-02C72B94B99B");
        studentScoreEntity.setNoteId("note1");
        studentScoreEntity.setLevelId("level1");
        studentScoreEntityList.add(studentScoreEntity);

        Mockito.when(scoreDao.get(anyString())).thenReturn(studentScoreEntityList);
        //获取学生的note
        List<NoteEntity> noteEntityList=new ArrayList<>();
        NoteEntity noteEntity=new NoteEntity();
        noteEntity.setId("note1");
        noteEntityList.add(noteEntity);
        Mockito.when(noteDao.getNotes(anyString(), anyString(), anyString(), Mockito.any(SimpleDateFormat.class))).thenReturn(noteEntityList);
        Mockito.when(studentDao.getAgencyByAttrId(anyString())).thenReturn(new ArrayList<AgencyModel>());
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setName("aa");
        Mockito.when(userProvider.getAgencyByUserId(anyString())).thenReturn(agencyModel);
        CheckLockResult checkMeasuresResult = new CheckLockResult();
        checkMeasuresResult.setSuccess(false);
        Mockito.when(analysisService.checkMeasures(anyString(), anyString(), anyString(), anyString(), anyString())).thenReturn(checkMeasuresResult);
        //测试区域不为null
//        OutputStream out = scoreStatisticsService.getDRDPExcel("", "11/11/1111", "12/12/1212");
//        Assert.assertTrue(out != null);
    }

    @Ignore
    @Test
    public void testWriteExcelByGroup1() throws Exception {
        List<ChildEntity> children = new ArrayList<>();
        ChildEntity childEntity = new ChildEntity();
        com.learninggenie.common.data.model.GroupEntity ge = new com.learninggenie.common.data.model.GroupEntity();
        ge.setName("456");
        childEntity.setGroup(ge);
        childEntity.setFirstName("h");
        childEntity.setLastName("j");
        children.add(childEntity);
        //根据班级id获取班级学生实体
        Mockito.when(groupDao.getChilds(anyString())).thenReturn(children);
        GroupEntry group = new GroupEntry();
        group.setDomainId("123");
        //根据班级获取班级实体
        Mockito.when(groupDao.getGroup(anyString())).thenReturn(group);
        com.learninggenie.common.data.model.DomainEntity domainEntity = new com.learninggenie.common.data.model.DomainEntity();
        domainEntity.setId("123");
        domainEntity.setName("DRDP-2015");
        //根据班级的domainId
        Mockito.when(domainDao.getDomain(anyString())).thenReturn(domainEntity);

        ReflectionTestUtils.setField(scoreStatisticsService, "idIT2015", "123");
        //设置评分模版
        ScoreTemplateEntity scoreTemplateEntry = new ScoreTemplateEntity();
        scoreTemplateEntry.setLevelsJson("[{\"id\":\"9136A78A-71AB-4D27-B1FE-1FF83291F7E3\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\"}]");
        scoreTemplateEntry.setDomainLevelsJson("[{\"domainId\":\"E363164F-BDCE-E411-AF66-02C72B94B99B\",\"measure\":\"ATL–REG1\",\"measureName\":\"Attention Maintenance\",\"levels\":[{\"id\":\"A7BF0C28-B115-4A48-A087-2E9D19FBE4DB\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Shifts attention frequently from one person or thing to another\"},{\"id\":\"1B8B7FBD-A13D-4B79-B474-F35FB479F9DF\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"tip\":\"Maintains attention, on own or with adult support, during brief activities\"},{\"id\":\"5AF52F6D-FA16-4EA5-84AB-6B0B6EB9AD03\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Maintains attention, with adult support, during activities that last for extended periods of time\"},{\"id\":\"1F997653-3111-4AA3-8444-C7333332F025\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Maintains attention on own during activities that last for extended periods of time\"},{\"id\":\"0F8D73D2-491E-41EE-B942-4E39BEFB0373\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\"},{\"id\":\"4F9F07FB-C23A-4781-92F5-526B3F4892FA\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"},{\"id\":\"9136A78A-71AB-4D27-B1FE-1FF83291F7E3\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\"}]},{\"domainId\":\"E463164F-BDCE-E411-AF66-02C72B94B99B\",\"measure\":\"ATL–REG2\",\"measureName\":\"Self-Comforting\",\"levels\":[{\"id\":\"9136A78A-71AB-4D27-B1FE-1FF83291F7E3\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"tip\":\"Responds to internal or external stimulation in basic ways\"},{\"id\":\"A7BF0C28-B115-4A48-A087-2E9D19FBE4DB\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Engages in behaviors that have previously worked to soothe self\"},{\"id\":\"1B8B7FBD-A13D-4B79-B474-F35FB479F9DF\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"tip\":\"Comforts self by seeking a familiar adult or a special thing\"},{\"id\":\"5AF52F6D-FA16-4EA5-84AB-6B0B6EB9AD03\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Comforts self in different ways, based on the situation\"},{\"id\":\"1F997653-3111-4AA3-8444-C7333332F025\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Anticipates need for comfort and prepares self by asking questions, getting a special thing, or in other ways\"},{\"id\":\"0F8D73D2-491E-41EE-B942-4E39BEFB0373\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\"},{\"id\":\"4F9F07FB-C23A-4781-92F5-526B3F4892FA\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"}]}]");
        scoreTemplateEntry.setPortfolioId("111");

        Mockito.when(portfolioService.getScoreTemplate(anyString())).thenReturn(scoreTemplateEntry);
        List<com.learninggenie.common.data.model.DomainEntity> domainEntityList = new ArrayList<>();
        com.learninggenie.common.data.model.DomainEntity domainEntity1 = new com.learninggenie.common.data.model.DomainEntity();
        domainEntity1.setId("E363164F-BDCE-E411-AF66-02C72B94B99B");
        domainEntity1.setSortIndex(1);
        com.learninggenie.common.data.model.DomainEntity domainEntity2 = new com.learninggenie.common.data.model.DomainEntity();
        domainEntity2.setId("E463164F-BDCE-E411-AF66-02C72B94B99B");
        domainEntity2.setSortIndex(2);
        domainEntityList.add(domainEntity1);
        domainEntityList.add(domainEntity2);
        Mockito.when(domainDao.getAllChildDomains(anyString())).thenReturn(domainEntityList);
        LevelEntity level = new LevelEntity();
        level.setName("Conditional Measure");
        level.setValue("1");
        level.setId("level1");
        Mockito.when(ratingService.getBestScore(Mockito.anyList(), Mockito.anyList())).thenReturn(level);
        //获取学生的note
        List<NoteEntity> noteEntityList = new ArrayList<>();
        NoteEntity noteEntity = new NoteEntity();
        noteEntity.setId("note1");
        noteEntityList.add(noteEntity);

        List<StudentScoreEntity> studentScoreEntityList = new ArrayList<>();
        StudentScoreEntity studentScoreEntity = new StudentScoreEntity();
        studentScoreEntity.setDomainId("E363164F-BDCE-E411-AF66-02C72B94B99B");
        studentScoreEntity.setNoteId("note1");
        studentScoreEntity.setLevelId("level1");
        studentScoreEntityList.add(studentScoreEntity);

        Mockito.when(scoreDao.get(anyString())).thenReturn(studentScoreEntityList);
        Mockito.when(noteDao.getNotes(anyString(), anyString(), anyString(), Mockito.any(SimpleDateFormat.class))).thenReturn(noteEntityList);


        InputStream in = new FileInputStream("src/test/resources/template.xls");
        Map<String, Object> map = scoreStatisticsService.writeExcelByGroup("", "07/13/2015", "01/13/2016", "", in, true, true);
        Assert.assertTrue(map != null);
    }

    //分值为1,EM Flag为真
    @Test
    public void testGetOutputScoreWithEMFlagWithComments1() {
        Map<String, String> outputMap = new HashMap<>();
        Map<String, NoteEntity> notesMap = new HashMap<>();
        List<StudentScoreEntity> scores = new ArrayList<>();
        LevelEntity studentBestScore = new LevelEntity();
        studentBestScore.setValue("1");
        new ScoreStatisticsServiceImpl().getOutputScoreWithEMFlagWithComments(outputMap, notesMap, scores, studentBestScore, "ATL1", 4, true, "", ExcelType.DRDP.toString(), new SpecialLevelNameModel());
        assertEquals("1", outputMap.get("ATL1"));
        assertEquals("1", outputMap.get("ATL1e"));
    }

    //分值为4,EM Flag为真
    @Test
    public void testGetOutputScoreWithEMFlagWithComments2() {
        Map<String, String> outputMap = new HashMap<>();
        Map<String, NoteEntity> notesMap = new HashMap<>();
        List<StudentScoreEntity> scores = new ArrayList<>();
        LevelEntity studentBestScore = new LevelEntity();
        studentBestScore.setValue("4");
        new ScoreStatisticsServiceImpl().getOutputScoreWithEMFlagWithComments(outputMap, notesMap, scores, studentBestScore, "ATL1", 4, true, "", ExcelType.DRDP.toString(), new SpecialLevelNameModel());
        assertEquals("4", outputMap.get("ATL1"));
        assertEquals("", outputMap.get("ATL1e"));
    }

    //分值为u,EM Flag为真
    @Test
    public void testGetOutputScoreWithEMFlagWithComments3() {
        Map<String, String> outputMap = new HashMap<>();
        Map<String, NoteEntity> notesMap = new HashMap<>();
        List<StudentScoreEntity> scores = new ArrayList<>();
        LevelEntity studentBestScore = new LevelEntity();
        studentBestScore.setValue("u");
        new ScoreStatisticsServiceImpl().getOutputScoreWithEMFlagWithComments(outputMap, notesMap, scores, studentBestScore, "ATL1", 4, true, "", ExcelType.DRDP.toString(), new SpecialLevelNameModel());
        assertEquals("u", outputMap.get("ATL1"));
        assertEquals("", outputMap.get("ATL1e"));
    }

    //分值为b,EM Flag为真, Measure不为ELD或者SPAN
    @Ignore
    @Test
    public void testGetOutputScoreWithEMFlagWithComments4_1() {
        Map<String, String> outputMap = new HashMap<>();
        Map<String, NoteEntity> notesMap = new HashMap<>();
        List<StudentScoreEntity> scores = new ArrayList<>();
        LevelEntity studentBestScore = new LevelEntity();
        studentBestScore.setValue("b");
        new ScoreStatisticsServiceImpl().getOutputScoreWithEMFlagWithComments(outputMap, notesMap, scores, studentBestScore, "ATL1", 4, true, "", ExcelType.DRDP.toString(), new SpecialLevelNameModel());
        assertEquals("c", outputMap.get("ATL1"));
        assertEquals("", outputMap.get("ATL1e"));
    }

    //分值为b,EM Flag为真, Measure为ELD
    @Ignore
    @Test
    public void testGetOutputScoreWithEMFlagWithComments4_2() {
        Map<String, String> outputMap = new HashMap<>();
        Map<String, NoteEntity> notesMap = new HashMap<>();
        List<StudentScoreEntity> scores = new ArrayList<>();
        LevelEntity studentBestScore = new LevelEntity();
        studentBestScore.setValue("b");
        new ScoreStatisticsServiceImpl().getOutputScoreWithEMFlagWithComments(outputMap, notesMap, scores, studentBestScore, "ELD1", 4, true, "", ExcelType.DRDP.toString(), new SpecialLevelNameModel());
        assertEquals("c", outputMap.get("ELD1"));
        assertEquals("", outputMap.get("ELD1e"));
    }

    //分值为b,EM Flag为真, Measure为SPAN
    @Ignore
    @Test
    public void testGetOutputScoreWithEMFlagWithComments4_3() {
        Map<String, String> outputMap = new HashMap<>();
        Map<String, NoteEntity> notesMap = new HashMap<>();
        List<StudentScoreEntity> scores = new ArrayList<>();
        LevelEntity studentBestScore = new LevelEntity();
        studentBestScore.setValue("b");
        new ScoreStatisticsServiceImpl().getOutputScoreWithEMFlagWithComments(outputMap, notesMap, scores, studentBestScore, "SPAN3", 4, true, "", ExcelType.DRDP.toString(), new SpecialLevelNameModel());
        assertEquals("c", outputMap.get("SPAN3"));
        assertEquals("", outputMap.get("SPAN3e"));
    }

    //分值为b,Measure为PD-HLTH7
    //Update:PS的PD-HLTH5,6,7,8,10,conditional全输出u
    @Ignore
    @Test
    public void testGetOutputScoreWithEMFlagWithComments4_4() {
        Map<String, String> outputMap = new HashMap<>();
        Map<String, NoteEntity> notesMap = new HashMap<>();
        List<StudentScoreEntity> scores = new ArrayList<>();
        LevelEntity studentBestScore = new LevelEntity();
        studentBestScore.setValue("b");
        new ScoreStatisticsServiceImpl().getOutputScoreWithEMFlagWithComments(outputMap, notesMap, scores, studentBestScore, "PD-HLTH7", 4, true, "A5845474-BDCE-E411-AF66-02C72B94B99B", ExcelType.DRDP.toString(), new SpecialLevelNameModel());
        assertEquals("u", outputMap.get("PD-HLTH7"));
        assertEquals("", outputMap.get("PD-HLTH7e"));
    }

    //分值为b,Measure为PD-HLTH8
    //Update:PS的PD-HLTH5,6,7,8,10,conditional全输出u
    @Ignore
    @Test
    public void testGetOutputScoreWithEMFlagWithComments4_5() {
        Map<String, String> outputMap = new HashMap<>();
        Map<String, NoteEntity> notesMap = new HashMap<>();
        List<StudentScoreEntity> scores = new ArrayList<>();
        LevelEntity studentBestScore = new LevelEntity();
        studentBestScore.setValue("b");
        new ScoreStatisticsServiceImpl().getOutputScoreWithEMFlagWithComments(outputMap, notesMap, scores, studentBestScore, "PD-HLTH8", 4, true, "A5845474-BDCE-E411-AF66-02C72B94B99B", ExcelType.DRDP.toString(), new SpecialLevelNameModel());
        assertEquals("u", outputMap.get("PD-HLTH8"));
        assertEquals("", outputMap.get("PD-HLTH8e"));
    }

    //分值为6,Measure为PD-HLTH7
    @Test
    public void testGetOutputScoreWithEMFlagWithComments4_6() {
        Map<String, String> outputMap = new HashMap<>();
        Map<String, NoteEntity> notesMap = new HashMap<>();
        List<StudentScoreEntity> scores = new ArrayList<>();
        LevelEntity studentBestScore = new LevelEntity();
        studentBestScore.setValue("6");
        new ScoreStatisticsServiceImpl().getOutputScoreWithEMFlagWithComments(outputMap, notesMap, scores, studentBestScore, "PD-HLTH7", 6, true, "", ExcelType.DRDP.toString(), new SpecialLevelNameModel());
        assertEquals("6", outputMap.get("PD-HLTH7"));
        assertEquals("", outputMap.get("PD-HLTH7e"));
    }

    //分值为空,EM Flag为真
    @Test
    public void testGetOutputScoreWithEMFlagWithComments5() {
        Map<String, String> outputMap = new HashMap<>();
        Map<String, NoteEntity> notesMap = new HashMap<>();
        List<StudentScoreEntity> scores = new ArrayList<>();
        LevelEntity studentBestScore = new LevelEntity();
        studentBestScore.setValue("e");
        new ScoreStatisticsServiceImpl().getOutputScoreWithEMFlagWithComments(outputMap, notesMap, scores, studentBestScore, "ATL1", 4, true, "", ExcelType.DRDP.toString(), new SpecialLevelNameModel());
        assertEquals("", outputMap.get("ATL1"));
        assertEquals("", outputMap.get("ATL1e"));
    }

    //分值为4,EM Flag为真, 多条note挑选出分值最高的那条作为c
    @Test
    public void testGetOutputScoreWithEMFlagWithComments6() {
        Map<String, String> outputMap = new HashMap<>();
        Map<String, NoteEntity> notesMap = new HashMap<>();
        NoteEntity note1 = new NoteEntity();
        note1.setId("n001");
        note1.setNoteMessage("m001");
        NoteEntity note2 = new NoteEntity();
        note2.setId("n002");
        note2.setNoteMessage("m002");
        notesMap.put("n001", note1);
        notesMap.put("n002", note2);
        List<StudentScoreEntity> scores = new ArrayList<>();
        StudentScoreEntity score1 = new StudentScoreEntity();
        score1.setLevelId("l001");
        score1.setNoteId("n001");
        StudentScoreEntity score2 = new StudentScoreEntity();
        score2.setLevelId("l002");
        score2.setNoteId("n002");
        scores.add(score1);
        scores.add(score2);
        LevelEntity studentBestScore = new LevelEntity();
        studentBestScore.setId("l001");
        studentBestScore.setValue("3");
        new ScoreStatisticsServiceImpl().getOutputScoreWithEMFlagWithComments(outputMap, notesMap, scores, studentBestScore, "ATL1", 4, true, "", ExcelType.DRDP.toString(), new SpecialLevelNameModel());
        assertEquals("3", outputMap.get("ATL1"));
        assertEquals("1", outputMap.get("ATL1e"));
   }
    @Test
    public void testExcelColumnMatch() throws IOException {
        InputStream in = new FileInputStream("src/test/resources/template.xlsx");
        List titles = Arrays.asList(new String[]{"allowupload","agency","site","teacher","class","icode","ssid","firstname","lastname","extradomains","enrollment","drdpcompletion","withdrawal","dob","gender","hispanic","africanamerican","otherasian","caucasian","nativeamerican","pacificislander","asianindian","cambodian","chinese","filipino","guamanian","hawaiian","hmong","japanese","korean","laotian","samoan","tahitian","vietnamese","ethnicityleftblank","primary","primarydesribe","generalassistance","assistancedescribe","otherthanenglish","homelanguageenglish","homelanguagespanish","homelanguageother","speaklanguageenglish","speaklanguagespanish","speaklanguageother","assistquestion","assistance","enrol_stateprogram","enrol_migrant","enrol_headstart","enrol_firstfive","enrol_earlyheadstart","enrol_title1","enrol_generalchildcare","enrol_familychildcare","enrol_tribalheadstart","enrol_other","enrol_subsidized","ieporifsp","notes","locked"
        });
        scoreStatisticsService.detectTitleChangeAndSendEmail(titles, new XSSFWorkbook(in));

    }
    @Test
    public void testExcelColumnNotMatch() throws IOException {
        InputStream in = new FileInputStream("src/test/resources/template.xlsx");
        List titles = Arrays.asList(new String[]{"allowupload","agency","site","teacher","class","icode","ssid","firstname","lastname","extradomains","enrollment","drdpcompletion","withdrawal","dob","gender","hispanic","africanamerican","otherasian","white","nativeamerican","pacificislander","asianindian","cambodian","chinese","filipino","guamanian","hawaiian","hmong","japanese","korean","laotian","samoan","tahitian","vietnamese","ethnicityleftblank","primary","primarydescribe","generalassistance","assistancedescribe","otherthanenglish","homelanguageenglish","homelanguagespanish","homelanguageother","speaklanguageenglish","speaklanguagespanish","speaklanguageother","assistquestion","assistance","enrol_stateprogram","enrol_migrant","enrol_headstart","enrol_firstfive","enrol_earlyheadstart","enrol_title1","enrol_generalchildcare","enrol_familychildcare","enrol_tribalheadstart","enrol_other","enrol_subsidized","ieporifsp","notes","locked"
        });
        scoreStatisticsService.detectTitleChangeAndSendEmail(titles, new XSSFWorkbook(in));
        Mockito.verify(emailService,new Times(1)).sendAsync(isA(EmailModel.class));
    }
    @Test(expected = BusinessException.class)
    public void testExcelEmpty() throws IOException {
         scoreStatisticsService.detectTitleChangeAndSendEmail(new ArrayList<String>(), new XSSFWorkbook());
    }
}
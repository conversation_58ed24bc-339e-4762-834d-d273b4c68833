package com.learninggenie.api.service.impl;

import com.learninggenie.api.model.ReleaseNotificationResponse;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.common.data.dao.ReleaseNotificationDao;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.entity.ReleaseNotificationEntity;
import com.learninggenie.common.data.entity.UserEntity;
import com.learninggenie.common.data.entity.UserMetaDataEntity;
import com.learninggenie.common.data.enums.UserRole;
import com.learninggenie.common.filesystem.FileSystem;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ReleaseNotificationServiceImplTest {
    @InjectMocks
    private ReleaseNotificationServiceImpl releaseNotificationService;
    @Mock
    private ReleaseNotificationDao releaseNotificationDao;
    @Mock
    private UserProvider userProvider;
    @Mock
    private FileSystem fileSystem;
    @Mock
    private UserDaoImpl userDao;

//    @Test
//    @Ignore
//    public void testGetReleaseNotifications() {
//        Integer pageNum = 1;
//        Integer pageSize = 2;
//        ReleaseNotificationResponse response = new ReleaseNotificationResponse();
//        List<ReleaseNotificationEntity> releaseNotifications = new ArrayList<>();
//        ReleaseNotificationEntity releaseNotificationEntity1 = new ReleaseNotificationEntity();
//        releaseNotificationEntity1.setId("1");
//        releaseNotificationEntity1.setTitle("v1");
//        releaseNotificationEntity1.setSummary("summary1");
//
//        releaseNotifications.add(releaseNotificationEntity1);
//
//        when(userProvider.getCurrentUserId()).thenReturn("");
//        when(releaseNotificationDao.getReleaseNotifications(eq(0), eq(pageSize), eq(null), eq(null), anyString(), anyString())).thenReturn(releaseNotifications);
//
//        ReleaseNotificationResponse releaseNotificationResponse = releaseNotificationService.getReleaseNotifications(pageNum, pageSize, null, null);
//
//        Assert.assertEquals(releaseNotificationResponse.getReleaseNotifications().size(), 1);
//        verify(userProvider, times(2)).getCurrentUserId();
//        verify(userProvider, times(0)).checkUser(anyString());
//    }


//    @Test
//    @Ignore
//    public void testGetReleaseNotifications_WithUserId() {
//        Integer pageSize = 10;
//        List<ReleaseNotificationEntity> releaseNotifications = new ArrayList<>();
//        ReleaseNotificationEntity releaseNotificationEntity1 = new ReleaseNotificationEntity();
//        releaseNotificationEntity1.setId("1");
//        releaseNotificationEntity1.setTitle("v1");
//        releaseNotificationEntity1.setSummary("summary1");
//        releaseNotifications.add(releaseNotificationEntity1);
//
//        UserEntity user = new UserEntity();
//        user.setRole(UserRole.PARENT.toString());
//
//        when(userProvider.getCurrentUserId()).thenReturn("1111");
//        when(releaseNotificationDao.getReleaseNotifications(eq(0), eq(pageSize), eq(null), eq(null), anyString(), anyString())).thenReturn(releaseNotifications);
//        when(userProvider.checkUser(anyString())).thenReturn(user);
//
//        ReleaseNotificationResponse releaseNotificationResponse = releaseNotificationService.getReleaseNotifications(null, null, null, null);
//
//        Assert.assertEquals(releaseNotificationResponse.getReleaseNotifications().size(), 1);
//        verify(userProvider, times(2)).getCurrentUserId();
//        verify(userProvider, times(2)).checkUser(anyString());
//    }

    @Test
    public void testGetReleaseNotification() {
        List<ReleaseNotificationEntity> releaseNotifications = new ArrayList<>();
        ReleaseNotificationEntity releaseNotificationEntity1 = new ReleaseNotificationEntity();
        releaseNotificationEntity1.setId("1");
        releaseNotificationEntity1.setTitle("v1");
        releaseNotificationEntity1.setSummary("summary1");
        releaseNotificationEntity1.setImgUrl("u1");
        releaseNotificationEntity1.setTag(0);
        releaseNotifications.add(releaseNotificationEntity1);

        ReleaseNotificationEntity releaseNotificationEntity3 = new ReleaseNotificationEntity();
        releaseNotificationEntity3.setId("3");
        releaseNotificationEntity3.setTitle("v3");
        releaseNotificationEntity3.setSummary("summary3");
        releaseNotificationEntity3.setImgUrl("u=3");
        releaseNotificationEntity3.setTag(1);
        releaseNotifications.add(releaseNotificationEntity3);

        ReleaseNotificationEntity currentReleaseNotification = new ReleaseNotificationEntity();
        currentReleaseNotification.setId("2");
        currentReleaseNotification.setImgUrl("url");
        UserEntity user = new UserEntity();
        user.setRole(UserRole.COLLABORATOR.toString());

        when(userProvider.checkUser(anyString())).thenReturn(user);
        when(userProvider.getCurrentUserId()).thenReturn("1111");
        when(releaseNotificationDao.getReleaseNotificationById("2")).thenReturn(currentReleaseNotification);
        when(releaseNotificationDao.getPreAndNextReleaseNotifications(eq("2") ,anyString())).thenReturn(releaseNotifications);
        when(fileSystem.getPublicUrl(anyString())).thenReturn("url");

        ReleaseNotificationResponse releaseNotificationResponse = releaseNotificationService.getReleaseNotification("2");

        Assert.assertEquals(releaseNotificationResponse.getReleaseNotification().getId(), "2");
        Assert.assertEquals(releaseNotificationResponse.getPreReleaseNotification().getId(), "1");
        Assert.assertEquals(releaseNotificationResponse.getNextReleaseNotification().getId(), "3");
        verify(releaseNotificationDao, times(1)).getPreAndNextReleaseNotifications(eq("2") ,anyString());
        verify(releaseNotificationDao, times(1)).getReleaseNotificationById("2");
        verify(fileSystem, times(3)).getPublicUrl(anyString());
    }

    @Test
    public void testGetReleaseNotificationReadingState() {
        ReleaseNotificationEntity currentReleaseNotification = new ReleaseNotificationEntity();
        currentReleaseNotification.setId("2");
        currentReleaseNotification.setImgUrl("url");
        UserEntity user = new UserEntity();
        user.setId("1");
        user.setRole(UserRole.COLLABORATOR.toString());

        UserMetaDataEntity meta = new UserMetaDataEntity();
        meta.setId("1");
        meta.setUser(user);
        meta.setMetaValue("123");
        Date date = null;

        when(userProvider.checkUser("1")).thenReturn(user);
        when(userDao.getMetaData("1", "RELEASE_TIMESTAMP")).thenReturn(meta);
        when(releaseNotificationDao.getReleaseNotificationsTopOneTime(anyString())).thenReturn(date);

        boolean flag = releaseNotificationService.getReleaseNotificationReadingState("1");
        Assert.assertTrue(flag);
        verify(userProvider, times(1)).checkUser("1");
        verify(userDao, times(1)).getMetaData("1", "RELEASE_TIMESTAMP");
        verify(releaseNotificationDao, times(1)).getReleaseNotificationsTopOneTime(anyString());
    }

}

package com.learninggenie.api.service.impl;

import com.google.gson.reflect.TypeToken;
import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.model.GetRequireCompleteRatingResponse;
import com.learninggenie.api.model.GetStateFrameworkInfoByStateResponse;
import com.learninggenie.api.model.SingleFrameworkNameResponse;
import com.learninggenie.api.model.domain.GetFrameworkMeasuresResponse;
import com.learninggenie.api.model.response.FrameworkInfoByStateResponse;
import com.learninggenie.api.provider.PortfolioProvider;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.common.cache.CacheService;
import com.learninggenie.common.constant.StateConstants;
import com.learninggenie.common.constant.cachekey.FrameworkKey;
import com.learninggenie.common.data.dao.AgencyDao;
import com.learninggenie.common.data.dao.DomainDao;
import com.learninggenie.common.data.dao.StudentDao;
import com.learninggenie.common.data.dao.frameworks.FrameworkDao;
import com.learninggenie.common.data.dao.lesson2.CurriculumUnitDao;
import com.learninggenie.common.data.entity.AgencyEntity;
import com.learninggenie.common.data.entity.AgencyMetaDataEntity;
import com.learninggenie.common.data.entity.frameworks.FrameworkEntity;
import com.learninggenie.common.data.entity.frameworks.MeasureEntity;
import com.learninggenie.common.data.entity.lesson2.curriculum.CurriculumUnitEntity;
import com.learninggenie.common.data.enums.AgencyMetaKey;
import com.learninggenie.common.data.enums.DrdpAttr;
import com.learninggenie.common.data.enums.group.NewAgeGroupEnum;
import com.learninggenie.common.data.mapper.mybatisplus.frameworks.FrameworkMapper;
import com.learninggenie.common.data.model.AgencyModel;
import com.learninggenie.common.data.model.DomainEntity;
import com.learninggenie.common.data.model.framework.GroupFrameworkStatsModel;
import com.learninggenie.common.region.RegionService;
import com.learninggenie.common.score.RatingService;
import com.learninggenie.common.utils.StringUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.util.*;
import java.util.stream.Collectors;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;


/**
 * 观察测评测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class PortfolioServiceImplTest {

    @InjectMocks
    private PortfolioServiceImpl portfolioService;

    @Mock
    private FrameworkMapper frameworkMapper;

    @Mock
    private CurriculumUnitDao curriculumUnitDao;

    @Mock
    private AgencyDao agencyDao;

    @Mock
    private UserProvider userProvider;

    @Mock
    private DomainDao domainDao;

    @Mock
    private RatingService ratingService;

    @Mock
    private PortfolioProvider portfolioProvider;

    @Mock
    private RegionService regionService;

    @Mock
    private StudentDao studentDao;

    @Mock
    private FrameworkDao frameworkDao;

    @Mock
    private CacheService redisCacheServiceImpl;

    @Mock
    private com.learninggenie.common.data.dao.GroupDao groupDao;

    /**
     * Case: 参数中传入了机构 Id，且该机构开启了所有测评点评分才能锁定开关
     * 结果：返回 true
     */
    @Test
    public void testGetAllMeasureStatusByAgencyId() {
        // 机构 Id
        String agencyId = UUID.randomUUID().toString();
        // 机构开关数据
        AgencyMetaDataEntity meta = new AgencyMetaDataEntity();
        meta.setAgencyId(agencyId);// 设置机构 Id
        meta.setMetaKey(AgencyMetaKey.COMPLETE_RATING_REQUIRED_FOR_CHILD_LOCK.toString());// 设置机构数据的 key
        meta.setMetaValue("true");// 设置机构数据的 value
        // mock 数据
        when(agencyDao.getMeta(anyString(), anyString())).thenReturn(meta);

        // 调用方法
        GetRequireCompleteRatingResponse allMeasureStatus = portfolioService.getRequireCompleteRating(agencyId, null, null, null);

        // 验证结果，该机构开启了所有测评点
        assertTrue(allMeasureStatus.getRequireCompleteRating());

    }

    /**
     * Case: 参数中传入了学校 Id，且该学校所属机构开启了所有测评点评分才能锁定开关
     * 结果：返回 true
     */
    @Test
    public void testGetAllMeasureStatusByCenterId() {
        // 学校 Id
        String centerId = UUID.randomUUID().toString();
        // 机构数据
        AgencyEntity agencyEntity = new AgencyEntity();
        agencyEntity.setId(UUID.randomUUID().toString());// 设置机构 Id
        // mock 数据
        when(agencyDao.getByCenterId(anyString())).thenReturn(agencyEntity);
        // 机构开关数据
        AgencyMetaDataEntity meta = new AgencyMetaDataEntity();
        meta.setAgencyId(UUID.randomUUID().toString());// 设置机构 Id
        meta.setMetaKey(AgencyMetaKey.COMPLETE_RATING_REQUIRED_FOR_CHILD_LOCK.toString());// 设置机构数据的 key
        meta.setMetaValue("true");// 设置机构数据的 value
        // mock 数据
        when(agencyDao.getMeta(anyString(), anyString())).thenReturn(meta);

        // 调用方法
        GetRequireCompleteRatingResponse allMeasureStatus = portfolioService.getRequireCompleteRating(null, centerId, null, null);

        // 验证结果，该机构开启了所有测评点
        assertTrue(allMeasureStatus.getRequireCompleteRating());
    }

    /**
     * Case: 参数中传入了班级 Id，且该班级所属机构开启了所有测评点评分才能锁定开关
     * 结果：返回 true
     */
    @Test
    public void testGetAllMeasureStatusByGroupId() {
        // 班级 Id
        String groupId = UUID.randomUUID().toString();
        // 机构数据
        AgencyEntity agencyEntity = new AgencyEntity();
        agencyEntity.setId(UUID.randomUUID().toString());// 设置机构 Id
        // mock 数据
        when(agencyDao.getByGroupId(anyString())).thenReturn(agencyEntity);
        // 机构开关数据
        AgencyMetaDataEntity meta = new AgencyMetaDataEntity();
        meta.setAgencyId(UUID.randomUUID().toString());// 设置机构 Id
        meta.setMetaKey(AgencyMetaKey.COMPLETE_RATING_REQUIRED_FOR_CHILD_LOCK.toString());// 设置机构数据的 key
        meta.setMetaValue("true");// 设置机构数据的 value
        // mock 数据
        when(agencyDao.getMeta(anyString(), anyString())).thenReturn(meta);

        // 调用方法
        GetRequireCompleteRatingResponse allMeasureStatus = portfolioService.getRequireCompleteRating(null, null, groupId, null);

        // 验证结果，该机构开启了所有测评点
        assertTrue(allMeasureStatus.getRequireCompleteRating());
    }

    /**
     * Case: 参数中传入了学生 Id，且该学生所属机构开启了所有测评点评分才能锁定开关
     * 结果：返回 true
     */
    @Test
    public void testGetAllMeasureStatusByChildId() {
        // 学生 Id
        String childId = UUID.randomUUID().toString();
        // 机构数据
        AgencyEntity agencyEntity = new AgencyEntity();
        agencyEntity.setId(UUID.randomUUID().toString());// 设置机构 Id
        // mock 数据
        when(agencyDao.getAgencyByChildId(anyString())).thenReturn(agencyEntity);
        // 机构开关数据
        AgencyMetaDataEntity meta = new AgencyMetaDataEntity();
        meta.setAgencyId(UUID.randomUUID().toString());// 设置机构 Id
        meta.setMetaKey(AgencyMetaKey.COMPLETE_RATING_REQUIRED_FOR_CHILD_LOCK.toString());// 设置机构数据的 key
        meta.setMetaValue("true");// 设置机构数据的 value
        // mock 数据
        when(agencyDao.getMeta(anyString(), anyString())).thenReturn(meta);

        // 调用方法
        GetRequireCompleteRatingResponse allMeasureStatus = portfolioService.getRequireCompleteRating(null, null, null, childId);

        // 验证结果，该机构开启了所有测评点
        assertTrue(allMeasureStatus.getRequireCompleteRating());
    }

    /**
     * 测试通过框架 ID 获取框架测评点信息
     */
    @Test
    public void testGetFrameworkMeasures() {
        // 框架 ID
        String frameworkId = "frameworkId001";
        // 用户 Id
        String userId = "userId001";
        // 机构 Id
        String agencyId = "agencyId001";
        // 语言
        String language = "en";
        // 班级 Id
        String groupId = "groupId001";
        String portfolioId = "portfolioId001";
        // 机构数据
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        // 测评点数据
        DomainEntity domainEntity = new DomainEntity();
        domainEntity.setId("domainId001");
        domainEntity.setCore(false);
        domainEntity.setAbbreviation("ALT");
        domainEntity.setSpanishLinkUrl("spanishLinkUrl");

        // 模拟数据
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        when(userProvider.getAgencyOpenDefaultClose(agencyId, "DRDP_KEY_MEASURE_SETTING")).thenReturn(false);
        when(domainDao.getDomain(frameworkId)).thenReturn(domainEntity);
        when(userProvider.getFrameworkLanguage(userId)).thenReturn(language);
        when(portfolioProvider.hasScoreTemplate(domainEntity.getId())).thenReturn(false);
        when(userProvider.getCurrentLang()).thenReturn("es");
        when(regionService.isChina()).thenReturn(false);

        // 调用方法
        GetFrameworkMeasuresResponse response = portfolioService.getFrameworkMeasures(frameworkId);

        // 验证结果
        Assert.assertEquals(1, response.getFrameworks().size());
    }

    /**
     * 测试获取班级框架测评点信息
     */
    @Test
    public void testGetFrameworkMeasuresByGroupId() {
        // 框架 ID
        String frameworkId = "frameworkId001";
        // 用户 Id
        String userId = "userId001";
        // 班级 Id
        String groupId = "groupId";
        // 机构 Id
        String agencyId = "agencyId";
        // 测评点数据
        DomainEntity domainEntity = new DomainEntity();
        domainEntity.setId("domainId001");
        domainEntity.setCore(false);
        domainEntity.setAbbreviation("ALT");
        domainEntity.setSpanishLinkUrl("spanishLinkUrl");
        // 机构信息
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        // 框架信息
        List<GroupFrameworkStatsModel> groupFrameworkStatsModels = new ArrayList<>();
        GroupFrameworkStatsModel groupFrameworkStatsModel = new GroupFrameworkStatsModel();
        groupFrameworkStatsModel.setFrameworkId(frameworkId);
        groupFrameworkStatsModels.add(groupFrameworkStatsModel);

        // 模拟接口调用
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(studentDao.getUsedFrameworksByGroup(groupId)).thenReturn(groupFrameworkStatsModels);
        when(studentDao.getMetaByGroupId(groupId, DrdpAttr.IEP.toString())).thenReturn(new ArrayList<>());
        when(groupDao.getGroupDomainId(groupId)).thenReturn(frameworkId);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        when(userProvider.getAgencyOpenDefaultClose(agencyId, AgencyMetaKey.DRDP_KEY_MEASURE_SETTING.toString())).thenReturn(false);
        when(domainDao.getDomain(frameworkId)).thenReturn(domainEntity);

        // 调用测试方法
        GetFrameworkMeasuresResponse response = portfolioService.getGroupFrameworkMeasures(groupId, true);

        // 验证结果，返回框架数量为 1
        Assert.assertEquals(1, response.getFrameworks().size());
    }

    /**
     * 测试当 country 或 state 参数为空时抛出异常
     */
    @Test(expected = BusinessException.class)
    public void testGetStateFrameworkInfoByState_ParamError() {
        // 调用方法并期待异常
        portfolioService.getStateFrameworkInfoByState("", "state");
        portfolioService.getStateFrameworkInfoByState("country", "");
    }

    /**
     * 测试当框架数据为空时返回空响应
     */
    @Test
    public void testGetStateFrameworkInfoByState_FrameworkListEmpty() {
        when(frameworkDao.getByStateAndCountry("country", "state")).thenReturn(Collections.emptyList());

        GetStateFrameworkInfoByStateResponse response = portfolioService.getStateFrameworkInfoByState("country", "state");
        assertTrue(CollectionUtils.isEmpty(response.getFrameworks()));
    }

    /**
     * 测试当框架数据不为空时返回正确响应
     */
    @Test
    public void testGetStateFrameworkInfoByState_FrameworkListNotEmpty() {
        // 模拟框架数据
        FrameworkEntity framework1 = new FrameworkEntity() {{
            setId("1");
            setName("Framework1");
            setLinkUrl("http://example.com/A");
            setGrades("[\"Grade1\",\"Grade2\"]");
        }};
        FrameworkEntity framework2 = new FrameworkEntity() {{
            setId("2");
            setName("Framework2");
            setLinkUrl("http://example.com/B");
            setGrades("[\"Grade3\",\"Grade4\"]");
        }};
        List<FrameworkEntity> frameworkEntities = new ArrayList<>();
        frameworkEntities.add(framework1);
        frameworkEntities.add(framework2);

        when(frameworkDao.getByStateAndCountry("country", "California")).thenReturn(frameworkEntities);
        // 创建 mock 的 FrameworkEntity 列表
        FrameworkEntity frameworkEntity1 = new FrameworkEntity();
        frameworkEntity1.setId("1");
        frameworkEntity1.setName("Framework A");
        frameworkEntity1.setLinkUrl("http://example.com/A");
        frameworkEntity1.setGrades("[\"Grade1\",\"Grade2\"]");

        FrameworkEntity frameworkEntity2 = new FrameworkEntity();
        frameworkEntity2.setId("2");
        frameworkEntity2.setName("Framework B");
        frameworkEntity2.setLinkUrl("http://example.com/B");
        frameworkEntity2.setGrades("[\"Grade3\",\"Grade4\"]");

        // Mock frameworkDao 返回值
        when(frameworkDao.getByIds(anyList())).thenReturn(Arrays.asList(frameworkEntity1, frameworkEntity2));

        when(redisCacheServiceImpl.getObj(anyString(),any())).thenReturn(null);

        GetStateFrameworkInfoByStateResponse response = portfolioService.getStateFrameworkInfoByState("country", "California");

        assertNotNull(response);
        assertFalse(CollectionUtils.isEmpty(response.getFrameworks()));
        assertEquals("DRDP2015-PRESCHOOL Comprehensive view", response.getDefaultFrameworkName());
        assertEquals("TK (4-5)", response.getDefaultGrade());
        assertTrue(response.getGrades().contains("Grade1"));
        assertTrue(response.getGrades().contains("Grade3"));
    }

    @Test
    public void testGetAllFrameworkInfoByStateResponse_CacheHit() {
        // Prepare cached data
        List<FrameworkInfoByStateResponse> cachedData = new ArrayList<>();
        Type listType = new TypeToken<List<FrameworkInfoByStateResponse>>() {}.getType();
        String cacheKey = StringUtil.joinString(FrameworkKey.FRAMEWORK_ALL_CACHE_KEY, StringUtil.replaceStrBlankAndSymbol("United States"));

        // Call the method
        List<FrameworkInfoByStateResponse> result = portfolioService.getAllFrameworkInfoByStateResponse();

        // Verify the result
        assertEquals(cachedData, result);
        verify(redisCacheServiceImpl).getListObj(eq(cacheKey), eq(listType));
        verifyNoMoreInteractions(redisCacheServiceImpl);
    }

//    @Test
//    public void testGetAllFrameworkInfoByStateResponse_CacheMiss() {
//        // Prepare mock data
//        when(redisCacheServiceImpl.getListObj(anyString(), any(Type.class))).thenReturn(null);
//        List<String> states = Collections.singletonList("CA");
//        when(portfolioService.getStates()).thenReturn(states); // Mock the method call
//
//
//        GetStateFrameworkInfoByStateResponse stateFrameworkInfo = new GetStateFrameworkInfoByStateResponse();
//        when(portfolioService.getStateFrameworkInfoByState("country", "state")).thenReturn(stateFrameworkInfo);
//        when(redisCacheServiceImpl.setObj(anyString(), any(), anyInt())).thenReturn(true);
//
//        // Call the method
//        List<FrameworkInfoByStateResponse> result = portfolioService.getAllFrameworkInfoByStateResponse();
//
//        // Verify the result
//        assertEquals(1, result.size());
//        assertEquals("CA", result.get(0).getState());
//        assertEquals(stateFrameworkInfo, result.get(0).getStateFrameworkInfo());
//
//        // Verify interactions
//        verify(redisCacheServiceImpl).getListObj(eq(FrameworkKey.FRAMEWORK_ALL_CACHE_KEY), any(Type.class));
//        verify(portfolioService).getStates();
//        verify(portfolioService).getStateFrameworkInfoByState(eq("United States"), eq("CA"));
//        verify(redisCacheServiceImpl).setObj(eq(FrameworkKey.FRAMEWORK_ALL_CACHE_KEY), eq(result), anyInt());
//    }


    @Test
    public void testGetDefaultInfoByState_California() {
        // 准备测试数据
        String state = StateConstants.CALIFORNIA;
        List<SingleFrameworkNameResponse> frameworkList = new ArrayList<>();

        // 调用方法
        List<String> result = portfolioService.getDefaultInfoByState(state, frameworkList);

        // 验证返回值
        List<String> expected = new ArrayList<>();
        expected.add("DRDP2015-PRESCHOOL Comprehensive view");
        expected.add(NewAgeGroupEnum.TK.getName());
        assertEquals(expected, result);
    }

    @Test
    public void testGetDefaultInfoByState_Arizona() {
        // 准备测试数据
        String state = StateConstants.ARIZONA;
        List<SingleFrameworkNameResponse> frameworkList = new ArrayList<>();

        // 调用方法
        List<String> result = portfolioService.getDefaultInfoByState(state, frameworkList);

        // 验证返回值
        List<String> expected = new ArrayList<>();
        expected.add("Arizona Early Learning Standards");
        expected.add(NewAgeGroupEnum.TK.getName());
        assertEquals(expected, result);
    }

    @Test
    public void testGetDefaultInfoByState_Maryland() {
        // 准备测试数据
        String state = StateConstants.MARYLAND;
        List<SingleFrameworkNameResponse> frameworkList = new ArrayList<>();

        // 调用方法
        List<String> result = portfolioService.getDefaultInfoByState(state, frameworkList);

        // 验证返回值
        List<String> expected = new ArrayList<>();
        expected.add("Maryland Early Learning Standards 0-48 months");
        expected.add(NewAgeGroupEnum.PS_PK.getName());
        assertEquals(expected, result);
    }

    @Test
    public void testGetDefaultInfoByState_OtherStates() {
        // 准备测试数据
        String state = "OtherState";
        List<SingleFrameworkNameResponse> frameworkList = new ArrayList<>();
        SingleFrameworkNameResponse framework1 = new SingleFrameworkNameResponse();
        framework1.setFrameworkName("Framework1");
        Map<String, String> gradeFrameworkIdMap1 = new HashMap<>();
        gradeFrameworkIdMap1.put(NewAgeGroupEnum.TK.getName(), "Framework1Id");
        framework1.setGradeFrameworkIdMap(gradeFrameworkIdMap1);
        frameworkList.add(framework1);

        // 调用方法
        List<String> result = portfolioService.getDefaultInfoByState(state, frameworkList);

        // 验证返回值
        List<String> expected = new ArrayList<>();
        expected.add("Framework1");
        expected.add(NewAgeGroupEnum.TK.getName());
        assertEquals(expected, result);
    }

    /**
     * 创建课程单元
     *
     * @param frameworkId   框架id
     * @param frameworkName 框架名称
     * @return {@link CurriculumUnitEntity}
     */
    private CurriculumUnitEntity createCurriculumUnit(String frameworkId, String frameworkName) {
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setFrameworkId(frameworkId);
        FrameworkEntity framework = new FrameworkEntity();
        framework.setId(frameworkId);
        framework.setName(frameworkName);
        when(frameworkDao.getById(frameworkId)).thenReturn(framework);
        return unit;
    }

    /**
     * 测试 supStateInfo 方法，覆盖所有状态分支
     */
    @Test
    public void testSupStateInfo_AllStates() {
        List<CurriculumUnitEntity> stateUnits = new ArrayList<>();

        // 创建包含每种状态的框架实体
        stateUnits.add(createCurriculumUnit("framework1", "Some IL Framework"));
        stateUnits.add(createCurriculumUnit("framework2", "Some Illinois Framework"));
        stateUnits.add(createCurriculumUnit("framework3", "Some Pennsylvania Framework"));
        stateUnits.add(createCurriculumUnit("framework4", "Some Ohio Framework"));
        stateUnits.add(createCurriculumUnit("framework5", "Some Missouri Framework"));
        stateUnits.add(createCurriculumUnit("framework6", "Some Texas Framework"));
        stateUnits.add(createCurriculumUnit("framework7", "Some Florida Framework"));
        stateUnits.add(createCurriculumUnit("framework8", "Some Other Framework"));
        // 模拟返回 stateUnit 列表
        when(frameworkMapper.getNullStateUnit()).thenReturn(stateUnits);

        int result = portfolioService.supStateInfo();

        // 验证 updateById 方法被调用次数与 stateUnits 大小相同
        verify(curriculumUnitDao, times(stateUnits.size())).updateById(any(CurriculumUnitEntity.class));

        // 验证每个单元的 state 设置是否正确
        assertEquals(StateConstants.ILLINOIS, stateUnits.get(0).getState());
        assertEquals(StateConstants.ILLINOIS, stateUnits.get(1).getState());
        assertEquals(StateConstants.PENNSYLVANIA, stateUnits.get(2).getState());
        assertEquals(StateConstants.OHIO, stateUnits.get(3).getState());
        assertEquals(StateConstants.MISSOURI, stateUnits.get(4).getState());
        assertEquals(StateConstants.TEXAS, stateUnits.get(5).getState());
        assertEquals(StateConstants.FLORIDA, stateUnits.get(6).getState());
        assertEquals(StateConstants.CALIFORNIA, stateUnits.get(7).getState());

        // 验证返回结果为 stateUnits 的大小
        assertEquals(stateUnits.size(), result);
    }

    /**
     * 测试框架 ID 为空的情况
     */
    @Test(expected = BusinessException.class)
    public void testGetDomainInfosByFrameworkId_NullFrameworkId() {
        portfolioService.getDomainInfosByFrameworkId(null);
    }

    /**
     * 测试框架 ID 为空字符串的情况
     */
    @Test(expected = BusinessException.class)
    public void testGetDomainInfosByFrameworkId_BlankFrameworkId() {
        portfolioService.getDomainInfosByFrameworkId("");
    }

    /**
     * 测试查询框架对应领域信息为空的情况
     */
    @Test
    public void testGetDomainInfosByFrameworkId_EmptyList() {
        String frameworkId = "validFrameworkId";
        when(frameworkMapper.getMesureByFrameworkId(frameworkId)).thenReturn(Collections.emptyList());
        com.learninggenie.common.data.entity.frameworks.FrameworkEntity framework = new FrameworkEntity();
        framework.setCountry("United States");
        when(frameworkDao.getById(any())).thenReturn(framework);

        List<MeasureEntity> result = portfolioService.getDomainInfosByFrameworkId(frameworkId);

        assertTrue(result.isEmpty());
        verify(frameworkMapper, times(1)).getMesureByFrameworkId(frameworkId);
    }

    /**
     * 测试查询框架对应领域信息非空的情况
     */
    @Test
    public void testGetDomainInfosByFrameworkId_NonEmptyList() {
        String frameworkId = "validFrameworkId";
        List<MeasureEntity> mockedList = Collections.singletonList(new MeasureEntity());
        when(frameworkMapper.getMesureByFrameworkId(frameworkId)).thenReturn(mockedList);
        com.learninggenie.common.data.entity.frameworks.FrameworkEntity framework = new FrameworkEntity();
        framework.setCountry("United States");
        when(frameworkDao.getById(any())).thenReturn(framework);

        List<MeasureEntity> result = portfolioService.getDomainInfosByFrameworkId(frameworkId);

        assertEquals(mockedList, result);
        verify(frameworkMapper, times(1)).getMesureByFrameworkId(frameworkId);
    }

    /**
     * 测试获取状态
     */
    @Test
    public void testGetStates() {
        // 方法调用
        List<String> states = portfolioService.getStates();
        // 验证结果
        assertNotNull(states);
    }


    /**
     * 测试框架数据为空的情况
     */
    @Test
    public void testDeleteFrameworkByFrameworkName_EmptyFrameworks() {
        String frameworkName = "nonExistentFramework";
        when(frameworkMapper.getFrameworkByFrameworkName(frameworkName)).thenReturn(Collections.emptyList());

        portfolioService.deleteFrameworkByFrameworkName(frameworkName);

        verify(frameworkMapper, never()).deleteFrameworkMeasureByFrameworkName(anyList());
        verify(frameworkMapper, never()).deleteFrameworkViewByFrameworkName(anyList());
        verify(frameworkMapper, never()).deleteFrameworkByFrameworkName(frameworkName);
    }

    /**
     * 测试框架数据不为空的情况
     */
    @Test
    public void testDeleteFrameworkByFrameworkName_NonEmptyFrameworks() {
        String frameworkName = "existingFramework";
        List<FrameworkEntity> frameworks = Arrays.asList(
                new FrameworkEntity(){{
                    setId("id1");
                    setEvaluationType("evaluationType1");
                }},
                new FrameworkEntity(){{
                    setId("id2");
                    setEvaluationType("evaluationType2");
                }}
        );
        when(frameworkMapper.getFrameworkByFrameworkName(frameworkName)).thenReturn(frameworks);

        portfolioService.deleteFrameworkByFrameworkName(frameworkName);

        List<String> evaluationTypes = frameworks.stream().map(FrameworkEntity::getEvaluationType).collect(Collectors.toList());
        verify(frameworkMapper, times(1)).deleteFrameworkMeasureByFrameworkName(evaluationTypes);
        List<String> frameworkIds = frameworks.stream().map(FrameworkEntity::getId).collect(Collectors.toList());
        verify(frameworkMapper, times(1)).deleteFrameworkViewByFrameworkName(frameworkIds);
        verify(frameworkMapper, times(1)).deleteFrameworkByFrameworkName(frameworkName);
    }
}

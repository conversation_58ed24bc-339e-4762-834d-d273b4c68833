package com.learninggenie.api.service.impl.inkind;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.toolkit.ChainWrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.model.DownFileResponse;
import com.learninggenie.api.model.InkindExportRequest;
import com.learninggenie.api.model.InkindStatisticRequest;
import com.learninggenie.api.model.SuccessResponse;
import com.learninggenie.api.model.googleslides.ReplaceRelation;
import com.learninggenie.api.model.inkind.*;
import com.learninggenie.api.model.inkind.goal.GetGoalReportListRequest;
import com.learninggenie.api.provider.RemoteProvider;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.security.AuthUserDetails;
import com.learninggenie.api.service.MediaService;
import com.learninggenie.api.provider.InKindProvider;
import com.learninggenie.api.service.inkind.InKindGoalService;
import com.learninggenie.api.service.inkind.InKindReportService;
import com.learninggenie.api.service.inkind.InKindStatisticsService;
import com.learninggenie.api.util.FileUploadUtil;
import com.learninggenie.common.data.dao.*;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.dao.inkind.*;
import com.learninggenie.common.data.dto.GroupWithCenter;
import com.learninggenie.common.data.entity.*;
import com.learninggenie.common.data.entity.CenterEntity;
import com.learninggenie.common.data.entity.GroupEntity;
import com.learninggenie.common.data.entity.UserEntity;
import com.learninggenie.common.data.entity.inkind.InKindReportApprove;
import com.learninggenie.common.data.entity.inkind.InKindsGoalSchoolYear;
import com.learninggenie.common.data.enums.AgencyMetaKey;
import com.learninggenie.common.data.enums.InKindStageEnum;
import com.learninggenie.common.data.enums.UserMetaKey;
import com.learninggenie.common.data.enums.UserRole;
import com.learninggenie.common.data.enums.googleslides.ReplaceType;
import com.learninggenie.common.data.enums.inkind.ActivityTypeValue;
import com.learninggenie.common.data.enums.inkind.ApproveRole;
import com.learninggenie.common.data.enums.inkind.InKindUnit;
import com.learninggenie.common.data.model.*;
import com.learninggenie.common.data.model.inkind.*;
import com.learninggenie.common.filesystem.FileSystem;
import com.learninggenie.common.utils.FileUtil;
import com.learninggenie.common.utils.ResourceUtil;
import com.learninggenie.common.utils.TimeUtil;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.Assert;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.env.Environment;
import org.springframework.test.util.ReflectionTestUtils;
import org.thymeleaf.TemplateEngine;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.times;

@ExtendWith(MockitoExtension.class)
class InKindExportServiceImplTest {
    @InjectMocks
    InKindExportServiceImpl inKindExportService;

    @Mock
    private InKindProvider inKindProvider;

    @Mock
    private InKindReportService inKindReportService;

    @Mock
    private InKindStatisticsService inKindStatisticsService;

    @Mock
    private InKindGoalService inKindGoalService;

    @Mock
    private UserProvider userProvider;

    @Mock
    private AgencyDao agencyDao;

    @Mock
    private CenterDao centerDao;

    @Mock
    private GroupDao groupDao;

    @Mock
    private StudentDao studentDao;

    @Mock
    private UserDaoImpl userDao;

    @Mock
    private InkindDao inkindDao;

    @Mock
    private InKindReportApproveDao inKindReportApproveDao;

    @Mock
    private InKindsGoalGrantDao inKindsGoalGrantDao;

    @Mock
    private InKindsGoalGroupDao inKindsGoalGroupDao;

    @Mock
    private ReportDao reportDao;

    @Mock
    private UsersFileDao usersFileDao;

    @Mock
    private MediaDao mediaDao;

    @Mock
    private MediaService mediaService;

    @Mock
    private FileSystem fileSystem;

    @Mock
    private RemoteProvider remoteProvider;

    @Mock
    private TemplateEngine templateEngine;

    @Mock
    private Environment env;

    private MockedStatic<ChainWrappers> chainWrappersMockedStatic;

    private MockedStatic<ResourceUtil> resourceUtilStatic;

    private MockedStatic<FileUtil> fileUtilStatic;

    private MockedStatic<FileUploadUtil> fileUploadUtilStatic;


    /**
     * 初始化 TableInfoHelper
     */
    @BeforeAll
    public static void beforeClass() {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), InKindReportModel.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), InKindReportApprove.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), InKindsGoalSchoolYear.class);
    }

    /**
     * 注册 MockedStatic
     */
    @BeforeEach
    public void beforeMethod() {
        // 打印
        resourceUtilStatic = mockStatic(ResourceUtil.class);
        chainWrappersMockedStatic = mockStatic(ChainWrappers.class);
        fileUtilStatic = mockStatic(FileUtil.class);
        fileUploadUtilStatic = mockStatic(FileUploadUtil.class);

        MockitoAnnotations.initMocks(this);

    }

    /**
     * 关闭 MockedStatic
     */
    @AfterEach
    public void afterMethod() {
        // 打印
        fileUploadUtilStatic.close();
        fileUtilStatic.close();
        chainWrappersMockedStatic.close();
        resourceUtilStatic.close();
    }


    /**
     * 测试用例：获取统计数据
     */
    @Disabled
    @Test
    void getStatisticsExcelAll() throws IOException {
        // 数据准备 -- 入参
        String userId = "U0001";
        String agencyId = "A0001";
        String centerId = "C0001";
        InkindStatisticRequest request = new InkindStatisticRequest();
        request.setActivityStartDateStr("2020-01-01");
        request.setActivityEndDateStr("2020-01-01");
        String[] centerIds = {"C0001"};
        request.setCenterIds(centerIds);
        String[] selectAllGroupByCenterIds = {"C0001"};
        request.setSelectAllGroupByCenterIds(selectAllGroupByCenterIds);
        String[] groupIds = {"G0001"};
        request.setGroupIds(groupIds);
        String[] inkindGroupIds = {"G0001"};
        request.setInkindGroupIds(inkindGroupIds);
        request.setAllCenter(true);
        request.setAllGroup(true);
        request.setTableType("Center");

        InkindStatisticRequest request1 = new InkindStatisticRequest();
        request1.setActivityStartDateStr("2020-01-01");
        request1.setActivityEndDateStr("2020-01-01");
        request1.setCenterIds(centerIds);
        request1.setGroupIds(groupIds);
        request1.setInkindGroupIds(inkindGroupIds);
        request1.setAllCenter(false);
        request1.setAllGroup(true);
        request1.setTableType("group");

        InkindStatisticRequest request2 = new InkindStatisticRequest();
        request2.setActivityStartDateStr("2020-01-01");
        request2.setActivityEndDateStr("2020-01-01");
        request2.setCenterIds(centerIds);
        request2.setGroupIds(groupIds);
        request2.setInkindGroupIds(inkindGroupIds);
        request2.setAllCenter(false);
        request2.setAllGroup(false);
        request2.setTableType("child");

        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole("AGENCY_ADMIN");

        List<AgencyModel> agencyModelList = new ArrayList<>();
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        agencyModel.setName("test Agency");
        agencyModelList.add(agencyModel);

        GetReportCenterResponse getReportCenterResponse = new GetReportCenterResponse();
        InKindReportCenterModel inKindReportCenterModel = new InKindReportCenterModel();
        inKindReportCenterModel.setId("C0001");
        inKindReportCenterModel.setChildrenNum(1);
        inKindReportCenterModel.setClassesNum(1);
        getReportCenterResponse.setCenters(Arrays.asList(inKindReportCenterModel));

        InkindReportPool inkindReportPool = new InkindReportPool();
        List<InkindReportEntity> reportList = new ArrayList<>();
        List<InkindReportAppendEntity> reportAppendList = new ArrayList<>();
        List<InkindReportAppendEntity> reportAgencyAppendList = new ArrayList<>();
        InkindReportEntity inkindReportEntity = new InkindReportEntity();
        inkindReportEntity.setId("D0001");
        inkindReportEntity.setActivityType(ActivityTypeValue.AT_HOME.toString());
        inkindReportEntity.setApproveStatus("APPROVED");
        inkindReportEntity.setEnrollmentId("E0001");
        inkindReportEntity.setCenterId("C0001");
        inkindReportEntity.setMoney(new BigDecimal(100));
        inkindReportEntity.setUnit("HOUR");
        inkindReportEntity.setValue(new BigDecimal(10));
        inkindReportEntity.setRateValue(new BigDecimal(10));
        reportList.add(inkindReportEntity);

        InkindReportAppendEntity inkindReportAppendEntity = new InkindReportAppendEntity();
        inkindReportAppendEntity.setId("D0001");
        inkindReportAppendEntity.setAgencyId(agencyId);
        inkindReportAppendEntity.setApproveStatus("APPROVED");
        inkindReportAppendEntity.setCenterId("C0001");
        inkindReportAppendEntity.setMoney(new BigDecimal(100));
        inkindReportAppendEntity.setUnit("HOUR");
        inkindReportAppendEntity.setValue(new BigDecimal(10));
        inkindReportAppendEntity.setRateValue(new BigDecimal(10));
        reportAppendList.add(inkindReportAppendEntity);

        InkindReportAppendEntity inkindReportAgencyAppendEntity = new InkindReportAppendEntity();
        inkindReportAgencyAppendEntity.setId("D0003");
        inkindReportAgencyAppendEntity.setAgencyId(agencyId);
        inkindReportAgencyAppendEntity.setApproveStatus("APPROVED");
        inkindReportAgencyAppendEntity.setType(ActivityTypeValue.AT_HOME.toString());
        inkindReportAgencyAppendEntity.setCenterId("C0001");
        inkindReportAgencyAppendEntity.setMoney(new BigDecimal(100));
        inkindReportAgencyAppendEntity.setUnit("HOUR");
        inkindReportAgencyAppendEntity.setValue(new BigDecimal(10));
        inkindReportAgencyAppendEntity.setRateValue(new BigDecimal(10));

        InkindReportAppendEntity inkindReportAgencyAppendEntity1 = new InkindReportAppendEntity();
        inkindReportAgencyAppendEntity1.setId("D0004");
        inkindReportAgencyAppendEntity1.setAgencyId(agencyId);
        inkindReportAgencyAppendEntity1.setApproveStatus("APPROVED");
        inkindReportAgencyAppendEntity1.setType(ActivityTypeValue.VOLUNTEER.toString());
        inkindReportAgencyAppendEntity1.setMoney(new BigDecimal(100));
        inkindReportAgencyAppendEntity1.setUnit("HOUR");
        inkindReportAgencyAppendEntity1.setCenterId("C0001");
        inkindReportAgencyAppendEntity1.setValue(new BigDecimal(10));
        inkindReportAgencyAppendEntity1.setRateValue(new BigDecimal(10));

        InkindReportAppendEntity inkindReportAgencyAppendEntity2 = new InkindReportAppendEntity();
        inkindReportAgencyAppendEntity2.setId("D0004");
        inkindReportAgencyAppendEntity2.setAgencyId(agencyId);
        inkindReportAgencyAppendEntity2.setApproveStatus("APPROVED");
        inkindReportAgencyAppendEntity2.setMoney(new BigDecimal(100));
        inkindReportAgencyAppendEntity2.setUnit("HOUR");
        inkindReportAgencyAppendEntity2.setType(ActivityTypeValue.VOLUNTEER.toString());
        inkindReportAgencyAppendEntity2.setValue(new BigDecimal(10));
        inkindReportAgencyAppendEntity2.setRateValue(new BigDecimal(10));

        InkindReportAppendEntity inkindReportAgencyAppendEntity3 = new InkindReportAppendEntity();
        inkindReportAgencyAppendEntity3.setId("D0005");
        inkindReportAgencyAppendEntity3.setAgencyId(agencyId);
        inkindReportAgencyAppendEntity3.setApproveStatus("APPROVED");
        inkindReportAgencyAppendEntity3.setMoney(new BigDecimal(100));
        inkindReportAgencyAppendEntity3.setType(ActivityTypeValue.MILEAGE.toString());
        inkindReportAgencyAppendEntity3.setUnit("MILE");
        inkindReportAgencyAppendEntity3.setValue(new BigDecimal(10));
        inkindReportAgencyAppendEntity3.setRateValue(new BigDecimal(10));

        InkindReportAppendEntity inkindReportAgencyAppendEntity4 = new InkindReportAppendEntity();
        inkindReportAgencyAppendEntity4.setId("D0006");
        inkindReportAgencyAppendEntity4.setAgencyId(agencyId);
        inkindReportAgencyAppendEntity4.setApproveStatus("APPROVED");
        inkindReportAgencyAppendEntity4.setMoney(new BigDecimal(100));
        inkindReportAgencyAppendEntity4.setUnit("HOUR");
        inkindReportAgencyAppendEntity4.setType(ActivityTypeValue.AT_HOME.toString());
        inkindReportAgencyAppendEntity4.setCenterId("C0001");
        inkindReportAgencyAppendEntity4.setValue(new BigDecimal(10));
        inkindReportAgencyAppendEntity4.setRateValue(new BigDecimal(10));

        InkindReportAppendEntity inkindReportAgencyAppendEntity5 = new InkindReportAppendEntity();
        inkindReportAgencyAppendEntity5.setId("D0007");
        inkindReportAgencyAppendEntity5.setAgencyId(agencyId);
        inkindReportAgencyAppendEntity5.setApproveStatus("APPROVED");
        inkindReportAgencyAppendEntity5.setMoney(new BigDecimal(100));
        inkindReportAgencyAppendEntity5.setType(ActivityTypeValue.AT_HOME.toString());
        inkindReportAgencyAppendEntity5.setUnit("HOUR");
        inkindReportAgencyAppendEntity5.setValue(new BigDecimal(10));
        inkindReportAgencyAppendEntity5.setRateValue(new BigDecimal(10));

        reportAgencyAppendList.add(inkindReportAgencyAppendEntity);
        reportAgencyAppendList.add(inkindReportAgencyAppendEntity1);
        reportAgencyAppendList.add(inkindReportAgencyAppendEntity2);
        reportAgencyAppendList.add(inkindReportAgencyAppendEntity3);
        reportAgencyAppendList.add(inkindReportAgencyAppendEntity4);
        reportAgencyAppendList.add(inkindReportAgencyAppendEntity5);

        inkindReportPool.setReportList(reportList);
        inkindReportPool.setReportAppendList(reportAppendList);
        inkindReportPool.setReportAgencyAppendList(reportAgencyAppendList);

        UserMetaDataEntity userMetaDataEntity = new UserMetaDataEntity();
        userMetaDataEntity.setMetaValue("true");

        List<CenterModel> centerModelList = new ArrayList<>();
        CenterModel centerModel = new CenterModel();
        centerModel.setId("C0001");
        centerModel.setName("test center");
        centerModelList.add(centerModel);

        List<EnrollmentModel> enrollmentModelList = new ArrayList<>();
        EnrollmentModel enrollmentModel = new EnrollmentModel();
        enrollmentModel.setId("E0001");
        enrollmentModel.setCenterId("C0001");
        enrollmentModelList.add(enrollmentModel);

        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setName("test");
        centerEntity.setId("COOO1");

        // 数据准备 -- 接口模拟
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.checkUser(userId)).thenReturn(userEntity);
        when(userDao.getAgencyByAgencyAdminId(userId)).thenReturn(agencyModelList);
        when(inkindDao.getByGroupIdsAndDates(Arrays.asList(request.getGroupIds()), request.getActivityStartDateStr(), request.getActivityEndDateStr(), InKindStageEnum.EFFECTIVE)).thenReturn(reportList);
        when(inkindDao.getAppendReportByCenterIdsAndGroupIdsAndDates(Arrays.asList(request.getCenterIds()), Arrays.asList(request.getGroupIds()), request.getActivityStartDateStr(), request.getActivityEndDateStr(), InKindStageEnum.EFFECTIVE)).thenReturn(reportAppendList);
        when(inkindDao.getAppendReportByAgencyIdAndDatesAndSelectCenters(agencyId, request.getActivityStartDateStr(), request.getActivityEndDateStr(), Arrays.asList(request.getCenterIds()), request.isAllCenter(), InKindStageEnum.EFFECTIVE)).thenReturn(reportAgencyAppendList);
        when(centerDao.getAllCenterAndGroupsByAgencyUserId(userId)).thenReturn(centerModelList);
        when(userDao.getMetaData(userId, UserMetaKey.FILTER_TRAINING_SITE.toString())).thenReturn(userMetaDataEntity);
        when(studentDao.getAllChildrenByIds(new ArrayList<>(Arrays.asList("E0001")))).thenReturn(enrollmentModelList);
        when(centerDao.getCenterById(centerId)).thenReturn(centerEntity);

        ReflectionTestUtils.setField(inKindExportService, "s3BucketName", "");
        ReflectionTestUtils.setField(inKindExportService, "s3Root", "");

        List<InkindReportAppendEntity> inkindReportAppendEntityList = new ArrayList<>();
        inKindExportService.getStatisticsExcelAll(request);
        inKindExportService.getStatisticsExcelAll(request1);
        inKindExportService.getStatisticsExcelAll(request2);

    }


    /**
     * 首页 Excel 下载
     */
    @Disabled
    @Test
    void testDownloadGrantExcel() throws IOException {
        String agencyId = "A0001";
        GetGoalReportListRequest request = new GetGoalReportListRequest();
        request.setSchoolYear("2023-2024");
        request.setGrantId("G0001");
        request.setGroupIds(Arrays.asList("G0001", "G0003"));
        request.setCenterIds(Arrays.asList("C0001"));
        request.setProgramName("test Program");
        request.setAgencyId(agencyId);

        // this.testGetGrantDetail();
//        Mockito.mockStatic(FileUploadUtil.class);
        ReflectionTestUtils.setField(inKindExportService, "s3BucketName", "");
        ReflectionTestUtils.setField(inKindExportService, "s3Root", "");
        DownFileResponse response = inKindExportService.downloadGrantExcel(request);
        assertNotNull(response);

        GetGoalReportListRequest request1 = new GetGoalReportListRequest();
        request1.setSchoolYear("2021-2022");
        request1.setGroupIds(Arrays.asList("G0001", "G0003"));
        request1.setCenterIds(Arrays.asList("C0001"));
        request1.setProgramName("test Program");
        request1.setAgencyId(agencyId);
        DownFileResponse response1 = inKindExportService.downloadGrantExcel(request1);
        assertNotNull(response1);
    }


    /**
     * 测试改版 AtHome PDF下载
     */
    @Test
    void testDownloadAtHomeInKindPDF() {
        // 数据准备
        InkindDonateListRequest request = new InkindDonateListRequest();
        request.setCurrentPage(1);
        request.setPageSize(10);
        request.setStartTime("2023-01-01");
        request.setEndTime("2024-01-01");
        request.setType("AT_HOME");
        request.setColumn("donorName");
        request.setOrder("ASC");
        request.setCurrentPage(1);
        request.setPageSize(10);
        request.setCommunity(false);
        request.setAllGroup(false);
        request.setPrecision(false);
        request.setAllCenter(false);
        request.setSelectCenters(Arrays.asList("C0001"));
        request.setSelectGroups(Arrays.asList("G0001"));
        request.setSelectAllGroupByCenterIds(Arrays.asList("C0001"));

        String userId = "U0001";
        String type = "AT_HOME";
        String agencyId = "A0001";
        String startDate = "2023-01-01";
        String endDate = "2024-01-01";
        String donateId = "D0001";
        String role = "AGENCY_ADMIN";
        String language = "en";
        AgencyModel agency = new AgencyModel();

        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole(role);
        userEntity.setUserName("test User");

        List<InKindDonate> inKindDonateList = new ArrayList<>();
        InKindDonate inkindDonate = new InKindDonate();
        inkindDonate.setId(donateId);
        inkindDonate.setAgencyId(agencyId);
        inkindDonate.setAgencyName("test Agency");
        inkindDonate.setCenterId("C0001");
        inkindDonate.setCenterName("test Center");
        inkindDonate.setGroupId("G0001");
        inkindDonate.setGroupName("test Group");
        inkindDonate.setDonorRelationship(role);
        inkindDonate.setApproveUserId(userId);
        inkindDonate.setApproveUserRelationship(role);
        inkindDonate.setReportType("NEW");
        inkindDonate.setUnit("MINUTE");
        inkindDonate.setRateUnit("MINUTE");
        inKindDonateList.add(inkindDonate);

        List<InKindReportApprove> ratifyReportList = new ArrayList<>();
        InKindReportApprove ratifyReport = new InKindReportApprove();
        ratifyReport.setId(donateId);
        ratifyReport.setSignatureId("S0001");
        ratifyReportList.add(ratifyReport);

        List<MediaEntity> medias = new ArrayList<>();
        MediaEntity mediaModel = new MediaEntity();
        mediaModel.setId("S0001");
        mediaModel.setPrivateFile(false);
        mediaModel.setRelativePath("test");
        medias.add(mediaModel);

        List<InKindReportApprove> ratifyKindReportApproveList = new ArrayList<>();
        InKindReportApprove inKindReportApprove = new InKindReportApprove();
        inKindReportApprove.setId(donateId);
        inKindReportApprove.setReportId("D0001");
        inKindReportApprove.setUserId("U0001");
        inKindReportApprove.setSignatureId("S0001");
        ratifyKindReportApproveList.add(inKindReportApprove);

        InKindDonate donate = new InKindDonate();
        donate.setId(donateId);

        ReflectionTestUtils.setField(inKindExportService, "endPoint", "e");
        ReflectionTestUtils.setField(inKindExportService, "pdfEndpoint", "p");
        List<AgencyModel> agencyModelList = new ArrayList<>();
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        agencyModel.setName("test Agency");
        agencyModelList.add(agencyModel);

        // 数据准备 -- 接口模拟

        when(userProvider.getCurrentLang()).thenReturn(language);
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        when(userProvider.checkUser(userId)).thenReturn(userEntity);
        when(inKindProvider.getAgencyId()).thenReturn(agencyId);

        InKindTotalInfo totalInfo = new InKindTotalInfo();
        totalInfo.setTotal(1);
        when(inkindDao.getInKindTotalInfoBySelectCenters(type, agencyId, TimeUtil.parseDate(startDate), TimeUtil.parseDate(endDate), false, Arrays.asList("C0001"), Arrays.asList("G0001"), false, false, Arrays.asList("C0001"), userEntity, false)).thenReturn(totalInfo);
        when(inkindDao.getDonateInkindByPageAndSelectCenters(type, agencyId, TimeUtil.parseDate(startDate), TimeUtil.parseDate(endDate), "donorName", "ASC", "0", "10", false, Arrays.asList("C0001"), Arrays.asList("G0001"), false, false, false, Arrays.asList("C0001"), userEntity, false)).thenReturn(inKindDonateList);
        when(inKindReportApproveDao.getRatifySignatureReportApproveList(Arrays.asList(donateId))).thenReturn(ratifyKindReportApproveList);
        when(mediaDao.getMedias(any())).thenReturn(medias);
        // when(userDao.getAgencyByAgencyAdminId(userId)).thenReturn(agencyModelList);
        // Mockito.when(userProvider.getUser(inKindReportApprove.getUserId())).thenReturn(userEntity);
        when(fileSystem.getPublicUrl(any())).thenReturn("http://test.com");
        when(fileSystem.getPublicUrl(any(), any())).thenReturn("http://test.com");
        // when(inkindDao.countDonateInkind(type, null, TimeUtil.parseDate(startDate), TimeUtil.parseDate(endDate), false, Arrays.asList("G0001"), Arrays.asList("C0001"), false, false, Arrays.asList("C0001"))).thenReturn(inKindDonateList.size());

        ReflectionTestUtils.setField(inKindExportService, "pdfBucket", "pdfBucket");
        resourceUtilStatic.when(() -> ResourceUtil.getResourceAsString(anyString())).thenReturn("html");
        fileUtilStatic.when(() -> FileUtil.randomTempFilePath(anyString())).thenReturn("test.html");

        SuccessResponse response = inKindExportService.downloadDonateInKindPDF(request);
        assertNotNull(response);

    }


    /**
     * 测试改版 Mileage PDF下载
     */
    @Test
    void testDownloadMileageInKindPDF() {
        InkindDonateListRequest request = new InkindDonateListRequest();
        request.setCurrentPage(1);
        request.setPageSize(10);
        request.setStartTime("2023-01-01");
        request.setEndTime("2024-01-01");
        request.setType("VOLUNTEER");
        request.setColumn("donorName");
        request.setOrder("ASC");
        request.setCurrentPage(1);
        request.setPageSize(10);
        request.setCommunity(false);
        request.setAllGroup(false);
        request.setPrecision(false);
        request.setAllCenter(false);
        request.setSelectCenters(Arrays.asList("C0001"));
        request.setSelectGroups(Arrays.asList("G0001"));
        request.setSelectAllGroupByCenterIds(Arrays.asList("C0001"));

        String userId = "U0001";
        String type = "VOLUNTEER";
        String agencyId = "A0001";
        String startDate = "2023-01-01";
        String endDate = "2024-01-01";
        String donateId = "D0001";
        String role = "AGENCY_ADMIN";
        String language = "en";
        AgencyModel agency = new AgencyModel();

        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole(role);
        userEntity.setUserName("test User");

        List<InKindDonate> inKindDonateList = new ArrayList<>();
        InKindDonate inkindDonate = new InKindDonate();
        inkindDonate.setId(donateId);
        inkindDonate.setAgencyId(agencyId);
        inkindDonate.setAgencyName("test Agency");
        // inkindDonate.setCenterId("C0001");
        // 志愿者活动需要 DonorType
        inkindDonate.setDonorType("ENTITY");
        inkindDonate.setCenterName("test Center");
        inkindDonate.setGroupId("G0001");
        inkindDonate.setGroupName("test Group");
        inkindDonate.setDonorRelationship(role);
        inkindDonate.setApproveUserId(userId);
        inkindDonate.setApproveUserRelationship(role);
        inkindDonate.setReportType("NEW");
        inkindDonate.setUnit("MINUTE");
        inkindDonate.setRateUnit("MINUTE");
        inKindDonateList.add(inkindDonate);

        List<InKindReportApprove> ratifyReportList = new ArrayList<>();
        InKindReportApprove ratifyReport = new InKindReportApprove();
        ratifyReport.setId(donateId);
        ratifyReport.setSignatureId("S0001");
        ratifyReportList.add(ratifyReport);

        List<MediaEntity> medias = new ArrayList<>();
        MediaEntity mediaModel = new MediaEntity();
        mediaModel.setId("S0001");
        mediaModel.setPrivateFile(false);
        mediaModel.setRelativePath("test");
        medias.add(mediaModel);

        List<InKindReportApprove> ratifyKindReportApproveList = new ArrayList<>();
        InKindReportApprove inKindReportApprove = new InKindReportApprove();
        inKindReportApprove.setId(donateId);
        inKindReportApprove.setReportId("D0001");
        inKindReportApprove.setUserId("U0001");
        inKindReportApprove.setSignatureId("S0001");
        ratifyKindReportApproveList.add(inKindReportApprove);

        InKindDonate donate = new InKindDonate();
        donate.setId(donateId);

        ReflectionTestUtils.setField(inKindExportService, "endPoint", "e");
        ReflectionTestUtils.setField(inKindExportService, "pdfEndpoint", "p");

        List<AgencyModel> agencyModelList = new ArrayList<>();
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        agencyModel.setName("test Agency");
        agencyModelList.add(agencyModel);
        // 数据准备 -- 接口模拟
        when(userProvider.getCurrentLang()).thenReturn(language);
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        when(userProvider.checkUser(userId)).thenReturn(userEntity);
        when(inKindProvider.getAgencyId()).thenReturn(agencyId);
        InKindTotalInfo totalInfo = new InKindTotalInfo();
        totalInfo.setTotal(1);
        when(inkindDao.getInKindTotalInfoBySelectCenters(type, agencyId, TimeUtil.parseDate(startDate), TimeUtil.parseDate(endDate), false, Arrays.asList("C0001"), Arrays.asList("G0001"), false, false, Arrays.asList("C0001"), userEntity, false)).thenReturn(totalInfo);

        when(inkindDao.getDonateInkindByPageAndSelectCenters(type, agencyId, TimeUtil.parseDate(startDate), TimeUtil.parseDate(endDate), "donorName", "ASC", "0", "10", false, Arrays.asList("C0001"), Arrays.asList("G0001"), false, false, false, Arrays.asList("C0001"), userEntity, false)).thenReturn(inKindDonateList);
        when(fileSystem.getPublicUrl(any(), any())).thenReturn("http://test.com");
        // when(userDao.getAgencyByAgencyAdminId(userId)).thenReturn(agencyModelList);
        // when(inkindDao.countDonateInkind(type, null, TimeUtil.parseDate(startDate), TimeUtil.parseDate(endDate), false, Arrays.asList("G0001"), Arrays.asList("C0001"), false, false, Arrays.asList("C0001"))).thenReturn(inKindDonateList.size());
        ReflectionTestUtils.setField(inKindExportService, "pdfBucket", "pdfBucket");
        resourceUtilStatic.when(() -> ResourceUtil.getResourceAsString(anyString())).thenReturn("html");
        fileUtilStatic.when(() -> FileUtil.randomTempFilePath(anyString())).thenReturn("test.html");

        SuccessResponse response = inKindExportService.downloadDonateInKindPDF(request);
        assertNotNull(response);
    }

    /**
     * 测试报告数量为 0 的情况
     */
    @Test
    void testDownloadNoDataInKindPDF() {
        // 数据准备
        InkindDonateListRequest request = new InkindDonateListRequest();
        request.setCurrentPage(1);
        request.setPageSize(10);
        request.setStartTime("2023-01-01");
        request.setEndTime("2024-01-01");
        request.setType("AT_HOME");
        request.setColumn("donorName");
        request.setOrder("ASC");
        request.setCurrentPage(1);
        request.setPageSize(10);
        request.setCommunity(false);
        request.setAllGroup(false);
        request.setPrecision(false);
        request.setAllCenter(false);
        request.setSelectCenters(Arrays.asList("C0001"));
        request.setSelectGroups(Arrays.asList("G0001"));
        request.setSelectAllGroupByCenterIds(Arrays.asList("C0001"));

        String userId = "U0001";
        String type = "AT_HOME";
        String agencyId = "A0001";
        String startDate = "2023-01-01";
        String endDate = "2024-01-01";
        String donateId = "D0001";
        String role = "AGENCY_ADMIN";
        String language = "en";
        AgencyModel agency = new AgencyModel();

        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole(role);
        userEntity.setUserName("test User");

        List<InKindDonate> inKindDonateList = new ArrayList<>();
        InKindDonate inkindDonate = new InKindDonate();
        inkindDonate.setId(donateId);
        inkindDonate.setAgencyId(agencyId);
        inkindDonate.setAgencyName("test Agency");
        inkindDonate.setCenterId("C0001");
        inkindDonate.setCenterName("test Center");
        inkindDonate.setGroupId("G0001");
        inkindDonate.setGroupName("test Group");
        inkindDonate.setDonorRelationship(role);
        inkindDonate.setApproveUserId(userId);
        inkindDonate.setApproveUserRelationship(role);
        inkindDonate.setReportType("NEW");
        inkindDonate.setUnit("MINUTE");
        inkindDonate.setRateUnit("MINUTE");
        inKindDonateList.add(inkindDonate);

        List<InKindReportApprove> ratifyReportList = new ArrayList<>();
        InKindReportApprove ratifyReport = new InKindReportApprove();
        ratifyReport.setId(donateId);
        ratifyReport.setSignatureId("S0001");
        ratifyReportList.add(ratifyReport);

        List<MediaEntity> medias = new ArrayList<>();
        MediaEntity mediaModel = new MediaEntity();
        mediaModel.setId("S0001");
        mediaModel.setPrivateFile(false);
        mediaModel.setRelativePath("test");
        medias.add(mediaModel);

        List<InKindReportApprove> ratifyKindReportApproveList = new ArrayList<>();
        InKindReportApprove inKindReportApprove = new InKindReportApprove();
        inKindReportApprove.setId(donateId);
        inKindReportApprove.setReportId("D0001");
        inKindReportApprove.setUserId("U0001");
        inKindReportApprove.setSignatureId("S0001");
        ratifyKindReportApproveList.add(inKindReportApprove);

        InKindDonate donate = new InKindDonate();
        donate.setId(donateId);

        ReflectionTestUtils.setField(inKindExportService, "endPoint", "e");
        ReflectionTestUtils.setField(inKindExportService, "pdfEndpoint", "p");
        List<AgencyModel> agencyModelList = new ArrayList<>();
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        agencyModel.setName("test Agency");
        agencyModelList.add(agencyModel);

        // 数据准备 -- 接口模拟

        when(userProvider.getCurrentLang()).thenReturn(language);
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.checkUser(userId)).thenReturn(userEntity);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        when(inKindProvider.getAgencyId()).thenReturn(agencyId);
        InKindTotalInfo totalInfo = new InKindTotalInfo();
        when(inkindDao.getInKindTotalInfoBySelectCenters(type, agencyId, TimeUtil.parseDate(startDate), TimeUtil.parseDate(endDate), false, Arrays.asList("C0001"), Arrays.asList("G0001"), false, false, Arrays.asList("C0001"), userEntity, false)).thenReturn(totalInfo);
        // when(userDao.getAgencyByAgencyAdminId(userId)).thenReturn(agencyModelList);

        // when(inkindDao.countDonateInkind(type, null, TimeUtil.parseDate(startDate), TimeUtil.parseDate(endDate), false, Arrays.asList("G0001"), Arrays.asList("C0001"), false, false, Arrays.asList("C0001"))).thenReturn(0);

        ReflectionTestUtils.setField(inKindExportService, "pdfBucket", "pdfBucket");
        resourceUtilStatic.when(() -> ResourceUtil.getResourceAsString(anyString())).thenReturn("html");
        fileUtilStatic.when(() -> FileUtil.randomTempFilePath(anyString())).thenReturn("test.html");

        Assert.assertThrows(BusinessException.class, () -> inKindExportService.downloadDonateInKindPDF(request));
    }

    /**
     * 测试报告数量大于 3000 的情况
     */
    @Test
    void testDownloadExceedDataInKindPDF() {
        // 数据准备
        InkindDonateListRequest request = new InkindDonateListRequest();
        request.setCurrentPage(1);
        request.setPageSize(10);
        request.setStartTime("2023-01-01");
        request.setEndTime("2024-01-01");
        request.setType("AT_HOME");
        request.setColumn("donorName");
        request.setOrder("ASC");
        request.setCurrentPage(1);
        request.setPageSize(10);
        request.setCommunity(false);
        request.setAllGroup(false);
        request.setPrecision(false);
        request.setAllCenter(false);
        request.setSelectCenters(Arrays.asList("C0001"));
        request.setSelectGroups(Arrays.asList("G0001"));
        request.setSelectAllGroupByCenterIds(Arrays.asList("C0001"));

        String userId = "U0001";
        String type = "AT_HOME";
        String agencyId = "A0001";
        String startDate = "2023-01-01";
        String endDate = "2024-01-01";
        String donateId = "D0001";
        String role = "AGENCY_ADMIN";
        String language = "en";
        AgencyModel agency = new AgencyModel();

        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole(role);
        userEntity.setUserName("test User");

        List<InKindDonate> inKindDonateList = new ArrayList<>();
        InKindDonate inkindDonate = new InKindDonate();
        inkindDonate.setId(donateId);
        inkindDonate.setAgencyId(agencyId);
        inkindDonate.setAgencyName("test Agency");
        inkindDonate.setCenterId("C0001");
        inkindDonate.setCenterName("test Center");
        inkindDonate.setGroupId("G0001");
        inkindDonate.setGroupName("test Group");
        inkindDonate.setDonorRelationship(role);
        inkindDonate.setApproveUserId(userId);
        inkindDonate.setApproveUserRelationship(role);
        inkindDonate.setReportType("NEW");
        inkindDonate.setUnit("MINUTE");
        inkindDonate.setRateUnit("MINUTE");
        inKindDonateList.add(inkindDonate);

        List<InKindReportApprove> ratifyReportList = new ArrayList<>();
        InKindReportApprove ratifyReport = new InKindReportApprove();
        ratifyReport.setId(donateId);
        ratifyReport.setSignatureId("S0001");
        ratifyReportList.add(ratifyReport);

        List<MediaEntity> medias = new ArrayList<>();
        MediaEntity mediaModel = new MediaEntity();
        mediaModel.setId("S0001");
        mediaModel.setPrivateFile(false);
        mediaModel.setRelativePath("test");
        medias.add(mediaModel);

        List<InKindReportApprove> ratifyKindReportApproveList = new ArrayList<>();
        InKindReportApprove inKindReportApprove = new InKindReportApprove();
        inKindReportApprove.setId(donateId);
        inKindReportApprove.setReportId("D0001");
        inKindReportApprove.setUserId("U0001");
        inKindReportApprove.setSignatureId("S0001");
        ratifyKindReportApproveList.add(inKindReportApprove);

        InKindDonate donate = new InKindDonate();
        donate.setId(donateId);

        ReflectionTestUtils.setField(inKindExportService, "endPoint", "e");
        ReflectionTestUtils.setField(inKindExportService, "pdfEndpoint", "p");

        List<AgencyModel> agencyModelList = new ArrayList<>();
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        agencyModel.setName("test Agency");
        agencyModelList.add(agencyModel);

        // 数据准备 -- 接口模拟

        when(userProvider.getCurrentLang()).thenReturn(language);
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.checkUser(userId)).thenReturn(userEntity);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        when(inKindProvider.getAgencyId()).thenReturn(agencyId);

        // when(inkindDao.countDonateInkind(type, null, TimeUtil.parseDate(startDate), TimeUtil.parseDate(endDate), false, Arrays.asList("G0001"), Arrays.asList("C0001"), false, false, Arrays.asList("C0001"))).thenReturn(3001);
        InKindTotalInfo totalInfo = new InKindTotalInfo();
        totalInfo.setTotal(3001);
        when(inkindDao.getInKindTotalInfoBySelectCenters(type, agencyId, TimeUtil.parseDate(startDate), TimeUtil.parseDate(endDate), false, Arrays.asList("C0001"), Arrays.asList("G0001"), false, false, Arrays.asList("C0001"), userEntity, false)).thenReturn(totalInfo);
        // when(userDao.getAgencyByAgencyAdminId(userId)).thenReturn(agencyModelList);
        ReflectionTestUtils.setField(inKindExportService, "pdfBucket", "pdfBucket");
        resourceUtilStatic.when(() -> ResourceUtil.getResourceAsString(anyString())).thenReturn("html");
        fileUtilStatic.when(() -> FileUtil.randomTempFilePath(anyString())).thenReturn("test.html");

        Assert.assertThrows(BusinessException.class, () -> inKindExportService.downloadDonateInKindPDF(request));
    }

    /**
     * 测试生成学生时间段内的pdf
     */
    @Test
    void testGenerateEnrollmentPdf() throws IOException {
        String enrollmentId = "E0001";
        String groupId = "G0001";
        String parentId = "P0001";
        String userId = "U0001";
        String agencyId = "A0001";
        String role = "AGENCY_ADMIN";

        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole(role);

        InkindExportRequest request = new InkindExportRequest();
        request.setEnrollmentId(enrollmentId);
        request.setFromDate("2023-7-1");
        request.setToDate("2023-08-1");

        List<UserModel> staffAll = new ArrayList<>();
        UserModel userModel = new UserModel();
        userModel.setId("U0001");
        userModel.setDisplayName("user1");
        userModel.setAvatarMediaUrl("test");
        UserModel userModel2 = new UserModel();
        userModel2.setId("U0002");
        userModel2.setAvatarMediaUrl("");
        userModel2.setDisplayName("user2");
        staffAll.add(userModel);
        staffAll.add(userModel2);

        GroupEntity group = new GroupEntity();
        group.setId(groupId);
        EnrollmentEntity enrollment = new EnrollmentEntity();
        enrollment.setId(enrollmentId);
        enrollment.setGroup(group);
        enrollment.setDisplayName("enrollment1");
        com.learninggenie.common.data.model.GroupEntity groupWithCenter = new com.learninggenie.common.data.model.GroupEntity();
        groupWithCenter.setId(groupId);
        groupWithCenter.setName("group1");
        com.learninggenie.common.data.model.CenterEntity centerEntity = new com.learninggenie.common.data.model.CenterEntity();
        centerEntity.setName("center1");
        groupWithCenter.setCenter(centerEntity);
        List<UserModel> parents = new ArrayList<>();

        UserEntity parentEntity = new UserEntity();
        parentEntity.setId(parentId);
        parentEntity.setEnrollments(Collections.singleton(enrollment));
        UserModel parent = new UserModel();
        parent.setId(parentId);
        parent.setFirstName("parent1");
        parent.setLastName("parent1");
        parent.setDisplayName("parent1");
        parents.add(parent);

        List<InkindReportEntity> reports = new ArrayList<>();
        InkindReportEntity reportEntity1 = new InkindReportEntity();
        reportEntity1.setId("I0001");
        reportEntity1.setActivityTypeId("A0004");
        reportEntity1.setActivityDate(TimeUtil.parse("2023-01-01", TimeUtil.format10));
        reportEntity1.setApproveUserId("U0001");
        reportEntity1.setType("IN_SYSTEM");
        reportEntity1.setCreateAtUtc(TimeUtil.parse("2023-01-01", TimeUtil.format10));
        reportEntity1.setUpdateAtUtc(TimeUtil.parse("2023-01-01", TimeUtil.format10));
        reportEntity1.setEnrollmentId(enrollmentId);
        reportEntity1.setParentId(parentId);
        reportEntity1.setCustom(false);
        reportEntity1.setDraft(false);
        reportEntity1.setUnit(InKindUnit.MILE.toString());
        reportEntity1.setRateUnit(InKindUnit.MILE.toString());
        reportEntity1.setRateValue(BigDecimal.valueOf(1.234));
        reportEntity1.setValue(BigDecimal.valueOf(11.00));
        reportEntity1.setApproveSignatureId("S0002");
        reports.add(reportEntity1);
        InkindReportEntity reportEntity2 = new InkindReportEntity();
        reportEntity2.setId("I0002");
        reportEntity2.setActivityTypeId("a0002");
        reportEntity2.setActivityDate(TimeUtil.parse("2023-01-01", TimeUtil.format10));
        reportEntity2.setApproveUserId("U0001");
        reportEntity2.setType("IN_SYSTEM");
        reportEntity2.setCreateAtUtc(TimeUtil.parse("2023-01-01", TimeUtil.format10));
        reportEntity2.setUpdateAtUtc(TimeUtil.parse("2023-01-01", TimeUtil.format10));
        reportEntity2.setEnrollmentId(enrollmentId);
        reportEntity2.setParentId(parentId);
        reportEntity2.setCustom(false);
        reportEntity2.setDraft(false);
        reportEntity2.setUnit(InKindUnit.MILE.toString());
        reportEntity2.setRateUnit(InKindUnit.MILE.toString());
        reportEntity2.setRateValue(BigDecimal.valueOf(1.234));
        reportEntity2.setValue(BigDecimal.valueOf(11.00));
        reportEntity2.setApproveSignatureId("S0002");
        reports.add(reportEntity2);

        List<InkindActivityTypeEntity> activityTypes = new ArrayList<>();
        InkindActivityTypeEntity activityTypeEntity1 = new InkindActivityTypeEntity();
        activityTypeEntity1.setId("A0004");
        activityTypeEntity1.setType("AT_HOME");
        activityTypes.add(activityTypeEntity1);
        InkindActivityTypeEntity activityTypeEntity2 = new InkindActivityTypeEntity();
        activityTypeEntity2.setId("A0002");
        activityTypeEntity2.setType("VOLUNTEER");
        activityTypes.add(activityTypeEntity2);

        List<MediaEntity> medias = new ArrayList<>();
        MediaEntity mediaModel = new MediaEntity();
        mediaModel.setId("S0001");
        mediaModel.setPrivateFile(false);
        mediaModel.setRelativePath("test");
        medias.add(mediaModel);

        List<InKindReportApprove> reportApproves2 = new ArrayList<>();
        InKindReportApprove reportApprove1 = new InKindReportApprove();
        reportApprove1.setUserId("U0002");
        reportApprove1.setReportId("I0001");
        reportApprove1.setCreateAtUtc(TimeUtil.parse("2023-01-01", TimeUtil.format10));
        reportApprove1.setSignatureId("S0001");
        reportApproves2.add(reportApprove1);

        // 注入属性
        ReflectionTestUtils.setField(inKindExportService, "mediaServer", "media.server");
        ReflectionTestUtils.setField(inKindExportService, "endPoint", "e");
        ReflectionTestUtils.setField(inKindExportService, "pdfEndpoint", "p");

        // 调用 getAgencyId() 的 mock 方法
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        // when(userProvider.checkUser(userId)).thenReturn(userEntity);
        // when(userDao.getAgencyByAgencyAdminId(userId)).thenReturn(Arrays.asList(agencyModel));
        when(inKindProvider.getAgencyId(userId)).thenReturn(agencyId);

        // 接口模拟
        when(studentDao.getChildWithGroupCenter(enrollmentId)).thenReturn(enrollment);
        when(groupDao.getGroupWithCenter(groupId)).thenReturn(groupWithCenter);
        when(inkindDao.getReport(any(), any(), any(), any(), any())).thenReturn(reports);
        when(inkindDao.getActivityTypeByAgencyId(agencyId)).thenReturn(activityTypes);
        when(mediaDao.getMedias(any())).thenReturn(medias);
        when(userDao.getUsersByUserIds(any())).thenReturn(parents);
        when(inKindReportApproveDao.getRatifySignatureReportApproveList(any())).thenReturn(reportApproves2);
        when(userDao.getUsersByUserIdsWithDeleted(anyList())).thenReturn(staffAll);
        Map<String, UserEntity> parentsMap = new HashMap<>();
        String key = parentEntity.getId().toLowerCase();
        if (parentEntity.getEnrollments() != null && !parentEntity.getEnrollments().isEmpty()) {
            key = parentEntity.getId().toLowerCase() + parentEntity.getEnrollments().stream().map(EnrollmentEntity::getId).map(String::toLowerCase).collect(Collectors.joining(""));
        }
        parentsMap.put(key, parentEntity);
        when(inKindProvider.getParentMap(Arrays.asList(enrollmentId))).thenReturn(parentsMap);
        // when(userDao.getAllParentsByStudentIds(anyString())).thenReturn(Collections.singletonList(parentEntity));

        resourceUtilStatic.when(() -> ResourceUtil.createHtmlFile(any(), anyString(), anyString(), any())).thenReturn("html");

        when(fileSystem.getPublicUrl(anyString())).thenReturn("test");
        when(fileSystem.upload(any(), anyString(), any())).thenReturn(UUID.randomUUID().toString());
        when(fileSystem.getPublicUrl(any(), anyString())).thenReturn("test");

        // 调用方法
        MapModel response = inKindExportService.generateEnrollmentPdf(request, userId);

        assertNotNull(response);
        verify(remoteProvider, times(1)).callPdfService(any());
    }


    /**
     * 测试下载活动Excel
     *
     * @throws IOException
     */
    @Test
    void testDownloadDonateInkindExcel() throws IOException {
        // 准备数据
        String userId = "U0001";
        String language = "en";
        String type = "type";
        String startTime = "2020-01-01";
        String endTime = "2020-01-01";
        String column = "name";
        String order = "asc";
        int currentPage = 1;
        int pageSize = 10;
        boolean community = true;
        List<String> selectGroups = new ArrayList<>();
        selectGroups.add("G0001");
        boolean isHighPrecision = true;
        List<String> selectCenters = new ArrayList<>();
        boolean isAllCenter = true;
        boolean isAllGroup = true;
        List<String> centerIds = new ArrayList<>();

        UserEntity user = new UserEntity();
        user.setId("U0001");
        user.setRole("AGENCY_ADMIN");

        List<AgencyModel> agencyList = new ArrayList<>();
        AgencyModel agency = new AgencyModel();
        agency.setId("A0001");
        agencyList.add(agency);

        List<InKindDonate> inKindDonateList = new ArrayList<>();
        InKindDonate inKindDonate = new InKindDonate();
        inKindDonate.setType("AT_HOME");
        inKindDonate.setUnit("MINUTE");
        inKindDonate.setRateUnit("HOUR");
        inKindDonate.setValue(new BigDecimal("1"));
        inKindDonate.setMoney(new BigDecimal("1"));
        inKindDonate.setApproveUserId("U0009");
        inKindDonate.setDonorType("ENTITY");
        inKindDonateList.add(inKindDonate);
        InKindDonate inKindDonate2 = new InKindDonate();
        inKindDonate2.setType("VOLUNTEER");
        inKindDonate2.setUnit("MINUTE");
        inKindDonate2.setRateUnit("HOUR");
        inKindDonate2.setValue(new BigDecimal("1"));
        inKindDonate2.setMoney(new BigDecimal("1"));
        inKindDonate2.setApproveUserId("U0009");
        inKindDonate2.setDonorType("ENTITY");
        inKindDonateList.add(inKindDonate2);
        InKindDonate inKindDonate3 = new InKindDonate();
        inKindDonate3.setType("MILEAGE");
        inKindDonate3.setUnit("MINUTE");
        inKindDonate3.setRateUnit("HOUR");
        inKindDonate3.setValue(new BigDecimal("1"));
        inKindDonate3.setMoney(new BigDecimal("1"));
        inKindDonate3.setApproveUserId("U0009");
        inKindDonate3.setDonorType("ENTITY");
        inKindDonateList.add(inKindDonate3);
        InKindDonate inKindDonate4 = new InKindDonate();
        inKindDonate4.setType("DONATION");
        inKindDonate4.setUnit("MINUTE");
        inKindDonate4.setRateUnit("HOUR");
        inKindDonate4.setValue(new BigDecimal("1"));
        inKindDonate4.setMoney(new BigDecimal("1"));
        inKindDonate4.setApproveUserId("U0009");
        inKindDonate4.setDonorType("ENTITY");
        inKindDonateList.add(inKindDonate4);

        List<MediaEntity> medias = new ArrayList<>();
        MediaEntity media = new MediaEntity();
        media.setId("S0001");
        media.setPrivateFile(true);
        medias.add(media);

        List<InKindReportApprove> reportApproveList = new ArrayList<>();
        InKindReportApprove inKindReportApprove = new InKindReportApprove();
        inKindReportApprove.setUserId("U0001");
        inKindReportApprove.setSignatureId("S0001");
        inKindReportApprove.setReportId("R0001");
        reportApproveList.add(inKindReportApprove);

        List<UserModel> staffAll = new ArrayList<>();
        UserModel userModel = new UserModel();
        userModel.setId("U0009");
        staffAll.add(userModel);


        ReflectionTestUtils.setField(inKindExportService, "s3BucketName", "s3");
        ReflectionTestUtils.setField(inKindExportService, "env", mock(Environment.class));
        ReflectionTestUtils.setField(inKindExportService, "endPoint", "ep");
        fileUploadUtilStatic.when(() -> FileUploadUtil.upload(any(), any(), anyString(), any())).thenReturn("html");

        // 模拟接口
        // 获取当前用户 Id
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        // 检查当前用户
        when(userProvider.checkUser(userId)).thenReturn(user);
        // 获取机构
        // when(userDao.getAgencyByAgencyAdminId(userId)).thenReturn(agencyList);
        when(inKindProvider.getAgencyId()).thenReturn("A0001");
        // 分页查询报告
        when(inkindDao.getDonateInkindByPageAndSelectCenters(type, "A0001", TimeUtil.parseDate(startTime), TimeUtil.parseDate(endTime), "name", "asc", "0", "10", true, selectCenters, Arrays.asList("G0001"), false, isAllCenter, isAllGroup, centerIds, user, false)).thenReturn(inKindDonateList);
        // 获取事件类型
        when(userProvider.getTimezoneOffsetNum()).thenReturn(1);
        // 获取媒体
        when(inkindDao.getMediaByIds(any())).thenReturn(medias);
        // 获取签名报告
        when(inKindReportApproveDao.getRatifySignatureReportApproveList(any())).thenReturn(reportApproveList);
        // 获取审批员工
        when(userDao.getUsersByUserIdsWithDeleted(any())).thenReturn(staffAll);

        inKindExportService.downloadDonateInKindExcel(userId, language, type, startTime, endTime, column, order, currentPage, pageSize, community, selectGroups, isHighPrecision, selectCenters, isAllCenter, isAllGroup, centerIds, false);

        // 验证结果
        fileUploadUtilStatic.verify(() -> FileUploadUtil.upload(any(), any(), anyString(), any()), times(1));
    }


    @Disabled
    @Test
    public void getStatisticsSlidesImage_Success() {

        // Mock data
        String agencyId = "AgencyId"; // 机构ID
        String activityStartDateStr = "2024-01-01"; // 活动开始日期字符串
        String activityEndDateStr = "2024-01-31"; // 活动结束日期字符串
        String userId = "U0001"; // 用户ID

        AgencyModel agencyModel = new AgencyModel(); // 机构模型
        agencyModel.setId(agencyId); // 设置机构ID
        agencyModel.setName("test Agency"); // 设置机构名称

        // 数据准备 -- 方法参数
        String enrollmentId = "E0001"; // 报名ID
        String enrollmentName = "test child"; // 报名名称
        String groupId = "G0001"; // 群组ID
        String date = "2023-01-01"; // 日期
        String role = "AGENCY_ADMIN"; // 角色

        Date date1 = TimeUtil.parse("2023-01-01", TimeUtil.format10); // 解析日期1
        Date date2 = TimeUtil.parse("2020-01-01", TimeUtil.format10); // 解析日期2
        List<InkindSchoolYearEntity> schoolYears = new ArrayList<>(); // 学年列表
        InkindSchoolYearEntity schoolYearEntity = new InkindSchoolYearEntity(); // 学年实体
        schoolYearEntity.setStartDate(date1); // 设置开始日期
        schoolYearEntity.setId("SchoolYearId"); // 设置学年 ID
        InkindSchoolYearEntity historySchoolYear = new InkindSchoolYearEntity(); // 历史学年实体
        historySchoolYear.setStartDate(date2); // 设置开始日期
        schoolYears.add(schoolYearEntity); // 添加学年实体
        schoolYears.add(historySchoolYear); // 添加历史学年实体

        UserEntity userEntity = new UserEntity(); // 用户实体
        userEntity.setId(userId); // 设置用户ID
        userEntity.setRole(role); // 设置用户角色
        AuthUserDetails authUserDetails = new AuthUserDetails(); // 认证用户细节
        authUserDetails.setAgencyId(agencyId); // 设置机构ID
        authUserDetails.setRole(role); // 设置用户角色

        List<EnrollmentEntity> children = new ArrayList<>(); // 孩子列表
        EnrollmentEntity enrollment = new EnrollmentEntity(); // 报名实体
        enrollment.setId(enrollmentId); // 设置报名ID
        enrollment.setDisplayName(enrollmentName); // 设置报名显示名称
        GroupEntity groupEntity = new GroupEntity(); // 群组实体
        groupEntity.setId(groupId); // 设置群组ID
        enrollment.setGroup(groupEntity); // 设置报名所属群组
        children.add(enrollment); // 添加报名实体到孩子列表
        // 一共五种角色, 审批的报告也对应五种角色
        List<ReportReviewModel> reviewModels = new ArrayList<>(); // 审批模型列表
        ReportReviewModel reviewModel1 = new ReportReviewModel(); // 审批模型1
        reviewModel1.setActivityTypeId("A0001"); // 设置活动类型ID
        reviewModel1.setApproveRole("AGENCY_ADMIN"); // 设置批准角色
        // 当前角色的groupId
        reviewModel1.setGroupId(groupId); // 设置群组ID
        reviewModels.add(reviewModel1); // 添加审批模型1到审批模型列表
        // 类似地设置审批模型2至审批模型5
        ReportReviewModel reviewModel2 = new ReportReviewModel();
        reviewModel2.setActivityTypeId("A0002");
        reviewModel2.setApproveRole(ApproveRole.ADMINS.toString());
        reviewModel2.setGroupId("S0001");
        reviewModels.add(reviewModel2);
        ReportReviewModel reviewModel3 = new ReportReviewModel();
        reviewModel3.setActivityTypeId("A0003");
        reviewModel3.setApproveRole(ApproveRole.TEACHER_ASSISTANTS.toString());
        reviewModel3.setGroupId("C0001");
        reviewModels.add(reviewModel3);
        ReportReviewModel reviewModel4 = new ReportReviewModel();
        reviewModel4.setActivityTypeId("A0004");
        reviewModel4.setApproveRole(ApproveRole.TEACHER_ASSISTANTS.toString());
        String groupId1 = "T0001";
        reviewModel4.setGroupId(groupId1);
        reviewModels.add(reviewModel4);
        ReportReviewModel reviewModel5 = new ReportReviewModel();
        reviewModel5.setActivityTypeId("A0005");
        reviewModel5.setApproveRole(ApproveRole.FAMILY_SERVICE_STAFF.toString());
        reviewModel5.setGroupId("F0001");
        reviewModels.add(reviewModel5);

        // 小孩待审核报告数据
        List<InkindReportEntity> reportEntities = new ArrayList<>();
        // 定义 inkind 报告
        InkindReportEntity reportEntity1 = new InkindReportEntity();
        reportEntity1.setId("R0001");
        reportEntity1.setEnrollmentId(enrollmentId);
        reportEntity1.setActivityTypeId("A0001");
        reportEntity1.setGroupId(groupId);
        reportEntity1.setCenterId("C0001");
        reportEntity1.setMoney(BigDecimal.valueOf(180.00));
        reportEntity1.setParentId("P0001");
        reportEntity1.setApproveStatus("APPROVED");
        reportEntity1.setActivityType(ActivityTypeValue.DONATION.toString());
        reportEntity1.setStatus(InKindStageEnum.EFFECTIVE.toString());
        reportEntity1.setUnit(InKindUnit.MINUTE.toString());
        reportEntity1.setRateUnit(InKindUnit.HOUR.toString());
        reportEntity1.setRateValue(BigDecimal.valueOf(14.190));
        reportEntity1.setValue(BigDecimal.valueOf(180.00));
        reportEntity1.setCustom(false);
        reportEntity1.setDraft(false);

        reportEntities.add(reportEntity1);
        InkindReportEntity reportEntity2 = new InkindReportEntity();
        reportEntity2.setId("R0002");
        reportEntity2.setEnrollmentId(enrollmentId);
        reportEntity2.setActivityTypeId("A0002");
        reportEntity2.setMoney(BigDecimal.valueOf(180.00));
        reportEntity2.setActivityType(ActivityTypeValue.MILEAGE.toString());
        reportEntity2.setStatus(InKindStageEnum.EFFECTIVE.toString());
        reportEntity2.setApproveStatus("APPROVED");
        reportEntity2.setGroupId(groupId);
        reportEntity2.setCenterId("C0001");
        reportEntity2.setParentId("P0002");
        reportEntity2.setUnit(InKindUnit.MILE.toString());
        reportEntity2.setRateUnit(InKindUnit.MILE.toString());
        reportEntity2.setRateValue(BigDecimal.valueOf(1.00));
        reportEntity2.setValue(BigDecimal.valueOf(10.00));
        reportEntity2.setCustom(false);
        reportEntity2.setDraft(false);
        reportEntities.add(reportEntity2);

        InkindReportEntity reportEntity3 = new InkindReportEntity();
        reportEntity3.setId("R0003");
        reportEntity3.setEnrollmentId(enrollmentId);
        reportEntity3.setActivityType(ActivityTypeValue.VOLUNTEER.toString());
        reportEntity3.setStatus(InKindStageEnum.EFFECTIVE.toString());
        reportEntity3.setActivityTypeId("A0003");
        reportEntity3.setGroupId(groupId1);
        reportEntity3.setCenterId("C0001");
        reportEntity3.setParentId("P0003");
        reportEntity3.setApproveStatus("APPROVED");
        reportEntity3.setMoney(BigDecimal.valueOf(180.00));
        reportEntity3.setUnit(InKindUnit.MILE.toString());
        reportEntity3.setRateUnit(InKindUnit.MILE.toString());
        reportEntity3.setRateValue(BigDecimal.valueOf(1.234));
        reportEntity3.setValue(BigDecimal.valueOf(11.00));
        reportEntity3.setCustom(false);
        reportEntity3.setDraft(false);
        reportEntities.add(reportEntity3);

        InkindReportEntity inkindReportEntity = new InkindReportEntity();
        inkindReportEntity.setId("D0001");
        inkindReportEntity.setActivityType(ActivityTypeValue.AT_HOME.toString());
        inkindReportEntity.setApproveStatus("APPROVED");
        inkindReportEntity.setStatus(InKindStageEnum.EFFECTIVE.toString());
        inkindReportEntity.setEnrollmentId("E0001");
        inkindReportEntity.setParentId("P0004");
        inkindReportEntity.setCenterId("C0001");
        inkindReportEntity.setGroupId(groupId1);
        inkindReportEntity.setMoney(new BigDecimal(100));
        inkindReportEntity.setUnit("HOUR");
        inkindReportEntity.setValue(new BigDecimal(10));
        inkindReportEntity.setRateValue(new BigDecimal(10));
        reportEntities.add(inkindReportEntity);

        List<InkindActivityTypeEntity> activityTypes = new ArrayList<>();
        InkindActivityTypeEntity activityTypeEntity1 = new InkindActivityTypeEntity();
        activityTypeEntity1.setId("A0001");
        activityTypeEntity1.setType("AT_HOME");
        activityTypes.add(activityTypeEntity1);
        InkindActivityTypeEntity activityTypeEntity2 = new InkindActivityTypeEntity();
        activityTypeEntity2.setId("A0002");
        activityTypeEntity2.setType("VOLUNTEER");
        activityTypes.add(activityTypeEntity2);

        // 学校班级的数据
        List<GroupWithCenter> groupWithCenterList = new ArrayList<>();
        GroupWithCenter groupWithCenter = new GroupWithCenter();
        groupWithCenter.setGroupId(groupId);
        groupWithCenter.setGroupName("G0001");
        groupWithCenter.setCenterId("C0001");
        groupWithCenter.setCenterName("C0001");
        groupWithCenterList.add(groupWithCenter);

        List<CenterModel> centerModelList = new ArrayList<>();
        CenterModel centerModel = new CenterModel();
        centerModel.setId("C0001");
        centerModel.setName("test center");
        centerModel.setGroupId(groupId);
        centerModel.setGroupName(groupId1);
        centerModel.setGroups(new ArrayList<GroupModel>() {{
            GroupModel groupModel = new GroupModel();
            groupModel.setId(groupId1);
            groupModel.setName(groupId1);
            groupModel.setChildren(new ArrayList<ChildModel>() {{
                ChildModel childModel = new ChildModel();
                childModel.setId("E0001");
                childModel.setLastName("LastName");
                childModel.setFirstName("FirstName");
                add(childModel);

                ChildModel childModel2 = new ChildModel();
                childModel2.setId("E0002");
                childModel2.setLastName("LastName");
                childModel2.setFirstName("FirstName");
                add(childModel2);
            }});
            add(groupModel);
        }});

        CenterModel centerModel2 = new CenterModel();
        centerModel2.setId("C0001");
        centerModel2.setName("test center");
        centerModel2.setGroupId(groupId1);
        centerModel2.setGroupName(groupId);
        centerModel2.setGroups(new ArrayList<GroupModel>() {{
            GroupModel groupModel = new GroupModel();
            groupModel.setId(groupId1);
            groupModel.setName(groupId1);
            groupModel.setChildren(new ArrayList<ChildModel>() {{
                ChildModel childModel = new ChildModel();
                childModel.setId("E0001");
                childModel.setLastName("LastName");
                childModel.setFirstName("FirstName");
                add(childModel);

                ChildModel childModel2 = new ChildModel();
                childModel2.setId("E0002");
                childModel2.setLastName("LastName");
                childModel2.setFirstName("FirstName");
                add(childModel2);
            }});
            add(groupModel);
        }});
        centerModelList.add(centerModel2);
        centerModelList.add(centerModel);

        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setName("test"); // 设置名称
        centerEntity.setId("COOO1"); // 设置ID

        // 缓存锁的数据
        String result = "OK";
        ArrayList<InKindGroupSetting> inKindGroupSettings = new ArrayList<>();
        InKindGroupSetting groupSetting = new InKindGroupSetting();
        groupSetting.setGroupId(groupId);
        groupSetting.setActivityGroupId("ActivityGroupId");
        groupSetting.setActivityGroupName("ActivityGroupName");
        groupSetting.setActivityTypeId("ActivityTypeId");
        groupSetting.setType("Type");
        groupSetting.setSchoolYearId("SchoolYearId");
        groupSetting.setSchoolYear("SchoolYear");

        inKindGroupSettings.add(groupSetting);

        List<EnrollmentModel> enrollmentModels = new ArrayList<>();
        EnrollmentModel enrollmentModel = new EnrollmentModel();
        enrollmentModel.setId("E0001");
        enrollmentModel.setLastName("LastName");
        enrollmentModel.setFirstName("FirstName");
        enrollmentModels.add(enrollmentModel);
        EnrollmentModel enrollmentModel2 = new EnrollmentModel();
        enrollmentModel2.setId("E0002");
        enrollmentModel2.setLastName("LastName");
        enrollmentModel2.setFirstName("FirstName");
        enrollmentModels.add(enrollmentModel2);

        // 定义生成图片的参数
        ArrayList<String> args = new ArrayList<>();
        args.add("--crop-h");
        args.add("2048");
        args.add("--crop-w");
        args.add("2048");
        args.add("--crop-x");
        args.add("0");
        args.add("--crop-y");
        args.add("0");
        args.add("--width");
        args.add("1200");
        // 通用 mock 方法
        agencyModel.setId(agencyId);
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userDao.getAgencyByAgencyAdminId(userEntity.getId())).thenReturn(Arrays.asList(agencyModel));
        // 接口模拟
        when(userProvider.checkUser(anyString())).thenReturn(userEntity);
        when(inkindDao.getSchoolYearByAgencyId(anyString())).thenReturn(schoolYears);
        // Mocking dependencies
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(centerDao.getAllCenterAndGroupsByAgencyUserId(anyString())).thenReturn(centerModelList);
        when(userProvider.getAgencyOpenDefaultClose(agencyId, AgencyMetaKey.INKIND_OPEN.toString())).thenReturn(true);
        when(userDao.getAgencyOwnerByAgencyId(anyString())).thenReturn(createMockUserList());
        when(agencyDao.getById(anyString())).thenReturn(createMockAgencyEntity());
        when(centerDao.getTrainingCenters(anyString())).thenReturn(createMockCenterList());
        when(mediaService.convertHtmlToImage(null, args)).thenReturn("mockImageUrl");
        when(inkindDao.getGroupSetting(anyList(), anyString())).thenReturn(inKindGroupSettings);
        when(inkindDao.getByGroupIdsAndDates(Arrays.asList(groupId), activityStartDateStr, activityEndDateStr, InKindStageEnum.EFFECTIVE)).thenReturn(reportEntities);
        ReflectionTestUtils.setField(inKindExportService, "s3BucketName", "");
        ReflectionTestUtils.setField(inKindExportService, "s3Root", "");
        // 查询小孩
        when(studentDao.getAllChildrenByIds(anyList())).thenReturn(enrollmentModels);
        // Call the method
        List<ReplaceRelation> relationList = inKindExportService.getStatisticsSlidesImage(agencyId, activityStartDateStr, activityEndDateStr);

        // Assertions
        Assertions.assertNotNull(relationList);
        Assertions.assertFalse(relationList.isEmpty());
        // Add more assertions as needed
    }

    // Mock data creation methods
    private List<UserModel> createMockUserList() {
        // Create and return mock user list
        UserModel userModel = new UserModel();
        userModel.setHealthStatsView(false);
        userModel.setHealthCheckOpen(false);
        userModel.setCheckStatus(false);
        userModel.setHideChildrenStatus(false);
        userModel.setHideIEPStatus(false);
        userModel.setGroupId("GroupId");
        userModel.setCenterId("CenterId");
        userModel.setId("Id");
        userModel.setEmail("Email");
        userModel.setRole(UserRole.AGENCY_ADMIN.toString());
        userModel.setFirstName("FirstName");
        userModel.setLastName("LastName");
        userModel.setDisplayName("DisplayName");
        userModel.setChildDisplayName("ChildDisplayName");

        return Collections.singletonList(userModel);
    }

    private AgencyEntity createMockAgencyEntity() {
        // Create and return mock agency entity
        AgencyEntity agencyEntity = new AgencyEntity();
        agencyEntity.setCenterId("CenterId");
        agencyEntity.setCenterModels(Lists.newArrayList());
        agencyEntity.setId("Id");
        agencyEntity.setName("Name");
        agencyEntity.setCreateAtUtc(new Date());
        agencyEntity.setUpdateAtUtc(new Date());
        agencyEntity.setLogoMediaId("LogoMediaId");
        agencyEntity.setIsDeleted(false);
        agencyEntity.setHidden(false);
        agencyEntity.setUsers(Sets.newHashSet());
        agencyEntity.setDeleted(false);
        agencyEntity.setState("State");
        agencyEntity.setEnrollmentId("EnrollmentId");
        agencyEntity.setPartitionKey(0);

        return agencyEntity;
    }

    private List<CenterEntity> createMockCenterList() {
        // Create and return mock center list
        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setId("Id");
        centerEntity.setCenterTimeZone("CenterTimeZone");
        centerEntity.setCreateAtUtc(new Date());
        centerEntity.setDeleted(false);
        centerEntity.setName("Name");
        centerEntity.setSendReportTime(new Date());
        centerEntity.setUser(new UserEntity());
        centerEntity.setPaymentPlan(new CenterPaymentPlanEntity());
        centerEntity.setIsDeleted(false);
        centerEntity.setGroups(Sets.newHashSet());
        centerEntity.setSubscribeRecords(Sets.newHashSet());
        centerEntity.setCenterMetaDataEntities(Sets.newHashSet());
        centerEntity.setLogoMedia(new MediaEntity());
        centerEntity.setAgencies(Sets.newHashSet());
        centerEntity.setUsers(Sets.newHashSet());
        centerEntity.setMetaValue("MetaValue");
        centerEntity.setTraining(false);
        centerEntity.setTrainingCenter(false);

        return Collections.singletonList(centerEntity);
    }

    private GetStatisticCenterResponse createMockGetStatisticCenterResponse() {
        // Create and return mock GetStatisticCenterResponse object
        GetStatisticCenterResponse response = new GetStatisticCenterResponse();
        response.setAgencys(Collections.singletonList(new InKindStatisticAgencyModel()));
        return response;
    }

    private List<ReplaceRelation> createMockReplaceRelationList() {
        // Create and return mock ReplaceRelation list
        ReplaceRelation replaceRelation = new ReplaceRelation();
        replaceRelation.setReplaceType(ReplaceType.IMAGE);
        replaceRelation.setOldReplaceText("{{ Inkind Summary Report }}");
        replaceRelation.setImageUrl("mockImageUrl");
        return Collections.singletonList(replaceRelation);
    }


    @Test
    void testSetSignatureLinkIdAttachmentWithEmptyReports() {
        List<InkindReportEntity> reports = Collections.emptyList();

        inKindExportService.setSignatureLinkIdAttachment(reports);

        assertEquals(0, reports.size());
    }

    @Test
    void testSetSignatureLinkIdAttachmentWithNullParentSignatureId() {
        InkindReportEntity reportWithNullParentSignatureId = new InkindReportEntity();
        reportWithNullParentSignatureId.setParentSignatureId(null);
        List<InkindReportEntity> reports = Collections.singletonList(reportWithNullParentSignatureId);

        inKindExportService.setSignatureLinkIdAttachment(reports);

        assertEquals(1, reports.size());
        assertEquals(null, reports.get(0).getParentSignatureAbsoluteUrl());
    }

    @Test
    void testSetSignatureLinkIdAttachmentWithPrivateMediaEntity() {
        InkindReportEntity report = new InkindReportEntity();
        report.setParentSignatureId("123");
        report.setId("1");
        List<InkindReportEntity> reports = Collections.singletonList(report);

        MediaEntity mediaEntity = new MediaEntity();
        mediaEntity.setId("123");
        mediaEntity.setPrivateFile(true);
        mediaEntity.setRelativePath("/private/path");

        when(inkindDao.getMediaByIds(anyList())).thenReturn(Collections.singletonList(mediaEntity));
        when(fileSystem.getPrivateUrl("/private/path")).thenReturn("privateUrl");

        inKindExportService.setSignatureLinkIdAttachment(reports);

        assertEquals("privateUrl", report.getParentSignatureAbsoluteUrl());
    }

    @Test
    void testSetSignatureLinkIdAttachmentWithPublicMediaEntity() {
        InkindReportEntity report = new InkindReportEntity();
        report.setParentSignatureId("123");
        report.setId("1");
        List<InkindReportEntity> reports = Collections.singletonList(report);

        MediaEntity mediaEntity = new MediaEntity();
        mediaEntity.setId("123");
        mediaEntity.setPrivateFile(false);
        mediaEntity.setRelativePath("/public/path");

        when(inkindDao.getMediaByIds(anyList())).thenReturn(Collections.singletonList(mediaEntity));
        when(fileSystem.getPublicUrl("/public/path")).thenReturn("publicUrl");

        inKindExportService.setSignatureLinkIdAttachment(reports);

        assertEquals("publicUrl", report.getParentSignatureAbsoluteUrl());
    }

    @Test
    void testSetSignatureLinkIdAttachmentWithNonMatchingMediaEntity() {
        InkindReportEntity report = new InkindReportEntity();
        report.setParentSignatureId("123");
        report.setId("1");
        List<InkindReportEntity> reports = Collections.singletonList(report);

        MediaEntity mediaEntity = new MediaEntity();
        mediaEntity.setId("456");
        mediaEntity.setPrivateFile(true);
        mediaEntity.setRelativePath("/private/path");

        when(inkindDao.getMediaByIds(anyList())).thenReturn(Collections.singletonList(mediaEntity));

        inKindExportService.setSignatureLinkIdAttachment(reports);

        assertEquals(null, report.getParentSignatureAbsoluteUrl());
    }

    @Test
    void testSetSignatureLinkIdAttachmentWithMatchingLinkId() {
        InkindReportEntity report = new InkindReportEntity();
        report.setParentSignatureId("123");
        report.setId("1");
        List<InkindReportEntity> reports = Collections.singletonList(report);

        InkindReportAppendEntity appendEntity = new InkindReportAppendEntity();
        appendEntity.setId("1");
        appendEntity.setLinkId("link123");

        when(inkindDao.getInkindReportAppendByIds(anyList())).thenReturn(Collections.singletonList(appendEntity));

        inKindExportService.setSignatureLinkIdAttachment(reports);

        assertEquals("link123", report.getLinkId());
    }

    @Test
    void testSetSignatureLinkIdAttachmentWithNonMatchingLinkId() {
        InkindReportEntity report = new InkindReportEntity();
        report.setParentSignatureId("123");
        report.setId("1");
        List<InkindReportEntity> reports = Collections.singletonList(report);

        InkindReportAppendEntity appendEntity = new InkindReportAppendEntity();
        appendEntity.setId("2");
        appendEntity.setLinkId("link123");

        when(inkindDao.getInkindReportAppendByIds(anyList())).thenReturn(Collections.singletonList(appendEntity));

        inKindExportService.setSignatureLinkIdAttachment(reports);

        assertEquals(null, report.getLinkId());
    }

    @Test
    void testSetSignatureLinkIdAttachmentWithMatchingAttachment() {
        InkindReportEntity report = new InkindReportEntity();
        report.setParentSignatureId("123");
        report.setId("1");
        List<InkindReportEntity> reports = Collections.singletonList(report);

        MapModel mapModel = new MapModel();
        mapModel.setKey("1");
        mapModel.setValue("456");

        MediaEntity attachmentEntity = new MediaEntity();
        attachmentEntity.setId("456");
        attachmentEntity.setPrivateFile(true);
        attachmentEntity.setRelativePath("/attachment/path");

        when(inkindDao.getReportIdAndMediaIdByReportId(anyList())).thenReturn(Collections.singletonList(mapModel));
        when(inkindDao.getMediasByReportIds(anyList())).thenReturn(Collections.singletonList(attachmentEntity));
        when(fileSystem.getPrivateUrl("/attachment/path")).thenReturn("attachmentPrivateUrl");

        inKindExportService.setSignatureLinkIdAttachment(reports);

        assertEquals("attachmentPrivateUrl", report.getMediaPath());
    }

    @Test
    void testSetSignatureLinkIdAttachmentWithNonMatchingAttachment() {
        InkindReportEntity report = new InkindReportEntity();
        report.setParentSignatureId("123");
        report.setId("1");
        List<InkindReportEntity> reports = Collections.singletonList(report);

        MapModel mapModel = new MapModel();
        mapModel.setKey("1");
        mapModel.setValue("789");

        MediaEntity attachmentEntity = new MediaEntity();
        attachmentEntity.setId("456");
        attachmentEntity.setPrivateFile(true);
        attachmentEntity.setRelativePath("/attachment/path");

        when(inkindDao.getReportIdAndMediaIdByReportId(anyList())).thenReturn(Collections.singletonList(mapModel));
        when(inkindDao.getMediasByReportIds(anyList())).thenReturn(Collections.singletonList(attachmentEntity));

        inKindExportService.setSignatureLinkIdAttachment(reports);

        assertEquals(null, report.getMediaPath());
    }

    @Test
    void testSetSignatureLinkIdAttachmentWithPublicAttachment() {
        InkindReportEntity report = new InkindReportEntity();
        report.setParentSignatureId("123");
        report.setId("1");
        List<InkindReportEntity> reports = Collections.singletonList(report);

        MapModel mapModel = new MapModel();
        mapModel.setKey("1");
        mapModel.setValue("456");

        MediaEntity attachmentEntity = new MediaEntity();
        attachmentEntity.setId("456");
        attachmentEntity.setPrivateFile(false);
        attachmentEntity.setRelativePath("/attachment/path");

        when(inkindDao.getReportIdAndMediaIdByReportId(anyList())).thenReturn(Collections.singletonList(mapModel));
        when(inkindDao.getMediasByReportIds(anyList())).thenReturn(Collections.singletonList(attachmentEntity));
        when(fileSystem.getPublicUrl("/attachment/path")).thenReturn("attachmentPublicUrl");

        inKindExportService.setSignatureLinkIdAttachment(reports);

        assertEquals("attachmentPublicUrl", report.getMediaPath());
    }

    @Test
    void testSetSignatureLinkIdAttachmentWithMultipleReports() {
        InkindReportEntity report1 = new InkindReportEntity();
        report1.setParentSignatureId("123");
        report1.setId("1");
        InkindReportEntity report2 = new InkindReportEntity();
        report2.setParentSignatureId("456");
        report2.setId("2");
        List<InkindReportEntity> reports = Arrays.asList(report1, report2);

        MediaEntity mediaEntity1 = new MediaEntity();
        mediaEntity1.setId("123");
        mediaEntity1.setPrivateFile(true);
        mediaEntity1.setRelativePath("/private/path1");

        MediaEntity mediaEntity2 = new MediaEntity();
        mediaEntity2.setId("456");
        mediaEntity2.setPrivateFile(false);
        mediaEntity2.setRelativePath("/public/path2");

        InkindReportAppendEntity appendEntity1 = new InkindReportAppendEntity();
        appendEntity1.setId("1");
        appendEntity1.setLinkId("link123");

        InkindReportAppendEntity appendEntity2 = new InkindReportAppendEntity();
        appendEntity2.setId("2");
        appendEntity2.setLinkId("link456");

        MapModel mapModel1 = new MapModel();
        mapModel1.setKey("1");
        mapModel1.setValue("789");

        MapModel mapModel2 = new MapModel();
        mapModel2.setKey("2");
        mapModel2.setValue("101");

        MediaEntity attachmentEntity1 = new MediaEntity();
        attachmentEntity1.setId("789");
        attachmentEntity1.setPrivateFile(true);
        attachmentEntity1.setRelativePath("/attachment/path1");

        MediaEntity attachmentEntity2 = new MediaEntity();
        attachmentEntity2.setId("101");
        attachmentEntity2.setPrivateFile(false);
        attachmentEntity2.setRelativePath("/attachment/path2");

        when(inkindDao.getMediaByIds(anyList())).thenReturn(Arrays.asList(mediaEntity1, mediaEntity2));
        when(inkindDao.getInkindReportAppendByIds(anyList())).thenReturn(Arrays.asList(appendEntity1, appendEntity2));
        when(inkindDao.getReportIdAndMediaIdByReportId(anyList())).thenReturn(Arrays.asList(mapModel1, mapModel2));
        when(inkindDao.getMediasByReportIds(anyList())).thenReturn(Arrays.asList(attachmentEntity1, attachmentEntity2));
        when(fileSystem.getPrivateUrl("/private/path1")).thenReturn("privateUrl1");
        when(fileSystem.getPublicUrl("/public/path2")).thenReturn("publicUrl2");
        when(fileSystem.getPrivateUrl("/attachment/path1")).thenReturn("attachmentPrivateUrl1");
        when(fileSystem.getPublicUrl("/attachment/path2")).thenReturn("attachmentPublicUrl2");

        inKindExportService.setSignatureLinkIdAttachment(reports);

        assertEquals("privateUrl1", report1.getParentSignatureAbsoluteUrl());
        assertEquals("link123", report1.getLinkId());
        assertEquals("attachmentPrivateUrl1", report1.getMediaPath());

        assertEquals("publicUrl2", report2.getParentSignatureAbsoluteUrl());
        assertEquals("link456", report2.getLinkId());
        assertEquals("attachmentPublicUrl2", report2.getMediaPath());
    }

}

package com.learninggenie.api.service.impl;

import com.learninggenie.api.model.tag.CreateTagRequest;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.common.data.dao.MetaDao;
import com.learninggenie.common.data.dao.TagDao;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.entity.TagCategoryEntity;
import com.learninggenie.common.data.entity.TagEntity;
import com.learninggenie.common.data.entity.UserEntity;
import com.learninggenie.common.data.enums.UserRole;
import com.learninggenie.common.data.model.AgencyModel;
import com.learninggenie.common.data.model.UserModel;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;


/**
 * Created by zjj on 2016/6/22.
 */
@RunWith(MockitoJUnitRunner.class)
public class TagServiceImplTest {
    @Mock
    private MetaDao metaDao;
    @Mock
    private TagDao tagDao;
    @Mock
    private UserDaoImpl userDao;
    @Mock
    private UserProvider userProvider;
    @InjectMocks
    private TagServiceImpl tagService;

    /**
     * 当前用户不是agency admin/owner 抛异常
     * zjj 2016.6.22
     */
    @Test(expected = Exception.class)
    public void testGetTagByUserIdWithException() {
        UserEntity user = new UserEntity();
        user.setId("u001");
        user.setRole(UserRole.SITE_ADMIN.toString());
        when(userProvider.checkUser("u001")).thenReturn(user);
        tagService.getTagByUserId("u001", null);
    }

    /**
     * agnecy owner 获取 tag
     * zjj 2016.6.22
     */
    @Test
    public void testGetTagByAgencyOwnerId() {
        UserEntity user = new UserEntity();
        String userId = "u001";
        user.setId(userId);
        user.setRole(UserRole.AGENCY_OWNER.toString());
        when(userProvider.checkUser(userId)).thenReturn(user);
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("a001");
        agencyModel.setName("a001");
        List<AgencyModel> agencyModelList = new ArrayList<>();
        agencyModelList.add(agencyModel);
        when(userDao.getAgencyByAgencyAdminId(userId)).thenReturn(agencyModelList);
        when(metaDao.getAppMeta(anyString())).thenReturn(null);
        tagService.getTagByUserId(userId, null);
        verify(tagDao, times(1)).getTagByAgencyId("a001");
    }

    /**
     * agnecy admin 获取 tag
     * zjj 2016.6.22
     */
    @Test
    public void testGetTagByAgencyAdminId() {
        UserEntity user = new UserEntity();
        String userId = "u001";
        user.setId(userId);
        user.setRole(UserRole.AGENCY_ADMIN.toString());
        when(userProvider.checkUser(userId)).thenReturn(user);
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("a001");
        agencyModel.setName("a001");
        List<AgencyModel> agencyModelList = new ArrayList<>();
        agencyModelList.add(agencyModel);
        when(userDao.getAgencyByAgencyAdminId(userId)).thenReturn(agencyModelList);
        UserModel userModel = new UserModel();
        userModel.setId("owner01");
        List<UserModel> userModelList = new ArrayList<>();
        userModelList.add(userModel);
//        when(userDao.getAgencyOwnerByAgencyId(agencyModel.getId())).thenReturn(userModelList);
        when(metaDao.getAppMeta(anyString())).thenReturn(null);
        tagService.getTagByUserId(userId, null);
        verify(tagDao, times(1)).getTagByAgencyId("a001");
    }

    /**
     * site admin 获取 tag
     * zjj 2016.6.22
     */
    @Test
    public void testGetTagBySiteAdminId() {
        UserEntity user = new UserEntity();
        String userId = "u001";
        user.setId(userId);
        user.setRole(UserRole.SITE_ADMIN.toString());
        when(userProvider.checkUser(userId)).thenReturn(user);
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("a001");
        agencyModel.setName("a001");
        List<AgencyModel> agencyModelList = new ArrayList<>();
        agencyModelList.add(agencyModel);
        when(userDao.getAgencyBySiteAdminId(userId)).thenReturn(agencyModelList);
        UserModel userModel = new UserModel();
        userModel.setId("siteadmin01");
        List<UserModel> userModelList = new ArrayList<>();
        userModelList.add(userModel);
//        when(userDao.getAgencyOwnerByAgencyId(agencyModel.getId())).thenReturn(userModelList);
        when(metaDao.getAppMeta(anyString())).thenReturn(null);
        tagService.getTagByUserId(userId, null);
        verify(tagDao, times(1)).getTagByAgencyId("a001");
    }

    /**
     * 老师获取 tag
     * zjj 2016.6.22
     */
    @Test
    public void testGetTagByTeacherId() {
        UserEntity user = new UserEntity();
        String userId = "u001";
        user.setId(userId);
        user.setRole(UserRole.COLLABORATOR.toString());
        when(userProvider.checkUser(userId)).thenReturn(user);
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("a001");
        agencyModel.setName("a001");
        List<AgencyModel> agencyModelList = new ArrayList<>();
        agencyModelList.add(agencyModel);
        when(userDao.getAgencyByTeacherId(userId)).thenReturn(agencyModelList);
        when(metaDao.getAppMeta(anyString())).thenReturn(null);
        UserModel userModel = new UserModel();
        userModel.setId("teacher01");
        List<UserModel> userModelList = new ArrayList<>();
        userModelList.add(userModel);
//        when(userDao.getAgencyOwnerByAgencyId(agencyModel.getId())).thenReturn(userModelList);
        tagService.getTagByUserId(userId, null);
        verify(tagDao, times(1)).getTagByAgencyId("a001");
    }

    /**
     * 当前用户不是agency admin/owner 抛异常
     * zjj 2016.6.22
     */
    @Test(expected = Exception.class)
    public void testInsertTagWithException() {
        UserEntity user = new UserEntity();
        String userId = "u001";
        user.setId(userId);
        user.setRole(UserRole.SITE_ADMIN.toString());
//        when(userProvider.checkUser(userId)).thenReturn(user);
        tagService.insertTag(Mockito.any(CreateTagRequest.class), anyString());
    }

    /**
     * 正常插入tag
     * zjj 2016.6.322
     */
    @Test
    public void testInsertTag() {
        UserEntity user = new UserEntity();
        String userId = "u001";
        user.setId(userId);
        user.setRole(UserRole.AGENCY_OWNER.toString());
        TagCategoryEntity tagCategoryEntity = new TagCategoryEntity();
        tagCategoryEntity.setId("t002");
        tagCategoryEntity.setName("t002");
        CreateTagRequest createTagRequest = new CreateTagRequest();
        createTagRequest.setCategoryName("t002");
        createTagRequest.setTagName("t001");
        when(userProvider.checkUser(userId)).thenReturn(user);
        when(tagDao.getTagCategoryByName(createTagRequest.getCategoryName())).thenReturn(tagCategoryEntity);
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("a001");
        agencyModel.setName("a001");
        List<AgencyModel> agencyModelList = new ArrayList<>();
        agencyModelList.add(agencyModel);
        when(userDao.getAgencyByAgencyAdminId(userId)).thenReturn(agencyModelList);
        tagService.insertTag(createTagRequest, userId);
        verify(tagDao, times(1)).insertTag(any(TagEntity.class), anyString());
        verify(tagDao, times(1)).insertAgencyTag(anyString(), anyString());
    }

    /**
     * 当前用户不是agency admin/owner 抛异常
     * zjj 2016.6.22
     */
    @Test(expected = Exception.class)
    public void testDeleteTagWithException() {
        UserEntity user = new UserEntity();
        String userId = "u001";
        String tagId = "t001";
        user.setId(userId);
        user.setRole(UserRole.SITE_ADMIN.toString());
        when(userProvider.checkUser(userId)).thenReturn(user);
        tagService.deleteTag(userId, tagId);
    }

    /**
     * 正常删除
     * zjj 2016.6.22
     */
    @Test
    public void testDeleteTag() {
        UserEntity user = new UserEntity();
        String userId = "u001";
        String tagId = "t001";
        user.setId(userId);
        user.setRole(UserRole.AGENCY_ADMIN.toString());
        when(userProvider.checkUser(userId)).thenReturn(user);
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("a001");
        agencyModel.setName("a001");
        List<AgencyModel> agencyModelList = new ArrayList<>();
        agencyModelList.add(agencyModel);
        when(userDao.getAgencyByAgencyAdminId(userId)).thenReturn(agencyModelList);
        tagService.deleteTag(userId, tagId);
        verify(tagDao, times(1)).deleteTagByAgencyIdIdAndTagId("a001", tagId);
    }

    /**
     * 当前用户不是agency admin/owner 抛异常
     * Zjj 2016.6.22
     */
    @Test(expected = Exception.class)
    public void testUpdateTagByIdWithException() {
        UserEntity user = new UserEntity();
        String userId = "u001";
        user.setId(userId);
        user.setRole(UserRole.SITE_ADMIN.toString());
        CreateTagRequest createTagRequest = new CreateTagRequest();
        createTagRequest.setTagId("t001");
        createTagRequest.setTagName("t001");
        when(userProvider.checkUser(userId)).thenReturn(user);
        tagService.updateTagById(createTagRequest, userId);
    }

    /**
     * 正常修改tag
     * zjj 2016.6.22
     */
    @Test
    public void testUpdateTagById() {
        UserEntity user = new UserEntity();
        String userId = "u001";
        user.setId(userId);
        user.setRole(UserRole.AGENCY_ADMIN.toString());
        CreateTagRequest createTagRequest = new CreateTagRequest();
        createTagRequest.setTagId("t001");
        createTagRequest.setTagName("t001");
        when(userProvider.checkUser(userId)).thenReturn(user);
        tagService.updateTagById(createTagRequest, userId);
        verify(tagDao, times(1)).updateTag(any(TagEntity.class));
    }

    @Test
    public void testGetTagByType_All(){
        tagService.getTagByType(null);
        verify(tagDao,times(1)).getAll();
        verify(tagDao,times(0)).getByType(anyString());
    }

    @Test
    public void testGetTagByType_ByType(){
        tagService.getTagByType("Custom");
        verify(tagDao,times(0)).getAll();
        verify(tagDao,times(1)).getByType(anyString());
    }
}
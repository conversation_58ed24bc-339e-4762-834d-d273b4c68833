package com.learninggenie.api.service.impl;

import com.learninggenie.common.cache.CacheModel;
import com.learninggenie.common.cache.CacheService;
import com.learninggenie.common.comm.quickblox.CommQuickbloxServiceImpl;
import com.learninggenie.common.data.dao.AgencyDao;
import com.learninggenie.common.data.dao.StudentDao;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.entity.*;
import com.learninggenie.common.data.enums.AgencyMetaKey;
import com.learninggenie.common.user.ParentProvider;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * comm quickblox服务impl测试
 *
 * <AUTHOR>
 * @date 2023/10/12
 */
@RunWith(MockitoJUnitRunner.class)
public class CommQuickbloxServiceImplTest {

    @Mock
    private StudentDao studentDao;

    @Mock
    private AgencyDao agencyDao;

    @Mock
    private UserDaoImpl userDao;

    @Mock
    private ParentProvider parentProvider;

    @Mock
    private CacheService cacheService;

    @InjectMocks
    private CommQuickbloxServiceImpl quickbloxService;


    /**
     * 测试获取聊天组成员为空子id返回空列表
     */
    @Test
    public void testGetChatGroupMembers_emptyChildId_returnsEmptyList() {
        List<UserEntity> chatGroupMembers = quickbloxService.getChatGroupMembers(null);
        Assertions.assertEquals(Collections.emptyList(), chatGroupMembers);
    }

    /**
     * 测试获取聊天组成员不活动子项返回空列表
     */
    @Test
    public void testGetChatGroupMembers_inactiveChild_returnsEmptyList() {
        String childId = "childId";
        when(studentDao.getById(childId)).thenReturn(null); // 模拟返回null表示找不到小孩信息

        List<UserEntity> chatGroupMembers = quickbloxService.getChatGroupMembers(childId);
        Assertions.assertEquals(Collections.emptyList(), chatGroupMembers);
    }

    /**
     * 测试获取聊天组成员通信未打开返回空列表
     */
    @Test
    public void testGetChatGroupMembers_commNotOpen_returnsEmptyList() {
        String childId = "childId";
        EnrollmentEntity child = new EnrollmentEntity();
        child.setId(childId);
        child.setInactive(false);
        when(studentDao.getById(childId)).thenReturn(child); // 模拟返回有效的小孩信息

        AgencyEntity agency = new AgencyEntity();
        String agencyId = "AgencyId";
        agency.setId(agencyId);
        when(agencyDao.getAgencyByChildId_V2(childId)).thenReturn(agency); // 模拟返回有效的机构信息

        AgencyMetaDataEntity commMeta = new AgencyMetaDataEntity();
        commMeta.setMetaKey(AgencyMetaKey.COMM_OPEN.toString());
        commMeta.setMetaValue("true");
        commMeta.setAgencyId(agencyId);
        commMeta.setId("MetaId");

        when(agencyDao.getMeta(anyString(), anyString())).thenReturn(commMeta); // 模拟返回有效的元数据信息

        List<UserEntity> chatGroupMembers = quickbloxService.getChatGroupMembers(childId);
        Assertions.assertEquals(Collections.emptyList(), chatGroupMembers);
    }

    /**
     * 测试获取聊天组成员没有父母返回空列表
     */
    @Test
    public void testGetChatGroupMembers_noParents_returnsEmptyList() {
        String childId = "childId";
        EnrollmentEntity child = new EnrollmentEntity();
        child.setId(childId);
        child.setInactive(false);
        when(studentDao.getById(childId)).thenReturn(child); // 模拟返回有效的小孩信息

        AgencyEntity agency = new AgencyEntity();
        String agencyId = "AgencyId";
        agency.setId(agencyId);
        when(agencyDao.getAgencyByChildId_V2(childId)).thenReturn(agency); // 模拟返回有效的机构信息

        AgencyMetaDataEntity commMeta = new AgencyMetaDataEntity();
        commMeta.setMetaKey(AgencyMetaKey.COMM_OPEN.toString());
        commMeta.setMetaValue("true");
        commMeta.setAgencyId(agencyId);
        commMeta.setId("MetaId");

        when(agencyDao.getMeta(anyString(), anyString())).thenReturn(commMeta); // 模拟返回有效的元数据信息

        List<UserEntity> chatGroupMembers = quickbloxService.getChatGroupMembers(childId);
        Assertions.assertEquals(Collections.emptyList(), chatGroupMembers);
    }

    /**
     * 测试获取聊天组成员无缓存
     */
    @Test
    public void testGetChatGroupMembers_noCache() {
        String childId = "childId";
        EnrollmentEntity child = new EnrollmentEntity();
        child.setId(childId);
        child.setInactive(false);
        GroupEntity group = new GroupEntity();
        group.setId("groupId");
        child.setGroup(group);
        when(studentDao.getById(childId)).thenReturn(child); // 模拟返回有效的小孩信息

        AgencyEntity agency = new AgencyEntity();
        String agencyId = "AgencyId";
        agency.setId(agencyId);
        when(agencyDao.getAgencyByChildId_V2(childId)).thenReturn(agency); // 模拟返回有效的机构信息

        ArrayList<UserEntity> userEntities = new ArrayList<>();
        UserEntity userEntity = new UserEntity();
        userEntity.setId("userId");
        userEntity.setFirstName("firstName");
        userEntity.setLastName("lastName");

        userEntities.add(userEntity);
        when(parentProvider.filterUnlinkParents(anyString(), anyList())).thenReturn(userEntities);
        when(userDao.getSpecialEducationTeachersByChildId(anyString())).thenReturn(userEntities); // 模拟返回有效的老师信息
        AgencyMetaDataEntity commMeta = new AgencyMetaDataEntity();
        commMeta.setMetaKey(AgencyMetaKey.COMM_OPEN.toString());
        commMeta.setMetaValue("true");
        commMeta.setAgencyId(agencyId);
        commMeta.setId("MetaId");

        when(agencyDao.getMeta(anyString(), anyString())).thenReturn(commMeta); // 模拟返回有效的元数据信息

        List<UserEntity> chatGroupMembers = quickbloxService.getChatGroupMembers(childId);
        Assertions.assertEquals(2, chatGroupMembers.size());
    }

    /**
     * 测试获取聊天组成员是否有缓存
     */
    @Test
    public void testGetChatGroupMembers_hasCache() {
        String childId = "childId";
        EnrollmentEntity child = new EnrollmentEntity();
        child.setId(childId);
        child.setInactive(false);
        GroupEntity group = new GroupEntity();
        group.setId("groupId");
        child.setGroup(group);
        when(studentDao.getById(childId)).thenReturn(child); // 模拟返回有效的小孩信息

        AgencyEntity agency = new AgencyEntity();
        String agencyId = "AgencyId";
        agency.setId(agencyId);
        when(agencyDao.getAgencyByChildId_V2(childId)).thenReturn(agency); // 模拟返回有效的机构信息

        ArrayList<UserEntity> userEntities = new ArrayList<>();
        UserEntity userEntity = new UserEntity();
        userEntity.setId("userId");
        userEntity.setFirstName("firstName");
        userEntity.setLastName("lastName");

        userEntities.add(userEntity);
        when(parentProvider.filterUnlinkParents(anyString(), anyList())).thenReturn(userEntities);
        when(userDao.getSpecialEducationTeachersByChildId(anyString())).thenReturn(userEntities); // 模拟返回有效的老师信息
        AgencyMetaDataEntity commMeta = new AgencyMetaDataEntity();
        commMeta.setMetaKey(AgencyMetaKey.COMM_OPEN.toString());
        commMeta.setMetaValue("true");
        commMeta.setAgencyId(agencyId);
        commMeta.setId("MetaId");

        when(agencyDao.getMeta(anyString(), anyString())).thenReturn(commMeta); // 模拟返回有效的元数据信息

        List<UserEntity> chatGroupMembers = quickbloxService.getChatGroupMembers(childId);
        Assertions.assertEquals(2, chatGroupMembers.size());
    }


    /**
     * 测试获取聊天群成员有缓存老师
     */
    @Test
    public void testGetChatGroupMembers_hasCacheTeacher() {
        String childId = "childId";
        EnrollmentEntity child = new EnrollmentEntity();
        child.setId(childId);
        child.setInactive(false);
        GroupEntity group = new GroupEntity();
        group.setId("groupId");
        child.setGroup(group);
        when(studentDao.getById(childId)).thenReturn(child); // 模拟返回有效的小孩信息

        AgencyEntity agency = new AgencyEntity();
        String agencyId = "AgencyId";
        agency.setId(agencyId);
        when(agencyDao.getAgencyByChildId_V2(childId)).thenReturn(agency); // 模拟返回有效的机构信息

        CacheModel cacheModel = new CacheModel();
        cacheModel.setValue("userId");

        ArrayList<UserEntity> userEntities = new ArrayList<>();
        UserEntity userEntity = new UserEntity();
        userEntity.setId("userId");
        userEntity.setFirstName("firstName");
        userEntity.setLastName("lastName");

        userEntities.add(userEntity);
        when(parentProvider.filterUnlinkParents(anyString(), anyList())).thenReturn(userEntities);
        AgencyMetaDataEntity commMeta = new AgencyMetaDataEntity();
        commMeta.setMetaKey(AgencyMetaKey.COMM_OPEN.toString());
        commMeta.setMetaValue("true");
        commMeta.setAgencyId(agencyId);
        commMeta.setId("MetaId");

        when(agencyDao.getMeta(anyString(), anyString())).thenReturn(commMeta); // 模拟返回有效的元数据信息

        List<UserEntity> chatGroupMembers = quickbloxService.getChatGroupMembers(childId);

        Assertions.assertEquals(1, chatGroupMembers.size());
    }
}

package com.learninggenie.api.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.jdbc.core.JdbcTemplate;

import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.model.CenterLevelIsOrNotObserved;
import com.learninggenie.api.model.CreateCenterPortfolioPeriodRequest;
import com.learninggenie.api.model.MacAddress;
import com.learninggenie.api.model.PeriodResponse;
import com.learninggenie.api.model.UpdateCenterPortfolioPeriodRequest;
import com.learninggenie.api.model.center.CenterGroupEnrollment;
import com.learninggenie.api.model.center.CenterResponse;
import com.learninggenie.api.model.center.CenterWithGroupSizeRequest;
import com.learninggenie.api.model.center.CenterWithGroupSizeResponse;
import com.learninggenie.api.provider.CenterProvider;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.provider.impl.PortfolioProviderImpl;
import com.learninggenie.api.service.impl.CenterServiceImpl;
import com.learninggenie.common.data.dao.AgencyDao;
import com.learninggenie.common.data.dao.CenterDao;
import com.learninggenie.common.data.dao.CentersMetaDataDao;
import com.learninggenie.common.data.dao.DomainDao;
import com.learninggenie.common.data.dao.GroupDao;
import com.learninggenie.common.data.dao.StudentDao;
import com.learninggenie.common.data.dao.impl.CenterMapper;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.dto.GroupWithCenter;
import com.learninggenie.common.data.entity.AgencyEntity;
import com.learninggenie.common.data.entity.AgencyIdentifierEntity;
import com.learninggenie.common.data.entity.CenterMetaDataEntity;
import com.learninggenie.common.data.entity.UserEntity;
import com.learninggenie.common.data.model.CenterEntity;
import com.learninggenie.common.data.model.CenterGroup;
import com.learninggenie.common.data.model.CenterGroupModel;
import com.learninggenie.common.data.model.CenterGroupUser;
import com.learninggenie.common.data.model.CenterModel;
import com.learninggenie.common.data.model.CenterPortfolioPeriod;
import com.learninggenie.common.data.model.DomainEntity;
import com.learninggenie.common.data.model.EnrollmentModel;
import com.learninggenie.common.data.model.GroupEntity;
import com.learninggenie.common.data.model.GroupEntry;
import com.learninggenie.common.data.model.StageEntity;
import com.learninggenie.common.data.model.UserModel;
import com.learninggenie.common.data.repository.UserRepository;
import com.learninggenie.common.filesystem.FileSystem;
import com.learninggenie.common.region.RegionService;
import com.learninggenie.common.report.Domain;
import com.learninggenie.common.utils.StringUtil;

@RunWith(MockitoJUnitRunner.class)
public class CenterServiceTest {
    @InjectMocks
    CenterServiceImpl centerService;
    @Mock
    private UserRepository userRepository;
    @Mock
    private UserProvider userProvider;
    @Mock
    private CenterDao centerDao;
    @Mock
    CenterMapper centerMapper;
    @Mock
    private FileSystem fileSystem;
    @Mock
    UserDaoImpl userDao;
    @Mock
    CentersMetaDataDao centersMetaDataDao;
    @Mock
    JdbcTemplate jdbcTemplate;
    @Mock
    DomainDao domainDao;
    @Mock
    NoteService noteService;
    @Mock
    CenterProvider centerProvider;
    @Mock
    private GroupDao groupDao;
    @Mock
    private AgencyDao agencyDao;
    @Mock
    private PortfolioProviderImpl portfolioProvider;
    @Mock
    private StudentDao studentDao;
 
    @Mock
    private RegionService regionService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void mergeCenterA2CenterBHasGroups() throws Exception {
        when(centerMapper.getCenterUserId(anyString())).thenReturn("123");
        List<GroupEntry> list1 = new ArrayList<>();
        list1.add(new GroupEntry("123", "ccv"));
        List<GroupEntry> list2 = new ArrayList<>();
        list2.add(new GroupEntry("456", "ccv"));
        when(centerMapper.getGroupList(eq("123"))).thenReturn(list1);
//        when(centerMapper.getGroupList(eq("456"))).thenReturn(list2);
        when(centerMapper.updateGroup(anyString(), anyString())).thenReturn(1);
        when(centerMapper.addGroupUser(anyString(), anyString())).thenReturn(1);
        when(centerMapper.deleteCenter(anyString())).thenReturn(1);
        Assert.assertTrue(centerService.mergeCenterA2CenterB("123", "456") != null);
    }


    @Test
    public void mergeCenterA2CenterBNoGroups() throws Exception {
        when(centerMapper.getCenterUserId(anyString())).thenReturn("123");
        List<GroupEntry> list1 = new ArrayList<>();
        List<GroupEntry> list2 = new ArrayList<>();
        list2.add(new GroupEntry("123", "ccv"));
        list2.add(new GroupEntry("456", "ccv"));
        when(centerMapper.getGroupList(eq("123"))).thenReturn(list1);
//        when(centerMapper.getGroupList(eq("456"))).thenReturn(list2);
//        when(centerMapper.updateGroup(anyString(), anyString())).thenReturn(1);
//        when(centerMapper.addGroupUser(anyString(), anyString())).thenReturn(1);
        when(centerMapper.deleteCenter(anyString())).thenReturn(1);
        Assert.assertTrue(centerService.mergeCenterA2CenterB("123", "456") != null);
    }

    @Test
    public void testGetCentersByName() {
        List<CenterGroupUser> cgu = new ArrayList<>();
        {
            CenterGroupUser c1 = new CenterGroupUser();
            c1.setCenterId("123");
            c1.setUid("123");
            c1.setGroupId("1");
            c1.setTid("1");
            cgu.add(c1);
        }
        {
            CenterGroupUser c1 = new CenterGroupUser();
            c1.setCenterId("123");
            c1.setUid("123");
            c1.setGroupId("2");
            c1.setTid("1");
            cgu.add(c1);
        }
        {
            CenterGroupUser c1 = new CenterGroupUser();
            c1.setCenterId("123");
            c1.setUid("123");
            c1.setGroupId("1");
            c1.setTid("2");
            cgu.add(c1);
        }
        {
            CenterGroupUser c1 = new CenterGroupUser();
            c1.setCenterId("456");
            c1.setUid("456");
            c1.setGroupId("3");
            c1.setTid("2");
            cgu.add(c1);
        }

        when(centerMapper.queryCentersByName(anyString())).thenReturn(cgu);
        List<CenterEntity> centers = centerService.getCentersByName("123");
        Assert.assertTrue(centers.size() == 2);
        for (CenterEntity c : centers) {
            if (c.getId().equals("123")) {
                Assert.assertTrue(c.getGroups().size() == 2);
                for (GroupEntity g : c.getGroups()) {
                    if (g.getId().equals("1")) {
                        Assert.assertTrue(g.getTeachers().size() == 2);
                    }
                }
            }
            if (c.getId().equals("456")) {
                Assert.assertTrue(c.getGroups().size() == 1);
            }
        }
    }

    @Test
    public void testGetCenterByName() {
        List<CenterGroup> list = new ArrayList<>();
        {
            CenterGroup cg = new CenterGroup("123", null, "1", null, null, "1", null);
            list.add(cg);
        }
        {
            CenterGroup cg = new CenterGroup("123", null, "1", null, null, "2", null);
            list.add(cg);
        }
        {
            CenterGroup cg = new CenterGroup("456", null, "1", null, null, "3", null);
            list.add(cg);
        }
        when(centerMapper.queryCentersByEmail(anyString())).thenReturn(list);
        List<CenterEntity> centers = centerService.getCentersByEmail("122");
        Assert.assertTrue(centers.size() == 2);
        for (CenterEntity c : centers) {
            if (c.getId().equals("123")) {
                Assert.assertTrue(c.getGroups().size() == 2);
            }
            if (c.getId().equals("456")) {
                Assert.assertTrue(c.getGroups().size() == 1);
            }
        }
    }

    @Test
    public void testMergeCenterA2CenterB() throws Exception{
        when(centerMapper.getCenterUserId(anyString())).thenReturn("123");
        List<GroupEntry> groupsA  = new ArrayList<>();
        GroupEntry groupEntity1 = new GroupEntry("123","111");
        GroupEntry groupEntity2 = new GroupEntry("123","111");
        groupsA.add(groupEntity1);
        groupsA.add(groupEntity2);
        when(centerMapper.getGroupList(anyString())).thenReturn(groupsA);
        when(centerMapper.updateGroup(anyString(), anyString())).thenReturn(1);
        when(centerMapper.addGroupUser(anyString(), anyString())).thenReturn(1);
        when(userDao.swicth2Teacher(anyString())).thenReturn(1);
        when(centerMapper.deleteCenter(anyString())).thenReturn(1);
        centerService.mergeCenterA2CenterB("123","456");
        verify(centerMapper, times(2)).updateGroup(anyString(), anyString());
        verify(centerMapper,times(2)).addGroupUser(anyString(), anyString());
        verify(userDao,times(1)).swicth2Teacher(anyString());
        verify(centersMetaDataDao,times(1)).mergeCenter(anyString(), anyString());
        verify(centerMapper,times(1)).deleteCenter(anyString());
    }

    @Ignore
    @Test
    public void sendMergeEmail() throws Exception {
        when(centerMapper.getCenterEmail(anyString())).thenReturn("<EMAIL>");
        centerService.sendMergeEmail("122", "<EMAIL>");
    }

    @Test
    public void testGetMacAddress(){
        List<CenterMetaDataEntity> metas=new ArrayList<CenterMetaDataEntity>();
        CenterMetaDataEntity meta1=new CenterMetaDataEntity();
        meta1.setId("001");
        meta1.setMetaValue("123:23sdf45:234f|Windows,Chrome");
        metas.add(meta1);
        when(centersMetaDataDao.getMetas(anyString(),anyString())).thenReturn(metas);
        List<MacAddress> macAddresses=centerService.getMacAddresses("001");
        Assert.assertTrue(macAddresses.size()>0);
        Assert.assertEquals("123:23sdf45:234f",macAddresses.get(0).getMacAddress());
        Assert.assertEquals("Windows,Chrome",macAddresses.get(0).getEquipment());
    }

    @Test
    public void testUpdateMacAddress(){
        int count=1;
        boolean flag=true;
       MacAddress macaddress=new MacAddress();
        macaddress.setId("001");
        macaddress.setMacAddress("1:2:3");
        macaddress.setEquipment("Windows");
        when(centersMetaDataDao.updateMetaById(anyString(), anyString())).thenReturn(count);
        when(centersMetaDataDao.exist((anyString()))).thenReturn(flag);
        centerService.updateMacAddress(macaddress);
        Assert.assertTrue(count>0);
    }

    //TODO 增加断言
    @Test
    public  void testAddMacAddress(){
        List<CenterMetaDataEntity> metas=new ArrayList<>();
        CenterMetaDataEntity meta1=new CenterMetaDataEntity();
        MacAddress macaddress=new MacAddress();
        macaddress.setId("001");
        macaddress.setMacAddress("1:2:3");
        macaddress.setEquipment("Windows");
        meta1.setId(macaddress.getId());
        //meta1.setMetaValue(macaddress.getMacAddress()+"|"+macaddress.getEquipment());
        meta1.setMetaValue("1|Windows");
        metas.add(meta1);
        when(centersMetaDataDao.getMetas(anyString(),anyString())).thenReturn(metas);
        centerService.addMacAddress(macaddress,"001");
    }
    @Autowired
    Environment env;

    /**
     * 设置学校的学院版功能开关
     * 执行更新操作
     * zjj 2016.3.1
     */
    @Test
    public void testSetCenterMetaUpdate(){
        String centerId="centerId1";
        String key="metaKey";
        String value="Value";
        //模拟centerMeata数据
        List<CenterMetaDataEntity> centerMetaDataEntityList=new ArrayList<>();
        CenterMetaDataEntity centerMetaDataEntity1=new CenterMetaDataEntity();
        centerMetaDataEntity1.setId("centerMeta1");
        centerMetaDataEntity1.setMetaKey(key);
        centerMetaDataEntity1.setMetaValue("metaValue");

        CenterMetaDataEntity centerMetaDataEntity2=new CenterMetaDataEntity();
        centerMetaDataEntity2.setId("centerMeta2");
        centerMetaDataEntity2.setMetaKey(key);
        centerMetaDataEntity2.setMetaValue("metaValue2");
        centerMetaDataEntityList.add(centerMetaDataEntity1);
        centerMetaDataEntityList.add(centerMetaDataEntity2);
        when(centersMetaDataDao.getCenter(anyString(),anyString())).thenReturn(centerMetaDataEntityList);
        centerService.setCenterMeta(centerId,key,value);
        verify(centersMetaDataDao,times(1)).updateMetaValue(anyString(), anyString(), anyString());
    }

    /**
     * 设置学校的学院版功能开关
     * 执行插入操作
     * zjj 2016.3.1
     */
    @Test
    public void testSetCenterMetaInsert(){
        String centerId="centerId1";
        String key="metaKey";
        String value="Value";
        //模拟centerMeata数据
        List<CenterMetaDataEntity> centerMetaDataEntityList=new ArrayList<>();

        when(centersMetaDataDao.getCenter(anyString(),anyString())).thenReturn(centerMetaDataEntityList);
        centerService.setCenterMeta(centerId,key,value);
        verify(centersMetaDataDao,times(1)).insertMetavalue(anyString(), anyString(), anyString());
    }

    @Test
    public void testCenterLevelIsOrNotObserved() {

        Domain domainEntity = new Domain();
        domainEntity.setId("d001");

        List<Domain> domains = new ArrayList<>();
        domains.add(domainEntity);

        List<com.learninggenie.api.model.note.NoteModel> allNotes = new ArrayList<>();
        com.learninggenie.api.model.note.NoteModel note = new com.learninggenie.api.model.note.NoteModel();
        note.setId("n001");
        allNotes.add(note);
        note.setDomains(domains);

        DomainEntity domain = new com.learninggenie.common.data.model.DomainEntity();
        domain.setId("d002");

        List<DomainEntity> domainEntities = new ArrayList<>();
        domainEntities.add(domain);

        String groupId = "g001";
        String fromDate = "01/01/1010";
        String toDate = "01/01/1111";

        List<String> domainIds = new ArrayList<>();
        //domainIds.add("d001");
        List<String> tagIds = new ArrayList<>();
        ///tagIds.add("t001");


//        when(domainDao.getDomainByGroupId(anyString())).thenReturn(domain);
//        when(domainDao.getAllChildDomains(anyString())).thenReturn(domainEntities);
//        when(noteService.getGroupNotes(anyString(), anyString(), anyString(), anyList(), anyList())).thenReturn(allNotes);

        CenterLevelIsOrNotObserved response = centerService.getCenterLevelIsOrNotObserved(groupId, fromDate, toDate, anyString(), domainEntities);

        verify(domainDao, times(1)).getDomainByCenterId(anyString());
        //serviceImpl新new的跟test新new的不一样，用any
        verify(noteService, times(1)).getCenterNotes(groupId,fromDate, toDate, domainIds, tagIds);
        Assert.assertEquals("g001", response.getCenterId());
        //Assert.assertEquals("d001",response.getObserved().get(0).getNodeDomains().get(0).getId());
        Assert.assertEquals("d002", response.getUnobserved().get(0).getNodeDomains().get(0).getId());
    }

    /**
     * 冲突周期为空，创建成功
     */
    @Test
    public void testCreateCenterPortfolioPeriod() {
        CreateCenterPortfolioPeriodRequest request = new CreateCenterPortfolioPeriodRequest();
        request.setCenterId("c001");
        request.setFromDate("2016/01/01");
        request.setToDate("2016/01/02");
        request.setAlias("superMan");
        request.setActived(true);

        com.learninggenie.common.data.entity.CenterEntity center = new com.learninggenie.common.data.entity.CenterEntity();
        center.setId("c001");
        center.setName("11");

        List<CenterPortfolioPeriod> centerPortfolioPeriodList = new ArrayList<>();
        CenterPortfolioPeriod period = new CenterPortfolioPeriod();
        period.setId("p001");
        period.setAlias("testPeriod");
        period.setCenterId("c001");
        Date from = new Date(2017 - 06 - 13);
        Date to = new Date(2017 - 06 - 14);
        period.setFromAtLocal(from);
        period.setToAtLocal(to);
        period.setActived(true);

        when(centerProvider.checkCenter("c001")).thenReturn(center);
        when(centerDao.getCenterPortfolioPeriods("c001")).thenReturn(centerPortfolioPeriodList);

        PeriodResponse response = centerService.createCenterPortfolioPeriod(request);

        verify(centerDao, times(1)).updateCenterPortfolioPeriodIsNotActive("c001");
        verify(centerDao, times(1)).addCenterPortfolioPeriod(any(CenterPortfolioPeriod.class));
        Assert.assertTrue(!StringUtil.isEmptyOrBlank(response.getId()));
    }

    //周期列表不为空，出现time error
    @Test(expected = BusinessException.class)
    public void testCreateCenterPortfolioPeriod_timeError() {
        CreateCenterPortfolioPeriodRequest request = new CreateCenterPortfolioPeriodRequest();
        request.setCenterId("c001");
        request.setFromDate("2016/01/01");
        request.setToDate("2016/01/02");
        request.setAlias("superMan");
        request.setActived(true);

        com.learninggenie.common.data.entity.CenterEntity center = new com.learninggenie.common.data.entity.CenterEntity();
        center.setId("c001");
        center.setName("11");

        List<CenterPortfolioPeriod> centerPortfolioPeriodList = new ArrayList<>();
        CenterPortfolioPeriod period = new CenterPortfolioPeriod();
        period.setId("p001");
        period.setAlias("testPeriod");
        period.setCenterId("g001");
        Date from = new Date(Date.parse("2016/01/01"));
        Date to = new Date(Date.parse("2016/01/02"));
        period.setFromAtLocal(from);
        period.setToAtLocal(to);
        period.setActived(true);
        centerPortfolioPeriodList.add(period);

        when(centerProvider.checkCenter("c001")).thenReturn(center);
        when(centerDao.getCenterPortfolioPeriods("c001")).thenReturn(centerPortfolioPeriodList);

        centerService.createCenterPortfolioPeriod(request);
    }

    /**
     * center not found
     */
    @Test(expected = BusinessException.class)
    public void testCreateCenterPortfolioPeriod_centerNotFound() {
        CreateCenterPortfolioPeriodRequest request = new CreateCenterPortfolioPeriodRequest();
        request.setCenterId("g001");
        request.setFromDate("2016/01/01");
        request.setToDate("2016/01/02");
        request.setAlias("superMan");
        request.setActived(true);

//        com.learninggenie.common.data.entity.CenterEntity center = new com.learninggenie.common.data.entity.CenterEntity();

//        when(centerProvider.checkCenter("c002")).thenReturn(center);

        centerService.createCenterPortfolioPeriod(request);
    }

    /**
     * 正常更新period，传入的别名为空
     */
    @Test
    public void testUpdateCenterPortfolioPeriod() {
        UpdateCenterPortfolioPeriodRequest request = new UpdateCenterPortfolioPeriodRequest();
        request.setId("p001");
        request.setCenterId("c001");
        request.setFromDate("2016/01/01");
        request.setToDate("2016/01/02");
        //request.setAlias("superMan");
        request.setActived(true);

        com.learninggenie.common.data.entity.CenterEntity center = new com.learninggenie.common.data.entity.CenterEntity();
        center.setId("c001");
        center.setName("1201");

        List<CenterPortfolioPeriod> centerPortfolioPeriodList = new ArrayList<>();
        CenterPortfolioPeriod period = new CenterPortfolioPeriod();
        period.setId("p001");
        period.setAlias("testPeriod");
        period.setCenterId("c001");
        Date from = new Date(Date.parse("2016/02/01"));
        Date to = new Date(Date.parse("2016/02/02"));
        period.setFromAtLocal(from);
        period.setToAtLocal(to);
        period.setActived(true);
        //groupPortfolioPeriodList.add(period);

        when(centerProvider.checkCenter("c001")).thenReturn(center);
        when(centerDao.getCenterPortfolioPeriods("c001")).thenReturn(centerPortfolioPeriodList);

        PeriodResponse response = centerService.updateCenterPortfolioPeriod(request);

        Assert.assertEquals("p001", response.getId());
        verify(centerDao, times(1)).updateCenterPortfolioPeriod(any(CenterPortfolioPeriod.class));
        verify(centerDao, times(0)).updateCenterPortfolioPeriodAlias(any(CenterPortfolioPeriod.class));
    }

    /**
     * 正常更新period，别名不为空更新别名
     */
    @Test
    public void testUpdateCenterPortfolioPeriod_aliasNotNull() {
        UpdateCenterPortfolioPeriodRequest request = new UpdateCenterPortfolioPeriodRequest();
        request.setId("p001");
        request.setCenterId("c001");
        request.setFromDate("2016/01/01");
        request.setToDate("2016/01/02");
        request.setAlias("superMan");
        request.setActived(true);

        com.learninggenie.common.data.entity.CenterEntity center = new com.learninggenie.common.data.entity.CenterEntity();
        center.setId("c001");
        center.setName("11");

        List<CenterPortfolioPeriod> centerPortfolioPeriodList = new ArrayList<>();
        CenterPortfolioPeriod period = new CenterPortfolioPeriod();
        period.setId("p001");
        period.setAlias("testPeriod");
        period.setCenterId("c001");
        Date from = new Date(Date.parse("2016/02/01"));
        Date to = new Date(Date.parse("2016/02/02"));
        period.setFromAtLocal(from);
        period.setToAtLocal(to);
        period.setActived(true);
        //groupPortfolioPeriodList.add(period);

        when(centerProvider.checkCenter("c001")).thenReturn(center);
        when(centerDao.getCenterPortfolioPeriods("c001")).thenReturn(centerPortfolioPeriodList);

        PeriodResponse response = centerService.updateCenterPortfolioPeriod(request);

        Assert.assertEquals("p001", response.getId());
        verify(centerDao, times(1)).updateCenterPortfolioPeriod(any(CenterPortfolioPeriod.class));
        verify(centerDao, times(1)).updateCenterPortfolioPeriodAlias(Mockito.any());
    }

    /**
     * center not found
     */
    @Test(expected = BusinessException.class)
    public void testUpdateCenterPortfolioPeriod_centerNotFound() {
        UpdateCenterPortfolioPeriodRequest request = new UpdateCenterPortfolioPeriodRequest();
        request.setCenterId("c001");
        request.setFromDate("2016/01/01");
        request.setToDate("2016/01/02");
        request.setAlias("superMan");
        request.setActived(true);

//        com.learninggenie.common.data.entity.CenterEntity center = new com.learninggenie.common.data.entity.CenterEntity();

        when(centerProvider.checkCenter("c001")).thenReturn(null);

        centerService.updateCenterPortfolioPeriod(request);
    }

    /**
     * 周期冲突重复，出现time error
     */
    @Test(expected = BusinessException.class)
    public void testUpdateCenterPortfolioPeriod_TimeError() {
        UpdateCenterPortfolioPeriodRequest request = new UpdateCenterPortfolioPeriodRequest();
        request.setId("p001");
        request.setCenterId("c001");
        request.setFromDate("2016/01/01");
        request.setToDate("2016/01/02");
        request.setAlias("superMan");
        request.setActived(true);

        com.learninggenie.common.data.entity.CenterEntity center = new com.learninggenie.common.data.entity.CenterEntity();
        center.setId("c001");
        center.setName("11");

        List<CenterPortfolioPeriod> centerPortfolioPeriodList = new ArrayList<>();
        CenterPortfolioPeriod period = new CenterPortfolioPeriod();
        period.setId("p001");
        period.setAlias("testPeriod");
        period.setCenterId("c001");
        Date from = new Date(Date.parse("2016/01/01"));
        Date to = new Date(Date.parse("2016/01/02"));
        period.setFromAtLocal(from);
        period.setToAtLocal(to);
        period.setActived(true);
        centerPortfolioPeriodList.add(period);

//        when(centerProvider.checkCenter("g001")).thenReturn(center);
//        when(centerDao.getCenterPortfolioPeriods("g001")).thenReturn(centerPortfolioPeriodList);

        centerService.updateCenterPortfolioPeriod(request);
    }

    /**
     * center未找到，抛出center not found异常
     */
    @Test(expected = BusinessException.class)
    public void testDeleteCenterPortfolioPeriod_CenterNotFound() {
        String centerId = "c001";
        String periodId = "p001";

        new com.learninggenie.common.data.entity.CenterEntity();

//        when(centerProvider.checkCenter("c002")).thenReturn(null);

        centerService.deleteCenterPortfolioPeriod(centerId, periodId);
    }

    /**
     * period not found
     */
    @Test(expected = BusinessException.class)
    public void testDeleteCenterPortfolioPeriod_PeriodNotFound() {
        String centerId = "c001";
        String periodId = "p001";

        com.learninggenie.common.data.entity.CenterEntity center = new com.learninggenie.common.data.entity.CenterEntity();
        center.setId("c001");
        center.setName("1201");

//        CenterPortfolioPeriod period = new CenterPortfolioPeriod();

        when(centerProvider.checkCenter("c001")).thenReturn(center);
//        when(centerDao.getCenterPortfolioPeriod("p002")).thenReturn(period);

        centerService.deleteCenterPortfolioPeriod(centerId, periodId);
    }

    /**
     * 成功删除Center周期
     */
    @Ignore
    public void testDeleteCenterPortfolioPeriod() {
        String centerId = "c001";
        String periodId = "p001";

        com.learninggenie.common.data.entity.CenterEntity center = new com.learninggenie.common.data.entity.CenterEntity();
        center.setId("c002");
        center.setName("11");
        center.setCenterTimeZone("American/Los_Angeles");

        List<CenterPortfolioPeriod> centerPortfolioPeriodList = new ArrayList<>();
        CenterPortfolioPeriod periodToRemove = new CenterPortfolioPeriod();
        periodToRemove.setId("p001");
        periodToRemove.setAlias("testPeriod");
        periodToRemove.setCenterId("c001");
        Date from = new Date(Date.parse("2018/01/01"));
        Date to = new Date(Date.parse("2018/01/02"));
        periodToRemove.setFromAtLocal(from);
        periodToRemove.setToAtLocal(to);
        periodToRemove.setActived(true);
        centerPortfolioPeriodList.add(periodToRemove);

        CenterPortfolioPeriod period = new CenterPortfolioPeriod();
        period.setId("p002");
        period.setAlias("testPeriod2");
        period.setCenterId("c002");
        Date from2 = new Date(Date.parse("2018/01/03"));
        Date to2 = new Date(Date.parse("2018/01/03"));
        period.setFromAtLocal(from2);
        period.setToAtLocal(to2);
        period.setActived(true);
        centerPortfolioPeriodList.add(period);

        when(centerProvider.checkCenter("c001")).thenReturn(center);
        when(centerDao.getCenterPortfolioPeriod("p001")).thenReturn(period);
        when(centerDao.getCenterPortfolioPeriods("c001")).thenReturn(centerPortfolioPeriodList);

        centerService.deleteCenterPortfolioPeriod(centerId, periodId);

        verify(centerDao, times(1)).deleteCenterPortfolioPeriod(centerId, periodId);
        verify(centerDao, times(1)).updateCenterPortfolioPeriod(period);
    }

    /**
     * 当前周期早于今天，不能删除这个周期
     */
    @Test(expected = BusinessException.class)
    public void testDeleteCenterPortfolioPeriod_HasPassedToday() {
        String centerId = "c001";
        String periodId = "p001";

        com.learninggenie.common.data.entity.CenterEntity center = new com.learninggenie.common.data.entity.CenterEntity();
        center.setId("c001");
        center.setName("11");

        List<CenterPortfolioPeriod> centerPortfolioPeriodList = new ArrayList<>();
        CenterPortfolioPeriod period = new CenterPortfolioPeriod();
        period.setId("p001");
        period.setAlias("testPeriod");
        period.setCenterId("c001");
        Date from = new Date(Date.parse("2016/01/01"));
        Date to = new Date(Date.parse("2016/01/02"));
        period.setFromAtLocal(from);
        period.setToAtLocal(to);
        period.setActived(true);
        centerPortfolioPeriodList.add(period);

        com.learninggenie.common.data.entity.CenterEntity centerNew = new com.learninggenie.common.data.entity.CenterEntity();
        centerNew.setId("c001");
        centerNew.setName("11");
        centerNew.setCenterTimeZone("American/Los_Angeles");

        when(centerProvider.checkCenter("c001")).thenReturn(center);
        when(centerDao.getCenterPortfolioPeriod("p001")).thenReturn(period);
//        when(centerDao.getCenterPortfolioPeriods("c001")).thenReturn(centerPortfolioPeriodList);

        centerService.deleteCenterPortfolioPeriod(centerId, periodId);

        verify(centerDao, times(0)).deleteCenterPortfolioPeriod(centerId, periodId);
        verify(centerDao, times(0)).updateCenterPortfolioPeriodIsActived(period);
    }

    /**
     * 获取学校portfolio所有周期，centerNotFound
     */
    @Test(expected = BusinessException.class)
    public void testGetCenterPortfolioPeriods_CenterNotFound() {
        String centerId = "g001";
//        com.learninggenie.common.data.entity.CenterEntity center = new com.learninggenie.common.data.entity.CenterEntity();
        //center.setId("c001");

        when(centerProvider.checkCenter(centerId)).thenReturn(null);

        centerService.getCenterPortfolioPeriods(centerId);
    }

    /**
     * 正常获取学校portfolio周期
     */
    @Test
    public void testGetCenterPortfolioPeriods() {
        String centerId = "c001";
        com.learninggenie.common.data.entity.CenterEntity center = new com.learninggenie.common.data.entity.CenterEntity();
        center.setId("c001");
        center.setName("11");

        List<CenterPortfolioPeriod> centerPortfolioPeriodList = new ArrayList<>();
        CenterPortfolioPeriod period = new CenterPortfolioPeriod();
        period.setId("p001");
        period.setAlias("testPeriod");
        period.setCenterId("c001");
        Date from = new Date(Date.parse("2016/01/01"));
        Date to = new Date(Date.parse("2016/01/02"));
        period.setFromAtLocal(from);
        period.setToAtLocal(to);
        period.setActived(true);
        centerPortfolioPeriodList.add(period);

        List<CenterPortfolioPeriod> response = new ArrayList<>();

        when(centerProvider.checkCenter(centerId)).thenReturn(center);
        when(centerDao.getCenterPortfolioPeriods(centerId)).thenReturn(centerPortfolioPeriodList);

        response = centerService.getCenterPortfolioPeriods(centerId);

        verify(centerDao, times(1)).getCenterPortfolioPeriods(centerId);
        verify(centerProvider, times(1)).checkCenter(centerId);
        Assert.assertEquals("c001", response.get(0).getCenterId());
    }

    /**
     * 成功学校周期激活周期
     */
    @Test
    public void testActiveClassPortfolioPeriod() {
        String centerId = "c001";
        com.learninggenie.common.data.entity.CenterEntity center = new com.learninggenie.common.data.entity.CenterEntity();
        center.setId("c001");
        center.setName("11");

        List<CenterPortfolioPeriod> centerPortfolioPeriodList = new ArrayList<>();
        CenterPortfolioPeriod periodToActive = new CenterPortfolioPeriod();
        periodToActive.setId("p001");
        periodToActive.setAlias("testPeriod");
        periodToActive.setCenterId("c001");
        Date from = new Date(Date.parse("2016/01/01"));
        Date to = new Date(Date.parse("2016/01/02"));
        periodToActive.setFromAtLocal(from);
        periodToActive.setToAtLocal(to);
        periodToActive.setActived(false);
        centerPortfolioPeriodList.add(periodToActive);

        CenterPortfolioPeriod periodToRemove = new CenterPortfolioPeriod();
        periodToRemove.setId("p001");
        periodToRemove.setAlias("testPeriod");
        periodToRemove.setCenterId("c001");
        Date from1 = new Date(Date.parse("2016/01/01"));
        Date to1 = new Date(Date.parse("2016/01/02"));
        periodToRemove.setFromAtLocal(from1);
        periodToRemove.setToAtLocal(to1);
        periodToRemove.setActived(true);


        when(centerProvider.checkCenter(centerId)).thenReturn(center);
        when(centerDao.getCenterPortfolioPeriod("p001")).thenReturn(periodToActive);
        when(centerDao.getActiveCenterPortfolioPeriod("c001")).thenReturn(periodToRemove);

        centerService.activeCenterPortfolioPeriod(centerId, "p001");

        verify(centerProvider, times(1)).checkCenter(centerId);
        verify(centerDao, times(1)).getCenterPortfolioPeriod("p001");
        verify(centerDao, times(1)).getActiveCenterPortfolioPeriod("c001");
        verify(centerDao, times(2)).updateCenterPortfolioPeriod(any(CenterPortfolioPeriod.class));
    }

    /**
     * 获取学校下班级数量
     * 学校下有班级
     */
    @Test
    @Ignore
    public void testGetGroupSizeOfCenter_HaveGroups() {
        UserModel userModel = new UserModel();
        userModel.setId("u001");
        userModel.setDisplayName("taf");

        CenterGroupModel groupModelWithCenter = new CenterGroupModel();
        groupModelWithCenter.setCenterId("c001");
        groupModelWithCenter.setCenterName("center");

        groupModelWithCenter.setGroupId("g001");
        groupModelWithCenter.setGroupName("group");

        List<CenterGroupModel> groupModelsWithCenter = new ArrayList<>();
        groupModelsWithCenter.add(groupModelWithCenter);

        CenterWithGroupSizeRequest request = new CenterWithGroupSizeRequest();
        List<String> centerIds = new ArrayList<>();
        centerIds.add("c001");
        request.setCenterIds(centerIds);

        when(userDao.getUserById("u001")).thenReturn(userModel);
        when(centerDao.getCenterGroupByCenter("c001")).thenReturn(groupModelsWithCenter);

        CenterWithGroupSizeResponse response = centerService.getGroupSizeOfCenter(request, "u001");

        Assert.assertEquals(response.getCenters().get(0).getCenterId(), "c001");
        Assert.assertEquals(response.getCenters().get(0).getGroupSizeOfCenter(), 1);
    }

    /**
     * 获取学校下班级数量
     * 学校下没有班级
     */
    @Test
    @Ignore
    public void testGetGroupSizeOfCenter_NoGroups() {
        UserModel userModel = new UserModel();
        userModel.setId("u001");
        userModel.setDisplayName("taf");

        CenterGroupModel groupModelWithCenter = new CenterGroupModel();
        groupModelWithCenter.setCenterId("c001");
        groupModelWithCenter.setCenterName("center");

        List<CenterGroupModel> groupModelsWithCenter = new ArrayList<>();
        groupModelsWithCenter.add(groupModelWithCenter);

        CenterWithGroupSizeRequest request = new CenterWithGroupSizeRequest();
        List<String> centerIds = new ArrayList<>();
        centerIds.add("c001");
        request.setCenterIds(centerIds);

        when(userDao.getUserById("u001")).thenReturn(userModel);
        when(centerDao.getCenterGroupByCenter("c001")).thenReturn(groupModelsWithCenter);

        CenterWithGroupSizeResponse response = centerService.getGroupSizeOfCenter(request, "u001");

        Assert.assertEquals(response.getCenters().get(0).getCenterId(), "c001");
        Assert.assertEquals(response.getCenters().get(0).getGroupSizeOfCenter(), 0);
    }

    /**
     * Case:查询用户未获取到或不存在
     * 结果:抛异常
     */
    @Test(expected = BusinessException.class)
    public void testGet_userNotExist() {
        String userId = "1";
        centerService.get(userId,anyString());
    }

    /**
     * Case:查询学校已被删除或者不存在
     * 结果:抛异常
     */
    @Test(expected = BusinessException.class)
    public void testGet_centerNotExist() {
        String centerId = "c001";
        String userId = "a001";
        UserEntity user = new UserEntity();
        user.setId(userId);
        when(userProvider.checkUser(user.getId())).thenReturn(user);
        when(centerDao.getCenterByCenterId(centerId)).thenReturn(null);
        centerService.get(userId, centerId);
        verify(centerProvider,times(1)).getOpenValueDefaultClose(anyString(),anyString());
        verify(centerProvider,times(1)).getOpenValueDefaultClose(anyString(),anyString());
        verify(centerDao,times(1)).getCountOfChildrenInThisCenter(anyString());
    }
    /**
     * AgencyOwner获取学校信息
     */
    @Test
    @Ignore
    public void  testGet_userIsAgencyOwner() {
        String centerId = "c001";
        String userId = "a001";
        //user
        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setRole("AGENCY_OWNER");

        //domain && classdomain
        DomainEntity domainEntity = new DomainEntity();
        domainEntity.setId("d001");
        DomainEntity classDomain = new DomainEntity();
        classDomain.setId("cd001");
        //group
        List<GroupEntity> group=new ArrayList<>();
        //center
        CenterEntity center = new CenterEntity();
        center.setId(centerId);
        center.setDomainId(domainEntity.getId());
        center.setGroups(group);
        //stage
        StageEntity stage = new StageEntity();
        stage.setId("s001");
        //group001
        GroupEntity group01 = new GroupEntity();
        group01.setId("g001");
        group01.setCenter(center);
        group01.setDomain(domainEntity);
        group01.setClassDomain(classDomain);
        group01.setStage(stage);
        group01.setChildCount(1);
        group.add(group01);
        //lastGroup01
        GroupEntity lastGroup01 = new GroupEntity();
        lastGroup01.setId("lastGroup01");
        lastGroup01.setCenter(center);
        lastGroup01.setDomain(domainEntity);
        lastGroup01.setClassDomain(classDomain);
        lastGroup01.setStage(stage);
        //agency
        AgencyEntity agency = new AgencyEntity();
        agency.setId("a001");
        //enrollment
        EnrollmentModel enrollment = new EnrollmentModel();
        enrollment.setId("e001");
        enrollment.setPrivatePhoto(false);
        List<EnrollmentModel> enrollmentModels = new ArrayList<>();
        enrollmentModels.add(enrollment);
        //parent
        List<UserModel> parent = new ArrayList<>();
        UserModel parent01 = new UserModel();
        parent01.setId("p001");
        parent01.setEnrollmentId(enrollment.getId());
        parent.add(parent01);
        //parentIsDelete
        List<UserModel> parentIsDelete = new ArrayList<>();
        UserModel parent02 = new UserModel();
        parent02.setId("p002");
        parent02.setEnrollmentId(enrollment.getId());
        parentIsDelete.add(parent02);
        //teacher
        UserModel teacher01 = new UserModel();
        teacher01.setId("t001");
        UserModel teacher02 = new UserModel();
        teacher01.setId("t002");
        List<UserModel> teachers = new ArrayList<>();
        teachers.add(teacher01);
        teachers.add(teacher02);
        UserModel teacher03 = new UserModel();
        teacher03.setId("t003");
        List<UserModel> teacherIsInvited = new ArrayList<>();
        teacherIsInvited.add(teacher03);

        //center信息加载
        when(centerDao.getCountOfChildrenInThisCenter(centerId)).thenReturn(1);
        when(userProvider.checkUser(user.getId())).thenReturn(user);
        when(centerDao.getCenterByCenterId(centerId)).thenReturn(center);
        when(domainDao.getDomain(center.getDomainId())).thenReturn(domainEntity);
        when(groupDao.getGroupsByCenterId((center.getId()))).thenReturn(group);
        //MapCenter
        when(groupDao.getGroupById((anyString()))).thenReturn(group01);
        when(agencyDao.getByCenterId(anyString())).thenReturn(agency);
        when(domainDao.getDomain(anyString())).thenReturn(domainEntity);
        when(studentDao.getChildrenCountByGroupId(group01.getId(),new ArrayList<>())).thenReturn(1);
        when(userDao.getTeacherCountByGroupId(group01.getId())).thenReturn(2);
        when(userDao.getParentCountByGroupId(group01.getId())).thenReturn(1);
        when(userDao.getTeachersInvitedByGroupId(group01.getId())).thenReturn(teacherIsInvited);
        when(portfolioProvider.hasScoreTemplate(group01.getDomain().getId())).thenReturn(false);
        when(domainDao.getDomain(group01.getDomain().getId())).thenReturn(classDomain);
        when(studentDao.getChildrenByGroupId(group01.getId(),null)).thenReturn(enrollmentModels);
        when(userDao.getParentsByStudentIdright(enrollment.getId())).thenReturn(parent);
        when(userDao.getDeletedParentsByStudentId(enrollment.getId())).thenReturn(parentIsDelete);
        when(groupDao.getLastGroupBylId(enrollment.getSourceGroupId())).thenReturn(lastGroup01);
        when(domainDao.getDomain(anyString())).thenReturn(lastGroup01.getDomain());
        CenterResponse centerResponse = centerService.get(userId, centerId);

        //孩子信息中获取家长数量和获取家长信息
        verify(userDao,times(2)).getParentsByStudentIdright(anyString());
        //学校一共有一个班级g001
        Assert.assertEquals(1,centerResponse.getGroups().size());
        //g001班级有一个学生
        Assert.assertEquals("1",centerResponse.getGroups().get(0).getChildCount().toString());
    }
    /**
     * 老师获取学校信息
     */
    @Test
    @Ignore
    public void testGet_userIsTeacher() {
        String userId = "t001";
        String centerId = "c001";
        //user
        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setRole("COLLABORATOR");

        //domain && classdomain
        DomainEntity domainEntity = new DomainEntity();
        domainEntity.setId("d001");
        DomainEntity classDomain = new DomainEntity();
        classDomain.setId("cd001");
        //group
        List<GroupEntity> group=new ArrayList<>();
        //center
        CenterEntity center = new CenterEntity();
        center.setId(centerId);
        center.setDomainId(domainEntity.getId());
        center.setGroups(group);
        //stage
        StageEntity stage = new StageEntity();
        stage.setId("s001");
        //group001
        GroupEntity group01 = new GroupEntity();
        group01.setId("g001");
        group01.setCenter(center);
        group01.setDomain(domainEntity);
        group01.setClassDomain(classDomain);
        group01.setStage(stage);
        group01.setChildCount(1);
        group.add(group01);
        //groupWithCenters
        List<GroupWithCenter> groupWithCenters = new ArrayList<>();
        GroupWithCenter groupWithCenter = new GroupWithCenter();
        groupWithCenter.setCenterId(center.getId());
        groupWithCenter.setGroupId(group01.getId());
        groupWithCenters.add(groupWithCenter);
        //lastGroup01
        GroupEntity lastGroup01 = new GroupEntity();
        lastGroup01.setId("lastGroup01");
        lastGroup01.setCenter(center);
        lastGroup01.setDomain(domainEntity);
        lastGroup01.setClassDomain(classDomain);
        lastGroup01.setStage(stage);
        //agency
        AgencyEntity agency = new AgencyEntity();
        agency.setId("a001");
        //enrollment
        EnrollmentModel enrollment = new EnrollmentModel();
        enrollment.setId("e001");
        enrollment.setPrivatePhoto(false);
        List<EnrollmentModel> enrollmentModels = new ArrayList<>();
        enrollmentModels.add(enrollment);
        //parent
        List<UserModel> parent = new ArrayList<>();
        UserModel parent01 = new UserModel();
        parent01.setId("p001");
        parent01.setEnrollmentId(enrollment.getId());
        parent.add(parent01);
        //parentIsDelete
        List<UserModel> parentIsDelete = new ArrayList<>();
        UserModel parent02 = new UserModel();
        parent02.setId("p002");
        parent02.setEnrollmentId(enrollment.getId());
        parentIsDelete.add(parent02);
        //teacher
        UserModel teacher01 = new UserModel();
        teacher01.setId("t001");
        UserModel teacher02 = new UserModel();
        teacher01.setId("t002");
        List<UserModel> teachers = new ArrayList<>();
        teachers.add(teacher01);
        teachers.add(teacher02);
        UserModel teacher03 = new UserModel();
        teacher03.setId("t003");
        List<UserModel> teacherIsInvited = new ArrayList<>();
        teacherIsInvited.add(teacher03);

        //center信息加载
        when(userProvider.checkUser(user.getId())).thenReturn(user);
        when(centerDao.getCountOfChildrenInThisCenter(centerId)).thenReturn(1);
        when(centerDao.getCenterByCenterId(centerId)).thenReturn(center);
        when(domainDao.getDomain(center.getDomainId())).thenReturn(domainEntity);
        when(groupDao.getGroupByTeacherId(userId)).thenReturn(groupWithCenters);
        when(groupDao.getGroupById(center.getDomainId())).thenReturn(group01);
        //MapCenter
        when(groupDao.getGroupById((anyString()))).thenReturn(group01);
        when(agencyDao.getByCenterId(anyString())).thenReturn(agency);
        when(domainDao.getDomain(anyString())).thenReturn(domainEntity);
        when(studentDao.getChildrenCountByGroupId(group01.getId(),new ArrayList<>())).thenReturn(1);
        when(userDao.getTeacherCountByGroupId(group01.getId())).thenReturn(2);
        when(userDao.getParentCountByGroupId(group01.getId())).thenReturn(1);
        when(userDao.getTeachersInvitedByGroupId(group01.getId())).thenReturn(teacherIsInvited);
        when(portfolioProvider.hasScoreTemplate(group01.getDomain().getId())).thenReturn(false);
        when(domainDao.getDomain(group01.getDomain().getId())).thenReturn(classDomain);
        when(studentDao.getChildrenByGroupId(group01.getId(),null)).thenReturn(enrollmentModels);
        when(userDao.getParentsByStudentIdright(enrollment.getId())).thenReturn(parent);
        when(userDao.getDeletedParentsByStudentId(enrollment.getId())).thenReturn(parentIsDelete);
        when(groupDao.getLastGroupBylId(enrollment.getSourceGroupId())).thenReturn(lastGroup01);
        when(domainDao.getDomain(anyString())).thenReturn(lastGroup01.getDomain());
        CenterResponse centerResponse = centerService.get(userId, centerId);
        Assert.assertEquals("g001",centerResponse.getGroups().get(0).getId());
    }
    /**
     * Grantee角色查询学校信息
     */
    @Test
    @Ignore
    public void testGet_userIsGrantee() {
        String userId = "t001";
        String centerId = "c001";
        //user
        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setType("GRANTEE");

        //domain && classdomain
        DomainEntity domainEntity = new DomainEntity();
        domainEntity.setId("d001");
        DomainEntity classDomain = new DomainEntity();
        classDomain.setId("cd001");
        //group
        List<GroupEntity> group=new ArrayList<>();
        //center
        CenterEntity center = new CenterEntity();
        center.setId(centerId);
        center.setDomainId(domainEntity.getId());
        center.setGroups(group);
        //centerModels
        List<CenterModel> centerModels = new ArrayList<>();
        CenterModel centerModel01 = new CenterModel();
        CenterModel centerModel02 = new CenterModel();
        centerModel01.setId(center.getId());
        centerModel02.setId("c002");
        centerModels.add(centerModel01);
        centerModels.add(centerModel02);
        //stage
        StageEntity stage = new StageEntity();
        stage.setId("s001");
        //agency
        AgencyEntity agency = new AgencyEntity();
        agency.setId("a001");
        //group001
        GroupEntity group01 = new GroupEntity();
        group01.setId("g001");
        group01.setCenter(center);
        group01.setDomain(domainEntity);
        group01.setClassDomain(classDomain);
        group01.setStage(stage);
        group01.setChildCount(1);
        group.add(group01);
        //agencyIdentifiers
        List<AgencyIdentifierEntity> agencyIdentifiers = new ArrayList<>();
        AgencyIdentifierEntity agencyIdentifierEntity = new AgencyIdentifierEntity();
        agencyIdentifierEntity.setId("I001");
        agencyIdentifierEntity.setName("zhizhi");
        agencyIdentifiers.add(agencyIdentifierEntity);
        //lastGroup01
        GroupEntity lastGroup01 = new GroupEntity();
        lastGroup01.setId("lastGroup01");
        lastGroup01.setCenter(center);
        lastGroup01.setDomain(domainEntity);
        lastGroup01.setClassDomain(classDomain);
        lastGroup01.setStage(stage);
        //enrollment
        EnrollmentModel enrollment = new EnrollmentModel();
        enrollment.setId("e001");
        enrollment.setPrivatePhoto(false);
        List<EnrollmentModel> enrollmentModels = new ArrayList<>();
        enrollmentModels.add(enrollment);
        //parent
        List<UserModel> parent = new ArrayList<>();
        UserModel parent01 = new UserModel();
        parent01.setId("p001");
        parent01.setEnrollmentId(enrollment.getId());
        parent.add(parent01);
        //parentIsDelete
        List<UserModel> parentIsDelete = new ArrayList<>();
        UserModel parent02 = new UserModel();
        parent02.setId("p002");
        parent02.setEnrollmentId(enrollment.getId());
        parentIsDelete.add(parent02);
        //teacher
        UserModel teacher01 = new UserModel();
        teacher01.setId("t001");
        UserModel teacher02 = new UserModel();
        teacher01.setId("t002");
        List<UserModel> teachers = new ArrayList<>();
        teachers.add(teacher01);
        teachers.add(teacher02);
        UserModel teacher03 = new UserModel();
        teacher03.setId("t003");
        List<UserModel> teacherIsInvited = new ArrayList<>();
        teacherIsInvited.add(teacher03);
        List<String> identifiersOfGrantee = new ArrayList<>();

        //center信息加载
        when(userProvider.checkUser(user.getId())).thenReturn(user);
        when(centerDao.getCountOfChildrenInThisCenter(centerId)).thenReturn(1);
        when(centerDao.getCenterByCenterId(centerId)).thenReturn(center);
        when(domainDao.getDomain(center.getDomainId())).thenReturn(domainEntity);
        when(centerDao.getCentersByAgencyUserId(userId)).thenReturn(centerModels);
        //返回identifier id 的小孩
        when(groupDao.getGroupsByCenterId((anyString()))).thenReturn(group);
        when(agencyDao.getIdentifierByUserId((userId))).thenReturn(agencyIdentifiers);
        when(studentDao.getChildrenByGroupId(group01.getId(),1000,1,
                "ORDER BY lastName",identifiersOfGrantee, false)).thenReturn(enrollmentModels);
        when(agencyDao.getByCenterId(anyString())).thenReturn(agency);
        when(domainDao.getDomain(anyString())).thenReturn(domainEntity);
        when(studentDao.getChildrenCountByGroupId(group01.getId(),new ArrayList<>())).thenReturn(1);
        when(userDao.getTeacherCountByGroupId(group01.getId())).thenReturn(2);
        when(userDao.getParentCountByGroupId(group01.getId())).thenReturn(1);
        when(userDao.getTeachersInvitedByGroupId(group01.getId())).thenReturn(teacherIsInvited);
        when(portfolioProvider.hasScoreTemplate(group01.getDomain().getId())).thenReturn(false);
        when(domainDao.getDomain(group01.getDomain().getId())).thenReturn(classDomain);
        when(studentDao.getChildrenByGroupId(group01.getId(),null)).thenReturn(enrollmentModels);
        when(userDao.getParentsByStudentIdright(enrollment.getId())).thenReturn(parent);
        when(userDao.getDeletedParentsByStudentId(enrollment.getId())).thenReturn(parentIsDelete);
        when(groupDao.getLastGroupBylId(enrollment.getSourceGroupId())).thenReturn(lastGroup01);
        when(domainDao.getDomain(anyString())).thenReturn(lastGroup01.getDomain());
        CenterResponse centerResponse = centerService.get(userId, centerId);
        Assert.assertEquals("g001",centerResponse.getGroups().get(0).getId());
    }
    /**
     * SpecialEducation类型的老师查询用户
     */
    @Test
    @Ignore
    public void testGet_userIsSpecialEducation(){
        String userId = "t001";
        String centerId = "c001";
        //user
        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setType("SPECIAL_EDUCATION");
        user.setRole("COLLABORATOR");

        //domain && classdomain
        DomainEntity domainEntity = new DomainEntity();
        domainEntity.setId("d001");
        DomainEntity classDomain = new DomainEntity();
        classDomain.setId("cd001");
        //group
        List<GroupEntity> group=new ArrayList<>();
        //center
        CenterEntity center = new CenterEntity();
        center.setId(centerId);
        center.setDomainId(domainEntity.getId());
        center.setGroups(group);
        //centerModels
        List<CenterModel> centerModels = new ArrayList<>();
        CenterModel centerModel01 = new CenterModel();
        CenterModel centerModel02 = new CenterModel();
        centerModel01.setId(center.getId());
        centerModel02.setId("c002");
        centerModels.add(centerModel01);
        centerModels.add(centerModel02);
        //stage
        StageEntity stage = new StageEntity();
        stage.setId("s001");
        //agency
        AgencyEntity agency = new AgencyEntity();
        agency.setId("a001");
        //group001
        GroupEntity group01 = new GroupEntity();
        group01.setId("g001");
        group01.setCenter(center);
        group01.setDomain(domainEntity);
        group01.setClassDomain(classDomain);
        group01.setStage(stage);
        group01.setChildCount(1);
        group.add(group01);
        //老师自己创建的班级
        List<com.learninggenie.common.data.entity.GroupEntity> userGroups = new ArrayList<>();
        com.learninggenie.common.data.entity.GroupEntity groupEntity = new com.learninggenie.common.data.entity.GroupEntity();
        groupEntity.setId("g002Byteacher");
        userGroups.add(groupEntity);
        //agencyIdentifiers
        List<AgencyIdentifierEntity> agencyIdentifiers = new ArrayList<>();
        AgencyIdentifierEntity agencyIdentifierEntity = new AgencyIdentifierEntity();
        agencyIdentifierEntity.setId("I001");
        agencyIdentifierEntity.setName("zhizhi");
        agencyIdentifiers.add(agencyIdentifierEntity);
        //lastGroup01
        GroupEntity lastGroup01 = new GroupEntity();
        lastGroup01.setId("lastGroup01");
        lastGroup01.setCenter(center);
        lastGroup01.setDomain(domainEntity);
        lastGroup01.setClassDomain(classDomain);
        lastGroup01.setStage(stage);
        //enrollment
        EnrollmentModel enrollment = new EnrollmentModel();
        enrollment.setId("e001");
        enrollment.setPrivatePhoto(false);
        List<EnrollmentModel> enrollmentModels = new ArrayList<>();
        enrollmentModels.add(enrollment);
        //parent
        List<UserModel> parent = new ArrayList<>();
        UserModel parent01 = new UserModel();
        parent01.setId("p001");
        parent01.setEnrollmentId(enrollment.getId());
        parent.add(parent01);
        //parentIsDelete
        List<UserModel> parentIsDelete = new ArrayList<>();
        UserModel parent02 = new UserModel();
        parent02.setId("p002");
        parent02.setEnrollmentId(enrollment.getId());
        parentIsDelete.add(parent02);
        //teacher
        UserModel teacher01 = new UserModel();
        teacher01.setId("t001");
        UserModel teacher02 = new UserModel();
        teacher01.setId("t002");
        List<UserModel> teachers = new ArrayList<>();
        teachers.add(teacher01);
        teachers.add(teacher02);
        UserModel teacher03 = new UserModel();
        teacher03.setId("t003");
        List<UserModel> teacherIsInvited = new ArrayList<>();
        teacherIsInvited.add(teacher03);
        List<String> identifiersOfGrantee = new ArrayList<>();

        //center信息加载
        when(userProvider.checkUser(user.getId())).thenReturn(user);
        when(centerDao.getCountOfChildrenInThisCenter(centerId)).thenReturn(1);
        when(centerDao.getCenterByCenterId(centerId)).thenReturn(center);
        when(domainDao.getDomain(center.getDomainId())).thenReturn(domainEntity);
        when(centerDao.getCentersByAgencyUserId(userId)).thenReturn(centerModels);
        //返回identifier id 的小孩
        when(groupDao.getGroupsByCenterId((anyString()))).thenReturn(group);
        when(agencyDao.getIdentifierByUserId((userId))).thenReturn(agencyIdentifiers);
        when(groupDao.getGroupByAgencyAdmin((userId))).thenReturn(userGroups);
        when(studentDao.getChildrenByGroupId(group01.getId(),1000,1,"ORDER BY lastName",identifiersOfGrantee, false)).thenReturn(enrollmentModels);
        when(studentDao.getChildrenByGroupId(groupEntity.getId(),1000,1,"ORDER BY lastName",identifiersOfGrantee, false)).thenReturn(enrollmentModels);
        when(agencyDao.getByCenterId(anyString())).thenReturn(agency);
        when(domainDao.getDomain(anyString())).thenReturn(domainEntity);
        when(studentDao.getChildrenCountByGroupId(group01.getId(),new ArrayList<>())).thenReturn(1);
        when(userDao.getTeacherCountByGroupId(group01.getId())).thenReturn(2);
        when(userDao.getParentCountByGroupId(group01.getId())).thenReturn(1);
        when(userDao.getTeachersInvitedByGroupId(group01.getId())).thenReturn(teacherIsInvited);
        when(portfolioProvider.hasScoreTemplate(group01.getDomain().getId())).thenReturn(false);
        when(domainDao.getDomain(group01.getDomain().getId())).thenReturn(classDomain);
        when(studentDao.getChildrenByGroupId(group01.getId(),null)).thenReturn(enrollmentModels);
        when(userDao.getParentsByStudentIdright(enrollment.getId())).thenReturn(parent);
        when(userDao.getDeletedParentsByStudentId(enrollment.getId())).thenReturn(parentIsDelete);
        when(groupDao.getLastGroupBylId(enrollment.getSourceGroupId())).thenReturn(lastGroup01);
        when(domainDao.getDomain(anyString())).thenReturn(lastGroup01.getDomain());
        CenterResponse centerResponse = centerService.get(userId, centerId);
        Assert.assertEquals("g002Byteacher",centerResponse.getGroups().get(0).getId());
        Assert.assertEquals("g001",centerResponse.getGroups().get(1).getId());
    }

    /**
     * 测试获取机构下学校数据的方法
     */
    @Test
    public void testGetCentersByAgencyId() {
        // 准备模拟数据
        String agencyId = "testAgencyId";
        com.learninggenie.common.data.entity.CenterEntity center1 = new com.learninggenie.common.data.entity.CenterEntity();
        center1.setId("1");
        center1.setName("B");
        com.learninggenie.common.data.entity.CenterEntity center2 = new com.learninggenie.common.data.entity.CenterEntity();
        center2.setId("2");
        center2.setName("A");
        com.learninggenie.common.data.entity.CenterEntity center3 = new com.learninggenie.common.data.entity.CenterEntity();
        center3.setId("3");
        center3.setName("a");
        com.learninggenie.common.data.entity.CenterEntity center4 = new com.learninggenie.common.data.entity.CenterEntity();
        center4.setId("4");
        center4.setName("c");
        com.learninggenie.common.data.entity.CenterEntity center5 = new com.learninggenie.common.data.entity.CenterEntity();
        center5.setId("5");
        center5.setName(null);
        List<com.learninggenie.common.data.entity.CenterEntity> centers = Arrays.asList(center1, center2, center3, center4, center5);

        // 模拟方法调用
        when(centerDao.getCentersByAgencyId(agencyId)).thenReturn(centers);
        when(groupDao.getCountByCenterId(anyString())).thenReturn(1);

        // 执行测试方法
        List<com.learninggenie.common.data.entity.CenterEntity> centersByAgencyId = centerService.getCentersByAgencyId(agencyId);

        // 结果校验
        assertEquals(5, centersByAgencyId.size()); // 验证获取的学校数量为 5
        // 验证排序正确
        assertEquals(center5.getId(), centersByAgencyId.get(0).getId());
        assertEquals(center2.getId(), centersByAgencyId.get(1).getId());
        assertEquals(center3.getId(), centersByAgencyId.get(2).getId());
        assertEquals(center1.getId(), centersByAgencyId.get(3).getId());
        assertEquals(center4.getId(), centersByAgencyId.get(4).getId());
    }

    /**
     * 测试 transform 方法（将孩子 Model 转化为 CenterGroupEnrollment Model）
     */
    @Test
    public void testTransform() {
        // 创建 EnrollmentModel 列表
        List<EnrollmentModel> enrollmentModels = new ArrayList<>();
        EnrollmentModel enrollmentModel = new EnrollmentModel();
        enrollmentModel.setId("testId");
        enrollmentModel.setDisplayName("testDisplayName");
        enrollmentModel.setFirstName("testFirstName");
        enrollmentModel.setLastName("testLastName");
        enrollmentModel.setAvatarUrl("testAvatarUrl");
        enrollmentModel.setAvatarMediaId("testAvatarMediaId");
        enrollmentModel.setInactive(true);
        enrollmentModel.setPrivatePhoto(true);
        enrollmentModel.setFrameworkId("testFrameworkId");
        enrollmentModel.setFrameworkName("testFrameworkName");
        enrollmentModel.setFrameworkLinkUrl("testFrameworkLinkUrl");
        enrollmentModel.setFrameworkSpanishLinkUrl("testFrameworkSpanishLinkUrl");
        enrollmentModel.setSourceGroupId("testSourceGroupId");
        enrollmentModels.add(enrollmentModel);

        // 模拟方法调用
        when(userProvider.getCurrentLang()).thenReturn("es-ES");
        when(regionService.isChina()).thenReturn(false);
        when(fileSystem.getPublicUrl(anyString())).thenReturn("testPublicUrl");
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId("testGroupId");
        groupEntity.setName("testGroupName");
        DomainEntity domainEntity = new DomainEntity();
        domainEntity.setId("testDomainId");
        domainEntity.setName("testDomainName");
        groupEntity.setDomain(domainEntity);
        when(groupDao.getLastGroupBylId(anyString())).thenReturn(groupEntity);
        when(domainDao.getDomain(anyString())).thenReturn(domainEntity);

        // 调用 transform 方法
        List<CenterGroupEnrollment> result = centerService.transfrom(enrollmentModels);

        // 验证结果
        assertEquals(1, result.size());
        CenterGroupEnrollment centerGroupEnrollment = result.get(0);
        assertEquals("TESTID", centerGroupEnrollment.getId()); // 验证 ID 转化为大写
        assertEquals("testDisplayName", centerGroupEnrollment.getDisplayName()); // 验证 DisplayName 保持不变
        assertEquals("testFirstName", centerGroupEnrollment.getFirstName()); // 验证 FirstName 保持不变
        assertEquals("testLastName", centerGroupEnrollment.getLastName()); // 验证 LastName 保持不变
        assertEquals("testPublicUrl", centerGroupEnrollment.getAvatarUrl()); // 验证 AvatarUrl 为转化后的 URL
        assertEquals("testAvatarMediaId", centerGroupEnrollment.getAvatarMediaId()); // 验证 AvatarMediaId 保持不变
        assertEquals("testFrameworkId", centerGroupEnrollment.getFrameworkId()); // 验证 FrameworkId 保持不变
        assertEquals("testFrameworkName", centerGroupEnrollment.getFrameworkName()); // 验证 FrameworkName 保持不变
        assertEquals("testFrameworkSpanishLinkUrl", centerGroupEnrollment.getFrameworkPdf()); // 验证 FrameworkPdf 为 FrameworkSpanishLinkUrl
        assertEquals(0, centerGroupEnrollment.getParentCount()); // 验证 ParentCount 为 0
        assertEquals("testGroupId", centerGroupEnrollment.getLastGroup().getId()); // 验证 LastGroup 为转化后的 Group
        assertEquals("testGroupName", centerGroupEnrollment.getLastGroup().getName()); // 验证 LastGroup 的 Name 保持不变
        assertEquals("testDomainId", centerGroupEnrollment.getLastGroup().getDomain().getId()); // 验证 LastGroup 的 Domain 的 ID 保持不变
        assertEquals("testDomainName", centerGroupEnrollment.getLastGroup().getDomain().getName()); // 验证 LastGroup 的 Domain 的 Name 保持不变
    }

    /**
     * 测试 transform 方法（将孩子 Model 转化为 CenterGroupEnrollment Model）（中国区域）
     */
    @Test
    public void testTransformInChina() {
        // 创建 EnrollmentModel 列表
        List<EnrollmentModel> enrollmentModels = new ArrayList<>();
        EnrollmentModel enrollmentModel = new EnrollmentModel();
        enrollmentModel.setId("testId");
        enrollmentModel.setDisplayName("testDisplayName");
        enrollmentModel.setFirstName("testFirstName");
        enrollmentModel.setLastName("testLastName");
        enrollmentModel.setAvatarUrl("testAvatarUrl");
        enrollmentModel.setAvatarMediaId("testAvatarMediaId");
        enrollmentModel.setInactive(true);
        enrollmentModel.setPrivatePhoto(true);
        enrollmentModel.setFrameworkId("testFrameworkId");
        enrollmentModel.setFrameworkName("testFrameworkName");
        enrollmentModel.setFrameworkLinkUrl("testFrameworkLinkUrl");
        enrollmentModel.setFrameworkSpanishLinkUrl("testFrameworkSpanishLinkUrl");
        enrollmentModel.setSourceGroupId("testSourceGroupId");
        enrollmentModels.add(enrollmentModel);

        // 模拟方法调用
        when(userProvider.getCurrentLang()).thenReturn("en-US");
        when(regionService.isChina()).thenReturn(true);
        when(fileSystem.getPublicUrl(anyString())).thenReturn("testPublicUrl");
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId("testGroupId");
        groupEntity.setName("testGroupName");
        DomainEntity domainEntity = new DomainEntity();
        domainEntity.setId("testDomainId");
        domainEntity.setName("testDomainName");
        groupEntity.setDomain(domainEntity);
        when(groupDao.getLastGroupBylId(anyString())).thenReturn(groupEntity);
        when(domainDao.getDomain(anyString())).thenReturn(domainEntity);

        // 调用 transform 方法
        List<CenterGroupEnrollment> result = centerService.transfrom(enrollmentModels);

        // 验证结果
        assertEquals(1, result.size());
        CenterGroupEnrollment centerGroupEnrollment = result.get(0);
        assertEquals("TESTID", centerGroupEnrollment.getId()); // 验证 ID 转化为大写
        assertEquals("testDisplayName", centerGroupEnrollment.getDisplayName()); // 验证 DisplayName 保持不变
        assertEquals("testFirstName", centerGroupEnrollment.getFirstName()); // 验证 FirstName 保持不变
        assertEquals("testLastName", centerGroupEnrollment.getLastName()); // 验证 LastName 保持不变
        assertEquals("testPublicUrl", centerGroupEnrollment.getAvatarUrl()); // 验证 AvatarUrl 为转化后的 URL
        assertEquals("testAvatarMediaId", centerGroupEnrollment.getAvatarMediaId()); // 验证 AvatarMediaId 保持不变
        assertEquals("testFrameworkId", centerGroupEnrollment.getFrameworkId()); // 验证 FrameworkId 保持不变
        assertEquals("testFrameworkName", centerGroupEnrollment.getFrameworkName()); // 验证 FrameworkName 保持不变
        assertEquals("testFrameworkLinkUrl", centerGroupEnrollment.getFrameworkPdf()); // 验证 FrameworkPdf 为 FrameworkLinkUrl
        assertEquals(0, centerGroupEnrollment.getParentCount()); // 验证 ParentCount 为 0
        assertEquals("testGroupId", centerGroupEnrollment.getLastGroup().getId()); // 验证 LastGroup 为转化后的 Group
        assertEquals("testGroupName", centerGroupEnrollment.getLastGroup().getName()); // 验证 LastGroup 的 Name 保持不变
        assertEquals("testDomainId", centerGroupEnrollment.getLastGroup().getDomain().getId()); // 验证 LastGroup 的 Domain 的 ID 保持不变
        assertEquals("testDomainName", centerGroupEnrollment.getLastGroup().getDomain().getName());// 验证 LastGroup 的 Domain 的 Name 保持不变
    }
}

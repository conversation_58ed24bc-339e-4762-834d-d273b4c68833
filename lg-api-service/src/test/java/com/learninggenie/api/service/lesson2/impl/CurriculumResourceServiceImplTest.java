package com.learninggenie.api.service.lesson2.impl;

import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.exception.ErrorCode;
import com.learninggenie.api.model.DownFileResponse;
import com.learninggenie.api.model.IdResponse;
import com.learninggenie.api.model.curriculum.CreateDownloadQuizTaskRequest;
import com.learninggenie.api.model.curriculum.GetDownloadQuizTaskResponse;
import com.learninggenie.api.model.lesson2.*;
import com.learninggenie.api.model.lesson2.curriculum.UnitMaterialsResponse;
import com.learninggenie.api.provider.RemoteProvider;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.service.lesson2.CurriculumService;
import com.learninggenie.common.data.dao.JobsJobDao;
import com.learninggenie.common.data.dao.ReportDao;
import com.learninggenie.common.data.dao.dll.SubjectDao;
import com.learninggenie.common.data.dao.lesson2.*;
import com.learninggenie.common.data.entity.dll.SubjectEntity;
import com.learninggenie.common.data.entity.lesson2.curriculum.*;
import com.learninggenie.common.data.entity.lesson2.plan.*;
import com.learninggenie.common.data.enums.*;
import com.learninggenie.common.data.enums.lesson2.QuizDownloadType;
import com.learninggenie.common.data.model.JobsJobEntity;
import com.learninggenie.common.data.model.PdfConvertJobEntity;
import com.learninggenie.common.filesystem.FileSystem;
import com.learninggenie.common.doc.service.DocsService;
import com.learninggenie.common.utils.JsonUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import com.learninggenie.common.utils.pdf.wk.WKHelper;

import java.io.File;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 课程资源服务实现类的单元测试
 * 测试 CurriculumResourceServiceImpl 类的所有公共方法
 * 
 * 测试覆盖以下功能：
 * 1. 获取单元资源（getUnitResources）
 * 2. 下载单元材料（downloadMaterials）
 * 3. 创建下载 Quiz 任务（createDownloadQuizTask）
 * 4. 执行下载 Quiz 任务（executeDownloadQuizTask）
 * 5. 获取下载 Quiz 任务状态（getDownloadQuizTask）
 *
 * <AUTHOR> Assistant
 * @date 2024-03-20
 */
@ExtendWith(MockitoExtension.class)
class CurriculumResourceServiceImplTest {

    @InjectMocks
    private CurriculumResourceServiceImpl curriculumResourceService;

    // Mock 所有依赖的 DAO 和服务
    @Mock
    private CurriculumUnitDao curriculumUnitDao;
    @Mock
    private CurriculumUnitPlanDao curriculumUnitPlanDao;
    @Mock
    private PlanDao planDao;
    @Mock
    private PlanItemDao planItemDao;
    @Mock
    private SubjectDao subjectDao;
    @Mock
    private LessonDao lessonDao;
    @Mock
    private UserProvider userProvider;
    @Mock
    private CurriculumService curriculumService;
    @Mock
    private CurriculumDao curriculumDao;
    @Mock
    private ReportDao reportDao;
    @Mock
    private JobsJobDao jobsJobDao;
    @Mock
    private FileSystem fileSystem;
    @Mock
    private RemoteProvider remoteProvider;
    @Mock
    private DocsService docsService;

    @Mock
    private File mockFile;

    // 测试用常量
    private static final String TEST_UNIT_ID = "unit_001";
    private static final String TEST_PLAN_ID = "plan_001";
    private static final String TEST_LESSON_ID = "lesson_001";
    private static final String TEST_USER_ID = "user_001";
    private static final String TEST_TENANT_ID = "tenant_001";

    /**
     * 测试前的准备工作
     * 设置必要的环境变量和配置
     */
    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(curriculumResourceService, "pdfBucket", "test-bucket");
        ReflectionTestUtils.setField(curriculumResourceService, "autoDeleteBucket", "test-bucket");
        ReflectionTestUtils.setField(curriculumResourceService, "s3Root", "s3://");
        ReflectionTestUtils.setField(curriculumResourceService, "pdfEndpoint", "https://test.com");
        ReflectionTestUtils.setField(curriculumResourceService, "autoDeleteBucket", "test-bucket-auto-delete"); // 初始化 s3BucketName 值
    }

    /**
     * 测试获取单元资源 - 成功场景
     * 验证：
     * 1. 能够正确获取单元的所有资源
     * 2. 包含词汇表但不包含问题
     * 3. 正确计算词汇数量
     */
    @Test
    void getUnitResources_Success() {
        // Arrange
        CurriculumUnitEntity unit = CurriculumResourceTestDataBuilder.createCurriculumUnit(TEST_UNIT_ID);
        List<CurriculumUnitPlanEntity> unitPlans = Collections.singletonList(
                CurriculumResourceTestDataBuilder.createCurriculumUnitPlan(TEST_UNIT_ID, TEST_PLAN_ID, 1));
        List<PlanEntity> plans = Collections.singletonList(
                CurriculumResourceTestDataBuilder.createPlan(TEST_PLAN_ID, 1));
        List<ItemEntity> items = Collections.singletonList(
                CurriculumResourceTestDataBuilder.createPlanItem(TEST_PLAN_ID, TEST_LESSON_ID, 1));
        List<SubjectEntity> subjects = Collections.singletonList(
                CurriculumResourceTestDataBuilder.createSubject(TEST_LESSON_ID, "Test Vocabulary", 1));

        when(curriculumUnitDao.getById(TEST_UNIT_ID)).thenReturn(unit);
        when(curriculumUnitPlanDao.getPlansByUnitId(TEST_UNIT_ID)).thenReturn(unitPlans);
        when(planDao.listPlansByIds(anyList())).thenReturn(plans);
        when(planItemDao.listByPlanIds(anyList())).thenReturn(items);
        when(subjectDao.listByLessonIdIn(anyList())).thenReturn(subjects);

        // Act
        GetUnitResourcesResponse response = curriculumResourceService.getUnitResources(TEST_UNIT_ID, true, false);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getWeeks().size());
        assertEquals(1, response.getVocabularyCount());
    }

    /**
     * 测试获取单元资源 - 无效单元 ID
     * 验证：
     * 1. 当单元 ID 不存在时抛出业务异常
     */
    @Test
    void getUnitResources_InvalidUnitId() {
        // Arrange
        when(curriculumUnitDao.getById(TEST_UNIT_ID)).thenReturn(null);

        // Act & Assert
        assertThrows(BusinessException.class, () ->
                curriculumResourceService.getUnitResources(TEST_UNIT_ID, true, false));
    }

    /**
     * 测试下载单元材料 - 成功场景
     * 验证：
     * 1. 能够成功创建 PDF 转换任务
     * 2. 正确调用远程 PD F服务
     * 3. 返回正确的任务 ID
     */
    @Test
    void downloadMaterials_Success() {
        // Arrange
        CurriculumUnitEntity unit = CurriculumResourceTestDataBuilder.createCurriculumUnit(TEST_UNIT_ID);
        CurriculumEntity curriculum = CurriculumResourceTestDataBuilder.createCurriculum("curriculum_001");
        UnitMaterialsResponse materialsResponse = new UnitMaterialsResponse();

        // Mock WKHelper 的静态方法
        try (MockedStatic<WKHelper> wkHelperMock = mockStatic(WKHelper.class)) {
            // Mock create 方法返回一个模拟的文件
            wkHelperMock.when(() -> WKHelper.create(anyString(), any())).thenReturn(mockFile);
            // Mock upload 方法返回一个 URL
            wkHelperMock.when(() -> WKHelper.upload(any(), any(), anyString()))
                    .thenReturn("https://test.com/test.html");

            // Mock 用户信息
            when(userProvider.getCurrentUserId()).thenReturn(TEST_USER_ID);
            when(curriculumUnitDao.getById(TEST_UNIT_ID)).thenReturn(unit);
            when(curriculumService.getUnitPlanMaterialsWithTranslation(TEST_UNIT_ID, null, null)).thenReturn(materialsResponse);
            when(curriculumDao.getById(anyString())).thenReturn(curriculum);

            // 捕获 PDF 转换任务参数
            ArgumentCaptor<PdfConvertJobEntity> jobCaptor = ArgumentCaptor.forClass(PdfConvertJobEntity.class);

            // Act
            IdResponse response = curriculumResourceService.downloadMaterials(TEST_UNIT_ID, null, null);

            // Assert
            assertNotNull(response);
            assertNotNull(response.getId());

            // 验证 PDF 转换任务的创建
            verify(reportDao).createPdfConvertJob(jobCaptor.capture());
            PdfConvertJobEntity capturedJob = jobCaptor.getValue();
            assertEquals(TEST_USER_ID, capturedJob.getCreatedBy());
            assertEquals(unit.getTenantId(), capturedJob.getAgencyId());
            assertEquals(PdfType.UNIT_MATERIALS.toString(), capturedJob.getType());

            // 验证远程服务调用
            verify(remoteProvider).callPdfService(eq(capturedJob.getId()), any());
        }
    }

    /**
     * 测试下载单元材料 - 无效单元 ID
     * 验证：
     * 1. 当单元 ID 不存在时抛出业务异常
     */
    @Test
    void downloadMaterials_InvalidUnitId() {
        // Arrange
        when(userProvider.getCurrentUserId()).thenReturn(TEST_USER_ID);
        when(curriculumUnitDao.getById(TEST_UNIT_ID)).thenReturn(null);

        // Act & Assert
        assertThrows(BusinessException.class, () ->
                curriculumResourceService.downloadMaterials(TEST_UNIT_ID, null, null));
    }

    /**
     * 测试创建下载 Quiz 任务 - 成功场景
     * 验证：
     * 1. 能够成功创建下载任务
     * 2. 正确设置任务参数
     * 3. 返回正确的任务 ID
     */
    @Test
    void createDownloadQuizTask_Success() {
        // Arrange
        CreateDownloadQuizTaskRequest request = new CreateDownloadQuizTaskRequest();
        request.setUnitId(TEST_UNIT_ID);
        request.setType(QuizDownloadType.ALL.toString());

        when(userProvider.getCurrentUserId()).thenReturn(TEST_USER_ID);
        when(userProvider.getCurrentAgencyId()).thenReturn(TEST_TENANT_ID);

        // Act
        DownFileResponse response = curriculumResourceService.createDownloadQuizTask(request);

        // Assert
        assertNotNull(response);
        assertNotNull(response.getId());
        verify(jobsJobDao).save(any());
    }

    /**
     * 测试执行下载 Quiz 任务 - 成功场景
     * 验证：
     * 1. 能够正确更新任务状态
     * 2. 正确处理任务参数
     */
    @Test
    void executeDownloadQuizTask_Success() {
        // Arrange
        String jobId = UUID.randomUUID().toString();
        JobsJobEntity job = CurriculumResourceTestDataBuilder.createJobsJob(jobId, TaskStatus.PENDING.toString());
        CreateDownloadQuizTaskRequest request = new CreateDownloadQuizTaskRequest();
        request.setUnitId(TEST_UNIT_ID);
        request.setType(QuizDownloadType.ALL.toString());
        job.setArgs(JsonUtil.toJson(request));

        when(jobsJobDao.getById(jobId)).thenReturn(job);
        when(curriculumUnitDao.getById(TEST_UNIT_ID)).thenReturn(CurriculumResourceTestDataBuilder.createCurriculumUnit(TEST_UNIT_ID));

        // Act
        curriculumResourceService.executeDownloadQuizTask(jobId);

        // Assert
        verify(jobsJobDao).updateStatusById(eq(jobId), eq(TaskStatus.PROCESSING.toString()));
    }

    /**
     * 测试获取下载 Quiz 任务 - 成功场景
     * 验证：
     * 1. 能够正确获取任务状态和结果
     * 2. 返回正确的下载 URL
     */
    @Test
    void getDownloadQuizTask_Success() {
        // Arrange
        String jobId = UUID.randomUUID().toString();
        String resultUrl = "https://test.com/result.zip";
        JobsJobEntity job = CurriculumResourceTestDataBuilder.createJobsJob(jobId, TaskStatus.SUCCESS.toString());
        job.setResult(resultUrl);

        when(jobsJobDao.getById(jobId)).thenReturn(job);

        // Act
        GetDownloadQuizTaskResponse response = curriculumResourceService.getDownloadQuizTask(jobId);

        // Assert
        assertNotNull(response);
        assertEquals(resultUrl, response.getUrl());
    }

    /**
     * 测试获取下载 Quiz 任务 - 无效任务 ID
     * 验证：
     * 1. 当任务 ID 不存在时抛出业务异常
     */
    @Test
    void getDownloadQuizTask_InvalidJobId() {
        // Arrange
        String jobId = UUID.randomUUID().toString();
        when(jobsJobDao.getById(jobId)).thenReturn(null);

        // Act & Assert
        assertThrows(BusinessException.class, () ->
                curriculumResourceService.getDownloadQuizTask(jobId));
    }

    /**
     * 测试导出词汇到 CSV - 成功场景
     * 验证：
     * 1. 能够正确获取单元资源
     * 2. 成功生成 CSV 文件
     * 3. 正确上传到 S3
     * 4. 返回正确的下载 URL
     */
    @Test
    void exportVocabulariesToCsv_Success() {
        // Arrange
        CurriculumUnitEntity unit = CurriculumResourceTestDataBuilder.createCurriculumUnit(TEST_UNIT_ID);
        
        // 创建测试数据
        WeekResource weekResource = new WeekResource();
        weekResource.setWeekId(TEST_PLAN_ID);
        weekResource.setWeekName("Test Week");
        weekResource.setWeekNum(1);

        DayResource dayResource = new DayResource();
        dayResource.setDayIndex(1);

        LessonResource lessonResource = new LessonResource();
        lessonResource.setLessonId(TEST_LESSON_ID);
        lessonResource.setLessonName("Test Lesson");
        lessonResource.setVocabularies(Arrays.asList("vocabulary1", "vocabulary2"));

        dayResource.setLessons(Collections.singletonList(lessonResource));
        weekResource.setDays(Collections.singletonList(dayResource));

        GetUnitResourcesResponse resourcesResponse = new GetUnitResourcesResponse();
        resourcesResponse.setWeeks(Collections.singletonList(weekResource));
        resourcesResponse.setVocabularyCount(2);

        // Mock 依赖
        when(curriculumUnitDao.getById(TEST_UNIT_ID)).thenReturn(unit);
        when(userProvider.getCurrentUserId()).thenReturn(TEST_USER_ID);
        
        // Mock getUnitResources 方法
        CurriculumResourceServiceImpl spyService = spy(curriculumResourceService);
        doReturn(resourcesResponse).when(spyService).getUnitResources(TEST_UNIT_ID, true, false);

        // Mock S3 上传
        String expectedUrl = "https://test.com/test.csv";
        when(fileSystem.upload(anyString(), anyString(), any(File.class))).thenReturn("s3://test-bucket/test.csv");
        when(fileSystem.getPublicUrl(anyString(), anyString(), anyString())).thenReturn(expectedUrl);

        // Act
        DownFileResponse response = spyService.exportVocabulariesToCsv(TEST_UNIT_ID, null);

        // Assert
        assertNotNull(response);
        assertEquals(expectedUrl, response.getUrl());
        
        // 验证文件上传
        verify(fileSystem).upload(eq("test-bucket-auto-delete"), contains("csv/Key Vocabularies"), any(File.class));
        verify(fileSystem).getPublicUrl(eq("https://test.com"), eq("test-bucket-auto-delete"), contains("csv/Key Vocabularies"));
    }

    /**
     * 测试导出词汇到 CSV - 无效单元 ID
     * 验证：
     * 1. 当单元 ID 不存在时抛出业务异常
     */
    @Test
    void exportVocabulariesToCsv_InvalidUnitId() {
        // Act & Assert
        assertThrows(BusinessException.class, () ->
                curriculumResourceService.exportVocabulariesToCsv(null, null));
    }

    /**
     * 测试导出词汇到 CSV - 空词汇列表
     * 验证：
     * 1. 当词汇列表为空时也能正常生成 CSV 文件
     */
    @Test
    void exportVocabulariesToCsv_EmptyVocabularies() {
        // Arrange
        CurriculumUnitEntity unit = CurriculumResourceTestDataBuilder.createCurriculumUnit(TEST_UNIT_ID);
        
        // 创建没有词汇的测试数据
        WeekResource weekResource = new WeekResource();
        weekResource.setWeekId(TEST_PLAN_ID);
        weekResource.setWeekName("Test Week");
        weekResource.setWeekNum(1);

        DayResource dayResource = new DayResource();
        dayResource.setDayIndex(1);

        LessonResource lessonResource = new LessonResource();
        lessonResource.setLessonId(TEST_LESSON_ID);
        lessonResource.setLessonName("Test Lesson");
        lessonResource.setVocabularies(Collections.singletonList("test_vocabular"));

        dayResource.setLessons(Collections.singletonList(lessonResource));
        weekResource.setDays(Collections.singletonList(dayResource));

        GetUnitResourcesResponse resourcesResponse = new GetUnitResourcesResponse();
        resourcesResponse.setWeeks(Collections.singletonList(weekResource));
        resourcesResponse.setVocabularyCount(0);

        // Mock 依赖
        when(curriculumUnitDao.getById(TEST_UNIT_ID)).thenReturn(unit);
        when(userProvider.getCurrentUserId()).thenReturn(TEST_USER_ID);
        
        // Mock getUnitResources 方法
        CurriculumResourceServiceImpl spyService = spy(curriculumResourceService);
        doReturn(resourcesResponse).when(spyService).getUnitResources(TEST_UNIT_ID, true, false);

        // Mock S3 上传
        String expectedUrl = "https://test.com/test.csv";
        when(fileSystem.upload(anyString(), anyString(), any(File.class))).thenReturn("s3://test-bucket/test.csv");
        when(fileSystem.getPublicUrl(anyString(), anyString(), anyString())).thenReturn(expectedUrl);

        // Act
        DownFileResponse response = spyService.exportVocabulariesToCsv(TEST_UNIT_ID, null);

        // Assert
        assertNotNull(response);
        assertEquals(expectedUrl, response.getUrl());
        
        // 验证文件上传
        verify(fileSystem).upload(eq("test-bucket-auto-delete"), contains("csv/Key Vocabularies"), any(File.class));
        verify(fileSystem).getPublicUrl(eq("https://test.com"), eq("test-bucket-auto-delete"), contains("csv/Key Vocabularies"));
    }

    /**
     * 测试导出词汇到CSV - 文件上传失败
     * 验证：
     * 1. 当 S3 上传失败时抛出业务异常
     * 2. 临时文件被正确清理
     */
    @Test
    void exportVocabulariesToCsv_UploadFailure() {
        // Arrange
        CurriculumUnitEntity unit = CurriculumResourceTestDataBuilder.createCurriculumUnit(TEST_UNIT_ID);
        
        // 创建测试数据
        GetUnitResourcesResponse resourcesResponse = new GetUnitResourcesResponse();
        resourcesResponse.setWeeks(Collections.emptyList());
        resourcesResponse.setVocabularyCount(0);

        // Mock getUnitResources 方法
        CurriculumResourceServiceImpl spyService = spy(curriculumResourceService);

        // Act & Assert
        assertThrows(BusinessException.class, () ->
                spyService.exportVocabulariesToCsv(TEST_UNIT_ID, null));
    }
}
package com.learninggenie.api.service.lesson2.impl;

import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.exception.ErrorCode;
import com.learninggenie.api.model.DownFileResponse;
import com.learninggenie.api.model.SuccessResponse;
import com.learninggenie.api.model.curriculum.LessonQuizQuestionModel;
import com.learninggenie.api.model.curriculum.UnitResourceResponse;
import com.learninggenie.api.model.lesson2.LessonDetailResponse;
import com.learninggenie.api.model.lesson2.LessonResourceArgs;
import com.learninggenie.api.model.lesson2.LessonStepModel;
import com.learninggenie.api.model.lesson2.bo.LessonDownloadBO;
import com.learninggenie.api.model.unitplanner.BatchGenerateTaskJob;
import com.learninggenie.api.model.unitplanner.CreateBatchGenerateTask;
import com.learninggenie.api.provider.RemoteProvider;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.service.GoogleDriveService;
import com.learninggenie.api.service.NotificationService;
import com.learninggenie.api.service.lesson2.CurriculumResourceService;
import com.learninggenie.api.service.lesson2.CurriculumService;
import com.learninggenie.api.service.lesson2.LessonService;
import com.learninggenie.common.cache.CacheModel;
import com.learninggenie.common.cache.CacheService;
import com.learninggenie.common.data.dao.JobsJobDao;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.dao.lesson2.LessonDao;
import com.learninggenie.common.data.entity.UserFileEntity;
import com.learninggenie.common.data.entity.lesson2.LessonEntity;
import com.learninggenie.common.data.entity.lesson2.LessonTemplateDownloadModel;
import com.learninggenie.common.data.enums.JobType;
import com.learninggenie.common.data.enums.StatusType;
import com.learninggenie.common.data.enums.TaskStatus;
import com.learninggenie.common.data.model.JobsJobEntity;
import com.learninggenie.common.filesystem.FileSystem;
import com.learninggenie.common.utils.JsonUtil;
import com.learninggenie.common.utils.StringUtil;
import com.learninggenie.common.utils.TimeUtil;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 课程下载服务实现类的单元测试
 * 测试 LessonDownloadServiceImpl 类的所有公共方法
 * <p>
 * 测试覆盖以下功能：
 * 1. 创建批量生成课程文件包任务（createBatchGenerateLessonTask）
 * 2. 批量生成任务作业（processBatchGenerateLessonTaskJob）
 * 3. 创建课程模板下载任务（createLessonTemplatesDownload）
 * 4. 处理课程模板下载任务（processLessonTemplatesDownload）
 */
@ExtendWith(MockitoExtension.class)
class LessonDownloadServiceImplTest {

    @InjectMocks
    private LessonDownloadServiceImpl lessonDownloadService;

    // Mock 所有依赖的 DAO 和服务
    @Mock
    private UserProvider userProvider;
    @Mock
    private LessonDao lessonDao;
    @Mock
    private LessonService lessonService;
    @Mock
    private CacheService cacheService;
    @Mock
    private FileSystem fileSystem;
    @Mock
    private UserDaoImpl userDao;
    @Mock
    private JobsJobDao jobsJobDao;
    @Mock
    private RemoteProvider remoteProvider;
    @Mock
    private CurriculumService curriculumService;
    @Mock
    private CurriculumResourceService curriculumResourceService;
    @Mock
    private GoogleDriveService googleDocsService;
    @Mock
    private NotificationService notificationService;

    // 测试用常量
    private static final String TEST_LESSON_ID = "lesson_001";
    private static final String TEST_USER_ID = "user_001";
    private static final String TEST_AGENCY_ID = "agency_001";
    private static final String TEST_BATCH_ID = "batch_001";
    private static final String TEST_JOB_ID = "job_001";
    private static final String TEST_CACHE_KEY = "test_cache_key";
    private static final String TEST_DOWNLOAD_URL = "https://test.com/download";
    private static final String TEST_LESSON_NAME = "Test Lesson";
    private static final String TEST_LANG_CODE = "en";
    private static final String TEST_PROJECT = "test_project";

    /**
     * 测试前的准备工作
     * 设置必要的环境变量和配置
     */
    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(lessonDownloadService, "autoDeleteBucket", "test-bucket");
        ReflectionTestUtils.setField(lessonDownloadService, "pdfEndpoint", "https://test.com");
    }

    /**
     * 测试创建批量生成课程文件包任务 - 成功场景
     * 验证：
     * 1. 能够成功创建批量任务
     * 2. 正确设置任务参数
     * 3. 返回正确的批次 ID
     */
    @Test
    void createBatchGenerateLessonTask_Success() throws IOException {
        // Arrange
        CreateBatchGenerateTask request = new CreateBatchGenerateTask();
        request.setLessonId(TEST_LESSON_ID);
        request.setFileType("pdf");
        request.setLangCode(TEST_LANG_CODE);

        LessonDetailResponse lessonDetail = createMockLessonDetail();

        when(userProvider.getCurrentUserId()).thenReturn(TEST_USER_ID);
        when(userProvider.getCurrentAgencyId()).thenReturn(TEST_AGENCY_ID);
        when(userProvider.getCurrentProject()).thenReturn(TEST_PROJECT);
        when(lessonService.getLessonDetail(TEST_LESSON_ID)).thenReturn(lessonDetail);
        when(cacheService.get(anyString())).thenReturn(null);
        when(lessonService.getMappedFrameworkIdsV2(any())).thenReturn("framework_ids");


        // Act
        SuccessResponse response;
        try (MockedStatic<TimeUtil> timeUtilMock = mockStatic(TimeUtil.class);
             MockedStatic<StringUtil> stringUtilMock = mockStatic(StringUtil.class)) {

            timeUtilMock.when(TimeUtil::getUTCTimeStr).thenReturn("2024-03-20 10:00:00");
            timeUtilMock.when(TimeUtil::getUtcNow).thenReturn(new Date());
            timeUtilMock.when(() -> TimeUtil.format(any(Date.class), anyString())).thenReturn("2024-03-20");
            timeUtilMock.when(() -> TimeUtil.format(anyString(), anyString())).thenReturn("2024-03-20");

            stringUtilMock.when(() -> StringUtil.filterNoValidatedChar(anyString())).thenReturn("test");
            stringUtilMock.when(() -> StringUtil.excapeFileName(anyString())).thenReturn("test.zip");

            response = lessonDownloadService.createBatchGenerateLessonTask(request);
        }

        // Assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertNotNull(response.getId()); // batchId 应该不为空

        // 验证 userDao.createUserFile 被调用
        verify(userDao).createUserFile(any(UserFileEntity.class));

        // 验证 jobsJobDao.saveBatch 被调用
        verify(jobsJobDao).saveBatch(anyList());

        // 验证 remoteProvider.callCommonGenerateResourceTaskServiceByJobType 被调用
        verify(remoteProvider, atLeastOnce()).callCommonGenerateResourceTaskServiceByJobType(anyString(), anyString());
    }

    /**
     * 测试创建批量生成课程文件包任务 - 缓存命中
     * 验证：
     * 1. 当缓存存在时直接返回缓存结果
     * 2. 不会创建新的任务
     */
    @Test
    void createBatchGenerateLessonTask_CacheHit() {
        // Arrange
        CreateBatchGenerateTask request = new CreateBatchGenerateTask();
        request.setLessonId(TEST_LESSON_ID);
        request.setFileType("pdf");
        request.setLangCode(TEST_LANG_CODE);

        LessonDetailResponse lessonDetail = createMockLessonDetail();
        CacheModel cacheModel = new CacheModel();
        cacheModel.setValue(TEST_DOWNLOAD_URL);

        when(userProvider.getCurrentUserId()).thenReturn(TEST_USER_ID);
        when(lessonService.getLessonDetail(TEST_LESSON_ID)).thenReturn(lessonDetail);
        when(cacheService.get(anyString())).thenReturn(cacheModel);

        // Act
        SuccessResponse response = lessonDownloadService.createBatchGenerateLessonTask(request);

        // Assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals(TEST_DOWNLOAD_URL, response.getMessage());

        // 验证没有创建新任务
        verify(userDao, never()).createUserFile(any(UserFileEntity.class));
        verify(jobsJobDao, never()).saveBatch(anyList());
    }

    /**
     * 测试创建批量生成课程文件包任务 - 用户未找到
     * 验证：
     * 1. 当用户 ID 为空时抛出业务异常
     */
    @Test
    void createBatchGenerateLessonTask_UserNotFound() {
        // Arrange
        CreateBatchGenerateTask request = new CreateBatchGenerateTask();
        request.setLessonId(TEST_LESSON_ID);
        request.setFileType("pdf");

        when(userProvider.getCurrentUserId()).thenReturn("");

        // Act & Assert
        BusinessException exception = assertThrows(BusinessException.class, () ->
                lessonDownloadService.createBatchGenerateLessonTask(request));

        assertEquals(ErrorCode.USER_NOT_FOUND, exception.getErrorCode());
    }

    /**
     * 测试创建批量生成课程文件包任务 - 课程未找到
     * 验证：
     * 1. 当课程不存在时抛出业务异常
     */
    @Test
    void createBatchGenerateLessonTask_LessonNotFound() {
        // Arrange
        CreateBatchGenerateTask request = new CreateBatchGenerateTask();
        request.setLessonId(TEST_LESSON_ID);
        request.setFileType("pdf");

        when(userProvider.getCurrentUserId()).thenReturn(TEST_USER_ID);
        when(lessonService.getLessonDetail(TEST_LESSON_ID)).thenReturn(null);

        // Act & Assert
        BusinessException exception = assertThrows(BusinessException.class, () ->
                lessonDownloadService.createBatchGenerateLessonTask(request));

        assertEquals(ErrorCode.LESSON_NOT_FOUND, exception.getErrorCode());
    }

    /**
     * 测试创建批量生成课程文件包任务 - 参数错误
     * 验证：
     * 1. 当课程 ID 为空时抛出业务异常
     */
    @Test
    void createBatchGenerateLessonTask_InvalidParameter() {
        // Arrange
        CreateBatchGenerateTask request = new CreateBatchGenerateTask();
        request.setLessonId("");
        request.setFileType("pdf");

        when(userProvider.getCurrentUserId()).thenReturn(TEST_USER_ID);

        // Act & Assert
        BusinessException exception = assertThrows(BusinessException.class, () ->
                lessonDownloadService.createBatchGenerateLessonTask(request));

        assertEquals(ErrorCode.PARAM_ERROR, exception.getErrorCode());
    }

    /**
     * 测试批量生成任务作业 - 成功场景
     * 验证：
     * 1. 能够成功处理批量任务
     * 2. 正确更新任务状态
     */
    @Test
    void processBatchGenerateLessonTaskJob_Success() {
        // Arrange
        BatchGenerateTaskJob request = new BatchGenerateTaskJob();
        request.setTaskIds(Arrays.asList(TEST_JOB_ID));

        // 创建处理开始时的任务实体（PENDING状态）
        JobsJobEntity pendingJobEntity = createMockJobEntity();
        pendingJobEntity.setStatus(TaskStatus.PENDING.toString());

        // 创建处理结束时的任务实体（SUCCESS状态）
        JobsJobEntity successJobEntity = createMockJobEntity();
        successJobEntity.setStatus(TaskStatus.SUCCESS.toString());
        // 设置任务结果，模拟Google文档类型的成功处理结果
        UnitResourceResponse unitResourceResponse = new UnitResourceResponse();
        unitResourceResponse.setUrl(TEST_DOWNLOAD_URL);
        unitResourceResponse.setPdfConvertJobId(TEST_DOWNLOAD_URL); // Google文档类型需要这个字段
        unitResourceResponse.setLessonTemplateDownloadModels(Collections.emptyList());
        successJobEntity.setResult(JsonUtil.toJson(unitResourceResponse));

        // Mock 开始处理时的查询（返回PENDING状态的任务）
        when(jobsJobDao.listByIds(request.getTaskIds())).thenReturn(Arrays.asList(pendingJobEntity));
        when(jobsJobDao.updateStatusAndCountIncById(anyString(), anyString())).thenReturn(true);

        // Mock 结束处理时的查询（返回SUCCESS状态的任务）
        when(jobsJobDao.listByBatchId(anyString())).thenReturn(Arrays.asList(successJobEntity));

        // Mock 缓存服务的通知锁机制，让它返回 true 表示成功获取锁
        when(cacheService.atomicGetAndSet(anyString(), anyString(), anyInt())).thenReturn(true);

        // 注意：由于fileType="google"，不会进入ZIP压缩逻辑，所以不需要Mock文件系统操作

        // Mock 通知服务 - 使用 doAnswer 处理不确定的返回类型
        doAnswer(invocation -> null).when(notificationService).onlySaveFileNotification(any());

        // Mock 生成 PDF 的相关调用
        DownFileResponse downFileResponse = new DownFileResponse();
        downFileResponse.setId("pdf_id");
        downFileResponse.setUrl(TEST_DOWNLOAD_URL);

        // 使用 doReturn().when() 语法避免严格参数匹配问题
        doReturn(downFileResponse).when(lessonService)
                .generateLessonPDF(anyString(), any(), anyBoolean(), anyBoolean(),
                        anyBoolean(), anyString(), any(), anyBoolean(), anyString());

        when(curriculumService.getLessonTemplateDownloadModels(anyString())).thenReturn(Collections.emptyList());
        when(lessonService.checkLessonSlides(anyString(), isNull())).thenReturn(null);

        // Mock PDF转换等待方法（void方法）
        doAnswer(invocation -> null).when(curriculumService).waitPdfConvertJobSuccess(any());

        // Act - Google文档分支不需要文件系统操作的静态方法Mock
        SuccessResponse response = lessonDownloadService.processBatchGenerateLessonTaskJob(request);

        // Assert
        assertNotNull(response);
        assertTrue(response.isSuccess());

        // 验证任务状态更新
        verify(jobsJobDao).updateStatusAndCountIncById(TEST_JOB_ID, TaskStatus.PROCESSING.toString());
        verify(jobsJobDao).updateStatusAndResponseById(eq(TEST_JOB_ID), eq(TaskStatus.SUCCESS.toString()), anyString());
    }

    /**
     * 测试批量生成任务作业 - 参数错误
     * 验证：
     * 1. 当任务 ID 列表为空时抛出业务异常
     */
    @Test
    void processBatchGenerateLessonTaskJob_InvalidParameter() {
        // Arrange
        BatchGenerateTaskJob request = new BatchGenerateTaskJob();
        request.setTaskIds(Collections.emptyList());

        // Act & Assert
        BusinessException exception = assertThrows(BusinessException.class, () ->
                lessonDownloadService.processBatchGenerateLessonTaskJob(request));

        assertEquals(ErrorCode.PARAM_ERROR, exception.getErrorCode());
    }

    /**
     * 测试批量生成任务作业 - 任务不存在
     * 验证：
     * 1. 当任务不存在时抛出业务异常
     */
    @Test
    void processBatchGenerateLessonTaskJob_TaskNotFound() {
        // Arrange
        BatchGenerateTaskJob request = new BatchGenerateTaskJob();
        request.setTaskIds(Arrays.asList(TEST_JOB_ID));

        when(jobsJobDao.listByIds(request.getTaskIds())).thenReturn(Collections.emptyList());

        // Act & Assert
        BusinessException exception = assertThrows(BusinessException.class, () ->
                lessonDownloadService.processBatchGenerateLessonTaskJob(request));

        assertEquals(ErrorCode.PARAM_ERROR, exception.getErrorCode());
    }

    /**
     * 测试创建课程模板下载任务 - 成功场景
     * 验证：
     * 1. 能够成功创建模板下载任务
     * 2. 正确设置任务参数
     * 3. 返回正确的任务 ID
     */
    @Test
    void createLessonTemplatesDownload_Success() {
        // Arrange
        LessonEntity lessonEntity = createMockLessonEntity();
        when(userProvider.getCurrentUserId()).thenReturn(TEST_USER_ID);
        when(userProvider.getCurrentAgencyId()).thenReturn(TEST_AGENCY_ID);
        when(lessonDao.getById(TEST_LESSON_ID)).thenReturn(lessonEntity);
        when(cacheService.get(anyString())).thenReturn(null);

        // Act
        DownFileResponse response = lessonDownloadService.createLessonTemplatesDownload(TEST_LESSON_ID);

        // Assert
        assertNotNull(response);
        assertNotNull(response.getId());

        // 验证 jobsJobDao.save 被调用
        verify(jobsJobDao).save(any(JobsJobEntity.class));

        // 验证 remoteProvider.callCommonGenerateResourceTaskServiceByJobType 被调用
        verify(remoteProvider).callCommonGenerateResourceTaskServiceByJobType(anyString(), anyString());
    }

    /**
     * 测试创建课程模板下载任务 - 缓存命中
     * 验证：
     * 1. 当缓存存在时直接返回缓存结果
     * 2. 不会创建新的任务
     */
    @Test
    void createLessonTemplatesDownload_CacheHit() {
        // Arrange
        LessonEntity lessonEntity = createMockLessonEntity();
        CacheModel cacheModel = new CacheModel();
        cacheModel.setValue(TEST_DOWNLOAD_URL);

        when(userProvider.getCurrentUserId()).thenReturn(TEST_USER_ID);
        when(userProvider.getCurrentAgencyId()).thenReturn(TEST_AGENCY_ID);
        when(lessonDao.getById(TEST_LESSON_ID)).thenReturn(lessonEntity);
        when(cacheService.get(anyString())).thenReturn(cacheModel);

        // Act
        DownFileResponse response = lessonDownloadService.createLessonTemplatesDownload(TEST_LESSON_ID);

        // Assert
        assertNotNull(response);
        assertEquals(TEST_DOWNLOAD_URL, response.getUrl());

        // 验证没有创建新任务
        verify(jobsJobDao, never()).save(any(JobsJobEntity.class));
        verify(remoteProvider, never()).callCommonGenerateResourceTaskServiceByJobType(anyString(), anyString());
    }

    /**
     * 测试创建课程模板下载任务 - 参数错误
     * 验证：
     * 1. 当课程 ID 为空时抛出业务异常
     */
    @Test
    void createLessonTemplatesDownload_InvalidParameter() {
        // Act & Assert
        BusinessException exception = assertThrows(BusinessException.class, () ->
                lessonDownloadService.createLessonTemplatesDownload(""));

        assertEquals(ErrorCode.PARAM_ERROR, exception.getErrorCode());
    }

    /**
     * 测试创建课程模板下载任务 - 课程不存在
     * 验证：
     * 1. 当课程不存在时抛出业务异常
     */
    @Test
    void createLessonTemplatesDownload_LessonNotFound() {
        // Arrange
        when(lessonDao.getById(TEST_LESSON_ID)).thenReturn(null);
        when(userProvider.getCurrentUserId()).thenReturn(TEST_USER_ID);
        when(userProvider.getCurrentAgencyId()).thenReturn(TEST_AGENCY_ID);

        // Act & Assert
        BusinessException exception = assertThrows(BusinessException.class, () ->
                lessonDownloadService.createLessonTemplatesDownload(TEST_LESSON_ID));

        assertEquals(ErrorCode.PARAM_ERROR, exception.getErrorCode());
    }

    /**
     * 测试处理课程模板下载任务 - 成功场景
     * 验证：
     * 1. 能够成功处理模板下载任务的基本流程
     * 2. 正确更新任务状态
     * 3. 处理异常情况
     * <p>
     * 注意：由于 checkAndDownloadTemplates 涉及复杂的文件系统操作和 File 对象创建，
     * 这里主要测试方法的参数验证和异常处理逻辑
     */
    @Test
    void processLessonTemplatesDownload_Success() {
        // Arrange
        JobsJobEntity jobEntity = createMockJobEntity();
        jobEntity.setType(JobType.LESSON_TEMPLATES.name());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("lessonId", TEST_LESSON_ID);
        jobEntity.setArgs(JsonUtil.toJson(jsonObject));

        when(jobsJobDao.getById(TEST_JOB_ID)).thenReturn(jobEntity);

        // 模拟 checkAndDownloadTemplates 抛出异常，测试异常处理逻辑
        when(curriculumService.getLessonTemplateDownloadModels(TEST_LESSON_ID))
                .thenReturn(Collections.emptyList()); // 返回空列表会导致异常

        // Act & Assert
        DownFileResponse response = lessonDownloadService.processLessonTemplatesDownload(TEST_JOB_ID);

        // 验证返回了空的响应对象（异常情况下的默认行为）
        assertNotNull(response);

        // 验证任务状态更新到 PROCESSING
        verify(jobsJobDao).updateStatusById(TEST_JOB_ID, TaskStatus.PROCESSING.toString());

        // 验证异常处理逻辑：当出现异常时，会调用 handleJobRetry
        verify(jobsJobDao).updateStatusAndCountIncById(TEST_JOB_ID, TaskStatus.PENDING.toString());
    }

    /**
     * 测试处理课程模板下载任务 - 文件操作成功场景（简化版）
     * 由于完整的文件操作测试过于复杂，这里测试核心业务逻辑
     */
    @Test
    void processLessonTemplatesDownload_WithValidData() {
        // Arrange
        JobsJobEntity jobEntity = createMockJobEntity();
        jobEntity.setType(JobType.LESSON_TEMPLATES.name());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("lessonId", TEST_LESSON_ID);
        jobEntity.setArgs(JsonUtil.toJson(jsonObject));

        when(jobsJobDao.getById(TEST_JOB_ID)).thenReturn(jobEntity);
        when(curriculumService.getLessonTemplateDownloadModels(TEST_LESSON_ID))
                .thenReturn(Arrays.asList(createMockLessonTemplateDownloadModel()));
        when(lessonDao.getById(TEST_LESSON_ID)).thenReturn(createMockLessonEntity());

        // Act - 这里会因为文件操作失败而进入异常处理
        DownFileResponse response = lessonDownloadService.processLessonTemplatesDownload(TEST_JOB_ID);

        // Assert
        assertNotNull(response);

        // 验证任务状态更新
        verify(jobsJobDao).updateStatusById(TEST_JOB_ID, TaskStatus.PROCESSING.toString());

        // 验证数据获取逻辑被正确调用
        verify(curriculumService).getLessonTemplateDownloadModels(TEST_LESSON_ID);
        verify(lessonDao).getById(TEST_LESSON_ID);
    }

    /**
     * 测试处理课程模板下载任务 - 参数错误
     * 验证：
     * 1. 当任务 ID 为空时抛出业务异常
     */
    @Test
    void processLessonTemplatesDownload_InvalidParameter() {
        // Act & Assert
        BusinessException exception = assertThrows(BusinessException.class, () ->
                lessonDownloadService.processLessonTemplatesDownload(""));

        assertEquals(ErrorCode.PARAM_ERROR, exception.getErrorCode());
    }

    /**
     * 测试处理课程模板下载任务 - 任务不存在
     * 验证：
     * 1. 当任务不存在时抛出业务异常
     */
    @Test
    void processLessonTemplatesDownload_TaskNotFound() {
        // Arrange
        when(jobsJobDao.getById(TEST_JOB_ID)).thenReturn(null);

        // Act & Assert
        BusinessException exception = assertThrows(BusinessException.class, () ->
                lessonDownloadService.processLessonTemplatesDownload(TEST_JOB_ID));

        assertEquals(ErrorCode.PARAM_ERROR, exception.getErrorCode());
    }

    /**
     * 测试生成资源批次 ID
     * 验证：
     * 1. 能够成功生成唯一的批次 ID
     * 2. 正确创建用户文件记录
     */
    @Test
    void generateResourceBatchId_Success() {
        // Arrange
        String name = TEST_LESSON_NAME;
        String userId = TEST_USER_ID;

        // Mock TimeUtil 静态方法
        try (MockedStatic<TimeUtil> timeUtilMock = mockStatic(TimeUtil.class)) {
            timeUtilMock.when(TimeUtil::getUtcNow).thenReturn(new Date());
            timeUtilMock.when(() -> TimeUtil.format(any(Date.class), anyString())).thenReturn("2024-03-20");

            // Act
            String batchId = lessonDownloadService.generateResourceBatchId(name, userId);

            // Assert
            assertNotNull(batchId);
            assertFalse(batchId.isEmpty());

            // 验证 userDao.createUserFile 被调用
            verify(userDao).createUserFile(any(UserFileEntity.class));
        }
    }

    /**
     * 测试获取 Quiz 文件夹名称
     * 验证：
     * 1. 能够正确生成 Quiz 文件夹名称
     * 2. 包含课程名称和时间戳
     */
    @Test
    void getQuizFolderName_Success() {
        // Arrange
        LessonDownloadBO lessonDownloadBO = new LessonDownloadBO();
        LessonDetailResponse lessonDetail = createMockLessonDetail();
        lessonDownloadBO.setLessonDetailResponse(lessonDetail);
        lessonDownloadBO.setCreateJobTime("2024-03-20 10:00:00");

        // Mock TimeUtil 静态方法
        try (MockedStatic<TimeUtil> timeUtilMock = mockStatic(TimeUtil.class)) {
            timeUtilMock.when(() -> TimeUtil.format(anyString(), anyString())).thenReturn("2024-03-20");

            // Act
            String folderName = lessonDownloadService.getQuizFolderName(lessonDownloadBO);

            // Assert
            assertNotNull(folderName);
            assertTrue(folderName.contains(TEST_LESSON_NAME));
            assertTrue(folderName.contains("Formative Assessment"));
        }
    }

    // 辅助方法 - 创建模拟的课程详情对象
    private LessonDetailResponse createMockLessonDetail() {
        LessonDetailResponse lessonDetail = new LessonDetailResponse();
        lessonDetail.setId(TEST_LESSON_ID);
        lessonDetail.setName(TEST_LESSON_NAME);
        lessonDetail.setUpdateTime(new Date());

        // 创建带有问题的步骤，用于测试形成性评估
        List<LessonStepModel> steps = new ArrayList<>();
        LessonStepModel step = new LessonStepModel();
        List<LessonQuizQuestionModel> questions = new ArrayList<>();
        LessonQuizQuestionModel question = new LessonQuizQuestionModel();
        question.setQuestion("Test question for formative assessment");
        question.setType("multiple_choice");
        questions.add(question);
        step.setQuestions(questions);
        steps.add(step);
        lessonDetail.setSteps(steps);

        return lessonDetail;
    }

    // 辅助方法 - 创建模拟的任务实体对象
    private JobsJobEntity createMockJobEntity() {
        JobsJobEntity jobEntity = new JobsJobEntity();
        jobEntity.setId(TEST_JOB_ID);
        jobEntity.setStatus(StatusType.PENDING.toString());
        jobEntity.setCreateUserId(TEST_USER_ID);
        jobEntity.setSubType(JobType.LESSON_PLAN_PDF.toString());
        jobEntity.setBatchId(TEST_BATCH_ID);

        // 设置任务参数
        LessonResourceArgs args = new LessonResourceArgs();
        args.setLessonId(TEST_LESSON_ID);
        args.setName(TEST_LESSON_NAME);
        args.setLangCode(TEST_LANG_CODE);
        args.setProject(TEST_PROJECT);
        args.setFileType("google"); // 使用google类型避免进入复杂的文件压缩逻辑
        jobEntity.setArgs(JsonUtil.toJson(args));

        return jobEntity;
    }

    // 辅助方法 - 创建模拟的课程实体对象
    private LessonEntity createMockLessonEntity() {
        LessonEntity lessonEntity = new LessonEntity();
        lessonEntity.setId(TEST_LESSON_ID);
        lessonEntity.setName(TEST_LESSON_NAME);
        lessonEntity.setUpdateAtUtc(new Date());
        return lessonEntity;
    }

    // 辅助方法 - 创建模拟的课程模板下载模型
    private LessonTemplateDownloadModel createMockLessonTemplateDownloadModel() {
        LessonTemplateDownloadModel model = new LessonTemplateDownloadModel();
        model.setTemplateName("Test Template");
        model.setTemplateUrl("https://test.com/template");
        return model;
    }


} 
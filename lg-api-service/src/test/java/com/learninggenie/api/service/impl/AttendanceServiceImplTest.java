package com.learninggenie.api.service.impl;

import com.amazonaws.services.lambda.model.InvokeResult;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.toolkit.ChainWrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.model.*;
import com.learninggenie.api.model.healthcheck.*;
import com.learninggenie.api.provider.RemoteProvider;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.service.HealthCheckService;
import com.learninggenie.api.service.StudentService;
import com.learninggenie.api.util.FileUploadUtil;
import com.learninggenie.common.data.dao.*;
import com.learninggenie.common.data.dao.authentication.UserProfileDao;
import com.learninggenie.common.data.dao.impl.AttendanceEntityDaoImpl;
import com.learninggenie.common.data.dao.impl.HealthQuarantineDaoImpl;
import com.learninggenie.common.data.dao.impl.HealthStatisticsHistoryDaoImpl;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.dto.GroupWithCenter;
import com.learninggenie.common.data.dto.ParentDto;
import com.learninggenie.common.data.entity.CenterEntity;
import com.learninggenie.common.data.entity.GroupEntity;
import com.learninggenie.common.data.entity.*;
import com.learninggenie.common.data.enums.*;
import com.learninggenie.common.data.mapper.mybatisplus.dhc.AttendanceMapper;
import com.learninggenie.common.data.mapper.mybatisplus.dhc.HealthQuarantineMapper;
import com.learninggenie.common.data.mapper.mybatisplus.dhc.HealthStatisticsHistoryChildMapper;
import com.learninggenie.common.data.model.DomainEntity;
import com.learninggenie.common.data.model.*;
import com.learninggenie.common.data.model.attendance.*;
import com.learninggenie.common.data.repository.EnrollmentRepository;
import com.learninggenie.common.encryption.EncryptionService;
import com.learninggenie.common.filesystem.FileSystem;
import com.learninggenie.common.utils.CmdUtil;
import com.learninggenie.common.utils.StringUtil;
import com.learninggenie.common.utils.TimeUtil;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class AttendanceServiceImplTest {
    @Spy
    @InjectMocks
    AttendanceServiceImpl attendanceService;

    @Mock
    UserProvider userProvider;

    @Mock
    HealthCheckFormDao healthCheckFormDao;

    @Mock
    MetaDao metaDao;

    @Mock
    EnrollmentRepository enrollmentRepository;

    @Mock
    UserDaoImpl userDao;

    @Mock
    AttendanceEntityDao attendanceEntityDao;

    @Mock
    HealthQuarantineDao healthQuarantineDao;

    @Mock
    JdbcTemplate jdbcTemplate;

    @Mock
    MessageDao messageDao;

    @Mock
    private EnrollmentDao enrollmentDao;

    @Mock
    private GroupDao groupDao;

    @Mock
    private UserProfileDao userProfileDao;

    @Mock
    private CenterDao centerDao;

    @Mock
    private AgencyDao agencyDao;

    @Mock
    private UsersMetaDataDao usersMetaDataDao;

    @Mock
    private StudentDao studentDao;

    @Mock
    private HealthStatisticsHistoryDao healthStatisticsHistoryDao;

    @Mock
    private HealthStatisticsHistoryDaoImpl healthStatisticsHistoryDaoImpl;

    @Mock
    private HealthQuarantineDaoImpl quarantineDaoImpl;

    @Mock
    private AttendanceEntityDaoImpl attendanceEntityDaoImpl;

    @Mock
    private HealthQuarantineDao quarantineDao;

    @Mock
    private FormsOptionDao formsOptionDao;

    @Mock
    private EncryptionService encryptionService;

    @Mock
    private AttendancePickUpUserDao attendancePickUpUserDao;

    @Mock
    private HealthStatisticsHistoryChildMapper healthStatisticsHistoryChildMapper;

    @Mock
    private HealthQuarantineMapper quarantineMapper;

    @Mock
    private AttendanceMapper attendanceMapper;

    @Mock
    private FormsResponseRecordDao formsResponseRecordDao;

    @Mock
    FileSystem fileSystem;

    @Mock
    ReportDao reportDao;

    @Mock
    RemoteProvider remoteProvider;

    @Mock
    StudentService studentService;

    @Mock
    private HealthCheckService healthCheckService;

    private MockedStatic<ChainWrappers> chainWrappersMockedStatic;

    private MockedStatic<FileUploadUtil> fileUtil;

    /**
     * 初始化 TableInfoHelper
     */
    @BeforeAll
    public static void beforeClass() {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), HealthStatisticsHistoryChildEntity.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), AttendanceEntity.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), HealthQuarantineEntity.class);
    }

    /**
     * 初始化静态模拟方法类
     */
    @BeforeEach
    public void beforeMethod() {
        chainWrappersMockedStatic = mockStatic(ChainWrappers.class);
        fileUtil = mockStatic(FileUploadUtil.class);
        // 初始化 jdbcTemplate
        ReflectionTestUtils.setField(quarantineDaoImpl, "jdbcTemplate", jdbcTemplate);
    }

    /**
     * 关闭静态模拟方法类
     */
    @AfterEach
    public void afterMethod() {
        chainWrappersMockedStatic.close();
        fileUtil.close();
    }

    @Test
    void updateQuarantine_messageNum() {
        // 修改未来日期的原因（远程学习），发送一次通知（家长）
        UpdateQuarantineRequest request = new UpdateQuarantineRequest();
        request.setReason("DISTANCE_LEANING");
        request.setNotes("My son distance learning");
        request.setStartDate(TimeUtil.addDays(new Date(), 1));
        request.setEndDate(TimeUtil.addDays(new Date(), 2));
        request.setChildId("1");
        request.setType("CHILD");
        UserModel currentUser = new UserModel();
        currentUser.setRole("");
        when(userDao.getUserById(Mockito.any())).thenReturn(currentUser);
        EnrollmentEntity child = new EnrollmentEntity();
        child.setDisplayName("");
        when(enrollmentRepository.findById(Mockito.any())).thenReturn(Optional.of(child));
        AttendanceEntityDaoImpl attendanceEntityDaoSpy = this.getAttendanceEntityDaoSpy();
        when(attendanceEntityDaoSpy.lambdaQuery().list()).thenReturn(null);
        attendanceService.updateQuarantine("", request, false);
        Mockito.verify(messageDao).createMessage(Mockito.any());
        // 清除 mockito 预设值，保障下个用例没有数据污染
        Mockito.reset(userDao, enrollmentRepository, messageDao);

        // 修改过去日期的原因，不发送通知
        UpdateQuarantineRequest request2 = new UpdateQuarantineRequest();
        request2.setReason("DISTANCE_LEANING");
        request2.setNotes("My son distance learning");
        request2.setStartDate(TimeUtil.addDays(new Date(), -2));
        request2.setEndDate(TimeUtil.addDays(new Date(), -1));
        request2.setChildId("1");
        request2.setType("CHILD");
        UserModel currentUser2 = new UserModel();
        currentUser2.setRole("");
        when(userDao.getUserById(Mockito.any())).thenReturn(currentUser2);
        EnrollmentEntity child2 = new EnrollmentEntity();
        child2.setDisplayName("");
        when(enrollmentRepository.findById(Mockito.any())).thenReturn(Optional.of(child2));

        attendanceService.updateQuarantine("", request2, false);
        Mockito.verify(messageDao, times(0)).createMessage(Mockito.any());
        // 清除 mockito 预设值，保障下个用例没有数据污染
        Mockito.reset(userDao, enrollmentRepository, messageDao);

        // 修改未来日期的原因（隔离），发送两次通知（家长，教师）
        UpdateQuarantineRequest request3 = new UpdateQuarantineRequest();
        request3.setReason("QUARANTINE");
        request3.setNotes("My son distance learning");
        request3.setStartDate(TimeUtil.addDays(new Date(), 1));
        request3.setEndDate(TimeUtil.addDays(new Date(), 2));
        request3.setChildId("1");
        request3.setType("CHILD");
        UserModel currentUser3 = new UserModel();
        currentUser3.setRole("");
        when(userDao.getUserById(Mockito.any())).thenReturn(currentUser3);
        EnrollmentEntity child3 = new EnrollmentEntity();
        child3.setDisplayName("");
        when(enrollmentRepository.findById(Mockito.any())).thenReturn(Optional.of(child3));

        attendanceService.updateQuarantine("", request3, false);
        Mockito.verify(messageDao, times(2)).createMessage(Mockito.any());
        // 清除 mockito 预设值，保障下个用例没有数据污染
        Mockito.reset(userDao, enrollmentRepository, messageDao);
    }

    @Test
    void supplementSignature() {
        // 请求准备
        SupplementSignatureRequest supplementSignatureRequest = new SupplementSignatureRequest();
        supplementSignatureRequest.setStatus("PASS");
        LocalDate localDate = LocalDate.now();
        supplementSignatureRequest.setLocalDate(localDate);
        supplementSignatureRequest.setSignInLocalDateTime(LocalDateTime.now());
        supplementSignatureRequest.setSignInParentId("1233");
        supplementSignatureRequest.setSignInUrl("123");
        supplementSignatureRequest.setSignOutLocalDateTime(LocalDateTime.now());
        supplementSignatureRequest.setSignOutParentId("123");
        supplementSignatureRequest.setSignOutUrl("123");
        supplementSignatureRequest.setFormId("12");
        String childId = "CDAF46B7-BE08-4CA1-B1FA-D42DAF123B41";
        supplementSignatureRequest.setChildId(childId);
        supplementSignatureRequest.setReason("");

        // 数据准备
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId(childId);
        when(groupDao.getGroupByChildId(childId)).thenReturn(groupEntity);
        com.learninggenie.common.data.entity.CenterEntity centerEntity = new com.learninggenie.common.data.entity.CenterEntity();
        centerEntity.setId(childId);
        lenient().when(centerDao.getByChildId(childId)).thenReturn(centerEntity);
        AgencyEntity agencyEntity = new AgencyEntity();
        agencyEntity.setId(childId);
        when(agencyDao.getByCenterId(childId)).thenReturn(agencyEntity);
        when(centerDao.getCenterByGroupId(childId)).thenReturn(centerEntity);
        when(agencyDao.getAgencyByChildId(childId)).thenReturn(agencyEntity);
        when(formsOptionDao.getListByFormId("12")).thenReturn(new ArrayList<>());
        HealthQuarantineDaoImpl quarantineDaoImplSpy = this.getHealthQuarantineDaoSpy();
        AttendanceEntityDaoImpl attendanceEntityDaoSpy = this.getAttendanceEntityDaoSpy();
        // 补签状态是 PASS
        doReturn(new ArrayList<>()).when(attendanceEntityDaoSpy).getPassAttendanceByChildIdAndDate(childId, TimeUtil.format(localDate, TimeUtil.format10), "desc");
        lenient().when(attendanceEntityDaoSpy.lambdaQuery().list()).thenReturn(null);
        lenient().doNothing().when(attendanceEntityDaoSpy).insert(any());
        lenient().when(quarantineDaoImplSpy.lambdaQuery().list()).thenReturn(new ArrayList<>());
        attendanceService.supplementSignature(supplementSignatureRequest);
        verify(agencyDao, times(1)).getByCenterId(childId);
        // 清除 mockito 预设值，保障下个用例没有数据污染
        Mockito.reset(attendanceEntityDaoSpy);

        // 当天签到记录最后一次状态是补签
        List<AttendanceEntity> attendanceList = new ArrayList<>();
        AttendanceEntity attendanceEntity = new AttendanceEntity();
        attendanceEntity.setChildId("1");
        attendanceEntity.setCreateLocalDate(TimeUtil.localDateToDate(LocalDate.of(2022, 06, 02)));
        attendanceEntity.setAttendanceDate(TimeUtil.localDateToDate(LocalDate.of(2022, 06, 02)));
        attendanceEntity.setType(CheckType.CHECK_OUT.toString());
        attendanceEntity.setAttendanceMethod(AttendanceMethodEnum.SUPPLEMENT.toString());
        attendanceList.add(attendanceEntity);
        doReturn(attendanceList).when(attendanceEntityDaoSpy).getPassAttendanceByChildIdAndDate(childId, TimeUtil.format(localDate, TimeUtil.format10), "desc");

        assertThrows(BusinessException.class, () -> attendanceService.supplementSignature(supplementSignatureRequest));
        // 清除 mockito 预设值，保障下个用例没有数据污染
        Mockito.reset(attendanceEntityDaoSpy, agencyDao);

        // 当天签到记录最后一次状态不是补签
        supplementSignatureRequest.setSignInParentId(null);
        List<AttendanceEntity> attendanceList2 = new ArrayList<>();
        AttendanceEntity attendanceEntity2 = new AttendanceEntity();
        attendanceEntity2.setChildId("1");
        attendanceEntity2.setCreateLocalDate(TimeUtil.localDateToDate(LocalDate.of(2022, 06, 02)));
        attendanceEntity2.setAttendanceDate(TimeUtil.localDateToDate(LocalDate.of(2022, 06, 02)));
        attendanceEntity2.setType(CheckType.CHECK_IN.toString());
        attendanceEntity2.setAttendanceMethod(AttendanceMethodEnum.SEARCH.toString());
        attendanceList2.add(attendanceEntity2);
        when(agencyDao.getByCenterId(childId)).thenReturn(agencyEntity);
        doReturn(attendanceList2).when(attendanceEntityDaoSpy).getPassAttendanceByChildIdAndDate(childId, TimeUtil.format(localDate, TimeUtil.format10), "desc");
        lenient().when(attendanceEntityDaoSpy.lambdaQuery().list()).thenReturn(null);
        lenient().doNothing().when(attendanceEntityDaoSpy).insert(any());
        lenient().when(quarantineDaoImplSpy.lambdaQuery().list()).thenReturn(new ArrayList<>());
        attendanceService.supplementSignature(supplementSignatureRequest);
        verify(agencyDao, times(1)).getByCenterId(childId);
        // 清除 mockito 预设值，保障下个用例没有数据污染
        Mockito.reset(attendanceEntityDaoSpy);

        // 补签到签退
        supplementSignatureRequest.setSignInParentId(childId);
        doReturn(new ArrayList<>()).when(attendanceEntityDaoSpy).getPassAttendanceByChildIdAndDate(childId, TimeUtil.format(localDate, TimeUtil.format10), "desc");
        lenient().when(attendanceEntityDaoSpy.lambdaQuery().list()).thenReturn(null);
        lenient().doNothing().when(attendanceEntityDaoSpy).insert(any());
        lenient().when(quarantineDaoImplSpy.lambdaQuery().list()).thenReturn(new ArrayList<>());
        attendanceService.supplementSignature(supplementSignatureRequest);
        verify(attendanceEntityDaoSpy, times(2)).insert(Mockito.any(AttendanceEntity.class));
        // 清除 mockito 预设值，保障下个用例没有数据污染
        Mockito.reset(attendanceEntityDaoSpy);

        // 补签退
        supplementSignatureRequest.setSignInParentId(null);
        doReturn(attendanceList2).when(attendanceEntityDaoSpy).getPassAttendanceByChildIdAndDate(childId, TimeUtil.format(localDate, TimeUtil.format10), "desc");
        lenient().when(attendanceEntityDaoSpy.lambdaQuery().list()).thenReturn(null);
        lenient().doNothing().when(attendanceEntityDaoSpy).insert(any());
        lenient().when(quarantineDaoImplSpy.lambdaQuery().list()).thenReturn(new ArrayList<>());
        attendanceService.supplementSignature(supplementSignatureRequest);
        verify(attendanceEntityDaoSpy, times(1)).insert(Mockito.any(AttendanceEntity.class));
        // 清除 mockito 预设值，保障下个用例没有数据污染
        Mockito.reset(attendanceEntityDaoSpy);

        // 补签状态是 REJECTIVE
        supplementSignatureRequest.setStatus(AttendanceStatusEnum.REJECTIVE.toString());
        doNothing().when(attendanceService).updateQuarantines(any(), any());
        attendanceService.supplementSignature(supplementSignatureRequest);
        verify(attendanceEntityDaoSpy, times(0)).insert(Mockito.any(AttendanceEntity.class));
        // 清除 mockito 预设值，保障下个用例没有数据污染
        Mockito.reset(attendanceEntityDaoSpy);

        /* 补签状态是 REJECTIVE
        supplementSignatureRequest.setStatus(AttendanceStatusEnum.REJECTIVE.toString());
        AttendanceServiceImpl spy = spy(attendanceService);
        doNothing().when(spy).updateQuarantines(any(),any());
        spy.supplementSignature(supplementSignatureRequest);
        verify(attendanceEntityDao, times(0)).insert(Mockito.any(AttendanceEntity.class)); */

    }

    @Test
    void listSignatureHistory() {
        // 请求准备
        String groupId = "1";
        LocalDate startDate = LocalDate.of(2022, 06, 02);
        LocalDate endDate = LocalDate.of(2022, 06, 30);

        // 判断小孩这天状态为正常
        GroupEntry groupEntry = new GroupEntry();
        groupEntry.setName("123");
        when(groupDao.getGroup(groupId)).thenReturn(groupEntry);
        when(studentDao.getChildrenByGroup(groupId)).thenReturn(new ArrayList<>());
        List<HealthStatisticsHistoryChildEntity> childHistory = new ArrayList<>();
        HealthStatisticsHistoryChildEntity entity = new HealthStatisticsHistoryChildEntity();
        entity.setLocalDate(LocalDate.of(2022, 06, 02));
        entity.setEnrollmentIds("1");
        childHistory.add(entity);
        HealthStatisticsHistoryDaoImpl healthStatisticsHistoryDaoSpy = this.getHealthStatisticsHistoryDaoSpy();
        when(healthStatisticsHistoryDaoSpy.lambdaQuery().list()).thenReturn(childHistory);
        HealthQuarantineDaoImpl quarantineDaoImplSpy = this.getHealthQuarantineDaoSpy();

        List<String> allChildIds = new ArrayList<>();
        allChildIds.add("1");

        // 添加 getFirstAttendanceDateByChildrenIds 的 mock
        List<AttendanceEntity> firstAttendanceDateList = new ArrayList<>();
        AttendanceEntity firstAttendanceEntity = new AttendanceEntity();
        firstAttendanceEntity.setChildId("1");
        firstAttendanceEntity.setAttendanceDate(TimeUtil.parse("2022-06-02 08:00:00", TimeUtil.dateFormat));
        firstAttendanceDateList.add(firstAttendanceEntity);
        when(attendanceEntityDao.getFirstAttendanceDateByChildrenIds(anyList())).thenReturn(firstAttendanceDateList);

        List<AttendanceEntity> attendanceList = new ArrayList<>();
        AttendanceEntity attendanceEntity = new AttendanceEntity();
        attendanceEntity.setChildId("1");
        attendanceEntity.setCreateLocalDate(TimeUtil.localDateToDate(LocalDate.of(2022, 06, 02)));
        attendanceEntity.setType(CheckType.CHECK_IN.toString());
        attendanceEntity.setAttendanceDate(TimeUtil.parse("2022-06-02 08:00:00", TimeUtil.dateFormat));
        attendanceList.add(attendanceEntity);
        AttendanceEntity attendanceEntity1 = new AttendanceEntity();
        attendanceEntity1.setChildId("1");
        attendanceEntity1.setCreateLocalDate(TimeUtil.localDateToDate(LocalDate.of(2022, 06, 02)));
        attendanceEntity1.setType(CheckType.CHECK_OUT.toString());
        attendanceEntity1.setAttendanceDate(TimeUtil.parse("2022-06-02 10:00:00", TimeUtil.dateFormat));
        attendanceList.add(attendanceEntity1);
        Mockito.lenient().when(attendanceEntityDao.getAttendanceByChildrenAndDate(allChildIds, startDate.toString(), endDate.toString())).thenReturn(attendanceList);
        when(quarantineDaoImplSpy.lambdaQuery().list()).thenReturn(new ArrayList<>());
        List<EnrollmentModel> currentChildren = new ArrayList<>();
        EnrollmentModel enrollmentModel = new EnrollmentModel();
        enrollmentModel.setId("1");
        enrollmentModel.setDisplayName("qqq");
        currentChildren.add(enrollmentModel);
        when(studentDao.getChildrenWithInactiveByIds(allChildIds)).thenReturn(currentChildren);
        when(studentService.getIepAttrMapWithEnrollmentIds(anyList())).thenReturn(new HashMap<>());
        ListSignatureHistoryResponse response = attendanceService.listSignatureHistory(startDate, endDate, groupId);
        assertEquals("NORMAL", response.getChildSignatureHistories().get(0).getSignatureHistoryList().get(0).getStatus());
        // 清除 mockito 预设值，保障下个用例没有数据污染
        Mockito.reset(attendanceEntityDao);

        // 判断小孩这天状态为缺勤 （有考勤记录的）
        List<AttendanceEntity> attendanceList2 = new ArrayList<>();
        AttendanceEntity attendanceEntity2 = new AttendanceEntity();
        attendanceEntity2.setChildId("1");
        attendanceEntity2.setCreateLocalDate(TimeUtil.localDateToDate(LocalDate.of(2022, 06, 02)));
        attendanceEntity2.setType(CheckType.CHECK_IN.toString());
        attendanceList2.add(attendanceEntity2);
        Mockito.lenient().when(attendanceEntityDao.getAttendanceByChildrenAndDate(allChildIds, startDate.toString(), endDate.toString())).thenReturn(attendanceList2);
        ListSignatureHistoryResponse response2 = attendanceService.listSignatureHistory(startDate, endDate, groupId);
        assertEquals(AttendanceType.UNFILLED.toString(), response2.getChildSignatureHistories().get(0).getSignatureHistoryList().get(0).getStatus());
        // 清除 mockito 预设值，保障下个用例没有数据污染
        Mockito.reset(attendanceEntityDao);

        // 判断小孩这天状态为缺勤 （没考勤记录的）
        Mockito.lenient().when(attendanceEntityDao.getAttendanceByChildrenAndDate(allChildIds, startDate.toString(), endDate.toString())).thenReturn(new ArrayList<>());
        ListSignatureHistoryResponse response3 = attendanceService.listSignatureHistory(startDate, endDate, groupId);
        assertEquals(AttendanceType.UNFILLED.toString(), response3.getChildSignatureHistories().get(0).getSignatureHistoryList().get(0).getStatus());
        // 清除 mockito 预设值，保障下个用例没有数据污染
        Mockito.reset(quarantineDaoImplSpy);

        // 判断小孩这天状态为离园原因（不隔离）
        List<HealthQuarantineEntity> quarantines = new ArrayList<>();
        HealthQuarantineEntity healthQuarantineEntity = new HealthQuarantineEntity();
        healthQuarantineEntity.setTargetId("1");
        healthQuarantineEntity.setReason("no");
        healthQuarantineEntity.setStartDate(TimeUtil.localDateToDate(LocalDate.of(2022, 06, 01)));
        healthQuarantineEntity.setEndDate(TimeUtil.localDateToDate(LocalDate.of(2022, 06, 03)));
        quarantines.add(healthQuarantineEntity);
        when(quarantineDaoImplSpy.lambdaQuery().list()).thenReturn(quarantines);
        ListSignatureHistoryResponse response4 = attendanceService.listSignatureHistory(startDate, endDate, groupId);
        assertEquals(AttendanceType.ABSENT.toString(), response4.getChildSignatureHistories().get(0).getSignatureHistoryList().get(0).getStatus());
        assertEquals(false, response4.getChildSignatureHistories().get(0).getSignatureHistoryList().get(0).isQuarantine());
        // 清除 mockito 预设值，保障下个用例没有数据污染
        Mockito.reset(quarantineDaoImplSpy);

        // 判断小孩这天状态为离园原因（隔离）
        List<HealthQuarantineEntity> quarantines2 = new ArrayList<>();
        HealthQuarantineEntity healthQuarantineEntity2 = new HealthQuarantineEntity();
        healthQuarantineEntity2.setTargetId("1");
        healthQuarantineEntity2.setReason(ChildStatus.QUARANTINE.toString());
        healthQuarantineEntity2.setStartDate(TimeUtil.localDateToDate(LocalDate.of(2022, 06, 01)));
        healthQuarantineEntity2.setEndDate(TimeUtil.localDateToDate(LocalDate.of(2022, 06, 03)));
        quarantines2.add(healthQuarantineEntity2);
        when(quarantineDaoImplSpy.lambdaQuery().list()).thenReturn(quarantines2);
        ListSignatureHistoryResponse response5 = attendanceService.listSignatureHistory(startDate, endDate, groupId);
        assertEquals(AttendanceType.ABSENT.toString(), response5.getChildSignatureHistories().get(0).getSignatureHistoryList().get(0).getStatus());
        assertEquals(true, response5.getChildSignatureHistories().get(0).getSignatureHistoryList().get(0).isQuarantine());
        // 清除 mockito 预设值，保障下个用例没有数据污染
        Mockito.reset(attendanceEntityDao, quarantineDaoImplSpy);

        // 签到签退存在补签类型
        List<AttendanceEntity> attendanceList3 = new ArrayList<>();
        AttendanceEntity attendanceEntity3 = new AttendanceEntity();
        attendanceEntity3.setChildId("1");
        attendanceEntity3.setCreateLocalDate(TimeUtil.localDateToDate(LocalDate.of(2022, 06, 02)));
        attendanceEntity3.setType(CheckType.CHECK_IN.toString());
        attendanceEntity3.setAttendanceMethod(AttendanceMethodEnum.SUPPLEMENT.toString());
        attendanceList3.add(attendanceEntity3);
        when(quarantineDaoImplSpy.lambdaQuery().list()).thenReturn(new ArrayList<>());
        Mockito.lenient().when(attendanceEntityDao.getAttendanceByChildrenAndDate(allChildIds, startDate.toString(), endDate.toString())).thenReturn(attendanceList3);
        ListSignatureHistoryResponse response6 = attendanceService.listSignatureHistory(startDate, endDate, groupId);
        assertEquals(true, response6.getChildSignatureHistories().get(0).getSignatureHistoryList().get(0).isSupplement());
        // 清除 mockito 预设值，保障下个用例没有数据污染
        Mockito.reset(attendanceEntityDao);

        // 签到签退不存在补签类型
        List<AttendanceEntity> attendanceList4 = new ArrayList<>();
        AttendanceEntity attendanceEntity4 = new AttendanceEntity();
        attendanceEntity4.setChildId("1");
        attendanceEntity4.setCreateLocalDate(TimeUtil.localDateToDate(LocalDate.of(2022, 06, 02)));
        attendanceEntity4.setType(CheckType.CHECK_IN.toString());
        attendanceEntity4.setAttendanceMethod("11");
        attendanceList3.add(attendanceEntity4);
        Mockito.lenient().when(attendanceEntityDao.getAttendanceByChildrenAndDate(allChildIds, startDate.toString(), endDate.toString())).thenReturn(attendanceList4);
        ListSignatureHistoryResponse response7 = attendanceService.listSignatureHistory(startDate, endDate, groupId);
        assertEquals(false, response7.getChildSignatureHistories().get(0).getSignatureHistoryList().get(0).isSupplement());
        // 清除 mockito 预设值，保障下个用例没有数据污染
        Mockito.reset(attendanceEntityDao);
    }

    private AttendanceEntityDaoImpl getAttendanceEntityDaoSpy() {
        AttendanceEntityDaoImpl spy = spy(attendanceEntityDaoImpl);
        AttendanceMapper mapper = this.attendanceMapper;
        ReflectionTestUtils.setField(spy, "baseMapper", mapper);
        ReflectionTestUtils.setField(attendanceService, "attendanceEntityDao", spy);  // 注入属性
        final LambdaUpdateChainWrapper<AttendanceEntity> lambdaUpdate = new LambdaUpdateChainWrapper<>(mapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaUpdateChain(mapper)).thenReturn(lambdaUpdate);

        final LambdaQueryChainWrapper<AttendanceEntity> lambdaQuery = new LambdaQueryChainWrapper<>(mapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(mapper)).thenReturn(lambdaQuery);
        return spy;
    }

    private HealthQuarantineDaoImpl getHealthQuarantineDaoSpy() {
        HealthQuarantineDaoImpl spy = spy(quarantineDaoImpl);
        HealthQuarantineMapper mapper = this.quarantineMapper;
        ReflectionTestUtils.setField(spy, "baseMapper", mapper);
        ReflectionTestUtils.setField(attendanceService, "quarantineDao", spy);  // 注入属性
        ReflectionTestUtils.setField(attendanceService, "healthQuarantineDao", spy);  // 注入属性

        final LambdaUpdateChainWrapper<HealthQuarantineEntity> lambdaUpdate = new LambdaUpdateChainWrapper<>(mapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaUpdateChain(mapper)).thenReturn(lambdaUpdate);

        final LambdaQueryChainWrapper<HealthQuarantineEntity> lambdaQuery = new LambdaQueryChainWrapper<>(mapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(mapper)).thenReturn(lambdaQuery);
        return spy;
    }

    private HealthStatisticsHistoryDaoImpl getHealthStatisticsHistoryDaoSpy() {
        HealthStatisticsHistoryDaoImpl spy = spy(healthStatisticsHistoryDaoImpl);
        HealthStatisticsHistoryChildMapper mapper = this.healthStatisticsHistoryChildMapper;
        ReflectionTestUtils.setField(spy, "baseMapper", mapper);
        ReflectionTestUtils.setField(attendanceService, "healthStatisticsHistoryDao", spy);  // 注入属性

        final LambdaUpdateChainWrapper<HealthStatisticsHistoryChildEntity> lambdaUpdate = new LambdaUpdateChainWrapper<>(mapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaUpdateChain(mapper)).thenReturn(lambdaUpdate);

        final LambdaQueryChainWrapper<HealthStatisticsHistoryChildEntity> lambdaQuery = new LambdaQueryChainWrapper<>(mapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(mapper)).thenReturn(lambdaQuery);
        return spy;
    }

    @Test
    void listNeedSignatureParent() {
        // 请求准备
        String childId = "1";

        // 有未签到的补签
        List<AttendanceEntity> attendanceList = new ArrayList<>();
        AttendanceEntity attendanceEntity = new AttendanceEntity();
        attendanceEntity.setId("1");
        attendanceEntity.setParentId("1");
        attendanceList.add(attendanceEntity);
        when(attendanceEntityDao.getNoSignUrlAttendanceByChildIdAndMethodType(childId, AttendanceMethodEnum.SUPPLEMENT.toString())).thenReturn(attendanceList);
        List<String> parentIds = new ArrayList<>();
        parentIds.add("1");
        when(attendanceEntityDao.getParentsByIds(parentIds, childId)).thenReturn(null);
        attendanceService.listNeedSignatureParent(childId);
        Mockito.verify(attendanceEntityDao, times(1)).getParentsByIds(parentIds, childId);
        // 清除 mockito 预设值，保障下个用例没有数据污染
        Mockito.reset(attendanceEntityDao);

        // 无未签到的补签
        when(attendanceEntityDao.getNoSignUrlAttendanceByChildIdAndMethodType(childId, AttendanceMethodEnum.SUPPLEMENT.toString())).thenReturn(new ArrayList<>());
        attendanceService.listNeedSignatureParent(childId);
        List<String> parentIds2 = new ArrayList<>();
        Mockito.verify(attendanceEntityDao, times(0)).getParentsByIds(parentIds2, childId);
        // 清除 mockito 预设值，保障下个用例没有数据污染
        Mockito.reset(attendanceEntityDao);
    }

    @Test
    void listNeedSignatureChildren() {
        // 请求准备
        ListNeedSignatureChildrenRequest request = new ListNeedSignatureChildrenRequest();
        List<String> childs = new ArrayList<>();
        childs.add("1");
        childs.add("2");
        request.setChildIds(childs);

        // 前台没有传来了小孩 Id
        ListNeedSignatureChildrenRequest request2 = new ListNeedSignatureChildrenRequest();
        when(studentDao.getChildByParent(null)).thenReturn(new ArrayList<>());
        attendanceService.listNeedSignatureChildren(request2);
        Mockito.verify(studentDao, times(1)).getChildByParent(null);
        // 清除 mockito 预设值，保障下个用例没有数据污染
        Mockito.reset(studentDao);

        // 前台传来了小孩 Id
        List<AttendanceEntity> attendanceList = new ArrayList<>();
        AttendanceEntity attendanceEntity = new AttendanceEntity();
        attendanceEntity.setId("1");
        attendanceList.add(attendanceEntity);
        when(attendanceEntityDao
                .getNoSignUrlAttendancesByChildIdsAndCurrentParentIdAndMethodType
                        (childs, null, AttendanceMethodEnum.SUPPLEMENT.toString())).thenReturn(attendanceList);
        when(usersMetaDataDao.getMeta( null, UserMetaKey.SHOW_SUPPLEMENT.toString())).thenReturn(null);
        attendanceService.listNeedSignatureChildren(request);
        Mockito.verify(studentDao, times(0)).getChildByParent(null);
        // 清除 mockito 预设值，保障下个用例没有数据污染
        Mockito.reset(attendanceEntityDao, usersMetaDataDao);

        // 当前家长的小孩存在未签名的补签记录
        when(attendanceEntityDao
                .getNoSignUrlAttendancesByChildIdsAndCurrentParentIdAndMethodType
                        (childs, null, AttendanceMethodEnum.SUPPLEMENT.toString())).thenReturn(attendanceList);
        when(usersMetaDataDao.getMeta(null, UserMetaKey.SHOW_SUPPLEMENT.toString())).thenReturn(null);
        attendanceService.listNeedSignatureChildren(request);
        Mockito.verify(usersMetaDataDao, times(1)).getMeta(null, UserMetaKey.SHOW_SUPPLEMENT.toString());
        // 清除 mockito 预设值，保障下个用例没有数据污染
        Mockito.reset(attendanceEntityDao, usersMetaDataDao);

        // 当前家长的小孩不存在未签名的补签记录
        Mockito.lenient().when(attendanceEntityDao
                .getNoSignUrlAttendancesByChildIdsAndCurrentParentIdAndMethodType
                        (childs, null, AttendanceMethodEnum.SUPPLEMENT.toString())).thenReturn(null);
        Mockito.verify(usersMetaDataDao, times(0)).getMeta(null, UserMetaKey.SHOW_SUPPLEMENT.toString());
    }

    @Test
    void supplementParentSignature() {
        // 请求准备
        SupplementParentSignatureRequest request = new SupplementParentSignatureRequest();
        request.setChildId("1");
        request.setParentId("1");
        request.setSignUrl("1");

        // 有未签名记录
        List<AttendanceEntity> attendanceList = new ArrayList<>();
        AttendanceEntity attendanceEntity = new AttendanceEntity();
        attendanceEntity.setId("1");
        attendanceList.add(attendanceEntity);
        when(attendanceEntityDao.getNoSignUrlAttendancesByChildIdAndCurrentParentIdAndMethodType("1", "1", AttendanceMethodEnum.SUPPLEMENT.toString())).thenReturn(attendanceList);
        SupplementParentSignatureResponse response = attendanceService.supplementParentSignature(request);
        Mockito.verify(attendanceEntityDao, times(1)).updateBatchById(attendanceList);
        assertEquals(true, response.isSuccess());
        // 清除 mockito 预设值，保障下个用例没有数据污染
        Mockito.reset(attendanceEntityDao);

        // 没有未签名记录
        when(attendanceEntityDao.getNoSignUrlAttendancesByChildIdAndCurrentParentIdAndMethodType("1", "1", AttendanceMethodEnum.SUPPLEMENT.toString())).thenReturn(null);
        response = attendanceService.supplementParentSignature(request);
        Mockito.verify(attendanceEntityDao, times(0)).updateBatchById(attendanceList);
        assertEquals(false, response.isSuccess());
    }

    /**
     * 测试检查冲突接口
     */
    @Test
    void testCheckSignInOutTimeConflict() {
        // 请求准备
        BatchSignInOutRequest request = new BatchSignInOutRequest();
        request.getEnrollmentIds().add("C001");
        request.getEnrollmentIds().add("C002");
        request.getEnrollmentIds().add("C003");
        request.setSignUrl("S001");
        request.setAttendanceTime("2020-01-01 00:30:00.000");
        request.setGroupId("G001");
        request.setType(CheckType.CHECK_OUT.toString());
        Date date = TimeUtil.parse(request.getAttendanceTime(), TimeUtil.format10);

        List<AttendanceEntity> attendanceEntityList = new ArrayList<>();
        AttendanceEntity attendanceEntity = new AttendanceEntity();
        attendanceEntity.setId("A001");
        attendanceEntity.setStatus("PASS");
        attendanceEntity.setChildId("C001");
        attendanceEntity.setParentId("P001");
        attendanceEntity.setType(CheckType.CHECK_IN.toString());
        attendanceEntity.setCreateLocalDate(TimeUtil.parse("2020-01-01", TimeUtil.format10));
        attendanceEntity.setAttendanceDate(TimeUtil.parse("2020-01-01 00:00:00.000", TimeUtil.format2));

        AttendanceEntity attendanceEntity2 = new AttendanceEntity();
        attendanceEntity2.setId("A002");
        attendanceEntity2.setStatus("PASS");
        attendanceEntity2.setChildId("C001");
        attendanceEntity2.setType(CheckType.CHECK_OUT.toString());
        attendanceEntity2.setCreateLocalDate(TimeUtil.parse("2020-01-01", TimeUtil.format10));
        attendanceEntity2.setAttendanceDate(TimeUtil.parse("2020-01-01 01:00:00.000", TimeUtil.format2));

        AttendanceEntity attendanceEntity3 = new AttendanceEntity();
        attendanceEntity3.setId("A003");
        attendanceEntity3.setStatus("PASS");
        attendanceEntity3.setChildId("C001");
        attendanceEntity3.setType(CheckType.CHECK_IN.toString());
        attendanceEntity3.setCreateLocalDate(TimeUtil.parse("2020-01-01", TimeUtil.format10));
        attendanceEntity3.setAttendanceDate(TimeUtil.parse("2020-01-01 02:00:00.000", TimeUtil.format2));

        AttendanceEntity attendanceEntity4 = new AttendanceEntity();
        attendanceEntity4.setId("A004");
        attendanceEntity4.setStatus("PASS");
        attendanceEntity4.setChildId("C002");
        attendanceEntity4.setType(CheckType.CHECK_IN.toString());
        attendanceEntity4.setCreateLocalDate(TimeUtil.parse("2020-01-01", TimeUtil.format10));
        attendanceEntity4.setAttendanceDate(TimeUtil.parse("2020-01-01 03:00:00.000", TimeUtil.format2));

        attendanceEntityList.add(attendanceEntity);
        attendanceEntityList.add(attendanceEntity2);
        attendanceEntityList.add(attendanceEntity3);
        attendanceEntityList.add(attendanceEntity4);

        List<EnrollmentModel> enrollmentModelList = new ArrayList<>();
        EnrollmentModel enrollmentModel = new EnrollmentModel();
        enrollmentModel.setId("C001");
        enrollmentModel.setGroupId("G001");
        enrollmentModel.setFirstName("C001");
        enrollmentModel.setLastName("C001");

        EnrollmentModel enrollmentModel2 = new EnrollmentModel();
        enrollmentModel2.setId("C002");
        enrollmentModel2.setGroupId("G001");
        enrollmentModel2.setDisplayName("C002");

        EnrollmentModel enrollmentModel3 = new EnrollmentModel();
        enrollmentModel3.setId("C003");
        enrollmentModel3.setGroupId("G001");
        enrollmentModel3.setFirstName("C001");
        enrollmentModel3.setLastName("C001");

        enrollmentModelList.add(enrollmentModel);
        enrollmentModelList.add(enrollmentModel2);
        enrollmentModelList.add(enrollmentModel3);

        List<ParentDto> parentDtoList = new ArrayList<>();
        ParentDto parentDto = new ParentDto();
        parentDto.setId("P001");
        parentDto.setChildId("C001");
        parentDto.setDisplayName("P001");
        parentDtoList.add(parentDto);

        List<ParentDto> parentDtoList2 = new ArrayList<>();
        ParentDto parentDto2 = new ParentDto();
        parentDto2.setId("P002");
        parentDto2.setChildId("C001");
        parentDto2.setDisplayName("P002");
        parentDtoList2.add(parentDto2);

        // 模拟请求
        when(attendanceEntityDao.getPassAttendanceByChildIdsAndDate(request.getEnrollmentIds(), TimeUtil.format(date, TimeUtil.format10), "ASC")).thenReturn(attendanceEntityList);
        when(studentDao.getChildrenWithInactiveByIds(request.getEnrollmentIds())).thenReturn(enrollmentModelList);
        when(enrollmentDao.getParentInvitationsByChildIds(request.getEnrollmentIds())).thenReturn(parentDtoList);
        when(enrollmentDao.getParentCodeByChildIds(request.getEnrollmentIds())).thenReturn(parentDtoList);
        when(attendancePickUpUserDao.getPickUpUserByChildIds(request.getEnrollmentIds())).thenReturn(parentDtoList2);
        BatchSignInOutTimeConflictResponse response = attendanceService.checkSignInOutTimeConflict(request);
        assertEquals(response.getTimeConflictEnrollmentModels().size(), 1);
    }

    /**
     * 测试批量签到签退接口
     */
    @Test
    void testBatchSignInOut() {
        // 请求准备
        BatchSignInOutRequest request = new BatchSignInOutRequest();
        request.getEnrollmentIds().add("C001");
//        request.getEnrollmentIds().add("C002");
        request.getEnrollmentIds().add("C003");
        request.setSignUrl("S001");
        request.setAttendanceTime("2020-01-01 00:30:00.000");
        request.setGroupId("G001");
        request.setType(CheckType.CHECK_OUT.toString());
        Date date = TimeUtil.parse(request.getAttendanceTime(), TimeUtil.format10);

        List<AttendanceEntity> attendanceEntityList = new ArrayList<>();
        AttendanceEntity attendanceEntity = new AttendanceEntity();
        attendanceEntity.setId("A001");
        attendanceEntity.setStatus("PASS");
        attendanceEntity.setChildId("C001");
        attendanceEntity.setParentId("P001");
        attendanceEntity.setType(CheckType.CHECK_IN.toString());
        attendanceEntity.setCreateLocalDate(TimeUtil.parse("2020-01-01", TimeUtil.format10));
        attendanceEntity.setAttendanceDate(TimeUtil.parse("2020-01-01 00:00:00.000", TimeUtil.format2));

        AttendanceEntity attendanceEntity2 = new AttendanceEntity();
        attendanceEntity2.setId("A002");
        attendanceEntity2.setStatus("PASS");
        attendanceEntity2.setChildId("C001");
        attendanceEntity2.setType(CheckType.CHECK_OUT.toString());
        attendanceEntity2.setCreateLocalDate(TimeUtil.parse("2020-01-01", TimeUtil.format10));
        attendanceEntity2.setAttendanceDate(TimeUtil.parse("2020-01-01 01:00:00.000", TimeUtil.format2));

        AttendanceEntity attendanceEntity3 = new AttendanceEntity();
        attendanceEntity3.setId("A003");
        attendanceEntity3.setStatus("PASS");
        attendanceEntity3.setChildId("C001");
        attendanceEntity3.setType(CheckType.CHECK_IN.toString());
        attendanceEntity3.setCreateLocalDate(TimeUtil.parse("2020-01-01", TimeUtil.format10));
        attendanceEntity3.setAttendanceDate(TimeUtil.parse("2020-01-01 02:00:00.000", TimeUtil.format2));

        AttendanceEntity attendanceEntity4 = new AttendanceEntity();
        attendanceEntity4.setId("A004");
        attendanceEntity4.setStatus("PASS");
        attendanceEntity4.setChildId("C002");
        attendanceEntity4.setType(CheckType.CHECK_IN.toString());
        attendanceEntity4.setCreateLocalDate(TimeUtil.parse("2020-01-01", TimeUtil.format10));
        attendanceEntity4.setAttendanceDate(TimeUtil.parse("2020-01-01 03:00:00.000", TimeUtil.format2));

        attendanceEntityList.add(attendanceEntity);
        attendanceEntityList.add(attendanceEntity2);
        attendanceEntityList.add(attendanceEntity3);
//        attendanceEntityList.add(attendanceEntity4);

        List<AttendanceEntity> attendanceEntityList2 = new ArrayList<>();
        AttendanceEntity attendanceEntity5 = new AttendanceEntity();
        attendanceEntity5.setId("A005");
        attendanceEntity5.setStatus("PENDING");
        attendanceEntity5.setChildId("C003");
        attendanceEntity5.setType(CheckType.CHECK_IN.toString());
        attendanceEntity5.setCreateLocalDate(TimeUtil.parse("2020-01-01", TimeUtil.format10));
        attendanceEntity5.setAttendanceDate(TimeUtil.parse("2020-01-01 04:00:00.000", TimeUtil.format2));
        attendanceEntityList2.add(attendanceEntity5);

        com.learninggenie.common.data.entity.CenterEntity centerEntity = new com.learninggenie.common.data.entity.CenterEntity();
        centerEntity.setId("C01");
        centerEntity.setName("center");

        AgencyEntity agencyEntity = new AgencyEntity();
        agencyEntity.setId("A01");
        agencyEntity.setName("agency");

        // 模拟请求
//        when(attendanceEntityDao.getPassAttendanceByChildIdsAndDate(request.getEnrollmentIds(), TimeUtil.format(date, TimeUtil.format10), "desc")).thenReturn(attendanceEntityList);
        when(attendanceEntityDao.getChildrenPendingAttendanceByDate(request.getEnrollmentIds(), TimeUtil.parse(date, TimeUtil.format10))).thenReturn(attendanceEntityList2);
        when(centerDao.getCenterByGroupId(request.getGroupId())).thenReturn(centerEntity);
        when(agencyDao.getByCenterId(centerEntity.getId())).thenReturn(agencyEntity);
        SuccessResponse successResponse = attendanceService.batchSignInOut(request);
        assertEquals(successResponse.isSuccess(), true);
    }

    /**
     * 测试删除签到签退记录
     */
    @Test
    void testDeleteSignInOutRecord() {
        // 参数准备
        DeleteSignInOutRecordRequest request = new DeleteSignInOutRecordRequest();
        request.getIds().add("A001");

        List<AttendanceEntity> attendanceEntityList = new ArrayList<>();
        AttendanceEntity attendanceEntity = new AttendanceEntity();
        attendanceEntity.setId("A001");
        attendanceEntity.setChildId("C001");
        attendanceEntity.setAttendanceDate(TimeUtil.parse("2020-01-01 00:00:00.000", TimeUtil.format2));
        attendanceEntity.setHealthCheckResponseRecordId("H001");
        attendanceEntityList.add(attendanceEntity);

        List<FormsResponseRecord> fillInVHCRecords = new ArrayList<>();
        FormsResponseRecord formsResponseRecord = new FormsResponseRecord();
        formsResponseRecord.setType(FormResponseRecordType.HEALTH_CHECK_FORM.toString());
        formsResponseRecord.setDeleted(false);
        formsResponseRecord.setId("H001");
        formsResponseRecord.setChildId("C001");
        formsResponseRecord.setCreateLocalDate(TimeUtil.parse("2020-01-01", TimeUtil.format10));
        fillInVHCRecords.add(formsResponseRecord);

        List<String> childIds = new ArrayList<>();
        childIds.add("C001");
        String date = TimeUtil.format(attendanceEntity.getAttendanceDate(), TimeUtil.format10);
//        List<String> recordIds = new ArrayList<>();
//        recordIds.add("H001");

        // 模拟请求
        Mockito.when(attendanceEntityDao.getByIds(request.getIds())).thenReturn(attendanceEntityList);
        Mockito.when(formsResponseRecordDao.getRecordsByChildIdsAndDateAndType(childIds, date, FormResponseRecordType.HEALTH_CHECK_FORM.toString())).thenReturn(fillInVHCRecords);
//        Mockito.when(formsResponseRecordDao.batchDeleteFormRespondRecordByIds(recordIds)).thenReturn(void)
        attendanceService.deleteSignInOutRecord(request);
        Mockito.verify(formsResponseRecordDao, times(1)).batchDeleteFormRespondRecordByIds(any());
        Mockito.verify(attendanceEntityDao, times(1)).deleteAttendanceByIds(any());
    }

    @Test
    void testUpdateSignInOutRecord() {
        // 参数准备
        UpdateSignInOutRecordRequest request = new UpdateSignInOutRecordRequest();
        request.setId("A001");
        request.setParentSignature("S001");
        request.setParentId("P001");

        AttendanceEntity attendanceEntity = new AttendanceEntity();
        attendanceEntity.setId("A001");
        attendanceEntity.setStatus("PASS");
        attendanceEntity.setChildId("C001");
        attendanceEntity.setParentId("P001");
        attendanceEntity.setType(CheckType.CHECK_IN.toString());
        attendanceEntity.setCreateLocalDate(TimeUtil.parse("2020-01-01", TimeUtil.format10));
        attendanceEntity.setAttendanceDate(TimeUtil.parse("2020-01-01 00:00:00.000", TimeUtil.format2));

        // 模拟接口
        when(attendanceEntityDao.getById(request.getId())).thenReturn(attendanceEntity);
        attendanceService.updateSignInOutRecord(request);
        Mockito.verify(attendanceEntityDao, times(1)).updateAttendanceParentSignatureAndParentId(attendanceEntity);
    }

    /**
     * 测试下载
     * case: 下载 DOWNLOAD_ATTENDANCE_STATISTICS 类型的文件
     */
    @Test
    void downLoadStatistics() throws IOException {
        // 请求参数准备
        AttendancesStatisticsRequest request = new AttendancesStatisticsRequest();
        request.setCenterIds(Arrays.asList("CenterId001","CenterId002")); // 设置学校 Id
        request.setGroupIds(Arrays.asList("GroupId001","GroupId002")); // 设置班级 Id
        request.setDownLoadFileType("statisticsExcel"); // 设置下载文件类型
        request.setFromDate("2023-10-01"); // 设置开始时间
        request.setToDate("2023-10-31"); // 设置结束时间
        request.setType("DOWNLOAD_ATTENDANCE_STATISTICS"); // 设置下载类型
        request.setWebDownload(true); // 设置是否是 web 下载
        // 模拟开通功能学校班级数据
        List<CenterModel> openHealthCardGroup = new ArrayList<>();
        CenterModel centerModel = new CenterModel(); // 创建学校数据
        centerModel.setId("CenterId001"); // 设置学校 Id
        centerModel.setName("CenterName001"); // 设置学校名称
        GroupModel groupModel = new GroupModel(); // 创建班级数据
        groupModel.setId("GroupId001"); // 设置班级 Id
        groupModel.setName("GroupName001"); // 设置班级名称
        groupModel.setSelect(true); // 设置班级是否选中
        centerModel.getGroups().add(groupModel); // 将班级数据添加到学校数据中
        openHealthCardGroup.add(centerModel); // 将学校数据添加到开通功能学校班级数据中
        CenterModel centerModel2 = new CenterModel(); // 创建学校数据
        centerModel2.setId("CenterId002"); // 设置学校 Id
        centerModel2.setName("CenterName002"); // 设置学校名称
        GroupModel groupModel2 = new GroupModel(); // 创建班级数据
        groupModel2.setId("GroupId002"); // 设置班级 Id
        groupModel2.setName("GroupName002"); // 设置班级名称
        groupModel2.setSelect(true); // 设置班级是否选中
        centerModel2.getGroups().add(groupModel2); // 将班级数据添加到学校数据中
        openHealthCardGroup.add(centerModel2); // 将学校数据添加到开通功能学校班级数据中
        when(healthCheckService.getOpenHealthCardGroup()).thenReturn(openHealthCardGroup); // 模拟开通功能学校班级数据
        // 模拟通过班级获取小孩方法
        List<EnrollmentEntity> enrollmentEntityList = new ArrayList<>(); // 创建小孩列表数据
        EnrollmentEntity enrollmentEntity = new EnrollmentEntity(); // 创建小孩数据
        enrollmentEntity.setId("ChildId001"); // 设置小孩 Id
        enrollmentEntityList.add(enrollmentEntity); // 将小孩数据添加到小孩列表数据中
        EnrollmentEntity enrollmentEntity2 = new EnrollmentEntity(); // 创建小孩数据
        enrollmentEntity2.setId("ChildId002"); // 设置小孩 Id
        enrollmentEntityList.add(enrollmentEntity2); // 将小孩数据添加到小孩列表数据中
        when(studentDao.getChildrenByGroupIds(any())).thenReturn(enrollmentEntityList); // 模拟通过班级获取小孩方法
        // 模拟通过小孩 Id 获取小孩信息
        List<EnrollmentModel> enrollmentModelList = new ArrayList<>(); // 创建小孩列表数据
        EnrollmentModel enrollmentModel = new EnrollmentModel(); // 创建小孩数据
        enrollmentModel.setId("ChildId001"); // 设置小孩 Id
        enrollmentModel.setDisplayName("ChildName001"); // 设置小孩名称
        enrollmentModel.setCenterId("CenterId001"); // 设置学校 Id
        enrollmentModel.setCenterName("CenterName001"); // 设置学校名称
        enrollmentModel.setGroupId("GroupId001"); // 设置班级 Id
        enrollmentModel.setGroupName("GroupName001"); // 设置班级名称
        enrollmentModelList.add(enrollmentModel); // 将小孩数据添加到小孩列表数据中
        EnrollmentModel enrollmentModel2 = new EnrollmentModel(); // 创建小孩数据
        enrollmentModel2.setId("ChildId002"); // 设置小孩 Id
        enrollmentModel2.setDisplayName("ChildName002"); // 设置小孩名称
        enrollmentModel2.setCenterId("CenterId002"); // 设置学校 Id
        enrollmentModel2.setCenterName("CenterName002"); // 设置学校名称
        enrollmentModel2.setGroupId("GroupId002"); // 设置班级 Id
        enrollmentModel2.setGroupName("GroupName002"); // 设置班级名称
        enrollmentModelList.add(enrollmentModel2); // 将小孩数据添加到小孩列表数据中
        when(studentDao.getChildrenByIds(any())).thenReturn(enrollmentModelList); // 模拟通过小孩 Id 获取小孩信息
        // 模拟获取当前用户 Id 方法
        when(userProvider.getCurrentUserId()).thenReturn("UserId001"); // 模拟获取当前用户 Id 方法
        // 模拟获取当前用户所属机构方法
        AgencyModel agency = new AgencyModel(); // 创建机构数据
        agency.setId("AgencyId001"); // 设置机构 Id
        agency.setName("AgencyName001"); // 设置机构名称
        when(userProvider.getAgencyByUserId(any())).thenReturn(agency); // 模拟获取当前用户所属机构方法
        // 模拟获取机构打开健康卡班级元数据方法
        AgencyMetaDataEntity meta = new AgencyMetaDataEntity(); // 创建机构元数据数据
        meta.setMetaKey(AgencyMetaKey.HEALTH_CARD_OPEN_GROUP.toString()); // 设置机构元数据 Key
        meta.setMetaValue("GroupId001,GroupId002"); // 设置机构元数据 Value
        when(agencyDao.getMeta(agency.getId(), AgencyMetaKey.HEALTH_CARD_OPEN_GROUP.toString())).thenReturn(meta); // 模拟获取机构打开健康卡班级元数据方法
        List<HealthQuarantineEntity> quarantines = new ArrayList<>(); // 创建隔离数据
        HealthQuarantineEntity quarantine = new HealthQuarantineEntity(); // 创建隔离数据
        quarantine.setId("QuarantineId001"); // 设置隔离 Id
        quarantine.setTargetId("ChildId001"); // 设置隔离对象 Id
        quarantine.setType("CHILD"); // 设置隔离对象类型
        quarantine.setReason("QUARANTINE"); // 设置隔离原因
        quarantine.setStartDate(TimeUtil.parseDate("2023-10-01")); // 设置隔离开始时间
        quarantine.setEndDate(TimeUtil.parseDate("2023-10-01")); // 设置隔离结束时间
        quarantines.add(quarantine); // 将隔离数据添加到隔离列表数据中
        when(quarantineDao.getQuarantineByTargetAndTypeAndDateRange(any(), any(), any(), any())).thenReturn(quarantines); // 模拟获取隔离数据方法
        // 模拟获取小孩出勤记录方法
        List<AttendanceStatisticsEntityOfExcel> presentCount = new ArrayList<>(); // 创建小孩出勤记录列表数据
        AttendanceStatisticsEntityOfExcel attendanceStatisticsEntityOfExcel = new AttendanceStatisticsEntityOfExcel(); // 创建小孩出勤记录数据
        attendanceStatisticsEntityOfExcel.setChildId("ChildId001"); // 设置小孩 Id
        attendanceStatisticsEntityOfExcel.setCenterName("CenterName001"); // 设置学校名称
        attendanceStatisticsEntityOfExcel.setClassName("GroupName001"); // 设置班级名称
        attendanceStatisticsEntityOfExcel.setChildName("ChildName001"); // 设置小孩名称
        attendanceStatisticsEntityOfExcel.setMonth("2023-10"); // 设置月份
        attendanceStatisticsEntityOfExcel.setPresent(20); // 设置出勤天数
        when(attendanceEntityDao.getChildsPresentCount(any(), any(), any())).thenReturn(presentCount); // 模拟获取小孩出勤记录方法
        // 初始化静态参数
        ReflectionTestUtils.setField(attendanceService, "s3Root", "https://s3.amazonaws.com/"); // 初始化 s3Root 值
        ReflectionTestUtils.setField(attendanceService, "s3BucketName", "cn.learninggenie.prod"); // 初始化 s3BucketName 值
        // 模拟上传文件方法
        fileUtil.when(() -> FileUploadUtil.upload(anyString(), any(), anyString(), any())).thenReturn("Monthly_Attendance_Statistics.xlsx");
        // 调取测试方法
        AttendanceExcelResponse response = attendanceService.downLoadStatistics(request);

        // 验证返回值
        assertEquals("https://s3.amazonaws.com/Monthly_Attendance_Statistics.xlsx", response.getUrl()); // 验证上传文件路径是否与预期一致
        assertEquals("Monthly_Attendance_Statistics", response.getFileName()); // 验证上传文件名称是否与预期一致
        assertEquals("10/01/2023 - 10/31/2023", response.getDateStr()); // 验证上传文件日期是否与预期一致
        assertEquals("UserId001", response.getUserId()); // 验证上传文件创建人是否与预期一致
    }

    /**
     * 测试处理考勤统计表头
     */
    @Test
    public void testProcessAttendanceHeader_DOWNLOAD_CENTER() {
        // 创建测试数据
        AttendancesStatisticsRequest request = new AttendancesStatisticsRequest();
        request.setType("DOWNLOAD_CENTER"); // 替换为实际的下载类型

        AttendanceHeader header = new AttendanceHeader();

        AgencyMetaDataEntity attendanceTeacherTime = new AgencyMetaDataEntity();
        attendanceTeacherTime.setMetaValue("true"); // 替换为实际的metaValue

        // 调用方法
        attendanceService.processAttendanceHeader(request, header, attendanceTeacherTime);

        // 进行断言
        assertTrue(header.getSignInOutTeacherFirst());
        assertFalse(header.getSignInOutShow());
    }

    /**
     * 测试处理考勤统计表头
     */
    @Test
    public void testProcessAttendanceHeader_DOWNLOAD_GROUP() {
        // 创建测试数据
        AttendancesStatisticsRequest request = new AttendancesStatisticsRequest();
        request.setType("DOWNLOAD_GROUP"); // 替换为实际的下载类型

        AttendanceHeader header = new AttendanceHeader();

        AgencyMetaDataEntity attendanceTeacherTime = new AgencyMetaDataEntity();
        attendanceTeacherTime.setMetaValue("true"); // 替换为实际的metaValue

        // 调用方法
        attendanceService.processAttendanceHeader(request, header, attendanceTeacherTime);

        // 进行断言
        assertTrue(header.getSignInOutTeacherFirst());
        assertFalse(header.getSignInOutShow());
    }

    /**
     * 测试处理考勤统计表头
     */
    @Test
    public void testProcessAttendanceHeader() {
        // 创建测试数据
        AttendancesStatisticsRequest request = new AttendancesStatisticsRequest();
        request.setType("DOWNLOAD_CHILDREN"); // 替换为实际的下载类型

        AttendanceHeader header = new AttendanceHeader();

        AgencyMetaDataEntity attendanceTeacherTime = new AgencyMetaDataEntity();
        attendanceTeacherTime.setMetaValue("true"); // 替换为实际的metaValue

        // 调用方法
        attendanceService.processAttendanceHeader(request, header, attendanceTeacherTime);

        // 进行断言
        assertTrue(header.getSignInOutTeacherFirst());
        assertTrue(header.getSignInOutShow());
    }

    /**
     * 测试获取考勤统计数据
     */
    @Test
    public void testGetAttendanceStatisticsData() {
        // 准备 AttendancesStatisticsRequest 数据
        AttendancesStatisticsRequest request = new AttendancesStatisticsRequest();
        request.setDownLoadFileType("pdf");
        String from = "2023-09-01";
        request.setFromDate(from);
        String to = "2023-09-30";
        request.setToDate(to);

        Date fromDate = TimeUtil.parseDate(from);
        Date toDate = TimeUtil.parseDate(to);
        request.setType("DOWNLOAD_CHILD");
        request.setAgencyId("AgencyId");
        List<String> groupIds = Collections.singletonList("GroupId");
        request.setGroupIds(groupIds);
        request.setWebDownload(true);
        List<String> childIds = Collections.singletonList("ChildId");
        request.setChildIds(childIds);

        String currentChild = "ChildId";

        List<HealthStatisticsHistoryChildEntity> childHistory = new ArrayList<>();
        HealthStatisticsHistoryChildEntity entity = new HealthStatisticsHistoryChildEntity();
        entity.setLocalDate(LocalDate.of(2022, 06, 02));
        entity.setEnrollmentIds("1");
        childHistory.add(entity);
        HealthStatisticsHistoryDaoImpl healthStatisticsHistoryDaoSpy = this.getHealthStatisticsHistoryDaoSpy();
        when(healthStatisticsHistoryDaoSpy.lambdaQuery()
                .select(HealthStatisticsHistoryChildEntity::getEnrollmentIds, HealthStatisticsHistoryChildEntity::getGroupId, HealthStatisticsHistoryChildEntity::getLocalDate) // 查询小孩 Ids，日期字段
                .in(HealthStatisticsHistoryChildEntity::getGroupId, groupIds) // 过滤班级
                .between(HealthStatisticsHistoryChildEntity::getLocalDate,
                        TimeUtil.parseLocalDate(fromDate).toString(),
                        TimeUtil.parseLocalDate(toDate).toString()) // 过滤日期
                .eq(HealthStatisticsHistoryChildEntity::getIsDeleted, false) // 过滤删除
                .list()).thenReturn(childHistory);

        // mock data GroupEntity
        List<com.learninggenie.common.data.model.GroupEntity> groupEntities = new ArrayList<>();
        com.learninggenie.common.data.model.GroupEntity groupEntity = new com.learninggenie.common.data.model.GroupEntity();
        groupEntity.setCenterId("CenterId");
        groupEntity.setId("GroupId");
        groupEntity.setName("GroupName");
        groupEntity.setMedia(new MediaEntity());
        groupEntity.setCreatedUtc("2020-09-01");
        com.learninggenie.common.data.model.CenterEntity centerEntity = new com.learninggenie.common.data.model.CenterEntity("CenterId", "CenterName");
        groupEntity.setCenter(centerEntity);
        DomainEntity domainEntity = new DomainEntity("DomainId", "DomainName", "DomainAbbr", "DomainMeasure", "DomainIcon", "DomainParentId", 1);
        groupEntity.setDomain(domainEntity);
        groupEntity.setStage(new StageEntity());
        groupEntity.setTeachers(Lists.newArrayList());
        groupEntity.setChilds(Lists.newArrayList());
        groupEntity.setGroupsMetaDataEntities(Lists.newArrayList());
        groupEntity.setIsDeleted(false);
        groupEntity.setInactive(false);
        groupEntity.setTraining(false);
        groupEntity.setIconPath("IconPath");
        groupEntity.setClassDomain(domainEntity);
        groupEntity.setProgramDomain(domainEntity);
        groupEntity.setCreateAtUtc(new Date());
        groupEntity.setUpdateAtUtc(new Date());
        groupEntity.setChildCount(0);
        groupEntity.setGroupInvitationEntities(Lists.newArrayList());
        groupEntity.setEnrollments(Sets.newHashSet());
        groupEntity.setFrameworkId(domainEntity.getId());
        groupEntity.setFrameworkName(domainEntity.getName());
        groupEntity.setCenterName(centerEntity.getName());
        groupEntities.add(groupEntity);

        when(groupDao.getGroupWithCenterByGroupIds(groupIds)).thenReturn(groupEntities);
        // 获取 user
        String userId = "UserId";
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("AgencyId");
        agencyModel.setName("AgencyName");

        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);

        // Mock student metadata
        String externalId = "External ID";
        ArrayList<EnrollmentMetaDataEntity> enrollmentMetaDataEntities = new ArrayList<>();
        EnrollmentMetaDataEntity enrollmentMetaDataEntity = new EnrollmentMetaDataEntity();
        enrollmentMetaDataEntity.setChildId(childIds.get(0));
        enrollmentMetaDataEntity.setUserId(userId);
        enrollmentMetaDataEntity.setGroupId(groupIds.get(0));
        enrollmentMetaDataEntity.setId("MetaId");
        enrollmentMetaDataEntity.setMetaKey(externalId);
        enrollmentMetaDataEntity.setMetaValue("ExternalId");
        enrollmentMetaDataEntities.add(enrollmentMetaDataEntity);

        when(studentDao.getMetasByChildIds(anyList(), eq(externalId))).thenReturn(enrollmentMetaDataEntities);
        // Mock student
        ArrayList<EnrollmentModel> enrollmentModels = new ArrayList<>();
        EnrollmentModel enrollmentModel = new EnrollmentModel();
        enrollmentModel.setGroupDomainId(domainEntity.getId());
        enrollmentModel.setChildName("ChildName");
        enrollmentModel.setId("ChildId");
        enrollmentModel.setFirstName("ChildFirstName");
        enrollmentModel.setLastName("ChildLastName");
        enrollmentModel.setMiddleName("ChildMiddleName");
        enrollmentModel.setDisplayName("ChildDisplayName");
        enrollmentModel.setAvatarMediaId("AvatarMediaId");
        enrollmentModel.setAvatarUrl("AvatarUrl");
        enrollmentModel.setGroupId(groupEntity.getId());
        enrollmentModel.setGroupName(groupEntity.getName());
        enrollmentModel.setLastGroupName(groupEntity.getName());
        enrollmentModel.setLastCenterName(centerEntity.getName());
        enrollmentModel.setCenterName(centerEntity.getName());
        enrollmentModel.setCenterId(centerEntity.getId());
        enrollmentModel.setBirthDate("02/18/2022");
        enrollmentModel.setGender("Female");
        enrollmentModel.setEnrollmentDate("02/18/2022");
        enrollmentModel.setWithdrawnDate("02/18/2022");
        enrollmentModels.add(enrollmentModel);
        when(studentDao.getChildrenWithInactiveByIds(anyList())).thenReturn(enrollmentModels);

        when(studentDao.getChildrenByIds(anyList())).thenReturn(enrollmentModels);

        // Mock attendance
        ArrayList<AttendanceEntity> attendanceEntities = new ArrayList<>();
        AttendanceEntity attendanceEntity = new AttendanceEntity();
        attendanceEntity.setHealthCheckDate(TimeUtil.getUtcNow());
        attendanceEntity.setHealthCheckApproveUserId("HealthCheckApproveUserId");
        attendanceEntity.setHealthCheckResponseRecordId("HealthCheckResponseRecordId");
        String dlSignUrl = "DlSignUrl";
        attendanceEntity.setDlSignUrl(dlSignUrl);
        attendanceEntity.setTakeUrl("TakeUrl");
        attendanceEntity.setApproveUserDisplayName("ApproveUserDisplayName");
        attendanceEntity.setCreateLocalDate(TimeUtil.getUtcNow());
        attendanceEntity.setAgencyId(agencyModel.getId());
        attendanceEntity.setCenterId(centerEntity.getId());
        attendanceEntity.setGroupId(groupEntity.getId());
        attendanceEntity.setId("AttendanceId");
        attendanceEntity.setChildId(currentChild);
        String parentId = "ParentId";
        attendanceEntity.setParentId(parentId);
        attendanceEntity.setParentResponseRecordId("ParentResponseRecordId");
        attendanceEntity.setTeacherResponseRecordId("TeacherResponseRecordId");
        attendanceEntity.setType("Normal");
        attendanceEntity.setStatus("Pending");
        attendanceEntity.setAttendanceMethod("AttendanceMethod");
        attendanceEntity.setApproveUserId("ApproveUserId");
        attendanceEntity.setAttendanceDate(TimeUtil.getUtcNow());
        attendanceEntity.setSignUrl("SignUrl");
        attendanceEntity.setRemark("Remark");
        attendanceEntity.setCreateAtUtc(TimeUtil.getUtcNow());
        attendanceEntity.setUpdateAtUtc(TimeUtil.getUtcNow());
        attendanceEntity.setDeleted(false);

        attendanceEntities.add(attendanceEntity);
        when(attendanceEntityDao.getAttendanceByGroupsAndDate(anyList(), anyString(), anyString())).thenReturn(attendanceEntities);
        when(attendanceEntityDao.getAttendanceByChildrenAndDate(anyList(), anyString(), anyString())).thenReturn(attendanceEntities);

        ArrayList<GroupEntity> groupEntityArrayList = new ArrayList<>();
        GroupEntity group = new GroupEntity();
        group.setId(groupEntity.getId());

        GroupEntity group2 = new GroupEntity();
        group.setId("extendGroupId");
        groupEntityArrayList.add(group);
        groupEntityArrayList.add(group2);

        // Mock user data
        ArrayList<UserEnrollmentModel> userEnrollmentModelArrayList = new ArrayList<>();
        UserEnrollmentModel userEnrollmentModel = new UserEnrollmentModel();
        userEnrollmentModel.setUserId(userId);
        userEnrollmentModel.setEnrollmentId(enrollmentModel.getId());
        String parentName = "ParentName";
        userEnrollmentModel.setParentName(parentName);
        userEnrollmentModel.setUserRelationship("Mather");
        userEnrollmentModel.setUserAvatar("UserAvatar");

        userEnrollmentModelArrayList.add(userEnrollmentModel);
        when(userDao.getUserEnrollmentByGroups(anyList())).thenReturn(userEnrollmentModelArrayList);
        when(userDao.getPickupUserEnrollmentByGroups(anyList())).thenReturn(userEnrollmentModelArrayList);

        // Mock quarantine
        ArrayList<HealthQuarantineEntity> healthQuarantineEntities = new ArrayList<>();
        HealthQuarantineEntity healthQuarantineEntity = new HealthQuarantineEntity();
        healthQuarantineEntity.setExcuseod(false);
        healthQuarantineEntity.setDeleted(false);
        healthQuarantineEntity.setId("QuarantineId");
        healthQuarantineEntity.setTargetId(currentChild);
        healthQuarantineEntity.setRole("QuarantineRole");
        healthQuarantineEntity.setType("QuarantineType");
        healthQuarantineEntity.setNote("QuarantineNote");
        String quarantineReason = "QuarantineReason";
        healthQuarantineEntity.setReason(quarantineReason);
        healthQuarantineEntity.setHealthStatus("QuarantineIdStatus");
        healthQuarantineEntity.setIsExcuseod(false);
        healthQuarantineEntity.setStartDate(new Date());
        healthQuarantineEntity.setEndDate(new Date());
        healthQuarantineEntity.setCreateUserId(userId);
        healthQuarantineEntity.setUpdateUserId(userId);
        healthQuarantineEntity.setCreateAtUtc(new Date());
        healthQuarantineEntity.setUpdateAtUtc(new Date());
        healthQuarantineEntity.setIsDeleted(false);
        healthQuarantineEntity.setQuarantineReason(quarantineReason);
        healthQuarantineEntity.setDateMonth("9");
        healthQuarantineEntities.add(healthQuarantineEntity);

        when(quarantineDao.getQuarantineByTargetAndTypeAndDateRange(anyList(), anyString(), anyString(), anyString())).thenReturn(healthQuarantineEntities);
        // Mock Form
        ArrayList<FormsResponseRecord> formsResponseRecords = new ArrayList<>();
        FormsResponseRecord formsResponseRecord = new FormsResponseRecord();
        formsResponseRecord.setArchive(false);
        formsResponseRecord.setDeleted(false);
        formsResponseRecord.setConsultParent(false);
        formsResponseRecord.setId("formsResponseRecordId");
        formsResponseRecord.setCreateUserId(userId);
        formsResponseRecord.setRole("Role");
        formsResponseRecord.setFormId("FormId");
        formsResponseRecord.setCreateAtUtc(new Date());
        formsResponseRecord.setCreateAtLocal(new Date());
        formsResponseRecord.setCreateLocalDate(new Date());
        formsResponseRecord.setRemark("Remark");
        formsResponseRecord.setStatus("Status");
        formsResponseRecord.setIsArchive(false);
        formsResponseRecord.setIsDeleted(false);
        formsResponseRecord.setUpdateAtUtc(new Date());
        formsResponseRecord.setUpdateAtLocal(new Date());
        formsResponseRecord.setChildId(currentChild);
        formsResponseRecord.setIsConsultParent(false);
        formsResponseRecord.setCreateDateStr(TimeUtil.getUTCTimeStr());
        formsResponseRecord.setAgencyId(agencyModel.getId());
        formsResponseRecord.setCenterId(centerEntity.getId());
        formsResponseRecord.setGroupId(groupEntity.getId());
        formsResponseRecord.setType("Type");
        formsResponseRecord.setResponseData(new byte[]{1, 2, 3, 4, 5});
        formsResponseRecord.setDlSignUrl(dlSignUrl);
        formsResponseRecord.setParentStatus("ParentStatus");
        formsResponseRecord.setQuarantine(false);
        formsResponseRecord.setAlertStatus("AlertStatus");
        formsResponseRecords.add(formsResponseRecord);

        when(formsResponseRecordDao.getDLSignByDateRole(anyList(), anyString(), anyString(), anyString())).thenReturn(formsResponseRecords);
        AttendanceStatisticsData statisticsData = attendanceService.getAttendanceStatisticsData(request, currentChild);
        assertTrue(statisticsData.isStudentIdShow());
        assertFalse(statisticsData.isAbbreviationShow());
        assertFalse(statisticsData.isRemarkShow());
        assertEquals(statisticsData.getTable().size(), 0);
        assertNull(statisticsData.getHeader());
        assertNull(statisticsData.getAllChild());
        assertEquals(statisticsData.getStatisticsDailyData().size(), 30);
        assertEquals(statisticsData.getAgencyName(), agencyModel.getName());
        assertEquals(statisticsData.getCenterNames().size(), 1);
        assertEquals(statisticsData.getGroupNames().size(), 1);
        assertEquals(statisticsData.getDateRange(), "09/01/2023 - 09/30/2023");
        assertEquals(statisticsData.getReasonList().size(), 0);
    }

    /**
     * 测试下载
     * case: 下载 DOWNLOAD_CENTER 类型的文件
     */
    @Test
    void downLoadStatistics_DOWNLOAD_CENTER() throws IOException {
        // 请求参数准备
        AttendancesStatisticsRequest request = new AttendancesStatisticsRequest();
        request.setCenterIds(Arrays.asList("CenterId001", "CenterId002")); // 设置学校 Id
        request.setGroupIds(Arrays.asList("GroupId001", "GroupId002")); // 设置班级 Id
        request.setDownLoadFileType("pdf"); // 设置下载文件类型
        request.setFromDate("2023-10-01"); // 设置开始时间
        request.setToDate("2023-10-31"); // 设置结束时间
        request.setType("DOWNLOAD_CENTER"); // 设置下载类型
        request.setWebDownload(true); // 设置是否是 web 下载
        List<HealthStatisticsHistoryChildEntity> childHistory = new ArrayList<>();
        HealthStatisticsHistoryChildEntity entity = new HealthStatisticsHistoryChildEntity();
        entity.setLocalDate(LocalDate.of(2022, 06, 02));
        entity.setEnrollmentIds("1");
        childHistory.add(entity);
        HealthStatisticsHistoryDaoImpl healthStatisticsHistoryDaoSpy = this.getHealthStatisticsHistoryDaoSpy();
        when(healthStatisticsHistoryDaoSpy.lambdaQuery()
                .select(HealthStatisticsHistoryChildEntity::getEnrollmentIds, HealthStatisticsHistoryChildEntity::getGroupId, HealthStatisticsHistoryChildEntity::getLocalDate) // 查询小孩 Ids，日期字段
                .in(HealthStatisticsHistoryChildEntity::getGroupId, request.getGroupIds()) // 过滤班级
                .between(HealthStatisticsHistoryChildEntity::getLocalDate,
                        TimeUtil.parseLocalDate(TimeUtil.parseDate(request.getFromDate())).toString(),
                        TimeUtil.parseLocalDate(TimeUtil.parseDate(request.getToDate())).toString()) // 过滤日期
                .eq(HealthStatisticsHistoryChildEntity::getIsDeleted, false) // 过滤删除
                .list()).thenReturn(childHistory);
        HealthQuarantineDaoImpl quarantineDaoImplSpy = this.getHealthQuarantineDaoSpy();

        // 模拟通过班级获取小孩方法
        List<EnrollmentEntity> enrollmentEntityList = new ArrayList<>(); // 创建小孩列表数据
        EnrollmentEntity enrollmentEntity = new EnrollmentEntity(); // 创建小孩数据
        enrollmentEntity.setId("ChildId001"); // 设置小孩 Id
        enrollmentEntityList.add(enrollmentEntity); // 将小孩数据添加到小孩列表数据中
        EnrollmentEntity enrollmentEntity2 = new EnrollmentEntity(); // 创建小孩数据
        enrollmentEntity2.setId("ChildId002"); // 设置小孩 Id
        enrollmentEntityList.add(enrollmentEntity2); // 将小孩数据添加到小孩列表数据中
        when(studentDao.getChildrenByGroupIds(any())).thenReturn(enrollmentEntityList); // 模拟通过班级获取小孩方法
        // 模拟通过小孩 Id 获取小孩信息
        List<EnrollmentModel> enrollmentModelList = new ArrayList<>(); // 创建小孩列表数据
        EnrollmentModel enrollmentModel = new EnrollmentModel(); // 创建小孩数据
        enrollmentModel.setId("ChildId001"); // 设置小孩 Id
        enrollmentModel.setDisplayName("ChildName001"); // 设置小孩名称
        enrollmentModel.setCenterId("CenterId001"); // 设置学校 Id
        enrollmentModel.setCenterName("CenterName001"); // 设置学校名称
        enrollmentModel.setGroupId("GroupId001"); // 设置班级 Id
        enrollmentModel.setGroupName("GroupName001"); // 设置班级名称
        enrollmentModelList.add(enrollmentModel); // 将小孩数据添加到小孩列表数据中
        EnrollmentModel enrollmentModel2 = new EnrollmentModel(); // 创建小孩数据
        enrollmentModel2.setId("ChildId002"); // 设置小孩 Id
        enrollmentModel2.setDisplayName("ChildName002"); // 设置小孩名称
        enrollmentModel2.setCenterId("CenterId002"); // 设置学校 Id
        enrollmentModel2.setCenterName("CenterName002"); // 设置学校名称
        enrollmentModel2.setGroupId("GroupId002"); // 设置班级 Id
        enrollmentModel2.setGroupName("GroupName002"); // 设置班级名称
        enrollmentModelList.add(enrollmentModel2); // 将小孩数据添加到小孩列表数据中
        when(studentDao.getChildrenByIds(any())).thenReturn(enrollmentModelList); // 模拟通过小孩 Id 获取小孩信息
        // 模拟获取当前用户 Id 方法
        when(userProvider.getCurrentUserId()).thenReturn("UserId001"); // 模拟获取当前用户 Id 方法
        // 模拟获取当前用户所属机构方法
        AgencyModel agency = new AgencyModel(); // 创建机构数据
        agency.setId("AgencyId001"); // 设置机构 Id
        agency.setName("AgencyName001"); // 设置机构名称
        when(userProvider.getAgencyByUserId(any())).thenReturn(agency); // 模拟获取当前用户所属机构方法
        // 模拟获取机构打开健康卡班级元数据方法
        AgencyMetaDataEntity meta = new AgencyMetaDataEntity(); // 创建机构元数据数据
        meta.setMetaKey(AgencyMetaKey.HEALTH_CARD_OPEN_GROUP.toString()); // 设置机构元数据 Key
        meta.setMetaValue("GroupId001,GroupId002"); // 设置机构元数据 Value
        when(agencyDao.getMeta(agency.getId(), AgencyMetaKey.HEALTH_CARD_OPEN_GROUP.toString())).thenReturn(meta); // 模拟获取机构打开健康卡班级元数据方法
        // Mock quarantine
        ArrayList<HealthQuarantineEntity> healthQuarantineEntities = new ArrayList<>();
        HealthQuarantineEntity healthQuarantineEntity = new HealthQuarantineEntity();
        healthQuarantineEntity.setExcuseod(false);
        healthQuarantineEntity.setDeleted(false);
        healthQuarantineEntity.setId("QuarantineId");
        healthQuarantineEntity.setTargetId("ChildId");
        healthQuarantineEntity.setRole("QuarantineRole");
        healthQuarantineEntity.setType("QuarantineType");
        healthQuarantineEntity.setNote("QuarantineNote");
        String quarantineReason = "QuarantineReason";
        healthQuarantineEntity.setReason(quarantineReason);
        healthQuarantineEntity.setHealthStatus("QuarantineIdStatus");
        healthQuarantineEntity.setIsExcuseod(false);
        healthQuarantineEntity.setStartDate(new Date());
        healthQuarantineEntity.setEndDate(new Date());
        healthQuarantineEntity.setCreateUserId("userId");
        healthQuarantineEntity.setUpdateUserId("userId");
        healthQuarantineEntity.setCreateAtUtc(new Date());
        healthQuarantineEntity.setUpdateAtUtc(new Date());
        healthQuarantineEntity.setIsDeleted(false);
        healthQuarantineEntity.setQuarantineReason(quarantineReason);
        healthQuarantineEntity.setDateMonth("9");
        healthQuarantineEntities.add(healthQuarantineEntity);
        when(quarantineDaoImplSpy.getQuarantineByTargetAndTypeAndDateRange(anyList(), anyString(), anyString(), anyString())).thenReturn(healthQuarantineEntities);
        // mock data GroupEntity
        List<com.learninggenie.common.data.model.GroupEntity> groupEntities = new ArrayList<>();
        com.learninggenie.common.data.model.GroupEntity groupEntity = new com.learninggenie.common.data.model.GroupEntity();
        groupEntity.setCenterId("CenterId");
        groupEntity.setId("GroupId");
        groupEntity.setName("GroupName");
        groupEntity.setMedia(new MediaEntity());
        groupEntity.setCreatedUtc("2020-09-01");
        com.learninggenie.common.data.model.CenterEntity centerEntity = new com.learninggenie.common.data.model.CenterEntity("CenterId", "CenterName");
        groupEntity.setCenter(centerEntity);
        DomainEntity domainEntity = new DomainEntity("DomainId", "DomainName", "DomainAbbr", "DomainMeasure", "DomainIcon", "DomainParentId", 1);
        groupEntity.setDomain(domainEntity);
        groupEntity.setStage(new StageEntity());
        groupEntity.setTeachers(Lists.newArrayList());
        groupEntity.setChilds(Lists.newArrayList());
        groupEntity.setGroupsMetaDataEntities(Lists.newArrayList());
        groupEntity.setIsDeleted(false);
        groupEntity.setInactive(false);
        groupEntity.setTraining(false);
        groupEntity.setIconPath("IconPath");
        groupEntity.setClassDomain(domainEntity);
        groupEntity.setProgramDomain(domainEntity);
        groupEntity.setCreateAtUtc(new Date());
        groupEntity.setUpdateAtUtc(new Date());
        groupEntity.setChildCount(0);
        groupEntity.setGroupInvitationEntities(Lists.newArrayList());
        groupEntity.setEnrollments(Sets.newHashSet());
        groupEntity.setFrameworkId(domainEntity.getId());
        groupEntity.setFrameworkName(domainEntity.getName());
        groupEntity.setCenterName(centerEntity.getName());
        groupEntities.add(groupEntity);

        when(groupDao.getGroupWithCenterByGroupIds(anyList())).thenReturn(groupEntities);
        when(userProfileDao.getByUserIdIn(anyList())).thenReturn(new ArrayList<>());
        // 模拟获取小孩出勤记录方法
        List<AttendanceStatisticsEntityOfExcel> presentCount = new ArrayList<>(); // 创建小孩出勤记录列表数据
        // 初始化静态参数
        ReflectionTestUtils.setField(attendanceService, "s3Root", "https://s3.amazonaws.com/"); // 初始化 s3Root 值
        ReflectionTestUtils.setField(attendanceService, "s3BucketName", "cn.learninggenie.prod"); // 初始化 s3BucketName 值
        ReflectionTestUtils.setField(attendanceService, "pdfBucket", "cn.learninggenie.prod"); // 初始化 s3BucketName 值
        ReflectionTestUtils.setField(attendanceService, "pdfEndpoint", "cn.learninggenie.prod"); // 初始化 s3BucketName 值
        // 模拟上传文件方法
        fileUtil.when(() -> FileUploadUtil.upload(anyString(), any(), anyString(), any())).thenReturn("Monthly_Attendance_Statistics.xlsx");
        when(fileSystem.getPublicUrl(any(), anyString())).thenReturn("https://s3.amazonaws.com/Monthly_Attendance_Statistics.xlsx");
        // 调取测试方法
        AttendanceExcelResponse response = attendanceService.downLoadStatistics(request);

        // 验证返回值
        assertNull(response.getUrl()); // 验证上传文件路径是否与预期一致
        assertEquals("Agency Attendance Statistics", response.getFileName()); // 验证上传文件名称是否与预期一致
        assertEquals("10/01/2023 - 10/31/2023", response.getDateStr()); // 验证上传文件日期是否与预期一致
        assertEquals("UserId001", response.getUserId()); // 验证上传文件创建人是否与预期一致
    }

    /**
     * 测试下载
     * case: 下载 DOWNLOAD_GROUP 类型的文件
     */
    @Test
    void downLoadStatistics_DOWNLOAD_GROUP() throws IOException {
        // 请求参数准备
        AttendancesStatisticsRequest request = new AttendancesStatisticsRequest();
        request.setCenterIds(Arrays.asList("CenterId001", "CenterId002")); // 设置学校 Id
        request.setGroupIds(Arrays.asList("GroupId001", "GroupId002")); // 设置班级 Id
        request.setDownLoadFileType("pdf"); // 设置下载文件类型
        request.setFromDate("2023-10-01"); // 设置开始时间
        request.setToDate("2023-10-31"); // 设置结束时间
        request.setType("DOWNLOAD_GROUP"); // 设置下载类型
        request.setWebDownload(true); // 设置是否是 web 下载
        // 模拟开通功能学校班级数据
        List<HealthStatisticsHistoryChildEntity> childHistory = new ArrayList<>();
        HealthStatisticsHistoryChildEntity entity = new HealthStatisticsHistoryChildEntity();
        entity.setLocalDate(LocalDate.of(2022, 06, 02));
        entity.setEnrollmentIds("1");
        childHistory.add(entity);
        HealthStatisticsHistoryDaoImpl healthStatisticsHistoryDaoSpy = this.getHealthStatisticsHistoryDaoSpy();
        when(healthStatisticsHistoryDaoSpy.lambdaQuery()
                .select(HealthStatisticsHistoryChildEntity::getEnrollmentIds, HealthStatisticsHistoryChildEntity::getGroupId, HealthStatisticsHistoryChildEntity::getLocalDate) // 查询小孩 Ids，日期字段
                .in(HealthStatisticsHistoryChildEntity::getGroupId, request.getGroupIds()) // 过滤班级
                .between(HealthStatisticsHistoryChildEntity::getLocalDate,
                        TimeUtil.parseLocalDate(TimeUtil.parseDate(request.getFromDate())).toString(),
                        TimeUtil.parseLocalDate(TimeUtil.parseDate(request.getToDate())).toString()) // 过滤日期
                .eq(HealthStatisticsHistoryChildEntity::getIsDeleted, false) // 过滤删除
                .list()).thenReturn(childHistory);
        HealthQuarantineDaoImpl quarantineDaoImplSpy = this.getHealthQuarantineDaoSpy();

        // 模拟通过小孩 Id 获取小孩信息
        List<EnrollmentModel> enrollmentModelList = new ArrayList<>(); // 创建小孩列表数据
        EnrollmentModel enrollmentModel = new EnrollmentModel(); // 创建小孩数据
        enrollmentModel.setId("ChildId001"); // 设置小孩 Id
        enrollmentModel.setDisplayName("ChildName001"); // 设置小孩名称
        enrollmentModel.setCenterId("CenterId001"); // 设置学校 Id
        enrollmentModel.setCenterName("CenterName001"); // 设置学校名称
        enrollmentModel.setGroupId("GroupId001"); // 设置班级 Id
        enrollmentModel.setGroupName("GroupName001"); // 设置班级名称
        enrollmentModelList.add(enrollmentModel); // 将小孩数据添加到小孩列表数据中
        EnrollmentModel enrollmentModel2 = new EnrollmentModel(); // 创建小孩数据
        enrollmentModel2.setId("ChildId002"); // 设置小孩 Id
        enrollmentModel2.setDisplayName("ChildName002"); // 设置小孩名称
        enrollmentModel2.setCenterId("CenterId002"); // 设置学校 Id
        enrollmentModel2.setCenterName("CenterName002"); // 设置学校名称
        enrollmentModel2.setGroupId("GroupId002"); // 设置班级 Id
        enrollmentModel2.setGroupName("GroupName002"); // 设置班级名称
        enrollmentModelList.add(enrollmentModel2); // 将小孩数据添加到小孩列表数据中
        when(studentDao.getChildrenByIds(any())).thenReturn(enrollmentModelList); // 模拟通过小孩 Id 获取小孩信息
        // 模拟获取当前用户 Id 方法
        when(userProvider.getCurrentUserId()).thenReturn("UserId001"); // 模拟获取当前用户 Id 方法
        // 模拟获取当前用户所属机构方法
        AgencyModel agency = new AgencyModel(); // 创建机构数据
        agency.setId("AgencyId001"); // 设置机构 Id
        agency.setName("AgencyName001"); // 设置机构名称
        when(userProvider.getAgencyByUserId(any())).thenReturn(agency); // 模拟获取当前用户所属机构方法
        // 模拟获取机构打开健康卡班级元数据方法
        AgencyMetaDataEntity meta = new AgencyMetaDataEntity(); // 创建机构元数据数据
        meta.setMetaKey(AgencyMetaKey.HEALTH_CARD_OPEN_GROUP.toString()); // 设置机构元数据 Key
        meta.setMetaValue("GroupId001,GroupId002"); // 设置机构元数据 Value
        when(agencyDao.getMeta(agency.getId(), AgencyMetaKey.HEALTH_CARD_OPEN_GROUP.toString())).thenReturn(meta); // 模拟获取机构打开健康卡班级元数据方法
        List<HealthQuarantineEntity> quarantines = new ArrayList<>(); // 创建隔离数据
        // Mock quarantine
        ArrayList<HealthQuarantineEntity> healthQuarantineEntities = new ArrayList<>();
        HealthQuarantineEntity healthQuarantineEntity = new HealthQuarantineEntity();
        healthQuarantineEntity.setExcuseod(false);
        healthQuarantineEntity.setDeleted(false);
        healthQuarantineEntity.setId("QuarantineId");
        healthQuarantineEntity.setTargetId("currentChild");
        healthQuarantineEntity.setRole("QuarantineRole");
        healthQuarantineEntity.setType("QuarantineType");
        healthQuarantineEntity.setNote("QuarantineNote");
        String quarantineReason = "QuarantineReason";
        healthQuarantineEntity.setReason(quarantineReason);
        healthQuarantineEntity.setHealthStatus("QuarantineIdStatus");
        healthQuarantineEntity.setIsExcuseod(false);
        healthQuarantineEntity.setStartDate(new Date());
        healthQuarantineEntity.setEndDate(new Date());
        healthQuarantineEntity.setCreateUserId("userId");
        healthQuarantineEntity.setUpdateUserId("userId");
        healthQuarantineEntity.setCreateAtUtc(new Date());
        healthQuarantineEntity.setUpdateAtUtc(new Date());
        healthQuarantineEntity.setIsDeleted(false);
        healthQuarantineEntity.setQuarantineReason(quarantineReason);
        healthQuarantineEntity.setDateMonth("9");
        healthQuarantineEntities.add(healthQuarantineEntity);

        when(quarantineDaoImplSpy.getQuarantineByTargetAndTypeAndDateRange(anyList(), anyString(), anyString(), anyString())).thenReturn(healthQuarantineEntities);
        // mock data GroupEntity
        List<com.learninggenie.common.data.model.GroupEntity> groupEntities = new ArrayList<>();
        com.learninggenie.common.data.model.GroupEntity groupEntity = new com.learninggenie.common.data.model.GroupEntity();
        groupEntity.setCenterId("CenterId");
        groupEntity.setId("GroupId");
        groupEntity.setName("GroupName");
        groupEntity.setMedia(new MediaEntity());
        groupEntity.setCreatedUtc("2020-09-01");
        com.learninggenie.common.data.model.CenterEntity centerEntity = new com.learninggenie.common.data.model.CenterEntity("CenterId", "CenterName");
        groupEntity.setCenter(centerEntity);
        DomainEntity domainEntity = new DomainEntity("DomainId", "DomainName", "DomainAbbr", "DomainMeasure", "DomainIcon", "DomainParentId", 1);
        groupEntity.setDomain(domainEntity);
        groupEntity.setStage(new StageEntity());
        groupEntity.setTeachers(Lists.newArrayList());
        groupEntity.setChilds(Lists.newArrayList());
        groupEntity.setGroupsMetaDataEntities(Lists.newArrayList());
        groupEntity.setIsDeleted(false);
        groupEntity.setInactive(false);
        groupEntity.setTraining(false);
        groupEntity.setIconPath("IconPath");
        groupEntity.setClassDomain(domainEntity);
        groupEntity.setProgramDomain(domainEntity);
        groupEntity.setCreateAtUtc(new Date());
        groupEntity.setUpdateAtUtc(new Date());
        groupEntity.setChildCount(0);
        groupEntity.setGroupInvitationEntities(Lists.newArrayList());
        groupEntity.setEnrollments(Sets.newHashSet());
        groupEntity.setFrameworkId(domainEntity.getId());
        groupEntity.setFrameworkName(domainEntity.getName());
        groupEntity.setCenterName(centerEntity.getName());
        groupEntities.add(groupEntity);

        when(groupDao.getGroupWithCenterByGroupIds(anyList())).thenReturn(groupEntities);
        // 模拟获取小孩出勤记录方法
        List<AttendanceStatisticsEntityOfExcel> presentCount = new ArrayList<>(); // 创建小孩出勤记录列表数据
        // 初始化静态参数
        ReflectionTestUtils.setField(attendanceService, "s3Root", "https://s3.amazonaws.com/"); // 初始化 s3Root 值
        ReflectionTestUtils.setField(attendanceService, "s3BucketName", "cn.learninggenie.prod"); // 初始化 s3BucketName 值
        ReflectionTestUtils.setField(attendanceService, "pdfBucket", "cn.learninggenie.prod"); // 初始化 s3BucketName 值
        ReflectionTestUtils.setField(attendanceService, "pdfEndpoint", "cn.learninggenie.prod"); // 初始化 s3BucketName 值
        // 模拟上传文件方法
        when(fileSystem.getPublicUrl(any(),any())).thenReturn("https://s3.amazonaws.com/Monthly_Attendance_GroupName_10-01-2023_10-31-2023_20231027013601767.pdf");
        fileUtil.when(() -> FileUploadUtil.upload(anyString(), any(), anyString(), any())).thenReturn("Monthly_Attendance_Statistics.xlsx");
        // 调取测试方法
        // 创建 MockedStatic 实例来模拟 CmdUtil 类的静态方法
        try (MockedStatic<CmdUtil> cmdUtilMockedStatic = Mockito.mockStatic(CmdUtil.class)) {
            // 指定当调用 executeCmd(cmd) 方法时不执行任何操作
            cmdUtilMockedStatic.when(() -> CmdUtil.executeCmd(Mockito.anyList())).thenAnswer(invocation -> {
                // 不执行任何操作，即模拟无返回值
                return null;
            });

            // 方法调用
            AttendanceExcelResponse response = attendanceService.downLoadStatistics(request);

            // 验证返回值
            assertNull(response.getUrl()); // 验证上传文件路径是否与预期一致
            assertEquals("Agency Attendance Statistics", response.getFileName()); // 验证上传文件名称是否与预期一致
            assertEquals("10/01/2023 - 10/31/2023", response.getDateStr()); // 验证上传文件日期是否与预期一致
            assertEquals("UserId001", response.getUserId()); // 验证上传文件创建人是否与预期一致
        }
    }
    /**
     * 测试获取用户管理下开通健康卡（签到）的班级方法
     * case: 用户角色为 AGENCY_OWNER
     */
    @Test
    public void testGetOpenHealthCardGroupIdsRoleIsAgencyOwner() {
        // 构造测试数据
        String userId = "testUserId";
        String agencyId = "testAgencyId";
        com.learninggenie.common.data.entity.UserEntity user = new com.learninggenie.common.data.entity.UserEntity();
        user.setRole(UserRole.AGENCY_OWNER.toString()); // 设置用户角色为 AGENCY_OWNER
        List<CenterModel> centerModels = new ArrayList<>();
        CenterModel center1 = new CenterModel();
        center1.setGroupId("groupId1");
        centerModels.add(center1);
        CenterModel center2 = new CenterModel();
        center2.setGroupId("groupId2");
        centerModels.add(center2);
        AgencyMetaDataEntity meta = new AgencyMetaDataEntity();
        meta.setMetaValue("groupId1,groupId3");

        // 设置模拟对象的行为
        when(userProvider.checkUser(userId)).thenReturn(user);
        when(centerDao.getCenterAndGroupsByAgencyUserId(userId)).thenReturn(centerModels);
        when(agencyDao.getMeta(agencyId, AgencyMetaKey.HEALTH_CARD_OPEN_GROUP.toString())).thenReturn(meta);

        // 调用被测试的方法
        List<String> result = attendanceService.getOpenHealthCardGroupIds(userId, agencyId);

        // 验证结果是否符合预期
        assertEquals(1, result.size());
        assertTrue(result.contains("groupId1")); // 返回结果应该包含 groupId1
        assertFalse(result.contains("groupId2")); // 返回结果不应该包含 groupId2
        assertFalse(result.contains("groupId3")); // 返回结果不应该包含 groupId3
    }

    /**
     * 测试获取用户管理下开通健康卡（签到）的班级方法
     * case: 用户角色为 SITE_ADMIN
     */
    @Test
    public void testGetOpenHealthCardGroupIdsRoleIsSiteAdmin() {
        // 构造测试数据
        String userId = "testUserId";
        String agencyId = "testAgencyId";
        com.learninggenie.common.data.entity.UserEntity user = new com.learninggenie.common.data.entity.UserEntity();
        user.setRole(UserRole.SITE_ADMIN.toString()); // 设置用户角色为 SITE_ADMIN
        List<CenterModel> centerModels = new ArrayList<>();
        CenterModel center1 = new CenterModel();
        center1.setGroupId("groupId1");
        centerModels.add(center1);
        CenterModel center2 = new CenterModel();
        center2.setGroupId("groupId2");
        centerModels.add(center2);
        AgencyMetaDataEntity meta = new AgencyMetaDataEntity();
        meta.setMetaValue("groupId1,groupId3");

        // 设置模拟对象的行为
        when(userProvider.checkUser(userId)).thenReturn(user);
        when(centerDao.getCenterAndGroupsBySiteAdminId(userId)).thenReturn(centerModels);
        when(agencyDao.getMeta(agencyId, AgencyMetaKey.HEALTH_CARD_OPEN_GROUP.toString())).thenReturn(meta);

        // 调用被测试的方法
        List<String> result = attendanceService.getOpenHealthCardGroupIds(userId, agencyId);

        // 验证结果是否符合预期
        assertEquals(1, result.size());
        assertTrue(result.contains("groupId1")); // 返回结果应该包含 groupId1
        assertFalse(result.contains("groupId2")); // 返回结果不应该包含 groupId2
        assertFalse(result.contains("groupId3")); // 返回结果不应该包含 groupId3
    }

    /**
     * 测试获取用户管理下开通健康卡（签到）的班级方法
     * case: 用户角色为 COLLABORATOR 老师
     */
    @Test
    public void testGetOpenHealthCardGroupIdsRoleIsTeacher() {
        // 构造测试数据
        String userId = "testUserId";
        String agencyId = "testAgencyId";
        com.learninggenie.common.data.entity.UserEntity user = new com.learninggenie.common.data.entity.UserEntity();
        user.setRole(UserRole.COLLABORATOR.toString());
        List<CenterModel> centerModels = new ArrayList<>();
        CenterModel center1 = new CenterModel();
        center1.setGroupId("groupId1");
        centerModels.add(center1);
        CenterModel center2 = new CenterModel();
        center2.setGroupId("groupId2");
        centerModels.add(center2);
        AgencyMetaDataEntity meta = new AgencyMetaDataEntity();
        meta.setMetaValue("groupId1,groupId3");

        // 设置模拟对象的行为
        when(userProvider.checkUser(userId)).thenReturn(user);
        when(centerDao.getCenterAndGroupsByTeacherId(userId)).thenReturn(centerModels);
        when(agencyDao.getMeta(agencyId, AgencyMetaKey.HEALTH_CARD_OPEN_GROUP.toString())).thenReturn(meta);

        // 调用被测试的方法
        List<String> result = attendanceService.getOpenHealthCardGroupIds(userId, agencyId);

        // 验证结果是否符合预期
        assertEquals(1, result.size());
        assertTrue(result.contains("groupId1")); // 返回结果应该包含 groupId1
        assertFalse(result.contains("groupId2")); // 返回结果不应该包含 groupId2
        assertFalse(result.contains("groupId3")); // 返回结果不应该包含 groupId3
    }


    @Test
    public void testGetChildLastDaysPdf_withEmptyGroupIds_throwsException() {
        AttendancesStatisticsRequest request = new AttendancesStatisticsRequest();
        request.setGroupIds(Collections.emptyList());

        assertThrows(BusinessException.class, () -> attendanceService.getChildLastDaysPdf(request));
    }

    @Test
    public void testGetChildLastDaysPdf() {
        // 创建 AttendancesStatisticsRequest 对象，并设置相关属性
        AttendancesStatisticsRequest request = new AttendancesStatisticsRequest();
        request.setGroupIds(Collections.singletonList("testGroupId")); // 设置班级 ID
        request.setCurrentDate(LocalDate.now()); // 设置当前日期
        request.setDay(1); // 设置天数

        // 模拟获取当前用户 ID
        String userId = "testUserId";
        when(userProvider.getCurrentUserId()).thenReturn(userId);

        // 创建 AgencyModel 对象，并设置相关属性
        AgencyModel agencyEntity = new AgencyModel();
        agencyEntity.setName("testAgencyName"); // 设置机构名称
        // 模拟获取当前用户所属机构
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyEntity);

        // 创建 UserEntity 对象，并设置相关属性
        com.learninggenie.common.data.entity.UserEntity user = new com.learninggenie.common.data.entity.UserEntity();
        user.setRole(UserRole.AGENCY_OWNER.toString()); // 设置用户角色为 AGENCY_OWNER
        // 模拟检查用户
        when(userProvider.checkUser(userId)).thenReturn(user);

        // 创建 GetStatisticsRequest 对象，并设置相关属性
        GetStatisticsRequest getStatisticsRequest = new GetStatisticsRequest();
        getStatisticsRequest.setGroupIds(request.getGroupIds()); // 设置班级 ID
        getStatisticsRequest.setDate(request.getCurrentDate()); // 设置日期
        getStatisticsRequest.setDay(request.getDay()); // 设置天数
        getStatisticsRequest.setType("CHILD"); // 设置类型

        // 创建 DayHealthStatisticsResponse 对象，并设置相关属性
        DayHealthStatisticsResponse statisticsByDay = new DayHealthStatisticsResponse();
        statisticsByDay.setStartDate(request.getCurrentDate().minusDays(request.getDay())); // 设置开始日期
        statisticsByDay.setEndDate(request.getCurrentDate()); // 设置结束日期
        statisticsByDay.setDay(request.getDay() + 1); // 设置天数

        // 模拟获取按天统计的数据
        when(healthCheckService.getStatisticsByDay(any(GetStatisticsRequest.class))).thenReturn(statisticsByDay);

        // 创建 CenterModel 列表，并添加相关数据
        List<CenterModel> centerModels = new ArrayList<>();
        CenterModel centerModel = new CenterModel();
        centerModel.setGroupId("testGroupId"); // 设置班级 ID
        centerModel.setGroupDeleted(false); // 设置班级是否被删除
        centerModel.setGroupInactive(false); // 设置班级是否处于非活动状态
        centerModels.add(centerModel);

        // 模拟获取机构用户管理的学校和班级
        when(centerDao.getCenterAndGroupsByAgencyUserId(any())).thenReturn(centerModels);

        // 创建 GroupWithCenter 列表，并添加相关数据
        List<GroupWithCenter> centerAndGroups = new ArrayList<>();
        GroupWithCenter groupWithCenter = new GroupWithCenter();
        groupWithCenter.setCenterId("testCenterId"); // 设置学校 ID
        groupWithCenter.setCenterName("testCenterName"); // 设置学校名称
        groupWithCenter.setGroupId("testGroupId"); // 设置班级 ID
        groupWithCenter.setGroupName("testGroupName"); // 设置班级名称
        centerAndGroups.add(groupWithCenter);

        // 模拟获取班级和学校的名称
        when(groupDao.getGroupAndCenterNamesByIds(StringUtil.convertIdsToString(request.getGroupIds()))).thenReturn(centerAndGroups);

        // 创建 PdfConvertJobEntity 对象，并设置相关属性
        String jobId = UUID.randomUUID().toString();
        PdfConvertJobEntity pdfConvertJobEntity = new PdfConvertJobEntity();
        pdfConvertJobEntity.setId(jobId); // 设置任务 ID
        pdfConvertJobEntity.setStatus(PdfConvertStatus.SUCCEED.toString()); // 设置任务状态
        pdfConvertJobEntity.setPdfUrl("testPdfUrl"); // 设置 PDF URL

        // 模拟获取 PDF 任务
        when(reportDao.getPdfJob(any())).thenReturn(pdfConvertJobEntity);

        // 模拟获取公共 URL
        when(fileSystem.getPublicUrl(any(), any())).thenReturn("testPdfUrl");

        // 创建一个完成的 Future 对象，并设置 InvokeResult 的状态码为 200
        Future<InvokeResult> future = CompletableFuture.completedFuture(new InvokeResult().withStatusCode(200));
        // 模拟调用 PDF 服务
        when(remoteProvider.callPdfService(any(), any())).thenReturn(future);

        // 使用反射设置 attendanceService 的字段值
        ReflectionTestUtils.setField(attendanceService, "s3Root", "s3Root");
        ReflectionTestUtils.setField(attendanceService, "pdfEndpoint", "pdfEndpoint");

        // 调用被测试的方法
        AttendanceExcelResponse response = attendanceService.getChildLastDaysPdf(request);

        // 验证结果
        assertEquals("testPdfUrl", response.getPdfUrl()); // 验证返回的 PDF URL 是否为 "testPdfUrl"
        assertEquals(LocalDate.now().plusDays(-1).format(DateTimeFormatter.ofPattern("MM/dd/yyyy")) + " - " + LocalDate.now().format(DateTimeFormatter.ofPattern("MM/dd/yyyy")), response.getDateStr()); // 验证返回的日期字符串是否符合预期
        assertEquals("Last " + statisticsByDay.getDay() + " Days' Health Tracking", response.getFileName()); // 验证返回的文件名是否符合预期
        assertEquals(Collections.singletonList("testCenterName"), response.getCenterNames()); // 验证返回的学校名称列表是否符合预期
        assertEquals(Collections.singletonList("testGroupName"), response.getGroupNames()); // 验证返回的班级名称列表是否符合预期
        assertEquals(request.getType(), response.getType()); // 验证返回的类型是否符合预期
        assertEquals(userId, response.getUserId()); // 验证返回的用户 ID 是否符合预期
    }

    @Test
    public void testGenerateAttendanceStatisticsPdf() {
        // 模拟依赖项
        when(userProvider.getCurrentUserId()).thenReturn("user123"); // 当调用 userProvider 的 getCurrentUserId 方法时，返回 "user123"
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(new AgencyModel()); // 当调用 userProvider 的 getAgencyByUserId 方法时，返回一个新的 AgencyModel 对象
        when(agencyDao.getMeta(any(), any())).thenReturn(new AgencyMetaDataEntity()); // 当调用 agencyDao 的 getMeta 方法时，返回一个新的 AgencyMetaDataEntity 对象
        when(centerDao.getCenter(anyString())).thenReturn(new CenterEntity()); // 当调用 centerDao 的 getCenter 方法时，返回一个新的 CenterEntity 对象
        when(fileSystem.getPublicUrl(anyString(), any())).thenReturn("http://example.com/logo.png"); // 当调用 fileSystem 的 getPublicUrl 方法时，返回 "http://example.com/logo.png"

        // 创建一个完成的 Future 对象，并设置 InvokeResult 的状态码为 200
        Future<InvokeResult> future = CompletableFuture.completedFuture(new InvokeResult().withStatusCode(200));
        // 当调用 remoteProvider 的 callPdfService 方法时，返回上面创建的 Future 对象
        when(remoteProvider.callPdfService(anyString(), anyList())).thenReturn(future);

        // 使用反射设置 attendanceService 的字段值
        ReflectionTestUtils.setField(attendanceService, "pdfBucket", "pdfBucket"); // 设置 pdfBucket 字段的值为 "pdfBucket"
        ReflectionTestUtils.setField(attendanceService, "s3Root", "s3Root"); // 设置 s3Root 字段的值为 "s3Root"
        ReflectionTestUtils.setField(attendanceService, "pdfEndpoint", "pdfEndpoint"); // 设置 pdfEndpoint 字段的值为 "pdfEndpoint"

        // 创建测试数据
        AttendanceStatisticsData statisticsData = new AttendanceStatisticsData();
        List<AttendanceStatisticsDailyData> attendanceStatisticsDailyDataList = new ArrayList<>();
        AttendanceStatisticsDailyData attendanceStatisticsDailyData = new AttendanceStatisticsDailyData();
        List<AttendanceCenterData> centerDataList = new ArrayList<>();
        AttendanceCenterData attendanceCenterData = new AttendanceCenterData();
        attendanceCenterData.setCenterId("centerId"); // 设置中心 ID
        centerDataList.add(attendanceCenterData); // 将中心数据添加到列表中
        statisticsData.setDateRange(""); // 设置日期范围
        statisticsData.setAgencyName(""); // 设置机构名称
        attendanceStatisticsDailyData.setCenterDataList(centerDataList); // 设置中心数据列表
        attendanceStatisticsDailyDataList.add(attendanceStatisticsDailyData); // 将每日统计数据添加到列表中
        statisticsData.setStatisticsDailyData(attendanceStatisticsDailyDataList); // 设置每日统计数据列表
        String name = "John Doe"; // 设置名字
        AttendancesStatisticsRequest request = new AttendancesStatisticsRequest();
        request.setType("DOWNLOAD_CENTER"); // 设置下载类型

        // 调用被测试的方法
        String result = attendanceService.generateAttendanceStatisticsPdf(statisticsData, name, request);

        // 验证预期行为
        verify(userProvider, times(3)).getCurrentUserId(); // 验证 getCurrentUserId 方法被调用了 3 次
        verify(userProvider, times(2)).getAgencyByUserId(anyString()); // 验证 getAgencyByUserId 方法被调用了 2 次
        verify(centerDao, times(1)).getCenter(anyString()); // 验证 getCenter 方法被调用了 1 次
        verify(fileSystem, times(1)).getPublicUrl(anyString(), any()); // 验证 getPublicUrl 方法被调用了 1 次
        verify(reportDao, times(1)).createPdfConvertJob(any(PdfConvertJobEntity.class)); // 验证 createPdfConvertJob 方法被调用了 1 次
        verify(remoteProvider, times(1)).callPdfService(anyString(), anyList()); // 验证 callPdfService 方法被调用了 1 次
    }
}

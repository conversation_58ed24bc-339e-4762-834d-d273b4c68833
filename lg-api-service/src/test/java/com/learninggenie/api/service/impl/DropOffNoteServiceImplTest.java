package com.learninggenie.api.service.impl;

import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.model.SuccessResponse;
import com.learninggenie.api.model.form.SubmitFormRequest;
import com.learninggenie.api.model.healthcheck.ChildrenFormsFillInfoResponse;
import com.learninggenie.api.model.healthcheck.FilledHealthFormRemindResponse;
import com.learninggenie.api.model.healthcheck.HealthCheckFormModel;
import com.learninggenie.api.model.healthcheck.HealthCheckFormResponse;
import com.learninggenie.api.model.note.NoteResponse;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.provider.impl.GroupProviderImpl;
import com.learninggenie.api.service.AgencyService;
import com.learninggenie.common.data.dao.*;
import com.learninggenie.common.data.dao.dropoffnote.DropOffNoteDao;
import com.learninggenie.common.data.entity.*;
import com.learninggenie.common.data.entity.CenterEntity;
import com.learninggenie.common.data.entity.GroupEntity;
import com.learninggenie.common.data.entity.UserEntity;
import com.learninggenie.common.data.enums.AgencyMetaKey;
import com.learninggenie.common.data.enums.AppMetaKey;
import com.learninggenie.common.data.enums.FormType;
import com.learninggenie.common.data.enums.form.QuestionType;
import com.learninggenie.common.data.model.*;
import com.learninggenie.common.data.model.form.common.FormModel;
import com.learninggenie.common.data.model.form.common.QuestionModel;
import com.learninggenie.common.data.model.form.common.ResponseRecord;
import com.learninggenie.common.utils.TimeUtil;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Time;
import java.util.*;

import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * DropOffNoteServiceImplTest 单元测试
 */
@ExtendWith(MockitoExtension.class)
public class DropOffNoteServiceImplTest {
    @InjectMocks
    private DropOffNoteServiceImpl dropOffNoteService;

    @Mock
    private HealthCheckFormDao healthCheckFormDao;

    @Mock
    private MetaDao metaDao;

    @Mock
    private UserProvider userProvider;

    @Mock
    private AgencyDao agencyDao;

    @Mock
    private CenterDao centerDao;

    @Mock
    private GroupDao groupDao;

    @Mock
    private StudentDao studentDao;

    @Mock
    private FormsResponseRecordDao formsResponseRecordDao;

    @Mock
    private FormDao formDao;

    @Mock
    private DropOffNoteDao dropOffNoteDao;

    @Mock
    private AgencyService agencyService;

    @InjectMocks
    private GroupProviderImpl groupProvider;

    @InjectMocks
    private HealthCheckServiceImpl healthCheckFormService;

    private static final String AGENCY_ID = "A001";

    private static final String FORM_ID = "F001";

    private static final String USER_ID = "U001";

    private static final String CLASS_ID = "C001";

    private static final String SCHOOL_ID = "S001";

    private static final String STUDENT_ID = "ST001";

    /**
     * 测试获取机构下的 Drop-off Note
     */
    @Test
    void getDetail() {
        // 数据准备
        String agencyId = AGENCY_ID;
        List<FormsFormEntity> formsFormEntities = new ArrayList<>();
        FormsFormEntity formsFormEntity = new FormsFormEntity();
        formsFormEntity.setAgencyId(agencyId);
        formsFormEntity.setId(FORM_ID);
        formsFormEntity.setEnable(true);
        formsFormEntity.setName("测试问卷");
        formsFormEntity.setFromDate(new Date());
        formsFormEntity.setToDate(new Date());
        formsFormEntity.setType(FormType.DROP_OFF_NOTE.toString());
        Time notifyTime = TimeUtil.strToTime("09:00:00");
        formsFormEntity.setNotifyTime(notifyTime);
        formsFormEntity.setNotifyDay("1,2,3,4,5");
        formsFormEntities.add(formsFormEntity);

        List<FormsQuestionEntity> formsQuestionEntities = new ArrayList<>();
        FormsQuestionEntity formsQuestionEntity = new FormsQuestionEntity();
        formsQuestionEntity.setFormId(formsFormEntity.getId());
        formsQuestionEntity.setId("Q001");
        formsQuestionEntity.setSortNum(1);
        formsQuestionEntity.setType(QuestionType.MULTI_INPUT.toString());
        formsQuestionEntity.setRequired(true);
        formsQuestionEntities.add(formsQuestionEntity);

        AppMetadataEntity metadataEntity = new AppMetadataEntity();
        metadataEntity.setMetaKey(AppMetaKey.DROP_OFF_NOTE_FORM_TEMPLATE.toString());
        metadataEntity.setMetaValue("{\n" +
                "  \"description\": \"\",\n" +
                "  \"headerImg\": \"lg_drop_off_note_default_header.png\",\n" +
                "  \"headerImgUrl\": \"https://s3.amazonaws.com/com.learning-genie.prod.us/lg_drop_off_note_default_header.png\",\n" +
                "  \"id\": \"form-1\",\n" +
                "  \"name\": \"Drop-off Note\",\n" +
                "  \"questions\": [{\n" +
                "              \"id\":\"84697AE0-88AA-6954-521D-0BE8AF293514\",\n" +
                "    \"description\": \"\",\n" +
                "    \"isNeedConfirm\": true,\n" +
                "    \"isPrefab\": false,\n" +
                "    \"isRequired\": true,\n" +
                "    \"name\": \"What time did your child wake up?\",\n" +
                "    \"type\": \"MULTI_INPUT\"\n" +
                "  }],\n" +
                "  \"sortNum\": 1,\n" +
                "  \"type\": \"DROP_OFF_NOTE\"\n" +
                "}");
        // 数据准备 -- 接口模拟

        Mockito.when(healthCheckFormDao.getFormByAgencyIdAndType(agencyId, FormType.DROP_OFF_NOTE.toString())).thenReturn(formsFormEntities);
        Mockito.when(healthCheckFormDao.getFormsQuestions(formsFormEntity.getId())).thenReturn(formsQuestionEntities);
        HealthCheckFormModel healthCheckFormModel = dropOffNoteService.getDetail(agencyId);
        Assert.assertEquals(healthCheckFormModel.getId(), formsFormEntity.getId());

        Mockito.when(healthCheckFormDao.getFormByAgencyIdAndType(agencyId, FormType.DROP_OFF_NOTE.toString())).thenReturn(new ArrayList<>());
        Mockito.when(metaDao.getAppMetaEntity(AppMetaKey.DROP_OFF_NOTE_FORM_TEMPLATE.toString())).thenReturn(metadataEntity);
        HealthCheckFormModel healthCheckFormModel1 = dropOffNoteService.getDetail(agencyId);
        Assert.assertEquals(healthCheckFormModel1.getId(), "");
    }

    /**
     * 测试更新 Drop-off note 时间相关信息
     */
    @Test
    void updateFormInfo() {
        // 数据准备
        String agencyId = AGENCY_ID;
        String formId = FORM_ID;
        String notifyTime = "09:00";
        String notifyDay = "1,2,3,4,5";

        HealthCheckFormModel healthCheckFormModel = new HealthCheckFormModel();
        healthCheckFormModel.setId(formId);
        healthCheckFormModel.setNotifyTime(notifyTime);
        healthCheckFormModel.setNotifyDay(notifyDay);
        Date fromDate = new Date();
        Date toDate = new Date();
        healthCheckFormModel.setFromDate(TimeUtil.format(fromDate, TimeUtil.dateFormat));
        healthCheckFormModel.setEnable(true);
        healthCheckFormModel.setNotifyTime("09:00:00");

        List<FormsFormEntity> formsFormEntities = new ArrayList<>();
        FormsFormEntity formEntity = new FormsFormEntity();
        formEntity.setId(formId);
        formEntity.setAgencyId(agencyId);
        formEntity.setNotifyDay(notifyDay);

        formEntity.setFromDate(fromDate);
        formEntity.setToDate(toDate);
        formEntity.setType(FormType.DROP_OFF_NOTE.toString());
        Time notifyTime1 = TimeUtil.strToTime("09:00:00");
        formEntity.setNotifyTime(notifyTime1);
        formsFormEntities.add(formEntity);

        // 数据准备 -- 接口模拟
        Mockito.when(healthCheckFormDao.getFormsForm(formId)).thenReturn(formEntity);

        HealthCheckFormResponse healthCheckFormResponse = dropOffNoteService.updateFormInfo(healthCheckFormModel);
        Assert.assertEquals(healthCheckFormResponse.getId(), formEntity.getId());
    }

    /**
     * 测试设置 Drop-off note 学校班级信息
     */
    @Test
    void setDropOffNoteClasses() {
        // 数据准备
        String userId = USER_ID;
        String classId = CLASS_ID;
        String schoolId = SCHOOL_ID;

        GetStatisticsRequest getStatisticsRequest = new GetStatisticsRequest();
        List<String> classIds = new ArrayList<>();
        classIds.add(classId);
        getStatisticsRequest.setGroupIds(classIds);

        List<String> siteIds = new ArrayList<>();
        siteIds.add(schoolId);
        getStatisticsRequest.setCenterIds(siteIds);

        AgencyModel agency = new AgencyModel();
        agency.setId(AGENCY_ID);

        // 数据准备 -- 接口模拟
        Mockito.when(userProvider.getCurrentUserId()).thenReturn(userId);
        Mockito.when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
//        Mockito.when(agencyDao.setMeta(agency.getId(), AgencyMetaKey.DROP_OFF_NOTE_OPEN_GROUP.toString(), classId))
        SuccessResponse response = dropOffNoteService.setDropOffNoteClasses(getStatisticsRequest);
        Assert.assertEquals(response.isSuccess(), true);
    }

    /**
     * 测试获取 Drop-off note 学校班级信息
     */
    @Test
    void getDropOffNoteClasses() {
        // 数据准备
        String userId = USER_ID;
        String classId = CLASS_ID;
        String schoolId = SCHOOL_ID;
        String agencyId = AGENCY_ID;

        AgencyModel agency = new AgencyModel();
        agency.setId(agencyId);

        com.learninggenie.common.data.entity.UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole("AGENCY_ADMIN");

        List<CenterModel> centerModels = new ArrayList<>();
        CenterModel centerModel = new CenterModel();
        centerModel.setId(schoolId);
        centerModel.setName("school");
        centerModel.setGroupId(classId);
        centerModel.setGroupName("class");
        centerModel.setGroupInactive(false);
        centerModels.add(centerModel);

        AgencyMetaDataEntity agencyMetaDataEntity = new AgencyMetaDataEntity();
        agencyMetaDataEntity.setAgencyId(agencyId);
        agencyMetaDataEntity.setMetaKey(AgencyMetaKey.DROP_OFF_NOTE_OPEN_GROUP.toString());
        agencyMetaDataEntity.setMetaKey(classId);

        List<com.learninggenie.common.data.entity.GroupEntity> groups = new ArrayList<>();
        com.learninggenie.common.data.entity.GroupEntity groupEntity = new com.learninggenie.common.data.entity.GroupEntity();
        groupEntity.setId(classId);
        groupEntity.setName("class");
        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setId(schoolId);
        groupEntity.setCenter(centerEntity);
        groupEntity.setDeleted(false);
        groupEntity.setInactive(false);
        GroupStageEntity groupStageEntity = new GroupStageEntity();
        groupStageEntity.setId("STAGE_ID");
        groupEntity.setStage(groupStageEntity);
        groups.add(groupEntity);
        List<String> centerIds = new ArrayList<>();
        centerIds.add(schoolId);

        // 数据准备 -- 接口模拟
        Mockito.when(userProvider.getCurrentUserId()).thenReturn(userId);
        Mockito.when(userProvider.checkUser(userId)).thenReturn(userEntity);
        Mockito.when(centerDao.getCenterAndGroupsByAgencyUserId(userId)).thenReturn(centerModels);
        Mockito.when(agencyDao.getMeta(agencyId, AgencyMetaKey.DROP_OFF_NOTE_OPEN_GROUP.toString())).thenReturn(agencyMetaDataEntity);
        Mockito.when(groupDao.getGroupsByCenterIds2(centerIds)).thenReturn(groups);
        List<CenterModel> centerModels1 = dropOffNoteService.getDropOffNoteClasses(agencyId);
        Assert.assertEquals(centerModels1.get(0).getId(), centerModel.getId());
    }

    /**
     * 测试关闭弹窗
     */
    @Test
    void updateDropOffNoteViewedStatus() {
        // 数据准备
        String userId = "U001";
        String childId = "C001";
        String agencyId = "A001";
        String timezone = "America/Los_Angeles";

        List<EnrollmentEntity> enrollmentEntities = new ArrayList<>();
        EnrollmentEntity enrollmentEntity = new EnrollmentEntity();
        enrollmentEntity.setId(childId);
        enrollmentEntity.setAgencyId(agencyId);
        enrollmentEntity.setDisplayName("child");
        enrollmentEntities.add(enrollmentEntity);

        AgencyEntity agency = new AgencyEntity();
        agency.setId(agencyId);

        // 数据准备 -- 接口模拟
        Mockito.when(studentDao.getChildByParent(userId)).thenReturn(enrollmentEntities);
        Mockito.when(agencyDao.getAgencyByChildId(childId)).thenReturn(agency);
        Mockito.when(centerDao.getFirstCenterTimeZoneByAgencyId(agencyId)).thenReturn(timezone);
        SuccessResponse response = dropOffNoteService.updateDropOffNoteViewedStatus(userId);
        Assert.assertEquals(response.isSuccess(), true);
    }

    /**
     * 测试小孩当天填写状态
     */
    @Test
    void getChildrenFormsFillInfo() {
        // 数据准备
        String userId = USER_ID;
        String childId = STUDENT_ID;
        String agencyId = AGENCY_ID;
        String classId = CLASS_ID;
        String formId = FORM_ID;
        String formType = FormType.DROP_OFF_NOTE.toString();
        String notifyDay = "1,2,3,4,5";
        String timeZone = "America/Los_Angeles";
        Date nowDate = TimeUtil.getUtcNow();
        Date localDate = TimeUtil.convertUtcToLocal(nowDate, timeZone);

        AgencyEntity agency = new AgencyEntity();
        agency.setId(agencyId);

        AgencyMetaDataEntity metaDataEntity = new AgencyMetaDataEntity();
        metaDataEntity.setAgencyId(agencyId);
        metaDataEntity.setMetaKey(AgencyMetaKey.DROP_OFF_NOTE_OPEN_GROUP.toString());
        metaDataEntity.setMetaValue(classId);


        AgencyMetaDataEntity metaDataEntity1 = new AgencyMetaDataEntity();
        metaDataEntity1.setAgencyId(agencyId);
        metaDataEntity1.setMetaKey(AgencyMetaKey.DROP_OFF_NOTE_OPEN.toString());
        metaDataEntity1.setMetaValue("true");

        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId(classId);

        List<EnrollmentEntity> enrollmentEntities = new ArrayList<>();
        EnrollmentEntity enrollmentEntity = new EnrollmentEntity();
        enrollmentEntity.setId(childId);
        enrollmentEntity.setAgencyId(agencyId);
        enrollmentEntity.setDisplayName("child");
        enrollmentEntity.setGroup(groupEntity);
        enrollmentEntities.add(enrollmentEntity);


        List<FormsFormEntity> formEntities = new ArrayList<>();
        FormsFormEntity formsFormEntity = new FormsFormEntity();
        formsFormEntity.setId(formId);
        formsFormEntity.setType(formType);
        formsFormEntity.setNotifyDay(notifyDay);
        formsFormEntity.setEnable(true);
        formEntities.add(formsFormEntity);

        FormsDropNoteEntity formsDropNoteEntity = new FormsDropNoteEntity();
        formsDropNoteEntity.setEnrollmentId(childId);
        formsDropNoteEntity.setFormId(formId);
        formsDropNoteEntity.setUserId(userId);
        formsDropNoteEntity.setCreateAtLocalDate(localDate);

        List<FormsDropNoteResponseRecordEntity> recordEntities = new ArrayList<>();
        FormsDropNoteResponseRecordEntity recordEntity = new FormsDropNoteResponseRecordEntity();
        recordEntity.setEnrollmentId(childId);
        recordEntity.setFormId(formId);
        recordEntity.setCreateUserId(userId);
        recordEntity.setResponseAtLocalDate(localDate);
        recordEntity.setId("R001");
        recordEntities.add(recordEntity);

        com.learninggenie.common.data.entity.GroupEntity group = new com.learninggenie.common.data.entity.GroupEntity();
        group.setId(classId);
        group.setName("class");

        // 数据准备 -- 接口模拟
        Mockito.when(studentDao.getChildByParent(userId)).thenReturn(enrollmentEntities);
        Mockito.when(agencyDao.getAgencyByChildId(childId)).thenReturn(agency);
//        Mockito.when(agencyDao.getMeta(agencyId, AgencyMetaKey.DROP_OFF_NOTE_OPEN.toString())).thenReturn(metaDataEntity1);
        Mockito.when(agencyService.getAgencyFunctionOpen(agencyId, AgencyMetaKey.DROP_OFF_NOTE_OPEN.toString())).thenReturn(true);
        Mockito.when(agencyDao.getMeta(agencyId, AgencyMetaKey.DROP_OFF_NOTE_OPEN_GROUP.toString())).thenReturn(metaDataEntity);
        Mockito.when(healthCheckFormDao.getFormByAgencyIdAndType(agencyId, FormType.DROP_OFF_NOTE.toString())).thenReturn(formEntities);
        Mockito.when(centerDao.getFirstCenterTimeZoneByAgencyId(AGENCY_ID)).thenReturn(timeZone);
        Mockito.when(healthCheckFormDao.getFormsDropOffNoteByFormIdAndChildIdAndDate(formId, userId, childId, TimeUtil.parse(localDate, TimeUtil.format10))).thenReturn(formsDropNoteEntity);
        Mockito.when(dropOffNoteDao.getDropOffNoteResponseRecordsByChildIdAndDateAndFormId(childId, TimeUtil.format(localDate, TimeUtil.format10), formId)).thenReturn(recordEntities);
        Mockito.when(groupDao.getGroupByChildId(childId)).thenReturn(group);
        ChildrenFormsFillInfoResponse response = dropOffNoteService.getChildrenFormsFillInfo(userId);
        Assert.assertEquals(response.getChildrenFillInfoList().get(0).isFormsFillStatus(), true);
    }

    /**
     * 提交 Drop-off Note 表单
     */
    @Test
    void submitForm() {
        // 数据准备
        String userId = USER_ID;
        String childId = STUDENT_ID;
        String agencyId = AGENCY_ID;
        String classId = CLASS_ID;
        String formId = FORM_ID;
        String formType = FormType.DROP_OFF_NOTE.toString();

        Date nowDate = TimeUtil.getUtcNow();
        String timeZone = "America/Los_Angeles";
        Date localDate = TimeUtil.convertUtcToLocal(nowDate, timeZone);

        AgencyEntity agency = new AgencyEntity();
        agency.setId(agencyId);

        AgencyMetaDataEntity metaDataEntity = new AgencyMetaDataEntity();
        metaDataEntity.setAgencyId(agencyId);
        metaDataEntity.setMetaKey(AgencyMetaKey.DROP_OFF_NOTE_OPEN_GROUP.toString());
        metaDataEntity.setMetaValue(classId);

        SubmitFormRequest request = new SubmitFormRequest();
        request.setChildId(childId);
        request.setFormId(formId);

        UserEntity currentUser = new UserEntity();
        currentUser.setId(userId);

        FormModel formModel = new FormModel();
        formModel.setId(formId);
        formModel.setType(formType);
        formModel.setDeleted(false);

        EnrollmentModel enrollmentModel = new EnrollmentModel();
        enrollmentModel.setId(childId);
        enrollmentModel.setAgencyId(agencyId);
        enrollmentModel.setDisplayName("child");

        ResponseRecord responseRecord = new ResponseRecord();
        responseRecord.setChildId(childId);
        responseRecord.setFormId(formId);
        responseRecord.setCreateUserId(userId);
        responseRecord.setCreateLocalDate(localDate);
        responseRecord.setChildName("child");
        responseRecord.setRelationship("relationship");

        // 数据准备 -- 接口模拟
        Mockito.when(userProvider.getCurrentUserId()).thenReturn(userId);
        Mockito.when(userProvider.getUser(userId)).thenReturn(currentUser);
        Mockito.when(agencyDao.getAgencyByChildId(childId)).thenReturn(agency);
        Mockito.when(centerDao.getFirstCenterTimeZoneByAgencyId(agencyId)).thenReturn(timeZone);
        Mockito.when(formDao.getFormDropOffNoteById(formId)).thenReturn(formModel);
        Mockito.when(studentDao.getEnrollment(childId)).thenReturn(enrollmentModel);
        Mockito.when(formDao.getDropOffNoteRecordByChildIdAndDateAndFormId(childId, TimeUtil.format(localDate, TimeUtil.format10), formId)).thenReturn(null);
        dropOffNoteService.submitForm(request);
        Mockito.when(formDao.getDropOffNoteRecordByChildIdAndDateAndFormId(childId, TimeUtil.format(localDate, TimeUtil.format10), formId)).thenReturn(responseRecord);
        assertThrows(BusinessException.class,
                () -> dropOffNoteService.submitForm(request));
    }

    /**
     * 获取 Drop-off Note 回复记录
     */
    @Test
    void getSubmittedFormDropOffNotes() {
        // 数据准备
        String userId = USER_ID;
        String childId = STUDENT_ID;
        String formId = FORM_ID;
        String fromDate = "2020-01-01";
        String toDate = "2032-01-02";

        List<ResponseRecord> responseRecords = new ArrayList<>();
        ResponseRecord responseRecord = new ResponseRecord();
        responseRecord.setChildId(childId);
        responseRecord.setFormId(formId);
        responseRecord.setCreateUserId(userId);
        responseRecord.setCreateLocalDate(TimeUtil.parse(fromDate, TimeUtil.format10));
        responseRecord.setChildName("child");
        responseRecord.setRelationship("relationship");
        String content = "{\"questions\":[{\"id\":\"Q001\",\"options\":[],\"content\":\"content\"}]}";
        responseRecord.setResponseData(content);
        responseRecords.add(responseRecord);

        List<QuestionModel> questions = new ArrayList<>();
        QuestionModel questionModel = new QuestionModel();
        questionModel.setId("Q001");
        questionModel.setName("question");
        questionModel.setFormId(formId);
        questionModel.setDeleted(false);
        questionModel.setType(QuestionType.MULTI_INPUT.toString());
        questionModel.setContent("content");
        questions.add(questionModel);

        List<String> formIds = new ArrayList<>();
        formIds.add(formId);

        // 数据准备 -- 接口模拟
        Mockito.when(formDao.getDropOffNoteRecordsByChildIdAndFormDateAndToDate(childId, fromDate, toDate)).thenReturn(responseRecords);
        Mockito.when(formDao.getQuestionsByFormIds(formIds)).thenReturn(questions);
        List<NoteResponse>  noteResponses = dropOffNoteService.getSubmittedFormDropOffNotes(childId, fromDate, toDate);
        Assert.assertEquals(noteResponses.size(), 1);
    }

    /**
     * 测试添加班级自动同步打开 Drop-off Note 功能
     */
    @Test
    void setNewGroupAddDropOffNote() {
        // 数据准备
        String agencyId = AGENCY_ID;
        String classId = CLASS_ID;

        AgencyMetaDataEntity metaDataEntity = new AgencyMetaDataEntity();
        metaDataEntity.setAgencyId(agencyId);
        metaDataEntity.setMetaKey(AgencyMetaKey.DROP_OFF_NOTE_OPEN.toString());
        metaDataEntity.setMetaValue("true");

        AgencyMetaDataEntity metaDataEntity1 = new AgencyMetaDataEntity();
        metaDataEntity1.setAgencyId(agencyId);
        metaDataEntity1.setId("id");
        metaDataEntity1.setMetaKey(AgencyMetaKey.DROP_OFF_NOTE_OPEN_GROUP.toString());
        metaDataEntity1.setMetaValue(classId);

        Map<String, List<String>> centerIdGroupMap = new HashMap<>();
        ArrayList<String> groupIds = new ArrayList<>();
        groupIds.add("group1");
        centerIdGroupMap.put("center1", groupIds);

        List<GroupEntity> groupEntities = new ArrayList<>();
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId("group2");
        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setId("center1");
        groupEntity.setCenter(centerEntity);
        groupEntities.add(groupEntity);


        // 数据准备 -- 接口模拟
        Mockito.when(agencyDao.getMeta(agencyId, AgencyMetaKey.DROP_OFF_NOTE_OPEN.toString())).thenReturn(metaDataEntity);
        Mockito.when(agencyDao.getMeta(agencyId, AgencyMetaKey.DROP_OFF_NOTE_OPEN_GROUP.toString())).thenReturn(metaDataEntity1);
        Mockito.when(groupDao.getGroupByAgency(agencyId)).thenReturn(groupEntities);
        groupProvider.addNewGroupToDropOffNote(agencyId, centerIdGroupMap);
//        Mockito.verify(agencyDao, Mockito.times(1)).updateMeta(metaDataEntity1.getId(), metaDataEntity1.getMetaValue());
    }

    /**
     * 测试编辑班级自动同步打开 Drop-off Note 功能
     */

    @Test
    void editGroupDropOffNote() {
        // 数据准备
        String agencyId = AGENCY_ID;
        String classId = CLASS_ID;

        AgencyMetaDataEntity metaDataEntity = new AgencyMetaDataEntity();
        metaDataEntity.setAgencyId(agencyId);
        metaDataEntity.setMetaKey(AgencyMetaKey.DROP_OFF_NOTE_OPEN.toString());
        metaDataEntity.setMetaValue("true");

        AgencyMetaDataEntity metaDataEntity1 = new AgencyMetaDataEntity();
        metaDataEntity1.setAgencyId(agencyId);
        metaDataEntity1.setMetaKey(AgencyMetaKey.DROP_OFF_NOTE_OPEN_GROUP.toString());
        metaDataEntity1.setId("id");
        metaDataEntity1.setMetaValue(classId);

        com.learninggenie.common.data.model.GroupEntity groupEntity = new com.learninggenie.common.data.model.GroupEntity();
        groupEntity.setId(classId);
        StageEntity stageEntity = new StageEntity();
        stageEntity.setId("72516154-3b50-e411-837d-02dbfc8648ce");
        groupEntity.setStage(stageEntity);
        com.learninggenie.common.data.model.CenterEntity centerEntity = new com.learninggenie.common.data.model.CenterEntity();
        centerEntity.setId("center1");
        groupEntity.setCenter(centerEntity);

        List<GroupEntity> groupEntities = new ArrayList<>();
        GroupEntity group = new GroupEntity();
        group.setId("group2");
        CenterEntity center = new CenterEntity();
        centerEntity.setId("center1");
        group.setCenter(center);
        groupEntities.add(group);

        // 数据准备 -- 接口模拟
        Mockito.when(agencyDao.getMeta(agencyId, AgencyMetaKey.DROP_OFF_NOTE_OPEN.toString())).thenReturn(metaDataEntity);
        Mockito.when(agencyDao.getMeta(agencyId, AgencyMetaKey.DROP_OFF_NOTE_OPEN_GROUP.toString())).thenReturn(metaDataEntity1);
        Mockito.when(groupDao.getGroupByAgency(agencyId)).thenReturn(groupEntities);
        groupProvider.editGroupDropOffNote(agencyId, groupEntity);
//        Mockito.verify(agencyDao, Mockito.times(1)).updateMeta(metaDataEntity1.getId(), metaDataEntity1.getMetaValue());
    }

    /**
     * 测试家长填写 Drop-off Note 弹窗
     */
    @Test
    void setDropOffNoteRemindStatus() {
        // 数据准备
        String childId = "Child1";
        String timeZone = "America/Los_Angeles";
        Date localDate = TimeUtil.convertUtcToLocal(TimeUtil.getUtcNow(), timeZone);

        FilledHealthFormRemindResponse response = new FilledHealthFormRemindResponse();
        List<EnrollmentEntity> children = new ArrayList<>();
        EnrollmentEntity enrollmentEntity = new EnrollmentEntity();
        enrollmentEntity.setId(childId);
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId(CLASS_ID);
        enrollmentEntity.setGroup(groupEntity);
        children.add(enrollmentEntity);

        AgencyEntity agency = new AgencyEntity();
        agency.setId(AGENCY_ID);

        AgencyMetaDataEntity metaDataEntity = new AgencyMetaDataEntity();
        metaDataEntity.setAgencyId(AGENCY_ID);
        metaDataEntity.setMetaKey(AgencyMetaKey.DROP_OFF_NOTE_OPEN.toString());
        metaDataEntity.setMetaValue("true");

        AgencyMetaDataEntity metaDataEntity1 = new AgencyMetaDataEntity();
        metaDataEntity1.setAgencyId(AGENCY_ID);
        metaDataEntity1.setMetaKey(AgencyMetaKey.DROP_OFF_NOTE_OPEN_GROUP.toString());
        metaDataEntity1.setMetaValue(CLASS_ID);

        List<FormsFormEntity> formEntities = new ArrayList<>();
        FormsFormEntity formsFormEntity = new FormsFormEntity();
        formsFormEntity.setId(FORM_ID);
        formsFormEntity.setType(FormType.DROP_OFF_NOTE.toString());
        formsFormEntity.setAgencyId(AGENCY_ID);
        formsFormEntity.setEnable(true);
        formEntities.add(formsFormEntity);

        FormsDropNoteEntity formsDropNoteEntity = new FormsDropNoteEntity();
        formsDropNoteEntity.setEnrollmentId(childId);
        formsDropNoteEntity.setTenantId(AGENCY_ID);
        formsDropNoteEntity.setFormId(FORM_ID);
        formsDropNoteEntity.setUserId(USER_ID);
        formsDropNoteEntity.setCreateAtLocalDate(TimeUtil.parse(TimeUtil.getUtcNow(), TimeUtil.format10));

        // 数据准备 -- 接口模拟
        Mockito.when(agencyDao.getAgencyByChildId(childId)).thenReturn(agency);
        Mockito.when(agencyDao.getMeta(AGENCY_ID, AgencyMetaKey.DROP_OFF_NOTE_OPEN.toString())).thenReturn(metaDataEntity);
        Mockito.when(agencyDao.getMeta(AGENCY_ID, AgencyMetaKey.DROP_OFF_NOTE_OPEN_GROUP.toString())).thenReturn(metaDataEntity1);
        Mockito.when(centerDao.getFirstCenterTimeZoneByAgencyId(AGENCY_ID)).thenReturn(timeZone);
        Mockito.when(healthCheckFormDao.getFormByAgencyIdAndType(AGENCY_ID, FormType.DROP_OFF_NOTE.toString())).thenReturn(formEntities);
        Mockito.when(healthCheckFormDao.getFormsDropOffNoteByFormIdAndChildIdAndDate(FORM_ID,  USER_ID, childId, TimeUtil.parse(localDate, TimeUtil.format10))).thenReturn(formsDropNoteEntity);
        Mockito.when(dropOffNoteDao.getDropOffNoteRecordCountByChildIdAndDateAndFormId(childId, TimeUtil.format(localDate, TimeUtil.format10), FORM_ID)).thenReturn(new Long(0));
        healthCheckFormService.setDropOffNoteRemindStatus(response, children, USER_ID);
        Assert.assertEquals(response.getDropNoteUnSubmitCount(), 1);
    }
}

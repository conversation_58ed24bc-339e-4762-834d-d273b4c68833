package com.learninggenie.api.service.lesson2;

import com.learninggenie.common.data.enums.TypicalBehaviorDisplayMode;
import org.junit.Test;
import static org.junit.Assert.assertEquals;

/**
 * 典型行为显示模式枚举测试
 */
public class TypicalBehaviorDisplayModeTest {

    @Test
    public void testFromCode_NewEnumValues() {
        // 测试新的枚举值
        assertEquals(TypicalBehaviorDisplayMode.DRDP_2015, 
            TypicalBehaviorDisplayMode.fromCode("DRDP_2015"));
        assertEquals(TypicalBehaviorDisplayMode.CA_PTKLF, 
            TypicalBehaviorDisplayMode.fromCode("CA_PTKLF"));
        assertEquals(TypicalBehaviorDisplayMode.DRDP_2025, 
            TypicalBehaviorDisplayMode.fromCode("DRDP_2025"));
    }

    @Test
    public void testFromCode_BackwardCompatibility() {
        // 测试向后兼容的 boolean 值
        assertEquals(TypicalBehaviorDisplayMode.CA_PTKLF, 
            TypicalBehaviorDisplayMode.fromCode("true"));
        assertEquals(TypicalBehaviorDisplayMode.CA_PTKLF, 
            TypicalBehaviorDisplayMode.fromCode("1"));
        
        assertEquals(TypicalBehaviorDisplayMode.DRDP_2015, 
            TypicalBehaviorDisplayMode.fromCode("false"));
        assertEquals(TypicalBehaviorDisplayMode.DRDP_2015, 
            TypicalBehaviorDisplayMode.fromCode("0"));
        assertEquals(TypicalBehaviorDisplayMode.CA_PTKLF,
            TypicalBehaviorDisplayMode.fromCode(""));
        assertEquals(TypicalBehaviorDisplayMode.CA_PTKLF,
            TypicalBehaviorDisplayMode.fromCode(null));
    }

    @Test
    public void testFromCode_CaseInsensitive() {
        // 测试大小写不敏感
        assertEquals(TypicalBehaviorDisplayMode.DRDP_2025, 
            TypicalBehaviorDisplayMode.fromCode("drdp_2025"));
        assertEquals(TypicalBehaviorDisplayMode.CA_PTKLF, 
            TypicalBehaviorDisplayMode.fromCode("ca_ptklf"));
    }

    @Test
    public void testShouldShowMappedBehaviors() {
        // 测试是否显示映射行为的逻辑
        assertEquals(false, TypicalBehaviorDisplayMode.DRDP_2015.shouldShowMappedBehaviors());
        assertEquals(true, TypicalBehaviorDisplayMode.CA_PTKLF.shouldShowMappedBehaviors());
        assertEquals(false, TypicalBehaviorDisplayMode.DRDP_2025.shouldShowMappedBehaviors());
    }

    @Test
    public void testIsDRDP2025() {
        // 测试是否为 DRDP 2025 模式
        assertEquals(false, TypicalBehaviorDisplayMode.DRDP_2015.isDRDP2025());
        assertEquals(false, TypicalBehaviorDisplayMode.CA_PTKLF.isDRDP2025());
        assertEquals(true, TypicalBehaviorDisplayMode.DRDP_2025.isDRDP2025());
    }
}
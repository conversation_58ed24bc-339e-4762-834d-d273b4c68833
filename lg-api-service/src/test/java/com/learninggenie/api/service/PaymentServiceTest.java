//package com.learninggenie.api.service;
//
//import com.google.gson.Gson;
//import com.learninggenie.api.model.CenterPaymentPlan;
//import com.learninggenie.api.model.ChargeResult;
//import com.learninggenie.api.service.impl.PaymentServiceImpl;
//import com.learninggenie.api.util.StringUtils;
//import com.learninggenie.common.data.entity.CenterEntity;
//import com.learninggenie.common.data.entity.CreditsRecordEntity;
//import com.learninggenie.common.data.entity.UserEntity;
//import com.learninggenie.common.data.entity.UserMetaDataEntity;
//import com.learninggenie.common.data.repository.*;
//import com.stripe.Stripe;
//import com.stripe.exception.*;
//import com.stripe.model.Plan;
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Ignore;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.*;
//import org.powermock.api.mockito.PowerMockito;
//import org.powermock.core.classloader.annotations.PrepareForTest;
//import org.powermock.modules.junit4.PowerMockRunner;
//
//import java.util.Date;
//
//@RunWith(PowerMockRunner.class)
//@PrepareForTest(StringUtils.class)
//public class PaymentServiceTest {
//    @InjectMocks
//    private PaymentService paymentService = new PaymentServiceImpl();
//    @Mock
//    private UserRepository userRepository;
//    @Mock
//    private CenterRepository centerRepository;
//    @Mock
//    private CenterPaymentPlanRepository paymentPlanRepository;
//    @Mock
//    private UserMetaDataRepository userMetaDataRepository;
//    @Mock
//    private CreditRecordRepository creditRecordRepository;
//    @Mock
//    private ReferRecordRepository referRecordRepository;
//    @Mock
//    private Stripe stripe;
//
//    @Before
//    public void setUp() throws AuthenticationException, InvalidRequestException, CardException {
//        MockitoAnnotations.initMocks(this);
//    }
//
//    @Ignore
//    @Test
//    public void testSubscribePlan() throws StripeException {
//        Plan plan = new Plan();
//        plan.setId("plan_001");
//        plan.setName("Small Plan");
//
//        CenterEntity center = new CenterEntity();
//        center.setId("center_001");
//        center.setName("Genie Center");
//
//        Mockito.when(Plan.retrieve(Mockito.anyString())).thenReturn(plan);
//        Mockito.when(centerRepository.findById("center_001")).thenReturn(Optional.ofNullable.thenReturn(center);
//
//
//        paymentService.subscribePlan("genie", "center_001", "plan_001","","");
//        Assert.assertNotNull(center.getPaymentPlan());
//    }
//
//    @Ignore
//    @Test
//    public void testGetSubscribedPlan() {
//        CenterPaymentPlan plan = paymentService.getSubscribedPlan("F41D906C-5219-E511-98B5-02678A71958D");
//        System.out.println(plan.getPlanName());
//        Assert.assertNotNull(plan);
//    }
//
//
//    @Ignore
//    @Test
//    public void testProcessPaymentResult() throws Exception{
//        String result = "{\n" +
//                //   "  \"created\": 1326853478,\n" +
//                "  \"livemode\": false,\n" +
//                "  \"id\": \"evt_00000000000000\",\n" +
//                "  \"type\": \"charge.succeeded\",\n" +
//                "  \"object\": \"event\",\n" +
//                "  \"request\": null,\n" +
//                "  \"pending_webhooks\": 1,\n" +
//                "  \"api_version\": \"2015-07-13\",\n" +
//                "  \"data\": {\n" +
//                "    \"object\": {\n" +
//                "      \"id\": \"ch_00000000000000\",\n" +
//                "      \"object\": \"charge\",\n" +
//                //   "      \"created\": 1437353239,\n" +
//                "      \"livemode\": false,\n" +
//                "      \"paid\": false,\n" +
//                "      \"status\": \"succeeded\",\n" +
//                "      \"amount\": 100,\n" +
//                "      \"currency\": \"usd\",\n" +
//                "      \"refunded\": false,\n" +
//                "      \"source\": {\n" +
//                "        \"id\": \"card_00000000000000\",\n" +
//                "        \"object\": \"card\",\n" +
//                "        \"last4\": \"4242\",\n" +
//                "        \"brand\": \"Visa\",\n" +
//                "        \"funding\": \"credit\",\n" +
//                "        \"exp_month\": 8,\n" +
//                "        \"exp_year\": 2016,\n" +
//                "        \"country\": \"US\",\n" +
//                "        \"name\": null,\n" +
//                "        \"address_line1\": null,\n" +
//                "        \"address_line2\": null,\n" +
//                "        \"address_city\": null,\n" +
//                "        \"address_state\": null,\n" +
//                "        \"address_zip\": null,\n" +
//                "        \"address_country\": null,\n" +
//                "        \"cvc_check\": null,\n" +
//                "        \"address_line1_check\": null,\n" +
//                "        \"address_zip_check\": null,\n" +
//                "        \"tokenization_method\": null,\n" +
//                "        \"dynamic_last4\": null,\n" +
//                "        \"metadata\": {},\n" +
//                "        \"customer\": null\n" +
//                "      },\n" +
//                "      \"captured\": true,\n" +
//                "      \"balance_transaction\": \"txn_00000000000000\",\n" +
//                "      \"failure_message\": null,\n" +
//                "      \"failure_code\": null,\n" +
//                "      \"amount_refunded\": 0,\n" +
//                "      \"customer\": null,\n" +
//                "      \"invoice\": null,\n" +
//                "      \"description\": \"My First Test Charge (created for API docs)\",\n" +
//                "      \"dispute\": null,\n" +
//                "      \"metadata\": {},\n" +
//                "      \"statement_descriptor\": null,\n" +
//                "      \"fraud_details\": {},\n" +
//                "      \"receipt_email\": null,\n" +
//                "      \"receipt_number\": null,\n" +
//                "      \"shipping\": null,\n" +
//                "      \"destination\": null,\n" +
//                "      \"application_fee\": null,\n" +
//                "      \"refunds\": {\n" +
//                "        \"object\": \"list\",\n" +
//                "        \"total_count\": 0,\n" +
//                "        \"has_more\": false,\n" +
//                "        \"url\": \"/v1/charges/ch_16QSR9EQp3cU0I8q9MQxyy29/refunds\",\n" +
//                "        \"data\": []\n" +
//                "      }\n" +
//                "    }\n" +
//                "  }\n" +
//                "}";
//
//        ChargeResult chargeResult = new Gson().fromJson(result, ChargeResult.class);
//        chargeResult.setCreated(new Date());
//        chargeResult.getData().getObject().setCreated(new Date().getTime());
//        final UserMetaDataEntity userMetaDataEntity = new UserMetaDataEntity();
//        userMetaDataEntity.setUser(new UserEntity());
//
//        Mockito.when(userMetaDataRepository.findTop1ByMetaKeyAndMetaValue(Mockito.anyString(), Mockito.anyString())).thenReturn(userMetaDataEntity);
//
//        paymentService.processPaymentResult(chargeResult.getData().getObject());
//        Mockito.verify(creditRecordRepository, Mockito.times(1)).saveAndFlush(Mockito.any(CreditsRecordEntity.class));
//    }
//}

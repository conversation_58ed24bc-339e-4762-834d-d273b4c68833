package com.learninggenie.api.service.impl;

import com.google.api.services.drive.Drive;
import com.google.auth.oauth2.AccessToken;
import com.google.auth.oauth2.GoogleCredentials;
import com.learninggenie.common.utils.TimeUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class GoogleDocsServiceImplTest {
    @InjectMocks
    private GoogleDriveServiceImpl googleDocsService;

    @Test
    public void buildDriveServiceTest() {
        GoogleCredentials credential = new GoogleCredentials(new AccessToken("token", TimeUtil.getUtcNow()));
        Drive drive = googleDocsService.buildDriveService(credential);
        assertEquals("https://www.googleapis.com/", drive.getRootUrl());
    }
}

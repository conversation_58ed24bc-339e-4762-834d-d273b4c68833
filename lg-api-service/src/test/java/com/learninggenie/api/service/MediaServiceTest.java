package com.learninggenie.api.service;

import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.model.*;
import com.learninggenie.api.model.agency.MediaResourceViewResponse;
import com.learninggenie.api.model.media.AdResponse;
import com.learninggenie.api.model.media.PreSignedRequest;
import com.learninggenie.api.model.media.PreSignedResponse;
import com.learninggenie.api.model.test.TestVideo;
import com.learninggenie.api.provider.RemoteProvider;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.service.impl.MediaServiceImpl;
import com.learninggenie.common.cache.CacheModel;
import com.learninggenie.common.cache.CacheService;
import com.learninggenie.common.data.dao.AgencyDao;
import com.learninggenie.common.data.dao.MediaBookDao;
import com.learninggenie.common.data.dao.MediaDao;
import com.learninggenie.common.data.dao.MetaDao;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.entity.*;
import com.learninggenie.common.data.enums.AppType;
import com.learninggenie.common.data.enums.Platform;
import com.learninggenie.common.data.model.AgencyModel;
import com.learninggenie.common.data.model.CreateMediaWithThumbnailRequest;
import com.learninggenie.common.data.model.UserModel;
import com.learninggenie.common.data.repository.MediaRepository;
import com.learninggenie.common.data.repository.NoteMediaRepository;
import com.learninggenie.common.data.repository.UserFileRepository;
import com.learninggenie.common.data.repository.UserRepository;
import com.learninggenie.common.filesystem.FileSystem;
import com.learninggenie.common.messaging.EmailModel;
import com.learninggenie.common.sharding.ShardingProvider;
import com.learninggenie.common.utils.CmdUtil;
import com.learninggenie.common.utils.ResourceUtil;
import com.learninggenie.common.utils.TimeUtil;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.core.env.Environment;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.times;

@RunWith(MockitoJUnitRunner.class)
public class MediaServiceTest {

    @InjectMocks
    private MediaServiceImpl mediaService;

    @Mock
    private FileSystem fileSystem;

    @Mock
    private CacheService cacheService;

    @Mock
    private UserProvider userProvider;

    @Mock
    private RemoteProvider remoteProvider;
    @Mock
    private UserRepository userRepository;
    @Mock
    private UserDaoImpl userDao;
    @Mock
    private AgencyDao agencyDao;
    @Mock
    private ShardingProvider shardingProvider;
    @Mock
    private MediaDao mediaDao;
    @Mock
    private Environment env;
    @Mock
    private MediaBookDao mediaBookDao;
    @Mock
    private com.learninggenie.common.messaging.EmailService emailService;
    @Mock
    private MetaDao metaDao;
    @Mock
    private UserFileRepository fileRepository;
    @Mock
    private MediaRepository mediaRepository;
    @Mock
    private NoteMediaRepository noteMediaRepository;

    @Ignore
    @Test
    public void testCreateMediaRecord() {
        MediaEntity media = new MediaEntity();
        media.setRelativePath("upload/test.png");
        mediaService.createMediaRecord(media);
        System.out.print(media.getId());
    }

    @Test
    public void testCreateThumbnail() throws IOException {
        // 数据准备
        // 请求体
        CreateMediaWithThumbnailRequest request = new CreateMediaWithThumbnailRequest();
        request.setBase64_file("base_64");
        request.setType("mp4");
        request.setHeight(480);
        request.setWidth(720);
        request.setKey("test.mp4");
        request.setDuration(25);
        request.setSize(1024 * 1024 * 20);
        request.setFileName("test.mp4");
        // media
        MediaEntity mediaEntity = new MediaEntity();
        mediaEntity.setFileType("mp4");
        mediaEntity.setId("mID");
        mediaEntity.setRelativePath("test.mp4");
        mediaEntity.setWeb(true);
        mediaEntity.setHeight(480);
        mediaEntity.setWidth(720);
        mediaEntity.setCompressed(false);
        // 数据模拟
        when(fileSystem.createMediaWithThumbnail(request)).thenReturn(mediaEntity);
        when(userProvider.getCurrentUserId()).thenReturn(null);
        ReflectionTestUtils.setField(mediaService, "defaultVideoCoverRelativePath", "");
        // 调用
        UploadMediaResponse mediaWithThumbnail = mediaService.createMediaWithThumbnail(request);
        // 验证
        Assert.assertEquals(mediaWithThumbnail.getId(), mediaEntity.getId());
    }

    @Test
    // 测试在输入有效时，获取下载文件响应方法返回响应
    public void getDownFileResponseReturnsResponseWhenInputsAreValid() throws IOException {
        // 定义有效的下载类型
        String downloadType = "validDownloadType";
        // 定义测试用户ID
        String userId = "testUserId";
        // 定义测试范围键
        String scopeKey = "testScopeKey";
        // 创建一个有效的生成文档文件对象
        File generateDocs = new File("validGenerateDocs");

        // 创建缓存模型对象
        CacheModel cacheModel = new CacheModel();
        cacheModel.setKey("testKey");
        cacheModel.setValue("testValue");
        // 当调用缓存服务的任意参数时，返回预设的缓存模型对象
        when(cacheService.get(anyString())).thenReturn(cacheModel);

        // 调用方法获取下载文件响应
        DownFileResponse result = mediaService.getDownFileResponse(downloadType, userId, scopeKey, generateDocs);

        // 确保结果不为空
        assertNotNull(result);
    }

    @Test
    // 测试在缓存模型为空时，获取下载文件响应方法返回空
    public void getDownFileResponseReturnsNullWhenCacheModelIsNull() throws IOException {
        // 定义下载类型为 Google Drive
        String downloadType = "google_drive";
        // 定义测试用户ID
        String userId = "testUserId";
        // 定义测试范围键
        String scopeKey = "testScopeKey";
        // 创建一个有效的生成文档文件对象
        File generateDocs = new File("validGenerateDocs");

        // 当调用缓存服务的任意参数时，返回空
        when(cacheService.get(anyString())).thenReturn(null);

        // 断言调用获取下载文件响应方法会抛出业务异常
        assertThrows(BusinessException.class, () -> {
            mediaService.getDownFileResponse(downloadType, userId, scopeKey, generateDocs);
        });
    }

    @Test
    // 测试在输入有效时，将HTML转换为图像方法返回路径
    public void convertHtmlToImageReturnsPathWhenInputsAreValid() {
        // 定义有效的HTML路径
        String htmlPath = "validHtmlPath";
        // 定义有效的参数列表
        List<String> args = Arrays.asList("validArg1", "validArg2");

        // 创建 MockedStatic 实例来模拟 CmdUtil 类的静态方法
        try (MockedStatic<CmdUtil> cmdUtilMockedStatic = Mockito.mockStatic(CmdUtil.class)) {
            // 指定当调用 executeCmd(cmd) 方法时不执行任何操作
            cmdUtilMockedStatic.when(() -> CmdUtil.executeCmd(Mockito.anyList())).thenAnswer(invocation -> {
                // 不执行任何操作，即模拟无返回值
                return null;
            });
            // 调用方法进行HTML转换为图像操作
            String result = mediaService.convertHtmlToImage(htmlPath, args);
            // 确保结果为空
            assertNull(result);
        }
    }

    @Test
    // 测试将HTML转换为图像
    public void convertHtmlToImage() {
        // 定义有效的HTML路径
        String htmlPath = "validHtmlPath";
        // 定义有效的参数列表
        List<String> args = Arrays.asList("validArg1", "validArg2");

        // 创建 MockedStatic 实例来模拟 CmdUtil 类的静态方法
        try (MockedStatic<CmdUtil> cmdUtilMockedStatic = Mockito.mockStatic(CmdUtil.class)) {
            // 指定当调用 executeCmd(cmd) 方法时不执行任何操作
            cmdUtilMockedStatic.when(() -> CmdUtil.executeCmd(Mockito.anyList())).thenAnswer(invocation -> {
                // 不执行任何操作，即模拟无返回值
                return null;
            });
            // 调用方法进行HTML转换为图像操作
            String result = mediaService.convertHtmlToImage(htmlPath, String.join(" ", args));
            // 确保结果为空
            assertNull(result);
        }
    }

    @Test
    public void addSpecialMediasTest() {
        when(userProvider.getCurrentUserId()).thenReturn("userId");
        UserEntity user = new UserEntity();
        user.setRole("COLLABORATOR");
        when(userRepository.findById(anyString())).thenReturn(Optional.of(user));

        // 数据准备
        List<AgencyModel> agencyModels = new ArrayList<>();
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(UUID.randomUUID().toString());
        agencyModels.add(agencyModel);
        when(userDao.getAgencyByTeacherId(anyString())).thenReturn(agencyModels);

        List<UserModel> users = new ArrayList<>();
        UserModel userModel = new UserModel();
        userModel.setType("GRANTEE");
        userModel.setId(UUID.randomUUID().toString());
        users.add(userModel);
        when(userDao.getAgencyOwnerByAgencyId(anyString())).thenReturn(users);

        List<AgencyEntity> agencyEntities = new ArrayList<>();
        AgencyEntity agency = new AgencyEntity();
        agency.setId(UUID.randomUUID().toString());
        agencyEntities.add(agency);
        when(agencyDao.getAgencyByIdentifierUserId(any())).thenReturn(agencyEntities);

        doNothing().when(shardingProvider).resetShardingHint(anyString());
        MediaEntity mediaEntity = new MediaEntity();
        doNothing().when(mediaDao).insertNoteMediaAll(mediaEntity);

        //方法调用
        mediaService.addSpecialMedias(mediaEntity);

        // 验证结果
        verify(userDao, times(1)).getAgencyOwnerByAgencyId(anyString());
        verify(agencyDao, times(1)).getAgencyByIdentifierUserId(any());
    }

    @Test
    public void getPreSignedUrlTest() {
        // 数据准备
        PreSignedRequest request = new PreSignedRequest();
        request.setPrivateFile(true);
        request.setFeature("Feature");
        request.setType("type");
        when(env.getProperty(anyString())).thenReturn("bucket");
        // 方法调用
        PreSignedResponse preSignedUrl = mediaService.getPreSignedUrl(request);
        // 验证结果
        assertNotNull(preSignedUrl);
        assertEquals("type", preSignedUrl.getFileType());
    }

    @Test
    public void getAllVideosTest() {
        // 数据准备
        List<MediaResourceEntity> resources = new ArrayList<>();
        MediaResourceEntity mediaResourceEntity = new MediaResourceEntity();
        mediaResourceEntity.setVideoList("video1,video2,video3");
        resources.add(mediaResourceEntity);
        // 模拟方法调用
        when(mediaBookDao.getAllMediaResource()).thenReturn(resources);
        // 验证结果
        List<TestVideo> allVideos = mediaService.getAllVideos();
        assertEquals(3, allVideos.size());
        assertEquals("video1", allVideos.get(0).getId());
    }

    @Test
    public void sendTryOutRemindEmailTest(){
        UserEntity user=new UserEntity();
        user.setEmail("email");
        user.setRole("AGENCY_ADMIN");
        when(userProvider.checkUser(anyString())).thenReturn(user);

        UserModel userModel=new UserModel();
        userModel.setEmail("email");
        when(userDao.getUserById(anyString())).thenReturn(userModel);

        AdEntity ad=new AdEntity();
        // 添加收件人
        ad.setAdTryOutEmails("adTryOutEmails");
        when( mediaDao.getAdById(anyString())).thenReturn(ad);

        AgencyEntity agency = new AgencyEntity();
        agency.setName("agencyName");
        agency.setId("agencyId");
        when(agencyDao.getByAgencyAdmin(anyString())).thenReturn(agency);

        List<UserModel> owners=new ArrayList<>();
        owners.add(userModel);
        when(userDao.getAgencyOwnerByAgencyId(anyString())).thenReturn(owners);
        doNothing().when(emailService).sendAsync(any(EmailModel.class));
        // 方法调用
        AdResponse adResponse = mediaService.sendTryOutRemindEmail("userId", "adId");
        // 验证结果
        assertEquals(true,adResponse.getShowStatus());
        assertEquals(true,adResponse.getAutoOpenStatus());
        assertEquals(0,adResponse.getMediaUrls().size());
    }

    @Test
    public void setAutoOpenSwitchTest() {
        when(userProvider.checkUser("userId")).thenReturn(null);
        AdEntity ad = new AdEntity();
        when(mediaDao.getAdById(anyString())).thenReturn(ad);
        doNothing().when(userDao).setMetaData(anyString(), anyString(), anyString());
        // 方法调用
        AdResponse adResponse = mediaService.setAutoOpenSwitch("userId", "adId", true);
        // 验证结果
        assertEquals(true, adResponse.getShowStatus());
        assertEquals(true, adResponse.getAutoOpenStatus());
        assertEquals(0, adResponse.getMediaUrls().size());
    }

    @Test
    public void getAdMediaTest() {
        UserEntity user = new UserEntity();
        user.setRole("AGENCY_ADMIN");
        when(userProvider.checkUser(anyString())).thenReturn(user);

        // 模拟只有在白名单里面的 Agency 才推送广告
        AgencyEntity agency = new AgencyEntity();
        agency.setId("AgencyId");
        when(agencyDao.getByAgencyAdmin(anyString())).thenReturn(agency);
        when(metaDao.getAppMeta(anyString())).thenReturn("openAdAgencyIds");

        AdEntity currentAd = new AdEntity();
        currentAd.setId("currentAdId");
        currentAd.setAdText("AdText");
        currentAd.setAdType("AdType");
        currentAd.setAdRelativePath("AdRelativePath");
        when(mediaDao.getCurrentAdEntity(anyString(), anyString())).thenReturn(currentAd);

        UserMetaDataEntity autoOpenSwitch = new UserMetaDataEntity();
        autoOpenSwitch.setMetaValue("metavalue");
        when(userDao.getMetaData(anyString(), anyString())).thenReturn(autoOpenSwitch);

        // 判断是否设置了不再显示
        AdResponse adMedia = mediaService.getAdMedia("userId", "localDate");
        assertEquals(true, adMedia.getShowStatus());
        assertEquals(false, adMedia.getAutoOpenStatus());
        assertEquals("AdType", adMedia.getAdType());
        assertEquals(1, adMedia.getMediaUrls().size());
    }

    @Test
    public void getMediaUrlTest() {
        // 数据准备
        MediaEntity media = new MediaEntity();
        media.setRelativePath("RelativePath.jpg");
        media.setCreateAtUtc(TimeUtil.getUtcNow());

        // 方法调用
        mediaService.getMediaUrl(media, MediaType.CENTERLOGO);
        mediaService.getMediaUrl(media, MediaType.USERAVATAR);
        mediaService.getMediaUrl(media, MediaType.CHILDAVATAR);
        mediaService.getMediaUrl(media, MediaType.ACTIVITYPHOTO);

        // 结果验证
        verify(fileSystem, times(4)).getPublicUrl(anyString());
    }

    @Test
    public void GetPopularSearchTest() {
        UserEntity userEntity = new UserEntity();
        when(userRepository.findById(anyString())).thenReturn(Optional.of(userEntity));
        AgencyModel agency = new AgencyModel();
        agency.setId("agencyId");
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agency);

        // 数据准备
        List<MediaResourceEntity> customChannel = new ArrayList<>();
        MediaResourceEntity mediaResourceEntity = new MediaResourceEntity();
        mediaResourceEntity.setSortIndex(2);
        mediaResourceEntity.setPlaylistId("1");
        mediaResourceEntity.setCategory("category");
        mediaResourceEntity.setVideoList("1,2");
        customChannel.add(mediaResourceEntity);
        when(mediaDao.getCustomChannel(anyString(), anyString())).thenReturn(customChannel);

        // 模拟查询所有免费的项目mediaDao.getFreeMediaResourc
        AgencyMetaDataEntity meta = new AgencyMetaDataEntity();
        meta.setMetaValue("metaValue");
        when(agencyDao.getMeta(anyString(), anyString())).thenReturn(meta);
        when(mediaDao.getFreeMediaResource(anyString())).thenReturn(customChannel);
        when(userProvider.isVersion(anyInt(), anyInt(), any(Platform.class), any(AppType.class))).thenReturn(true);

        // 增加频道视频数量
        when(mediaBookDao.getMediaResourceByCategory(anyString())).thenReturn(customChannel);

        // 方法调用
        List<MediaResourceViewResponse> mediaResourceViewResponses = mediaService.GetPopularSearch("userId", "type");
        assertEquals(1, mediaResourceViewResponses.size());
        assertEquals(2, mediaResourceViewResponses.get(0).getPlaylist_ids().size());
        assertEquals(2, mediaResourceViewResponses.get(0).getVideoSize());
        assertEquals("system", mediaResourceViewResponses.get(0).getType());
    }

    @Test
    public void deleteMetaDataTest() {
        // 模拟删除调用
        doNothing().when(mediaDao).deleteMetaData(anyString());
        mediaService.deleteMetaData("1");

        // 结果验证
        verify(mediaDao, times(1)).deleteMetaData(anyString());
    }

    @Test
    public void createChannelsTest() {
        UserEntity user = new UserEntity();
        user.setId("userId");
        when(userProvider.checkUserByEmail(anyString())).thenReturn(user);
        AgencyModel agency = new AgencyModel();
        agency.setId("agenctId");
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agency);

        // 模拟已经存在付费项目，更新
        AgencyMetaDataEntity metaData = new AgencyMetaDataEntity();
        metaData.setId("metaDataId");
        when(agencyDao.getMeta(anyString(), anyString())).thenReturn(metaData);
        doNothing().when(agencyDao).updateMeta(anyString(), anyString());

        // 方法调用
        mediaService.createChannels("email", "channelsIds");

        // 结果验证
        verify(userProvider, times(1)).checkUserByEmail(anyString());
        verify(userProvider, times(1)).getAgencyByUserId(anyString());
        verify(agencyDao, times(1)).getMeta(anyString(), anyString());
        verify(agencyDao, times(1)).updateMeta(anyString(), anyString());
    }

    @Test
    public void batchUpdateChannelsTest() {
        doNothing().when(mediaDao).updateMediasResourcesPaidByCategory(anyString(), anyInt());
        mediaService.batchUpdateChannels("categories", 1);

        // 结果验证
        verify(mediaDao, times(1)).updateMediasResourcesPaidByCategory(anyString(), anyInt());
    }

    @Test
    public void getMediaResourceTest() {
        // 数据准备
        String category = UUID.randomUUID().toString();
        List<MediaResourceEntity> mediaResourceEntities = new ArrayList<>();
        MediaResourceEntity mediaResourceEntity = new MediaResourceEntity();
        mediaResourceEntity.setCategory(category);
        mediaResourceEntities.add(mediaResourceEntity);
        when(mediaDao.getMediasResources()).thenReturn(mediaResourceEntities);

        // 方法调用
        List<MediaResourceEntity> mediaResource = mediaService.getMediaResource();

        // 结果验证
        assertNotNull(mediaResource);
        assertEquals(1, mediaResource.size());
        assertEquals(category, mediaResource.get(0).getCategory());
    }

    @Test
    public void getFileTest() {
        // 数据准备
        UserFileEntity file = new UserFileEntity();
        MediaEntity media = new MediaEntity();
        media.setRelativePath("RelativePath");
        file.setMedia(media);
        when(fileRepository.findById(anyString())).thenReturn(Optional.of(file));

        InputStream fileStream = new InputStream() {
            @Override
            public int read() throws IOException {
                return 0;
            }
        };
        when(fileSystem.getFileStream(anyString())).thenReturn(fileStream);
        FileInfo fileId = mediaService.getFile("fileId");

        // 结果验证
        assertNotNull(fileId);
    }

//    @Test
//    public void replacePhoto() throws IOException {
//        // 数据准备
//        Photo photo = new Photo();
//        photo.setId("photoId");
//
//        // 读取图片数据
//        File resourceAsFile = ResourceUtil.getResourceAsFile("watermark.png");
//        byte[] imageBytes = Files.readAllBytes(resourceAsFile.toPath());
//        String s = Base64.getEncoder().encodeToString(imageBytes);
//        photo.setImageString(s);
//
//        MediaEntity mediaEntity = new MediaEntity();
//        mediaEntity.setRelativePath(".RelativePath");
//        when(mediaRepository.findById(anyString())).thenReturn(Optional.of(mediaEntity));
//        NoteMediaEntity noteMediaEntity = new NoteMediaEntity();
//        when(noteMediaRepository.findById(anyString())).thenReturn(Optional.of(noteMediaEntity));
//
//        // 方法调用
//        mediaService.replacePhoto(photo);
//
//        // 结果验证
//        verify(fileSystem, times(1)).upload(anyString(), any(File.class));
//        verify(noteMediaRepository, times(1)).saveAndFlush(any(NoteMediaEntity.class));
//    }

    @Test
    public void createMediaRecordTest() {
        // 数据准备
        String mediaEntityId = UUID.randomUUID().toString();
        MediaEntity mediaEntity = new MediaEntity();
        mediaEntity.setId(mediaEntityId);
        when(mediaRepository.save(any(MediaEntity.class))).thenReturn(null);

        // 方法调用
        MediaEntity mediaRecord = mediaService.createMediaRecord(mediaEntity);

        // 结果验证
        assertEquals(mediaEntityId, mediaRecord.getId());
    }
}

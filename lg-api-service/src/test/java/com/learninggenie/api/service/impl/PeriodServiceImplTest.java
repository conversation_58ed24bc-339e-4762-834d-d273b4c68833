package com.learninggenie.api.service.impl;

import com.learninggenie.api.model.DownFileResponse;
import com.learninggenie.api.model.SchoolYearModel;
import com.learninggenie.api.provider.RemoteProvider;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.common.data.dao.*;
import com.learninggenie.common.data.entity.*;
import com.learninggenie.common.data.entity.UserEntity;
import com.learninggenie.common.data.enums.AgencyMetaKey;
import com.learninggenie.common.data.model.*;
import com.learninggenie.common.data.model.CenterEntity;
import com.learninggenie.common.data.model.period.ChildPeriodModel;
import com.learninggenie.common.data.repository.UserRepository;
import com.learninggenie.common.filesystem.FileSystem;
import com.learninggenie.common.score.RatingService;
import com.learninggenie.common.score.model.RatingStatisticModel;
import com.learninggenie.common.utils.TimeUtil;
import org.junit.Assert;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PeriodServiceImplTest {
    @InjectMocks
    private PeriodServiceImpl periodServiceImpl;
    @Mock
    private PeriodsGroupDao periodsGroupDao;
    @Mock
    private UserRepository userRepository;
    @Mock
    private CenterDao centerDao;
    @Mock
    private GroupDao groupDao;
    @Mock
    private StudentDao studentDao;
    @Mock
    private UserProvider userProvider;
    @Mock
    private RemoteProvider remoteProvider;

    @Mock
    private AgencyDao agencyDao;

    @Mock
    private DomainDao domainDao;

    @Mock
    private RatingService ratingService;

    @Mock
    private FileSystem fileSystem;

    @Test
    void testSetGroups() {
        PeriodsGroupEntity periodsGroupEntity = new PeriodsGroupEntity();
        periodsGroupEntity.setAgencyInfo(new AgencyInfo());
        periodsGroupEntity.setCenters(new ArrayList<>());
        periodsGroupEntity.setEntryDates(new ArrayList<>());
        periodsGroupEntity.setFilter(true);
        periodsGroupEntity.setFirstAssessmentDuration(1);
        periodsGroupEntity.setGroupId("42");
        periodsGroupEntity.setGroupIds(new ArrayList<>());
        periodsGroupEntity.setId("42");
        periodsGroupEntity.setIsDeleted((byte) 'A');
        periodsGroupEntity.setLateEnrolType("Late Enrol Type");
        periodsGroupEntity.setLatePolicys(new ArrayList<>());
        periodsGroupEntity.setMinimumDuration("Minimum Duration");
        periodsGroupEntity.setName("Name");
        periodsGroupEntity.setNoSetted(true);
        periodsGroupEntity.setPeriodGroupStatus("Period Group Status");
        periodsGroupEntity.setPeriodGroupType("Period Group Type");
        periodsGroupEntity.setPeriods(new ArrayList<>());
        periodsGroupEntity.setPolicy("Policy");
        periodsGroupEntity.setRole("Role");
        periodsGroupEntity.setSameGroup(true);
        periodsGroupEntity.setSchoolYear("School Year");
        periodsGroupEntity.setSetted(true);
        periodsGroupEntity.setSettedMix(true);
        periodsGroupEntity.setShortPeriodDays("Short Period Days");
        periodsGroupEntity.setType("Type");
        periodsGroupEntity.setUpdateType("2020-03-01");
        periodsGroupEntity.setUserId("42");
        periodsGroupEntity.setUserName("janedoe");
        when(periodsGroupDao.queryGroups(any(), any()))
                .thenReturn(new ArrayList<>());
        when(userRepository.findById(any())).thenReturn(Optional.of(new UserEntity()));
        when(centerDao.getCentersByAgencyId(any()))
                .thenReturn(new ArrayList<>());
        when(groupDao.getGroupsByPeriodGroupId(any()))
                .thenReturn(new ArrayList<>());
        when(studentDao.getChildrenByGroupIds(any()))
                .thenReturn(new ArrayList<>());
        when(userProvider.getCurrentAgencyId())
                .thenReturn("");
        // 设置新的周期组, 会调用远程接口
        periodServiceImpl.setGroups(periodsGroupEntity);
        verify(remoteProvider, times(1))
                .callDashboardUpsertServer(any(), any(), any());
        // 清除所有的mock
        reset(remoteProvider, userProvider, studentDao, groupDao,
                centerDao, userRepository, periodsGroupDao);

        periodsGroupEntity.setNoSetted(false);
        when(periodsGroupDao.queryGroups(any(), any()))
                .thenReturn(new ArrayList<>());

        // 设置新的周期组, 会调用远程接口
        periodServiceImpl.setGroups(periodsGroupEntity);
        verify(remoteProvider, times(1))
                .callDashboardUpsertServer(any(), any(), any());
    }

    @Test
    void testUpdateEnrollmentPeriodActive() {
        List<RatingPeriodEntity> saveRatingPeriodsTemp = new ArrayList<>();
        RatingPeriodEntity ratingPeriodEntity = new RatingPeriodEntity();
        ratingPeriodEntity.setActived(true);
        ratingPeriodEntity.setEnrollmentId("42");
        ratingPeriodEntity.setAlias("2022-2023 time1");
        saveRatingPeriodsTemp.add(ratingPeriodEntity);
        // 修改小孩周期，修改学年包含有活跃周期，则清除其他学年的活跃周期
        periodServiceImpl.updateEnrollmentPeriodActive(saveRatingPeriodsTemp, "2022-2023");
        // 小孩当前学年存在活跃周期才去清除其他学年的活跃周期
        verify(studentDao, times(1)).batchUpdatePeriodActive(any(), any());
        // 清除所有的mock
        reset(studentDao);

        // 修改小孩周期，修改学年不包含有活跃周期，则不清除其他学年的活跃周期
        periodServiceImpl.updateEnrollmentPeriodActive(saveRatingPeriodsTemp, "2023-2024");
        // 小孩当前学年存在活跃周期才去清除其他学年的活跃周期
        verify(studentDao, times(0)).batchUpdatePeriodActive(any(), any());
        // 清除所有的mock
        reset(studentDao);

        ratingPeriodEntity.setActived(false);
        // 修改小孩周期，修改学年不包含有活跃周期，则不清除其他学年的活跃周期
        periodServiceImpl.updateEnrollmentPeriodActive(saveRatingPeriodsTemp, "2022-2023");
        // 小孩当前学年存在活跃周期才去清除其他学年的活跃周期
        verify(studentDao, times(0)).batchUpdatePeriodActive(any(), any());
        // 清除所有的mock
        reset(studentDao);

    }

    /**
     * Case: 小孩的 metaData 中没有当前周期的完成时间但小孩完成了所有评分
     * 结果: 导出的数据中，完成时间为所有评分中最晚的完成时间
     */
    @Test
    public void testExportChildPeriod() {
        // 准备数据
        String agencyId = UUID.randomUUID().toString(); // 机构 Id
        String currentTime = "2023-11-13 11:20:22.820"; // 当前时间
        String centerId = ""; // 学校 Id
        String groupId = UUID.randomUUID().toString(); // 班级 Id
        String schoolYear = "2023-2024"; // 学年
        String userId = UUID.randomUUID().toString(); // 用户 Id
        // mock 用户 Id
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        // 机构信息
        AgencyModel agency = new AgencyModel();
        agency.setId(agencyId); // 设置机构 Id
        agency.setName("Learning Genie"); // 设置机构名称
        // mock 机构信息
        when(userProvider.getAgencyByUserIdOrAgencyId(anyString(), anyString())).thenReturn(agency);
        // 机构是否开启完成所有测评点才能锁定开关信息
        AgencyMetaDataEntity allMeasuresMeta = new AgencyMetaDataEntity();
        allMeasuresMeta.setMetaValue("false"); // 设置机构未开启完成所有测评点才能锁定开关
        // mock 机构是否开启完成所有测评点才能锁定开关信息
        when(agencyDao.getMeta(agencyId, AgencyMetaKey.COMPLETE_RATING_REQUIRED_FOR_CHILD_LOCK.toString())).thenReturn(allMeasuresMeta);
        // 机构是否开启关键测评点开关
        AgencyMetaDataEntity keyMeasuresMeta = new AgencyMetaDataEntity();
        keyMeasuresMeta.setMetaValue("true"); // 设置机构开启关键测评点开关
        // mock 机构是否开启关键测评点开关
        when(agencyDao.getMeta(agencyId, AgencyMetaKey.DRDP_KEY_MEASURE_SETTING.toString())).thenReturn(keyMeasuresMeta);
        String childId = "E163164F-BDCE-E411-AF66-02C72B94B99B"; // 小孩 Id
        Date fromAtLocal = new Date(); // 开始时间
        Date toAtLocal = new Date(); // 结束时间
        String alias = "2023-2024 Time4"; // 周期别名
        String frameworkId = UUID.randomUUID().toString(); // 框架 Id
        String timezone = "America/Los_Angeles"; // 时区
        // 小孩本学年所有周期列表
        List<ChildPeriodModel> childPeriods = new ArrayList<>();
        // 小孩周期信息
        ChildPeriodModel childPeriod = new ChildPeriodModel();
        childPeriod.setEnrollmentId(childId); // 设置小孩 Id
        childPeriod.setFromAtLocal(fromAtLocal); // 设置开始时间
        childPeriod.setToAtLocal(toAtLocal); // 设置结束时间
        childPeriod.setAlias(alias); // 设置周期别名
        childPeriod.setFrameworkId(frameworkId); // 设置框架 Id
        childPeriod.setDomainId(frameworkId); // 设置领域 Id
        childPeriod.setTimezone(timezone); // 设置时区
        childPeriods.add(childPeriod); // 添加小孩周期信息
        // mock 小孩本学年所有周期列表
        when(studentDao.getPeriodsByGroupIdsAndSchoolYear(anyList(), anyString())).thenReturn(childPeriods);
        // 学校信息
        CenterEntity center = new CenterEntity();
        center.setId(UUID.randomUUID().toString()); // 设置学校 Id
        center.setName("School 1"); // 设置学校名称
        // 班级信息
        com.learninggenie.common.data.model.GroupEntity groupEntity = new com.learninggenie.common.data.model.GroupEntity();
        groupEntity.setId(groupId); // 设置班级 Id
        groupEntity.setName("Class 1"); // 设置班级名称
        groupEntity.setCenter(center); // 设置学校信息
        // mock 班级信息
        when(groupDao.getGroupWithCenter(groupId)).thenReturn(groupEntity); // mock 班级信息
        // 班级信息
        com.learninggenie.common.data.entity.GroupEntity group = new com.learninggenie.common.data.entity.GroupEntity();
        group.setId(groupId); // 设置班级 Id
        group.setName("Class 1"); // 设置班级名称
        // 小孩信息列表
        List<EnrollmentEntity> enrollmentEntities = new ArrayList<>();
        // 小孩信息
        EnrollmentEntity enrollmentEntity = new EnrollmentEntity();
        enrollmentEntity.setId(childId); // 设置小孩 Id
        enrollmentEntity.setFrameworkId(frameworkId); // 设置框架 Id
        enrollmentEntity.setFirstName("John"); // 设置小孩名
        enrollmentEntity.setLastName("Doe"); // 设置小孩姓
        Date entryDate = new Date(2020, 8, 15); // 入学日期
        Date birthDate = new Date(2018, 8, 15); // 生日
        enrollmentEntity.setBirthDate(birthDate); // 设置小孩生日
        enrollmentEntity.setEnrollmentDate(entryDate); // 设置小孩入学日期
        enrollmentEntity.setGroup(group); // 设置班级信息
        enrollmentEntities.add(enrollmentEntity); // 添加小孩信息
        // mock 小孩信息列表
        when(studentDao.getChildrenByGroupIds(anyList())).thenReturn(enrollmentEntities);
        // 小孩的完成评分时间信息列表
        List<EnrollmentMetaDataEntity> metaDataEntities = new ArrayList<>();
        // 小孩的完成评分时间信息
        EnrollmentMetaDataEntity metaDataEntity = new EnrollmentMetaDataEntity();
        metaDataEntity.setMetaValue("2023-2024 Time4|11/10/2023|E163164F-BDCE-E411-AF66-02C72B94B99B"); // 设置完成评分时间信息
        metaDataEntity.setChildId(childId); // 设置小孩 Id
        metaDataEntities.add(metaDataEntity); // 添加小孩的完成评分时间信息
        // mock 小孩的完成评分时间信息列表
        when(studentDao.getMetasByChildIds(anyList(), anyString())).thenReturn(metaDataEntities);
        // 机构下所有框架的核心测评点信息列表
        List<KeyMeasureEntity> keyMeasuresSetting = new ArrayList<>();
        String measureId = UUID.randomUUID().toString(); // 测评点 Id
        KeyMeasureEntity keyMeasures = new KeyMeasureEntity();
        keyMeasures.setDomainId(frameworkId); // 设置框架 Id
        keyMeasures.setMeasureId(measureId); // 设置测评点 Id
        keyMeasuresSetting.add(keyMeasures); // 添加测评点信息
        // mock 机构下所有框架的核心测评点信息列表
        when(domainDao.getKeyMeasuresSetting(anyString(), anyList())).thenReturn(keyMeasuresSetting);
        // 小孩的完成时间信息
        RatingStatisticModel ratingStatistic = new RatingStatisticModel();
        ratingStatistic.setCompleted(true); // 设置完成状态
        ratingStatistic.setLastRateAtUtc(new Date()); // 设置完成时间
        // mock 小孩的完成时间信息
        when(ratingService.isCompleted(anyString(), anyString(), anyString(), anyString(), anyMap())).thenReturn(ratingStatistic);
        // 文件地址
        String url = "https://www.learning-genie.com/learning-genie";
        // mock 生成文件地址
        when(fileSystem.getPublicUrl(anyString())).thenReturn(url);


        // 调用方法
        DownFileResponse downFileResponse = periodServiceImpl.exportChildPeriod(agencyId, currentTime, centerId, groupId, schoolYear);

        // 验证结果 生成文件地址相同
        Assertions.assertEquals(url, downFileResponse.getUrl());
    }

    @Test
    public void testGetSchoolYearsData() {
        // 准备数据
        Date date = TimeUtil.parse("2024-01-01", TimeUtil.format10);
        // 调用方法
        List<SchoolYearModel> schoolYearsData = periodServiceImpl.getSchoolYearsData(date);
        // 验证结果
        Assertions.assertEquals(2, schoolYearsData.size()); // 验证学年数量为 2
        Assertions.assertEquals("2023-2024", schoolYearsData.get(0).getValue()); // 验证第一个学年值为 2023-2024
        Assertions.assertEquals("current", schoolYearsData.get(0).getSchoolYear()); // 验证 2023-2024 学年为当前学年
        Assertions.assertEquals("2024-2025", schoolYearsData.get(1).getValue()); // 验证第二个学年值为 2024-2025
        Assertions.assertEquals("next", schoolYearsData.get(1).getSchoolYear()); // 验证 2024-2025 学年为下一个学年

        // 准备数据
        date = TimeUtil.parse("2024-03-31", TimeUtil.format10);
        // 调用方法
        schoolYearsData = periodServiceImpl.getSchoolYearsData(date);
        // 验证结果
        Assertions.assertEquals(2, schoolYearsData.size()); // 验证学年数量为 2
        Assertions.assertEquals("2023-2024", schoolYearsData.get(0).getValue()); // 验证第一个学年值为 2023-2024
        Assertions.assertEquals("current", schoolYearsData.get(0).getSchoolYear()); // 验证 2023-2024 学年为当前学年
        Assertions.assertEquals("2024-2025", schoolYearsData.get(1).getValue()); // 验证第二个学年值为 2024-2025
        Assertions.assertEquals("next", schoolYearsData.get(1).getSchoolYear()); // 验证 2024-2025 学年为下一个学年

        // 准备数据
        date = TimeUtil.parse("2024-04-01", TimeUtil.format10);
        // 调用方法
        schoolYearsData = periodServiceImpl.getSchoolYearsData(date);
        // 验证结果
        Assertions.assertEquals(3, schoolYearsData.size()); // 验证学年数量为 3
        Assertions.assertEquals("2023-2024", schoolYearsData.get(0).getValue()); // 验证第一个学年值为 2023-2024
        Assertions.assertEquals("last", schoolYearsData.get(0).getSchoolYear()); // 验证 2023-2024 学年为上一个学年
        Assertions.assertEquals("2024-2025", schoolYearsData.get(1).getValue()); // 验证第二个学年值为 2024-2025
        Assertions.assertEquals("current", schoolYearsData.get(1).getSchoolYear()); // 验证 2024-2025 学年为当前学年
        Assertions.assertEquals("2025-2026", schoolYearsData.get(2).getValue()); // 验证第三个学年值为 2025-2026
        Assertions.assertEquals("next", schoolYearsData.get(2).getSchoolYear()); // 验证 2025-2026 学年为下一个学年

        // 准备数据
        date = TimeUtil.parse("2024-09-15", TimeUtil.format10);
        // 调用方法
        schoolYearsData = periodServiceImpl.getSchoolYearsData(date);
        // 验证结果
        Assertions.assertEquals(3, schoolYearsData.size()); // 验证学年数量为 3
        Assertions.assertEquals("2023-2024", schoolYearsData.get(0).getValue()); // 验证第一个学年值为 2023-2024
        Assertions.assertEquals("last", schoolYearsData.get(0).getSchoolYear()); // 验证 2023-2024 学年为上一个学年
        Assertions.assertEquals("2024-2025", schoolYearsData.get(1).getValue()); // 验证第二个学年值为 2024-2025
        Assertions.assertEquals("current", schoolYearsData.get(1).getSchoolYear()); // 验证 2024-2025 学年为当前学年
        Assertions.assertEquals("2025-2026", schoolYearsData.get(2).getValue()); // 验证第三个学年值为 2025-2026
        Assertions.assertEquals("next", schoolYearsData.get(2).getSchoolYear()); // 验证 2025-2026 学年为下一个学年

        // 准备数据
        date = TimeUtil.parse("2024-09-16", TimeUtil.format10);
        // 调用方法
        schoolYearsData = periodServiceImpl.getSchoolYearsData(date);
        // 验证结果
        Assertions.assertEquals(2, schoolYearsData.size()); // 验证学年数量为 2
        Assertions.assertEquals("2024-2025", schoolYearsData.get(0).getValue()); // 验证第一个学年值为 2024-2025
        Assertions.assertEquals("current", schoolYearsData.get(0).getSchoolYear()); // 验证 2024-2025 学年为当前学年
        Assertions.assertEquals("2025-2026", schoolYearsData.get(1).getValue()); // 验证第二个学年值为 2025-2026
        Assertions.assertEquals("next", schoolYearsData.get(1).getSchoolYear()); // 验证 2025-2026 学年为下一个学年

        // 准备数据
        date = TimeUtil.parse("2024-12-31", TimeUtil.format10);
        // 调用方法
        schoolYearsData = periodServiceImpl.getSchoolYearsData(date);
        // 验证结果
        Assertions.assertEquals(2, schoolYearsData.size()); // 验证学年数量为 2
        Assertions.assertEquals("2024-2025", schoolYearsData.get(0).getValue()); // 验证第一个学年值为 2024-2025
        Assertions.assertEquals("current", schoolYearsData.get(0).getSchoolYear()); // 验证 2024-2025 学年为当前学年
        Assertions.assertEquals("2025-2026", schoolYearsData.get(1).getValue()); // 验证第二个学年值为 2025-2026
        Assertions.assertEquals("next", schoolYearsData.get(1).getSchoolYear()); // 验证 2025-2026 学年为下一个学年
        
    }
}

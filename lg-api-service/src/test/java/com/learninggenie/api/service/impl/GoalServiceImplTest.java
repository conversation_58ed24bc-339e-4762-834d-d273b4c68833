package com.learninggenie.api.service.impl;

import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.model.goal.GoalStatus;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.common.cache.CacheService;
import com.learninggenie.common.data.dao.DomainDao;
import com.learninggenie.common.data.dao.GoalDao;
import com.learninggenie.common.data.dao.PortfolioDao;
import com.learninggenie.common.data.entity.AnalysisGoalEntity;
import com.learninggenie.common.data.entity.AnalysisGroupEntity;
import com.learninggenie.common.data.entity.AnalysisObjectMeasureEntity;
import com.learninggenie.common.data.entity.AnalysisObjectiveEntity;
import com.learninggenie.common.data.model.AgencyModel;
import com.learninggenie.common.data.model.DomainEntity;
import com.learninggenie.common.data.model.ScoreTemplateEntity;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;


/**
 * Created by zjj on 2016/9/18.
 */
@RunWith(MockitoJUnitRunner.class)
public class GoalServiceImplTest {
    @InjectMocks
    private GoalServiceImpl goalService;
    @Mock
    private DomainDao domainDao;
    @Mock
    private PortfolioDao portfolioDao;
    @Mock
    private GoalDao goalDao;
    @Mock
    private UserProvider userProvider;
    @Mock
    private CacheService cacheService;

    /**
     * 添加goal
     * case: 正常添加
     * zjj 2016.9.18
     */
    @Test
    public void testCreateGoal() {
        AgencyModel agency = new AgencyModel();
        agency.setId("a001");
        agency.setName("a001");
        String userId = "u001";
        AnalysisGoalEntity goalEntity = new AnalysisGoalEntity();
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        goalService.createGoal(goalEntity,userId);
        verify(goalDao, times(1)).createGoal(Mockito.any(AnalysisGoalEntity.class));
    }

    /**
     * 添加goal
     * case: 未找到agency
     * zjj 2016.9.18
     */
    @Ignore
    @Test(expected = BusinessException.class)
    public void testCreateGoalWith_exception() {
        String userId = "u001";
        AnalysisGoalEntity goalEntity = new AnalysisGoalEntity();
        when(userProvider.getAgencyByUserId(userId)).thenReturn(null);
        goalService.createGoal(goalEntity,userId);
    }

    /**
     * 刪除goal
     * case: 正常刪除goal
     * zjj 2016.9.18
     */
    @Test
    public void testDeleteGoal(){
        String id = "g001";
        List<AnalysisGroupEntity> groupEntities = new ArrayList<>();
        AnalysisGroupEntity groupEntity = new AnalysisGroupEntity();
        groupEntity.setId("g001");
        groupEntities.add(groupEntity);
        when(goalDao.getGroupByGoalId(anyString())).thenReturn(groupEntities);
        List<AnalysisObjectiveEntity> objectiveEntities = new ArrayList<>();
        AnalysisObjectiveEntity objectiveEntity = new AnalysisObjectiveEntity();
        objectiveEntity.setId("o001");
        objectiveEntities.add(objectiveEntity);
        when(goalDao.getObjectiveByGroupId(anyString())).thenReturn(objectiveEntities);
        goalService.deleteGoal(id);
        verify(goalDao, times(1)).deleteGoal(anyString());
        verify(goalDao, times(1)).deleteGroups(anyList());
        verify(goalDao, times(1)).deleteObjectives(anyList());
    }

    /**
     * 更新goal
     * case: 正常更新
     * zjj 2016.9.18
     */
    @Ignore
    @Test
    public void testUpdateGoal(){
        //传入的数据
        AnalysisGoalEntity goalEntity = new AnalysisGoalEntity();
        goalEntity.setDomainId("d001");
        //修改前的数据
        AnalysisGoalEntity goalEntity1 = new AnalysisGoalEntity();
        goalEntity1.setDomainId("d002");
        when(goalDao.getGoal(anyString())).thenReturn(goalEntity1);

        List<AnalysisObjectiveEntity> objectiveEntities = new ArrayList<>();
        AnalysisObjectiveEntity objectiveEntity = new AnalysisObjectiveEntity();
        objectiveEntity.setId("o001");
        objectiveEntities.add(objectiveEntity);
        when(goalDao.getObjectiveByGoalId(anyString())).thenReturn(objectiveEntities);

        goalService.updateGoal(goalEntity);
        verify(goalDao, times(1)).updateGoal(goalEntity);
        verify(goalDao, times(1)).deleteObjectivesMeasure(anyList());
    }

    /**
     * 根據用戶的id 獲取goal
     * case: 正常獲取
     * zjj 2016.9.18
     */
    @Test
    @Ignore
    public void testGetGoalByUserId(){
        AgencyModel agency = new AgencyModel();
        agency.setId("a001");
        agency.setName("a001");
        String userId = "u001";
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        List<AnalysisGoalEntity> goalEntityList = new ArrayList<>();
        AnalysisGoalEntity goalEntity1 = new AnalysisGoalEntity();
        goalEntity1.setId("g001");
        goalEntityList.add(goalEntity1);
        AnalysisGoalEntity goalEntity2 = new AnalysisGoalEntity();
        goalEntity2.setId("g002");
        goalEntityList.add(goalEntity2);
        when(goalDao.getGoalByAgencyId(agency.getId())).thenReturn(goalEntityList);
        List<AnalysisGoalEntity> goals = goalService.getGoalByUserId(userId,null, null);
        Assert.assertEquals(2,goals.size());
        Assert.assertEquals("g001",goals.get(0).getId());
        verify(goalDao, times(1)).getGoalByAgencyId(agency.getId());
    }

    /**
     * 根據用戶的id 獲取goal
     * 找不到agency 报错
     */
    @Ignore
    @Test (expected = BusinessException.class)
    public void testGetGoalByUserIdWithException(){
        String userId = "u001";
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(null);
        goalService.getGoalByUserId(userId,null, null);
    }

    /**
     * 根据goalId 获取goal
     * case: 正常获取
     * zjj 2016.9.18
     */
    @Ignore
    @Test
    public void testGetGoalById() {
        String id = "g001";
        AnalysisGoalEntity goalEntity = new AnalysisGoalEntity();
        goalEntity.setId(id);
        when(goalDao.getGoal(id)).thenReturn(goalEntity);
        List<AnalysisGroupEntity> groups = new ArrayList<>();
        AnalysisGroupEntity groupEntity = new AnalysisGroupEntity();
        groupEntity.setId("group001");
        groups.add(groupEntity);
        when(goalDao.getGroupByGoalId(anyString())).thenReturn(groups);
        List<AnalysisObjectiveEntity> objectives = new ArrayList<>();
        AnalysisObjectiveEntity objectiveEntity = new AnalysisObjectiveEntity();
        objectiveEntity.setId("objective001");
        objectives.add(objectiveEntity);
        when(goalDao.getObjectiveByGroupId(anyString())).thenReturn(objectives);
        List<AnalysisObjectMeasureEntity> objectMeasureEntities = new ArrayList<>();
        AnalysisObjectMeasureEntity objectMeasureEntity = new AnalysisObjectMeasureEntity();
        objectMeasureEntity.setObjectiveId("objective001");
        objectMeasureEntity.setMeasureId("measureId");
        objectMeasureEntities.add(objectMeasureEntity);
        when(goalDao.getObjectiveMeasure(anyString())).thenReturn(objectMeasureEntities);
        ScoreTemplateEntity scoreTemplateEntity = new ScoreTemplateEntity();
        scoreTemplateEntity.setDomainLevelsJson("[{\"domainId\":\"measureId\",\"measure\":\"LLD8\",\"measureName\":\"Phonological Awareness\",\"levels\":[{\"id\":\"77F413F8-1EC3-4B70-B6E6-2F153CD936EE\",\"name\":\" Exploring Middle\",\"type\":\"radio\",\"sortIndex\":\"3\",\"value\":\"4\",\"tip\":\"Attends to sounds or elements of language\"},{\"id\":\"976C1402-59B1-4B21-8F16-EAB81A329989\",\"name\":\" Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Demonstrates awareness of variations in sounds\"},{\"id\":\"CFC998E8-3D19-44DB-BE98-664395648330\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Engages actively in play with sounds in words or rhymes, or sings simple songs, or\\nrepeats simple nursery rhymes\"},{\"id\":\"405BA949-F8FF-4CB0-A384-766174446ADF\",\"name\":\"Building Middle\",\"type\":\"radio\",\"sortIndex\":\"6\",\"value\":\"7\",\"tip\":\"Demonstrates awareness of larger units of language (e.g., words, syllables)\"},{\"id\":\"790D37D5-FF13-4DCD-84A3-5847C81323B2\",\"name\":\"Building Later\",\"type\":\"radio\",\"sortIndex\":\"7\",\"value\":\"8\",\"tip\":\"Blends larger units of language (e.g., compound words and syllables) with or without the support of pictures or objects;and segments larger units of language (e.g.\\ncompound words and syllables) with or without the support of pictures or objects\"},{\"id\":\"B16025FA-6DEF-47B4-808E-FAD1C0748308\",\"name\":\"UR (Unable to Rate)\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"},{\"id\":\"63BBC32F-686E-4859-81C5-463C366CD4F7\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"12\",\"value\":\"e\"},{\"id\":\"E72D58A8-167D-4068-8D25-216A174AAEE0\",\"name\":\"Integrating Earlier\",\"type\":\"radio\",\"sortIndex\":\"8\",\"value\":\"9\",\"tip\":\"Segments larger units of language (e.g., compound words and syllables) with or without the support of pictures or objectsBlends smaller units of language (e.g., onsetsand rimes), with or without the support of pictures or objects; and Segments smaller units of language (e.g., onsets and rimes), with or without the support of pictures or objects\"},{\"id\":\"F2A37092-00A9-4592-A182-625B59DEA07B\",\"name\":\"Not yet\",\"type\":\"radio\",\"sortIndex\":\"21\",\"value\":\"n\"}]}]");
        when(portfolioDao.loadScoreTemplate(anyString())).thenReturn(scoreTemplateEntity);
        AnalysisGoalEntity goal = goalService.getGoal(id);
        Assert.assertEquals(id, goal.getId());
        verify(goalDao, times(1)).getGoal(id);
    }

    @Test
    public void testCreateGroup(){
        AnalysisGroupEntity groupEntity = new AnalysisGroupEntity();
        goalService.createGroup(groupEntity);
        verify(goalDao, times(1)).createGroup(groupEntity);
    }
    @Test
    public void testDeleteGroup(){
        String id = "groupId";
        goalService.deleteGroup(id);
        verify(goalDao, times(1)).deleteGroup(id);
    }
    @Test
    public void testUpdateGroup(){
        AnalysisGroupEntity groupEntity = new AnalysisGroupEntity();
        goalService.updateGroup(groupEntity);
        verify(goalDao, times(1)).updateGroup(groupEntity);
    }
    @Test
    public void testGetGroupByGoalId (){
        String goalId = "goalId";
        List<AnalysisGroupEntity> groupEntities = new ArrayList<>();
        AnalysisGroupEntity groupEntity1 = new AnalysisGroupEntity();
        groupEntity1.setId("g001");
        AnalysisGroupEntity groupEntity2 = new AnalysisGroupEntity();
        groupEntity2.setId("g002");
        groupEntities.add(groupEntity1);
        groupEntities.add(groupEntity2);
        when(goalDao.getGroupByGoalId(goalId)).thenReturn(groupEntities);
        List<AnalysisGroupEntity> groups = goalService.getGroupByGoalId(goalId);
        Assert.assertEquals(2,groups.size());
        verify(goalDao, times(1)).getGroupByGoalId(goalId);
    }
    @Test
    public void testGetGroupById (){
        String id = "g001";
        AnalysisGroupEntity groupEntity = new AnalysisGroupEntity();
        groupEntity.setId(id);
        when(goalDao.getGroup(id)).thenReturn(groupEntity);
        goalService.getGroup(id);
        verify(goalDao, times(1)).getGroup(id);
    }

    @Test
    public void testCreateObjective(){
        AnalysisObjectiveEntity objectiveEntity = new AnalysisObjectiveEntity();
        List<AnalysisObjectMeasureEntity> objectMeasureEntities = new ArrayList<>();
        AnalysisObjectMeasureEntity objectMeasureEntity1 = new AnalysisObjectMeasureEntity();
        objectMeasureEntity1.setObjectiveId("o001");
        objectMeasureEntity1.setMeasureId("m001");
        objectMeasureEntity1.setLevelId("l001");
        objectMeasureEntities.add(objectMeasureEntity1);
        objectiveEntity.setObjectMeasureList(objectMeasureEntities);
        goalService.createObjective(objectiveEntity);
        verify(goalDao, times(1)).createObjective(objectiveEntity);
    }

    @Test
    public void testDeleteObjective(){
        String id = "o001";
        goalService.deleteObjective(id);
        verify(goalDao, times(1)).deleteObjective(id);
    }

    @Test
    public void testUpdateObjective(){
        AnalysisObjectiveEntity objectiveEntity = new AnalysisObjectiveEntity();
        objectiveEntity.setId("o001");
        List<AnalysisObjectMeasureEntity> objectMeasureEntities = new ArrayList<>();
        AnalysisObjectMeasureEntity objectMeasureEntity1 = new AnalysisObjectMeasureEntity();
        objectMeasureEntity1.setObjectiveId("o001");
        objectMeasureEntity1.setMeasureId("m001");
        objectMeasureEntity1.setLevelId("l001");
        objectMeasureEntities.add(objectMeasureEntity1);
        objectiveEntity.setObjectMeasureList(objectMeasureEntities);
        AnalysisGoalEntity goalEntity = new AnalysisGoalEntity();
        goalEntity.setDomainId("m001");
        when(goalDao.getGoalByObjective(anyString())).thenReturn(goalEntity);
        List<DomainEntity> domainEntities = new ArrayList<>();
        DomainEntity domainEntity1 = new DomainEntity();
        domainEntity1.setId("m001");
        DomainEntity domainEntity2 = new DomainEntity();
        domainEntity2.setId("d002");
        domainEntities.add(domainEntity1);
        domainEntities.add(domainEntity2);
        when(domainDao.getAllChildDomains(anyString())).thenReturn(domainEntities);
        goalService.updateObjective(objectiveEntity);
        verify(goalDao, times(1)).updateObjective(objectiveEntity);
        verify(goalDao, times(1)).deleteObjectiveMeasure("o001");
        verify(goalDao, times(1)).createObjectiveMeasure(objectMeasureEntities,"o001");
    }

    @Test
    public void getObjectiveByGroupId (){
        String groupId = "g001";
        List<AnalysisObjectiveEntity> analysisObjectiveEntities = new ArrayList<>();
        AnalysisObjectiveEntity analysisObjectiveEntity1 = new AnalysisObjectiveEntity();
        analysisObjectiveEntity1.setId("o001");
        analysisObjectiveEntities.add(analysisObjectiveEntity1);
        when(goalDao.getObjectiveByGroupId(groupId)).thenReturn(analysisObjectiveEntities);
        List<AnalysisObjectMeasureEntity> objectMeasureEntities = new ArrayList<>();
        AnalysisObjectMeasureEntity objectMeasureEntity1 = new AnalysisObjectMeasureEntity();
        objectMeasureEntity1.setObjectiveId("o001");
        objectMeasureEntity1.setMeasureId("m001");
        objectMeasureEntity1.setLevelId("l001");
        objectMeasureEntities.add(objectMeasureEntity1);
        when(goalDao.getObjectiveMeasure("o001")).thenReturn(objectMeasureEntities);
        List<AnalysisObjectiveEntity> objectiveEntityList = goalService.getObjectiveByGroupId(groupId);
        Assert.assertEquals(1,objectiveEntityList.size());
        Assert.assertEquals(1,objectiveEntityList.get(0).getObjectMeasureList().size());
        verify(goalDao, times(1)).getObjectiveMeasure("o001");
    }
    @Test
    public void getObjectiveById (){
        String id = "g001";
        AnalysisObjectiveEntity objectiveEntity = new AnalysisObjectiveEntity();
        objectiveEntity.setId("o001");
        when(goalDao.getObjective(id)).thenReturn(objectiveEntity);
        List<AnalysisObjectMeasureEntity> objectMeasureEntities = new ArrayList<>();
        AnalysisObjectMeasureEntity objectMeasureEntity1 = new AnalysisObjectMeasureEntity();
        objectMeasureEntity1.setObjectiveId("o001");
        objectMeasureEntity1.setMeasureId("m001");
        objectMeasureEntity1.setLevelId("l001");
        objectMeasureEntities.add(objectMeasureEntity1);
        when(goalDao.getObjectiveMeasure("o001")).thenReturn(objectMeasureEntities);
        AnalysisObjectiveEntity objective= goalService.getObjective(id);
        Assert.assertEquals(1,objective.getObjectMeasureList().size());
        verify(goalDao, times(1)).getObjectiveMeasure("o001");
    }

    @Test
    @Ignore
    public void testStatus(){
        String userId = "u001";
        AgencyModel agency = new AgencyModel();
        agency.setId("a001");
        agency.setName("a001");
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        List<AnalysisGoalEntity> goalEntityList = new ArrayList<>();
        AnalysisGoalEntity goalEntity1 = new AnalysisGoalEntity();
        goalEntity1.setId("g001");
        goalEntityList.add(goalEntity1);
        when(goalDao.getGoalByAgencyId(agency.getId())).thenReturn(goalEntityList);

        List<AnalysisGroupEntity> groupEntities = new ArrayList<>();
        AnalysisGroupEntity groupEntity1 = new AnalysisGroupEntity();
        groupEntity1.setId("g01");
        groupEntities.add(groupEntity1);
        when(goalDao.getGroupByGoalId("g001")).thenReturn(groupEntities);

        List<AnalysisObjectiveEntity> analysisObjectiveEntities = new ArrayList<>();
        AnalysisObjectiveEntity analysisObjectiveEntity1 = new AnalysisObjectiveEntity();
        analysisObjectiveEntity1.setId("o001");
        analysisObjectiveEntities.add(analysisObjectiveEntity1);
        when(goalDao.getObjectiveByGroupId("g01")).thenReturn(analysisObjectiveEntities);
        GoalStatus goalStatus = goalService.status(userId,null, null);
        Assert.assertEquals(3,goalStatus.getStatus());
    }

}
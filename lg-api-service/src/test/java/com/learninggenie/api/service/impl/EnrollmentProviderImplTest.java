package com.learninggenie.api.service.impl;

import com.learninggenie.api.provider.impl.EnrollmentProviderImpl;
import com.learninggenie.common.data.dao.StudentDao;
import com.learninggenie.common.data.entity.EnrollmentMetaDataEntity;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class EnrollmentProviderImplTest {

    @Mock
    private StudentDao studentDao;

    @InjectMocks
    private EnrollmentProviderImpl enrollmentProviderImpl;

    private static final String ELD = "ELD";

    private static final String IEP = "IEP/IFSP";

    /**
     * 测试 getIEPMapByEnrollmentIds
     */
    @Test
    public void testGetIEPMapByEnrollmentIds() {

        // 数据准备
        List<EnrollmentMetaDataEntity> iepMetas = new ArrayList<>();
        List<String> enrollmentIds = new ArrayList<>();
        enrollmentIds.add("E00001");
        // 接口模拟
        when(studentDao.getMetaByEnrollmentIds(enrollmentIds, IEP)).thenReturn(iepMetas);
        enrollmentProviderImpl.getIEPMapByEnrollmentIds(enrollmentIds);

        // 验证
        verify(studentDao, times(1)).getMetaByEnrollmentIds(enrollmentIds, IEP);
    }


    /**
     * 测试 getELDMapByEnrollmentIds
     */
    @Test
    public void testGetIEPAndELDAttrsByEnrollmentIds() {

        // 数据准备
        List<EnrollmentMetaDataEntity> iepMetas = new ArrayList<>();
        List<String> enrollmentIds = new ArrayList<>();
        enrollmentIds.add("E00002");

        // 接口模拟
        when(studentDao.getMetaByEnrollmentIds(enrollmentIds, ELD)).thenReturn(iepMetas);
        enrollmentProviderImpl.getIEPAndELDAttrsByEnrollmentIds(enrollmentIds);

        // 验证
        verify(studentDao, times(1)).getMetaByEnrollmentIds(enrollmentIds, ELD);
    }

}

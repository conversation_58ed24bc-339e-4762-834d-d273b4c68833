package com.learninggenie.api.service.impl;

import com.google.api.client.util.Lists;
import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.model.AgencyTipsResponse;
import com.learninggenie.api.model.Status;
import com.learninggenie.api.model.agency.AgencyDrdpSetting;
import com.learninggenie.api.model.agency.DLSwitchRequest;
import com.learninggenie.api.model.agency.ManualAddingRosterOpen;
import com.learninggenie.api.model.agency.SetManualAddingRosterRequest;
import com.learninggenie.api.model.note.OpenModel;
import com.learninggenie.api.model.note.SiteAdminOpenRequest;
import com.learninggenie.api.model.user.CreateAgencyIdentifierRequest;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.security.AuthUserDetails;
import com.learninggenie.api.service.UserService;
import com.learninggenie.common.data.dao.AgencyDao;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.dao.users.MetaDataDao;
import com.learninggenie.common.data.entity.AgencyIdentifierEntity;
import com.learninggenie.common.data.entity.AgencyMetaDataEntity;
import com.learninggenie.common.data.entity.GroupEntity;
import com.learninggenie.common.data.entity.UserEntity;
import com.learninggenie.common.data.entity.users.MetaDataEntity;
import com.learninggenie.common.data.enums.AgencyMetaKey;
import com.learninggenie.common.data.enums.DrdpSettingKey;
import com.learninggenie.common.data.enums.DrdpSettingValue;
import com.learninggenie.common.data.enums.UserMetaKey;
import com.learninggenie.common.data.model.AgencyModel;
import com.learninggenie.common.data.model.drdp.setting.DRDPSetting;
import com.learninggenie.common.data.repository.UserRepository;
import com.learninggenie.common.score.RatingService;
import com.learninggenie.common.utils.MSG;
import com.learninggenie.common.utils.RateUtil;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static com.learninggenie.common.data.enums.UserMetaKey.DISABLE_DRDP_TIP;
import static com.learninggenie.common.data.enums.UserRole.TEACHING_ASSISTANT;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Created by Shaow on 2017/2/3.
 */
@RunWith(MockitoJUnitRunner.class)
public class AgencyServiceImplTest {
    @Mock
    private UserRepository userRepository;
    @Mock
    private AgencyDao agencyDao;
    @Mock
    private UserProvider userProvider;
    @Mock
    private MetaDataDao userMetaDao;
    @Mock
    private UserService userService;
    @Mock
    private UserDaoImpl userDao;
    @Mock
    private RatingService ratingService;

    @InjectMocks
    private AgencyServiceImpl agencyService;


    @Test(expected = BusinessException.class)
    public void testCreateIdentifierWithIdentifierIsNUll() {
        CreateAgencyIdentifierRequest request = new CreateAgencyIdentifierRequest();
        request.setIdentifierName(null);
        agencyService.createIdentifier(request);
    }

    @Test(expected = BusinessException.class)
    public void testCreateIdentifierWithEmailExist() {
        CreateAgencyIdentifierRequest request = new CreateAgencyIdentifierRequest();
        request.setIdentifierName("<EMAIL>");
        AgencyIdentifierEntity identifierEntity = new AgencyIdentifierEntity();
        when(agencyDao.getIdentifierByName(Mockito.anyString())).thenReturn(identifierEntity);
        agencyService.createIdentifier(request);
    }

    @Test
    public void deleteIdentifier() throws Exception {
        String identifierId = "i001";
        agencyService.deleteIdentifier(identifierId);
    }

    @Test
    public void getIdentifier() throws Exception {
        String type = "special_education";
        String search = "";
        String order = "";
        agencyService.getIdentifier(type, search, order);
    }

    @Test
    public void createIdentifierUser() throws Exception {
        agencyService.createIdentifierUser("i001", "u002");
    }


    @Test
    public void testGetIdentifier() throws Exception {
        String email = "<EMAIL>";
        agencyService.getAgency(email);
    }

    @Test
    public void deleteAgencyCross() throws Exception {
        String identifierId = "i001";
        String userId = "u001";
        agencyService.deleteAgencyCross(identifierId, userId);
    }

    @Test
    public void getIdentifierByAgency() throws Exception {
        String userId = "u001";
        String agencyId = "a001";
        agencyService.getIdentifierByAgency(userId, agencyId);
    }

    /**
     * 正常两个开关修改
     */
    @Test
    public void testSetUserManualAddingRoster() {
        String userId1 = "user001";
        String userId2 = "user002";
        boolean openFlag1 = true;
        boolean openFlag2 = false;
        ManualAddingRosterOpen addingRosterOpen1 = new ManualAddingRosterOpen();
        addingRosterOpen1.setUserId(userId1);
        addingRosterOpen1.setOpenFlag(openFlag1);

        ManualAddingRosterOpen addingRosterOpen2 = new ManualAddingRosterOpen();
        addingRosterOpen2.setUserId(userId2);
        addingRosterOpen2.setOpenFlag(openFlag2);

        List<ManualAddingRosterOpen> manualAddingRosterOpens = new ArrayList<>();
        manualAddingRosterOpens.add(addingRosterOpen1);
        manualAddingRosterOpens.add(addingRosterOpen2);

        SetManualAddingRosterRequest request = new SetManualAddingRosterRequest();
        request.setManualAddingRosterOpen(manualAddingRosterOpens);

        UserEntity user001 = new UserEntity();
        user001.setId("user001");
        user001.setFirstName("user001");
        user001.setRole("COLLABORATOR");

        UserEntity user002 = new UserEntity();
        user002.setId("user002");
        user002.setFirstName("user001");
        user002.setRole("COLLABORATOR");

        when(userRepository.findById(userId1)).thenReturn(Optional.of(user001));
        when(userRepository.findById(userId2)).thenReturn(Optional.of(user002));

        agencyService.setUserManualAddingRoster(request);
        verify(userRepository, times(2)).findById(Mockito.anyString());
        verify(userService, times(1)).setMeta(Mockito.anyString(), Mockito.eq("true"), Mockito.eq(userId1));
        verify(userService, times(1)).setMeta(Mockito.anyString(), Mockito.eq("false"), Mockito.eq(userId2));
    }

    /**
     * 两个开关修改，有一个用户是agency owner 一个是teacher,用户是agency owner或者
     * agency admin总是打开
     */
    @Test
    public void testSetUserManualAddingRoster_twoDiffRole() {
        String userId1 = "user001";
        String userId2 = "user002";
        boolean openFlag2 = false;
        ManualAddingRosterOpen addingRosterOpen1 = new ManualAddingRosterOpen();
        addingRosterOpen1.setUserId(userId1);
        addingRosterOpen1.setOpenFlag(openFlag2);

        ManualAddingRosterOpen addingRosterOpen2 = new ManualAddingRosterOpen();
        addingRosterOpen2.setUserId(userId2);
        addingRosterOpen2.setOpenFlag(openFlag2);

        List<ManualAddingRosterOpen> manualAddingRosterOpens = new ArrayList<>();
        manualAddingRosterOpens.add(addingRosterOpen1);
        manualAddingRosterOpens.add(addingRosterOpen2);

        SetManualAddingRosterRequest request = new SetManualAddingRosterRequest();
        request.setManualAddingRosterOpen(manualAddingRosterOpens);

        UserEntity user001 = new UserEntity();
        user001.setId("user001");
        user001.setFirstName("user001");
        user001.setRole("AGENCY_OWNER");

        UserEntity user002 = new UserEntity();
        user002.setId("user002");
        user002.setFirstName("user001");
        user002.setRole("COLLABORATOR");

        when(userRepository.findById(userId1)).thenReturn(Optional.of(user001));
        when(userRepository.findById(userId2)).thenReturn(Optional.of(user002));

        agencyService.setUserManualAddingRoster(request);
        verify(userRepository, times(2)).findById(Mockito.anyString());
        verify(userService, times(1)).setMeta(Mockito.anyString(), Mockito.eq("true"), Mockito.eq(userId1));
        verify(userService, times(1)).setMeta(Mockito.anyString(), Mockito.eq("false"), Mockito.eq(userId2));
    }

    /**
     * 两个开关修改，有一个用户是agency admin 一个是teacher,用户是agency owner或者
     * agency admin总是打开
     */
    @Test
    public void testSetUserManualAddingRoster_twoDiffRole_agencyAdmin() {
        String userId1 = "user001";
        String userId2 = "user002";
        boolean openFlag2 = false;
        ManualAddingRosterOpen addingRosterOpen1 = new ManualAddingRosterOpen();
        addingRosterOpen1.setUserId(userId1);
        addingRosterOpen1.setOpenFlag(openFlag2);

        ManualAddingRosterOpen addingRosterOpen2 = new ManualAddingRosterOpen();
        addingRosterOpen2.setUserId(userId2);
        addingRosterOpen2.setOpenFlag(openFlag2);

        List<ManualAddingRosterOpen> manualAddingRosterOpens = new ArrayList<>();
        manualAddingRosterOpens.add(addingRosterOpen1);
        manualAddingRosterOpens.add(addingRosterOpen2);

        SetManualAddingRosterRequest request = new SetManualAddingRosterRequest();
        request.setManualAddingRosterOpen(manualAddingRosterOpens);

        UserEntity user001 = new UserEntity();
        user001.setId("user001");
        user001.setFirstName("user001");
        user001.setRole("AGENCY_OWNER");

        UserEntity user002 = new UserEntity();
        user002.setId("user002");
        user002.setFirstName("user001");
        user002.setRole("COLLABORATOR");

        when(userRepository.findById(userId1)).thenReturn(Optional.of(user001));
        when(userRepository.findById(userId2)).thenReturn(Optional.of(user002));

        agencyService.setUserManualAddingRoster(request);
        verify(userRepository, times(2)).findById(Mockito.anyString());
        verify(userService, times(1)).setMeta(Mockito.anyString(), Mockito.eq("true"), Mockito.eq(userId1));
        verify(userService, times(1)).setMeta(Mockito.anyString(), Mockito.eq("false"), Mockito.eq(userId2));
    }

    @Test
    public void getAgencyTips() {
        // 未查看过返回所有弹窗
        AgencyModel agency = new AgencyModel();
        agency.setId("001");
        when(userProvider.getCurrentUserId()).thenReturn("001");
        when(userProvider.getAgencyByUserId("001")).thenReturn(agency);
        when(agencyDao.getMetas(Mockito.anyString(), Mockito.any())).thenReturn(new ArrayList<>());
        when(userMetaDao.getByUserIdsAndMetaKeysIn(Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());
        // 模拟当前用户
        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setUsername("001");
        currentUser.setAgencyId("001");
        final String userId = currentUser.getId();
        // 调用时返回当前模拟的用户对象
        when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // 模拟当前用户的元数据
        MetaDataEntity meta = new MetaDataEntity();
        meta.setMetaKey(DISABLE_DRDP_TIP.toString());
        // 当前用户已经被提醒过
        meta.setMetaValue("true");
        // 调用时返回用户的元数据
        when(userMetaDao.getByUserIdAndMetaKey(userId, DISABLE_DRDP_TIP.toString())).thenReturn(meta);

        AgencyTipsResponse agencyTips = agencyService.getAgencyTips();
        Assert.assertEquals(agencyTips.getTipsList().size(), agencyService.tipsList.size() + agencyService.userTipsList.size());
        Mockito.reset(userProvider, agencyDao, userMetaDao);

        // 已查看其中一个弹窗返回剩余弹窗
        AgencyMetaDataEntity metaData = new AgencyMetaDataEntity();
        metaData.setMetaKey(AgencyMetaKey.TIPS_DHC_OR_CHECK.toString());
        metaData.setMetaValue("END");
        metaData.setAgencyId("001");
        when(userProvider.getCurrentUserId()).thenReturn("001");
        when(userProvider.getAgencyByUserId("001")).thenReturn(agency);
        when(agencyDao.getMetas(Mockito.anyString(), Mockito.any())).thenReturn(Arrays.asList(metaData));
        when(userMetaDao.getByUserIdsAndMetaKeysIn(Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());

        // 调用时返回当前模拟的用户对象
//        when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // 调用时返回用户的元数据
//        when(userMetaDao.getByUserIdAndMetaKey(userId, DISABLE_DRDP_TIP.toString())).thenReturn(meta);

        AgencyTipsResponse agencyTips2 = agencyService.getAgencyTips();
        Assert.assertEquals(agencyTips2.getTipsList().size(), agencyService.tipsList.size() + agencyService.userTipsList.size() - 1);

    }

    @Test
    public void getDisabledDrdpTip() {
        // 模拟已经给用户提醒过关闭功能弹窗的情况
        // 创建测试所需的响应对象
        final AgencyTipsResponse response = new AgencyTipsResponse();
        // 模拟当前用户
        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setUsername("001");
        currentUser.setAgencyId("001");
        final String userId = currentUser.getId();
        // 调用时返回当前模拟的用户对象
        when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // 模拟当前用户的元数据
        MetaDataEntity meta = new MetaDataEntity();
        meta.setMetaKey(DISABLE_DRDP_TIP.toString());
        // 当前用户已经被提醒过
        meta.setMetaValue("true");
        // 调用时返回用户的元数据
        when(userMetaDao.getByUserIdAndMetaKey(userId, DISABLE_DRDP_TIP.toString())).thenReturn(meta);
        // 调用方法
        AgencyTipsResponse result = agencyService.getDisabledDrdpTip(response);
        // 验证结果
        Assert.assertEquals(response, result);
        Mockito.reset(userProvider, userMetaDao, agencyDao);
        // 模拟未给用户提醒过关闭功能弹窗的情况
        // 1. 未给机构开启DRDP上传功能
        // 创建测试所需的响应对象
        final AgencyTipsResponse response1 = new AgencyTipsResponse();
        // 模拟当前用户
        AuthUserDetails currentUser1 = new AuthUserDetails();
        currentUser1.setAgencyId("001");
        currentUser1.setUsername("001");
        // 调用时返回当前模拟的用户对象
        when(userProvider.getCurrentUser()).thenReturn(currentUser1);
        final String userId1 = currentUser.getId();
        // 模拟未给用户提醒过关闭功能弹窗的情况
        // 未给用户提醒
        // 模拟当前用户的元数据
        MetaDataEntity meta1 = new MetaDataEntity();
        meta1.setMetaKey(DISABLE_DRDP_TIP.toString());
        // 模拟当前用户未被提醒过
        meta1.setMetaValue("false");
        // 返回当前用户的元数据
        when(userMetaDao.getByUserIdAndMetaKey(userId1, DISABLE_DRDP_TIP.toString())).thenReturn(meta1);
        String agencyId = currentUser1.getAgencyId();
        // 模拟未给机构开启DRDP
        boolean featureOpenFalse = false;
        when(userProvider.getAgencyOpenDefaultOpen(agencyId, AgencyMetaKey.DRDP_UPLOAD.toString())).thenReturn(featureOpenFalse);
        // 模拟机构元数据
        AgencyMetaDataEntity uploadMetaNull = null;
        // 返回机构元数据
        when(agencyDao.getMeta(agencyId, DrdpSettingKey.UPLOAD_DRDP_SETTING.toString())).thenReturn(uploadMetaNull);
        // 调用方法
        AgencyTipsResponse result1 = agencyService.getDisabledDrdpTip(response1);
        // 验证结果
        Assert.assertEquals(response1, result1);
        Mockito.reset(userMetaDao, agencyDao);
        // 2.给机构开启DRDP上传功能,但是机构未开启DRDP上传功能
        // 模拟给机构开启了上传
        boolean featureOpenTrue = true;
        // 返回开启机构上传
        when(userProvider.getAgencyOpenDefaultOpen(agencyId, AgencyMetaKey.DRDP_UPLOAD.toString())).thenReturn(featureOpenTrue);
        AgencyMetaDataEntity uploadMetaIsFalse = new AgencyMetaDataEntity();
        uploadMetaIsFalse.setMetaKey(DrdpSettingKey.UPLOAD_DRDP_SETTING.toString());
        uploadMetaIsFalse.setMetaValue("false");
        when(agencyDao.getMeta(agencyId, DrdpSettingKey.UPLOAD_DRDP_SETTING.toString())).thenReturn(uploadMetaIsFalse);
        AgencyTipsResponse result2 = agencyService.getDisabledDrdpTip(response1);
        Assert.assertEquals(response1, result2);
        Mockito.reset(userMetaDao, agencyDao);

        // 3. 给机构开启DRDP上传功能,机构开启DRDP上传功能,用户角色不是 Teacher
        uploadMetaIsFalse.setMetaValue(DrdpSettingValue.DRDP_UPLOAD_OPEN.toString());
        when(agencyDao.getMeta(agencyId, DrdpSettingKey.UPLOAD_DRDP_SETTING.toString())).thenReturn(uploadMetaIsFalse);
        AgencyTipsResponse result3 = agencyService.getDisabledDrdpTip(response1);
        // 验证弹窗列表中是否有 DISABLE_DRDP_TIP
        Assert.assertEquals(result3.getTipsList().get(0), DISABLE_DRDP_TIP.toString());
        Mockito.reset(userProvider, userMetaDao, agencyDao);
        // 模拟未给老师开启权限
        AuthUserDetails user = new AuthUserDetails();
        user.setUsername("005");
        user.setAgencyId("007");
        // 用户ID
        final String userId2 = user.getId();
        // 机构ID
        final String agencyId2 = user.getAgencyId();
        // 是老师情况
        user.setRole(TEACHING_ASSISTANT.toString());
        when(userProvider.getCurrentUser()).thenReturn(user);
        MetaDataEntity metaDataEntity = new MetaDataEntity();
        metaDataEntity.setMetaValue("false");
        when(userMetaDao.getByUserIdAndMetaKey(userId2, DISABLE_DRDP_TIP.toString())).thenReturn(metaDataEntity);
        // 给机构开启上传
        boolean featureOpen1 = true;
        when(userProvider.getAgencyOpenDefaultOpen(agencyId2, AgencyMetaKey.DRDP_UPLOAD.toString())).thenReturn(featureOpen1);
        // 机构自身开启上传
        AgencyMetaDataEntity agencyMetaDataEntity = new AgencyMetaDataEntity();
        agencyMetaDataEntity.setMetaValue(DrdpSettingValue.DRDP_UPLOAD_OPEN.toString());
        when(agencyDao.getMeta(agencyId2, DrdpSettingKey.UPLOAD_DRDP_SETTING.toString())).thenReturn(agencyMetaDataEntity);
        // 机构未给老师开启上传情况
        AgencyMetaDataEntity permissionMeta = new AgencyMetaDataEntity();
        permissionMeta.setMetaValue("false");
        when(agencyDao.getMeta(agencyId2, DrdpSettingKey.LOCK_UPLOAD_SETTING.toString())).thenReturn(permissionMeta);
        AgencyTipsResponse tipsResponse = agencyService.getDisabledDrdpTip(response1);
        Assert.assertEquals(response1, tipsResponse);
        // 5. 给机构开启DRDP上传功能,机构开启DRDP上传功能,用户角色是 Teacher, 有开启上传
        // 模拟机构给老师开启了上传功能情况
        permissionMeta.setMetaValue(DrdpSettingValue.LOCK_BY_TEACHER.toString());
        AgencyTipsResponse response2 = agencyService.getDisabledDrdpTip(response1);
        Assert.assertEquals(response2.getTipsList().get(0), DISABLE_DRDP_TIP.toString());

    }
    @Test
    public void setAgencyTip() {
        // 标记已查看弹窗
        AgencyModel agency = new AgencyModel();
        agency.setId("001");
        when(userProvider.getCurrentUserId()).thenReturn("001");
        when(userProvider.getAgencyByUserId("001")).thenReturn(agency);

        agencyService.setAgencyTip(AgencyMetaKey.TIPS_DHC_OR_CHECK.toString(), "END");
        verify(agencyDao, times(1)).setMeta(Mockito.anyString(), Mockito.anyString(), Mockito.anyString());
        Mockito.reset(userProvider, agencyDao);

        when(userProvider.getCurrentUserId()).thenReturn("001");
        when(userProvider.getAgencyByUserId("001")).thenReturn(agency);

        agencyService.setAgencyTip(UserMetaKey.TIPS_SUPPLEMENT_TEACHER.toString(), "END");
        verify(userMetaDao, times(1)).saveMeta(Mockito.any());
        Mockito.reset(userProvider, agencyDao);

        // 标记错误弹窗
        when(userProvider.getCurrentUserId()).thenReturn("001");
        when(userProvider.getAgencyByUserId("001")).thenReturn(agency);
        agencyService.setAgencyTip("aa", "END");
        verify(agencyDao, times(0)).setMeta(Mockito.anyString(), Mockito.anyString(), Mockito.anyString());
    }

    @Test
    public void testGetDlRequireParentSignOpen() {
        String userId = "testUserId";
        String currentUserId = "testCurrentUserId";
        AgencyModel agency = new AgencyModel();
        agency.setId("testAgencyId");
        AgencyMetaDataEntity meta = new AgencyMetaDataEntity();
        meta.setMetaKey(AgencyMetaKey.REQUIRE_PARENT_SIGN.toString());
        meta.setMetaValue("true");

        when(userProvider.getCurrentUserId()).thenReturn(currentUserId);
        when(userProvider.getAgencyByUserId(currentUserId)).thenReturn(agency);
        when(agencyDao.getMeta(agency.getId(), AgencyMetaKey.REQUIRE_PARENT_SIGN.toString())).thenReturn(meta);

        OpenModel result = agencyService.getDlRequireParentSignOpen(userId);

        verify(userProvider, times(1)).getCurrentUserId();
        verify(userProvider, times(1)).getAgencyByUserId(currentUserId);
        verify(agencyDao, times(1)).getMeta(agency.getId(), AgencyMetaKey.REQUIRE_PARENT_SIGN.toString());

        assertTrue(result.isOpen());
    }

    @Test
    public void testGetDlRequireParentSignOpen2() {
        String userId = "testUserId";
        String currentUserId = "";
        AgencyModel agency = null;
        AgencyMetaDataEntity meta = new AgencyMetaDataEntity();
        meta.setMetaKey(AgencyMetaKey.REQUIRE_PARENT_SIGN.toString());
        meta.setMetaValue("true");

        when(userProvider.getCurrentUserId()).thenReturn(currentUserId);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);

        BusinessException businessException = Assertions.assertThrows(BusinessException.class, () -> {
            OpenModel result = agencyService.getDlRequireParentSignOpen(userId);
        });

        Assert.assertEquals(MSG.t("NO_PERMISSION"), businessException.getDetail());
    }

    @Test
    public void testOpenDLParentSign() {
        DLSwitchRequest request = new DLSwitchRequest();
        request.setOpen(true);
        request.setDlChildCheckSwitchOpen(true);
        request.setRemarkShow(true);
        request.setAbbreviationShow(true);
        request.setCheckTemperatureMeasuremrnt(true);
        request.setManualReview(true);
        request.setStudentIdShow(true);
        request.setDlRequireParentSign(true);
        String userId = "testUserId";
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("testAgencyId");
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        agencyService.openDLParentSign(request, userId);
        verify(userProvider, times(1)).getAgencyByUserId(userId);
        verify(agencyDao, times(1)).setMeta(eq("testAgencyId"), eq(AgencyMetaKey.HEALTH_CARD_DL_PARENT_SIGN.toString()), eq("true"));
        verify(agencyDao, times(1)).setMeta(eq("testAgencyId"), eq(AgencyMetaKey.HEALTH_CARD_DL_CHILD_SIGN_IN_OUT.toString()), eq("true"));
        verify(agencyDao, times(1)).setMeta(eq("testAgencyId"), eq(AgencyMetaKey.REMARK_SHOW.toString()), eq("true"));
        verify(agencyDao, times(1)).setMeta(eq("testAgencyId"), eq(AgencyMetaKey.ABBREVIATION_SHOW.toString()), eq("true"));
        verify(agencyDao, times(1)).setMeta(eq("testAgencyId"), eq(AgencyMetaKey.HEALTH_CARD_MANUAL_REVIEW.toString()), eq("true"));
        verify(agencyDao, times(1)).setMeta(eq("testAgencyId"), eq(AgencyMetaKey.HEALTH_CARD_CHECK_TEMPERATURE_MEASUREMENT.toString()), eq("true"));
        verify(agencyDao, times(1)).setMeta(eq("testAgencyId"), eq(AgencyMetaKey.STUDENT_ID_SHOW.toString()), eq("true"));
        verify(agencyDao, times(1)).setMeta(eq("testAgencyId"), eq(AgencyMetaKey.REQUIRE_PARENT_SIGN.toString()), eq("true"));
    }

    @Test
    public void testOpenDLParentSign_Throw() {
        DLSwitchRequest request = new DLSwitchRequest();
        request.setOpen(true);
        request.setDlChildCheckSwitchOpen(true);
        request.setRemarkShow(true);
        request.setAbbreviationShow(true);
        request.setCheckTemperatureMeasuremrnt(true);
        request.setManualReview(true);
        request.setStudentIdShow(true);
        request.setDlRequireParentSign(true);
        String userId = "testUserId";
        AgencyModel agencyModel = null;
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        BusinessException exception = Assertions.assertThrows(BusinessException.class, () -> {
            // test method call
            agencyService.openDLParentSign(request, userId);
        });
        Assert.assertEquals(MSG.t("NO_AGENCY"), exception.getDetail());
    }

    @Test
    public void testCheckAgencyShowDrdpV2Tip_returnFalse() {
        String agencyId = "1";
        when(userProvider.getCurrentAgencyId()).thenReturn(agencyId);

        AgencyMetaDataEntity agencyMetadata = new AgencyMetaDataEntity();
        agencyMetadata.setAgencyId(agencyId);
        agencyMetadata.setMetaKey(AgencyMetaKey.DRDP_ONLINE_API_V2_TIP.toString());
        agencyMetadata.setMetaValue("END");
        when(agencyDao.getMeta(agencyId, AgencyMetaKey.DRDP_ONLINE_API_V2_TIP.toString())).thenReturn(agencyMetadata);

        boolean result = agencyService.checkAgencyShowDrdpV2Tip();
        Assert.assertFalse(result);
    }

    @Test
    public void testCheckAgencyShowDrdpV2Tip_returnTrue() {
        String agencyId = "1";

        when(userProvider.getCurrentAgencyId()).thenReturn(agencyId);
        AgencyMetaDataEntity agencyMetadata = new AgencyMetaDataEntity();
        agencyMetadata.setAgencyId(agencyId);
        agencyMetadata.setMetaKey(AgencyMetaKey.DRDP_ONLINE_API_V2_TIP.toString());
        agencyMetadata.setMetaValue("");
        when(agencyDao.getMeta(agencyId, AgencyMetaKey.DRDP_ONLINE_API_V2_TIP.toString())).thenReturn(agencyMetadata);

        List<DRDPSetting> agencyDrdpSettings = Lists.newArrayList();
        agencyDrdpSettings.add(new DRDPSetting());
        when(agencyDao.getDRDPSettingByAgency(agencyId)).thenReturn(agencyDrdpSettings);

        boolean result = agencyService.checkAgencyShowDrdpV2Tip();
        Assert.assertTrue(result);
    }

    @Test
    public void testSetAgencyFramework() {
        List<GroupEntity> groups = Lists.newArrayList();
        GroupEntity itGroup = new GroupEntity();
        itGroup.setDomainId(RateUtil.FRAMEWORK_IT_ID);
        groups.add(itGroup);

        GroupEntity psGroup = new GroupEntity();
        psGroup.setDomainId(RateUtil.FRAMEWORK_PS_ID);
        groups.add(psGroup);

        GroupEntity kGroup = new GroupEntity();
        kGroup.setDomainId(RateUtil.FRAMEWORK_K_ID);
        groups.add(kGroup);

        GroupEntity saGroup = new GroupEntity();
        saGroup.setDomainId(RateUtil.FRAMEWORK_SA_ID);
        groups.add(saGroup);

        when(ratingService.isITFramework(RateUtil.FRAMEWORK_IT_ID)).thenReturn(true);
        when(ratingService.isPSFramework(RateUtil.FRAMEWORK_PS_ID)).thenReturn(true);
        when(ratingService.isKFramework(RateUtil.FRAMEWORK_K_ID)).thenReturn(true);
        when(ratingService.isSAFramework(RateUtil.FRAMEWORK_SA_ID)).thenReturn(true);

        AgencyDrdpSetting agencyDrdpSetting = new AgencyDrdpSetting();
        agencyService.setAgencyFramework(agencyDrdpSetting, groups);

        Assert.assertTrue(agencyDrdpSetting.getHasITFramework());
        Assert.assertTrue(agencyDrdpSetting.getHasPSFramework());
        Assert.assertTrue(agencyDrdpSetting.getHasKFramework());
        Assert.assertTrue(agencyDrdpSetting.getHasSAFramework());
    }

    /**
     * 测试 AgencyServiceImpl.setCompleteRatingOpen() 方法
     * case: 设置完成评分开关为打开
     */
    @Test
    public void setCompleteRatingOpen() {
        // 定义一个模拟的机构ID
        String currentAgencyId = "agencyId";
        // 定义开关状态为打开
        boolean open = true;

        // 当调用 userProvider.getCurrentAgencyId() 方法时，返回模拟的机构ID
        when(userProvider.getCurrentAgencyId()).thenReturn(currentAgencyId);

        // 调用待测试的 setCompleteRatingOpen 方法
        agencyService.setCompleteRatingOpen(open);

        // 验证 agencyDao.setMeta 方法是否被调用了一次，并且参数符合预期
        verify(agencyDao, times(1)).setMeta(currentAgencyId, AgencyMetaKey.COMPLETE_RATING_REQUIRED_FOR_CHILD_LOCK.toString(), String.valueOf(open));
    }

    /**
     * 测试 AgencyServiceImpl.getCompleteRatingOpen() 方法
     * case: 获取完成评分开关状态
     */
    @Test
    public void getCompleteRatingOpen() {
        // 模拟 userProvider.getCurrentAgencyId() 方法，返回一个特定的机构ID
        when(userProvider.getCurrentAgencyId()).thenReturn("agency123");

        // 模拟 agencyDao.getMeta() 方法，返回一个特定的 AgencyMetaDataEntity
        AgencyMetaDataEntity meta = new AgencyMetaDataEntity();
        meta.setMetaValue("true"); // 设置 meta 的值为 true
        when(agencyDao.getMeta("agency123", AgencyMetaKey.COMPLETE_RATING_REQUIRED_FOR_CHILD_LOCK.toString())).thenReturn(meta); // 当调用 agencyDao.getMeta() 方法时，返回模拟的 AgencyMetaDataEntity

        // 调用待测试的 getCompleteRatingOpen 方法
        Status status = agencyService.getCompleteRatingOpen();

        // 验证结果
        Assert.assertEquals("true", status.getStatus()); // 验证返回的状态是 true
    }

    @Test
    public void testBatchSetSiteAdminManagementAccessOpen() {
        // Arrange
        OpenModel openModel1 = new OpenModel();
        openModel1.setUserId("user1");
        openModel1.setOpen(true);

        OpenModel openModel2 = new OpenModel();
        openModel2.setUserId("");
        openModel2.setOpen(false);

        SiteAdminOpenRequest request = new SiteAdminOpenRequest();
        request.setOpens(Arrays.asList(openModel1, openModel2));

        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setRole("agency_admin");
        when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // Act
        agencyService.batchSetSiteAdminManagementAccessOpen(request);

        // Assert
        verify(userDao, times(4)).setMetaData(anyString(), anyString(), anyString());
    }

    @Test
    public void testBatchSetSiteAdminManagementAccessOpen2() {
        // Arrange
        OpenModel openModel1 = new OpenModel();
        openModel1.setUserId("user1");
        openModel1.setOpen(true);

        OpenModel openModel2 = new OpenModel();
        openModel2.setUserId("");
        openModel2.setOpen(false);

        SiteAdminOpenRequest request = new SiteAdminOpenRequest();
        request.setOpens(Arrays.asList(openModel1, openModel2));

        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setRole("SITE_ADMIN");
        when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // Act
        agencyService.batchSetSiteAdminManagementAccessOpen(request);

        // Assert
        verify(userDao, times(0)).setMetaData(anyString(), anyString(), anyString());
    }
}

package com.learninggenie.api.service.impl;

import com.learninggenie.api.model.report.ListFilterViewsResponse;
import com.learninggenie.api.model.view.ListViewRequest;
import com.learninggenie.api.model.view.ListViewResponse;
import com.learninggenie.api.model.view.ViewModel;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.security.AuthUserDetails;
import com.learninggenie.api.service.DashboardService;
import com.learninggenie.common.data.dao.CenterDao;
import com.learninggenie.common.data.dao.GroupDao;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.dao.views.FilterDao;
import com.learninggenie.common.data.dao.views.ViewDao;
import com.learninggenie.common.data.entity.AgencyUserEntity;
import com.learninggenie.common.data.entity.CenterEntity;
import com.learninggenie.common.data.entity.GroupEntity;
import com.learninggenie.common.data.entity.UserEntity;
import com.learninggenie.common.data.entity.views.FilterEntity;
import com.learninggenie.common.data.entity.views.ViewEntity;
import com.learninggenie.common.data.enums.UserRole;
import com.learninggenie.common.data.enums.UserType;
import com.learninggenie.common.data.model.AgencyModel;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;

/**
 * Created by zry on 2022/7/28.
 */
@RunWith(MockitoJUnitRunner.class)
public class ViewServiceImplTest {

    @InjectMocks
    ViewServiceImpl viewService;

    @Mock
    private UserProvider userProvider;

    @Mock
    private ViewDao viewDao;

    @Mock
    private FilterDao filterDao;

    @Mock
    private UserDaoImpl userDao;

    @Mock
    private CenterDao centerDao;

    @Mock
    private GroupDao groupDao;

    @Mock
    private DashboardService dashboardService;

    @Test
    public void testCreateView() {
        // 数据准备 -- 登录人信息
        AuthUserDetails user = new AuthUserDetails();
        user.setUsername("430f89e8-abab-4bd0-b284-fb690598344b");
        user.setAgencyId("f4de3604-8959-4131-b8ac-c45db4442c13");
        // 数据准备 -- 入参
        ViewModel request = new ViewModel();
        request.setName("视图01");
        request.setReportType("学生报告");
        request.setSortIndex(1L);
        List<FilterEntity> filters = new ArrayList<>();
        FilterEntity filterEntity1 = new FilterEntity();
        FilterEntity filterEntity2 = new FilterEntity();
        filterEntity1.setName("框架");
        filterEntity1.setValue("IT框架,PS框架");
        filterEntity1.setSortIndex(1L);
        filterEntity1.setAgencyId(user.getAgencyId());
        filterEntity2.setName("班级");
        filterEntity2.setValue("二班,三班");
        filterEntity2.setSortIndex(2L);
        filterEntity2.setAgencyId(user.getAgencyId());
        filters.add(filterEntity1);
        filters.add(filterEntity2);
        request.setFilters(filters);

        // 数据准备 -- 接口模拟
        Mockito.when(userProvider.getCurrentUser()).thenReturn(user);

        // 调用
        viewService.createView(request);

        // 验证 -- 视图创建入参校验
        ArgumentCaptor<ViewEntity> viewEntityArgumentCaptor = ArgumentCaptor.forClass(ViewEntity.class);
        verify(viewDao).save(viewEntityArgumentCaptor.capture());
        ViewEntity view = viewEntityArgumentCaptor.getValue();

        assertNotNull("视图 ID应该不为空", view.getId());
        assertEquals("视图 ID 和入参 ID 不相同", view.getName(), request.getName());
        assertNotNull("视图排序索引应该不为空", view.getSortIndex());
        if (request.getSortIndex() != null) {
            assertEquals("视图排序索引和入参排序索引应该相同", view.getSortIndex(), request.getSortIndex());
        }
        assertEquals("视图报告类型和入参报告类型应该一致", view.getReportType(), request.getReportType());
        assertFalse("视图不应该被删除", view.getDeleted());
        assertNotNull("视图创建时间应该不为空", view.getCreateAtUtc());
        assertNotNull("视图修改时间应该不为空", view.getUpdateAtUtc());
        assertEquals(view.getUpdateAtUtc(), view.getCreateAtUtc());
        assertEquals("视图创建用户 ID 和应该为当前登录人 ID 应该相同", view.getCreateUserId(), user.getId());
        assertEquals("用户机构 ID 和当前登录人机构 ID 应该相同", view.getAgencyId(), user.getAgencyId());

        // 验证 -- 过滤条件创建方法入参校验
        ArgumentCaptor<List<FilterEntity>> filterArgumentCaptor = ArgumentCaptor.forClass(List.class);
        verify(filterDao).saveBatch(filterArgumentCaptor.capture());
        List<FilterEntity> filterEntities = filterArgumentCaptor.getValue();
        List<FilterEntity> requestFilters = request.getFilters();
        AtomicInteger i = new AtomicInteger(); // 遍历下标
        filterEntities.forEach(item -> {
            FilterEntity requestFilter = requestFilters.get(i.get());
            assertNotNull("过滤条件的 ID 应该不为空", item.getId()); // 过滤条件的 ID 应该不为空
            assertEquals(" 过滤条件的名称 和入参过滤条件的名称应该相同", requestFilter.getName(), item.getName()); // 过滤条件的名称应该为入参过滤条件的名称
            assertEquals("过滤条件的值 和入参过滤条件的值应该相同", requestFilter.getValue(), item.getValue()); // 过滤条件的值应该为入参过滤条件的值
            assertEquals("过滤条件的所属视图 ID 和 保存的视图 ID 应该相同", view.getId(), item.getViewId()); // 过滤条件的所属视图 ID 应该为保存的视图 ID
            assertEquals("过滤条件的所属视图的机构 ID 和 保存的视图的机构 ID 不相同", view.getAgencyId(), item.getAgencyId()); // 过滤条件的所属视图 ID 应该为保存的视图 ID
            assertNotNull("过滤条件排序索引为空", item.getSortIndex()); // 过滤条件排序索引应该不为空
            if (requestFilter.getSortIndex() != null) {
                assertEquals("过滤条件排序索引和入参排序索引不相同", requestFilter.getSortIndex(), item.getSortIndex()); // 过滤条件排序索引 和入参排序索引应该相同
            }
            assertFalse("过滤条件不应该被删除", item.getDeleted()); // 过滤条件应该未删除
            i.getAndIncrement();
        });

        // 验证 -- 方法调用次数
        verify(userProvider, Mockito.times(1)).getCurrentUser();
        verify(viewDao, Mockito.times(1)).save(any());
        verify(filterDao, Mockito.times(1)).saveBatch(anyList());
    }

    /**
     * 视图模拟数据
     */
    private List<ViewEntity> getViewsData(String type, List<String> userIds, String agencyId) {
        List<ViewEntity> viewList = new ArrayList<>();
        ViewEntity view1 = new ViewEntity();
        ViewEntity view2 = new ViewEntity();
        ViewEntity view3 = new ViewEntity();
        ViewEntity view4 = new ViewEntity();
        view1.setId(UUID.randomUUID().toString());
        view2.setId(UUID.randomUUID().toString());
        view3.setId(UUID.randomUUID().toString());
        view4.setId(UUID.randomUUID().toString());
        view1.setCreateUserId(userIds.get(0));
        view2.setCreateUserId(userIds.get(1));
        view3.setCreateUserId(userIds.get(0));
        view4.setCreateUserId(userIds.get(1));
        viewList.add(view1);
        viewList.add(view2);
        viewList.add(view3);
        viewList.add(view4);
        for (ViewEntity view : viewList) {
            view.setDeleted(false);
            view.setId(UUID.randomUUID().toString());
            view.setReportType(type);
            view.setAgencyId(agencyId);
        }
        return viewList;
    }

    /**
     * 视图下的过滤条件模拟数据
     */
    private List<FilterEntity> getFiltersData(List<ViewEntity> viewsData) {
        List<FilterEntity> filterList = new ArrayList<>();
        for (ViewEntity view : viewsData) {
            FilterEntity filter1 = new FilterEntity();
            FilterEntity filter2 = new FilterEntity();
            FilterEntity filter3 = new FilterEntity();
            filter1.setId("aa5ccd7d-46a4-48e6-ac61-f0829bfb5b6c");
            filter2.setId("5d2efede-7e7b-41e4-8725-f3f78be6380e");
            filter3.setId("af355be9-8deb-4c1a-b01e-dc98d7767118");
            String id = view.getId();
            filter1.setViewId(id);
            filter2.setViewId(id);
            filter3.setViewId(id);
            filterList.add(filter1);
            filterList.add(filter2);
            filterList.add(filter3);
        }
        int i = 0;
        for (FilterEntity filter : filterList) {
            filter.setAgencyId(viewsData.get(0).getAgencyId());
            filter.setName("默认Name" + i);
            filter.setValue("默认Value" + i);
            i++;
        }
        return filterList;
    }

    /**
     * 统一机构下的机构管理员、机构所有人的数据模拟
     */
    List<AgencyUserEntity> getAgencyUserEntities(String userId, String agencyId) {
        List<AgencyUserEntity> agencyUserEntities = new ArrayList<>();
        AgencyUserEntity user1 = new AgencyUserEntity();
        AgencyUserEntity user2 = new AgencyUserEntity();
        user1.setUserId(userId);
        user1.setAgencyId(agencyId);
        user2.setAgencyId(agencyId);
        user2.setUserId("c0249971-0871-4b8d-9555-5f4756fa7758");
        agencyUserEntities.add(user1);
        agencyUserEntities.add(user2);
        return agencyUserEntities;
    }

    /**
     * 当前用户角色为 机构管理员、机构所有人
     */
    @Test
    public void testListViews_UserRoleIsAgencyOwner() {
        // 数据准备 -- 登录人信息
        String userId = "e1c49525-e04e-4155-9f3f-2e05a21e2fce";
        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setType(UserType.GRANTEE.toString()); // 设置当前用户为 grantee
        user.setRole(UserRole.AGENCY_OWNER.toString()); // grantee 用户同时是机构所有人

        AgencyModel agency = new AgencyModel();
        agency.setId("4f02fe4c-f973-4f12-9ada-364d321082cd");

        // 数据准备 -- 入参
        ListViewRequest request = new ListViewRequest();
        request.setReportType("学生报告");

        // 数据准备 -- 机构用户信息、视图信息、过滤条件信息
        List<AgencyUserEntity> agencyUsers = this.getAgencyUserEntities(user.getId(), agency.getId());
        List<String> userIds = agencyUsers.stream().map(AgencyUserEntity::getUserId).collect(Collectors.toList());
        List<ViewEntity> viewsData = this.getViewsData(request.getReportType(), userIds, agency.getId());
        List<FilterEntity> filtersData = this.getFiltersData(viewsData);

        ListFilterViewsResponse listFilterViewsResponse = new ListFilterViewsResponse();

        // 数据准备 -- 接口模拟
        Mockito.when(userProvider.getCurrentUserId()).thenReturn(userId);
        Mockito.when(userProvider.checkUser(userId)).thenReturn(user);
        Mockito.when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        Mockito.when(viewDao.listByReportTypeAndAgencyId(anyString(), anyString())).thenReturn(viewsData);
        Mockito.when(filterDao.listByViewIdIn(anyList())).thenReturn(filtersData);
        Mockito.when(dashboardService.listFilterViews(anyString(), anyString())).thenReturn(listFilterViewsResponse);
        // 调用
        ListViewResponse listViewResponse = viewService.listViews(request);

        // 验证 -- 出参结果校验
        List<ViewModel> viewsResponse = listViewResponse.getViews();
        for (ViewModel view : viewsResponse) {
            assertTrue("出参视图 ID 不在 获取的视图 ID 中", viewsData.stream().map(ViewEntity::getId).collect(Collectors.toList()).contains(view.getId()));
            assertEquals("出参视图类型 和 当前视图类型不相同", request.getReportType(), view.getReportType());
            assertEquals("出参视图所属机构 ID 和 当前机构 ID 不相同", agency.getId(), view.getAgencyId());
            assertTrue("出参视图创建用户 ID 不在 所有用户 ID 中", agencyUsers.stream().map(AgencyUserEntity::getUserId).collect(Collectors.toList()).contains(view.getCreateUserId()));
            List<FilterEntity> filters = view.getFilters();
            for (FilterEntity filter : filters) {
                List<FilterEntity> filterCollect = filtersData.stream().filter(item -> item.getViewId().equalsIgnoreCase(view.getId())).collect(Collectors.toList());
                List<FilterEntity> filterEntities = filterCollect.stream().filter(item -> item.getId().equalsIgnoreCase(filter.getId())).collect(Collectors.toList());

                assertEquals("出参过滤条件 ID 不在 模拟数据过滤条件 ID 中", 1, filterEntities.size());
                FilterEntity filterData = filterEntities.get(0);
                assertEquals("出参过滤条件所属机构 ID 和 当前机构 ID 不相同", agency.getId(), filter.getAgencyId());
                assertEquals("出参过滤条件所属视图 ID 和 模拟数据中过滤条件所属视图 ID 不相同", filterData.getViewId(), filter.getViewId());
            }
        }

        // 验证 -- 方法调用次数
        verify(userProvider, Mockito.times(1)).getCurrentUserId();
        verify(userProvider, Mockito.times(1)).checkUser(anyString());
        verify(userProvider, Mockito.times(1)).getAgencyByUserId(anyString());

        verify(userDao, Mockito.times(0)).getTeacherByAgencyId(anyString());

        verify(viewDao, Mockito.times(1)).listByReportTypeAndAgencyId(anyString(), anyString());
        verify(filterDao, Mockito.times(1)).listByViewIdIn(anyList());

        verify(groupDao, Mockito.times(0)).getGroupsByTeacherId(anyString());
        verify(groupDao, Mockito.times(0)).getGroupsByCenterIds(anyList());
        verify(centerDao, Mockito.times(0)).getBySiteAdminId(anyString());
    }

    /**
     * 当前用户角色为 园长
     */
    @Test
    public void testListViews_UserRoleIsSiteAdmin() {
        // 数据准备 -- 登录人信息
        String userId = "e1c49525-e04e-4155-9f3f-2e05a21e2fce";
        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setRole(UserRole.SITE_ADMIN.toString()); // 设置当前用户为 园长
        AgencyModel agency = new AgencyModel();
        agency.setId("4f02fe4c-f973-4f12-9ada-364d321082cd");

        // 数据准备 -- 入参
        ListViewRequest request = new ListViewRequest();
        request.setReportType("学生报告");

        // 数据准备 -- 同一机构下园长信息、视图信息、过滤条件信息
        List<CenterEntity> centerList = new ArrayList<>();
        CenterEntity center1 = new CenterEntity();
        center1.setId("e9eb154a-7bcb-4956-b0b7-d7480b52404f");
        CenterEntity center2 = new CenterEntity();
        center2.setId(user.getId());
        centerList.add(center1);
        centerList.add(center2);

        List<String> userIds = centerList.stream().map(CenterEntity::getId).collect(Collectors.toList());

        List<ViewEntity> viewsData = this.getViewsData(request.getReportType(), userIds, agency.getId());
        List<FilterEntity> filtersData = this.getFiltersData(viewsData);

        ListFilterViewsResponse listFilterViewsResponse = new ListFilterViewsResponse();

        // 数据准备 -- 接口模拟
        Mockito.when(userProvider.getCurrentUserId()).thenReturn(userId);
        Mockito.when(userProvider.checkUser(userId)).thenReturn(user);
        Mockito.when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        Mockito.when(viewDao.listByReportTypeAndAgencyId(anyString(), anyString())).thenReturn(viewsData);
        Mockito.when(filterDao.listByViewIdIn(anyList())).thenReturn(filtersData);
        Mockito.when(centerDao.getBySiteAdminId(anyString())).thenReturn(centerList);
        Mockito.when(dashboardService.listFilterViews(anyString(), anyString())).thenReturn(listFilterViewsResponse);
        // 调用
        ListViewResponse listViewResponse = viewService.listViews(request);

        // 验证 -- 出参结果校验
        List<ViewModel> viewsResponse = listViewResponse.getViews();
        for (ViewModel view : viewsResponse) {
            assertTrue("出参视图 ID 不在 获取的视图 ID 中", viewsData.stream().map(ViewEntity::getId).collect(Collectors.toList()).contains(view.getId()));
            assertEquals("出参视图类型 和 当前视图类型不相同", request.getReportType(), view.getReportType());
            assertEquals("出参视图所属机构 ID 和 当前机构 ID 不相同", agency.getId(), view.getAgencyId());
            assertTrue("出参视图创建用户 ID 不在 所有用户 ID 中", userIds.contains(view.getCreateUserId()));
            List<FilterEntity> filters = view.getFilters();
            for (FilterEntity filter : filters) {
                List<FilterEntity> filterCollect = filtersData.stream().filter(item -> item.getViewId().equalsIgnoreCase(view.getId())).collect(Collectors.toList());
                List<FilterEntity> filterEntities = filterCollect.stream().filter(item -> item.getId().equalsIgnoreCase(filter.getId())).collect(Collectors.toList());

                assertEquals("出参过滤条件 ID 不在 模拟数据过滤条件 ID 中", 1, filterEntities.size());
                FilterEntity filterData = filterEntities.get(0);
                assertEquals("出参过滤条件所属机构 ID 和 当前机构 ID 不相同", agency.getId(), filter.getAgencyId());
                assertEquals("出参过滤条件所属视图 ID 和 模拟数据中过滤条件所属视图 ID 不相同", filterData.getViewId(), filter.getViewId());
            }
        }

        // 验证 -- 方法调用次数
        verify(userProvider, Mockito.times(1)).getCurrentUserId();
        verify(userProvider, Mockito.times(1)).checkUser(anyString());
        verify(userProvider, Mockito.times(1)).getAgencyByUserId(anyString());


        verify(viewDao, Mockito.times(1)).listByReportTypeAndAgencyId(anyString(), anyString());
        verify(filterDao, Mockito.times(1)).listByViewIdIn(anyList());

        verify(groupDao, Mockito.times(0)).getGroupsByTeacherId(anyString());
        verify(groupDao, Mockito.times(1)).getGroupsByCenterIds(anyList());
        verify(centerDao, Mockito.times(2)).getBySiteAdminId(anyString());
    }

    /**
     * 当前用户角色为 老师
     */
    @Test
    public void testListViews_UserRoleIsTeacher() {
        // 数据准备 -- 登录人信息
        String userId = "e1c49525-e04e-4155-9f3f-2e05a21e2fce";
        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setRole(UserRole.COLLABORATOR.toString()); // 设置当前用户为 老师
        AgencyModel agency = new AgencyModel();
        agency.setId("4f02fe4c-f973-4f12-9ada-364d321082cd");

        // 数据准备 -- 入参
        ListViewRequest request = new ListViewRequest();
        request.setReportType("学生报告");

        // 数据准备 -- 同一班级老师信息、视图信息、过滤条件信息
        List<GroupEntity> groups = new ArrayList<>();
        GroupEntity group1 = new GroupEntity();
        GroupEntity group2 = new GroupEntity();
        group1.setId(user.getId());
        group2.setId("60dc7c52-3d24-4f82-9400-83e9e1d7537b");
        groups.add(group1);
        groups.add(group2);
        List<String> userIds = groups.stream().map(GroupEntity::getId).collect(Collectors.toList());
        List<ViewEntity> viewsData = this.getViewsData(request.getReportType(), userIds, agency.getId());
        List<FilterEntity> filtersData = this.getFiltersData(viewsData);

        List<CenterEntity> centers = new ArrayList<>();
        CenterEntity center = new CenterEntity();
        center.setId("806709a0-5b5b-402f-8e29-b279a9b45a07");
        HashSet<GroupEntity> groupSet = new HashSet<>(groups);
        center.setGroups(groupSet);
        centers.add(center);

        ListFilterViewsResponse listFilterViewsResponse = new ListFilterViewsResponse();

        // 数据准备 -- 接口模拟
        Mockito.when(userProvider.getCurrentUserId()).thenReturn(userId);
        Mockito.when(userProvider.checkUser(userId)).thenReturn(user);
        Mockito.when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        Mockito.when(viewDao.listByReportTypeAndAgencyId(anyString(), anyString())).thenReturn(viewsData);
        Mockito.when(filterDao.listByViewIdIn(anyList())).thenReturn(filtersData);
        Mockito.when(groupDao.getGroupsByTeacherId(anyString())).thenReturn(groups);
        Mockito.when(centerDao.getCentersByGroupIds(anyList())).thenReturn(centers);
        Mockito.when(dashboardService.listFilterViews(anyString(), anyString())).thenReturn(listFilterViewsResponse);

        // 调用
        ListViewResponse listViewResponse = viewService.listViews(request);

        // 验证 -- 出参结果校验
        List<ViewModel> viewsResponse = listViewResponse.getViews();
        for (ViewModel view : viewsResponse) {
            assertTrue("出参视图 ID 不在 获取的视图 ID 中", viewsData.stream().map(ViewEntity::getId).collect(Collectors.toList()).contains(view.getId()));
            assertEquals("出参视图类型 和 当前视图类型不相同", request.getReportType(), view.getReportType());
            assertEquals("出参视图所属机构 ID 和 当前机构 ID 不相同", agency.getId(), view.getAgencyId());
            assertTrue("出参视图创建用户 ID 不在 所有用户 ID 中", userIds.contains(view.getCreateUserId()));
            List<FilterEntity> filters = view.getFilters();
            for (FilterEntity filter : filters) {
                List<FilterEntity> filterCollect = filtersData.stream().filter(item -> item.getViewId().equalsIgnoreCase(view.getId())).collect(Collectors.toList());
                List<FilterEntity> filterEntities = filterCollect.stream().filter(item -> item.getId().equalsIgnoreCase(filter.getId())).collect(Collectors.toList());

                assertEquals("出参过滤条件 ID 不在 模拟数据过滤条件 ID 中", 1, filterEntities.size());
                FilterEntity filterData = filterEntities.get(0);
                assertEquals("出参过滤条件所属机构 ID 和 当前机构 ID 不相同", agency.getId(), filter.getAgencyId());
                assertEquals("出参过滤条件所属视图 ID 和 模拟数据中过滤条件所属视图 ID 不相同", filterData.getViewId(), filter.getViewId());
            }
        }

        // 验证 -- 方法调用次数
        verify(userProvider, Mockito.times(1)).getCurrentUserId();
        verify(userProvider, Mockito.times(1)).checkUser(anyString());
        verify(userProvider, Mockito.times(1)).getAgencyByUserId(anyString());

        verify(viewDao, Mockito.times(1)).listByReportTypeAndAgencyId(anyString(), anyString());
        verify(filterDao, Mockito.times(1)).listByViewIdIn(anyList());

        verify(groupDao, Mockito.times(2)).getGroupsByTeacherId(anyString());
        verify(groupDao, Mockito.times(0)).getGroupsByCenterIds(anyList());
        verify(centerDao, Mockito.times(0)).getBySiteAdminId(anyString());
    }

    @Test
    public void testUpdateView() {
        // 数据准备 -- 登录人信息
        AuthUserDetails user = new AuthUserDetails();
        user.setUsername("430f89e8-abab-4bd0-b284-fb690598344b");
        user.setAgencyId("f4de3604-8959-4131-b8ac-c45db4442c13");
        // 数据准备 -- 入参
        ViewModel request = new ViewModel();
        request.setId(UUID.randomUUID().toString());
        request.setName("修改后视图名");
        request.setReportType("学生报告");
        request.setSortIndex(1L);
        List<FilterEntity> filters = new ArrayList<>();
        FilterEntity filterEntity1 = new FilterEntity();
        FilterEntity filterEntity2 = new FilterEntity();
        filterEntity1.setName("框架");
        filterEntity1.setValue("IT框架,PS框架");
        filterEntity1.setSortIndex(1L);
        filterEntity1.setAgencyId(user.getAgencyId());
        filterEntity1.setViewId(request.getId());

        filterEntity2.setName("班级");
        filterEntity2.setValue("二班,三班");
        filterEntity2.setSortIndex(2L);
        filterEntity2.setAgencyId(user.getAgencyId());
        filterEntity2.setViewId(request.getId());

        filters.add(filterEntity1);
        filters.add(filterEntity2);
        request.setFilters(filters);

        // 修改前的视图信息
        ViewEntity viewData = new ViewEntity();
        viewData.setId(request.getId());
        viewData.setDeleted(false);
        viewData.setName("修改前视图名");

        // 修改前的过滤条件信息
        List<FilterEntity> filtersData = this.getFiltersData(Collections.singletonList(viewData));
        request.setId(viewData.getId()); // 视图 ID 一致
        List<FilterEntity> requestFilters = request.getFilters();
        requestFilters.get(0).setId(filtersData.get(0).getId());
        requestFilters.get(1).setId(filtersData.get(1).getId());

        // 数据准备 -- 接口模拟
        doReturn(viewData).when(viewDao).getById(request.getId());
        doReturn(filtersData).when(filterDao).listByViewId(request.getId());

        // 调用
        viewService.updateView(request);

        // 验证 -- 视图名称是否被修改
        ArgumentCaptor<ViewEntity> viewArgument = ArgumentCaptor.forClass(ViewEntity.class);
        verify(viewDao).updateById(viewArgument.capture());
        ViewEntity viewArgValue = viewArgument.getValue();
        assertEquals("视图名和修改后的视图名不一样", request.getName(), viewArgValue.getName());

        // 验证 -- 过滤条件是否被修改
        ArgumentCaptor<List<FilterEntity>> filterArgumentCaptor = ArgumentCaptor.forClass(List.class);
        verify(filterDao).updateBatchById(filterArgumentCaptor.capture());
        List<FilterEntity> filtersArg = filterArgumentCaptor.getValue();
        for (FilterEntity filter : filtersArg) {
            List<FilterEntity> requestFilters1 = request.getFilters();
            // 可以使用 stream 过滤返回对象
            if (filter.getId().equals(requestFilters1.get(0).getId())) {
                assertEquals("过滤条件的名称应该是修改前的名称", filtersData.get(0).getName(), filter.getName());
                assertEquals("过滤条件的名称应该是修改后的值", requestFilters1.get(0).getValue(), filter.getValue());
            } else if (filter.getId().equals(requestFilters1.get(1).getId())) {
                assertEquals("过滤条件的名称应该是修改前的名称", filtersData.get(1).getName(), filter.getName());
                assertEquals("过滤条件的名称应该是修改后的值", requestFilters1.get(1).getValue(), filter.getValue());
            } else {
                assertEquals("过滤条件的名称应该是修改前的名称", filtersData.get(2).getName(), filter.getName());
                assertEquals("过滤条件的名称应该是修改前的值", filtersData.get(2).getValue(), filter.getValue());
            }
        }

        // 验证 -- 方法调用次数
        verify(viewDao, Mockito.times(1)).getById(anyString()); // 更新视图名称不相同则更新视图名
        verify(viewDao, Mockito.times(1)).updateById(any());
        verify(filterDao, Mockito.times(1)).listByViewId(anyString());
    }

    @Test
    public void testDeleteView() {
        // 数据准备 -- 入参
        String request = "4ceddfb9-3372-45e8-94bb-757024083904";

        // 数据准备 -- 准备删除的视图信息
        ViewEntity viewData = new ViewEntity();
        viewData.setId(request);
        viewData.setName("视图01");
        List<FilterEntity> filterEntities = this.getFiltersData(Collections.singletonList(viewData));
        // 数据准备 -- 接口模拟
        doReturn(viewData).when(viewDao).getById(request);
        doReturn(filterEntities).when(filterDao).listByViewId(request);

        // 调用
        viewService.deleteView(request);

        // 验证 -- 逻辑删除视图入参校验
        ArgumentCaptor<ViewEntity> acView = ArgumentCaptor.forClass(ViewEntity.class);
        verify(viewDao).updateById(acView.capture());
        ViewEntity viewArgValue = acView.getValue();
        assertEquals("被删除视图 ID 和 入参视图 ID 不相同", request, viewArgValue.getId());
        assertTrue("被删除视图删除状态应为真", viewArgValue.getDeleted());
        // 验证 -- 逻辑删除视图过滤条件入参校验
        ArgumentCaptor<List<FilterEntity>> acFilters = ArgumentCaptor.forClass(List.class);
        verify(filterDao).updateBatchById(acFilters.capture());
        List<FilterEntity> filtersValue = acFilters.getValue();
        filtersValue.forEach(filter -> {
            assertEquals("被删除过滤条件所属视图 ID 和 入参视图 ID 相同", request, filter.getViewId());
            assertNotNull("被删除过滤条件 ID 和 入参视图下的过滤条件 ID 不相同", filterEntities.stream().filter(item -> item.getId().equals(filter.getId())).findFirst().get());
            assertTrue("被删除过滤条件删除状态应为真", filter.getDeleted());
        });

        // 验证 -- 方法调用次数
        verify(viewDao, Mockito.times(1)).getById(anyString()); // 更新视图名称不相同则更新视图名
        verify(viewDao, Mockito.times(1)).updateById(any());
        verify(filterDao, Mockito.times(1)).listByViewId(anyString());
        verify(filterDao, Mockito.times(1)).updateBatchById(anyList());
    }
}
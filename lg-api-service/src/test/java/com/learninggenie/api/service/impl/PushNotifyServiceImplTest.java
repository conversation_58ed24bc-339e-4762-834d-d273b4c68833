//package com.learninggenie.api.service.impl;
//
//import com.learninggenie.api.exception.BusinessException;
//import com.learninggenie.api.provider.UserProvider;
//import com.learninggenie.api.service.PushNotifyService;
//import com.learninggenie.common.comm.CommService;
//import com.learninggenie.common.data.dao.*;
//import com.learninggenie.common.data.dao.impl.UserDaoImpl;
//import com.learninggenie.common.data.entity.CenterEntity;
//import com.learninggenie.common.data.entity.GroupEntity;
//import com.learninggenie.common.data.enums.UserRole;
//import com.learninggenie.common.data.model.AgencyModel;
//import com.learninggenie.common.data.model.EnrollmentModel;
//import com.learninggenie.common.data.model.UserModel;
//import com.learninggenie.common.data.model.push.PushNotifyRequest;
//import com.learninggenie.common.data.model.push.SendingPlan;
//import com.learninggenie.common.data.repository.CenterRepository;
//import com.learninggenie.common.data.repository.GroupRepository;
//import com.learninggenie.common.filesystem.FileSystem;
//import com.learninggenie.common.utils.ResourceUtil;
//import com.learninggenie.common.utils.RestApiUtil;
//import com.learninggenie.common.utils.TimeUtil;
//import org.junit.Ignore;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.powermock.core.classloader.annotations.PrepareForTest;
//import org.powermock.modules.junit4.PowerMockRunner;
//
//import java.util.*;
//
//import static org.junit.Assert.*;
//
//@RunWith(PowerMockRunner.class)
//@PrepareForTest({TimeUtil.class, ResourceUtil.class, RestApiUtil.class})
//public class PushNotifyServiceImplTest {
//    @InjectMocks
//    private PushNotifyServiceImpl pushService;
//    @Mock
//    private AgencyDao agencyDao;
//    @Mock
//    private PushNotificationDao pushNotificationDao;
//    @Mock
//    private UserDaoImpl userDao;
//    @Mock
//    private MediaBookDao mediaBookDao;
//    @Mock
//    private CenterDao centerDao;
//    @Mock
//    private CommService commService;
//    @Mock
//    private StudentDao studentDao;
//    @Mock
//    private FileSystem fileSystem;
//    @Mock
//    private GroupRepository groupRepository;
//    @Mock
//    private UserProvider userProvider;
//
//
//    /**
//     * 成功添加一条推送
//     */
//    @Test
//    public void addOnePushNotification() {
//
//        Date utcNow = TimeUtil.getUtcNow();
//        Date localTime = TimeUtil.convertUtcToLocal(utcNow, "America/Los_Angeles");
//        Date pushTime = TimeUtil.addDays(localTime, 1);
//        String pushTimeStr = TimeUtil.format(pushTime, TimeUtil.format2);
//
//        PushNotifyRequest request = new PushNotifyRequest();
//        request.setPushTime(pushTimeStr);
//        request.setMediaCategory("Shawn Brown");
//        List<String> videoIds = new ArrayList<>();
//        videoIds.add("v001");
//        request.setVideoIds(videoIds);
//        SendingPlan plan = new SendingPlan();
//        plan.setSelectedType("Days");
//        request.setPlan(plan);
//        UserModel user = new UserModel();
//        user.setId("u001");
//        user.setRole("AGENCY_OWNER");
//
//        AgencyModel agencyModel = new AgencyModel();
//        agencyModel.setId("a001");
//        agencyModel.setName("agency");
//
//        List<AgencyModel> agencyModels = new ArrayList<>();
//        agencyModels.add(agencyModel);
//
//        EnrollmentModel enrollmentModel = new EnrollmentModel();
//        enrollmentModel.setId("e001");
//
//        List<EnrollmentModel> enrollmentModels = new ArrayList<>();
//        enrollmentModels.add(enrollmentModel);
//
//        List<EnrollmentModel> enrollmentModels2 = new ArrayList<>();
//
//        List<String> ids = new ArrayList<>();
//        ids.add("c001");
//        ids.add("g001");
//
//        request.setCenterIds(ids);
//
//        Mockito.when(userDao.getUserById("u001")).thenReturn(user);
//        Mockito.when(userDao.getAgencyByAgencyAdminId("u001")).thenReturn(agencyModels);
//        Mockito.when(centerDao.getFirstCenterTimeZoneByAgencyId("a001")).thenReturn("America/Los_Angeles");
//        Mockito.when(userProvider.getAgencyByUserId(Mockito.anyString())).thenReturn(agencyModel);
//        Mockito.when(studentDao.getChildrenByCenterIds(ids, null)).thenReturn(enrollmentModels);
//        Mockito.when(studentDao.getChildrenByCenterIds(ids, null)).thenReturn(enrollmentModels);
//
//        pushService.addPushNotifications(request, "u001");
//
//        Mockito.verify(pushNotificationDao, Mockito.times(1)).batchCreatePushNotifications(Mockito.anyObject());
//    }
//
//    /**
//     * 测试AddPushNotifications是否保存数据成功
//     */
//    @Test
//    @Ignore
//    public void testAddPushNotifications_save() {
//        String userId = "u0001";
//        String agencyId = "1";
//        List<String> videos = Collections.singletonList("1");
//        List<String> centerIds = Collections.singletonList("1");
//        List<String> groupIds = Collections.singletonList("1");
//
//        PushNotifyRequest request = new PushNotifyRequest();
//        request.setMediaCategory("creagory");
//        request.setAgencyId(agencyId);
//        Long time = new Date().getTime() + 60000L;
//        request.setPushTime(TimeUtil.format(new Date(time), TimeUtil.format2));
//        request.setVideoIds(videos);
//        request.setCenterIds(centerIds);
//        request.setCenterIds(groupIds);
//        GroupEntity groupEntity = new GroupEntity();
//        groupEntity.setId("1");
//        AgencyModel agencyModel = new AgencyModel();
//        agencyModel.setId(agencyId);
//
//        EnrollmentModel enrollmentModel = new EnrollmentModel();
//        enrollmentModel.setId("e001");
//
//        List<EnrollmentModel> enrollmentModels = new ArrayList<>();
//        enrollmentModels.add(enrollmentModel);
//
//        List<EnrollmentModel> enrollmentModels2 = new ArrayList<>();
//
//        List<String> ids = new ArrayList<>();
//        ids.add("c001");
//        ids.add("g001");
//
//        request.setCenterIds(ids);
//
//        UserModel userModel = new UserModel();
//        userModel.setId(userId);
//        userModel.setAgencies(Arrays.asList(agencyModel));
//        userModel.setRole(UserRole.AGENCY_ADMIN.toString());
//        Mockito.when(userDao.getUserById(userId)).thenReturn(userModel);
//        Mockito.when(userDao.getAgencyByAgencyAdminId(userId)).thenReturn(Arrays.asList(agencyModel));
//        Mockito.when(groupRepository.findById("1")).thenReturn(Optional.ofNullable.thenReturn(groupEntity);
//        CenterEntity centerEntity = new CenterEntity();
//        centerEntity.setId("1");
//        Mockito.when(centerDao.getCenter("1")).thenReturn(centerEntity);
//        Mockito.when(centerDao.getFirstCenterTimeZoneByAgencyId(Mockito.anyString())).thenReturn("Asia/Shanghai");
//        Mockito.when(userProvider.getAgencyByUserId(Mockito.anyString())).thenReturn(agencyModel);
//        Mockito.when(studentDao.getChildrenByCenterIds(ids, null)).thenReturn(enrollmentModels);
//        Mockito.when(studentDao.getChildrenByCenterIds(ids, null)).thenReturn(enrollmentModels);
//
//        pushService.addPushNotifications(request, userId);
//
//        Mockito.verify(pushNotificationDao, Mockito.times(1)).batchCreatePushNotifications(Mockito.anyObject());
//    }
//
//    /**
//     * 没有小孩
//     */
//    @Test(expected = BusinessException.class)
//    public void addOnePushNotification_NoChild() {
//        Date utcNow = TimeUtil.getUtcNow();
//        Date localTime = TimeUtil.convertUtcToLocal(utcNow, "America/Los_Angeles");
//        Date pushTime = TimeUtil.addDays(localTime, 1);
//        String pushTimeStr = TimeUtil.format(pushTime, TimeUtil.format2);
//
//        PushNotifyRequest request = new PushNotifyRequest();
//        request.setPushTime(pushTimeStr);
//        request.setMediaCategory("Shawn Brown");
//        List<String> videoIds = new ArrayList<>();
//        videoIds.add("v001");
//        request.setVideoIds(videoIds);
//
//        UserModel user = new UserModel();
//        user.setId("u001");
//        user.setRole("AGENCY_OWNER");
//
//        AgencyModel agencyModel = new AgencyModel();
//        agencyModel.setId("a001");
//        agencyModel.setName("agency");
//
//        List<AgencyModel> agencyModels = new ArrayList<>();
//        agencyModels.add(agencyModel);
//
//        EnrollmentModel enrollmentModel = new EnrollmentModel();
//        enrollmentModel.setId("e001");
//
//        List<EnrollmentModel> enrollmentModels2 = new ArrayList<>();
//
//        List<String> ids = new ArrayList<>();
//        ids.add("c001");
//        ids.add("g001");
//
//        request.setCenterIds(ids);
//
//        Mockito.when(userDao.getUserById("u001")).thenReturn(user);
//        Mockito.when(userDao.getAgencyByAgencyAdminId("u001")).thenReturn(agencyModels);
//        Mockito.when(centerDao.getFirstCenterTimeZoneByAgencyId("a001")).thenReturn("America/Los_Angeles");
//        Mockito.when(userProvider.getAgencyByUserId(Mockito.anyString())).thenReturn(agencyModel);
//        Mockito.when(studentDao.getChildrenByCenterIds(ids, null)).thenReturn(enrollmentModels2);
//        Mockito.when(studentDao.getChildrenByCenterIds(ids, null)).thenReturn(enrollmentModels2);
//
//        pushService.addPushNotifications(request, "u001");
//
//        Mockito.verify(pushNotificationDao, Mockito.times(1)).batchCreatePushNotifications(Mockito.anyObject());
//    }
//}
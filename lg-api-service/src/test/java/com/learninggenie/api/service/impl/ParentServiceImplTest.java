package com.learninggenie.api.service.impl;

import com.amazonaws.services.lambda.model.InvokeResult;
import com.learninggenie.api.model.DownFileResponse;
import com.learninggenie.api.model.InvitationParentRequest;
import com.learninggenie.api.model.InvitationStateConstants;
import com.learninggenie.api.model.invitationparent.InvitationParentResponse;
import com.learninggenie.api.provider.RemoteProvider;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.security.DotNetPasswordEncoder;
import com.learninggenie.api.service.AccountService;
import com.learninggenie.api.service.EnrollmentService;
import com.learninggenie.common.data.dao.*;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.dto.EnrollmentDTO;
import com.learninggenie.common.data.entity.*;
import com.learninggenie.common.data.model.PdfConvertJobEntity;
import com.learninggenie.common.data.model.UserModel;
import com.learninggenie.common.data.repository.CenterRepository;
import com.learninggenie.common.data.repository.GroupRepository;
import com.learninggenie.common.data.repository.UserRepository;
import com.learninggenie.common.filesystem.FileSystem;
import com.learninggenie.common.messaging.EmailModel;
import com.learninggenie.common.messaging.MandrillServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * ParentServiceImpl 单元测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class ParentServiceImplTest {

    @InjectMocks
    private ParentServiceImpl parentService;

    @Mock
    InvitationsEnrollmentInvitationDao invitationsEnrollmentInvitationDao;

    @Mock
    UserRepository userRepository;

    @Mock
    UserDaoImpl userDao;

    @Mock
    LoginLogDao loginLogDao;

    @Mock
    InvitationEnrollmentinvitationDao invitationEnrollmentinvitationDao;

    @Mock
    StudentDao studentDao;

    @Mock
    EnrollmentService enrollmentService;

    @Mock
    DotNetPasswordEncoder dotNetPasswordEncoder;

    @Mock
    AccountService accountService;

    @Mock
    CenterRepository centerRepository;

    @Mock
    private GroupRepository groupRepository;

    @Mock
    private UserProvider userProvider;

    @Mock
    private FileSystem fileSystem;

    @Mock
    private ReportDao reportDao;

    @Mock
    private RemoteProvider remoteProvider;

    @Mock
    private MediaDao mediaDao;

    @Mock
    private UsersFileDao usersFileDao;

    @Mock
    private MandrillServiceImpl mandrillService;

    @Mock
    private AgencyDao agencyDao;

    /**
     * 测试设置邀请信息方法
     * case: 家长账户已存在，没有邀请信息的家长，处理类型是去邀请（0）
     */
    @Test
    public void testSetInvitationDataCaseParentNoInvitation() {
        // 准备方法入参
        InvitationParentResponse invitationParentResponse = new InvitationParentResponse();
        String createUserId = "createUserId001";
        String email = "email002";
        String enrollmentId = "enrollmentId001";
        String type = "0";
        int verifyNum = 0;
        // 数据模拟
        UserEntity userEntity = new UserEntity();
        userEntity.setId("userId001");
        UserModel user = new UserModel();
        user.setId(userEntity.getId());
        user.setEmail("email002");
        String token = "token001";
        when(invitationsEnrollmentInvitationDao.getDatasByEmailAndEnrollementId(email, enrollmentId, InvitationStateConstants.NOINVITATION))
                .thenReturn(null);
        when(invitationsEnrollmentInvitationDao.getDatasByEmailAndEnrollementId(email, enrollmentId, InvitationStateConstants.NOLINK))
                .thenReturn(null);
        when(invitationsEnrollmentInvitationDao.getDatasByEmailAndEnrollementId(email, enrollmentId, InvitationStateConstants.LINK))
                .thenReturn(null);
        when(userRepository.getTop1ByEmailAndIsDeletedFalse(email)).thenReturn(userEntity);
        when(loginLogDao.getLastLoginTokenByUserId(user.getId())).thenReturn(token);
        when(invitationsEnrollmentInvitationDao.getByEmailAndEnrollementIdAndState(email, InvitationStateConstants.NOLINK))
                .thenReturn(Collections.emptyList());
        when(invitationsEnrollmentInvitationDao.getByEmailAndEnrollementIdAndState(email, InvitationStateConstants.LINK))
                .thenReturn(Collections.emptyList());

        doNothing().when(invitationEnrollmentinvitationDao).addInvitationsEnrollmentInvitation(any());
        doNothing().when(studentDao).saveStudentParent(any());
        when(enrollmentService.getByEnrollmentId(anyString()))
                .thenReturn(new EnrollmentDTO());
        when(invitationsEnrollmentInvitationDao.getByEmail(user.getEmail()))
                .thenReturn(Collections.emptyList());
        // 调用测试方法
        parentService.setInvitationData(invitationParentResponse, createUserId, email, enrollmentId, type, verifyNum);
        // 验证
        verify(invitationsEnrollmentInvitationDao, times(1)).addInvitationsEnrollmentInvitation(any(InvitationsEnrollmentInvitationEntity.class)); // 创建了邀请信息
        verify(invitationEnrollmentinvitationDao, times(1)).addInvitationsEnrollmentInvitation(any(InvitationEnrollmentinvitationEntity.class)); // 创建了邀请关系
        verify(studentDao, times(1)).saveStudentParent(any(UserEnrollmentEntity.class)); // 创建了家长孩子关系
    }

    /**
     * 测试设置邀请信息方法
     * case: 家长账户已存在，家长和该小孩不存在待邀请的关系，有其他已连接的关系，处理类型是去邀请（0）
     */
    @Test
    public void testSetInvitationDataCaseParentLinked() {
        // 准备方法入参
        InvitationParentResponse invitationParentResponse = new InvitationParentResponse();
        String createUserId = "createUserId001";
        String email = "email002";
        String enrollmentId = "enrollmentId001";
        String type = "0";
        int verifyNum = 0;
        // 数据模拟
        UserEntity userEntity = new UserEntity();
        userEntity.setId("userId001");
        UserModel user = new UserModel();
        user.setId(userEntity.getId());
        user.setEmail("email002");
        String token = "token001";
        List<InvitationsEnrollmentInvitationEntity> otherLinks = new ArrayList<>();
        InvitationsEnrollmentInvitationEntity otherLink = new InvitationsEnrollmentInvitationEntity();
        otherLink.setId("otherLink001");
        otherLinks.add(otherLink);
        when(invitationsEnrollmentInvitationDao.getDatasByEmailAndEnrollementId(email, enrollmentId, InvitationStateConstants.NOINVITATION))
                .thenReturn(null);
        when(invitationsEnrollmentInvitationDao.getDatasByEmailAndEnrollementId(email, enrollmentId, InvitationStateConstants.NOLINK))
                .thenReturn(null);
        when(invitationsEnrollmentInvitationDao.getDatasByEmailAndEnrollementId(email, enrollmentId, InvitationStateConstants.LINK))
                .thenReturn(null);
        when(userRepository.getTop1ByEmailAndIsDeletedFalse(email)).thenReturn(userEntity);
        when(loginLogDao.getLastLoginTokenByUserId(user.getId())).thenReturn(token);
        when(invitationsEnrollmentInvitationDao.getByEmailAndEnrollementIdAndState(email, InvitationStateConstants.NOLINK))
                .thenReturn(Collections.emptyList());
        when(invitationsEnrollmentInvitationDao.getByEmailAndEnrollementIdAndState(email, InvitationStateConstants.LINK))
                .thenReturn(otherLinks);
        doNothing().when(invitationEnrollmentinvitationDao).addInvitationsEnrollmentInvitation(any());
        doNothing().when(studentDao).saveStudentParent(any());
        when(enrollmentService.getByEnrollmentId(anyString()))
                .thenReturn(new EnrollmentDTO());
        when(invitationsEnrollmentInvitationDao.getByEmail(user.getEmail()))
                .thenReturn(Collections.emptyList());
        // 调用测试方法
        parentService.setInvitationData(invitationParentResponse, createUserId, email, enrollmentId, type, verifyNum);
        // 验证
        verify(invitationsEnrollmentInvitationDao, times(1)).addInvitationsEnrollmentInvitation(any(InvitationsEnrollmentInvitationEntity.class)); // 创建了邀请信息
        verify(invitationEnrollmentinvitationDao, times(1)).addInvitationsEnrollmentInvitation(any(InvitationEnrollmentinvitationEntity.class)); // 创建了邀请关系
        verify(studentDao, times(1)).saveStudentParent(any(UserEnrollmentEntity.class)); // 创建了家长孩子关系
    }

    /**
     * 测试设置邀请信息方法
     * case: 家长账户已存在，家长有其他已邀请的关系，处理类型是去邀请（0）
     */
    @Test
    public void testSetInvitationDataCaseParentHaveInvited() {
        // 准备方法入参
        InvitationParentResponse invitationParentResponse = new InvitationParentResponse();
        String createUserId = "createUserId001";
        String email = "email002";
        String enrollmentId = "enrollmentId001";
        String type = "0";
        int verifyNum = 0;
        // 数据模拟
        UserEntity userEntity = new UserEntity();
        userEntity.setId("userId001");
        UserModel user = new UserModel();
        user.setId(userEntity.getId());
        user.setEmail("email002");
        String token = "token001";
        List<InvitationsEnrollmentInvitationEntity> otherInvitations = new ArrayList<>();
        InvitationsEnrollmentInvitationEntity otherInvitation = new InvitationsEnrollmentInvitationEntity();
        otherInvitation.setId("otherLink001");
        otherInvitations.add(otherInvitation);
        when(invitationsEnrollmentInvitationDao.getDatasByEmailAndEnrollementId(email, enrollmentId, InvitationStateConstants.NOINVITATION))
                .thenReturn(null);
        when(invitationsEnrollmentInvitationDao.getDatasByEmailAndEnrollementId(email, enrollmentId, InvitationStateConstants.NOLINK))
                .thenReturn(null);
        when(invitationsEnrollmentInvitationDao.getDatasByEmailAndEnrollementId(email, enrollmentId, InvitationStateConstants.LINK))
                .thenReturn(null);
        when(userRepository.getTop1ByEmailAndIsDeletedFalse(email)).thenReturn(userEntity);
        when(loginLogDao.getLastLoginTokenByUserId(user.getId())).thenReturn(token);

        when(userRepository.updatePassword(anyString(), anyString())).thenReturn(1);
        when(dotNetPasswordEncoder.encode(anyString())).thenReturn("password");
        doNothing().when(invitationsEnrollmentInvitationDao).updateInvitationsEnrollmentInvitation(any(InvitationsEnrollmentInvitationEntity.class));

        when(invitationsEnrollmentInvitationDao.getByEmailAndEnrollementIdAndState(email, InvitationStateConstants.NOLINK))
                .thenReturn(otherInvitations);
        when(invitationsEnrollmentInvitationDao.getByEmailAndEnrollementIdAndState(email, InvitationStateConstants.LINK))
                .thenReturn(Collections.emptyList());
        when(invitationsEnrollmentInvitationDao.getLinkInvitationByApplyUserIds(anyString()))
                .thenReturn(Collections.emptyList());
        doNothing().when(invitationEnrollmentinvitationDao).addInvitationsEnrollmentInvitation(any());
        doNothing().when(studentDao).saveStudentParent(any());
        when(enrollmentService.getByEnrollmentId(anyString()))
                .thenReturn(new EnrollmentDTO());
        when(invitationsEnrollmentInvitationDao.getByEmail(user.getEmail()))
                .thenReturn(Collections.emptyList());
        // 调用测试方法
        parentService.setInvitationData(invitationParentResponse, createUserId, email, enrollmentId, type, verifyNum);
        // 验证
        verify(invitationsEnrollmentInvitationDao, times(1)).addInvitationsEnrollmentInvitation(any(InvitationsEnrollmentInvitationEntity.class)); // 创建了邀请信息
        verify(invitationEnrollmentinvitationDao, times(1)).addInvitationsEnrollmentInvitation(any(InvitationEnrollmentinvitationEntity.class)); // 创建了邀请关系
        verify(studentDao, times(1)).saveStudentParent(any(UserEnrollmentEntity.class)); // 创建了家长孩子关系
        verify(invitationsEnrollmentInvitationDao, times(1)).updateInvitationsEnrollmentInvitation(any(InvitationsEnrollmentInvitationEntity.class)); // 更新了邀请信息
    }

    /**
     * 测试设置邀请信息方法
     * case: 家长账号不存在的情况，处理类型是去邀请（0）
     */
    @Test
    public void testSetInvitationDataCaseParentNotExist() {
        // 准备方法入参
        InvitationParentResponse invitationParentResponse = new InvitationParentResponse();
        String createUserId = "createUserId001";
        String email = "email002";
        String enrollmentId = "enrollmentId001";
        String type = "0";
        int verifyNum = 0;
        // 数据模拟
        UserEntity userEntity = new UserEntity();
        userEntity.setId("userId001");
        UserModel user = new UserModel();
        user.setId(userEntity.getId());
        user.setEmail("email002");
        when(invitationsEnrollmentInvitationDao.getDatasByEmailAndEnrollementId(email, enrollmentId, InvitationStateConstants.NOINVITATION))
                .thenReturn(null);
        when(invitationsEnrollmentInvitationDao.getDatasByEmailAndEnrollementId(email, enrollmentId, InvitationStateConstants.NOLINK))
                .thenReturn(null);
        when(invitationsEnrollmentInvitationDao.getDatasByEmailAndEnrollementId(email, enrollmentId, InvitationStateConstants.LINK))
                .thenReturn(null);
        when(userRepository.getTop1ByEmailAndIsDeletedFalse(email)).thenReturn(null);
        when(accountService.initParent(anyString(), anyString(), anyString())).thenReturn(userEntity);
        when(userRepository.getTop1ByEmailAndIsDeletedFalse(email)).thenReturn(null);
        when(invitationsEnrollmentInvitationDao.getByEmailAndEnrollementIdAndState(email, InvitationStateConstants.NOLINK))
                .thenReturn(Collections.emptyList());
        when(invitationsEnrollmentInvitationDao.getByEmailAndEnrollementIdAndState(email, InvitationStateConstants.LINK))
                .thenReturn(Collections.emptyList());
        doNothing().when(invitationEnrollmentinvitationDao).addInvitationsEnrollmentInvitation(any());
        doNothing().when(studentDao).saveStudentParent(any());
        when(enrollmentService.getByEnrollmentId(anyString()))
                .thenReturn(new EnrollmentDTO());
        when(invitationsEnrollmentInvitationDao.getByEmail(user.getEmail()))
                .thenReturn(Collections.emptyList());
        // 调用测试方法
        parentService.setInvitationData(invitationParentResponse, createUserId, email, enrollmentId, type, verifyNum);
        // 验证
        verify(invitationsEnrollmentInvitationDao, times(1)).addInvitationsEnrollmentInvitation(any(InvitationsEnrollmentInvitationEntity.class)); // 创建了邀请信息
        verify(invitationEnrollmentinvitationDao, times(1)).addInvitationsEnrollmentInvitation(any(InvitationEnrollmentinvitationEntity.class)); // 创建了邀请关系
        verify(studentDao, times(1)).saveStudentParent(any(UserEnrollmentEntity.class)); // 创建了家长孩子关系
    }

    /**
     * 测试设置邀请信息方法
     * case: 家长账户已存在，家长和该小孩存在待邀请的关系，有待连接的邀请关系，处理类型是去邀请（0）
     */
    @Test
    public void testSetInvitationDataCaseParentNoInvitationAndHaveOtherInvitation() {
        // 准备方法入参
        InvitationParentResponse invitationParentResponse = new InvitationParentResponse();
        String createUserId = "createUserId001";
        String email = "email002";
        String enrollmentId = "enrollmentId001";
        String type = "0";
        int verifyNum = 0;
        // 数据模拟
        UserEntity userEntity = new UserEntity();
        userEntity.setId("userId001");
        UserModel user = new UserModel();
        user.setId(userEntity.getId());
        user.setEmail("email002");
        String token = "token001";
        InvitationsEnrollmentInvitationEntity noInvitation = new InvitationsEnrollmentInvitationEntity();
        noInvitation.setId("noInvitation001");
        when(invitationsEnrollmentInvitationDao.getDatasByEmailAndEnrollementId(email, enrollmentId, InvitationStateConstants.NOINVITATION))
                .thenReturn(noInvitation);
        when(invitationsEnrollmentInvitationDao.getDatasByEmailAndEnrollementId(email, enrollmentId, InvitationStateConstants.NOLINK))
                .thenReturn(null);
        when(invitationsEnrollmentInvitationDao.getDatasByEmailAndEnrollementId(email, enrollmentId, InvitationStateConstants.LINK))
                .thenReturn(null);
        when(userRepository.getTop1ByEmailAndIsDeletedFalse(email)).thenReturn(userEntity);
        when(loginLogDao.getLastLoginTokenByUserId(user.getId())).thenReturn(token);

        when(userRepository.updatePassword(anyString(), anyString())).thenReturn(1);
        when(dotNetPasswordEncoder.encode(anyString())).thenReturn("password");
        doNothing().when(invitationsEnrollmentInvitationDao).updateInvitationsEnrollmentInvitation(any(InvitationsEnrollmentInvitationEntity.class));

        when(invitationsEnrollmentInvitationDao.getByEmailAndEnrollementIdAndState(email, InvitationStateConstants.NOLINK))
                .thenReturn(null);
        when(invitationsEnrollmentInvitationDao.getByEmailAndEnrollementIdAndState(email, InvitationStateConstants.LINK))
                .thenReturn(Collections.emptyList());
        when(invitationsEnrollmentInvitationDao.getLinkInvitationByApplyUserIds(anyString()))
                .thenReturn(Collections.emptyList());
        doNothing().when(studentDao).saveStudentParent(any());
        when(enrollmentService.getByEnrollmentId(anyString()))
                .thenReturn(new EnrollmentDTO());
        when(invitationsEnrollmentInvitationDao.getByEmail(user.getEmail()))
                .thenReturn(Collections.emptyList());
        // 调用测试方法
        parentService.setInvitationData(invitationParentResponse, createUserId, email, enrollmentId, type, verifyNum);
        // 验证
        verify(studentDao, times(1)).saveStudentParent(any(UserEnrollmentEntity.class)); // 创建了家长孩子关系
        verify(invitationsEnrollmentInvitationDao, times(1)).updateInvitationsEnrollmentInvitation(any(InvitationsEnrollmentInvitationEntity.class)); // 更新了邀请信息
    }

    @Test
    public void testExportChildParentInvitations() {
        // 创建一个 InvitationParentResponse 对象
        InvitationParentResponse invitationParentResponse = new InvitationParentResponse();
        // 创建一个 InvitationParentRequest 对象，并设置其 groupId 属性
        InvitationParentRequest request = new InvitationParentRequest();
        request.setGroupId("testGroupId");
        // 创建一个 EnrollmentDTO 对象的列表，并添加一个新的 EnrollmentDTO 对象
        List<EnrollmentDTO> baseEnrollmentDTOS = new ArrayList<>();
        EnrollmentDTO enrollmentDTO = new EnrollmentDTO();
        enrollmentDTO.setEnrollmentId("testEnrollmentId");
        baseEnrollmentDTOS.add(enrollmentDTO);
        // 设置语言为英语
        String languages = "en";

        // 创建一个 GroupEntity 对象，并设置其 center 属性
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setCenter(new CenterEntity());
        // 创建一个 CenterEntity 对象，并设置其 id 和 name 属性
        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setId("testCenterId");
        centerEntity.setName("testCenterName");

        // 当调用 groupRepository 的 findOne 方法时，返回上面创建的 GroupEntity 对象
        when(groupRepository.findById(anyString())).thenReturn(Optional.of(groupEntity));
        // 当调用 centerRepository 的 findOne 方法时，返回上面创建的 CenterEntity 对象
        when(centerRepository.findById(any())).thenReturn(Optional.of(centerEntity));
        // 当调用 userProvider 的 getCurrentUserId 方法时，返回 "testUserId"
        when(userProvider.getCurrentUserId()).thenReturn("testUserId");
        // 当调用 fileSystem 的 upload 方法时，返回空字符串
        when(fileSystem.upload(anyString(), anyString(), any(File.class))).thenReturn("");
        // 当调用 fileSystem 的 getPublicUrl 方法时，返回 "testPublicUrl"
        when(fileSystem.getPublicUrl(anyString(), anyString())).thenReturn("testPublicUrl");
        // 当调用 remoteProvider 的 callPdfService 方法时，返回一个状态码为 200 的 InvokeResult
        when(remoteProvider.callPdfService(anyString(), anyList())).thenReturn(CompletableFuture.completedFuture(new InvokeResult().withStatusCode(200)));
        // 创建一个 PdfConvertJobEntity 对象，并设置其属性
        PdfConvertJobEntity pdfJob = new PdfConvertJobEntity();
        pdfJob.setId("pdfJobId001");
        pdfJob.setPdfName("test.pdf");
        pdfJob.setUrl("https://s3.amazonaws.com/com.learning-genie.prod.pdf/test.html");
        pdfJob.setPdfUrl("https://s3.amazonaws.com/com.learning-genie.prod.pdf/test.pdf");
        pdfJob.setStatus("SUCCEED");
        pdfJob.setType("IMPORT_PDF");
        pdfJob.setPdfPath("test.pdf");
        pdfJob.setBucket("com.learning-genie.prod.pdf");
        // 当调用 reportDao 的 getPdfJob 方法时，返回上面创建的 PdfConvertJobEntity 对象
        when(reportDao.getPdfJob(anyString())).thenReturn(pdfJob);
        // 使用反射设置 parentService 的字段值
        ReflectionTestUtils.setField(parentService, "webServer", "webServer");
        ReflectionTestUtils.setField(parentService, "pdfBucket", "pdfBucket");
        ReflectionTestUtils.setField(parentService, "s3Root", "s3Root");
        ReflectionTestUtils.setField(parentService, "pdfEndpoint", "pdfEndpoint");

        // 调用 exportChildParentInvitions 方法
        DownFileResponse result = parentService.exportChildParentInvitions(invitationParentResponse, request, baseEnrollmentDTOS, languages);

        // 断言结果不为空
        assertNotNull(result);
        // 断言返回的 URL 为 "testPdfUrl"
        assertEquals("https://s3.amazonaws.com/com.learning-genie.prod.pdf/test.pdf", result.getUrl());
    }

    /**
     * 测试有 Site Admin 的情况
     */
    @Test
    public void testSendEmail_WithSiteAdmin() {
        String parentEmail = "<EMAIL>";
        String enrollmentId = "enroll123";
        String type = "0";
        String adminEmail = "<EMAIL>";
        boolean isCC = false;

        //创建小孩对象
        EnrollmentDTO enrollmentDTO = new EnrollmentDTO();
        enrollmentDTO.setEnrollmentId(enrollmentId);
        enrollmentDTO.setEnrollmentName("John Doe");
        enrollmentDTO.setClassName("Class A");
        enrollmentDTO.setHeadImage("image/path");
        enrollmentDTO.setInitialPwd("initialPwd");

        //创建一个 CenterEntity 对象
        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setName("Center Name");
        // 创建一个园长
        UserEntity siteAdmin = new UserEntity();
        siteAdmin.setEmail("<EMAIL>");

        //模拟对象行为
        when(enrollmentService.getByEnrollmentId(enrollmentId)).thenReturn(enrollmentDTO);
        when(centerRepository.findById(enrollmentDTO.getCenterId())).thenReturn(Optional.of(centerEntity));
        when(invitationsEnrollmentInvitationDao.getByEmail(parentEmail)).thenReturn(Collections.singletonList(new InvitationsEnrollmentInvitationEntity()));
        when(userDao.getSiteAdminByChild(enrollmentId)).thenReturn(Collections.singletonList(siteAdmin));
        ReflectionTestUtils.setField(parentService, "emailTemplateVersion", "v1");

        //执行方法
        parentService.sendEmail(parentEmail, enrollmentId, type, adminEmail, isCC);

        //验证
        verify(mandrillService).sendAsync(any(EmailModel.class));
    }

    /**
     * 测试有老师的情况
     */
    @Test
    public void testSendEmail_WithoutSiteAdmin_WithTeacher() {
        String parentEmail = "<EMAIL>";
        String enrollmentId = "enroll123";
        String type = "0";
        String adminEmail = "<EMAIL>";
        boolean isCC = false;

        //创建小孩对象
        EnrollmentDTO enrollmentDTO = new EnrollmentDTO();
        enrollmentDTO.setEnrollmentId(enrollmentId);
        enrollmentDTO.setEnrollmentName("John Doe");
        enrollmentDTO.setClassName("Class A");
        enrollmentDTO.setHeadImage("image/path");
        enrollmentDTO.setInitialPwd("initialPwd");

        //创建一个 CenterEntity 对象
        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setName("Center Name");

        // 创建一个班级对象
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId("Group Id");

        //创建一个老师
        UserModel teacher = new UserModel();
        teacher.setEmail("Teacher Email");

        //模拟对象行为
        when(enrollmentService.getByEnrollmentId(enrollmentId)).thenReturn(enrollmentDTO);
        when(centerRepository.findById(enrollmentDTO.getCenterId())).thenReturn(Optional.of(centerEntity));
        when(invitationsEnrollmentInvitationDao.getByEmail(parentEmail)).thenReturn(Collections.singletonList(new InvitationsEnrollmentInvitationEntity()));
        when(userDao.getSiteAdminByChild(enrollmentId)).thenReturn(Collections.emptyList());
        when(groupRepository.findById(enrollmentDTO.getGroupId())).thenReturn(Optional.of(groupEntity));
        when(userDao.getTeachers(groupEntity.getId())).thenReturn(Collections.singletonList(teacher));
        ReflectionTestUtils.setField(parentService, "emailTemplateVersion", "v1");

        //执行方法
        parentService.sendEmail(parentEmail, enrollmentId, type, adminEmail, isCC);

        //验证
        verify(mandrillService).sendAsync(any(EmailModel.class));
    }

    /**
     * 测试无老师无园长的情况
     */
    @Test
    public void testSendEmail_WithoutSiteAdmin_WithoutTeacher_WithAgencyOwner() {
        String parentEmail = "<EMAIL>";
        String enrollmentId = "enroll123";
        String type = "0";
        String adminEmail = "<EMAIL>";
        boolean isCC = false;

        //创建小孩对象
        EnrollmentDTO enrollmentDTO = new EnrollmentDTO();
        enrollmentDTO.setEnrollmentId(enrollmentId);
        enrollmentDTO.setEnrollmentName("John Doe");
        enrollmentDTO.setClassName("Class A");
        enrollmentDTO.setHeadImage("image/path");
        enrollmentDTO.setInitialPwd("initialPwd");

        //创建一个 CenterEntity 对象
        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setName("Center Name");

        // 创建一个班级对象
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId("Group Id");

        //创建一个机构对象
        AgencyEntity agency = new AgencyEntity();
        agency.setId("Agency Id");

        //创建一个 Owner 对象
        UserModel agencyOwner = new UserModel();
        agencyOwner.setEmail("Agency Owner Email");

        //模拟对象行为
        when(enrollmentService.getByEnrollmentId(enrollmentId)).thenReturn(enrollmentDTO);
        when(centerRepository.findById(enrollmentDTO.getCenterId())).thenReturn(Optional.of(centerEntity));
        when(invitationsEnrollmentInvitationDao.getByEmail(parentEmail)).thenReturn(Collections.singletonList(new InvitationsEnrollmentInvitationEntity()));
        when(userDao.getSiteAdminByChild(enrollmentId)).thenReturn(Collections.emptyList());
        when(groupRepository.findById(enrollmentDTO.getGroupId())).thenReturn(Optional.of(groupEntity));
        when(userDao.getTeachers(groupEntity.getId())).thenReturn(Collections.emptyList());
        when(agencyDao.getAgencyByChildId(enrollmentId)).thenReturn(agency);
        when(userDao.getAgencyOwnerByAgencyId(agency.getId())).thenReturn(Collections.singletonList(agencyOwner));
        ReflectionTestUtils.setField(parentService, "emailTemplateVersion", "v1");

        //执行方法
        parentService.sendEmail(parentEmail, enrollmentId, type, adminEmail, isCC);

        //验证
        verify(mandrillService).sendAsync(any(EmailModel.class));
    }


}

package com.learninggenie.api.service.impl;

import com.learninggenie.api.model.SuccessResponse;
import com.learninggenie.api.model.dll.*;
import com.learninggenie.api.provider.RemoteProvider;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.security.AuthUserDetails;
import com.learninggenie.common.comm.CommService;
import com.learninggenie.common.data.dao.DLLDao;
import com.learninggenie.common.data.dao.GroupDao;
import com.learninggenie.common.data.dao.PushNotificationDao;
import com.learninggenie.common.data.dao.StudentDao;
import com.learninggenie.common.data.dao.dll.SubjectDao;
import com.learninggenie.common.data.dao.dll.SubjectLanguageDao;
import com.learninggenie.common.data.dao.dll.SubjectMediaDao;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.entity.EnrollmentEntity;
import com.learninggenie.common.data.entity.UserEntity;
import com.learninggenie.common.data.entity.dll.DLLResourceEntity;
import com.learninggenie.common.data.entity.dll.SubjectEntity;
import com.learninggenie.common.data.entity.dll.SubjectLanguageEntity;
import com.learninggenie.common.data.entity.dll.SubjectMediaEntity;
import com.learninggenie.common.data.enums.UserRole;
import com.learninggenie.common.data.model.*;
import com.learninggenie.common.data.model.dll.*;
import com.learninggenie.common.data.model.push.DeviceEntity;
import com.learninggenie.common.filesystem.FileSystem;
import com.learninggenie.common.utils.StringUtil;
import com.learninggenie.common.utils.TimeUtil;
import org.jetbrains.annotations.NotNull;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class DLLServiceImplTest {
    @InjectMocks
    private DLLServiceImpl dllService;
    @Mock
    private DLLDao dllDao;
    @Mock
    UserProvider userProvider;
    @Mock
    FileSystem fileSystem;
    @Mock
    private UserDaoImpl userDao;
    @Mock
    private GroupDao groupDao;

    @Mock
    private StudentDao studentDao;

    @Mock
    private RemoteProvider remoteProvider;

    @Mock
    private PushNotificationDao pushNotificationDao;

    @Mock
    private CommService commService;

    @Mock
    private SubjectDao subjectDao;

    @Mock
    private SubjectMediaDao subjectMediaDao;

    @Mock
    private SubjectLanguageDao subjectLanguageDao;

    @Test
    void getResourceTheme() {
        List<DLLResourceThemeEntity> resourceThemeEntities = new ArrayList<>();
        DLLResourceThemeEntity resourceThemeEntity = new DLLResourceThemeEntity();
        resourceThemeEntity.setAgencyId("A001");
        resourceThemeEntity.setType("COMMON");
        resourceThemeEntities.add(resourceThemeEntity);

        DLLResourceThemeEntity resourceThemeEntity1 = new DLLResourceThemeEntity();
        resourceThemeEntity1.setAgencyId("A001");
        resourceThemeEntity1.setType("CUSTOM");
        resourceThemeEntities.add(resourceThemeEntity1);

        String agencyId = "A001";
        String userId = "U001";
        // 机构信息
        AgencyModel agency = new AgencyModel();
        agency.setId(agencyId);
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agency);
//        when(userProvider.getAgencyByUserId(userId)).thenReturn(null);
        when(dllDao.getResourceTheme(agencyId)).thenReturn(resourceThemeEntities);
        assertEquals(2, resourceThemeEntities.size());

        dllService.getResourceTheme(userId);
//        assertEquals(2, resourceThemeEntities.size());

    }

    @Test
    void getResource() {

        List<DLLResourceEntity> resourceEntities = new ArrayList<>();
        DLLResourceEntity resourceEntity = new DLLResourceEntity();
        resourceEntity.setAgencyId("A001");
        resourceEntity.setThemeId("T001");
        resourceEntity.setRelativePath("M001");
        resourceEntities.add(resourceEntity);


        String agencyId = "A001";
        String themeId = "T001";
        when(dllDao.getResourceByThemeId(anyString(), anyString())).thenReturn(resourceEntities);

        DLLResourceThemeEntity resourceThemeEntity = new DLLResourceThemeEntity();
//        resourceThemeEntity.setAgencyId("A002");
//        resourceThemeEntity.setId("T001");
        resourceThemeEntity.setTitle("TITLE");
        when(dllDao.getResourceThemeById(anyString())).thenReturn(resourceThemeEntity);

//        DLLResourceThemeEntity themeEntity = dllDao.getResourceThemeById(themeId);

        dllService.getResource(agencyId, themeId);
    }

    @Test
    void createResourceTheme() {
        DLLResourceThemeRequest request = new DLLResourceThemeRequest();
        request.setThemeName("NAME");

        // 机构信息
        String agencyId = "A001";
        AgencyModel agency = new AgencyModel();
        agency.setId(agencyId);
        when(userProvider.getCurrentUserId()).thenReturn("U001");
        when(userProvider.getAgencyByUserId("U001")).thenReturn(agency);
//        String agencyId = userProvider.getAgencyByUserId(userId).getId();

        // 用户 ID
        String userId = "U001";

        // 将参数转换成数据库 model
        DLLResourceThemeEntity themeEntity = new DLLResourceThemeEntity();
        themeEntity.setAgencyId(agencyId); // 机构 ID
        themeEntity.setId(UUID.randomUUID().toString()); // 主题 ID
        themeEntity.setDeleted(false); // 删除状态
        themeEntity.setCreateUserId(userId); // 创建人 ID
        themeEntity.setType("CUSTOM"); // 类型自定义
        themeEntity.setTitle(request.getThemeName()); // 主题名
        themeEntity.setCreateAtUtc(new Date());
        themeEntity.setUpdateAtUtc(new Date());
//
        SuccessResponse response = dllService.createResourceTheme(request);
        Mockito.verify(dllDao, times(1)).createResourceTheme(any());
        assertEquals(true, response.isSuccess());
    }

    @Test
    void editResourceTheme() {
        DLLResourceThemeRequest request = new DLLResourceThemeRequest();
        request.setThemeName("NAME");
        request.setId("T001");

        DLLResourceThemeEntity themeEntity = new DLLResourceThemeEntity();
        // 用户 ID
//        String userId = "U001";
        // 获取时间
        Date utcNow = TimeUtil.getUtcNow();
        themeEntity.setUpdateAtUtc(utcNow); // 设置更新时间
        themeEntity.setTitle("UPDATE"); // 设置主题名
        themeEntity.setId("T001"); // 设置主题 ID
        SuccessResponse response = dllService.editResourceTheme(request);
        Mockito.verify(dllDao, times(1)).editResourceTheme(any());
        assertEquals(true, response.isSuccess());
    }

    @Test
    void deleteResourceTheme() {
        String themeId = "T001";
        dllService.deleteResourceTheme(themeId);
        Mockito.verify(dllDao, times(1)).deleteResourceTheme(themeId);
        SuccessResponse response = dllService.deleteResourceTheme(themeId);
        assertEquals(true, response.isSuccess());
    }

    @Test
    void createResource() {
        DLLResourceRequest request = new DLLResourceRequest();
        request.setId("S001");
        request.setThemeId("T001");
        request.setContent("CONTENT");
        request.setMediaId("M001");

        String agencyId = "A001";
        AgencyModel agency = new AgencyModel();
        agency.setId(agencyId);
        when(userProvider.getCurrentUserId()).thenReturn("U001");
        when(userProvider.getAgencyByUserId("U001")).thenReturn(agency);

        SuccessResponse response = dllService.createResource(request);
        Mockito.verify(dllDao, times(1)).createResource(any());
        assertEquals(true, response.isSuccess());
    }

    @Test
    void editResource() {
        DLLResourceRequest request = new DLLResourceRequest();
        request.setId("S001");
        request.setThemeId("T001");
        request.setContent("CONTENT");
        request.setMediaId("M002");

        SuccessResponse response = dllService.editResource(request);
        Mockito.verify(dllDao, times(1)).editResource(any());
        assertEquals(true, response.isSuccess());
    }

    @Test
    void deleteResource() {
        String resourceId = "R001";
        dllService.deleteResource(resourceId);
        Mockito.verify(dllDao, times(1)).deleteResource(resourceId);
        SuccessResponse response = dllService.deleteResource(resourceId);
        assertEquals(true, response.isSuccess());
    }

    /**
     * 测试管理员/员工获取未读回复
     * case: 测试管理员/员工获取未读回复
     */
    @Test
    public void testGetUnreadReply() {
        // 准备数据
//        String role = "COLLABORATOR";
        String userId = "U001";
        // 用户信息
        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole(UserRole.COLLABORATOR.toString());
        // 机构信息班级信息
        List<String> teacherGroupIds = new ArrayList<>();
        teacherGroupIds.add("group_3");
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.checkUser(userId)).thenReturn(userEntity);
        when(groupDao.getGroupIdsByTeacherId(anyString())).thenReturn(teacherGroupIds);
        when(dllDao.getUnreadReplyByGroupIds(teacherGroupIds)).thenReturn(new ArrayList<>());
        DLLReplyUnreadResponse response = dllService.getUnreadReplyList("");
        assertEquals(0, response.getDllUnreadGroupIds().size());

        String adminUserId = "U002";
        // 用户信息
        UserEntity adminUserEntity = new UserEntity();
        adminUserEntity.setId(userId);
        // 机构信息班级信息
        List<String> siteAdminGroupIds = new ArrayList<>();
        siteAdminGroupIds.add("group_2");
        siteAdminGroupIds.add("group_3");

        // 未读回复信息
        List< DLLReplyModel > unreadReplyList = new ArrayList<>();
        DLLReplyModel replyModel = new DLLReplyModel();
        replyModel.setGroupId("group_1");
        unreadReplyList.add(replyModel);

        adminUserEntity.setRole(UserRole.SITE_ADMIN.toString());
        when(userProvider.getCurrentUserId()).thenReturn(adminUserId);
        when(userProvider.checkUser(adminUserId)).thenReturn(adminUserEntity);
        when(groupDao.getGroupIdsBySiteAdminId(anyString())).thenReturn(siteAdminGroupIds);
        when(dllDao.getUnreadReplyByGroupIds(siteAdminGroupIds)).thenReturn(unreadReplyList);
        DLLReplyUnreadResponse adminResponse = dllService.getUnreadReplyList("");
        assertEquals(1, adminResponse.getDllUnreadGroupIds().size());

        String agencyAdminUserId = "U003";
        // 用户信息
        UserEntity agencyAdminUserEntity = new UserEntity();
        agencyAdminUserEntity.setId(userId);
        // 机构信息班级信息
        List<String> agencyAdminGroupIds = new ArrayList<>();
        agencyAdminGroupIds.add("group_1");
        agencyAdminGroupIds.add("group_2");
        agencyAdminGroupIds.add("group_3");

        DLLReplyModel replyModel1 = new DLLReplyModel();
        replyModel1.setGroupId("group_2");
        unreadReplyList.add(replyModel1);

        agencyAdminUserEntity.setRole(UserRole.AGENCY_ADMIN.toString());
        when(userProvider.getCurrentUserId()).thenReturn(agencyAdminUserId);
        when(userProvider.checkUser(agencyAdminUserId)).thenReturn(agencyAdminUserEntity);
        when(groupDao.getGroupIdsByAgencyAdminId(anyString())).thenReturn(agencyAdminGroupIds);
        when(dllDao.getUnreadReplyByGroupIds(agencyAdminGroupIds)).thenReturn(unreadReplyList);
        DLLReplyUnreadResponse agencyAdminResponse = dllService.getUnreadReplyList("");
        assertEquals(2, agencyAdminResponse.getDllUnreadGroupIds().size());

    }

    /**
     * 测试获取老师布置作业详情.
     * case: 获取老师布置作业详情.
     */
    @Test
    public void testGetTeacherDetailStatistics() {
        // 准备数据
        String role = "COLLABORATOR";
        String userId = "U001";
        // 用户信息
        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole(role);
        List<String> centerIds = new ArrayList<>();
        centerIds.add("center_1");
        centerIds.add("center_2");
        centerIds.add("center_3");
        List<String> selectedCenterIds = new ArrayList<>();
        selectedCenterIds.add("selected_center_1");
        selectedCenterIds.add("selected_center_2");
        List<String> groupIds = new ArrayList<>();
        groupIds.add("group_1");
        groupIds.add("group_2");
        // 机构数据
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("AGENCY_ID");
        // 请求参数
        EngagementDashboardRequest request = new EngagementDashboardRequest();
        request.setFromDate("2023-07-21");
        request.setToDate("2023-07-27");
        request.setAgencyId("test-agencyId");
        request.setCenterIds(centerIds);
        request.setSelectAllGroupByCenterIds(selectedCenterIds);
        request.setGroupId("test-groupId");
        request.setCenterIds(groupIds);
        lenient().when(userProvider.getCurrentUserId()).thenReturn(userId);
        lenient().when(userProvider.checkUser(userId)).thenReturn(userEntity);
        lenient().when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        lenient().when(userDao.getStaffsByGroupIds(groupIds)).thenReturn(new ArrayList<UserModel>());
        DLLTeacherDashboardResponse response = dllService.getTeacherDetailStatistics(request);
        Assert.assertEquals(true, response != null);
    }

    /**
     * 验证创建家庭作业
     * case: 不仅分享给DLL小孩子
     */
    @Test
    public void createHomeworkNotOnlyShareDLLChild() {
        DLLLanguageRequest request = this.commonRequest(false, "");

        // 调用接口
        SuccessResponse response = dllService.createHomework(request);
        assertTrue(response != null);
    }

    /**
     * 验证创建家庭作业
     * case: 不仅分享给DLL小孩子
     */
    @Test
    public void createHomeworkOnlyShareDLLChild() {
        DLLLanguageRequest request = this.commonRequest(true, "");

        // 调用接口
        SuccessResponse response = dllService.createHomework(request);
        assertTrue(response != null);
    }

    /**
     * 验证编辑家庭作业
     */
    @Test
    public void editHomework() {
        DLLLanguageRequest request = this.commonRequest(false, TimeUtil.format(new Date(), TimeUtil.format2));
        // 调用接口
        SuccessResponse response = dllService.editHomework(request);
        assertTrue(response != null);
    }

    /**
     * 验证设置 DLL 主题信息
     */
    @Test
    public void testSetSubjects() {
        // 数据准备
        SetSubjectsRequest request = new SetSubjectsRequest();
        request.setSourceId("test-sourceId001");
        request.setSourceType("DLL_LIBRARY");
        List<DLLSetSubjectModel> subjects = new ArrayList<>();
        DLLSetSubjectModel subject = new DLLSetSubjectModel();
        subject.setTitle("title-subject");
        subject.setContent("content-subject");
        subject.setId("test-subjectId001");
        List<String> mediaIds = new ArrayList<>();
        mediaIds.add("mediaId-1");
        mediaIds.add("mediaId-2");
        subject.setMediaIds(mediaIds);
        subject.setDescription("test-description");
        subject.setSourceId("test-sourceId001");
        List<DLLSubjectLanguageModel> languages = new ArrayList<>();
        DLLSubjectLanguageModel language = new DLLSubjectLanguageModel();
        language.setLangCode("en");
        language.setContent("content-language1");
        DLLSubjectLanguageModel language2 = new DLLSubjectLanguageModel();
        language2.setLangCode("zh");
        language2.setContent("content-language2");
        languages.add(language);
        languages.add(language2);
        subject.setLanguages(languages);
        DLLSetSubjectModel newSubject = new DLLSetSubjectModel();
        newSubject.setTitle("title-newSubject");
        newSubject.setContent("content-newSubject");
        newSubject.setDescription("test-description");
        newSubject.setSourceId("test-sourceId001");
        subjects.add(subject);
        subjects.add(newSubject);
        request.setSubjects(subjects);
        // 用户信息
        String userId = "U001";
        String agencyId = "test-agencyId";
        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setUsername(userId);
        // DLL 数据
        List<SubjectEntity> subjectEntities = new ArrayList<>();
        SubjectEntity subjectEntity = new SubjectEntity();
        subjectEntity.setId("test-subjectId001");
        subjectEntity.setContent("content-subject3");
        subjectEntity.setAgencyId(agencyId);
        subjectEntity.setDescription("test-description");
        subjectEntity.setLanguage("en");
        subjectEntity.setSourceId("test-sourceId001");
        subjectEntity.setSortIndex(0);
        subjectEntity.setSourceType("DLL_LIBRARY");
        SubjectEntity subjectEntity2 = new SubjectEntity();
        subjectEntity2.setId("test-subjectId002");
        subjectEntity2.setContent("content-subject4");
        subjectEntity2.setAgencyId(agencyId);
        subjectEntity2.setDescription("test-description");
        subjectEntity2.setLanguage("zh");
        subjectEntity2.setSourceId("test-sourceId001");
        subjectEntity2.setSortIndex(0);
        subjectEntity2.setSourceType("DLL_LIBRARY");
        subjectEntities.add(subjectEntity);
        subjectEntities.add(subjectEntity2);
        // DLL 语言
        List<SubjectMediaEntity> subjectMediaEntities = new ArrayList<>();
        SubjectMediaEntity subjectMediaEntity = new SubjectMediaEntity();
        subjectMediaEntity.setId("test-subjectMediaId001");
        subjectMediaEntity.setSubjectId("test-subjectId001");
        subjectMediaEntity.setMediaId("test-mediaId001");
        subjectMediaEntities.add(subjectMediaEntity);
        List<SubjectLanguageEntity> subjectLanguageEntities = new ArrayList<>();
        SubjectLanguageEntity subjectLanguageEntity = new SubjectLanguageEntity();
        subjectLanguageEntity.setId("test-subjectLanguageId001");
        subjectLanguageEntity.setSubjectId("test-subjectId001");
        subjectLanguageEntity.setLangCode("en");
        subjectLanguageEntity.setContent("content-language");
        subjectLanguageEntity.setAgencyId(agencyId);
        subjectLanguageEntities.add(subjectLanguageEntity);
        // 模拟请求
        when(userProvider.getCurrentUser()).thenReturn(currentUser);
        when(userProvider.getCurrentLang()).thenReturn("en");
        when(subjectDao.listBySourceId(request.getSourceId())).thenReturn(subjectEntities);
        when(subjectMediaDao.listBySubjectIdIn(subjectEntities.stream().map(SubjectEntity::getId).collect(Collectors.toList()))).thenReturn(subjectMediaEntities);
        when(subjectLanguageDao.listBySubjectIdIn(subjectEntities.stream().map(SubjectEntity::getId).collect(Collectors.toList()))).thenReturn(subjectLanguageEntities);

        // 调用接口
        boolean response = dllService.setSubjects(request);
        assertFalse(response);
    }

    @Test
    public void testGetHomeworkList() {
        // 准备数据
        String groupId = "test-groupId001";
        String fromDate = "2024-06-12 16:00:00.000";
        String toDate = "2024-06-20 15:59:59.000";
        String userId = "U001";
        List<DLLHomeworkModel> homeworkModels = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            DLLHomeworkModel homeworkModel = new DLLHomeworkModel();
            homeworkModel.setHomeworkId("test-homeworkId00" + i);
            homeworkModel.setTitle("title-homework");
            homeworkModel.setPublishDate(TimeUtil.parseDate("2024-06-17"));
            homeworkModel.setPublishStatus("PUBLIC");
            homeworkModel.setLangCount(1);
            homeworkModel.setLang("en");
            homeworkModel.setShareParent(true);
            homeworkModel.setContent("content-homework");
            homeworkModel.setDescription("test-description");
            homeworkModel.setSource("DLL_LIBRARY");
            homeworkModel.setGroupId(groupId);
            homeworkModel.setDeleted(false);
            homeworkModel.setAgencyId("test-agencyId");
            homeworkModels.add(homeworkModel);
        }
        List<DLLHomeworkContentModel> contentModels = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            DLLHomeworkContentModel contentModel = new DLLHomeworkContentModel();
            contentModel.setHomeworkId("test-homeworkId00" + i);
            contentModel.setLangCode("en");
            contentModel.setContent("content-homework");
            contentModel.setSortIndex(i);
            contentModel.setAgencyId("test-agencyId");
            contentModels.add(contentModel);
        }

        List<DLLLanguageModel> languageModels = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            DLLLanguageModel languageModel = new DLLLanguageModel();
            languageModel.setCode("en");
            languageModel.setName("English");
            languageModel.setOriginalName("English");
            languageModel.setTtsCode("en-US");
            languageModel.setTtsVoice("en-US-Wavenet-A");
            languageModels.add(languageModel);
        }

        List<DLLHomeworkMediaModel> mediaModels = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            DLLHomeworkMediaModel mediaModel = new DLLHomeworkMediaModel();
            mediaModel.setHomeworkId("test-homeworkId00" + i);
            mediaModel.setMediaId("test-mediaId00" + i);
            mediaModel.setFileName("test-fileName00" + i);
            mediaModel.setFileType("audio");
            mediaModel.setScreenshotUrl("test-screenshotUrl00" + i);
            mediaModels.add(mediaModel);
        }

        List<DLLReplyParentModel> replyModels = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            DLLReplyParentModel replyModel = new DLLReplyParentModel();
            replyModel.setId("test-replyId00" + i);
            replyModel.setHomeworkId("test-homeworkId00" + i);
            replyModel.setContent("content-reply");
            replyModel.setAvatarUrl("test-avatarUrl00" + i);
            replyModel.setCreateUserId(userId);
            replyModel.setChildName("test-childName00" + i);
            replyModel.setRelationShip("test-relationShip00" + i);
            replyModels.add(replyModel);
        }

        List<DLLReplyMediaModel> replyMediaModels = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            DLLReplyMediaModel replyMediaModel = new DLLReplyMediaModel();
            replyMediaModel.setReplyId("test-replyId00" + i);
            replyMediaModel.setMediaId("test-mediaId00" + i);
            replyMediaModel.setFileName("test-fileName00" + i);
            replyMediaModel.setFileType("audio");
            replyMediaModel.setScreenshotUrl("test-screenshotUrl00" + i);
            replyMediaModels.add(replyMediaModel);
        }

        List<DLLHomeworkEditHistoryModel> historyModels = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            DLLHomeworkEditHistoryModel historyModel = new DLLHomeworkEditHistoryModel();
            historyModel.setHomeworkId("test-homeworkId00" + i);
            historyModel.setContent("content-history");
            historyModel.setRole("test-role00" + i);
            historyModel.setCreateAtUtc(TimeUtil.parseDate("2024-06-17"));
            historyModel.setCreateUserId("test-createUserId00" + i);
            historyModels.add(historyModel);
        }

        List<String> homeworkIds = homeworkModels.stream().map(DLLHomeworkModel::getHomeworkId).collect(Collectors.toList());

        String newFromDate = TimeUtil.format(TimeUtil.addHours(TimeUtil.parse(fromDate, TimeUtil.format2), -8), TimeUtil.format2);
        String newToDate = TimeUtil.format(TimeUtil.addHours(TimeUtil.parse(toDate, TimeUtil.format2), -8), TimeUtil.format2);
        // 模拟请求
        lenient().when(userProvider.getTimezoneOffsetNum()).thenReturn(8);
        lenient().when(dllDao.getHomeworkListByGroup(groupId, newFromDate, newToDate)).thenReturn(homeworkModels);
        lenient().when(dllDao.getHomeworkContentByHomeworkIds(homeworkIds)).thenReturn(contentModels);
        lenient().when(dllDao.getLanguageModel()).thenReturn(languageModels);
        lenient().when(fileSystem.getPublicUrl(anyString())).thenReturn("url");
        lenient().when(dllDao.getHomeworkMedia(homeworkIds)).thenReturn(mediaModels);
        lenient().when(dllDao.getDLLReplyByHomeworkIds(homeworkIds)).thenReturn(replyModels);
        lenient().when(dllDao.getReplyMedia(replyModels.stream().map(DLLReplyParentModel::getId).collect(Collectors.toList()))).thenReturn(replyMediaModels);
        lenient().when(userProvider.getCurrentUserId()).thenReturn(userId);
        lenient().when(userProvider.getCurrentLang()).thenReturn("en");
        lenient().when(dllDao.getDLLHomeworkEditRecord(homeworkIds)).thenReturn(historyModels);

        // 接口调用
        List<DLLHomeworkListResponse> responses = dllService.getHomeworkList(groupId, fromDate, toDate);
        assertFalse(responses.size() == 0);
    }

    @Test
    public void testBatchCreateHomework() {
        // 模拟请求
        DLLBatchCreateRequest requests = new DLLBatchCreateRequest();
        List<DLLLanguageRequest> dllLanguageRequests = new ArrayList<>();
        DLLLanguageRequest commonRequest = commonRequest(true, "");
        dllLanguageRequests.add(commonRequest);
        requests.setDllLanguageRequests(dllLanguageRequests);
        // 调用接口
        SuccessResponse successResponse = dllService.batchCreateHomework(requests);

        // 校验结果
        assertTrue(successResponse.isSuccess());
    }

    @Test
    public void testGetCoachHomeworkList() {
        DLLCoachModel request = new DLLCoachModel();
        List<String> groupIds = new ArrayList<>();
        String groupId = "test-groupId000";
        groupIds.add(groupId);
        request.setGroupIds(groupIds);
        String fromDate = "2024-06-11 16:00:00.000";
        String toDate = "2024-06-20 15:59:59.000";
        request.setFromDate(fromDate);
        request.setEndDate(toDate);
        request.setLangCodes(Collections.singletonList("en"));
        request.setPageNum(1);
        request.setPageSize(10);
        String userId = "test-userId";

        String newFromDate = TimeUtil.format(TimeUtil.addHours(TimeUtil.parse(request.getFromDate(), TimeUtil.format2), -8), TimeUtil.format2);
        String newToDate = TimeUtil.format(TimeUtil.addHours(TimeUtil.parse(request.getEndDate(), TimeUtil.format2), -8), TimeUtil.format2);

        List<DLLHomeworkModel> homeworkModels = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            DLLHomeworkModel homeworkModel = new DLLHomeworkModel();
            homeworkModel.setHomeworkId("test-homeworkId00" + i);
            homeworkModel.setTitle("title-homework");
            homeworkModel.setPublishDate(TimeUtil.parseDate("2024-06-15 16:00:00.000"));
            homeworkModel.setPublishStatus("PUBLIC");
            homeworkModel.setLangCount(1);
            homeworkModel.setLang("en");
            homeworkModel.setShareParent(true);
            homeworkModel.setContent("content-homework");
            homeworkModel.setDescription("test-description");
            homeworkModel.setSource("DLL_LIBRARY");
            homeworkModel.setGroupId(groupId);
            homeworkModel.setDeleted(false);
            homeworkModel.setAgencyId("test-agencyId");
            homeworkModels.add(homeworkModel);
        }
        List<DLLHomeworkContentModel> contentModels = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            DLLHomeworkContentModel contentModel = new DLLHomeworkContentModel();
            contentModel.setHomeworkId("test-homeworkId00" + i);
            contentModel.setLangCode("en");
            contentModel.setContent("content-homework");
            contentModel.setCreateUserId("test-createUserId00" + i);
            contentModel.setCreateAtUtc(TimeUtil.parseDate("2024-06-17"));
            contentModels.add(contentModel);
        }

        List<DLLLanguageModel> languageModels = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            DLLLanguageModel languageModel = new DLLLanguageModel();
            languageModel.setCode("en");
            languageModel.setName("English");
            languageModel.setOriginalName("English");
            languageModel.setTtsCode("en-US");
            languageModel.setTtsVoice("en-US-Wavenet-A");
            languageModels.add(languageModel);
        }

        List<DLLHomeworkMediaModel> mediaModels = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            DLLHomeworkMediaModel mediaModel = new DLLHomeworkMediaModel();
            mediaModel.setHomeworkId("test-homeworkId00" + i);
            mediaModel.setMediaId("test-mediaId00" + i);
            mediaModel.setMediaUrl("url");
            mediaModel.setScreenshotUrl("url");
            mediaModel.setVoiceDuration("10");
            mediaModels.add(mediaModel);
        }

        List<DLLReplyParentModel> replyModels = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            DLLReplyParentModel replyModel = new DLLReplyParentModel();
            replyModel.setHomeworkId("test-homeworkId00" + i);
            replyModel.setId("test-replyId00" + i);
            replyModel.setChildName("test-childName00" + i);
            replyModel.setRelationShip("test-relationShip00" + i);
            replyModel.setAvatarUrl("url");
            replyModel.setCreateUserId(userId);
            replyModel.setCreateAtUtc(TimeUtil.parseDate("2024-06-17"));
            replyModel.setContent("test-content00" + i);
            replyModels.add(replyModel);
        }

        List<DLLReplyMediaModel> replyMediaModels = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            DLLReplyMediaModel replyMediaModel = new DLLReplyMediaModel();
            replyMediaModel.setReplyId("test-replyId00" + i);
            replyMediaModel.setMediaId("test-mediaId00" + i);
            replyMediaModel.setMediaUrl("url");
            replyMediaModel.setScreenshotUrl("url");
            replyMediaModel.setVoiceDuration("10");
            replyMediaModel.setFileType("mp3");
            replyMediaModel.setFileName("test-fileName00" + i);
            replyMediaModels.add(replyMediaModel);
        }

        List<DLLHomeworkEditHistoryModel> historyModels = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            DLLHomeworkEditHistoryModel historyModel = new DLLHomeworkEditHistoryModel();
            historyModel.setHomeworkId("test-homeworkId00" + i);
            historyModel.setId("test-id00" + i);
            historyModel.setContent("test-content00" + i);
            historyModel.setCreateAtUtc(TimeUtil.parseDate("2024-06-17"));
            historyModel.setRole("test-role00" + i);
            historyModel.setFirstName("test-firstName00" + i);
            historyModel.setLastName("test-lastName00" + i);
            historyModel.setCreateUserId("test-createUserId00" + i);
            historyModels.add(historyModel);
        }
        List<String> homeworkIds = homeworkModels.stream().map(DLLHomeworkModel::getHomeworkId).collect(Collectors.toList());
        // 模拟请求
        lenient().when(userProvider.getTimezoneOffsetNum()).thenReturn(8);
        lenient().when(dllDao.getCoachHomeworkListByGroupIds(request.getGroupIds(),
                newFromDate, newToDate, request.getPageSize(), request.getPageNum(), request.getLangCodes())).thenReturn(homeworkModels);
        lenient().when(dllDao.getCoachHomeworkCountByGroupIds(request.getGroupIds(), newFromDate, newToDate, request.getLangCodes())).thenReturn(3);
        lenient().when(dllDao.getHomeworkContentByHomeworkIds(homeworkIds)).thenReturn(contentModels);
        lenient().when(dllDao.getLanguageModel()).thenReturn(languageModels);
        lenient().when(dllDao.getHomeworkMedia(homeworkIds)).thenReturn(mediaModels);
        lenient().when(dllDao.getDLLReplyByHomeworkIds(homeworkIds)).thenReturn(replyModels);
        lenient().when(dllDao.getReplyMedia(replyModels.stream().map(DLLReplyParentModel::getId).collect(Collectors.toList()))).thenReturn(replyMediaModels);
        lenient().when(userProvider.getCurrentUserId()).thenReturn(userId);
        lenient().when(fileSystem.getPublicUrl(anyString())).thenReturn("publishUrl");
        lenient().when(dllDao.getDLLHomeworkEditRecord(homeworkIds)).thenReturn(historyModels);
        // 接口调用
        DLLCoachHomeworkResponse response = dllService.getCoachHomeworkList(request);
        assertTrue(response != null);
    }

    /**
     * 公共请求参数
     */
    @NotNull
    private DLLLanguageRequest commonRequest(boolean onlyShareDLLChild, String publishDate) {
        // 数据准备
        DLLLanguageRequest request = new DLLLanguageRequest();
        request.setHomeworkId("test-homeworkId001");
        request.setTitle("title-homework");
        request.setContent("content-homework");
        request.setGroupId("test-groupId001");
        request.setIsShareParent(true);
        request.setPublishStatus("PUBLIC");
        request.setLang("en");
        request.setIsRemindParent(false);
        request.setOnlyShareDLLChild(onlyShareDLLChild);
        request.setSourceType("DLL_LIBRARY");
        request.setDescription("test-description");
        List<String> mediaIds = new ArrayList<>();
        mediaIds.add("test-mediaId001");
        request.setMediaIds(mediaIds);
        List<DLLLangContentModel> langContentModels = new ArrayList<>();
        DLLLangContentModel langContentModel = new DLLLangContentModel();
        langContentModel.setLang("en");
        langContentModel.setLangCode("en");
        langContentModel.setContent("test-content");
        langContentModel.setId("test-id");
        langContentModel.setSortIndex(1);
        langContentModels.add(langContentModel);
        request.setLangContentModels(langContentModels);
        request.setPublishDate(publishDate);
        String userId = "U001";
        AgencyModel agencyModel = new AgencyModel();
        String agencyId = "AGENCY_ID";
        agencyModel.setId(agencyId);
        // 小孩数据
        List<EnrollmentEntity> enrollmentEntities = new ArrayList<>();
        EnrollmentEntity enrollmentEntity = new EnrollmentEntity();
        enrollmentEntity.setId("test-enrollmentId001");
        enrollmentEntity.setAgencyId(agencyId);
        enrollmentEntities.add(enrollmentEntity);
        // DLL 小孩数据
        List<EnrollmentModel> enrollmentModels = new ArrayList<>();
        EnrollmentModel enrollmentModel = new EnrollmentModel();
        enrollmentModel.setId("test-enrollmentId001");
        enrollmentModel.setAgencyId(agencyId);
        enrollmentModel.setGroupId("test-groupId001");
        enrollmentModels.add(enrollmentModel);
        // 当前用户
        UserModel user = new UserModel();
        user.setId(userId);
        user.setDisplayName("test-displayName");
        // 家长信息
        List<UserEntity> userEntities = new ArrayList<>();
        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntities.add(userEntity);
        // 设备信息
        DeviceEntity userDevice = new DeviceEntity();
        userDevice.setId("test-deviceId001");
        userDevice.setEndpointArn("test-endpoint");
        userDevice.setPlatform("Web");
        // 家长信息
        List<com.learninggenie.common.data.model.UserEntity> parents = new ArrayList<>();
        userEntity.setId(userId);
        // 返回结果
        ResultPojo resultPojo = new ResultPojo();
        resultPojo.setData("test-data");
        resultPojo.setStatus(200);
        // 家庭作业
        DLLHomeworkModel homeworkModel = new DLLHomeworkModel();
        homeworkModel.setOnlyShareDLLChild(onlyShareDLLChild);
        homeworkModel.setDescription("description");
        // 翻译的内容
        List<DLLHomeworkContentModel> contentModels = new ArrayList<>();
        DLLHomeworkContentModel contentModel = new DLLHomeworkContentModel();
        contentModel.setContent("test-content");
        contentModel.setLangCode("zh");
        contentModel.setId("test-id");
        contentModel.setSortIndex(1);
        contentModel.setHomeworkId(request.getHomeworkId());
        contentModel.setAgencyId(agencyId);
        contentModels.add(contentModel);

        // 模拟数据
        lenient().when(userProvider.getTimezoneOffsetNum()).thenReturn(8);
        lenient().when(dllDao.getHomeworkById(request.getHomeworkId())).thenReturn(homeworkModel);
        lenient().when(userProvider.getCurrentUserId()).thenReturn(userId);
        lenient().when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        lenient().doNothing().when(dllDao).batchCreateHomeworkLanguage(any());
        lenient().doNothing().when(dllDao).batchCreateHomeworkMedia(any());
        lenient().when(studentDao.getAllByGroupId(request.getGroupId())).thenReturn(enrollmentEntities);
        lenient().when(studentDao.getDllChildrenByAgency(Collections.singletonList(enrollmentEntity.getId()))).thenReturn(enrollmentModels);
        lenient().doNothing().when(dllDao).batchCreateHomeworkEnrollment(any());
        lenient().doNothing().when(dllDao).createHomeworkEditRecord(any());
        lenient().doNothing().when(remoteProvider).callTextToSpeechService(any());
        lenient().when(userDao.getUserById(userId)).thenReturn(user);
        lenient().when(studentDao.getAllByGroupId(request.getGroupId())).thenReturn(enrollmentEntities);
        lenient().when(studentDao.getDllChildrenByAgency(Collections.singletonList(enrollmentEntity.getId()))).thenReturn(enrollmentModels);
        lenient().when(userDao.getParentsByStudentIds(StringUtil.convertIdsToString(Collections.singletonList(enrollmentEntity.getId())))).thenReturn(userEntities);
        lenient().when(pushNotificationDao.getDeviceByUser(userId)).thenReturn(userDevice);
        lenient().when(commService.sendSNSMessages(any())).thenReturn(resultPojo);
        lenient().doNothing().when(dllDao).updateHomeworkNotify(anyList(), anyBoolean());
        lenient().when(groupDao.getParentByGroupId(request.getGroupId())).thenReturn(parents);
        lenient().when(dllDao.getHomeworkContentByHomeworkId(request.getHomeworkId())).thenReturn(contentModels);
        lenient().doNothing().when(dllDao).updateHomeworkContent(any());
        return request;
    }
}
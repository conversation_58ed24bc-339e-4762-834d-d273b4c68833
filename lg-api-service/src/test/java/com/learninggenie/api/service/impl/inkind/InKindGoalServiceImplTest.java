package com.learninggenie.api.service.impl.inkind;

import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.model.SuccessResponse;
import com.learninggenie.api.model.inkind.goal.*;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.provider.impl.InKindProviderImpl;
import com.learninggenie.common.data.dao.*;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.dao.inkind.*;
import com.learninggenie.common.data.entity.*;
import com.learninggenie.common.data.entity.inkind.*;
import com.learninggenie.common.data.enums.AgencyMetaKey;
import com.learninggenie.common.data.enums.UserRole;
import com.learninggenie.common.data.enums.inkind.GoalGrantStep;
import com.learninggenie.common.data.enums.inkind.InKindGoalSchoolYearStep;
import com.learninggenie.common.data.model.AgencyModel;
import com.learninggenie.common.data.model.CenterGroupModel;
import com.learninggenie.common.data.model.EnrollmentModel;
import com.learninggenie.common.data.model.inkind.goal.InKindGoalReport;
import com.learninggenie.common.utils.*;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class InKindGoalServiceImplTest {
    @InjectMocks
    public InKindGoalServiceImpl inKindGoalService;

    @Mock
    private InKindProviderImpl inKindCommonService;

    @Mock
    private UserProvider userProvider;

    @Mock
    private AgencyDao agencyDao;

    @Mock
    private CenterDao centerDao;

    @Mock
    private GroupDao groupDao;

    @Mock
    private StudentDao studentDao;

    @Mock
    private UserDaoImpl userDao;

    @Mock
    private InkindDao inkindDao;

    @Mock
    private ReportDao reportDao;

    @Mock
    private InKindsGoalGroupDao inKindsGoalGroupDao;

    @Mock
    private InKindsGoalSchoolYearDao inKindsGoalSchoolYearDao;

    @Mock
    private InKindsGoalValueDao inKindsGoalValueDao;

    @Mock
    private InKindsGoalMonthDao inKindsGoalMonthDao;

    @Mock
    private InKindReportGroupDao inKindReportGroupDao;

    @Mock
    private InKindReportGrantDao inKindReportGrantDao;

    @Mock
    private InKindsGoalGrantDao inKindsGoalGrantDao;

    /**
     * 获取 grant 详情
     */
    @Test
    void testGetGrantDetail() {
        String grantId = "G0001";
        String userId = "U0001";
        String agencyId = "A0001";

        List<AgencyModel> agencyModelList = new ArrayList<>();
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        agencyModel.setName("test Agency");
        agencyModelList.add(agencyModel);

        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole("AGENCY_ADMIN");

        List<GroupEntity> groupEntityList = new ArrayList<>();
        CenterEntity center = new CenterEntity();
        center.setId("C0001");
        center.setName("test Center");
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId("G0001");
        groupEntity.setName("test Group");
        groupEntity.setCenter(center);
        groupEntityList.add(groupEntity);
        GroupEntity groupEntity2 = new GroupEntity();
        groupEntity2.setId("G0003");
        groupEntity2.setName("test Group");
        groupEntity2.setCenter(center);
        groupEntityList.add(groupEntity2);


        List<GroupEntity> groupByAgency = new ArrayList<>();
        GroupEntity groupEntity1 = new GroupEntity();
        groupEntity1.setId("G0001");
        groupEntity1.setName("test Group");
        groupByAgency.add(groupEntity1);
        GroupEntity groupEntity4 = new GroupEntity();
        groupEntity4.setId("G0003");
        groupEntity4.setName("test Group");
        groupByAgency.add(groupEntity4);

        InKindsGoalGrant grantEntity = new InKindsGoalGrant();
        grantEntity.setId(grantId);
        grantEntity.setUnassignedAmount(new BigDecimal(100));
        grantEntity.setStep(GoalGrantStep.FINAL_COMPLETED.toString());
        grantEntity.setGoalAmount(new BigDecimal(1000));
        grantEntity.setStartDate(new Date());
        grantEntity.setEndDate(new Date());
        grantEntity.setCreateAtUtc(new Date());
        grantEntity.setGrantName("test Grant");
        grantEntity.setDeleted(false);

        InKindsGoalGrant grantEntity1 = new InKindsGoalGrant();
        grantEntity1.setId(grantId);
        grantEntity1.setUnassignedAmount(new BigDecimal(100));
        grantEntity1.setGoalAmount(new BigDecimal(100));
        grantEntity1.setStep(GoalGrantStep.FINAL_COMPLETED.toString());
        grantEntity1.setStartDate(new Date());
        grantEntity1.setEndDate(new Date());
        grantEntity1.setCreateAtUtc(new Date());
        grantEntity1.setGrantName("test Grant");
        grantEntity1.setDeleted(false);

        List<InKindsGoalValue> goalValueList = new ArrayList<>();
        InKindsGoalValue goalValue = new InKindsGoalValue();
        goalValue.setId("V0001");
        goalValue.setGrantId(grantId);
        goalValue.setGroupId("G0001");
        goalValue.setMonth("2023-04-01");
        goalValue.setGoalValue(new BigDecimal(100));
        goalValue.setDeleted(false);
        goalValueList.add(goalValue);
        InKindsGoalValue goalValue1 = new InKindsGoalValue();
        goalValue1.setId("V0002");
        goalValue1.setGrantId(grantId);
        goalValue1.setGroupId("G0002");
        goalValue1.setMonth("2023-04-01");
        goalValue1.setGoalValue(new BigDecimal(100));
        goalValue1.setDeleted(false);
        goalValueList.add(goalValue1);
        InKindsGoalValue goalValue2 = new InKindsGoalValue();
        goalValue2.setId("V0002");
        goalValue2.setGrantId(grantId);
        goalValue2.setGroupId("G0003");
        goalValue2.setMonth("2023-04-01");
        goalValue2.setGoalValue(new BigDecimal(100));
        goalValue2.setDeleted(false);
        goalValueList.add(goalValue2);

        List<InKindsGoalGroup> goalGroups = new ArrayList<>();
        InKindsGoalGroup goalGroup = new InKindsGoalGroup();
        goalGroup.setId("G0001");
        goalGroup.setGrantId(grantId);
        goalGroup.setGroupId("G0001");
        goalGroup.setType("HS");
        goalGroup.setDeleted(false);
        goalGroups.add(goalGroup);
        InKindsGoalGroup goalGroup1 = new InKindsGoalGroup();
        goalGroup1.setId("G0001");
        goalGroup1.setGrantId(grantId);
        goalGroup1.setGroupId("G0003");
        goalGroup1.setDeleted(false);
        goalGroup1.setType("EHS");
        goalGroups.add(goalGroup1);

        List<CenterEntity> centers = new ArrayList<>();
        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setId("C0001");
        centerEntity.setName("test Center");
        centers.add(centerEntity);

        List<InKindsGoalValue> valueList = new ArrayList<>();
        InKindsGoalValue value1 = new InKindsGoalValue();
        value1.setId("V0002");
        value1.setGrantId(grantId);
        value1.setGroupId("G0002");
        value1.setMonth("2023-04-01");
        value1.setGoalValue(new BigDecimal(100));
        value1.setDeleted(false);
        valueList.add(value1);

        String metaKey = InKindGoalSchoolYearStep.CENTER_KEY.getValue() + grantId;
        AgencyMetaDataEntity agencyMetaData = new AgencyMetaDataEntity();
        agencyMetaData.setAgencyId(agencyId);
        agencyMetaData.setMetaKey(metaKey);
        agencyMetaData.setMetaValue("C0001");

        String metaKey1 = InKindGoalSchoolYearStep.GROUP_KEY.getValue() + grantId;
        AgencyMetaDataEntity agencyMetaData1 = new AgencyMetaDataEntity();
        agencyMetaData1.setAgencyId(agencyId);
        agencyMetaData1.setMetaKey(metaKey1);
        agencyMetaData1.setMetaValue("G0001");

        List<CenterGroupModel> centerGroupByAgency = new ArrayList<>();
        CenterGroupModel centerGroupModel = new CenterGroupModel();
        centerGroupModel.setCenterId("C0001");
        centerGroupModel.setGroupId("G0003");
        centerGroupModel.setCenterName("test Center");
        centerGroupByAgency.add(centerGroupModel);

        // 数据准备 -- 接口模拟
        // when(userProvider.getCurrentUserId()).thenReturn(userId);
        // when(userProvider.checkUser(userId)).thenReturn(userEntity);
        // when(userDao.getAgencyByAgencyAdminId(userId)).thenReturn(agencyModelList);
        when(inKindCommonService.getAgencyId()).thenReturn(agencyId);
        when(groupDao.getGroupByAgency(agencyId)).thenReturn(groupByAgency);
        when(inKindsGoalGrantDao.getById(grantId)).thenReturn(grantEntity);
        when(inKindsGoalValueDao.getInKindsGoalValueByGrantId(grantId)).thenReturn(goalValueList);
        when(groupDao.getAllGroupsByIds2(anyList())).thenReturn(groupEntityList);
        when(inKindsGoalGroupDao.getInKindsGoalGroupByGrantId(grantId)).thenReturn(goalGroups);
        when(centerDao.getAllCentersByCenterIds(anyList())).thenReturn(centers);
        // Mockito.when(inKindsGoalValueDao.getInKindsGoalValueByGrantIdAndGroupIds(grantId, new ArrayList<>(Arrays.asList("G0002")))).thenReturn(valueList);
        when(agencyDao.getMeta(agencyId, metaKey)).thenReturn(agencyMetaData);
        when(agencyDao.getMeta(agencyId, AgencyMetaKey.IN_KIND_GOALS_ADD_DONATION_DATA_OPEN.toString())).thenReturn(null);
        when(centerDao.getCenterGroupByAgency(agencyId)).thenReturn(centerGroupByAgency);
        when(agencyDao.getMeta(agencyId, metaKey1)).thenReturn(agencyMetaData1);
        GetGoalResponse response = inKindGoalService.getGrantDetail(grantId);
        assertNotNull(response);
        assertEquals(response.getGrantId(), grantId);
        assertEquals(response.isTip(), true);


        when(inKindsGoalGrantDao.getById(grantId)).thenReturn(grantEntity1);
        inKindGoalService.getGrantDetail(grantId);

        when(agencyDao.getMeta(agencyId, metaKey)).thenReturn(null);
//        Mockito.when(centerDao.getCenterGroupByAgency(agencyId)).thenReturn(new ArrayList<>());
        inKindGoalService.getGrantDetail(grantId);

    }


    /**
     * 添加 grant 过程设置日期、班级、名字
     */
    @Test
    void testCreateGrantWithDateAndClass() {
        String userId = "U0001";
        String agencyId = "A0001";

        List<AgencyModel> agencyModelList = new ArrayList<>();
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        agencyModel.setName("test Agency");
        agencyModelList.add(agencyModel);

        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole("AGENCY_ADMIN");

        InKindGrantDateAndClassRequest request = new InKindGrantDateAndClassRequest();
        request.setGrantName("test grant");
        request.setStartDate("2020-01-01");
        request.setEndDate("2020-01-01");
        request.setGroupIds(Arrays.asList("G0001"));
        request.setMonths(Arrays.asList("2020-01"));

        // 数据准备 -- 接口模拟
        // when(userDao.getAgencyByAgencyAdminId(userId)).thenReturn(agencyModelList);
        // when(userProvider.getCurrentUserId()).thenReturn(userId);
        // when(userProvider.checkUser(userId)).thenReturn(userEntity);
        when(inKindCommonService.getAgencyId()).thenReturn(agencyId);
        when(inKindsGoalGrantDao.getInKindsGoalGrantByNameAndAgencyId("test grant", agencyId)).thenReturn(new ArrayList<>());
        SuccessResponse response = inKindGoalService.createGrantWithDateAndClass(request);
        assertEquals(response.isSuccess(), true);

        InKindGrantDateAndClassRequest request1 = new InKindGrantDateAndClassRequest();
        assertThrows(BusinessException.class,
                () -> inKindGoalService.createGrantWithDateAndClass(request1));

        List<InKindsGoalGrant> goalGrants = new ArrayList<>();
        InKindsGoalGrant goalGrant1 = new InKindsGoalGrant();
        goalGrant1.setId("G0002");
        goalGrant1.setGrantName("test grant");
        goalGrants.add(goalGrant1);
        when(inKindsGoalGrantDao.getInKindsGoalGrantByNameAndAgencyId("test grant", agencyId)).thenReturn(goalGrants);
        assertThrows(BusinessException.class,
                () -> inKindGoalService.createGrantWithDateAndClass(request));

    }


    /**
     * 更新 grant 班级和时间信息
     */
    @Disabled
    @Test
    void testUpdateGrantDateAndClass() {
        String userId = "U0001";
        String agencyId = "A0001";
        String grantId = "G0001";

        List<InKindsGoalGrant> goalGrants = new ArrayList<>();
        InKindsGoalGrant goalGrant = new InKindsGoalGrant();
        goalGrant.setId(grantId);
        goalGrant.setGrantName("test grant");
        goalGrants.add(goalGrant);

        InKindsGoalGrant goalGrant1 = new InKindsGoalGrant();
        goalGrant1.setId("G0002");
        goalGrant1.setGrantName("test grant");
        goalGrants.add(goalGrant1);

        List<AgencyModel> agencyModelList = new ArrayList<>();
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        agencyModel.setName("test Agency");
        agencyModelList.add(agencyModel);

        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole("AGENCY_ADMIN");

        List<InKindsGoalGroup> inKindsGoalGroups = new ArrayList<>();
        InKindsGoalGroup goalGroup = new InKindsGoalGroup();
        goalGroup.setGrantId(goalGrant.getId());
        goalGroup.setGroupId("G0001");
        goalGroup.setGroupName("test group");
        goalGroup.setType("HS");
        inKindsGoalGroups.add(goalGroup);
        InKindsGoalGroup goalGroup1 = new InKindsGoalGroup();
        goalGroup1.setGrantId(goalGrant.getId());
        goalGroup1.setGroupId("G0002");
        goalGroup.setType("EHS");
        goalGroup1.setGroupName("test group");
        inKindsGoalGroups.add(goalGroup1);

        InKindGrantDateAndClassRequest request = new InKindGrantDateAndClassRequest();
        request.setGrantId(goalGrant.getId());
        request.setGrantName(goalGrant.getGrantName());
        request.setStartDate("2020-01-01");
        request.setEndDate("2020-12-31");
        request.setCreate(false);
        request.setGroupIds(Arrays.asList("G0002", "G0003"));
        request.setMonths(Arrays.asList("2020-02", "2020-03"));

        List<InKindsGoalValue> inKindsGoalValues = new ArrayList<>();
        InKindsGoalValue goalValue = new InKindsGoalValue();
        goalValue.setGrantId(goalGrant.getId());
        goalValue.setGroupId(goalGroup.getGroupId());
        goalValue.setMonth("2020-01");
        goalValue.setGoalValue(new BigDecimal(100));
        inKindsGoalValues.add(goalValue);

        InKindsGoalValue goalValue1 = new InKindsGoalValue();
        goalValue1.setGrantId(goalGrant.getId());
        goalValue1.setGroupId(goalGroup.getGroupId());
        goalValue1.setMonth("2020-02");
        goalValue1.setGoalValue(new BigDecimal(100));
        inKindsGoalValues.add(goalValue1);

        List<InKindsGoalMonth> goalMonths = new ArrayList<>();
        InKindsGoalMonth goalMonth = new InKindsGoalMonth();
        goalMonth.setGrantId(goalGrant.getId());
        goalMonth.setMonth("2020-01");
        goalMonths.add(goalMonth);

        InKindsGoalMonth goalMonth1 = new InKindsGoalMonth();
        goalMonth1.setGrantId(goalGrant.getId());
        goalMonth1.setMonth("2020-02");
        goalMonths.add(goalMonth1);

        // 数据准备 -- 接口模拟
        when(userDao.getAgencyByAgencyAdminId(userId)).thenReturn(agencyModelList);
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.checkUser(userId)).thenReturn(userEntity);
        when(inKindsGoalGrantDao.getInKindsGoalGrantByNameAndAgencyId("test grant", agencyId)).thenReturn(new ArrayList<>());
        when(inKindsGoalGrantDao.getById(grantId)).thenReturn(goalGrant);
        when(inKindsGoalValueDao.getInKindsGoalValueByGrantId(grantId)).thenReturn(inKindsGoalValues);
        when(inKindsGoalGroupDao.getInKindsGoalGroupByGrantId(grantId)).thenReturn(inKindsGoalGroups);
        when(inKindsGoalMonthDao.getInKindsGoalMonthByGrantId(grantId)).thenReturn(goalMonths);
        SuccessResponse response = inKindGoalService.updateGrantDateAndClass(request);
        assertEquals(response.isSuccess(), true);

        InKindGrantDateAndClassRequest request1 = new InKindGrantDateAndClassRequest();
        assertThrows(BusinessException.class,
                () -> inKindGoalService.updateGrantDateAndClass(request1));

        when(inKindsGoalGrantDao.getInKindsGoalGrantByNameAndAgencyId("test grant", agencyId)).thenReturn(goalGrants);
        assertThrows(BusinessException.class,
                () -> inKindGoalService.updateGrantDateAndClass(request));
    }

    /**
     * 获取 grant 创建信息
     */
    @Test
    void testGetCreateGrantInfo() {
        String userId = "U0001";
        String agencyId = "A0001";

        List<InKindsGoalGrant> goalGrants = new ArrayList<>();
        InKindsGoalGrant goalGrant = new InKindsGoalGrant();
        goalGrant.setGrantName("test grant");
        goalGrant.setId("G0001");
        goalGrant.setStep(GoalGrantStep.STEP1_COMPLETED.toString());
        goalGrants.add(goalGrant);

        List<InKindsGoalGroup> inKindsGoalGroups = new ArrayList<>();
        InKindsGoalGroup goalGroup = new InKindsGoalGroup();
        goalGroup.setGrantId(goalGrant.getId());
        goalGroup.setGroupId("G0001");
        goalGroup.setGroupName("test group");
        inKindsGoalGroups.add(goalGroup);

        // 数据准备 -- 接口模拟
        when(inKindsGoalGrantDao.getInKindsGoalGrantByAgencyIdAndUserId(agencyId, userId, GoalGrantStep.STEP1_COMPLETED.toString())).thenReturn(goalGrants);
        when(inKindsGoalGroupDao.getInKindsGoalGroupByGrantId(goalGrant.getId())).thenReturn(inKindsGoalGroups);
        InKindGrantDateAndClassResponse response = inKindGoalService.getCreateGrantInfo(agencyId, userId);
        assertEquals(response.getGrantName(), goalGrant.getGrantName());
        assertEquals(response.getGrantId(), goalGrant.getId());

        assertThrows(BusinessException.class,
                () -> inKindGoalService.getCreateGrantInfo("", userId));

    }

    /**
     * 设置 grant 分配金额
     */
    @Test
    void testUpdateGrantAmount() {
        String userId = "U0001";
        String agencyId = "A0001";
        String grantId = "G0001";

        List<AgencyModel> agencyModelList = new ArrayList<>();
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        agencyModel.setName("test Agency");
        agencyModelList.add(agencyModel);

        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole("AGENCY_ADMIN");

        SaveGoalRequest request = new SaveGoalRequest();
        request.setGrantId("G0001");
        request.setGoalTotal(new BigDecimal(1000));
        request.setUnassignedTotal(new BigDecimal(100));
        List<GroupGoalModel> groupGoalModels = new ArrayList<>();
        GroupGoalModel groupGoalModel = new GroupGoalModel();
        groupGoalModel.setGroupId("G0001");
        groupGoalModel.setGoalValue(new BigDecimal(100));
        groupGoalModel.setMonth("2020-01");
        groupGoalModel.setGroupId("G0001");
        groupGoalModels.add(groupGoalModel);
        GroupGoalModel groupGoalModel1 = new GroupGoalModel();
        groupGoalModel1.setGroupId("G0001");
        groupGoalModel1.setGoalValue(new BigDecimal(100));
        groupGoalModel1.setMonth("2020-01");
        groupGoalModel1.setGroupId("G0002");
        groupGoalModels.add(groupGoalModel1);
        request.setGroupGoals(groupGoalModels);

        List<InKindsGoalValue> inKindsGoalValues = new ArrayList<>();
        InKindsGoalValue inKindsGoalValue = new InKindsGoalValue();
        inKindsGoalValue.setGrantId("G0001");
        inKindsGoalValue.setMonth("2020-01");
        inKindsGoalValue.setGroupId("G0001");
        inKindsGoalValue.setCenterId("C0001");
        inKindsGoalValues.add(inKindsGoalValue);
        InKindsGoalValue inKindsGoalValue1 = new InKindsGoalValue();
        inKindsGoalValue1.setGrantId("G0001");
        inKindsGoalValue1.setMonth("2020-01");
        inKindsGoalValue1.setGroupId("G0003");
        inKindsGoalValue1.setCenterId("C0001");
        inKindsGoalValues.add(inKindsGoalValue1);

        InKindsGoalGrant inKindsGoalGrant = new InKindsGoalGrant();
        inKindsGoalGrant.setId("G0001");

        String metaKey = InKindGoalSchoolYearStep.CENTER_KEY.getValue() + grantId;
        AgencyMetaDataEntity agencyMetaData = new AgencyMetaDataEntity();
        agencyMetaData.setAgencyId(agencyId);
        agencyMetaData.setMetaKey(metaKey);
        agencyMetaData.setMetaValue("C0001");

        List<CenterGroupModel> centerGroupByAgency = new ArrayList<>();
        CenterGroupModel centerGroupModel = new CenterGroupModel();
        centerGroupModel.setCenterId("C0002");
        centerGroupModel.setGroupId("G0001");
        centerGroupByAgency.add(centerGroupModel);

        String metaKey1 = InKindGoalSchoolYearStep.GROUP_KEY.getValue() + grantId;
        AgencyMetaDataEntity agencyMetaData1 = new AgencyMetaDataEntity();
        agencyMetaData1.setAgencyId(agencyId);
        agencyMetaData1.setMetaKey(metaKey1);
        agencyMetaData1.setMetaValue("G0001");

        // 数据准备 -- 接口模拟
        // when(userProvider.getCurrentUserId()).thenReturn(userId);
        // when(userProvider.checkUser(userId)).thenReturn(userEntity);
        // when(userDao.getAgencyByAgencyAdminId(userId)).thenReturn(agencyModelList);
        when(inKindCommonService.getAgencyId()).thenReturn(agencyId);
        when(inKindsGoalValueDao.getInKindsGoalValueByGrantId(grantId)).thenReturn(inKindsGoalValues);
        when(agencyDao.getMeta(agencyId, metaKey)).thenReturn(agencyMetaData);
        when(centerDao.getCenterGroupByAgency(agencyId)).thenReturn(centerGroupByAgency);
        when(agencyDao.getMeta(agencyId, metaKey1)).thenReturn(agencyMetaData1);
        when(agencyDao.getMeta(agencyId, AgencyMetaKey.IN_KIND_GOALS_ADD_DONATION_DATA_OPEN.toString())).thenReturn(null);

        SuccessResponse response = inKindGoalService.updateGrantAmount(request);
        assertEquals(response.isSuccess(), true);

        when(agencyDao.getMeta(agencyId, metaKey)).thenReturn(null);
        when(agencyDao.getMeta(agencyId, metaKey1)).thenReturn(null);
        when(inKindsGoalValueDao.getInKindsGoalValueByGrantId(grantId)).thenReturn(new ArrayList<>());
        SuccessResponse response1 = inKindGoalService.updateGrantAmount(request);
        assertEquals(response1.isSuccess(), true);


    }

    /**
     * 获取已经设置过的grant、学年列表
     */
    @Test
    void testGetGrantFilterList() {
        String userId = "U0001";
        String agencyId = "A0001";

        List<AgencyModel> agencyModelList = new ArrayList<>();
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        agencyModel.setName("test Agency");
        agencyModelList.add(agencyModel);

        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole("AGENCY_ADMIN");

        UserEntity userEntity1 = new UserEntity();
        userEntity1.setId(userId);
        userEntity1.setRole(UserRole.SITE_ADMIN.toString());

        UserEntity userEntity2 = new UserEntity();
        userEntity2.setId(userId);
        userEntity2.setRole(UserRole.COLLABORATOR.toString());

        List<InKindsGoalGrant> grantEntityList = new ArrayList<>();
        InKindsGoalGrant grantEntity = new InKindsGoalGrant();
        grantEntity.setId("G0001");
        grantEntity.setGrantName("test Grant");
        grantEntity.setStep(GoalGrantStep.FINAL_COMPLETED.toString());
        grantEntityList.add(grantEntity);

        List<InKindsGoalMonth> months = new ArrayList<>();
        InKindsGoalMonth month = new InKindsGoalMonth();
        month.setMonth("2020-01");
        month.setGrantId("G0001");
        months.add(month);

        List<InKindsGoalGroup> groups = new ArrayList<>();
        InKindsGoalGroup group = new InKindsGoalGroup();
        group.setGroupId("G0001");
        group.setGrantId("G0001");
        group.setCenterId("C0001");
        groups.add(group);

        List<GroupEntity> agencyAdminGroupList = new ArrayList<>();
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId("G0001");
        groupEntity.setName("test Group");
        CenterEntity center = new CenterEntity();
        center.setId("C0001");
        center.setName("test Center");
        groupEntity.setCenter(center);
        agencyAdminGroupList.add(groupEntity);

        List<InKindsGoalGroup> goalGroups = new ArrayList<>();
        InKindsGoalGroup goalGroup = new InKindsGoalGroup();
        goalGroup.setGroupId("G0001");
        goalGroup.setGrantId("G0001");
        goalGroup.setCenterId("C0001");
        goalGroup.setAgencyId(agencyId);
        goalGroups.add(goalGroup);

        List<InKindsGoalSchoolYear> schoolYears = new ArrayList<>();
        InKindsGoalSchoolYear schoolYear = new InKindsGoalSchoolYear();
        schoolYear.setSchoolYear("2020-2021");
        schoolYear.setStep(InKindGoalSchoolYearStep.STEP3_GOAL_COMPLETE.toString());
        schoolYears.add(schoolYear);

        List<InKindsGoalGroup> goalGroupList = new ArrayList<>();
        InKindsGoalGroup goalGroup1 = new InKindsGoalGroup();
        goalGroup1.setGroupId("G0001");
        goalGroup1.setSchoolYear("2020-2021");
        goalGroup1.setCenterId("C0001");
        goalGroup1.setAgencyId(agencyId);
        goalGroupList.add(goalGroup1);

        List<InKindsGoalMonth> goalMonthList = new ArrayList<>();
        InKindsGoalMonth goalMonth = new InKindsGoalMonth();
        goalMonth.setMonth("2020-01");
        goalMonth.setSchoolYear("2020-2021");
        goalMonth.setAgencyId(agencyId);
        goalMonthList.add(goalMonth);

        // 数据准备 -- 接口模拟
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.checkUser(userId)).thenReturn(userEntity);
        when(inKindCommonService.getAgencyId(userId)).thenReturn(agencyId);
        when(inKindsGoalMonthDao.getInKindsGoalMonthByGrantIds(new ArrayList<>(Arrays.asList("G0001")))).thenReturn(months);
        when(inKindsGoalGroupDao.getInKindsGoalGroupByGrantIds(new ArrayList<>(Arrays.asList("G0001")))).thenReturn(groups);
        when(inKindsGoalGroupDao.getInKindsGoalGroupByAgencyId(agencyId)).thenReturn(goalGroups);
        // when(userDao.getAgencyByAgencyAdminId(userId)).thenReturn(agencyModelList);
        when(groupDao.getAllGroupByAgencyAdmin(userId)).thenReturn(agencyAdminGroupList);
        when(inKindsGoalGrantDao.getInKindsGoalGrantByAgencyId(agencyId)).thenReturn(grantEntityList);

        when(inKindsGoalSchoolYearDao.list(any())).thenReturn(schoolYears);
        when(inKindsGoalGroupDao.list(any())).thenReturn(goalGroupList);
        when(inKindsGoalMonthDao.list(any())).thenReturn(goalMonthList);
        InKindGrantFilterListResponse response = inKindGoalService.getGrantFilterList(agencyId);
        assertNotNull(response);


        // when(userDao.getAgencyBySiteAdminId(userId)).thenReturn(agencyModelList);
        when(userProvider.checkUser(userId)).thenReturn(userEntity1);
        InKindGrantFilterListResponse response1 = inKindGoalService.getGrantFilterList(agencyId);
        assertNotNull(response1);

        // when(userDao.getAgencyByTeacherId(userId)).thenReturn(agencyModelList);
        when(userProvider.checkUser(userId)).thenReturn(userEntity2);
        InKindGrantFilterListResponse response2 = inKindGoalService.getGrantFilterList(agencyId);
        assertNotNull(response2);

    }

    /**
     * 删除 grant 和学年
     */
    @Test
    void testDeleteGrantAndSchoolYear() {
        DeleteGoalRequest request = new DeleteGoalRequest();
        request.setSchoolYear("2023-2024");

        DeleteGoalRequest request1 = new DeleteGoalRequest();
        request1.setGrantId("G0001");

        String userId = "U0001";
        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole("AGENCY_ADMIN");

        String agencyId = "A0001";
        List<AgencyModel> agencyModelList = new ArrayList<>();
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        agencyModel.setName("test Agency");
        agencyModelList.add(agencyModel);

        // 数据准备 -- 接口模拟
        // when(userProvider.getCurrentUserId()).thenReturn(userId);
        // when(userProvider.checkUser(userId)).thenReturn(userEntity);
        // when(userDao.getAgencyByAgencyAdminId(userId)).thenReturn(agencyModelList);
        when(inKindCommonService.getAgencyId()).thenReturn(agencyId);
        SuccessResponse response = inKindGoalService.deleteGrant(request);
        verify(inKindsGoalSchoolYearDao, times(1)).deleteByAgencyIdAndSchoolYear(agencyId, request.getSchoolYear());
        verify(inKindsGoalValueDao, times(1)).deleteInKindsGoalValueByAgencyIdAndSchoolYear(agencyId, request.getSchoolYear());
        verify(inKindsGoalGroupDao, times(1)).deleteByAgencyIdAndSchoolYear(agencyId, request.getSchoolYear());
        verify(inKindsGoalMonthDao, times(1)).deleteByAgencyIdAndSchoolYear(agencyId, request.getSchoolYear());
        assertEquals(response.isSuccess(), true);

        SuccessResponse response1 = inKindGoalService.deleteGrant(request1);
        verify(inKindsGoalGrantDao, times(1)).deleteInKindsGoalGrantById(request1.getGrantId());
        verify(inKindsGoalValueDao, times(1)).deleteInKindsGoalValueByGrantId(request1.getGrantId());
        verify(inKindsGoalMonthDao, times(1)).deleteInKindsGoalMonthByGrantId(request1.getGrantId());
        assertEquals(response1.isSuccess(), true);
    }

    /**
     * 测试 inkind goal 报告接口
     */
    @Disabled
    @Test
    void testGetGoalReport() throws ParseException {
        // 数据准备
        String agencyId = "A0001";
        String grantId = "G0001";
        String userId = "U0001";
        Date now = new Date();
        String startDate = "2021-01-01";

        List<AgencyModel> agencyModelList = new ArrayList<>();
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        agencyModel.setName("test Agency");
        agencyModelList.add(agencyModel);

        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole("AGENCY_ADMIN");

        GetGoalReportListRequest request = new GetGoalReportListRequest();
        request.setGrantId(grantId);
        request.setGroupIds(Arrays.asList("G0001", "G0003"));
        request.setCenterIds(Arrays.asList("C0001"));
        request.setProgramName("test Program");
        request.setMonth(startDate);
        request.setAgencyId(agencyId);

        List<InKindsGoalGroup> inKindsGoalGroupList = new ArrayList<>();
        InKindsGoalGroup inKindsGoalGroup = new InKindsGoalGroup();
        inKindsGoalGroup.setGrantId(grantId);
        inKindsGoalGroup.setAgencyId(agencyId);
        inKindsGoalGroup.setGroupId("G0001");
        inKindsGoalGroup.setGroupName("test Group");
        inKindsGoalGroupList.add(inKindsGoalGroup);

        List<InKindsGoalMonth> months = new ArrayList<>();
        InKindsGoalMonth month = new InKindsGoalMonth();
        month.setGrantId(grantId);
        month.setAgencyId(agencyId);
        month.setMonth(startDate);
        months.add(month);

        String endDate = TimeUtil.format(TimeUtil.getLastDayOfMonth(startDate), TimeUtil.format2);
        List<InKindGoalReport> totalReports = new ArrayList<>();
        InKindGoalReport totalReport = new InKindGoalReport();
        totalReport.setEnrollmentId("E0001");
        totalReport.setCenterId("C0001");
        totalReport.setGroupId("G0001");
        totalReport.setUnit("HOUR");
        totalReport.setValue(new BigDecimal(10));
        totalReport.setRateValue(new BigDecimal(10));
        totalReport.setRateUnit("HOUR");
        totalReport.setMoney(new BigDecimal(100));
        totalReport.setActivityDate(TimeUtil.parse(now, TimeUtil.format10));
        totalReports.add(totalReport);

        List<GroupEntity> groupEntityList = new ArrayList<>();
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId("G0001");
        groupEntity.setName("test Group");
        CenterEntity center = new CenterEntity();
        center.setId("C0001");
        center.setName("test Center");
        groupEntity.setCenter(center);
        groupEntityList.add(groupEntity);

        List<EnrollmentModel> children = new ArrayList<>();
        EnrollmentModel enrollmentModel1 = new EnrollmentModel();
        enrollmentModel1.setId("E0001");
        com.learninggenie.common.data.model.GroupEntity modelGroup1 = new com.learninggenie.common.data.model.GroupEntity();
        modelGroup1.setId("M0001");
        enrollmentModel1.setGroup(modelGroup1);
        enrollmentModel1.setCenterId("C0001");
        enrollmentModel1.setGroupId("G0001");
        EnrollmentModel enrollmentModel2 = new EnrollmentModel();
        enrollmentModel2.setId("E0002");
        com.learninggenie.common.data.model.GroupEntity modelGroup2 = new com.learninggenie.common.data.model.GroupEntity();
        modelGroup2.setId("M0002");
        enrollmentModel2.setCenterId("C0002");
        enrollmentModel2.setGroupId("G0003");
        enrollmentModel2.setGroup(modelGroup2);
        children.add(enrollmentModel1);
        children.add(enrollmentModel2);

        List<com.learninggenie.common.data.model.GroupEntity> groupsList = new ArrayList<>();
        com.learninggenie.common.data.model.GroupEntity group1 = new com.learninggenie.common.data.model.GroupEntity();
        group1.setCenterId("C0001");
        group1.setId("G0001");
        group1.setCenter(new com.learninggenie.common.data.model.CenterEntity());
        com.learninggenie.common.data.model.GroupEntity group2 = new com.learninggenie.common.data.model.GroupEntity();
        group2.setCenterId("C0002");
        group2.setId("G0002");
        group2.setCenter(new com.learninggenie.common.data.model.CenterEntity());
        groupsList.add(group1);
        groupsList.add(group2);

        List<String> groupIds = new ArrayList<>();
        groupIds.add("G0001");
        groupIds.add("G0003");

        List<String> allChilds = new ArrayList<>();
        allChilds.add("E0002");

        List<EnrollmentEntity> currentChildren = new ArrayList<>();
        EnrollmentEntity enrollmentEntity1 = new EnrollmentEntity();
        enrollmentEntity1.setId("E0001");
        enrollmentEntity1.setGroup(groupEntity);
        EnrollmentEntity enrollmentEntity2 = new EnrollmentEntity();
        enrollmentEntity1.setId("E0002");
        enrollmentEntity1.setGroup(groupEntity);
        GroupEntity groupEntity1 = new GroupEntity();
        groupEntity1.setId("G0002");
        groupEntity1.setName("test Group1");
        enrollmentEntity2.setId("E0002");
        enrollmentEntity2.setGroup(groupEntity1);
        currentChildren.add(enrollmentEntity1);
        currentChildren.add(enrollmentEntity2);

        List<InKindReportGroup> inKindReportGroups = new ArrayList<>();
        InKindReportGroup inKindReportGroup1 = new InKindReportGroup();
        inKindReportGroup1.setGroupId("G0001");
        inKindReportGroups.add(inKindReportGroup1);
        InKindReportGroup inKindReportGroup2 = new InKindReportGroup();
        inKindReportGroup2.setGroupId("G0003");
        inKindReportGroups.add(inKindReportGroup2);

        // 数据准备 -- 接口模拟
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.checkUser(userId)).thenReturn(userEntity);
        when(inKindsGoalGroupDao.getInKindsGoalGroupByGrantId(request.getGrantId())).thenReturn(inKindsGoalGroupList);
        when(groupDao.getAllGroupByAgencyAdmin(userId)).thenReturn(groupEntityList);
        when(inKindsGoalMonthDao.getInKindsGoalMonthByGrantId(request.getGrantId())).thenReturn(months);
        when(reportDao.getGoalSubmissionReport(startDate, endDate, agencyId, new ArrayList<>(Arrays.asList("G0001")))).thenReturn(totalReports);
        when(studentDao.getAllChildrenByGroupIds(groupIds)).thenReturn(currentChildren);
        when(studentDao.getAllChildrenByIds(allChilds)).thenReturn(children);
        when(groupDao.getGroupWithCenterByGroupIds(groupIds)).thenReturn(groupsList);
        when(inKindReportGroupDao.getInKindReportGroupByReportIds(anyList())).thenReturn(inKindReportGroups);
        GetGoalReportListResponse response = inKindGoalService.getGoalReport(request);
        assertNotNull(response);
        assertEquals(2, response.getCompletionProgress().getSites().size()); // 验证学校数量为 2
        assertEquals(0, response.getCompletionProgress().getClassNum()); // 验证班级数量为 0
        assertEquals(0, response.getCompletionProgress().getChildNum()); // 验证小孩数量为 0

    }

    /**
     * 测试清空待分配金额接口
     */
    @Test
    void testClearUnassignedValue() {
        // 数据准备
        String grantId = "G0001";

        String agencyId = "A0001";
        String userId = "U0001";

        List<AgencyModel> agencyModelList = new ArrayList<>();
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        agencyModel.setName("test Agency");
        agencyModelList.add(agencyModel);

        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole("AGENCY_ADMIN");

        InKindsGoalGrant goalGrant = new InKindsGoalGrant();
        goalGrant.setId(grantId);
        goalGrant.setUnassignedAmount(new BigDecimal(100));
        // 数据准备 -- 接口模拟
        when(inKindsGoalGrantDao.getById(grantId)).thenReturn(goalGrant);
        // when(userProvider.getCurrentUserId()).thenReturn(userId);
        // when(userProvider.checkUser(userId)).thenReturn(userEntity);
        // when(userDao.getAgencyByAgencyAdminId(userId)).thenReturn(agencyModelList);
        when(inKindCommonService.getAgencyId()).thenReturn(agencyId);
        inKindGoalService.clearUnassignedValue(grantId, "", new BigDecimal(100));
        verify(inKindsGoalGrantDao, times(1)).updateById(goalGrant);
    }
}

package com.learninggenie.api.service;

import com.learninggenie.api.config.TestBase;
import com.learninggenie.common.data.dao.*;
import com.learninggenie.common.data.dao.impl.CenterMapper;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.web.client.RestTemplate;

import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

@Ignore
public class ImportDataServiceTest extends TestBase {
    @InjectMocks
    @Autowired
    ImportDataService importDataService;
    @Mock
    CenterMapper centerMapper;
    @Mock
    GroupDao groupDao;
    @Mock
    UserDaoImpl userDao;
    @Mock
    StudentDao studentDao;
    @Mock
    DataSource dataSource;
    @Mock
    ImportRecordDao importRecordDao;
    @Autowired
    @Spy
    PlatformTransactionManager transactionManager;
    @Mock
    private CentersMetaDataDao centersMetaDataDao;
    @Mock
    private GroupsMetaDataDao groupsMetaDataDao;
    @Mock
    private UsersMetaDataDao usersMetaDataDao;
    @Mock
    private EnrollementsMetaDataDao enrollementsMetaDataDao;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Autowired
    private RestTemplate restTemplate;

    @Ignore
    @Test
    public void te() {
        Map<String, Object> map = new HashMap<>();
        map.put("user_id", "03C844EB-A54F-E511-A1D4-02F5040BB599");
        Collection<String> list = new ArrayList<>();
        list.add("EE009D3F-0BED-4A1C-847C-0FD00425CEB1");
        map.put("enrollment_ids", list);
        String x = restTemplate.postForObject("http://52.8.67.228/api/enrollments/send_invitations", map, String.class);
        System.out.println(x);
    }
}

package com.learninggenie.api.service;

import com.learninggenie.api.config.TestBase;
import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.exception.ErrorCode;
import com.learninggenie.common.data.entity.AlbumEntity;
import com.learninggenie.common.data.entity.AlbumPageEntity;
import com.learninggenie.common.data.repository.AlbumRepository;
import com.learninggenie.common.utils.MSG;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Ignore
public class AlbumServiceTest extends TestBase {
    public static final String SPACE = " ";
    public static final String UNDER_SCORE = "_";
    public static final String HTML_SUFFIX = ".html";
    public static final String PDF_SUFFIX = ".pdf";
    public static final String JPEG_SUFFIX = ".jpg";
    @Autowired
    private AlbumRepository albumRepository;

    @Ignore
    @Test
    public void WriteAlbumToFile() throws IOException {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        AlbumEntity album = albumRepository.findById("7765C8BE-1D6A-4FAB-8B76-0019CB9DC9CD").orElse(null);
        if(album == null){
            throw new BusinessException(ErrorCode.ALBUM_NOT_FOUND, MSG.t("NO_ALB"));
        }
        List<AlbumPageEntity> pageEntities =  new ArrayList<>(album.getPages());
        Collections.sort(pageEntities, AlbumPageEntity.PAGE);
//        String command = "C:\\Users\\<USER>\\Desktop\\wkhtmltopdf\\bin\\wkhtmltoimage.exe ";

//        List<File> htmls = new ArrayList();
//        for(AlbumPageEntity pageEntity: pageEntities) {
//            StringBuilder sb = new StringBuilder();
//            sb.append(command);
//            File f = new File(String.valueOf(pageEntity.getPageNum()+ HTML_SUFFIX));
//            htmls.add(f);
//            HtmlImageGenerator imageGenerator = new HtmlImageGenerator();
//            imageGenerator.loadHtml(pageEntity.getHtml());
//            try(FileWriter out = new FileWriter(f)) {
//                out.write(pageEntity.getHtml());
//            }
//            sb.append(f.getAbsolutePath());
//            sb.append(SPACE);
//            sb.append(pageEntity.getPageNum() + JPEG_SUFFIX);
//            System.out.println(sb);
//            //Runtime.getRuntime().exec(sb.toString());
//            imageGenerator.saveAsImage(pageEntity.getPageNum() + JPEG_SUFFIX);
//        }


        //cleanup(htmls);
        stopWatch.stop();
        System.out.println(stopWatch.getTotalTimeMillis()/1000.0);
    }

    private void cleanup(List<File> htmls) {
        for(File f: htmls){
            f.delete();
        }
    }

}

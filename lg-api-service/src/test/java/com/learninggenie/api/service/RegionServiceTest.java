package com.learninggenie.api.service;

import com.learninggenie.common.data.config.BeanFactory;
import com.learninggenie.common.data.enums.RegionType;
import com.learninggenie.common.utils.RegionServiceUtil;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * @author: ZL
 * @Description:
 * @Date: Created in 10:34 2018/3/9
 */
@RunWith(MockitoJUnitRunner.class)
public class RegionServiceTest {
    @Mock
    private BeanFactory beanFactory;

    public String prepareEnvironment(){
        return "us";
    }
    /**
     *  case: 获取regionType的值
     */
    @Test
    public void testRegionService_GetRegionType(){
        String key = this.prepareEnvironment();
        RegionType regionByProperties = RegionServiceUtil.getRegionByProperties(key);
        Assert.assertEquals(RegionType.US,regionByProperties);
    }
}

package com.learninggenie.api.service.impl.inkind;

import com.learninggenie.api.model.ActivityAssignmentStatusResponse;
import com.learninggenie.api.model.AssignmentList;
import com.learninggenie.api.model.CreateAssignmentRequest;
import com.learninggenie.api.model.inkind.*;
import com.learninggenie.api.model.inkind.constant.InKindConstant;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.security.AuthUserDetails;
import com.learninggenie.api.provider.InKindProvider;
import com.learninggenie.common.data.dao.AgencyDao;
import com.learninggenie.common.data.dao.InkindDao;
import com.learninggenie.common.data.dao.StudentDao;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.dao.users.UserReminderDao;
import com.learninggenie.common.data.dto.inkind.AssignmentJobDTO;
import com.learninggenie.common.data.dto.inkind.InKindReportDTO;
import com.learninggenie.common.data.entity.*;
import com.learninggenie.common.data.entity.users.UserReminderEntity;
import com.learninggenie.common.data.enums.AgencyMetaKey;
import com.learninggenie.common.data.enums.AppType;
import com.learninggenie.common.data.enums.Platform;
import com.learninggenie.common.data.enums.UserMetaKey;
import com.learninggenie.common.data.enums.inkind.ActivityTypeValue;
import com.learninggenie.common.data.enums.inkind.AssignStateType;
import com.learninggenie.common.data.enums.user.UserRemindType;
import com.learninggenie.common.data.mapper.dynamo.AgencyMetadataMapper;
import com.learninggenie.common.data.model.EnrollmentModel;
import com.learninggenie.common.data.model.UserModel;
import com.learninggenie.common.data.model.dynamo.AgencyMetadata;
import com.learninggenie.common.filesystem.FileSystem;
import com.learninggenie.common.utils.TimeUtil;
import org.junit.Assert;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;


@ExtendWith(MockitoExtension.class)
public class InKindAssignmentServiceImplTest {

    @InjectMocks
    private InKindAssignmentServiceImpl inKindAssignmentService;

    @Mock
    private InKindProvider inKindProvider;

    @Mock
    private AgencyMetadataMapper agencyMetadataMapper;

    @Mock
    private UserProvider userProvider;

    @Mock
    private UserReminderDao userReminderDao;

    @Mock
    private StudentDao studentDao;

    @Mock
    private InkindDao inkindDao;

    @Mock
    private AgencyDao agencyDao;

    @Mock
    private UserDaoImpl userDao;

    @Mock
    FileSystem fileSystem;

    private static final String AGENCY_ID = "agency_1";

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetTemplateCount() {

        // 准备数据
        AssignTemplateEntity templateWithCountThree = new AssignTemplateEntity();
        templateWithCountThree.setActivityRepeatCount(3);

        AssignTemplateEntity templateWithCountTwo = new AssignTemplateEntity();
        templateWithCountTwo.setActivityRepeatCount(2);

        List<AssignTemplateEntity> templateListWithCount = Arrays.asList(templateWithCountThree, templateWithCountTwo);

        List<AssignTemplateEntity> emptyTemplateList = Collections.emptyList();

        // 3 from the first template + 2 from the second
        int expectedCount = 5;
        int actualCount = inKindAssignmentService.getTemplateCount(templateListWithCount);
        assertEquals(expectedCount, actualCount, "The count should match the expected total activity count.");

        expectedCount = 0;
        actualCount = inKindAssignmentService.getTemplateCount(emptyTemplateList);
        assertEquals(expectedCount, actualCount, "The count should be zero for an empty list.");

        actualCount = inKindAssignmentService.getTemplateCount(null);
        assertEquals(expectedCount, actualCount, "The count should be zero for a null list.");

    }


    @Test
    public void testIsOldAppVersion() {

        final int OLD_IOS_VERSION = 433;
        final int OLD_ANDROID_VERSION = 592;
        final int NEW_IOS_VERSION = 434;
        final int NEW_ANDROID_VERSION = 593;


        when(userProvider.isVersion(0, OLD_IOS_VERSION, Platform.IOS, AppType.Parents)).thenReturn(true);
        when(userProvider.isVersion(0, OLD_ANDROID_VERSION, Platform.Android, AppType.Parents)).thenReturn(true);

        Assertions.assertTrue(inKindAssignmentService.isOldAppVersion(OLD_IOS_VERSION, OLD_ANDROID_VERSION, AppType.Parents));

        when(userProvider.isVersion(0, NEW_IOS_VERSION, Platform.IOS, AppType.Parents)).thenReturn(false);
        when(userProvider.isVersion(0, OLD_ANDROID_VERSION, Platform.Android, AppType.Parents)).thenReturn(true);

        Assertions.assertTrue(inKindAssignmentService.isOldAppVersion(NEW_IOS_VERSION, OLD_ANDROID_VERSION, AppType.Parents));

        when(userProvider.isVersion(0, NEW_IOS_VERSION, Platform.IOS, AppType.Parents)).thenReturn(false);
        when(userProvider.isVersion(0, NEW_ANDROID_VERSION, Platform.Android, AppType.Parents)).thenReturn(false);

        Assertions.assertFalse(inKindAssignmentService.isOldAppVersion(NEW_IOS_VERSION, NEW_ANDROID_VERSION, AppType.Parents));

    }


    @Test
    public void testCopyRepeatTemplate() {
        AssignTemplateEntity template = new AssignTemplateEntity();
        template.setActivityRepeatCount(3);
        template.setId("1");
        template.setActivityTypeId("11");

        List<AssignTemplateEntity> originalTemplates = Arrays.asList(template);

        List<AssignTemplateEntity> copiedTemplates = inKindAssignmentService.copyRepeatTemplate(originalTemplates);

        assertEquals(3, copiedTemplates.size(), "Should create 3 copies of the template");
        copiedTemplates.forEach(copy -> assertEquals(1, copy.getActivityRepeatCount(),
                "Each copy should have an activity repeat count of 1"));

    }

    @Test
    public void testDelOldGroupAssign() {

        List<AssignEntity> assignEntityList;
        final String newGroupId = "new_group_id";
        final String oldGroupId = "old_group_id";
        String childId1 = "1";
        String childId2 = "2";

        AssignEntity newGroupAssign = new AssignEntity();
        newGroupAssign.setGroupId(newGroupId);
        newGroupAssign.setEnrollmentId(childId1);

        AssignEntity oldGroupAssign = new AssignEntity();
        oldGroupAssign.setGroupId(oldGroupId);
        oldGroupAssign.setEnrollmentId(childId2);

        assignEntityList = Arrays.asList(newGroupAssign, oldGroupAssign);

        // Setup mock behavior
        Mockito.when(studentDao.getGroupIdByChildId(childId1)).thenReturn(newGroupId);

        // Execute the method under test
        List<AssignEntity> result = inKindAssignmentService.delOldGroupAssign(assignEntityList);

        // Assertions
        assertEquals(1, result.size(), "Should return only one assignment");
        assertEquals(newGroupId, result.get(0).getGroupId(), "The returned assignment should belong to the new group");


        // Setup mock behavior to return oldGroupId for both enrollments
        Mockito.when(studentDao.getGroupIdByChildId(childId1)).thenReturn(oldGroupId);
        Mockito.when(studentDao.getGroupIdByChildId(childId2)).thenReturn(newGroupId);
        // Execute the method under test
        result = inKindAssignmentService.delOldGroupAssign(assignEntityList);
        // Assertions
        assertEquals(Collections.emptyList(), result, "Should return an empty list when all group IDs are old");


        // Execute the method under test with an empty list
        result = inKindAssignmentService.delOldGroupAssign(Collections.emptyList());
        // Assertions
        assertEquals(Collections.emptyList(), result, "Should return an empty list when input is empty");
    }


    @Test
    public void testDelOldGroupAssignmentJob() {

        String ENROLLMENT_ID = "child_1";
        String CURRENT_GROUP_ID = "group_1";

        AssignmentJobDTO currentGroupAssignment = new AssignmentJobDTO();
        currentGroupAssignment.setEnrollmentId(ENROLLMENT_ID);
        currentGroupAssignment.setGroupId(CURRENT_GROUP_ID);

        when(studentDao.getGroupIdByChildId(ENROLLMENT_ID)).thenReturn(CURRENT_GROUP_ID);

        List<AssignmentJobEntity> result = inKindAssignmentService.delOldGroupAssignmentJob(Collections.singletonList(currentGroupAssignment));

        assertEquals(1, result.size(), "Should return 1 assignment job for current group");

        String OLD_GROUP_ID = "group_2";
        AssignmentJobDTO oldGroupAssignment = new AssignmentJobDTO();
        oldGroupAssignment.setEnrollmentId(ENROLLMENT_ID);
        oldGroupAssignment.setGroupId(OLD_GROUP_ID);

        when(studentDao.getGroupIdByChildId(ENROLLMENT_ID)).thenReturn(CURRENT_GROUP_ID);

        result = inKindAssignmentService.delOldGroupAssignmentJob(Collections.singletonList(oldGroupAssignment));

        assertEquals(0, result.size(), "Should return 0 assignment jobs for old group");

        currentGroupAssignment = new AssignmentJobDTO();
        currentGroupAssignment.setEnrollmentId(ENROLLMENT_ID);
        currentGroupAssignment.setGroupId(CURRENT_GROUP_ID);

        oldGroupAssignment = new AssignmentJobDTO();
        oldGroupAssignment.setEnrollmentId(ENROLLMENT_ID);
        oldGroupAssignment.setGroupId(OLD_GROUP_ID);

        when(studentDao.getGroupIdByChildId(ENROLLMENT_ID)).thenReturn(CURRENT_GROUP_ID);

        result = inKindAssignmentService.delOldGroupAssignmentJob(Arrays.asList(currentGroupAssignment, oldGroupAssignment));

        assertEquals(1, result.size(), "Should return 1 assignment job when list contains both current and old group assignments");
    }

    @Test
    public void testInsertAssigmentJobWhenDelAssignment() {

        // Setup
        String assignmentId = "assignId1";
        List<AssignmentJobEntity> jobList = new ArrayList<>();
        AssignmentJobEntity unvalidJob1 = new AssignmentJobEntity();

        unvalidJob1.setEnrollmentId("child_id_1");
        unvalidJob1.setReportId("reprot_id_1");

        jobList.add(unvalidJob1);

        when(inkindDao.findUnValidAssignJobByAssignId(assignmentId)).thenReturn(jobList);

        List<InkindReportEntity> reportList = new ArrayList<>();

        InKindReportDTO reportDTO1 = new InKindReportDTO();
        reportDTO1.setActivityDate(new Date());
        reportDTO1.setEnrollmentId("child_id_1");
        reportDTO1.setThemeName("themeName");
        reportDTO1.setActivityNumber("activityNumber");
        reportDTO1.setActivityDescription("activityDescription");
        reportDTO1.setSourceName("sourceName");
        reportDTO1.setRateUnit("rateUnit");

        InKindReportDTO reportDTO2 = new InKindReportDTO();
        reportDTO2.setEnrollmentId("child_id_2");
        reportDTO2.setActivityDate(new Date());
        reportDTO2.setThemeName("themeName");
        reportDTO2.setActivityNumber("activityNumber");
        reportDTO2.setActivityDescription("activityDescription");
        reportDTO2.setSourceName("sourceName");
        reportDTO2.setRateUnit("rateUnit");

        reportList.add(reportDTO1);
        reportList.add(reportDTO2);


        when(inkindDao.findReportByIds(anyList())).thenReturn(reportList);


        List<AgencyEntity> agencyList = new ArrayList<>();
        AgencyEntity agencyEntity1 = new AgencyEntity();
        agencyEntity1.setId("agencyId1");
        agencyEntity1.setEnrollmentId("child_id_1");
        agencyList.add(agencyEntity1);

        when(agencyDao.getAgencyByChildIds(anyList())).thenReturn(agencyList);

        // Execute
        inKindAssignmentService.insertAssigmentJobWhenDelAssignment(assignmentId);

        // Verify
        verify(inkindDao, never()).batchCreatAssignmentJob(anyList());
        verify(inkindDao, never()).updateAssignStatus(anyList(), eq(AssignStateType.IN_PROGRESS.toString()), any(Date.class));

    }


    @Test
    public void testActivityAssignmentStatusOfParentsHigh() {
        // Preparing mock data
        AssignEntity assignEntity = new AssignEntity();
        assignEntity.setId("assignId1");
        assignEntity.setEndDate(new Date());
        assignEntity.setStartDate(new Date());


        AssignTemplateEntity assignTemplateEntity = new AssignTemplateEntity();
        assignTemplateEntity.setId("templateId1");
        assignTemplateEntity.setType(ActivityTypeValue.AT_HOME.toString());
        assignTemplateEntity.setFinish(true);

        AssignMediaEntity assignMediaEntity = new AssignMediaEntity();
        assignMediaEntity.setId("mediaId1");

        AssignmentJobEntity assignmentJobEntity = new AssignmentJobEntity();
        assignmentJobEntity.setId("jobId1");

        AssignToEntity assignToEntity = new AssignToEntity();
        assignToEntity.setAssignId("assignId1");
        assignToEntity.setGroupId("group_id");
        assignToEntity.setEnrollmentId("enrollmentId1");

        Map<String, AssignEntity> assignmentMap = new HashMap<>();
        assignmentMap.put("assignId1", assignEntity);

        Map<String, List<AssignTemplateEntity>> templateMap = new HashMap<>();
        List<AssignTemplateEntity> templateList = new ArrayList<>();
        templateList.add(assignTemplateEntity);
        templateMap.put("assignId1", templateList);

        Map<String, List<AssignMediaEntity>> mediaMap = new HashMap<>();
        List<AssignMediaEntity> mediaList = new ArrayList<>();
        mediaList.add(assignMediaEntity);
        mediaMap.put("assignId1", mediaList);

        Map<String, List<AssignmentJobEntity>> jobTemplateMap = new HashMap<>();
        List<AssignmentJobEntity> jobList = new ArrayList<>();
        jobList.add(assignmentJobEntity);
        jobTemplateMap.put("assignId1", jobList);

        List<AssignToEntity> childAssignList = new ArrayList<>();
        childAssignList.add(assignToEntity);

        UserModel user = new UserModel();
        user.setDisplayName("displayName");
        when(userDao.getUserInfo(assignEntity.getCreateUserId())).thenReturn(user);

        List<EnrollmentModel> enrollmentList = new ArrayList<>();
        EnrollmentModel enrollmentModel = new EnrollmentModel();
        enrollmentModel.setId("enrollmentId1");
        enrollmentModel.setGroupId("group_id");
        enrollmentList.add(enrollmentModel);
        when(studentDao.getAllChildrenByIds(anyList())).thenReturn(enrollmentList);


        // Call the method under test
        ActivityAssignmentStatusResponse response = inKindAssignmentService.activityAssignmentStatusOfParentsHigh(
                childAssignList, assignmentMap, templateMap, jobTemplateMap, mediaMap, new Date());

        // Assertions
        assertEquals(1, response.getAssignment().size(), "The assignment list size should be 1.");
        // Additional assertions can be added to verify other properties of the response object
    }


    @Test
    public void testActivityAssignmentStatusOfParentsLow() throws Exception {
        // Prepare test data
        Date localNow = new Date();
        List<String> childAssignIds = new ArrayList<>();
        childAssignIds.add("assign1");

        AssignEntity assignEntity = new AssignEntity();
        assignEntity.setCreatAtUtc(TimeUtil.addDays(localNow, -5));
        assignEntity.setEndDate(TimeUtil.addDays(localNow, 5));
        assignEntity.setStartDate(TimeUtil.addDays(localNow, -5));

        Map<String, AssignEntity> assignmentMap = new HashMap<>();
        assignmentMap.put("assign1", assignEntity);

        AssignTemplateEntity templateEntity = new AssignTemplateEntity();
        templateEntity.setActivityNumber("1");
        templateEntity.setActivityGroupName("activityName");
        templateEntity.setType("Type");
        templateEntity.setChildrenSubmitActivityCount(2);
        templateEntity.setFinish(false);
        templateEntity.setActivityRepeatCount(5);
        templateEntity.setType(ActivityTypeValue.AT_HOME.toString());
        List<AssignTemplateEntity> templateList = new ArrayList<>();
        templateList.add(templateEntity);

        Map<String, List<AssignTemplateEntity>> templateMap = new HashMap<>();
        templateMap.put("assign1", templateList);

        AssignmentJobEntity jobEntity = new AssignmentJobEntity();
        jobEntity.setId("job_id");

        List<AssignmentJobEntity> jobTemplateList = new ArrayList<>();
        jobTemplateList.add(jobEntity);

        Map<String, List<AssignmentJobEntity>> jobTemplateMap = new HashMap<>();
        jobTemplateMap.put("assign1", jobTemplateList);

        AssignMediaEntity mediaEntity = new AssignMediaEntity();
        mediaEntity.setFileName("file");

        List<AssignMediaEntity> mediaList = new ArrayList<>();
        mediaList.add(mediaEntity);

        Map<String, List<AssignMediaEntity>> mediaMap = new HashMap<>();
        mediaMap.put("assign1", mediaList);

        // Call the method under test
        ActivityAssignmentStatusResponse response = inKindAssignmentService.activityAssignmentStatusOfParentsLow(
                childAssignIds, assignmentMap, templateMap, jobTemplateMap, mediaMap, localNow);

        // Assert the results
        assertNotNull("Response should not be null", response);
        Assert.assertFalse("Assignment list should not be empty", response.getAssignment().isEmpty());
        AssignmentList assignmentList = response.getAssignment().get(0);
        Assert.assertEquals("Assignment ID should match", "assign1", assignmentList.getId());

    }

    @Test
    public void testActivityAssignmentStatusOfParentsLowMore() throws Exception {
        // Prepare test data
        Date localNow = new Date();
        List<String> childAssignIds = new ArrayList<>();
        childAssignIds.add("assign1");

        AssignEntity assignEntity = new AssignEntity();
        assignEntity.setCreatAtUtc(TimeUtil.addDays(localNow, -5));
        assignEntity.setEndDate(TimeUtil.addDays(localNow, 5));
        assignEntity.setStartDate(TimeUtil.addDays(localNow, -5));

        Map<String, AssignEntity> assignmentMap = new HashMap<>();
        assignmentMap.put("assign1", assignEntity);

        AssignTemplateEntity templateEntity = new AssignTemplateEntity();
        templateEntity.setActivityNumber("1");
        templateEntity.setActivityGroupName("activityName");
        templateEntity.setType("Type");
        templateEntity.setChildrenSubmitActivityCount(2);
        templateEntity.setFinish(false);
        templateEntity.setActivityRepeatCount(5);
        templateEntity.setType(ActivityTypeValue.AT_HOME.toString());

        AssignTemplateEntity templateEntity2 = new AssignTemplateEntity();
        templateEntity2.setId("template_id");
        templateEntity2.setActivityNumber("1");
        templateEntity2.setActivityGroupName("activityName");
        templateEntity2.setType("Type");
        templateEntity2.setChildrenSubmitActivityCount(2);
        templateEntity2.setFinish(false);
        templateEntity2.setActivityRepeatCount(5);
        templateEntity2.setType(ActivityTypeValue.AT_HOME.toString());
        templateEntity2.setChildrenSubmitActivityCount(10);

        List<AssignTemplateEntity> templateList = new ArrayList<>();
        templateList.add(templateEntity);
        templateList.add(templateEntity2);

        Map<String, List<AssignTemplateEntity>> templateMap = new HashMap<>();
        templateMap.put("assign1", templateList);

        AssignmentJobEntity jobEntity = new AssignmentJobEntity();
        jobEntity.setId("job_id");
        jobEntity.setTemplateId("template_id");
        AssignmentJobEntity jobEntity2 = new AssignmentJobEntity();
        jobEntity.setId("job_id2");
        jobEntity.setTemplateId("template_id");
        List<AssignmentJobEntity> jobTemplateList = new ArrayList<>();
        jobTemplateList.add(jobEntity);
        jobTemplateList.add(jobEntity2);

        Map<String, List<AssignmentJobEntity>> jobTemplateMap = new HashMap<>();
        jobTemplateMap.put("template_id", jobTemplateList);

        AssignMediaEntity mediaEntity = new AssignMediaEntity();
        mediaEntity.setFileName("file");

        List<AssignMediaEntity> mediaList = new ArrayList<>();
        mediaList.add(mediaEntity);

        Map<String, List<AssignMediaEntity>> mediaMap = new HashMap<>();
        mediaMap.put("assign1", mediaList);

        // Call the method under test
        ActivityAssignmentStatusResponse response = inKindAssignmentService.activityAssignmentStatusOfParentsLow(
                childAssignIds, assignmentMap, templateMap, jobTemplateMap, mediaMap, localNow);

        // Assert the results
        assertNotNull("Response should not be null", response);
        Assert.assertFalse("Assignment list should not be empty", response.getAssignment().isEmpty());
        AssignmentList assignmentList = response.getAssignment().get(0);
        Assert.assertEquals("Assignment ID should match", "assign1", assignmentList.getId());

    }


    @Test
    void checkedSubmitReportTest_WithValidReport() throws Exception {

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        List<AssignTemplateEntity> templateList;
        List<InkindReportEntity> reportList;
        List<AssignmentJobEntity> expectJobList;
        // Setup data for each test
        templateList = new ArrayList<>();
        templateList.add(new AssignTemplateEntity());
        templateList.get(0).setActivityDescription("test");
        templateList.get(0).setActivityNumber("123");
        templateList.get(0).setActivityTypeId("1");
        templateList.get(0).setAssignId("assignId");
        templateList.get(0).setRateUnit("unit");
        templateList.get(0).setSourceName("source");
        templateList.get(0).setThemeName("theme");
        templateList.get(0).setActivityRepeatCount(2);

        reportList = new ArrayList<>();
        reportList.add(new InkindReportEntity());
        reportList.get(0).setActivityDescription("test");
        reportList.get(0).setActivityNumber("123");
        reportList.get(0).setActivityTypeId("1");
        reportList.get(0).setActivityDate(dateFormat.parse("2022-01-01 00:00:00"));
        reportList.get(0).setSourceName("source");
        reportList.get(0).setThemeName("theme");
        reportList.get(0).setRateUnit("unit");
        reportList.get(0).setId("reportId1");
        reportList.get(0).setEnrollmentId("enrollmentId1");

        expectJobList = new ArrayList<>();
        AssignmentJobEntity jobEntity = new AssignmentJobEntity();
        jobEntity.setAssignId("assignId");
        jobEntity.setTemplateId(templateList.get(0).getId());
        jobEntity.setEnrollmentId("enrollmentId1");
        jobEntity.setReportId("reportId1");
        expectJobList.add(jobEntity);
        // Given
        when(inkindDao.findReportByParentIds(any(), any(), any(), any())).thenReturn(reportList);
        when(inkindDao.findAssignJobByReportIds(anyList())).thenReturn(new ArrayList<>());

        // When
        inKindAssignmentService.checkedSubmitReport(dateFormat.parse("2022-01-01 00:00:00"),
                dateFormat.parse("2022-01-02 00:00:00"), Arrays.asList("enrollmentId1"), "agencyId", templateList,
                new Date(), "assignId");

        // Then
        verify(inkindDao, times(0)).batchCreatAssignmentJob(expectJobList);
        verify(inkindDao, times(1)).updateAssignStatus(anyList(), anyString(), any());


        when(inkindDao.findReportByParentIds(any(), any(), any(), any())).thenReturn(new ArrayList<>());

        // When
        inKindAssignmentService.checkedSubmitReport(dateFormat.parse("2022-01-01 00:00:00"),
                dateFormat.parse("2022-01-02 00:00:00"), Arrays.asList("enrollmentId1"), "agencyId", templateList,
                new Date(), "assignId");

        // Then
        verify(inkindDao, times(1)).batchCreatAssignmentJob(anyList());
        verify(inkindDao, times(1)).updateAssignStatus(anyList(), anyString(), any());
    }


    @Test
    public void testDeleteFutureInKindShouldDeleteOldGroupAssignments() throws Exception {

        String CHILD_ID = "child_id";
        String NEW_GROUP_ID = "new_group_id";
        String OLD_GROUP_ID = "old_group_id";

        // Given
        Date now = new Date();
        AssignToEntity oldGroupAssignment = new AssignToEntity();
        oldGroupAssignment.setId("id1");
        oldGroupAssignment.setGroupId(OLD_GROUP_ID);

        List<AssignToEntity> assignToList = new ArrayList<>();
        assignToList.add(oldGroupAssignment);

        when(userProvider.getTimezoneOffsetNum()).thenReturn(0);
        when(inkindDao.findValidAssignToByGroupId(anyString(), anyString())).thenReturn(Collections.emptyList());
        when(inkindDao.findAssignToByEnrollmentIdAndStartDate(anyString(), any())).thenReturn(assignToList);

        // When
        inKindAssignmentService.deleteFutureInKind(CHILD_ID, NEW_GROUP_ID);

        // Then
        verify(inkindDao).updateChangeGroupAssignToEntity(Collections.singletonList("id1"), 1);
        verifyNoMoreInteractions(inkindDao);

        now = new Date();
        AssignToEntity newGroupAssignment = new AssignToEntity();
        newGroupAssignment.setId("id2");
        newGroupAssignment.setGroupId(NEW_GROUP_ID);

        when(userProvider.getTimezoneOffsetNum()).thenReturn(0);
        when(inkindDao.findAssignToByEnrollmentIdAndStartDate(anyString(), any())).thenReturn(Collections.emptyList());
        when(inkindDao.findValidAssignToByGroupId(anyString(), anyString())).thenReturn(Collections.singletonList(newGroupAssignment));

        // When
        inKindAssignmentService.deleteFutureInKind(CHILD_ID, NEW_GROUP_ID);

        // Then
        verify(inkindDao).updateChangeGroupAssignToEntity(Collections.singletonList("id2"), 0);


    }

    @Test
    public void listTemplateByAssignIds_ReturnsCorrectList() {

        List<String> assignIds = Arrays.asList("assignId1", "assignId2");

        List<AssignTemplateEntity> expectedAssignTemplateEntities;

        AssignTemplateEntity assignTemplateEntity1 = new AssignTemplateEntity();
        assignTemplateEntity1.setActivityTypeId("activityType1");
        assignTemplateEntity1.setAgencyId("agency1");
        assignTemplateEntity1.setId("assignId1");

        AssignTemplateEntity assignTemplateEntity2 = new AssignTemplateEntity();
        assignTemplateEntity2.setActivityTypeId("activityType2");
        assignTemplateEntity2.setAgencyId("agency2");
        assignTemplateEntity2.setId("assignId2");

        expectedAssignTemplateEntities = Arrays.asList(assignTemplateEntity1, assignTemplateEntity2);


        when(inkindDao.findPureTemplateByAssignIds(assignIds)).thenReturn(expectedAssignTemplateEntities);
        List<AssignTemplateEntity> result = inKindAssignmentService.listTemplateByAssignIds(assignIds);
        assertEquals(expectedAssignTemplateEntities, result, "The returned list should match the expected list.");

        result = inKindAssignmentService.listTemplateByAssignIds(Collections.emptyList());
        assertEquals(Collections.emptyList(), result, "Expecting an empty list when input is an empty list.");

        when(inkindDao.findPureTemplateByAssignIds(assignIds)).thenReturn(null);
        result = inKindAssignmentService.listTemplateByAssignIds(assignIds);
        assertEquals(null, result, "Expecting an empty list when DAO returns null.");

    }


    @Test
    public void testCreateOrUpdateSubmissionCutoffDayUpdatesExistingMetadata() throws Exception {
        when(userProvider.getCurrentAgencyId()).thenReturn("agencyId");
        when(userProvider.getCurrentUserId()).thenReturn("userId");
        // when(reminderParentChildDao.getByUserId("userId", "WEB_INKIND_CUT_OFF_DAY")).thenReturn(new ReminderParentChild());
        when(agencyMetadataMapper.get(anyString(), anyString())).thenReturn(new AgencyMetadata());

        SubmitActivityCutoffRequest request = new SubmitActivityCutoffRequest();
        request.setSubmissionCutoffDayOpen(true);
        request.setSubmissionCutoffDay(20);

        inKindAssignmentService.createOrUpdateSubmissionCutoffDay(request);

        verify(agencyMetadataMapper, times(2)).save(any(AgencyMetadata.class));
    }


    /**
     * 测试家长获取 in_kind 任务弹窗方法
     */
    @Test
    void testGetParentShowAlert() {
        // 模拟接口
        String currentUserId = "U0001";
        when(userProvider.getCurrentUserId()).thenReturn(currentUserId); // 模拟获取当前用户
        List<EnrollmentEntity> enrollmentEntities = new ArrayList<>();
        EnrollmentEntity enrollmentEntity = new EnrollmentEntity();
        enrollmentEntity.setId("E0001");
        enrollmentEntities.add(enrollmentEntity);
        when(studentDao.getEnrollmentsByParentId(anyString())).thenReturn(enrollmentEntities); // 模拟获取家长的小孩
        AssignEntity assignEntity = new AssignEntity();
        assignEntity.setEnrollmentId("E0001");
        assignEntity.setId("A0001");
        when(inkindDao.getAssignmentNoShowParentByEnrollmentId(anyList())).thenReturn(assignEntity); // 模拟获取家长的小孩的 InKind 任务
        UserModel userModel = new UserModel();
        userModel.setDisplayName("DisplayName");
        userModel.setRole("AGENCY_ADMIN");
        when(userDao.getUserInfo(any())).thenReturn(userModel); // 模拟获取任务创建人用户信息
        when(inkindDao.updateAssignmentShowParentByEnrollmentIds(anyList())).thenReturn(1); // 模拟更新 InKind 任务的家长弹窗状态

        // 调用方法
        ParentInKindAlertResponse parentShowAlert = inKindAssignmentService.getParentShowAlert();

        // 验证结果
        Assertions.assertEquals("E0001", parentShowAlert.getEnrollmentId()); // 验证返回的小孩 Id
        Assertions.assertEquals("A0001", parentShowAlert.getId()); // 验证返回的 InKind 任务 Id
        Assertions.assertTrue(parentShowAlert.isShowAlert()); // 验证返回的是否显示弹窗
        Assertions.assertEquals(userModel.getDisplayName(), parentShowAlert.getTeacherName()); // 验证返回的创建人姓名
        Assertions.assertEquals("Agency Admin", parentShowAlert.getRole()); // 验证返回的创建人角色
    }

    /**
     * 测试获取提醒家长提交 In-Kind 任务弹窗方法
     */
    @Test
    void testGetRemindParentSubmitAssignment() {
        // 配置参数
        String userId = "U0001"; // 用户 Id
        // 设置班级
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId("G001");
        // 设置小孩对象
        EnrollmentEntity enrollmentEntity = new EnrollmentEntity();
        enrollmentEntity.setId("E0001");
        enrollmentEntity.setAgencyId("A0001");
        enrollmentEntity.setGroup(groupEntity);
        EnrollmentEntity enrollmentEntity1 = new EnrollmentEntity();
        enrollmentEntity1.setAgencyId("A0002");
        enrollmentEntity1.setId("E0002");
        enrollmentEntity1.setGroup(groupEntity);
        List<EnrollmentEntity> enrollmentEntities = new ArrayList<>();
        enrollmentEntities.add(enrollmentEntity);
        enrollmentEntities.add(enrollmentEntity1);
        // 设置小孩 ids 数组
        List<String> enrollmentIds = new ArrayList<>();
        enrollmentIds.add("E0001");
        enrollmentIds.add("E0002");
        String localNowDate = TimeUtil.format(TimeUtil.getUtcNow(), TimeUtil.format10);

        // 机构参数
        AgencyEntity agencyEntity = new AgencyEntity();
        agencyEntity.setId("A0001");
        agencyEntity.setEnrollmentId("E0001");
        AgencyEntity agencyEntity1 = new AgencyEntity();
        agencyEntity1.setId("A0002");
        agencyEntity.setEnrollmentId("E0002");
        List<AgencyEntity> agencyEntities = new ArrayList<>();
        agencyEntities.add(agencyEntity);
        agencyEntities.add(agencyEntity1);
        // 机构 ids 集合
        List<String> agencyIds = new ArrayList<>();
        agencyIds.add("A0001");
        agencyIds.add("A0002");
        // 开通 In-Kind 的机构 ids 集合
        List<String> openInKindAgencyIds = new ArrayList<>();
        openInKindAgencyIds.add("A0001");

        // 进行中任务集合
        List<AssignEntity> assignEntities = new ArrayList<>();
        AssignEntity assignEntity = new AssignEntity();
        assignEntity.setId("A001");
        assignEntity.setEnrollmentId("E0001");
        assignEntity.setAgencyId("A0001");
        assignEntity.setStartDate(TimeUtil.parse(TimeUtil.getUtcNow(), TimeUtil.format10));
        assignEntity.setEndDate(TimeUtil.parse(TimeUtil.getUtcNow(), TimeUtil.format10));
        assignEntity.setGroupId("G001");
        assignEntities.add(assignEntity);
        // 进行中的任务 ids 集合
        List<String> inProgressAssignIds = new ArrayList<>();
        inProgressAssignIds.add("A001");

        // 提交的 In-Kind 任务集合
        List<AssignmentJobEntity> assignmentJobEntities = new ArrayList<>();
        AssignmentJobEntity assignmentJobEntity = new AssignmentJobDTO();
        assignmentJobEntity.setAssignId("A001");
        assignmentJobEntity.setId("J001");
        assignmentJobEntity.setTemplateId("T001");
        assignmentJobEntity.setEnrollmentId("E0001");
        assignmentJobEntities.add(assignmentJobEntity);

        List<AssignmentJobDTO> assignmentJobDTOList = new ArrayList<>();
        AssignmentJobDTO assignmentJobDTO = new AssignmentJobDTO();
        assignmentJobDTO.setAssignId("A001");
        assignmentJobDTO.setId("J001");
        assignmentJobDTO.setTemplateId("T001");
        assignmentJobDTO.setGroupId("G001");
        assignmentJobDTO.setEnrollmentId("E0001");
        assignmentJobDTOList.add(assignmentJobDTO);

        // 任务模版
        List<AssignTemplateEntity> templateEntities = new ArrayList<>();
        AssignTemplateEntity templateEntity = new AssignTemplateEntity();
        templateEntity.setId("T001");
        templateEntity.setAssignId("A001");
        templateEntities.add(templateEntity);
        AssignTemplateEntity templateEntity1 = new AssignTemplateEntity();
        templateEntity1.setId("T002");
        templateEntity1.setAssignId("A001");
        templateEntities.add(templateEntity1);

        when(userProvider.getCurrentUserId()).thenReturn(userId); // 模拟获取当前用户
        // 家长没有待提醒的 In-Kind 任务
        when(userReminderDao.getUserNeedRemindCountByUserIdAndReminderType(userId, UserRemindType.PARENT_SUBMIT_ASSIGNMENT.toString())).thenReturn(new Long(0)); // 模拟获取家长需要提交 InKind 任务的数量
        // 模拟接口
        RemindParentSubmitAssignmentResponse response = inKindAssignmentService.getRemindParentSubmitAssignment();
        Assertions.assertFalse(response.isShowAlert());

        // 家长有创建提醒的 In-Kind 任务，不提醒提交任务
        when(userReminderDao.getUserNeedRemindCountByUserIdAndReminderType(userId, UserRemindType.PARENT_SUBMIT_ASSIGNMENT.toString())).thenReturn(new Long(1)); // 模拟获取家长需要提交 InKind 任务的数量
        when(studentDao.getChildByParent(userId)).thenReturn(enrollmentEntities); // 模拟获取家长的小孩
        when(inkindDao.getAssignmentNoShowParentByEnrollmentId(enrollmentIds)).thenReturn(new AssignEntity()); // 模拟获取家长的小孩的 InKind 任务
        RemindParentSubmitAssignmentResponse response1 = inKindAssignmentService.getRemindParentSubmitAssignment();
        Assertions.assertFalse(response1.isShowAlert());

        // 没有进行中的任务，不提醒提交任务
        when(userReminderDao.getUserNeedRemindCountByUserIdAndReminderType(userId, UserRemindType.PARENT_SUBMIT_ASSIGNMENT.toString())).thenReturn(new Long(1)); // 模拟获取家长需要提交 InKind 任务的数量
        when(studentDao.getChildByParent(userId)).thenReturn(enrollmentEntities); // 模拟获取家长的小孩
        when(inkindDao.getAssignmentNoShowParentByEnrollmentId(enrollmentIds)).thenReturn(null); // 模拟获取家长的小孩的 InKind 任务
        when(agencyDao.getAgencyByChildIds(enrollmentIds)).thenReturn(agencyEntities); // 模拟获取家长的小孩的机构
        when(agencyDao.getAgencyIsOpenByAgencyIds(AgencyMetaKey.INKIND_OPEN.toString(), agencyIds)).thenReturn(openInKindAgencyIds);
        // 根据小孩 ids 集合, 获取进行中的任务
        when(inkindDao.getCurrentAssignByEnrollmentIdsAndDate(enrollmentIds, localNowDate)).thenReturn(new ArrayList<>());

        RemindParentSubmitAssignmentResponse response2 = inKindAssignmentService.getRemindParentSubmitAssignment();
        Assertions.assertFalse(response2.isShowAlert());

        // 任务全部提交，不提醒提交任务
        when(inkindDao.getCurrentAssignByEnrollmentIdsAndDate(enrollmentIds, localNowDate)).thenReturn(assignEntities);
        when(inkindDao.getTemplateCountByAssignIdsAndEnrollmentIds(inProgressAssignIds, enrollmentIds)).thenReturn(1);
        when(inkindDao.getAssignJobByEnrollmentIdsAndAssignIds(enrollmentIds, inProgressAssignIds)).thenReturn(assignmentJobDTOList);
        RemindParentSubmitAssignmentResponse response3 = inKindAssignmentService.getRemindParentSubmitAssignment();
        Assertions.assertFalse(response3.isShowAlert());

        // when(inKindAssignmentService.delOldGroupAssign(assignEntities)).thenReturn(assignEntities);
        when(studentDao.getGroupIdByChildId(anyString())).thenReturn("G001");
        when(inkindDao.getTemplateCountByAssignIdsAndEnrollmentIds(inProgressAssignIds, enrollmentIds)).thenReturn(10);
        when(inkindDao.getAssignJobByEnrollmentIdsAndAssignIds(enrollmentIds, inProgressAssignIds)).thenReturn(assignmentJobDTOList);
        when(inkindDao.findTemplateByAssignId(inProgressAssignIds)).thenReturn(templateEntities);
        RemindParentSubmitAssignmentResponse response4 = inKindAssignmentService.getRemindParentSubmitAssignment();
        // 断言 showAlert 应该为 true
        Assertions.assertTrue(response4.isShowAlert());
        // 断言 progress 应该为 0.5
        Assertions.assertEquals(0.1F, response4.getProgress());
        // 断言 children 集合的大小应该为 1
        Assertions.assertEquals(1, response4.getChildren().size());

    }

    @Test
    public void testGetAssignmentRepeatOpenInit() {

        AuthUserDetails authUserDetails = new AuthUserDetails();
        authUserDetails.setAgencyId("agency_id");
        when(userProvider.getCurrentUser()).thenReturn(authUserDetails);
        AgencyMetadata metaData = new AgencyMetadata();
        when(agencyMetadataMapper.get("agency_id", "INKIND_ACTIVITY_REPEAT_OPEN")).thenReturn(null);

        AssignmentRepeatResponse response = inKindAssignmentService.getAssignmentRepeatOpen(false);

        assertNotNull(response);
        // Additional assertions can be added to verify the behavior and the result of the method
    }

    @Test
    public void testUpdateAssignmentRepeatOpenInit() {

        AuthUserDetails authUserDetails = new AuthUserDetails();
        authUserDetails.setAgencyId("agency_id");
        when(userProvider.getCurrentUser()).thenReturn(authUserDetails);
        AgencyMetadata metaData = new AgencyMetadata();
        when(agencyMetadataMapper.get("agency_id", "INKIND_ACTIVITY_REPEAT_OPEN")).thenReturn(null);

        AssignmentRepeatRequest assignmentRepeatRequest = new AssignmentRepeatRequest();
        assignmentRepeatRequest.setMetaValue("true");
        inKindAssignmentService.updateAssignmentRepeatOpen(assignmentRepeatRequest);

        metaData.setAgencyId("agency_id");
        metaData.setMetaKey("INKIND_ACTIVITY_REPEAT_OPEN");
        metaData.setMetaValue("true");
        // Assert
        verify(agencyMetadataMapper, times(2)).save(metaData);
    }


    @Test
    public void testCheckAssignmentBeforeCreate_time() {
        // Arrange
        CreateAssignmentRequest request = new CreateAssignmentRequest();
        request.setStartDate(TimeUtil.addDays(new Date(), -5));
        request.setEndDate(TimeUtil.addDays(new Date(), 5));

        List<AssignChildInfo> childrenInfos = new ArrayList<>();
        AssignChildInfo assignChildInfo = new AssignChildInfo();
        assignChildInfo.setId("child_id_1");
        childrenInfos.add(assignChildInfo);
        request.setChildrenInfo(childrenInfos);

        LocalDate start = LocalDate.parse(TimeUtil.format(request.getStartDate(), "yyyy-MM-dd"));
        LocalDate end = LocalDate.parse(TimeUtil.format(request.getEndDate(), "yyyy-MM-dd"));
        // 获得传入时间的 月份的 第一天 与最后一天
        Date first = TimeUtil.localDateToDate((start));
        Date last = TimeUtil.localDateToDate(end);

        AssignEntity assignEntity = new AssignEntity();
        assignEntity.setId("assign_id");
        assignEntity.setStartDate(TimeUtil.addDays(new Date(), -3));
        assignEntity.setEndDate(TimeUtil.addDays(new Date(), 2));

        ArrayList<AssignEntity> assignList = new ArrayList<>();
        assignList.add(assignEntity);

        when(inkindDao.getAssignmentByAgencyId(first, last, "")).thenReturn(assignList);


        // when(userProvider.getCurrentUserId()).thenReturn("user_id");
        // when(userProvider.checkUser("user_id")).thenReturn(new UserEntity());


        List<AssignToEntity> toList = new ArrayList<>();
        AssignToEntity assignToEntity = new AssignToEntity();
        assignToEntity.setAssignId("assign_id");
        assignToEntity.setEnrollmentId("child_id_1");
        toList.add(assignToEntity);
        when(inkindDao.findAssignToByAssignId(anyList())).thenReturn(toList);
        when(inKindProvider.getAgencyId()).thenReturn("");

        // Act
        CheckAssignmentBeforeCreateResponse response = inKindAssignmentService.checkAssignmentBeforeCreate(request);

        // Assert
        assertNotNull(response);
    }

    @Test
    public void testActivityAssignmentStatusOfParentsSuccess() {

        String startDate = "2024-06-20";
        String endDate = "2024-06-21";
        String childId = "child_id";
        String agencyId = "agency_1";

        when(userProvider.getTimezoneOffsetNum()).thenReturn(0);

        List<AssignEntity> assignmentList = new ArrayList<>();
        AssignEntity assign1 = new AssignEntity();
        assign1.setId("assign_id_1");
        assign1.setStartDate(TimeUtil.parseDate(startDate));
        assign1.setEndDate(TimeUtil.parseDate(endDate));
        assignmentList.add(assign1);

        AgencyEntity agency = new AgencyEntity();
        agency.setId(agencyId);
        when(agencyDao.getAgencyByChildId_V2(childId)).thenReturn(agency);

        when(inkindDao.getAssignmentByAgencyId(TimeUtil.parseDate(startDate), TimeUtil.parseDate(endDate), agencyId)).thenReturn(assignmentList);


        List<AssignToEntity> childAssignList = new ArrayList<>();
        AssignToEntity assignToEntity = new AssignToEntity();
        assignToEntity.setAssignId("assign_id_1");
        childAssignList.add(assignToEntity);
        List<String> assignmentIdList = new ArrayList<>();
        assignmentIdList.add("assign_id_1");
        when(inkindDao.findAssignToByEnrollmentId(childId, assignmentIdList)).thenReturn(childAssignList);

        List<String> childAssignIds = childAssignList.stream().map(x -> x.getAssignId()).collect(Collectors.toList());
        List<AssignTemplateEntity> templateList = new ArrayList<>();
        AssignTemplateEntity template = new AssignTemplateEntity();
        template.setAssignId("assign_id_1");
        template.setType("AT_HOME");
        templateList.add(template);
        when(inkindDao.findTemplateByAssignId(childAssignIds)).thenReturn(templateList);

        List<AssignmentJobEntity> jobList = new ArrayList<>();
        when(inkindDao.findAssignJobByEnrollmentId(childId, childAssignIds)).thenReturn(jobList);

        List<AssignMediaEntity> medias = new ArrayList<>();
        AssignMediaEntity media = new AssignMediaEntity();
        media.setMediaUrl("media.a");
        media.setAssignId("assign_id_1");
        medias.add(media);

        when(inkindDao.findAssignMediaByAssignId(childAssignIds)).thenReturn(medias);

        when(userProvider.isVersion(0, InKindConstant.OLD_PARENT_IOS_VERSION_ABOUT_REPEAT_ASSIGNMENT_TEMPLATE, Platform.IOS, AppType.Parents)).thenReturn(true);

        ActivityAssignmentStatusResponse actualResponse =
                inKindAssignmentService.activityAssignmentStatusOfParents(startDate, endDate, childId, agencyId);
        // Assert
        assertNotNull(actualResponse);

    }

    @Test
    public void getTemplateOtherMinutesOpen_whenMetadataExist_shouldReturnTrue() {
        // Arrange
        AgencyMetadata agencyMetadata = new AgencyMetadata();
        agencyMetadata.setAgencyId(AGENCY_ID);
        agencyMetadata.setMetaKey("INKIND_TEMPLATE_OTHER_MINUTES_OPEN");
        agencyMetadata.setMetaValue("true");

        when(agencyMetadataMapper.get(AGENCY_ID, "INKIND_TEMPLATE_OTHER_MINUTES_OPEN")).thenReturn(Optional.of(agencyMetadata).get());

        // Act
        GetTemplateOtherMinutesOpenResponse response = inKindAssignmentService.getTemplateOtherMinutesOpen(AGENCY_ID);

        // Assert
        assertTrue(response.isTemplateOtherMinutes());
        verify(agencyMetadataMapper).get(AGENCY_ID, "INKIND_TEMPLATE_OTHER_MINUTES_OPEN");
    }

    @Test
    public void getTemplateOtherMinutesOpen_whenMetadataNotExist_shouldInsertAndReturnTrue() {
        // Arrange
        when(agencyMetadataMapper.get(AGENCY_ID, "INKIND_TEMPLATE_OTHER_MINUTES_OPEN")).thenReturn(null);

        // Act
        GetTemplateOtherMinutesOpenResponse response = inKindAssignmentService.getTemplateOtherMinutesOpen(AGENCY_ID);

        // Assert
        assertTrue(response.isTemplateOtherMinutes());
        verify(agencyMetadataMapper, times(1)).save(any(AgencyMetadata.class));
    }

    @Test
    public void getTemplateOtherMinutesOpen_whenMetaValueIsFalse_shouldReturnFalse() {
        // Arrange
        AgencyMetadata agencyMetadata = new AgencyMetadata();
        agencyMetadata.setAgencyId(AGENCY_ID);
        agencyMetadata.setMetaKey("INKIND_TEMPLATE_OTHER_MINUTES_OPEN");
        agencyMetadata.setMetaValue("false");

        when(agencyMetadataMapper.get(AGENCY_ID, "INKIND_TEMPLATE_OTHER_MINUTES_OPEN")).thenReturn(Optional.of(agencyMetadata).get());

        // Act
        GetTemplateOtherMinutesOpenResponse response = inKindAssignmentService.getTemplateOtherMinutesOpen(AGENCY_ID);

        // Assert
        assertFalse(response.isTemplateOtherMinutes());
        verify(agencyMetadataMapper).get(AGENCY_ID, "INKIND_TEMPLATE_OTHER_MINUTES_OPEN");
    }


    @Test
    public void testUpdateTemplateOtherMinutesOpenSuccess() {
        when(userProvider.getCurrentUser()).thenReturn(new AuthUserDetails() {
            @Override
            public String getAgencyId() {
                return AGENCY_ID;
            }
        });
        // Arrange
        when(userProvider.getCurrentUser()).thenReturn(new AuthUserDetails());
        TemplateOtherMinutesOpenRequest request = new TemplateOtherMinutesOpenRequest();
        request.setTemplateOtherMinutes(true);
        // Act
        inKindAssignmentService.updateTemplateOtherMinutesOpen(request);

        // Assert
        verify(agencyMetadataMapper, times(1)).save(any(AgencyMetadata.class));
    }

    @Test
    public void testUpdateTemplateOtherMinutesOpenWhenUserNotLoggedIn() {
        // Arrange
        when(userProvider.getCurrentUser()).thenReturn(null);
        TemplateOtherMinutesOpenRequest request = new TemplateOtherMinutesOpenRequest();
        request.setTemplateOtherMinutes(true);
        // Act & Assert
        Assertions.assertThrows(RuntimeException.class, () -> inKindAssignmentService.updateTemplateOtherMinutesOpen(request),
                "Expected to throw an exception when user is not logged in");
    }

    @Test
    public void testUpdateTemplateOtherMinutesOpenWithInvalidRequest() {
        // Arrange
        when(userProvider.getCurrentUser()).thenReturn(new AuthUserDetails());
        TemplateOtherMinutesOpenRequest request = new TemplateOtherMinutesOpenRequest();
        request.setTemplateOtherMinutes(true);
        // Act & Assert
        Assertions.assertDoesNotThrow(() -> inKindAssignmentService.updateTemplateOtherMinutesOpen(request),
                "Expected method not to throw for a null templateOtherMinutes value");
    }

    @Test
    public void testCreateOrUpdateSubmissionCutoffDayCreatesNewMetadata() throws Exception {
        when(userProvider.getCurrentAgencyId()).thenReturn("agencyId");
        when(userProvider.getCurrentUserId()).thenReturn("userId");
        // when(reminderParentChildDao.getByUserId("userId", "WEB_INKIND_CUT_OFF_DAY")).thenReturn(new ReminderParentChild());
        SubmitActivityCutoffRequest request = new SubmitActivityCutoffRequest();
        request.setSubmissionCutoffDayOpen(true);
        request.setSubmissionCutoffDay(15);

        inKindAssignmentService.createOrUpdateSubmissionCutoffDay(request);

        verify(agencyMetadataMapper, times(2)).save(any(AgencyMetadata.class));
    }

    @Test
    public void testCreateOrUpdateSubmissionCutoffDayClosesSubmission() throws Exception {
        when(userProvider.getCurrentAgencyId()).thenReturn("agencyId");
        when(userProvider.getCurrentUserId()).thenReturn("userId");
        when(agencyMetadataMapper.get(anyString(), anyString())).thenReturn(new AgencyMetadata());
        // when(reminderParentChildDao.getByUserId("userId", "WEB_INKIND_CUT_OFF_DAY")).thenReturn(new ReminderParentChild());
        SubmitActivityCutoffRequest request = new SubmitActivityCutoffRequest();
        request.setSubmissionCutoffDayOpen(false);
        request.setSubmissionCutoffDay(10);

        inKindAssignmentService.createOrUpdateSubmissionCutoffDay(request);

        ArgumentCaptor<AgencyMetadata> metaDataCaptor = ArgumentCaptor.forClass(AgencyMetadata.class);
        verify(agencyMetadataMapper).save(metaDataCaptor.capture());
        AgencyMetadata agencyMetadata = metaDataCaptor.getValue();
        agencyMetadata.setAgencyId("agencyId");
        agencyMetadata.setMetaValue("false");
        assertEquals("agencyId", agencyMetadata.getAgencyId());
        assertEquals(Boolean.FALSE.toString(), agencyMetadata.getMetaValue());
    }

    @Test
    public void testCreateOrUpdateLimitSubmitActivityTimeOpenTrue() {
        AuthUserDetails authUserDetails = new AuthUserDetails();
        authUserDetails.setAgencyId(AGENCY_ID);
        // Setup
        LimitSubmitActivityTimeRequest request = new LimitSubmitActivityTimeRequest();
        request.setLimitSubmitActivityTimeOpen(true);
        request.setLimitSubmitActivityTimeType("custom");
        request.setLimitSubmitActivityTime(10);
        // Execution
        inKindAssignmentService.createOrUpdateLimitSubmitActivityTime(request);

        // Verification
        verify(agencyMetadataMapper, times(2)).save(any(AgencyMetadata.class));
    }

    @Test
    public void testCreateOrUpdateLimitSubmitActivityTimeOpenFalseWithValidDate() {
        AuthUserDetails authUserDetails = new AuthUserDetails();
        authUserDetails.setAgencyId(AGENCY_ID);
        // Setup
        LimitSubmitActivityTimeRequest request = new LimitSubmitActivityTimeRequest();
        request.setLimitSubmitActivityTimeOpen(false);
        request.setLimitSubmitActivityTimeType("custom");
        request.setLimitSubmitActivityTime(10);

        // Execution
        inKindAssignmentService.createOrUpdateLimitSubmitActivityTime(request);
    }

    @Test
    public void testCreateOrUpdateLimitSubmitActivityTimeOpenFalseWithInvalidDate() {
        AuthUserDetails authUserDetails = new AuthUserDetails();
        authUserDetails.setAgencyId(AGENCY_ID);
        // Setup
        LimitSubmitActivityTimeRequest request = new LimitSubmitActivityTimeRequest();
        request.setLimitSubmitActivityTimeOpen(false);
        request.setLimitSubmitActivityTimeType("unCustom");
        request.setLimitSubmitActivityTime(10);

        // Execution
        inKindAssignmentService.createOrUpdateLimitSubmitActivityTime(request);
    }

    @Test
    public void testCreateOrUpdateLimitSubmitActivityTimeWithNoDateProvided() {
        AuthUserDetails authUserDetails = new AuthUserDetails();
        authUserDetails.setAgencyId(AGENCY_ID);
        // Setup
        LimitSubmitActivityTimeRequest request = new LimitSubmitActivityTimeRequest();
        request.setLimitSubmitActivityTimeOpen(true);

        // Execution
        inKindAssignmentService.createOrUpdateLimitSubmitActivityTime(request);
    }


    @Test
    public void getLimitSubmitActivityTime_whenLimitTimeOpenIsFalse_shouldReturnNull() {
        AgencyMetadata agencyMetadata = new AgencyMetadata();
        agencyMetadata.setMetaValue("custom-80");
        when(agencyMetadataMapper.get("agencyId", AgencyMetaKey.INKIND_LIMIT_SUBMIT_ACTIVITY_TIME_OPEN.toString())).thenReturn(agencyMetadata);
        when(agencyMetadataMapper.get("agencyId", AgencyMetaKey.INKIND_LIMIT_SUBMIT_ACTIVITY_TIME.toString())).thenReturn(agencyMetadata);
        assertNotNull(inKindAssignmentService.getLimitSubmitActivityTime("agencyId"));
    }

    @Test
    public void getLimitSubmitActivityTime_whenLimitTimeValueIsNotValid_shouldReturnNull() {
        AgencyMetadata limitTimeOpenMetaData = new AgencyMetadata();
        limitTimeOpenMetaData.setMetaValue("true");

        AgencyMetadata limitTimeMetaData = new AgencyMetadata();
        limitTimeMetaData.setMetaValue("custom-75");

        when(agencyMetadataMapper.get("agencyId", AgencyMetaKey.INKIND_LIMIT_SUBMIT_ACTIVITY_TIME_OPEN.toString()))
                .thenReturn(limitTimeOpenMetaData);
        when(agencyMetadataMapper.get("agencyId", AgencyMetaKey.INKIND_LIMIT_SUBMIT_ACTIVITY_TIME.toString()))
                .thenReturn(limitTimeMetaData);

        assertNotNull(inKindAssignmentService.getLimitSubmitActivityTime("agencyId"));
    }

    @Test
    public void getLimitSubmitActivityTime_whenDataIsValid_shouldReturnExpectedResponse() {
        AgencyMetadata limitTimeOpenMetaData = new AgencyMetadata();
        limitTimeOpenMetaData.setMetaValue("true");

        String[] limitTimeValueArray = {"month", "60"};
        AgencyMetadata limitTimeMetaData = new AgencyMetadata();
        limitTimeMetaData.setMetaValue(String.join("-", limitTimeValueArray));

        AgencyMetadata agencyMetadata = new AgencyMetadata();
        agencyMetadata.setMetaValue("custom-80");
        when(agencyMetadataMapper.get("agencyId", AgencyMetaKey.INKIND_LIMIT_SUBMIT_ACTIVITY_TIME_OPEN.toString())).thenReturn(agencyMetadata);
        when(agencyMetadataMapper.get("agencyId", AgencyMetaKey.INKIND_LIMIT_SUBMIT_ACTIVITY_TIME.toString())).thenReturn(agencyMetadata);

        LimitSubmitActivityTimeResponse response = inKindAssignmentService.getLimitSubmitActivityTime("agencyId");

        assertNotNull(response);
        assertFalse(response.getLimitSubmitActivityTimeOpen());
        Assert.assertEquals("custom", response.getLimitSubmitActivityTimeType());
        Assert.assertEquals(String.valueOf(80), response.getLimitSubmitActivityTime().toString());
    }

    @Test
    public void getLimitSubmitActivityTime_whenLimitTimeOpenMetaDataIsNull_shouldReturnNull() {
        AgencyMetadata agencyMetadata = new AgencyMetadata();
        agencyMetadata.setMetaValue("custom-80");
        when(agencyMetadataMapper.get("agencyId", AgencyMetaKey.INKIND_LIMIT_SUBMIT_ACTIVITY_TIME_OPEN.toString())).thenReturn(agencyMetadata);
        when(agencyMetadataMapper.get("agencyId", AgencyMetaKey.INKIND_LIMIT_SUBMIT_ACTIVITY_TIME.toString())).thenReturn(agencyMetadata);
        assertNotNull(inKindAssignmentService.getLimitSubmitActivityTime("agencyId"));
    }

    @Test
    public void getLimitSubmitActivityTime_whenLimitTimeMetaDataIsNull_shouldReturnNull() {
        AgencyMetadata agencyMetadata = new AgencyMetadata();
        agencyMetadata.setMetaValue("custom-80");

        when(agencyMetadataMapper.get("agencyId", AgencyMetaKey.INKIND_LIMIT_SUBMIT_ACTIVITY_TIME_OPEN.toString())).thenReturn(agencyMetadata);
        when(agencyMetadataMapper.get("agencyId", AgencyMetaKey.INKIND_LIMIT_SUBMIT_ACTIVITY_TIME.toString())).thenReturn(agencyMetadata);

        assertNotNull(inKindAssignmentService.getLimitSubmitActivityTime("agencyId"));
    }


    @Test
    public void getLimitSubmitActivityTime_ValidAgencyId_ReturnsResponse() {
        LimitSubmitActivityTimeResponse expectedResponse = new LimitSubmitActivityTimeResponse();
        expectedResponse.setLimitSubmitActivityTimeOpen(true);
        expectedResponse.setLimitSubmitActivityTimeType("custom");
        expectedResponse.setLimitSubmitActivityTime(10);

        // when(userProvider.getCurrentAgencyId()).thenReturn(AGENCY_ID);

        LimitSubmitActivityTimeResponse actualResponse = inKindAssignmentService.getLimitSubmitActivityTime(AGENCY_ID);

        assertNull(actualResponse);
    }

    @Test
    public void testListTemplateSubmitInfo_NoChildren() {
        // 设置模拟返回值
        when(userProvider.getCurrentUserId()).thenReturn("parentId");
        when(studentDao.getChildByParent("parentId")).thenReturn(Collections.emptyList());

        // 调用方法
        List<InKindActivitySubmitInfoResponse> result = inKindAssignmentService.listTemplateSubmitInfo("1");

        // 断言结果
        assertTrue(result.isEmpty());
    }

    @Test
    public void testListTemplateSubmitInfo_WithChildren() {
        // 设置模拟返回值
        String parentId = "parent_id";
        String childId = "child_id";
        String agencyId = "agency_id";

        EnrollmentEntity enrollmentEntity = new EnrollmentEntity();
        enrollmentEntity.setId(childId);

        AgencyEntity agencyEntity = new AgencyEntity();
        agencyEntity.setId(agencyId);
        agencyEntity.setEnrollmentId(childId);

        when(userProvider.getCurrentUserId()).thenReturn(parentId);
        when(studentDao.getChildByParent(parentId)).thenReturn(Collections.singletonList(enrollmentEntity));
        when(agencyDao.getAgencyByChildIds(Collections.singletonList(childId))).thenReturn(Collections.singletonList(agencyEntity));

        SubmitActivityCutoffResponse cutoffResponse = new SubmitActivityCutoffResponse();
        cutoffResponse.setSubmissionCutoffDayOpen(true);
        cutoffResponse.setSubmissionCutoffDay(30);

        LimitSubmitActivityTimeResponse limitTimeResponse = new LimitSubmitActivityTimeResponse();
        limitTimeResponse.setLimitSubmitActivityTimeOpen(true);
        limitTimeResponse.setLimitSubmitActivityTime(45);

        GetTemplateOtherMinutesOpenResponse templateOtherMinutesOpenResponse = new GetTemplateOtherMinutesOpenResponse();

        // 调用方法
        List<InKindActivitySubmitInfoResponse> result = inKindAssignmentService.listTemplateSubmitInfo("1");

        // 断言结果
        assertFalse(result.isEmpty());
        Assert.assertEquals(1, result.size());
        InKindActivitySubmitInfoResponse response = result.get(0);
        Assert.assertEquals(childId, response.getEnrollmentId());
        assertNull(response.getCutoffTime());
        Assert.assertEquals(null, response.getLimitTime());
        Assert.assertEquals(Arrays.asList("15", "30", "45", "60", "75", "Other"), response.getSubmitTimes());
    }


    @Test
    public void getSubmissionCutoffDay_ReturnsCorrectResponse() {
        String agencyId = "testAgencyId";
        String userId = "testUserId";
        when(userProvider.getCurrentAgencyId()).thenReturn(agencyId);
        when(userProvider.getCurrentUserId()).thenReturn(userId);

        UserMetaDataEntity metaDataEntity = new UserMetaDataEntity();
        metaDataEntity.setMetaValue("true");
        when(userDao.getMetaData(userId, UserMetaKey.INKIND_CUT_OFF_DAY_NEW_TIP.toString())).thenReturn(metaDataEntity);
        SubmitActivityCutoffResponse submitActivityCutoffResponse = new SubmitActivityCutoffResponse();
        when(inKindProvider.getSubmissionCutoffDay(agencyId, false)).thenReturn(submitActivityCutoffResponse);

        SubmitActivityCutoffResponse response = inKindAssignmentService.getSubmissionCutoffDay();

        assertNotNull(response);
        assertTrue(response.isNeedRemind());
        verify(userDao).getMetaData(userId, UserMetaKey.INKIND_CUT_OFF_DAY_NEW_TIP.toString());
    }

    @Test
    public void getSubmissionCutoffDay_NeedRemindFalse() {
        String agencyId = "testAgencyId";
        String userId = "testUserId";
        when(userProvider.getCurrentAgencyId()).thenReturn(agencyId);
        when(userProvider.getCurrentUserId()).thenReturn(userId);

        UserMetaDataEntity metaDataEntity = new UserMetaDataEntity();
        metaDataEntity.setMetaValue("false");
        when(userDao.getMetaData(userId, UserMetaKey.INKIND_CUT_OFF_DAY_NEW_TIP.toString())).thenReturn(metaDataEntity);
        SubmitActivityCutoffResponse submitActivityCutoffResponse = new SubmitActivityCutoffResponse();
        when(inKindProvider.getSubmissionCutoffDay(agencyId, false)).thenReturn(submitActivityCutoffResponse);

        SubmitActivityCutoffResponse response = inKindAssignmentService.getSubmissionCutoffDay();

        assertFalse(response.isNeedRemind());
        verify(userDao).getMetaData(userId, UserMetaKey.INKIND_CUT_OFF_DAY_NEW_TIP.toString());
    }

    @Test
    public void getSubmissionCutoffDay_NoMetaData() {
        String agencyId = "testAgencyId";
        String userId = "testUserId";
        when(userProvider.getCurrentAgencyId()).thenReturn(agencyId);
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userDao.getMetaData(userId, UserMetaKey.INKIND_CUT_OFF_DAY_NEW_TIP.toString())).thenReturn(null);
        SubmitActivityCutoffResponse submitActivityCutoffResponse = new SubmitActivityCutoffResponse();
        when(inKindProvider.getSubmissionCutoffDay(agencyId, false)).thenReturn(submitActivityCutoffResponse);

        SubmitActivityCutoffResponse response = inKindAssignmentService.getSubmissionCutoffDay();

        assertTrue(response.isNeedRemind());
        verify(userDao).getMetaData(userId, UserMetaKey.INKIND_CUT_OFF_DAY_NEW_TIP.toString());
        verify(userDao).setMetaData(userId, UserMetaKey.INKIND_CUT_OFF_DAY_NEW_TIP.toString(), Boolean.TRUE.toString());
    }


    @Test
    public void testCheckAssignmentBeforeCreate_id() {

        // Arrange
        CreateAssignmentRequest request = new CreateAssignmentRequest();
        request.setId("assignment_id");


        when(inkindDao.checkAssignmentById(request.getId(), 0)).thenReturn(0);
        // Act
        CheckAssignmentBeforeCreateResponse response = inKindAssignmentService.checkAssignmentBeforeCreate(request);

        // Assert
        assertNotNull(response);

    }

    @Test
    public void testGetReminderParentSubmitReport_WithUserReminder() {
        // Mock user reminder entity
        UserReminderEntity userReminderEntity = new UserReminderEntity();
        userReminderEntity.setNeedRemind(true);
        userReminderEntity.setReminderContent("Please submit the report");
        AuthUserDetails user = new AuthUserDetails();
        user.setAgencyId("agencyId");
        user.setUsername("parent_Id");

        when(userProvider.getCurrentUser()).thenReturn(user);
        when(userReminderDao.getByUserIdAndType("parent_Id", UserRemindType.PARENT_INKIND_REPORT.toString())).thenReturn(userReminderEntity);

        ReminderParentSubmitReportResponse response = inKindAssignmentService.getReminderParentSubmitReport();

        Assert.assertEquals(true, response.isReminderState());
        Assert.assertEquals("Please submit the report", response.getReminderContent());
    }

    @Test
    public void testUpdateReminderParentSubmitReport() {

        AuthUserDetails user = new AuthUserDetails();
        user.setUsername("user_id");

        when(userProvider.getCurrentUser()).thenReturn(user);
        // 调用 updateReminderParentSubmitReport 方法
        inKindAssignmentService.updateReminderParentSubmitReport();

        // 验证 userReminderDao 的 updateByUserIdAndType 方法是否被正确调用
        verify(userReminderDao).updateByUserIdAndType("user_id", UserRemindType.PARENT_INKIND_REPORT.toString());
    }


}

package com.learninggenie.api.service.impl;

import com.google.common.collect.Maps;
import com.learninggenie.api.model.ExportSftpAccountInfoResponse;
import com.learninggenie.api.model.ExportSftpCsvFileModel;
import com.learninggenie.api.model.ExportSftpDataModel;
import com.learninggenie.api.model.ExportSftpExportDataResponse;
import com.learninggenie.api.model.ExportSftpResultModel;
import com.learninggenie.api.service.ExportSftpService;
import com.learninggenie.common.data.dao.AgencyDao;
import com.learninggenie.common.data.dao.DomainDao;
import com.learninggenie.common.data.dao.export.ExportSftpSettingDao;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.entity.AgencyEntity;
import com.learninggenie.common.data.entity.AgencyMetaDataEntity;
import com.learninggenie.common.data.entity.KeyMeasureSettingEntity;
import com.learninggenie.common.data.entity.export.ExportSftpRecord;
import com.learninggenie.common.data.entity.export.ExportSftpSetting;
import com.learninggenie.common.data.enums.AgencyMetaKey;
import com.learninggenie.common.data.enums.ExportSftpExportType;
import com.learninggenie.common.data.model.KeyMeasureEntity;
import com.learninggenie.common.data.model.UserModel;
import com.learninggenie.common.filesystem.FileSystem;
import com.learninggenie.common.messaging.EmailModel;
import com.learninggenie.common.messaging.EmailService;
import com.learninggenie.common.score.RatingService;
import com.learninggenie.common.utils.FileUtil;
import com.learninggenie.common.utils.SFTPUtil;
import com.learninggenie.common.utils.TimeUtil;
import org.jetbrains.annotations.NotNull;
import org.junit.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedConstruction;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static junit.framework.Assert.assertNotNull;
import static net.sf.ezmorph.test.ArrayAssertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mockConstruction;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * AutoExportSftpServiceImplTest
 */
@ExtendWith(MockitoExtension.class)
@RunWith(MockitoJUnitRunner.class)
public class AutoExportSftpServiceImplTest {

    @Mock
    private ExportSftpSettingDao exportSftpSettingDao;

    @Mock
    private UserDaoImpl userDaoImpl;

    @Mock
    private AgencyDao agencyDao;

    @Mock
    private ExportSftpService exportSftpService;

    @Mock
    private FileSystem fileSystem;

    @Mock
    private EmailService emailService;

    @Mock
    private DomainDao domainDao;

    @Mock
    private RatingService ratingService;


    @InjectMocks
    private AutoExportSftpServiceImpl exportSftp;

    /**
     * 测试导出 IMMEDIATELY 类型的数据
     *
     * @throws Exception 异常
     */
    @Test
    public void testExportIMMEDIATELY() throws Exception {
        // 设置 mock 数据
        String settingId = "settingId";
        ExportSftpExportType exportType = ExportSftpExportType.IMMEDIATELY;
        Date exportDate = new Date();

        ExportSftpSetting exportSetting = getExportSftpSetting();

        ExportSftpSetting setting = getExportSftpSetting();

        AgencyEntity agency = new AgencyEntity();
        agency.setName("agencyName");

        ExportSftpResultModel expectedResult = new ExportSftpResultModel();
        expectedResult.setCenterCount(1);
        expectedResult.setGroupCount(1);

        // 设置 ExportSftpDataModel 结果
        ExportSftpDataModel sampleData = getExportSftpDataModel();

        List<ExportSftpDataModel> sampleDataList = new ArrayList<>();
        sampleDataList.add(sampleData);
        // 设置返回结果
        ExportSftpExportDataResponse exportDataResponse = new ExportSftpExportDataResponse();
        exportDataResponse.setModels(sampleDataList);

        // 设置 ExportSftpAccountInfoResponse 结果
        ExportSftpAccountInfoResponse accountInfoResponse = new ExportSftpAccountInfoResponse();
        accountInfoResponse.setHost("host");
        accountInfoResponse.setPort("6379");
        accountInfoResponse.setUsername("username");
        accountInfoResponse.setPassword("password");

        // 获取导出设置数据
        when(exportSftpSettingDao.getById(eq(settingId))).thenReturn(exportSetting);
        when(agencyDao.getById(eq("tenantId"))).thenReturn(agency);
        when(exportSftpService.getExportData(eq("userId"), isNull(), eq(exportType))).thenReturn(exportDataResponse);
        lenient().when(exportSftpService.getSftpAccountInfo(eq("userId"))).thenReturn(accountInfoResponse);
        lenient().when(fileSystem.getPublicUrl(anyString())).thenReturn("fileUrl");
        List<KeyMeasureSettingEntity> keyMeasureSettingEntities = new ArrayList<>();
        KeyMeasureSettingEntity keyMeasureSettingEntity = new KeyMeasureSettingEntity();
        keyMeasureSettingEntity.setId("key001");
        keyMeasureSettingEntity.setFrameworkId("frameworkId");
        keyMeasureSettingEntity.setAgencyId("agencyId");
        keyMeasureSettingEntities.add(keyMeasureSettingEntity);
        when(domainDao.getSettingFramework(any())).thenReturn(keyMeasureSettingEntities);
        List<KeyMeasureEntity> keyMeasureEntities = new ArrayList<>();
        KeyMeasureEntity keyMeasureEntity = new KeyMeasureEntity();
        keyMeasureEntity.setId("measure001");
        keyMeasureEntity.setKeyMeasureSettingId("key001");
        keyMeasureEntity.setAgencyId("agencyId");
        keyMeasureEntities.add(keyMeasureEntity);
        when(domainDao.getKeyMeasuresSetting(anyList())).thenReturn(keyMeasureEntities);
        // when(exportSftpSettingDao.getSettingByTenantId(anyString())).thenReturn(setting);
//        when(ratingService.isOUSDFramework(anyString())).thenReturn(false);
        // SFTP 封装类
        try (MockedConstruction<SFTPUtil> drdpServiceMockedConstruction = mockConstruction(SFTPUtil.class, (mock, context) -> {
            when(mock.login()).thenReturn("success");
            doNothing().when(mock).moveFiles(anyString(), any());
            doNothing().when(mock).uploadAndArchive(anyString(), any(), anyString(), anyString(), any());
            doNothing().when(mock).logout();
        })) {
            ExportSftpResultModel result = exportSftp.export(settingId, exportType, exportDate);
            assertEquals(expectedResult.getCenterCount(), result.getCenterCount());
            assertEquals(expectedResult.getGroupCount(), result.getGroupCount());
            assertEquals("null", result.getSampleDataJson());

            verify(exportSftpSettingDao).getById(eq(settingId));
            verify(agencyDao).getById(eq("tenantId"));
            verify(exportSftpService).getExportData(eq("userId"), isNull(), eq(exportType));
            verify(exportSftpSettingDao).getById(anyString());
            verify(exportSftpService, times(1)).getExportData(anyString(), any(), any());
        }
    }

    /**
     * 获取 ExportSftpSetting 示例数据
     *
     * @return ExportSftpSetting 示例数据
     */
    @NotNull
    private static ExportSftpSetting getExportSftpSetting() {
        ExportSftpSetting exportSetting = new ExportSftpSetting();
        exportSetting.setUserId("userId");
        exportSetting.setTenantId("tenantId");
        exportSetting.setGroupJson("{\n" +
                "    \"settingId\":\"settingId\",\n" +
                "    \"isExportAll\":false,\n" +
                "    \"centerModels\":[\n" +
                "        {\n" +
                "            \"centerId\":\"centerId\",\n" +
                "            \"centerName\":\"centerName\",\n" +
                "            \"isSelectAllGroups\":false,\n" +
                "            \"groupModels\":[\n" +
                "                {\n" +
                "                    \"groupId\":\"groupId\",\n" +
                "                    \"groupName\":\"groupName\",\n" +
                "                    \"groupIsDeleted\":false,\n" +
                "                    \"groupIsInactive\":false,\n" +
                "                    \"groupIsOutside\":false\n" +
                "                }\n" +
                "]}],\"exportGradeStageIds\":[\"exportGradeStageIds\"]}");
        exportSetting.setMapJson("{}");
        return exportSetting;
    }


    /**
     * 测试导出 SAMPLE 类型的数据
     *
     * @throws Exception 异常
     */
    @Test
    public void testExportSAMPLE() throws Exception {
        // 设置 mock 数据
        String settingId = "settingId";
        ExportSftpExportType exportType = ExportSftpExportType.SAMPLE;
        Date exportDate = new Date();

        ExportSftpSetting exportSetting = new ExportSftpSetting();
        exportSetting.setUserId("userId");
        exportSetting.setTenantId("tenantId");

        AgencyEntity agency = new AgencyEntity();
        agency.setName("agencyName");

        ExportSftpResultModel expectedResult = new ExportSftpResultModel();
        expectedResult.setCenterCount(0);
        expectedResult.setGroupCount(0);

        // 设置 ExportSftpDataModel 结果
        ExportSftpDataModel sampleData = getExportSftpDataModel();

        List<ExportSftpDataModel> sampleDataList = new ArrayList<>();
        sampleDataList.add(sampleData);
        // 设置返回结果
        ExportSftpExportDataResponse exportDataResponse = new ExportSftpExportDataResponse();
        exportDataResponse.setModels(sampleDataList);

        // 设置 ExportSftpAccountInfoResponse 结果
        ExportSftpAccountInfoResponse accountInfoResponse = new ExportSftpAccountInfoResponse();
        accountInfoResponse.setHost("host");
        accountInfoResponse.setPort("6379");
        accountInfoResponse.setUsername("username");
        accountInfoResponse.setPassword("password");


        when(exportSftpSettingDao.getById(eq(settingId))).thenReturn(exportSetting);
        when(agencyDao.getById(eq("tenantId"))).thenReturn(agency);
        // SFTP 封装类
        try (MockedConstruction<SFTPUtil> drdpServiceMockedConstruction = mockConstruction(SFTPUtil.class, (mock, context) -> {
            when(mock.login()).thenReturn("success");
            doNothing().when(mock).moveFiles(anyString(), any());
            doNothing().when(mock).uploadAndArchive(anyString(), any(), anyString(), anyString(), any());
            doNothing().when(mock).logout();
        })) {
            ExportSftpResultModel result = exportSftp.export(settingId, exportType, exportDate);

            assertEquals(expectedResult.getCenterCount(), result.getCenterCount());
            assertEquals(expectedResult.getGroupCount(), result.getGroupCount());
            assertEquals("null", result.getSampleDataJson());

            verify(exportSftpSettingDao).getById(eq(settingId));
            verify(agencyDao).getById(eq("tenantId"));
            verify(exportSftpService, times(2)).getSampleData(eq("userId"), eq(settingId));
            verify(exportSftpSettingDao).getById(anyString());
        }
    }

    /**
     * 获取 ExportSftpDataModel 示例数据
     *
     * @return ExportSftpDataModel 示例数据
     */
    @NotNull
    private static ExportSftpDataModel getExportSftpDataModel() {
        ExportSftpDataModel sampleData = new ExportSftpDataModel();
        sampleData.setCenterId("centerId");
        sampleData.setGroupGrade("grade");
        sampleData.setFrameworkId("frameworkId");
        sampleData.setGroupGrade("groupGrade");
        sampleData.setStageId("stageId");
        sampleData.setCenterName("centerName");
        sampleData.setFrameworkName("frameworkName");
        sampleData.setGroupIds(Collections.singletonList("groupId"));

        ArrayList<String> tableHeader = new ArrayList<>();
        tableHeader.add("key1");
        sampleData.setTableHeader(tableHeader);

        ArrayList<Map<String, String>> tableData = new ArrayList<>();
        Map<String, String> tableDataMap = Maps.newHashMap();
        tableDataMap.put("key1", "value321");
        tableData.add(tableDataMap);
        sampleData.setTableData(tableData);
        return sampleData;
    }

    /**
     * 测试导出 SCHEDULE 类型的数据
     */
    @Test
    public void sendExportSuccessEmail_ExportTypeIsSchedule() {
        ExportSftpRecord successExport = new ExportSftpRecord();
        successExport.setExportType(ExportSftpExportType.SCHEDULE.toString());
        String dataTime = "2023-06-27 21:00:00";
        Date parse = TimeUtil.parse(dataTime, TimeUtil.dateFormat);
        successExport.setScheduledExportTime(parse);
        successExport.setSchoolYear("2023");
        successExport.setSettingId("settingId");
        successExport.setTenantId("tenantId");

        List<String> originAgencyName = new ArrayList<>();
        originAgencyName.add("AgencyName");

        ExportSftpResultModel resultModel = new ExportSftpResultModel();
        resultModel.setCenterCount(1);
        resultModel.setGroupCount(1);
        resultModel.setGroupJson("{\n" +
                "  \"settingId\": \"settingId_3daa0f97b3f9\",\n" +
                "  \"isExportAll\": false,\n" +
                "  \"centerModels\": [\n" +
                "    {\n" +
                "      \"centerId\": \"centerId_9267f35a802e\",\n" +
                "      \"centerName\": \"centerName_ceeeafe2680b\",\n" +
                "      \"isSelectAllGroups\": false,\n" +
                "      \"groupModels\": [\n" +
                "        {\n" +
                "          \"groupId\": \"groupId_256e996b4254\",\n" +
                "          \"groupName\": \"groupName_120fe46f5547\",\n" +
                "          \"groupIsDeleted\": false,\n" +
                "          \"groupIsInactive\": false,\n" +
                "          \"groupIsOutside\": false,\n" +
                "          \"groupIsNoChild\": false\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ],\n" +
                "  \"exportGradeStageIds\": [\n" +
                "    \"exportGradeStageIds_339c60d15706\"\n" +
                "  ]\n" +
                "}");
        ExportSftpSetting settingEntity = new ExportSftpSetting();
        settingEntity.setNotifyEmails("email1,email2");
        settingEntity.setUserId("userId");

        UserModel userModel = new UserModel();
        userModel.setEmail("userEmail");

        ReflectionTestUtils.setField(exportSftp, "webServer", "webServer");

        when(exportSftpSettingDao.getById("settingId")).thenReturn(settingEntity);
        when(userDaoImpl.getUserById("userId")).thenReturn(userModel);

        try {
            exportSftp.sendExportSuccessEmail(successExport, originAgencyName, resultModel);
        } catch (Exception e) {
            e.printStackTrace();
        }

        // Verify the email is sent with the correct parameters
        verify(emailService, times(1)).sendAsync(any(EmailModel.class));
    }

    /**
     * 测试导出 IMMEDIATELY 类型的数据
     */
    @Test
    public void sendExportSuccessEmail_ExportTypeIsImmediately() {
        ExportSftpRecord successExport = new ExportSftpRecord();
        successExport.setExportType(ExportSftpExportType.IMMEDIATELY.toString());
        successExport.setScheduledExportTime(null);
        successExport.setSchoolYear("2023");
        successExport.setSettingId("settingId");
        successExport.setTenantId("tenantId");

        List<String> originAgencyName = new ArrayList<>();
        originAgencyName.add("AgencyName");

        ExportSftpResultModel resultModel = new ExportSftpResultModel();
        resultModel.setCenterCount(1);
        resultModel.setGroupCount(1);
        resultModel.setGroupJson("{\n" +
                "  \"settingId\": \"settingId_3daa0f97b3f9\",\n" +
                "  \"isExportAll\": false,\n" +
                "  \"centerModels\": [\n" +
                "    {\n" +
                "      \"centerId\": \"centerId_9267f35a802e\",\n" +
                "      \"centerName\": \"centerName_ceeeafe2680b\",\n" +
                "      \"isSelectAllGroups\": false,\n" +
                "      \"groupModels\": [\n" +
                "        {\n" +
                "          \"groupId\": \"groupId_256e996b4254\",\n" +
                "          \"groupName\": \"groupName_120fe46f5547\",\n" +
                "          \"groupIsDeleted\": false,\n" +
                "          \"groupIsInactive\": false,\n" +
                "          \"groupIsOutside\": false,\n" +
                "          \"groupIsNoChild\": false\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ],\n" +
                "  \"exportGradeStageIds\": [\n" +
                "    \"exportGradeStageIds_339c60d15706\"\n" +
                "  ]\n" +
                "}");

        ExportSftpSetting settingEntity = new ExportSftpSetting();
        settingEntity.setNotifyEmails("email1,email2");
        settingEntity.setUserId("userId");

        UserModel userModel = new UserModel();
        userModel.setEmail("userEmail");

        ReflectionTestUtils.setField(exportSftp, "webServer", "webServer");

        when(exportSftpSettingDao.getById("settingId")).thenReturn(settingEntity);
        when(userDaoImpl.getUserById("userId")).thenReturn(userModel);

        try {
            exportSftp.sendExportSuccessEmail(successExport, originAgencyName, resultModel);
        } catch (Exception e) {
            e.printStackTrace();
        }

        // Verify the email is sent with the correct parameters
        verify(emailService, times(1)).sendAsync(any(EmailModel.class));
    }

    /**
     * 测试导出 SAMPLE 类型的数据
     */
    @Test
    public void sendExportSuccessEmail_EmptyOriginAgencyName() {
        ExportSftpRecord successExport = new ExportSftpRecord();
        successExport.setExportType(ExportSftpExportType.SCHEDULE.toString());
        String dataTime = "2023-06-27 21:00:00";
        Date parse = TimeUtil.parse(dataTime, TimeUtil.dateFormat);
        successExport.setScheduledExportTime(parse);
        successExport.setSchoolYear("2023");
        successExport.setSettingId("settingId");
        successExport.setTenantId("tenantId");

        List<String> originAgencyName = new ArrayList<>();
        ReflectionTestUtils.setField(exportSftp, "webServer", "webServer");

        ExportSftpResultModel resultModel = new ExportSftpResultModel();
        resultModel.setCenterCount(1);
        resultModel.setGroupCount(1);

        try {
            exportSftp.sendExportSuccessEmail(successExport, originAgencyName, resultModel);
        } catch (Exception e) {
            e.printStackTrace();
        }

        // Verify that no email is sent
        // Verify the email is sent with the correct parameters
        verify(emailService, times(0)).sendAsync(any(EmailModel.class));
    }

    /**
     * 测试导出 SAMPLE 类型的数据
     */
    @Test
    public void sendExportSuccessEmail_EmptySiteAndClassCount() {
        ExportSftpRecord successExport = new ExportSftpRecord();
        successExport.setExportType(ExportSftpExportType.SCHEDULE.toString());
        String dataTime = "2023-06-27 21:00:00";
        Date parse = TimeUtil.parse(dataTime, TimeUtil.dateFormat);
        successExport.setScheduledExportTime(parse);
        successExport.setSchoolYear("2023");
        successExport.setSettingId("settingId");
        successExport.setTenantId("tenantId");

        List<String> originAgencyName = new ArrayList<>();
        originAgencyName.add("AgencyName");

        ReflectionTestUtils.setField(exportSftp, "webServer", "webServer");

        ExportSftpResultModel resultModel = new ExportSftpResultModel();
        resultModel.setCenterCount(0);
        resultModel.setGroupCount(0);

        try {
            exportSftp.sendExportSuccessEmail(successExport, originAgencyName, resultModel);
        } catch (Exception e) {
            e.printStackTrace();
        }

        // Verify that no email is sent
        verify(emailService, times(0)).sendAsync(any(EmailModel.class));
    }

    /**
     * 测试导出 SAMPLE 类型的数据
     */
    @Test
    public void sendExportSuccessEmail_EmptyEmailTemplateVersion() {
        ExportSftpRecord successExport = new ExportSftpRecord();
        successExport.setExportType(ExportSftpExportType.SCHEDULE.toString());
        String dataTime = "2023-06-27 21:00:00";
        Date parse = TimeUtil.parse(dataTime, TimeUtil.dateFormat);
        successExport.setScheduledExportTime(parse);
        successExport.setSchoolYear("2023");
        successExport.setSettingId("settingId");
        successExport.setTenantId("tenantId");

        List<String> originAgencyName = new ArrayList<>();
        originAgencyName.add("AgencyName");

        ReflectionTestUtils.setField(exportSftp, "webServer", "webServer");

        ExportSftpResultModel resultModel = new ExportSftpResultModel();
        resultModel.setCenterCount(1);
        resultModel.setGroupCount(1);
        resultModel.setGroupJson("{\n" +
                "  \"settingId\": \"settingId_3daa0f97b3f9\",\n" +
                "  \"isExportAll\": false,\n" +
                "  \"centerModels\": [\n" +
                "    {\n" +
                "      \"centerId\": \"centerId_9267f35a802e\",\n" +
                "      \"centerName\": \"centerName_ceeeafe2680b\",\n" +
                "      \"isSelectAllGroups\": false,\n" +
                "      \"groupModels\": [\n" +
                "        {\n" +
                "          \"groupId\": \"groupId_256e996b4254\",\n" +
                "          \"groupName\": \"groupName_120fe46f5547\",\n" +
                "          \"groupIsDeleted\": false,\n" +
                "          \"groupIsInactive\": false,\n" +
                "          \"groupIsOutside\": false,\n" +
                "          \"groupIsNoChild\": false\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ],\n" +
                "  \"exportGradeStageIds\": [\n" +
                "    \"exportGradeStageIds_339c60d15706\"\n" +
                "  ]\n" +
                "}");
        ExportSftpSetting settingEntity = new ExportSftpSetting();
        settingEntity.setNotifyEmails("email1,email2");
        settingEntity.setUserId("userId");

        UserModel userModel = new UserModel();
        userModel.setEmail("userEmail");

        when(exportSftpSettingDao.getById("settingId")).thenReturn(settingEntity);
        when(userDaoImpl.getUserById(settingEntity.getUserId())).thenReturn(userModel);

        try {
            exportSftp.sendExportSuccessEmail(successExport, originAgencyName, resultModel);
        } catch (Exception e) {
            e.printStackTrace();
        }

        // Verify the email is sent with the correct parameters
        verify(emailService, times(1)).sendAsync(any(EmailModel.class));
    }


    /**
     * 测试将数据转换为文件模型
     * 测试 sample 的导出类型，并且 All Sites 是开启的状态的情况
     */
    @Test
    public void testConvertDataToFileModels_WithSampleAndOpenAllSite() {
        // 准备测试数据
        // 准备 getExportData 的返回值，ExportSftpDataModel 是要导出的数据
        List<ExportSftpDataModel> mockModelList = new ArrayList<>();
        // 构造 ExportSftpDataModel
        ExportSftpDataModel mockModel = new ExportSftpDataModel();
        // 设置 ExportSftpDataModel 的属性
        // 设置学校 Id
        mockModel.setCenterId("centerId");
        // 设置学校名称
        mockModel.setCenterName("centerName");
        // 设置年级
        mockModel.setGroupGrade("groupGrade");
        // 设置阶段 Id
        mockModel.setStageId("stageId");
        // 设置班级 Id 的集合
        mockModel.setGroupIds(Collections.singletonList("groupIds"));
        // 设置框架 Id
        mockModel.setFrameworkId("frameworkId");
        // 设置框架名称
        mockModel.setFrameworkName("frameworkName");
        // 设置表头
        mockModel.setTableHeader(Collections.singletonList("tableHeader"));
        // 设置表数据
        mockModel.setTableData(Collections.singletonList(Collections.singletonMap("key", "value")));

        // 添加到 mockModelList 中
        mockModelList.add(mockModel);
        // 准备 convertDataToFileModels 的返回值，ExportSftpCsvFileModel 是要导出的文件
        List<ExportSftpCsvFileModel> mockFileModelList = new ArrayList<>();

        // 定义要导出的类型
        ExportSftpExportType exportType = ExportSftpExportType.SAMPLE;
        // 定义导出时间
        Date exportDate = new Date();
        // 定义导出根路径
        String rootPath = FileUtil.randomTempFilePath("rootPath/");
        // 定义导出的机构是否开启了导出所有学校的功能
        AgencyMetaDataEntity exportAllSitesOpen = new AgencyMetaDataEntity();
        // 设置导出所有学校的功能的属性
        exportAllSitesOpen.setMetaValue("true");

        List<KeyMeasureSettingEntity> keyMeasureSettingEntities = new ArrayList<>();
        KeyMeasureSettingEntity keyMeasureSettingEntity = new KeyMeasureSettingEntity();
        keyMeasureSettingEntity.setId("key001");
        keyMeasureSettingEntity.setFrameworkId("frameworkId");
        keyMeasureSettingEntity.setAgencyId("agencyId");
        keyMeasureSettingEntities.add(keyMeasureSettingEntity);
        when(domainDao.getSettingFramework(any())).thenReturn(keyMeasureSettingEntities);
        List<KeyMeasureEntity> keyMeasureEntities = new ArrayList<>();
        KeyMeasureEntity keyMeasureEntity = new KeyMeasureEntity();
        keyMeasureEntity.setId("measure001");
        keyMeasureEntity.setKeyMeasureSettingId("key001");
        keyMeasureEntity.setAgencyId("agencyId");
        keyMeasureEntities.add(keyMeasureEntity);
        when(domainDao.getKeyMeasuresSetting(anyList())).thenReturn(keyMeasureEntities);
        ExportSftpSetting setting = getExportSftpSetting();
        // when(exportSftpSettingDao.getSettingByTenantId(anyString())).thenReturn(setting);
//        when(ratingService.isOUSDFramework(anyString())).thenReturn(false);

        exportSftp.convertDataToFileModels(exportType, exportDate, mockModelList, mockFileModelList, rootPath, exportAllSitesOpen, "agencyId", "originAgencyName", false, setting);

        // 校验 mockFileModelList 中是否包含数据
        assertEquals(1, mockFileModelList.size());
        ExportSftpCsvFileModel fileModel = mockFileModelList.get(0);
        // 校验 sftpPath 和 localRootPath 是否正确
        String sftpPath = fileModel.getSftpPath();
        // 校验 sftpPath
        assertEquals(sftpPath, "Sample Data/");
        String localRootPath = fileModel.getLocalRootPath();
        // 校验 localRootPath
        assertEquals(localRootPath, rootPath);
        // 校验完成之后删除文件
        // 删除生成各个 CSV 文件
        FileUtil.deleteDir(rootPath);
    }


    /**
     * 测试将数据转换为文件模型
     * 测试 Immediately 的导出类型，并且 All Sites 是开启的状态的情况
     */
    @Test
    public void testConvertDataToFileModels_WithImmediatelyAndOpenAllSite() {
        // 准备测试数据
        // 准备 getExportData 的返回值，ExportSftpDataModel 是要导出的数据
        List<ExportSftpDataModel> mockModelList = new ArrayList<>();
        // 构造 ExportSftpDataModel
        ExportSftpDataModel mockModel = new ExportSftpDataModel();
        // 设置 ExportSftpDataModel 的属性
        // 设置学校 Id
        mockModel.setCenterId("centerId");
        // 设置学校名称
        mockModel.setCenterName("centerName");
        // 设置年级
        mockModel.setGroupGrade("groupGrade");
        // 设置阶段 Id
        mockModel.setStageId("stageId");
        // 设置班级 Id 的集合
        mockModel.setGroupIds(Collections.singletonList("groupIds"));
        // 设置框架 Id
        mockModel.setFrameworkId("frameworkId");
        // 设置框架名称
        mockModel.setFrameworkName("frameworkName");
        // 设置表头
        mockModel.setTableHeader(Collections.singletonList("tableHeader"));
        // 设置表数据
        mockModel.setTableData(Collections.singletonList(Collections.singletonMap("key", "value")));

        // 添加到 mockModelList 中
        mockModelList.add(mockModel);

        List<KeyMeasureSettingEntity> keyMeasureSettingEntities = new ArrayList<>();
        KeyMeasureSettingEntity keyMeasureSettingEntity = new KeyMeasureSettingEntity();
        keyMeasureSettingEntity.setId("key001");
        keyMeasureSettingEntity.setFrameworkId("frameworkId");
        keyMeasureSettingEntity.setAgencyId("agencyId");
        keyMeasureSettingEntities.add(keyMeasureSettingEntity);
        when(domainDao.getSettingFramework(any())).thenReturn(keyMeasureSettingEntities);
        List<KeyMeasureEntity> keyMeasureEntities = new ArrayList<>();
        KeyMeasureEntity keyMeasureEntity = new KeyMeasureEntity();
        keyMeasureEntity.setId("measure001");
        keyMeasureEntity.setKeyMeasureSettingId("key001");
        keyMeasureEntity.setAgencyId("agencyId");
        keyMeasureEntities.add(keyMeasureEntity);
        when(domainDao.getKeyMeasuresSetting(anyList())).thenReturn(keyMeasureEntities);

        // 准备 convertDataToFileModels 的返回值，ExportSftpCsvFileModel 是要导出的文件
        List<ExportSftpCsvFileModel> mockFileModelList = new ArrayList<>();

        // 定义要导出的类型
        ExportSftpExportType exportType = ExportSftpExportType.IMMEDIATELY;
        // 定义导出时间
        Date exportDate = new Date();
        // 定义导出根路径
        String rootPath = FileUtil.randomTempFilePath("rootPath/");
        // 定义导出的机构是否开启了导出所有学校的功能
        AgencyMetaDataEntity exportAllSitesOpen = new AgencyMetaDataEntity();
        // 设置导出所有学校的功能的属性
        exportAllSitesOpen.setMetaValue("true");
        ExportSftpSetting setting = getExportSftpSetting();
        // when(exportSftpSettingDao.getSettingByTenantId(anyString())).thenReturn(setting);
//        when(ratingService.isOUSDFramework(anyString())).thenReturn(false);

        exportSftp.convertDataToFileModels(exportType, exportDate, mockModelList, mockFileModelList, rootPath, exportAllSitesOpen, "agencyId", "originAgencyName", false, setting);

        // 校验 mockFileModelList 中是否包含数据
        assertEquals(1, mockFileModelList.size());
        ExportSftpCsvFileModel fileModel = mockFileModelList.get(0);
        // 校验 sftpPath 和 localRootPath 是否正确
        String sftpPath = fileModel.getSftpPath();
        // 校验 sftpPath
        assertEquals(sftpPath, "Latest Exported Data/centerName/groupGrade/");
        String localRootPath = fileModel.getLocalRootPath();
        // 校验 localRootPath
        assertEquals(localRootPath, rootPath);
        // 校验完成之后删除文件
        // 删除生成各个 CSV 文件
        FileUtil.deleteDir(rootPath);
    }


    /**
     * 测试将数据转换为文件模型
     * 测试 Immediately 的导出类型，并且 All Sites 是关闭的状态的情况
     */
    @Test
    public void testConvertDataToFileModels_WithImmediatelyAndNotOpenAllSite() {
        // 准备测试数据
        // 准备 getExportData 的返回值，ExportSftpDataModel 是要导出的数据
        List<ExportSftpDataModel> mockModelList = new ArrayList<>();
        // 构造 ExportSftpDataModel
        ExportSftpDataModel mockModel = new ExportSftpDataModel();
        // 设置 ExportSftpDataModel 的属性
        // 设置学校 Id
        mockModel.setCenterId("centerId");
        // 设置学校名称
        mockModel.setCenterName("centerName");
        // 设置年级
        mockModel.setGroupGrade("groupGrade");
        // 设置阶段 Id
        mockModel.setStageId("stageId");
        // 设置班级 Id 的集合
        mockModel.setGroupIds(Collections.singletonList("groupIds"));
        // 设置框架 Id
        mockModel.setFrameworkId("frameworkId");
        // 设置框架名称
        mockModel.setFrameworkName("frameworkName");
        // 设置表头
        mockModel.setTableHeader(Collections.singletonList("tableHeader"));
        // 设置表数据
        mockModel.setTableData(Collections.singletonList(Collections.singletonMap("key", "value")));

        // 添加到 mockModelList 中
        mockModelList.add(mockModel);
        // 准备 convertDataToFileModels 的返回值，ExportSftpCsvFileModel 是要导出的文件
        List<ExportSftpCsvFileModel> mockFileModelList = new ArrayList<>();

        // 定义要导出的类型
        ExportSftpExportType exportType = ExportSftpExportType.IMMEDIATELY;
        // 定义导出时间
        Date exportDate = new Date();
        // 定义导出根路径
        String rootPath = FileUtil.randomTempFilePath("rootPath/");
        // 定义导出的机构是否开启了导出所有学校的功能
        AgencyMetaDataEntity exportAllSitesOpen = new AgencyMetaDataEntity();
        // 设置导出所有学校的功能的属性
        exportAllSitesOpen.setMetaValue("false");

        List<KeyMeasureSettingEntity> keyMeasureSettingEntities = new ArrayList<>();
        KeyMeasureSettingEntity keyMeasureSettingEntity = new KeyMeasureSettingEntity();
        keyMeasureSettingEntity.setId("key001");
        keyMeasureSettingEntity.setFrameworkId("frameworkId");
        keyMeasureSettingEntity.setAgencyId("agencyId");
        keyMeasureSettingEntities.add(keyMeasureSettingEntity);
        when(domainDao.getSettingFramework(any())).thenReturn(keyMeasureSettingEntities);
        List<KeyMeasureEntity> keyMeasureEntities = new ArrayList<>();
        KeyMeasureEntity keyMeasureEntity = new KeyMeasureEntity();
        keyMeasureEntity.setId("measure001");
        keyMeasureEntity.setKeyMeasureSettingId("key001");
        keyMeasureEntity.setAgencyId("agencyId");
        keyMeasureEntities.add(keyMeasureEntity);
        when(domainDao.getKeyMeasuresSetting(anyList())).thenReturn(keyMeasureEntities);
        ExportSftpSetting setting = getExportSftpSetting();
        // when(exportSftpSettingDao.getSettingByTenantId(anyString())).thenReturn(setting);
//        when(ratingService.isOUSDFramework(anyString())).thenReturn(false);

        exportSftp.convertDataToFileModels(exportType, exportDate, mockModelList, mockFileModelList, rootPath, exportAllSitesOpen, "agencyId", "originAgencyName", false, setting);

        // 校验 mockFileModelList 中是否包含数据
        assertEquals(1, mockFileModelList.size());
        ExportSftpCsvFileModel fileModel = mockFileModelList.get(0);
        // 校验 sftpPath 和 localRootPath 是否正确
        String sftpPath = fileModel.getSftpPath();
        // 校验 sftpPath
        assertEquals(sftpPath, "Latest Exported Data/");
        String localRootPath = fileModel.getLocalRootPath();
        // 校验 localRootPath
        assertNotNull(localRootPath);
        // 校验完成之后删除文件
        // 删除生成各个 CSV 文件
        FileUtil.deleteDir(rootPath);
    }


    /**
     * 测试将数据转换为文件模型
     * 测试 VerifyData 的导出类型
     */
    @Test
    public void testConvertDataToFileModels_WithVerifyData() {
        // 准备测试数据
        // 准备 getExportData 的返回值，ExportSftpDataModel 是要导出的数据
        List<ExportSftpDataModel> mockModelList = new ArrayList<>();
        // 构造 ExportSftpDataModel
        ExportSftpDataModel mockModel = new ExportSftpDataModel();
        // 设置 ExportSftpDataModel 的属性
        // 设置学校 Id
        mockModel.setCenterId("centerId");
        // 设置学校名称
        mockModel.setCenterName("centerName");
        // 设置年级
        mockModel.setGroupGrade("groupGrade");
        // 设置阶段 Id
        mockModel.setStageId("stageId");
        // 设置班级 Id 的集合
        mockModel.setGroupIds(Collections.singletonList("groupIds"));
        // 设置框架 Id
        mockModel.setFrameworkId("frameworkId");
        // 设置框架名称
        mockModel.setFrameworkName("frameworkName");
        // 设置表头
        mockModel.setTableHeader(Collections.singletonList("tableHeader"));
        // 设置表数据
        mockModel.setTableData(Collections.singletonList(Collections.singletonMap("key", "value")));

        // 添加到 mockModelList 中
        mockModelList.add(mockModel);

        List<KeyMeasureSettingEntity> keyMeasureSettingEntities = new ArrayList<>();
        KeyMeasureSettingEntity keyMeasureSettingEntity = new KeyMeasureSettingEntity();
        keyMeasureSettingEntity.setId("key001");
        keyMeasureSettingEntity.setFrameworkId("frameworkId");
        keyMeasureSettingEntity.setAgencyId("agencyId");
        keyMeasureSettingEntities.add(keyMeasureSettingEntity);
        when(domainDao.getSettingFramework(any())).thenReturn(keyMeasureSettingEntities);
        List<KeyMeasureEntity> keyMeasureEntities = new ArrayList<>();
        KeyMeasureEntity keyMeasureEntity = new KeyMeasureEntity();
        keyMeasureEntity.setId("measure001");
        keyMeasureEntity.setKeyMeasureSettingId("key001");
        keyMeasureEntity.setAgencyId("agencyId");
        keyMeasureEntities.add(keyMeasureEntity);
        when(domainDao.getKeyMeasuresSetting(anyList())).thenReturn(keyMeasureEntities);

        AgencyMetaDataEntity keyMeasuresMeta = new AgencyMetaDataEntity();
        keyMeasuresMeta.setMetaValue("true");
        when(agencyDao.getMeta("agencyId", AgencyMetaKey.DRDP_KEY_MEASURE_SETTING.toString())).thenReturn(keyMeasuresMeta);
        // 准备 convertDataToFileModels 的返回值，ExportSftpCsvFileModel 是要导出的文件
        List<ExportSftpCsvFileModel> mockFileModelList = new ArrayList<>();

        // 定义要导出的类型
        ExportSftpExportType exportType = ExportSftpExportType.VERIFY_DATA;
        // 定义导出时间
        Date exportDate = new Date();
        // 定义导出根路径
        String rootPath = FileUtil.randomTempFilePath("rootPath/");
        // 定义导出的机构是否开启了导出所有学校的功能
        AgencyMetaDataEntity exportAllSitesOpen = new AgencyMetaDataEntity();
        // 设置导出所有学校的功能的属性
        exportAllSitesOpen.setMetaValue("true");
        ExportSftpSetting setting = getExportSftpSetting();
        // when(exportSftpSettingDao.getSettingByTenantId(anyString())).thenReturn(setting);
//        when(ratingService.isOUSDFramework(anyString())).thenReturn(false);

        exportSftp.convertDataToFileModels(exportType, exportDate, mockModelList, mockFileModelList, rootPath, exportAllSitesOpen, "agencyId", "originAgencyName", false, setting);

        // 校验 mockFileModelList 中是否包含数据
        assertEquals(1, mockFileModelList.size());
        ExportSftpCsvFileModel fileModel = mockFileModelList.get(0);
        // 校验 sftpPath 和 localRootPath 是否正确
        String sftpPath = fileModel.getSftpPath();
        // 校验 sftpPath
        assertEquals(sftpPath, "Latest Exported Data/centerName/groupGrade/");
        String localRootPath = fileModel.getLocalRootPath();
        // 校验 localRootPath
        assertEquals(localRootPath, rootPath);
        // 校验完成之后删除文件
        // 删除生成各个 CSV 文件
        FileUtil.deleteDir(rootPath);
    }


}
package com.learninggenie.api.service.impl.inkind;

import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.exception.ErrorCode;
import com.learninggenie.api.model.*;
import com.learninggenie.api.model.inkind.*;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.provider.impl.InKindProviderImpl;
import com.learninggenie.api.security.AuthUserDetails;
import com.learninggenie.api.service.NoteService;
import com.learninggenie.common.cache.CacheModel;
import com.learninggenie.common.cache.CacheService;
import com.learninggenie.common.data.dao.*;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.dao.inkind.*;
import com.learninggenie.common.data.dao.users.MetaDataDao;
import com.learninggenie.common.data.dto.GroupWithCenter;
import com.learninggenie.common.data.entity.GroupEntity;
import com.learninggenie.common.data.entity.UserEntity;
import com.learninggenie.common.data.entity.*;
import com.learninggenie.common.data.entity.inkind.*;
import com.learninggenie.common.data.enums.*;
import com.learninggenie.common.data.enums.inkind.*;
import com.learninggenie.common.data.model.*;
import com.learninggenie.common.data.model.inkind.*;
import com.learninggenie.common.filesystem.FileSystem;
import com.learninggenie.common.utils.*;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class InKindReportServiceImplTest {
    @InjectMocks
    InKindReportServiceImpl inKindReportService;

    @Mock
    private InKindProviderImpl inKindCommonService;

    @Mock
    private UserProvider userProvider;

    @Mock
    private AgencyDao agencyDao;

    @Mock
    private CenterDao centerDao;

    @Mock
    private GroupDao groupDao;

    @Mock
    private StudentDao studentDao;

    @Mock
    private UserDaoImpl userDao;

    @Mock
    private InkindDao inkindDao;

    @Mock
    private InKindReportApproveDao inKindReportApproveDao;

    @Mock
    private InKindReportGrantDao inKindReportGrantDao;

    @Mock
    private InKindsGoalGrantDao inKindsGoalGrantDao;

    @Mock
    private MediaBookDao mediaBookDao;

    @Mock
    private NoteDao noteDao;

    @Mock
    private NoteService noteService;

    @Mock
    private MediaDao mediaDao;

    @Mock
    private MetaDataDao metaDataDao;

    @Mock
    private FileSystem fileSystem;

    @Mock
    private CacheService cacheService;

    /**
     * mock 获取机构 id 的方法
     *
     * @param user 用户
     * @param agencyId 机构 id
     */
    private void mockGetAgencyId(UserEntity user, String agencyId) {
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        String userId = user.getId();
        when(inKindCommonService.getAgencyId()).thenReturn(agencyId);
    }

    /**
     * 测试获取单个小孩的待审核数据，审核加锁
     * case: 用户角色为机构管理员
     */
    @Test
    void testGetPendingInkindsV2ByAgencyAdmin() {
        // 数据准备 -- 方法参数
        String enrollmentId = "E0001";
        String enrollmentName = "test child";
        String groupId = "G0001";
        String date = "2023-01-01";
        String userId = "U0001";
        String agencyId = "A0001";
        String role = "AGENCY_ADMIN";

        Date date1 = TimeUtil.parse("2023-01-01", TimeUtil.format10);
        Date date2 = TimeUtil.parse("2020-01-01", TimeUtil.format10);
        List<InkindSchoolYearEntity> schoolYears = new ArrayList<>();
        InkindSchoolYearEntity schoolYearEntity = new InkindSchoolYearEntity();
        schoolYearEntity.setStartDate(date1);
        InkindSchoolYearEntity historySchoolYear = new InkindSchoolYearEntity();
        historySchoolYear.setStartDate(date2);
        schoolYears.add(schoolYearEntity);
        schoolYears.add(historySchoolYear);

        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole(role);
        AuthUserDetails authUserDetails = new AuthUserDetails();
        authUserDetails.setAgencyId(agencyId);
        authUserDetails.setRole(role);

        List<EnrollmentEntity> children = new ArrayList<>();
        EnrollmentEntity enrollment = new EnrollmentEntity();
        enrollment.setId(enrollmentId);
        enrollment.setDisplayName(enrollmentName);
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId(groupId);
        enrollment.setGroup(groupEntity);
        children.add(enrollment);
        // 一共五种角色, 审批的报告也对应五种角色
        List<ReportReviewModel> reviewModels = new ArrayList<>();
        ReportReviewModel reviewModel1 = new ReportReviewModel();
        reviewModel1.setActivityTypeId("A0001");
        reviewModel1.setApproveRole("AGENCY_ADMIN");
        // 当前角色的groupId
        reviewModel1.setGroupId(groupId);
        reviewModels.add(reviewModel1);
        ReportReviewModel reviewModel2 = new ReportReviewModel();
        reviewModel2.setActivityTypeId("A0002");
        reviewModel2.setApproveRole(ApproveRole.ADMINS.toString());
        reviewModel2.setGroupId("S0001");
        reviewModels.add(reviewModel2);
        ReportReviewModel reviewModel3 = new ReportReviewModel();
        reviewModel3.setActivityTypeId("A0003");
        reviewModel3.setApproveRole(ApproveRole.TEACHER_ASSISTANTS.toString());
        reviewModel3.setGroupId("C0001");
        reviewModels.add(reviewModel3);
        ReportReviewModel reviewModel4 = new ReportReviewModel();
        reviewModel4.setActivityTypeId("A0004");
        reviewModel4.setApproveRole(ApproveRole.TEACHER_ASSISTANTS.toString());
        reviewModel4.setGroupId("T0001");
        reviewModels.add(reviewModel4);
        ReportReviewModel reviewModel5 = new ReportReviewModel();
        reviewModel5.setActivityTypeId("A0005");
        reviewModel5.setApproveRole(ApproveRole.FAMILY_SERVICE_STAFF.toString());
        reviewModel5.setGroupId("F0001");
        reviewModels.add(reviewModel5);

        // 小孩待审核报告数据
        List<InkindReportEntity> reportEntities = new ArrayList<>();
        InkindReportEntity reportEntity1 = new InkindReportEntity();
        reportEntity1.setId("R0001");
        reportEntity1.setEnrollmentId(enrollmentId);
        reportEntity1.setActivityTypeId("A0001");
        reportEntity1.setGroupId(groupId);
        reportEntity1.setParentId("P0001");
        reportEntity1.setUnit(InKindUnit.MINUTE.toString());
        reportEntity1.setRateUnit(InKindUnit.HOUR.toString());
        reportEntity1.setRateValue(BigDecimal.valueOf(14.190));
        reportEntity1.setValue(BigDecimal.valueOf(180.00));
        reportEntity1.setCustom(false);
        reportEntity1.setDraft(false);

        reportEntities.add(reportEntity1);
        InkindReportEntity reportEntity2 = new InkindReportEntity();
        reportEntity2.setId("R0002");
        reportEntity2.setEnrollmentId(enrollmentId);
        reportEntity2.setActivityTypeId("A0002");
        reportEntity2.setGroupId(groupId);
        reportEntity2.setParentId("P0002");
        reportEntity2.setUnit(InKindUnit.MILE.toString());
        reportEntity2.setRateUnit(InKindUnit.MILE.toString());
        reportEntity2.setRateValue(BigDecimal.valueOf(1.00));
        reportEntity2.setValue(BigDecimal.valueOf(10.00));
        reportEntity2.setCustom(false);
        reportEntity2.setDraft(false);
        reportEntities.add(reportEntity2);

        InkindReportEntity reportEntity3 = new InkindReportEntity();
        reportEntity3.setId("R0003");
        reportEntity3.setEnrollmentId(enrollmentId);
        reportEntity3.setActivityTypeId("A0003");
        reportEntity3.setGroupId("T0001");
        reportEntity3.setParentId("P0003");
        reportEntity3.setUnit(InKindUnit.MILE.toString());
        reportEntity3.setRateUnit(InKindUnit.MILE.toString());
        reportEntity3.setRateValue(BigDecimal.valueOf(1.234));
        reportEntity3.setValue(BigDecimal.valueOf(11.00));
        reportEntity3.setCustom(false);
        reportEntity3.setDraft(false);
        reportEntities.add(reportEntity3);

        List<InkindActivityTypeEntity> activityTypes = new ArrayList<>();
        InkindActivityTypeEntity activityTypeEntity1 = new InkindActivityTypeEntity();
        activityTypeEntity1.setId("A0001");
        activityTypeEntity1.setType("AT_HOME");
        activityTypes.add(activityTypeEntity1);
        InkindActivityTypeEntity activityTypeEntity2 = new InkindActivityTypeEntity();
        activityTypeEntity2.setId("A0002");
        activityTypeEntity2.setType("VOLUNTEER");
        activityTypes.add(activityTypeEntity2);

        // 学校班级的数据
        List<GroupWithCenter> groupWithCenterList = new ArrayList<>();
        GroupWithCenter groupWithCenter = new GroupWithCenter();
        groupWithCenter.setGroupId(groupId);
        groupWithCenter.setGroupName("G0001");
        groupWithCenter.setCenterId("C0001");
        groupWithCenter.setCenterName("C0001");
        groupWithCenterList.add(groupWithCenter);

        // 缓存锁的数据
        String result = "OK";
        // 通用 mock 方法
        mockGetAgencyId(userEntity, agencyId);
        // 接口模拟
        when(userProvider.checkUser(userId)).thenReturn(userEntity);
        when(studentDao.getChildWithGroupCenter(enrollmentId)).thenReturn(enrollment);
        // when(inkindDao.getSchoolYearByAgencyId(agencyId)).thenReturn(schoolYears);

        // when(inkindDao.getReviewBySchoolYearIds(any())).thenReturn(reviewModels);
        List<MapModel> approve = reviewModels.stream().map(x -> new MapModel(x.getActivityTypeId(), x.getGroupId())).collect(Collectors.toList());
        when(inKindCommonService.getHistoryApproveModel(userId)).thenReturn(approve);
        when(inKindCommonService.getCurrentSchoolYear(any())).thenReturn(schoolYearEntity);
        when(inKindCommonService.getEnrollmentDisplayName(enrollment.getDisplayName(), enrollment.getFirstName(), enrollment.getLastName())).thenReturn(enrollmentName);
        Map<String, String> childrenMap =
                children.stream().collect(Collectors.toMap(x -> x.getId().toLowerCase(), x -> x.getDisplayName(), (a, b) -> a));
        when(inKindCommonService.setChildren(Arrays.asList(enrollmentId))).thenReturn(childrenMap);
        doAnswer(invocation -> {
            Map<String, String> gMap = invocation.getArgument(1);
            Map<String, String> cMap = invocation.getArgument(2);
            gMap.putAll(groupWithCenterList.stream().collect(
                    Collectors.toMap(GroupWithCenter::getGroupId, GroupWithCenter::getGroupName)));
            groupWithCenterList.forEach(e -> {
                cMap.put(e.getCenterId(), e.getCenterName());
            });
            return null; // void方法返回null
        }).when(inKindCommonService).setGroupAndCenterMap(
                eq(Arrays.asList(groupId)),
                any(Map.class),
                any(Map.class)
        );
        // Map<String, UserEntity> parentsMap = inKindCommonService.getParentMap(Arrays.asList(enrollmentId));
        when(userProvider.getCurrentUser()).thenReturn(authUserDetails);
        when(inkindDao.getReport(enrollmentId, InkindApproveStatus.PENDING)).thenReturn(reportEntities);
        // when(studentDao.getAllChildrenById(any())).thenReturn(children);
        when(inkindDao.getActivityTypeByAgencyId(agencyId)).thenReturn(activityTypes);
        // when(groupDao.getGroupAndCenterNamesByIds(any())).thenReturn(groupWithCenterList);
        // Mockito.when(cacheService.get("INKIND_" + enrollment.getGroup().getId() + "_" + enrollmentId)).thenReturn(null);
        // Mockito.when(cacheService.set(anyString(), anyString(), anyInt())).thenReturn(result);
        // 调用接口
        InkindChildWithStatsResponse pendingInkindsV2 = inKindReportService.getPendingInkinds(enrollmentId, groupId, date, userId);

        // 结果校验
        Assertions.assertNotNull(pendingInkindsV2);
        Assertions.assertEquals(pendingInkindsV2.getEnrollmentId(), enrollmentId);
        List<InkindReportModel> reports = pendingInkindsV2.getReports();
        Assertions.assertEquals(reports.size(), 2);
        InkindReportModel inkindReportModel = reports.get(0);
        reports.stream().filter(report -> report.getId().equals("R0001")).findFirst().ifPresent(report -> {
            Assertions.assertEquals(report.getId(), "R0001");
            Assertions.assertEquals(report.getEnrollmentId(), enrollmentId);
            Assertions.assertEquals(report.getEnrollmentName(), enrollmentName);
            Assertions.assertEquals(report.getGroupId(), groupId);
            Assertions.assertEquals(report.getGroupName(), "G0001");
            Assertions.assertEquals(report.getActivityTypeId(), "A0001");
            Assertions.assertEquals(report.getType(), "AT_HOME");
            Assertions.assertEquals(report.getRateUnit(), InKindUnit.HOUR.toString());
            Assertions.assertEquals(report.getUnit(), InKindUnit.MINUTE.toString());
            Assertions.assertEquals(report.getHourValue(), "3.00");
            Assertions.assertEquals(report.getMinuteValue(), "180");
            Assertions.assertEquals(report.getMileValue(), "0");
            Assertions.assertEquals(report.getRateValue(), BigDecimal.valueOf(14.19));
            Assertions.assertEquals(report.getValue(), BigDecimal.valueOf(180.0));
            Assertions.assertEquals(report.getMoney(), BigDecimal.valueOf(42.57));
        });

        reports.stream().filter(report -> report.getId().equals("R0002")).findFirst().ifPresent(report -> {
            Assertions.assertEquals(report.getId(), "R0002");
            Assertions.assertEquals(report.getEnrollmentId(), enrollmentId);
            Assertions.assertEquals(report.getEnrollmentName(), enrollmentName);
            Assertions.assertEquals(report.getGroupId(), groupId);
            Assertions.assertEquals(report.getGroupName(), "G0001");
            Assertions.assertEquals(report.getActivityTypeId(), "A0002");
            Assertions.assertEquals(report.getType(), "VOLUNTEER");
            Assertions.assertEquals(report.getRateUnit(), InKindUnit.MILE.toString());
            Assertions.assertEquals(report.getUnit(), InKindUnit.MILE.toString());
            Assertions.assertEquals(report.getHourValue(), "0");
            Assertions.assertEquals(report.getMinuteValue(), "0");
            Assertions.assertEquals(report.getMileValue(), "10");
            Assertions.assertEquals(report.getRateValue(), BigDecimal.valueOf(1.0));
            Assertions.assertEquals(report.getValue(), BigDecimal.valueOf(10.0));
            Assertions.assertEquals(report.getMoney(), BigDecimal.valueOf(10.00).setScale(2, RoundingMode.HALF_UP));
        });
    }

    /**
     * 测试获取单个小孩的待审核数据，审核加锁
     * case: 用户角色为老师
     */
    @Test
    void testGetPendingInkindsV2ByTeacher() {
        // 数据准备 -- 方法参数
        String enrollmentId = "E0001";
        String enrollmentName = "test child";
        String groupId = "G0001";
        String date = "2023-01-01";
        String userId = "U0001";
        String agencyId = "A0001";
        String role = "TEACHING_ASSISTANT";

        Date date1 = TimeUtil.parse("2023-01-01", TimeUtil.format10);
        Date date2 = TimeUtil.parse("2020-01-01", TimeUtil.format10);
        List<InkindSchoolYearEntity> schoolYears = new ArrayList<>();
        InkindSchoolYearEntity schoolYearEntity = new InkindSchoolYearEntity();
        schoolYearEntity.setStartDate(date1);
        InkindSchoolYearEntity historySchoolYear = new InkindSchoolYearEntity();
        historySchoolYear.setStartDate(date2);
        schoolYears.add(schoolYearEntity);
        schoolYears.add(historySchoolYear);

        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole(role);
        AuthUserDetails authUserDetails = new AuthUserDetails();
        authUserDetails.setAgencyId(agencyId);
        authUserDetails.setRole(role);

        List<EnrollmentEntity> children = new ArrayList<>();
        EnrollmentEntity enrollment = new EnrollmentEntity();
        enrollment.setId(enrollmentId);
        enrollment.setDisplayName(enrollmentName);
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId(groupId);
        enrollment.setGroup(groupEntity);
        children.add(enrollment);
        // 一共五种角色, 审批的报告也对应五种角色
        List<ReportReviewModel> reviewModels = new ArrayList<>();
        ReportReviewModel reviewModel1 = new ReportReviewModel();
        reviewModel1.setActivityTypeId("A0001");
        reviewModel1.setApproveRole(ApproveRole.ADMINS.toString());
        // 当前角色的groupId
        reviewModel1.setGroupId(groupId);
        reviewModels.add(reviewModel1);
        ReportReviewModel reviewModel2 = new ReportReviewModel();
        reviewModel2.setActivityTypeId("A0002");
        reviewModel2.setApproveRole(ApproveRole.ADMINS.toString());
        reviewModel2.setGroupId("S0001");
        reviewModels.add(reviewModel2);
        ReportReviewModel reviewModel3 = new ReportReviewModel();
        reviewModel3.setActivityTypeId("A0003");
        reviewModel3.setApproveRole(ApproveRole.TEACHER_ASSISTANTS.toString());
        reviewModel3.setGroupId("C0001");
        reviewModels.add(reviewModel3);
        ReportReviewModel reviewModel4 = new ReportReviewModel();
        reviewModel4.setActivityTypeId("A0004");
        reviewModel4.setApproveRole(ApproveRole.TEACHER_ASSISTANTS.toString());
        reviewModel4.setGroupId(groupId);
        reviewModels.add(reviewModel4);
        ReportReviewModel reviewModel5 = new ReportReviewModel();
        reviewModel5.setActivityTypeId("A0005");
        reviewModel5.setApproveRole(ApproveRole.FAMILY_SERVICE_STAFF.toString());
        reviewModel5.setGroupId("F0001");
        reviewModels.add(reviewModel5);

        // 小孩待审核报告数据
        List<InkindReportEntity> reportEntities = new ArrayList<>();
        InkindReportEntity reportEntity1 = new InkindReportEntity();
        reportEntity1.setId("R0001");
        reportEntity1.setEnrollmentId(enrollmentId);
        reportEntity1.setActivityTypeId("A0004");
        reportEntity1.setGroupId(groupId);
        reportEntity1.setParentId("P0001");
        reportEntity1.setUnit(InKindUnit.MINUTE.toString());
        reportEntity1.setRateUnit(InKindUnit.HOUR.toString());
        reportEntity1.setRateValue(BigDecimal.valueOf(14.190));
        reportEntity1.setValue(BigDecimal.valueOf(180.00));
        reportEntity1.setCustom(false);
        reportEntity1.setDraft(false);
        reportEntities.add(reportEntity1);

        InkindReportEntity reportEntity2 = new InkindReportEntity();
        reportEntity2.setId("R0002");
        reportEntity2.setEnrollmentId(enrollmentId);
        reportEntity2.setActivityTypeId("A0002");
        reportEntity2.setGroupId(groupId);
        reportEntity2.setParentId("P0002");
        reportEntity2.setUnit(InKindUnit.MILE.toString());
        reportEntity2.setRateUnit(InKindUnit.MILE.toString());
        reportEntity2.setRateValue(BigDecimal.valueOf(1.00));
        reportEntity2.setValue(BigDecimal.valueOf(10.00));
        reportEntity2.setCustom(false);
        reportEntity2.setDraft(false);
        reportEntities.add(reportEntity2);

        InkindReportEntity reportEntity3 = new InkindReportEntity();
        reportEntity3.setId("R0003");
        reportEntity3.setEnrollmentId(enrollmentId);
        reportEntity3.setActivityTypeId("A0003");
        reportEntity3.setGroupId("T0001");
        reportEntity3.setParentId("P0003");
        reportEntity3.setUnit(InKindUnit.MILE.toString());
        reportEntity3.setRateUnit(InKindUnit.MILE.toString());
        reportEntity3.setRateValue(BigDecimal.valueOf(1.234));
        reportEntity3.setValue(BigDecimal.valueOf(11.00));
        reportEntity3.setCustom(false);
        reportEntity3.setDraft(false);
        reportEntities.add(reportEntity3);

        List<InkindActivityTypeEntity> activityTypes = new ArrayList<>();
        InkindActivityTypeEntity activityTypeEntity1 = new InkindActivityTypeEntity();
        activityTypeEntity1.setId("A0004");
        activityTypeEntity1.setType("AT_HOME");
        activityTypes.add(activityTypeEntity1);
        InkindActivityTypeEntity activityTypeEntity2 = new InkindActivityTypeEntity();
        activityTypeEntity2.setId("A0002");
        activityTypeEntity2.setType("VOLUNTEER");
        activityTypes.add(activityTypeEntity2);

        // 学校班级的数据
        List<GroupWithCenter> groupWithCenterList = new ArrayList<>();
        GroupWithCenter groupWithCenter = new GroupWithCenter();
        groupWithCenter.setGroupId(groupId);
        groupWithCenter.setGroupName("G0001");
        groupWithCenter.setCenterId("C0001");
        groupWithCenter.setCenterName("C0001");
        groupWithCenterList.add(groupWithCenter);

        // 缓存锁的数据
        String result = "OK";
        // 通用 mock 方法
        mockGetAgencyId(userEntity, agencyId);
        // 接口模拟
        when(userProvider.checkUser(userId)).thenReturn(userEntity);
        when(studentDao.getChildWithGroupCenter(enrollmentId)).thenReturn(enrollment);
        // when(inkindDao.getSchoolYearByAgencyId(agencyId)).thenReturn(schoolYears);
        // when(inkindDao.getReviewBySchoolYearIds(any())).thenReturn(reviewModels);
        when(userProvider.getCurrentUser()).thenReturn(authUserDetails);
        when(inkindDao.getReport(enrollmentId, InkindApproveStatus.PENDING)).thenReturn(reportEntities);
        // when(studentDao.getAllChildrenById(any())).thenReturn(children);
        when(inkindDao.getActivityTypeByAgencyId(agencyId)).thenReturn(activityTypes);
        // when(groupDao.getGroupAndCenterNamesByIds(any())).thenReturn(groupWithCenterList);
        // Mockito.when(cacheService.get("INKIND_" + enrollment.getGroup().getId() + "_" + enrollmentId)).thenReturn(null);
        // Mockito.when(cacheService.set(anyString(), anyString(), anyInt())).thenReturn(result);
        List<MapModel> approve = reviewModels.stream().map(x -> new MapModel(x.getActivityTypeId(), x.getGroupId())).collect(Collectors.toList());
        when(inKindCommonService.getHistoryApproveModel(userId)).thenReturn(approve);
        Map<String, String> childrenMap =
                children.stream().collect(Collectors.toMap(x -> x.getId().toLowerCase(), x -> x.getDisplayName(), (a, b) -> a));
        when(inKindCommonService.setChildren(Arrays.asList(enrollmentId))).thenReturn(childrenMap);
        when(inKindCommonService.getCurrentSchoolYear(any())).thenReturn(schoolYearEntity);
        doAnswer(invocation -> {
            Map<String, String> gMap = invocation.getArgument(1);
            Map<String, String> cMap = invocation.getArgument(2);
            gMap.putAll(groupWithCenterList.stream().collect(
                    Collectors.toMap(GroupWithCenter::getGroupId, GroupWithCenter::getGroupName)));
            groupWithCenterList.forEach(e -> {
                cMap.put(e.getCenterId(), e.getCenterName());
            });
            return null; // void方法返回null
        }).when(inKindCommonService).setGroupAndCenterMap(
                eq(Arrays.asList(groupId)),
                any(Map.class),
                any(Map.class)
        );
        // 调用接口
        InkindChildWithStatsResponse pendingInkindsV2 = inKindReportService.getPendingInkinds(enrollmentId, groupId, date, userId);

        // 结果校验
        Assertions.assertNotNull(pendingInkindsV2);
        Assertions.assertEquals(pendingInkindsV2.getEnrollmentId(), enrollmentId);
        List<InkindReportModel> reports = pendingInkindsV2.getReports();
        Assertions.assertEquals(reports.size(), 1);
        InkindReportModel inkindReportModel = reports.get(0);
        reports.stream().filter(report -> report.getId().equals("R0001")).findFirst().ifPresent(report -> {
            Assertions.assertEquals(report.getId(), "R0001");
            Assertions.assertEquals(report.getEnrollmentId(), enrollmentId);
            Assertions.assertEquals(report.getEnrollmentName(), enrollmentName);
            Assertions.assertEquals(report.getGroupId(), groupId);
            Assertions.assertEquals(report.getGroupName(), "G0001");
            Assertions.assertEquals(report.getActivityTypeId(), "A0004");
            Assertions.assertEquals(report.getType(), "AT_HOME");
            Assertions.assertEquals(report.getRateUnit(), InKindUnit.HOUR.toString());
            Assertions.assertEquals(report.getUnit(), InKindUnit.MINUTE.toString());
            Assertions.assertEquals(report.getHourValue(), "3.00");
            Assertions.assertEquals(report.getMinuteValue(), "180");
            Assertions.assertEquals(report.getMileValue(), "0");
            Assertions.assertEquals(report.getRateValue(), BigDecimal.valueOf(14.19));
            Assertions.assertEquals(report.getValue(), BigDecimal.valueOf(180.0));
            Assertions.assertEquals(report.getMoney(), BigDecimal.valueOf(42.57));
        });
    }


    /**
     * 测试获取单个学生报告列表
     */
    @Test
    void testGetEnrollmentInkindsV2() {
        // 参数准备
        String userId = "U0001";
        String fromDate = "2023-07-17";
        String toDate = "2023-07-24";
        String parentId = "P0001";
        String groupId = "G0001";
        String enrollmentId = "E0001";
        String timeZone = "8";
        String approveStatus = "APPROVED";
        String agencyId = "A0001";

        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole("AGENCY_ADMIN");

        GroupEntity group = new GroupEntity();
        group.setId(groupId);
        EnrollmentEntity enrollment = new EnrollmentEntity();
        enrollment.setId(enrollmentId);
        enrollment.setGroup(group);
        enrollment.setDisplayName("enrollment1");

        List<GroupWithCenter> groupWithCenters = new ArrayList<>();
        GroupWithCenter groupWithCenter = new GroupWithCenter();
        groupWithCenter.setGroupId(groupId);
        groupWithCenter.setGroupName("group");
        groupWithCenter.setCenterId("C0001");
        groupWithCenter.setCenterName("center");
        groupWithCenters.add(groupWithCenter);

        List<InkindSchoolYearEntity> schoolYears = new ArrayList<>();
        InkindSchoolYearEntity schoolYearEntity = new InkindSchoolYearEntity();
        schoolYearEntity.setStartDate(TimeUtil.parseDate("2020-01-05"));
        schoolYearEntity.setShowRate(true);
        schoolYears.add(schoolYearEntity);

        UserEntity parent = new UserEntity();
        parent.setId(parentId);
        parent.setEnrollments(Collections.singleton(enrollment));
        List<InkindReportEntity> inkinds = new ArrayList<>();
        InkindReportEntity reportEntity1 = new InkindReportEntity();
        reportEntity1.setId("I0001");
        reportEntity1.setActivityDate(TimeUtil.parse("2023-01-01", TimeUtil.format10));
        reportEntity1.setApproveUserId("U0001");
        reportEntity1.setType("IN_SYSTEM");
        reportEntity1.setCreateAtUtc(TimeUtil.parse("2023-01-01", TimeUtil.format10));
        reportEntity1.setEnrollmentId(enrollmentId);
        reportEntity1.setParentId(parentId);
        reportEntity1.setCustom(false);
        reportEntity1.setDraft(false);
        reportEntity1.setUnit(InKindUnit.MILE.toString());
        reportEntity1.setRateUnit(InKindUnit.MILE.toString());
        reportEntity1.setRateValue(BigDecimal.valueOf(1.234));
        reportEntity1.setValue(BigDecimal.valueOf(11.00));
        inkinds.add(reportEntity1);
        InkindReportEntity reportEntity2 = new InkindReportEntity();
        reportEntity2.setId("I0002");
        reportEntity2.setActivityDate(TimeUtil.parse("2023-01-02", TimeUtil.format10));
        reportEntity2.setApproveUserId("U0001");
        reportEntity2.setType("OUT_SYSTEM");
        reportEntity2.setCreateAtUtc(TimeUtil.parse("2023-01-02", TimeUtil.format10));
        reportEntity2.setEnrollmentId(enrollmentId);
        reportEntity2.setParentId(parentId);
        reportEntity2.setCustom(false);
        reportEntity2.setDraft(false);
        reportEntity2.setUnit(InKindUnit.MILE.toString());
        reportEntity2.setRateUnit(InKindUnit.MILE.toString());
        reportEntity2.setRateValue(BigDecimal.valueOf(1.234));
        reportEntity2.setValue(BigDecimal.valueOf(11.00));
        inkinds.add(reportEntity2);

        List<InKindReportApprove> reportApproves2 = new ArrayList<>();
        InKindReportApprove reportApprove1 = new InKindReportApprove();
        reportApprove1.setUserId("U0002");
        reportApprove1.setReportId("I0001");
        reportApprove1.setCreateAtUtc(TimeUtil.parse("2023-01-01", TimeUtil.format10));
        reportApprove1.setSignatureId("S0001");
        reportApproves2.add(reportApprove1);

        List<UserModel> staffAll = new ArrayList<>();
        UserModel userModel = new UserModel();
        userModel.setId("U0001");
        userModel.setDisplayName("user1");
        userModel.setAvatarMediaUrl("test");
        UserModel userModel2 = new UserModel();
        userModel2.setId("U0002");
        userModel2.setAvatarMediaUrl("");
        userModel2.setDisplayName("user2");
        staffAll.add(userModel);
        staffAll.add(userModel2);

        InkindAttachmentModel inkindAttachment = new InkindAttachmentModel();
        inkindAttachment.setName("test");
        inkindAttachment.setRelativePath("test");
        inkindAttachment.setSize(1);

        List<InkindActivityTypeEntity> activityTypes = new ArrayList<>();
        InkindActivityTypeEntity activityTypeEntity1 = new InkindActivityTypeEntity();
        activityTypeEntity1.setId("A0004");
        activityTypeEntity1.setType("AT_HOME");
        activityTypes.add(activityTypeEntity1);
        InkindActivityTypeEntity activityTypeEntity2 = new InkindActivityTypeEntity();
        activityTypeEntity2.setId("A0002");
        activityTypeEntity2.setType("VOLUNTEER");
        activityTypes.add(activityTypeEntity2);


        Integer totalCount = 2;
        Integer pendingCount = 0;

        // 通用 mock 方法
        mockGetAgencyId(userEntity, agencyId);
        // 注入属性
        ReflectionTestUtils.setField(inKindReportService, "userDefaultAvatarUrl", "userDefaultAvatarUrl");
        // 接口模拟
        when(userProvider.checkUser(userId)).thenReturn(userEntity);
        when(studentDao.getChildWithGroupCenter(enrollmentId)).thenReturn(enrollment);
        // when(userDao.getAllParentsByStudentIds(anyString())).thenReturn(Collections.singletonList(parent));
        // when(studentDao.getAllChildrenById(anyString())).thenReturn(Collections.singletonList(enrollment));
        when(inkindDao.getDonorParentByEnrollmentIds(anyList())).thenReturn(Collections.singletonList(parent));
        // when(groupDao.getGroupAndCenterNamesByIds(anyString())).thenReturn(groupWithCenters);
        when(inkindDao.getActivityTypeByAgencyId(anyString())).thenReturn(activityTypes);
        when(inkindDao.getInkindsByChildIdV2(any())).thenReturn(inkinds);
        when(inKindReportApproveDao.getRatifySignatureReportApproveList(anyList())).thenReturn(reportApproves2);
        when(userDao.getUsersByUserIdsWithDeleted(anyList())).thenReturn(staffAll);
        when(fileSystem.getPublicUrl(anyString())).thenReturn("test");
        when(inkindDao.getInkindAttachmentByReportId(anyString())).thenReturn(inkindAttachment);
        when(inkindDao.getInkindsCountByChildIdV2(anyString(), anyString(), anyString(), anyList(), anyString(), anyString())).thenReturn(totalCount);
        when(inkindDao.getInkindsCountByChildId(anyString(), anyString(), any(), any(), anyString(), any())).thenReturn(pendingCount);
        // when(inkindDao.getSchoolYearByAgencyId(agencyId)).thenReturn(schoolYears);
        when(inKindCommonService.getCurrentSchoolYear(any())).thenReturn(schoolYearEntity);
        Map<String, String> childrenMap =
                Collections.singletonList(enrollment).stream().collect(Collectors.toMap(x -> x.getId().toLowerCase(), x -> x.getDisplayName(), (a, b) -> a));
        when(inKindCommonService.setChildren(Arrays.asList(enrollmentId))).thenReturn(childrenMap);
        doAnswer(invocation -> {
            Map<String, String> gMap = invocation.getArgument(1);
            Map<String, String> cMap = invocation.getArgument(2);
            gMap.putAll(groupWithCenters.stream().collect(
                    Collectors.toMap(GroupWithCenter::getGroupId, GroupWithCenter::getGroupName)));
            groupWithCenters.forEach(e -> {
                cMap.put(e.getCenterId(), e.getCenterName());
            });
            return null; // void方法返回null
        }).when(inKindCommonService).setGroupAndCenterMap(
                eq(Arrays.asList(groupId)),
                any(Map.class),
                any(Map.class)
        );
        // 调用方法
        InkindChildResponse enrollmentInkindsV2 = inKindReportService.getEnrollmentInkinds("", enrollmentId, parentId, approveStatus, fromDate, toDate, 0, 0, userId, timeZone);
        // 验证结果
        Assertions.assertEquals(2, enrollmentInkindsV2.getTotalCount());
        Assertions.assertEquals(0, enrollmentInkindsV2.getPendingCount());

        List<InkindReportModel> inkindReportAppends = enrollmentInkindsV2.getReports();
        Assertions.assertEquals(2, inkindReportAppends.size());
        InkindReportModel inkindReport1 = inkindReportAppends.get(0);
        Assertions.assertEquals("I0002", inkindReport1.getId());
        Assertions.assertEquals("E0001", inkindReport1.getEnrollmentId());
        Assertions.assertEquals("enrollment1", inkindReport1.getEnrollmentName());
        Assertions.assertEquals("P0001", inkindReport1.getParentId());
        Assertions.assertEquals("MILE", inkindReport1.getRateUnit());
        Assertions.assertEquals("1.234", inkindReport1.getRateValue().toString());
        Assertions.assertEquals("0", inkindReport1.getHourValue());
        Assertions.assertEquals("0", inkindReport1.getMinuteValue());
        Assertions.assertEquals("11", inkindReport1.getMileValue());
        Assertions.assertEquals("13.57", inkindReport1.getValue().toString());
        Assertions.assertEquals("MILE", inkindReport1.getUnit());
        Assertions.assertEquals("2023-01-02", inkindReport1.getActivityDate());
        Assertions.assertEquals("U0001", inkindReport1.getApproveUserId());
        Assertions.assertEquals("user1", inkindReport1.getApproveUserName());
        Assertions.assertEquals("13.57", inkindReport1.getMoney().toString());
        Assertions.assertEquals("OUT_SYSTEM", inkindReport1.getSystemType());
        Assertions.assertEquals("13.5740", inkindReport1.getHighPrecisionValue().toString());
        Assertions.assertEquals("0", inkindReport1.getHighPrecisionTime().toString());
        Assertions.assertEquals("test", inkindReport1.getAttattachmentName());
        Assertions.assertEquals("test", inkindReport1.getAttattachmentSrc());
        Assertions.assertEquals(1, inkindReport1.getAttattachmentSize());
        Assertions.assertEquals("test", inkindReport1.getApproveAvatarUrl());
        InkindReportModel inkindReport2 = inkindReportAppends.get(1);
        Assertions.assertEquals("I0001", inkindReport2.getId());
        Assertions.assertEquals("E0001", inkindReport2.getEnrollmentId());
        Assertions.assertEquals("enrollment1", inkindReport2.getEnrollmentName());
        Assertions.assertEquals("P0001", inkindReport2.getParentId());
        Assertions.assertEquals("MILE", inkindReport2.getRateUnit());
        Assertions.assertEquals("1.234", inkindReport2.getRateValue().toString());
        Assertions.assertEquals("0", inkindReport2.getHourValue());
        Assertions.assertEquals("0", inkindReport2.getMinuteValue());
        Assertions.assertEquals("11", inkindReport2.getMileValue());
        Assertions.assertEquals("13.57", inkindReport2.getValue().toString());
        Assertions.assertEquals("MILE", inkindReport2.getUnit());
        Assertions.assertEquals("2023-01-01", inkindReport2.getActivityDate());
        Assertions.assertEquals("U0001", inkindReport2.getApproveUserId());
        Assertions.assertEquals("user1", inkindReport2.getApproveUserName());
        Assertions.assertEquals("13.57", inkindReport2.getMoney().toString());
        Assertions.assertEquals("IN_SYSTEM", inkindReport2.getSystemType());
        Assertions.assertEquals("13.5740", inkindReport2.getHighPrecisionValue().toString());
        Assertions.assertEquals("0", inkindReport2.getHighPrecisionTime().toString());
        Assertions.assertEquals("test", inkindReport2.getAttattachmentName());
        Assertions.assertEquals("test", inkindReport2.getAttattachmentSrc());
        Assertions.assertEquals(1, inkindReport2.getAttattachmentSize());
        Assertions.assertEquals("test", inkindReport2.getApproveAvatarUrl());
        Assertions.assertEquals("user2", inkindReport2.getStaff2Name());
        Assertions.assertEquals("userDefaultAvatarUrl", inkindReport2.getStaff2AvatarUrl());
        Assertions.assertEquals("S0001", inkindReport2.getStaff2SignatureId());

        Assertions.assertEquals(true, enrollmentInkindsV2.getShowValue());
    }


    /**
     * 测试获取下一个待审核的小孩的 In-kind
     * 角色: AGENCY_OWNER
     */
    @Test
    void testGetNextPendingInkindsV2Case1() {
        // 准备数据
        String enrollmentId = "E0001";
        String date = "2020-01-01";
        String userId = "U0001";

        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setRole("AGENCY_OWNER");
        UserEntity user = new UserEntity();
        user.setRole("AGENCY_OWNER");

        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId("G0001");

        List<AgencyModel> agencyModels = new ArrayList<>();
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("A0001");
        agencyModels.add(agencyModel);


        EnrollmentEntity enrollment = new EnrollmentEntity();
        enrollment.setId("E0001");
        enrollment.setGroup(groupEntity);

        CacheModel cacheModel = new CacheModel();
        cacheModel.setValue("U0001");

        List<InkindReportEntity> pendingReports = new ArrayList<>();
        InkindReportEntity inkindReportEntity = new InkindReportEntity();
        inkindReportEntity.setId("R0001");
        inkindReportEntity.setActivityTypeId("T0001");
        inkindReportEntity.setGroupId("G0001");
        inkindReportEntity.setEnrollmentId("E0001");
        inkindReportEntity.setParentId("P0001");
        inkindReportEntity.setRateValue(new BigDecimal("1"));
        inkindReportEntity.setRateUnit("HOUR");
        inkindReportEntity.setUnit("HOUR");
        inkindReportEntity.setValue(new BigDecimal("1"));
        inkindReportEntity.setCustom(true);
        inkindReportEntity.setActivityNumber("AC0001");
        inkindReportEntity.setActivityDescription("ActivityDescription");
        inkindReportEntity.setDomainName("DomainName");
        inkindReportEntity.setThemeName("ThemeName");
        inkindReportEntity.setSourceName("SourceName");
        inkindReportEntity.setParentComment("ParentComment");
        inkindReportEntity.setActivityDate(new Date());
        inkindReportEntity.setApproveStatus("PENDING");
        inkindReportEntity.setApproveUserName("ApproveUserName");
        inkindReportEntity.setApproveComment("ApproveComment");
        inkindReportEntity.setApproveUserId("ApproveUserId");
        inkindReportEntity.setApproveSignatureId("ApproveSignatureId");
        inkindReportEntity.setApproveAtUtc(new Date());
        inkindReportEntity.setDraft(true);
        inkindReportEntity.setCreateAtUtc(new Date());
        pendingReports.add(inkindReportEntity);
        InkindReportEntity inkindReportEntity2 = new InkindReportEntity();
        inkindReportEntity2.setActivityTypeId("T0002");
        inkindReportEntity2.setGroupId("G0002");
        inkindReportEntity2.setEnrollmentId("E0002");
        inkindReportEntity2.setParentId("P0002");
        inkindReportEntity2.setRateValue(new BigDecimal("2"));
        inkindReportEntity2.setRateUnit("MINUTE");
        inkindReportEntity2.setUnit("MINUTE");
        inkindReportEntity2.setValue(new BigDecimal("2"));
        inkindReportEntity2.setCustom(true);
        inkindReportEntity2.setActivityNumber("AC0002");
        inkindReportEntity2.setActivityDescription("ActivityDescription2");
        inkindReportEntity2.setDomainName("DomainName2");
        inkindReportEntity2.setThemeName("ThemeName2");
        inkindReportEntity2.setSourceName("SourceName2");
        inkindReportEntity2.setParentComment("ParentComment2");
        inkindReportEntity2.setActivityDate(new Date());
        inkindReportEntity2.setApproveStatus("PENDING");
        inkindReportEntity2.setApproveUserName("ApproveUserName2");
        inkindReportEntity2.setApproveComment("ApproveComment2");
        inkindReportEntity2.setApproveUserId("ApproveUserId2");
        inkindReportEntity2.setApproveSignatureId("ApproveSignatureId2");
        inkindReportEntity2.setApproveAtUtc(new Date());
        inkindReportEntity2.setDraft(true);
        inkindReportEntity2.setCreateAtUtc(new Date());
        pendingReports.add(inkindReportEntity2);


        List<EnrollmentModel> enrollmentModels = new ArrayList<>();
        EnrollmentModel enrollmentModel = new EnrollmentModel();
        enrollmentModel.setId("E0001");
        enrollmentModel.setLastName("LastName");
        enrollmentModel.setFirstName("FirstName");
        enrollmentModels.add(enrollmentModel);
        EnrollmentModel enrollmentModel2 = new EnrollmentModel();
        enrollmentModel2.setId("E0002");
        enrollmentModel2.setLastName("LastName");
        enrollmentModel2.setFirstName("FirstName");
        enrollmentModels.add(enrollmentModel2);

        List<EnrollmentEntity> children = new ArrayList<>();
        EnrollmentEntity child = new EnrollmentEntity();
        child.setId("E0001");
        child.setDisplayName("chiledName");
        children.add(child);

        List<InkindActivityTypeEntity> activityTypes = new ArrayList<>();
        InkindActivityTypeEntity activityType = new InkindActivityTypeEntity();
        activityTypes.add(activityType);

        List<UserEntity> parents = new ArrayList<>();
        UserEntity parent = new UserEntity();
        parent.setId("U0001");
        Set<EnrollmentEntity> enrollments = new HashSet<>();
        enrollments.add(enrollment);
        parent.setEnrollments(enrollments);
        parents.add(parent);

        EnrollmentEntity nextEnrollment = new EnrollmentEntity();
        nextEnrollment.setId("E0001");
        nextEnrollment.setDisplayName("DisplayName");
        nextEnrollment.setFirstName("FirstName");
        nextEnrollment.setLastName("LastName");
        MediaEntity mediaEntity = new MediaEntity();
        mediaEntity.setRelativePath("http://www.baidu.com");
        nextEnrollment.setAvatarMedia(mediaEntity);
        nextEnrollment.setGroup(groupEntity);


        // 模拟接口
        // 获取当前用户
        when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // 检查当前用户
        // when(userProvider.checkUser(any())).thenReturn(user);
        // 获取机构
        // when(userDao.getAgencyByAgencyAdminId(any())).thenReturn(agencyModels);
        // 从缓存中获取信息
        when(cacheService.get(anyString())).thenReturn(cacheModel);
        // 获取家长待审核的数据
        when(inkindDao.getReportByGroupIds(Arrays.asList("G0001"), InkindApproveStatus.PENDING, InKindStageEnum.APPROVE)).thenReturn(pendingReports);
        // 查询小孩
        when(studentDao.getAllChildrenByIds(anyList())).thenReturn(enrollmentModels);
        // 查询小孩
        // when(studentDao.getAllChildrenById(any())).thenReturn(children);
        // 获取活动类型
        when(inkindDao.getActivityTypeByAgencyId(any())).thenReturn(activityTypes);
        // 获取所有家长
        // when(userDao.getAllParentsByStudentIds(any())).thenReturn(new ArrayList<>());
        // 获取下一个小孩
        when(studentDao.getChildWithGroupCenter(any())).thenReturn(nextEnrollment);

        when(inKindCommonService.getEnrollmentDisplayName(nextEnrollment.getDisplayName(), nextEnrollment.getFirstName(), nextEnrollment.getLastName())).thenReturn("DisplayName");
        Map<String, String> childrenMap =
                children.stream().collect(Collectors.toMap(x -> x.getId().toLowerCase(), x -> x.getDisplayName(), (a, b) -> a));
        when(inKindCommonService.setChildren(Arrays.asList(enrollmentId))).thenReturn(childrenMap);
        // doAnswer(invocation -> {
        //     Map<String, String> gMap = invocation.getArgument(1);
        //     Map<String, String> cMap = invocation.getArgument(2);
        //     gMap.putAll(groupWithCenterList.stream().collect(
        //             Collectors.toMap(GroupWithCenter::getGroupId, GroupWithCenter::getGroupName)));
        //     groupWithCenterList.forEach(e -> {
        //         cMap.put(e.getCenterId(), e.getCenterName());
        //     });
        //     return null; // void方法返回null
        // }).when(inKindCommonService).setGroupAndCenterMap(
        //         eq(Arrays.asList(groupId)),
        //         any(Map.class),
        //         any(Map.class)
        // );

        InkindChildWithStatsResponse response = inKindReportService.getNextPendingInkinds(enrollmentId, null, date, userId);

        // 验证
        verify(cacheService, times(1)).delete(any());
        Assertions.assertEquals(enrollmentId, response.getEnrollmentId());
        Assertions.assertEquals("DisplayName", response.getEnrollmentName());
        Assertions.assertEquals(true, response.getHasReview());
        Assertions.assertEquals(1, response.getReports().size());
        Assertions.assertEquals("R0001", response.getReports().get(0).getId());

    }

    /**
     * 测试获取下一个待审核的小孩的 In-kind
     * 角色: FAMILY_SERVICE
     */
    @Test
    void testGetNextPendingInkindsV2Case2() {
        // 准备数据
        String enrollmentId = "E0001";
        String date = "2020-01-01";
        String userId = "U0001";
        String groupId = "G0001";
        String parentId = "P0001";

        Date date1 = TimeUtil.parse("2020-01-01", TimeUtil.format10);
        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setRole("FAMILY_SERVICE");
        UserEntity user = new UserEntity();
        user.setRole("FAMILY_SERVICE");

        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId(groupId);

        List<AgencyModel> agencyModels = new ArrayList<>();
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("A0001");
        agencyModels.add(agencyModel);


        EnrollmentEntity enrollment = new EnrollmentEntity();
        enrollment.setId("E0001");
        enrollment.setGroup(groupEntity);

        CacheModel cacheModel = new CacheModel();
        cacheModel.setValue(userId);

        List<InkindReportEntity> pendingReports = new ArrayList<>();
        InkindReportEntity inkindReportEntity = new InkindReportEntity();
        inkindReportEntity.setId("R0001");
        inkindReportEntity.setActivityTypeId("T0001");
        inkindReportEntity.setGroupId(groupId);
        inkindReportEntity.setEnrollmentId("E0001");
        inkindReportEntity.setRateValue(new BigDecimal("1"));
        inkindReportEntity.setRateUnit("HOUR");
        inkindReportEntity.setUnit("HOUR");
        inkindReportEntity.setValue(new BigDecimal("1"));
        inkindReportEntity.setCustom(true);
        inkindReportEntity.setActivityNumber("AC0001");
        inkindReportEntity.setActivityDescription("ActivityDescription");
        inkindReportEntity.setDomainName("DomainName");
        inkindReportEntity.setThemeName("ThemeName");
        inkindReportEntity.setSourceName("SourceName");
        inkindReportEntity.setParentComment("ParentComment");
        inkindReportEntity.setApproveStatus("PENDING");
        inkindReportEntity.setApproveUserName("ApproveUserName");
        inkindReportEntity.setApproveUserId("ApproveUserId");
        inkindReportEntity.setApproveSignatureId("ApproveSignatureId");
        inkindReportEntity.setParentId(parentId);
        inkindReportEntity.setActivityDate(date1);
        inkindReportEntity.setDraft(true);
        pendingReports.add(inkindReportEntity);

        List<EnrollmentModel> enrollmentModels = new ArrayList<>();
        EnrollmentModel enrollmentModel = new EnrollmentModel();
        enrollmentModel.setId("E0001");
        enrollmentModel.setLastName("LastName");
        enrollmentModel.setFirstName("FirstName");
        enrollmentModels.add(enrollmentModel);

        List<EnrollmentEntity> children = new ArrayList<>();
        EnrollmentEntity child = new EnrollmentEntity();
        child.setId("E0001");
        child.setDisplayName("chiledName");
        children.add(child);

        List<InkindActivityTypeEntity> activityTypes = new ArrayList<>();
        InkindActivityTypeEntity activityType = new InkindActivityTypeEntity();
        activityTypes.add(activityType);

        List<UserEntity> parents = new ArrayList<>();
        UserEntity parent = new UserEntity();
        parent.setId(parentId);
        Set<EnrollmentEntity> enrollments = new HashSet<>();
        enrollments.add(enrollment);
        parent.setEnrollments(enrollments);
        parents.add(parent);

        EnrollmentEntity nextEnrollment = new EnrollmentEntity();
        nextEnrollment.setId(enrollmentId);
        nextEnrollment.setDisplayName("DisplayName");
        nextEnrollment.setFirstName("FirstName");
        nextEnrollment.setLastName("LastName");
        MediaEntity mediaEntity = new MediaEntity();
        mediaEntity.setRelativePath("http://www.baidu.com");
        nextEnrollment.setAvatarMedia(mediaEntity);
        nextEnrollment.setGroup(groupEntity);

        List<ReportReviewModel> reviewModels = new ArrayList<>();
        ReportReviewModel reviewModel1 = new ReportReviewModel();
        reviewModel1.setActivityTypeId("T0001");
        reviewModel1.setGroupId(groupId);
        reviewModel1.setApproveRole("FAMILY_SERVICE_STAFF");
        reviewModels.add(reviewModel1);
        ReportReviewModel reviewModel2 = new ReportReviewModel();
        reviewModel2.setActivityTypeId("T0002");
        reviewModel2.setGroupId("G0002");
        reviewModel2.setApproveRole("FAMILY_SERVICE_STAFF");
        reviewModels.add(reviewModel2);
        ReportReviewModel reviewModel3 = new ReportReviewModel();
        reviewModel3.setActivityTypeId("T0003");
        reviewModel3.setGroupId("G0001");
        reviewModel3.setApproveRole("ADMINS");
        reviewModels.add(reviewModel3);

        List<InkindSchoolYearEntity> schoolYears = new ArrayList<>();
        InkindSchoolYearEntity inkindSchoolYearEntity = new InkindSchoolYearEntity();
        inkindSchoolYearEntity.setId("S0001");
        inkindSchoolYearEntity.setStartDate(date1);
        schoolYears.add(inkindSchoolYearEntity);

        // 模拟接口
        // 获取当前用户
        when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // 检查当前用户
        // when(userProvider.checkUser(any())).thenReturn(user);
        // 获取机构
        // when(userDao.getAgencyByTeacherId(any())).thenReturn(agencyModels);
        // 获取学年
        // when(inkindDao.getSchoolYearByAgencyId(any())).thenReturn(schoolYears);
        // 获取复查
        // when(inkindDao.getReviewBySchoolYearId(anyString())).thenReturn(reviewModels);
        // when(inkindDao.getReviewBySchoolYearIds(anyList())).thenReturn(reviewModels);
        // 获取家长待审核的数据
        when(inkindDao.getReportByGroupIds(Arrays.asList(groupId), InkindApproveStatus.PENDING, InKindStageEnum.APPROVE)).thenReturn(pendingReports);
        // 查询小孩
        when(studentDao.getAllChildrenByIds(anyList())).thenReturn(enrollmentModels);
        // 获取下一个小孩
        when(studentDao.getChildWithGroupCenter(any())).thenReturn(nextEnrollment);
        // 查询小孩
        // when(studentDao.getAllChildrenById(any())).thenReturn(children);
        // 设置审批小孩锁
        when(cacheService.set(anyString(), anyString(), any(Integer.class))).thenReturn("OK");
        when(inKindCommonService.getEnrollmentDisplayName(nextEnrollment.getDisplayName(), nextEnrollment.getFirstName(), nextEnrollment.getLastName())).thenReturn("DisplayName");
        Map<String, String> childrenMap =
                children.stream().collect(Collectors.toMap(x -> x.getId().toLowerCase(), x -> x.getDisplayName(), (a, b) -> a));
        when(inKindCommonService.setChildren(Arrays.asList(enrollmentId))).thenReturn(childrenMap);
        when(inKindCommonService.checkReview(userId, groupId)).thenReturn(true);
        List<MapModel> approve = reviewModels.stream().map(x -> new MapModel(x.getActivityTypeId(), x.getGroupId())).collect(Collectors.toList());
        when(inKindCommonService.getHistoryApproveModel(currentUser.getId())).thenReturn(approve);

        InkindChildWithStatsResponse response = inKindReportService.getNextPendingInkinds(enrollmentId, null, date, userId);

        // 验证结果
        Assertions.assertEquals(enrollmentId, response.getEnrollmentId());
        Assertions.assertEquals("DisplayName", response.getEnrollmentName());
        Assertions.assertEquals(true, response.getHasReview());
        Assertions.assertEquals(1, response.getReports().size());
        Assertions.assertEquals("R0001", response.getReports().get(0).getId());
    }

    /**
     * 测试 groupId 或 userId 为 null 或空字符串的情况。
     * 期望抛出 BusinessException。
     */
    @Test
    void testGetIgnoreInkindsV2WithNullOrEmptyGroupIdOrUserId() {
        assertThrows(BusinessException.class, () -> inKindReportService.getIgnoreInkinds(null, "search", "2023-01-01", "2023-12-31", 1, 10, "userId"));
        assertThrows(BusinessException.class, () -> inKindReportService.getIgnoreInkinds("", "search", "2023-01-01", "2023-12-31", 1, 10, "userId"));
        assertThrows(BusinessException.class, () -> inKindReportService.getIgnoreInkinds("groupId", "search", "2023-01-01", "2023-12-31", 1, 10, null));
        assertThrows(BusinessException.class, () -> inKindReportService.getIgnoreInkinds("groupId", "search", "2023-01-01", "2023-12-31", 1, 10, ""));
    }

    /**
     * 测试 fromDate 或 toDate 格式无效的情况。
     * 期望抛出 BusinessException。
     */
    @Test
    void testGetIgnoreInkindsV2WithInvalidDateFormat() {
        assertThrows(BusinessException.class, () -> inKindReportService.getIgnoreInkinds("groupId", "search", "0000-00-00", "2023-12-31", 1, 10, "userId"));
        assertThrows(BusinessException.class, () -> inKindReportService.getIgnoreInkinds("groupId", "search", "2023-01-01", "2020-02-23", 1, 10, "userId"));
    }

    /**
     * 测试 group 为 null 的情况。
     * 期望抛出 BusinessException。
     */
    @Test
    void testGetIgnoreInkindsV2WithNullGroup() {
        when(groupDao.getGroupWithCenter(anyString())).thenReturn(null);

        assertThrows(BusinessException.class, () -> inKindReportService.getIgnoreInkinds("groupId", "search", "2023-01-01", "2023-12-31", 1, 10, "userId"));
    }

    @Test
    void testBatchUpdateRate_Success() {
        // 准备测试数据
        String userId = "U0001";
        String agencyId = "A0001";
        List<String> activityGroupIds = Arrays.asList("AG001", "AG002", "AG003");
        BigDecimal value = new BigDecimal("15.50");
        String fromDate = "2023-01-01";
        String toDate = "2023-12-31";

        BatchUpdateRateRequest request = new BatchUpdateRateRequest();
        request.setActivityGroupIds(activityGroupIds);
        request.setValue(value);
        request.setFromDate(fromDate);
        request.setToDate(toDate);

        // Mock 依赖
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);

        // Mock DAO 方法
        when(inkindDao.batchUpdateInkindRate(eq(agencyId), eq(activityGroupIds), eq(value), eq(fromDate), eq(toDate)))
            .thenReturn(10);
        when(inkindDao.batchUpdateInkindRateAppend(eq(agencyId), eq(activityGroupIds), eq(value), eq(fromDate), eq(toDate)))
            .thenReturn(5);

        // 执行测试
        SuccessResponse response = inKindReportService.batchUpdateRate(request);

        // 验证结果
        assertNotNull(response);
        assertTrue(response.isSuccess());
        
        // 验证方法调用
        verify(userProvider).getCurrentUserId();
        verify(userProvider).getAgencyByUserId(userId);
        verify(inkindDao).batchUpdateInkindRate(agencyId, activityGroupIds, value, fromDate, toDate);
        verify(inkindDao).batchUpdateInkindRateAppend(agencyId, activityGroupIds, value, fromDate, toDate);
    }

    @Test
    void testBatchUpdateRate_EmptyActivityGroupIds() {
        // 准备测试数据
        String userId = "U0001";
        String agencyId = "A0001";
        List<String> activityGroupIds = new ArrayList<>();
        BigDecimal value = new BigDecimal("15.50");
        String fromDate = "2023-01-01";
        String toDate = "2023-12-31";

        BatchUpdateRateRequest request = new BatchUpdateRateRequest();
        request.setActivityGroupIds(activityGroupIds);
        request.setValue(value);
        request.setFromDate(fromDate);
        request.setToDate(toDate);

        // Mock 依赖
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            inKindReportService.batchUpdateRate(request);
        });

        assertEquals(ErrorCode.PARAM_ERROR, exception.getErrorCode());
        assertEquals(MSG.t("ER_PARAM"), exception.getDetail());
    }

    @Test
    void testBatchUpdateRate_NullValue() {
        // 准备测试数据
        String userId = "U0001";
        String agencyId = "A0001";
        List<String> activityGroupIds = Arrays.asList("AG001", "AG002");
        BigDecimal value = null;
        String fromDate = "2023-01-01";
        String toDate = "2023-12-31";

        BatchUpdateRateRequest request = new BatchUpdateRateRequest();
        request.setActivityGroupIds(activityGroupIds);
        request.setValue(value);
        request.setFromDate(fromDate);
        request.setToDate(toDate);

        // Mock 依赖
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            inKindReportService.batchUpdateRate(request);
        });

        assertEquals(ErrorCode.PARAM_ERROR, exception.getErrorCode());
        assertEquals(MSG.t("ER_PARAM"), exception.getDetail());
    }

    @Test
    void testBatchUpdateRate_ZeroValue() {
        // 准备测试数据
        String userId = "U0001";
        String agencyId = "A0001";
        List<String> activityGroupIds = Arrays.asList("AG001", "AG002");
        BigDecimal value = BigDecimal.ZERO;
        String fromDate = "2023-01-01";
        String toDate = "2023-12-31";

        BatchUpdateRateRequest request = new BatchUpdateRateRequest();
        request.setActivityGroupIds(activityGroupIds);
        request.setValue(value);
        request.setFromDate(fromDate);
        request.setToDate(toDate);

        // Mock 依赖
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            inKindReportService.batchUpdateRate(request);
        });

        assertEquals(ErrorCode.PARAM_ERROR, exception.getErrorCode());
        assertEquals(MSG.t("ER_PARAM"), exception.getDetail());
    }

    @Test
    void testBatchUpdateRate_NegativeValue() {
        // 准备测试数据
        String userId = "U0001";
        String agencyId = "A0001";
        List<String> activityGroupIds = Arrays.asList("AG001", "AG002");
        BigDecimal value = new BigDecimal("-5.00");
        String fromDate = "2023-01-01";
        String toDate = "2023-12-31";

        BatchUpdateRateRequest request = new BatchUpdateRateRequest();
        request.setActivityGroupIds(activityGroupIds);
        request.setValue(value);
        request.setFromDate(fromDate);
        request.setToDate(toDate);

        // Mock 依赖
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            inKindReportService.batchUpdateRate(request);
        });

        assertEquals(ErrorCode.PARAM_ERROR, exception.getErrorCode());
        assertEquals(MSG.t("ER_PARAM"), exception.getDetail());
    }

    @Test
    void testBatchUpdateRate_EmptyFromDate() {
        // 准备测试数据
        String userId = "U0001";
        String agencyId = "A0001";
        List<String> activityGroupIds = Arrays.asList("AG001", "AG002");
        BigDecimal value = new BigDecimal("15.50");
        String fromDate = "";
        String toDate = "2023-12-31";

        BatchUpdateRateRequest request = new BatchUpdateRateRequest();
        request.setActivityGroupIds(activityGroupIds);
        request.setValue(value);
        request.setFromDate(fromDate);
        request.setToDate(toDate);

        // Mock 依赖
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            inKindReportService.batchUpdateRate(request);
        });

        assertEquals(ErrorCode.PARAM_ERROR, exception.getErrorCode());
        assertEquals(MSG.t("ER_PARAM"), exception.getDetail());
    }

    @Test
    void testBatchUpdateRate_EmptyToDate() {
        // 准备测试数据
        String userId = "U0001";
        String agencyId = "A0001";
        List<String> activityGroupIds = Arrays.asList("AG001", "AG002");
        BigDecimal value = new BigDecimal("15.50");
        String fromDate = "2023-01-01";
        String toDate = "";

        BatchUpdateRateRequest request = new BatchUpdateRateRequest();
        request.setActivityGroupIds(activityGroupIds);
        request.setValue(value);
        request.setFromDate(fromDate);
        request.setToDate(toDate);

        // Mock 依赖
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            inKindReportService.batchUpdateRate(request);
        });

        assertEquals(ErrorCode.PARAM_ERROR, exception.getErrorCode());
        assertEquals(MSG.t("ER_PARAM"), exception.getDetail());
    }

    @Test
    void testBatchUpdateRate_InvalidFromDate() {
        // 准备测试数据
        String userId = "U0001";
        String agencyId = "A0001";
        List<String> activityGroupIds = Arrays.asList("AG001", "AG002");
        BigDecimal value = new BigDecimal("15.50");
        String fromDate = "2023-13-01"; // 无效日期
        String toDate = "2023-12-31";

        BatchUpdateRateRequest request = new BatchUpdateRateRequest();
        request.setActivityGroupIds(activityGroupIds);
        request.setValue(value);
        request.setFromDate(fromDate);
        request.setToDate(toDate);

        // Mock 依赖
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            inKindReportService.batchUpdateRate(request);
        });

        assertEquals(ErrorCode.PARAM_ERROR, exception.getErrorCode());
        assertEquals(MSG.t("ER_PARAM"), exception.getDetail());
    }


    @Test
    void testBatchUpdateRate_InvalidDateRange() {
        // 准备测试数据
        String userId = "U0001";
        String agencyId = "A0001";
        List<String> activityGroupIds = Arrays.asList("AG001", "AG002");
        BigDecimal value = new BigDecimal("15.50");
        String fromDate = "2023-12-31";
        String toDate = "2023-01-01"; // 结束日期早于开始日期

        BatchUpdateRateRequest request = new BatchUpdateRateRequest();
        request.setActivityGroupIds(activityGroupIds);
        request.setValue(value);
        request.setFromDate(fromDate);
        request.setToDate(toDate);

        // Mock 依赖
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            inKindReportService.batchUpdateRate(request);
        });

        assertEquals(ErrorCode.PARAM_ERROR, exception.getErrorCode());
        assertEquals(MSG.t("ER_PARAM"), exception.getDetail());
    }

    @Test
    void testBatchUpdateRate_DatabaseException() {
        // 准备测试数据
        String userId = "U0001";
        String agencyId = "A0001";
        List<String> activityGroupIds = Arrays.asList("AG001", "AG002");
        BigDecimal value = new BigDecimal("15.50");
        String fromDate = "2023-01-01";
        String toDate = "2023-12-31";

        BatchUpdateRateRequest request = new BatchUpdateRateRequest();
        request.setActivityGroupIds(activityGroupIds);
        request.setValue(value);
        request.setFromDate(fromDate);
        request.setToDate(toDate);

        // Mock 依赖
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);

        // Mock DAO 方法抛出异常
        when(inkindDao.batchUpdateInkindRate(eq(agencyId), eq(activityGroupIds), eq(value), eq(fromDate), eq(toDate)))
            .thenThrow(new RuntimeException("Database error"));

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            inKindReportService.batchUpdateRate(request);
        });

        assertEquals(ErrorCode.INTERNAL_SERVER_ERROR, exception.getErrorCode());
        assertEquals("Database error", exception.getDetail());
    }

    @Test
    void testBatchUpdateRate_AppendDatabaseException() {
        // 准备测试数据
        String userId = "U0001";
        String agencyId = "A0001";
        List<String> activityGroupIds = Arrays.asList("AG001", "AG002");
        BigDecimal value = new BigDecimal("15.50");
        String fromDate = "2023-01-01";
        String toDate = "2023-12-31";

        BatchUpdateRateRequest request = new BatchUpdateRateRequest();
        request.setActivityGroupIds(activityGroupIds);
        request.setValue(value);
        request.setFromDate(fromDate);
        request.setToDate(toDate);

        // Mock 依赖
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);

        // Mock 第一个 DAO 方法成功，第二个抛出异常
        when(inkindDao.batchUpdateInkindRate(eq(agencyId), eq(activityGroupIds), eq(value), eq(fromDate), eq(toDate)))
            .thenReturn(5);
        when(inkindDao.batchUpdateInkindRateAppend(eq(agencyId), eq(activityGroupIds), eq(value), eq(fromDate), eq(toDate)))
            .thenThrow(new RuntimeException("Append database error"));

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            inKindReportService.batchUpdateRate(request);
        });

        assertEquals(ErrorCode.INTERNAL_SERVER_ERROR, exception.getErrorCode());
        assertEquals("Append database error", exception.getDetail());
    }
}

package com.learninggenie.api.service.impl;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import com.google.protobuf.InvalidProtocolBufferException;
import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.exception.ErrorCode;
import com.learninggenie.api.model.DownFileResponse;
import com.learninggenie.api.model.SendEmailSuccessResponse;
import com.learninggenie.api.model.SendParentProgressReportEmailRequest;
import com.learninggenie.api.model.agency.AgencySnapshotResponse;
import com.learninggenie.api.model.googleslides.ReplaceRelation;
import com.learninggenie.api.model.report.*;
import com.learninggenie.api.model.translation.TranslationResponse;
import com.learninggenie.api.provider.OpenAIProvider;
import com.learninggenie.api.provider.RemoteProvider;
import com.learninggenie.api.provider.ScoreProvider;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.security.AuthUserDetails;
import com.learninggenie.api.service.*;
import com.learninggenie.common.data.dao.*;
import com.learninggenie.common.data.dao.frameworks.FrameworkDao;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.dao.prompts.PromptDao;
import com.learninggenie.common.data.dao.report.ReportBenchmarkSettingDao;
import com.learninggenie.common.data.dao.report.ReportBenchmarkViewDao;
import com.learninggenie.common.data.dao.users.MetaDataDao;
import com.learninggenie.common.data.entity.BenchmarkSettingEntity;
import com.learninggenie.common.data.entity.SnapshotAliasEntity;
import com.learninggenie.common.data.entity.UserEntity;
import com.learninggenie.common.data.entity.ViewEntity;
import com.learninggenie.common.data.entity.frameworks.FrameworkEntity;
import com.learninggenie.common.data.entity.prompt.PromptEntity;
import com.learninggenie.common.data.entity.report.ReportBenchmarkSettingEntity;
import com.learninggenie.common.data.entity.report.ReportBenchmarkViewEntity;
import com.learninggenie.common.data.entity.users.MetaDataEntity;
import com.learninggenie.common.data.enums.*;
import com.learninggenie.common.data.enums.prompt.PromptScene;
import com.learninggenie.common.data.enums.report.ReportViewLevelEnum;
import com.learninggenie.common.data.enums.report.ReportViewTypeEnum;
import com.learninggenie.common.data.model.*;
import com.learninggenie.common.data.model.report.ActionPlanEntity;
import com.learninggenie.common.data.model.report.BenchmarkViewModel;
import com.learninggenie.common.filesystem.FileSystem;
import com.learninggenie.common.framwork.FrameworkProvider;
import com.learninggenie.common.messaging.EmailModel;
import com.learninggenie.common.report.AnalysisService;
import com.learninggenie.common.report.BenchmarkResponse;
import com.learninggenie.common.report.LGSnapshot;
import com.learninggenie.common.score.*;
import com.learninggenie.common.sharding.ShardingProvider;
import com.learninggenie.common.utils.JsonUtil;
import com.learninggenie.common.utils.ResourceUtil;
import com.learninggenie.common.utils.TimeUtil;
import com.learninggenie.common.utils.pdf.wk.WKHelper;
import org.json.JSONObject;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import org.thymeleaf.TemplateEngine;

import java.io.IOException;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.util.*;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Create by hxl 2023/04/24.
 * ReportServiceImplTest 的测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class ReportServiceImplTest {

    @InjectMocks
    private ReportServiceImpl reportService;

    @Mock
    private StudentDao studentDao;

    @Mock
    private AnalysisService analysisService;

    @Mock
    private TemplateEngine templateEngine;

    @Mock
    private DomainScoreService domainScoreService;

    @Mock
    private RatingService ratingService;

    @Mock
    private NoteService noteService;

    @Mock
    private ReportDao reportDao;

    @Mock
    private UserProvider userProvider;

    @Mock
    private UserService userService;

    @Mock
    private FrameworkDao frameworkDao;

    @Mock
    private PortfolioService portfolioService;

    @Mock
    private PortfolioDao portfolioDao;

    @Mock
    private ScoreProvider scoreProvider;

    @Mock
    private DomainDao domainDao;

    @Mock
    private MediaDao mediaDao;

    @Mock
    private FileSystem fileSystem;

    @Mock
    private RemoteProvider remoteProvider;

    @Mock
    private UserDaoImpl userDao;

    @Mock
    private AgencyDao agencyDao;

    @Mock
    private CenterDao centerDao;

    @Mock
    private ReportBenchmarkViewDao benchmarkViewDao;

    @Mock
    private ReportBenchmarkSettingDao benchmarkSettingDao;

    @Mock
    private MetaDataDao usersMetaDataDao;

    @Mock
    private FrameworkProvider frameworkProvider;

    @Mock
    private PromptDao promptDao;

    @Mock
    private OpenAIProvider openAIProvider;

    @Mock
    private MediaService mediaService;

    @Mock
    private EmailService emailService;

    @Mock
    private NoteDao noteDao;

    @Mock
    private ShardingProvider shardingProvider;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        ReflectionTestUtils.setField(reportService,"idIT2015","it2015");
        ReflectionTestUtils.setField(reportService,"idPS2015","ps2015");
        ReflectionTestUtils.setField(reportService,"pdfBucket","pdfBucket");
        ReflectionTestUtils.setField(reportService,"s3Root","s3RootKey");
        ReflectionTestUtils.setField(reportService,"pdfEndpoint","pdfEndpointKey");
    }

    /**
     * 测试生成班级测评详细报告 PDF
     * case: 正常生成 PDF
     */
    @Test
    public void testGenerateGroupDetailMeasurePdfWhenAllParametersValid() {
        // 准备阶段
        MeasuresReportResponse reportInfo = new MeasuresReportResponse(); // 创建一个新的测评报告响应实体
        String pdfFileName = "pdfFileName"; // 设置 PDF 文件的名称
        ReportPdfBaseInfo homeBaseInfo = new ReportPdfBaseInfo(); // 创建一个新的报告 PDF 基本信息实体
        PdfType type = PdfType.GROUP_DETAIL_REPORT; // 设置 PDF 类型为班级详细报告
        String agencyId = "agencyId001"; // 设置机构 Id
        String groupId = "groupId001"; // 设置班级 Id
        String languageAbbr = "en-US"; // 设置语言为英语
        MockedStatic<WKHelper> wkHelperMockedStatic = mockStatic(WKHelper.class); // 创建一个 WKHelper 的模拟静态方法
        wkHelperMockedStatic.when(() -> WKHelper.upload(any(), any(), any())).thenReturn("publicUrl"); // 当调用 WKHelper 的 upload 方法时，返回一个公共 URL
        ReflectionTestUtils.setField(reportService, "endPoint", "endPoint"); // 设置报告服务的 endPoint 字段
        ReflectionTestUtils.setField(reportService, "pdfEndpoint", "pdfEndpoint"); // 设置报告服务的 pdfEndpoint 字段

        // 执行阶段
        assertDoesNotThrow(() -> reportService.generateGroupDetailMeasurePdf(reportInfo, pdfFileName, homeBaseInfo, type, agencyId, groupId, languageAbbr)); // 调用报告服务的 generateGroupDetailMeasurePdf 方法生成 PDF，如果有异常则抛出

        // 断言阶段
        verify(reportDao, times(1)).createPdfConvertJob(any(PdfConvertJobEntity.class)); // 验证 reportDao 的 createPdfConvertJob 方法被调用了一次
        verify(remoteProvider, times(1)).callPdfService(anyString(), any()); // 验证 remoteProvider的callPdfServer 方法被调用了一次
        wkHelperMockedStatic.close(); // 关闭 WKHelper 的模拟静态方法
    }

    /**
     * 测试生成班级测评详细报告 PDF
     * case: 没有选择语言，默认英语，也可以生成 PDF
     */
    @Test
    public void testGenerateGroupDetailMeasurePdfWhenLanguageAbbrNotProvided() {
        // 准备阶段
        MeasuresReportResponse reportInfo = new MeasuresReportResponse(); // 创建一个新的测评报告响应实体
        String pdfFileName = "pdfFileName"; // 设置 PDF 文件的名称
        ReportPdfBaseInfo homeBaseInfo = new ReportPdfBaseInfo(); // 创建一个新的报告 PDF 基本信息实体
        PdfType type = PdfType.GROUP_DETAIL_REPORT; // 设置 PDF 类型为班级详细报告
        String agencyId = "agencyId001"; // 设置机构 ID
        String groupId = "groupId001"; // 设置组 ID
        String languageAbbr = null; // 不设置语言缩写

        MockedStatic<WKHelper> wkHelperMockedStatic = mockStatic(WKHelper.class); // 创建一个 WKHelper 的模拟静态方法
        wkHelperMockedStatic.when(() -> WKHelper.upload(any(), any(), any())).thenReturn("publicUrl"); // 当调用 WKHelper 的 upload 方法时，返回一个公共 URL
        ReflectionTestUtils.setField(reportService, "endPoint", "endPoint"); // 设置报告服务的 endPoint 字段
        ReflectionTestUtils.setField(reportService, "pdfEndpoint", "pdfEndpoint"); // 设置报告服务的 pdfEndpoint 字段

        // 执行阶段
        assertDoesNotThrow(() -> reportService.generateGroupDetailMeasurePdf(reportInfo, pdfFileName, homeBaseInfo, type, agencyId, groupId, languageAbbr)); // 调用报告服务的 generateGroupDetailMeasurePdf 方法生成 PDF，如果有异常则抛出

        // 断言阶段
        verify(reportDao, times(1)).createPdfConvertJob(any(PdfConvertJobEntity.class)); // 验证 reportDao 的 createPdfConvertJob 方法被调用了一次
        verify(remoteProvider, times(1)).callPdfService(anyString(), any()); // 验证 remoteProvider 的 callPdfServer 方法被调用了一次
        wkHelperMockedStatic.close(); // 关闭 WKHelper 的模拟静态方法
    }

    /**
     * 测试生成班级测评详细报告 PDF
     * case: 没有传递机构 Id，也可以生成 PDF
     */
    @Test
    public void testGenerateGroupDetailMeasurePdfWhenAgencyIdNotProvided() {
        // 准备阶段
        MeasuresReportResponse reportInfo = new MeasuresReportResponse(); // 创建一个新的测评报告响应实体
        String pdfFileName = "pdfFileName"; // 设置 PDF 文件的名称
        ReportPdfBaseInfo homeBaseInfo = new ReportPdfBaseInfo(); // 创建一个新的报告 PDF 基本信息实体
        PdfType type = PdfType.GROUP_DETAIL_REPORT; // 设置 PDF 类型为班级详细报告
        String agencyId = null; // 不设置机构 ID
        String groupId = "groupId001"; // 设置组 ID
        String languageAbbr = "en-US"; // 设置语言为英语
        MockedStatic<WKHelper> wkHelperMockedStatic = mockStatic(WKHelper.class); // 创建一个 WKHelper 的模拟静态方法
        wkHelperMockedStatic.when(() -> WKHelper.upload(any(), any(), any())).thenReturn("publicUrl"); // 当调用 WKHelper 的 upload 方法时，返回一个公共 URL
        ReflectionTestUtils.setField(reportService, "endPoint", "endPoint"); // 设置报告服务的 endPoint 字段
        ReflectionTestUtils.setField(reportService, "pdfEndpoint", "pdfEndpoint"); // 设置报告服务的 pdfEndpoint 字段

        // 执行阶段
        assertDoesNotThrow(() -> reportService.generateGroupDetailMeasurePdf(reportInfo, pdfFileName, homeBaseInfo, type, agencyId, groupId, languageAbbr)); // 调用报告服务的 generateGroupDetailMeasurePdf 方法生成 PDF，如果有异常则抛出

        // 断言阶段
        verify(reportDao, times(1)).createPdfConvertJob(any(PdfConvertJobEntity.class)); // 验证 reportDao 的 createPdfConvertJob 方法被调用了一次
        verify(remoteProvider, times(1)).callPdfService(anyString(), any()); // 验证 remoteProvider 的 callPdfServer 方法被调用了一次
        wkHelperMockedStatic.close(); // 关闭 WKHelper 的模拟静态方法
    }

    /**
     * Create by hxl 2023/04/24.
     * 测试 ReportServiceImpl.getChildDomainReport 方法
     */
    @Test
    public void testGetChildDomainReportWhenSnapshotNotFoundShouldThrowBusinessException() {
        // arrange
        final String childId = "123";
        final String snapshotId = "456";
        final boolean openTarget = true;
        final String language = "en-ES";
        final StudentSnapshotEntity snapshotEntity = null;
        when(studentDao.getCurrentSnapshotBySnapshotId(snapshotId)).thenReturn(snapshotEntity);
        // act
        try {
            reportService.getChildDomainReport(childId, snapshotId, openTarget, language);
            fail("BusinessException should be thrown");
        } catch (BusinessException e) {
            // assert
            assertEquals(ErrorCode.SNAPSHOT_NOT_FOUND, e.getErrorCode());
        }
    }

    /**
     * Create by hxl 2023/04/24.
     * 测试 ReportServiceImpl.getChildDomainReport 方法
     */
    @Test
    public void testGetChildDomainReportWhenSnapshotFoundShouldReturnChildDomainViewResponse() {
        // arrange
        final String childId = "789";
        final String snapshotId = "101112";
        final boolean openTarget = true;
        final String language = "en-US";
        final String jsonLanguage = "{\n"
                + "\t\"Approaches to Learning - Self Regulation\":\"Enfoques al aprendizaje–Autoregulación\",\n"
                + "\t\"Social and Emotional Development\":\"Desarrollo socio-emocional\",\n"
                + "\t\"Language Development\":\"Desarrollo del Idioma\",\n"
                + "\t\"Literacy Development\":\"Desarrollo lectoescritura\",\n"
                + "\t\"Mathematics\":\"Matemáticas\",\n"
                + "\t\"Science\":\"Ciencia\",\n"
                + "\t\"Physical Development\":\"Desarrollo físico\",\n"
                + "\t\"Health\":\"Salud\",\n"
                + "\t\"History Social Science\":\"Ciencias sociales– Historia\",\n"
                + "\t\"Visual & Performing Arts\":\"Artes visuales y escénicas\",\n"
                + "\t\"English Language Development\":\"Desarrollo de la lengua inglesa\",\n"
                + "\t\"Language and Literacy Development\":\"Desarrollo del Idioma y lectoescritura\",\n"
                + "\t\"Cognition, Including Math and Science\":\"Desarrollo cognitivo, incluyendo atemáticas y Ciencias\",\n"
                + "\t\"Physical Development - Health\":\"Desarrollo físico–Salud\"\n"
                + "}";
        final JSONObject jsonObject = new JSONObject(jsonLanguage);
        final StudentSnapshotEntity snapshotEntity = new StudentSnapshotEntity();
        final ActionPlanEntity actionPlanEntity = new ActionPlanEntity();
        actionPlanEntity.setShowAllAlias(true);

        when(ratingService.getChildDomainLanguageJson(language)).thenReturn(jsonObject);
        when(studentDao.getCurrentSnapshotBySnapshotId(snapshotId)).thenReturn(snapshotEntity);
        when(reportDao.findActionPlanBySnapshotId(snapshotId)).thenReturn(actionPlanEntity);
        final SnapshotResponse snapshotResponse = new SnapshotResponse();
        when(analysisService.getSnapshot(snapshotEntity, false, null)).thenReturn(snapshotResponse);
        final ChildDomainViewResponse expectedResponse = new ChildDomainViewResponse();
        when(analysisService.getChildDomainReport(snapshotResponse, true)).thenReturn(expectedResponse);
        final FrameworkData snapshotDomainScore = new FrameworkData();
        when(domainScoreService.getSnapshotDomainScore(snapshotEntity)).thenReturn(snapshotDomainScore);
        // act
        final ChildDomainViewResponse actualResponse = reportService.getChildDomainReport(childId, snapshotId, openTarget, language);
        // assert
        assertSame(expectedResponse, actualResponse);
        assertTrue(actualResponse.isShowAllAlias());
    }

    /**
     * 测试生成家长报告 PDF
     */
    @Test
    public void testGetParentReportPDF() {
        // 模拟数据
        StudentSnapshotEntity studentSnapshotEntity = new StudentSnapshotEntity();
        SnapshotResponse snapshotResponse = new SnapshotResponse();
        RatingRecordModel ratingRecord = new RatingRecordModel();
        ratingRecord.setFrameworkId("frameworkId");
        ratingRecord.setFramework("frameworkName");
        snapshotResponse.setRatingRecord(ratingRecord);
        snapshotResponse.setPeriodAlias("2023-2024 Fall");

        ScoreTemplateEntity scoreTemplateEntity = new ScoreTemplateEntity();
        scoreTemplateEntity.setDomainLevelsJson("[{\"domainId\":\"domainId\",\"measure\":\"SL.K.1\",\"measureName\":\"measureName\",\"levels\":[{\"id\":\"DE3E297E-AD26-4DB6-88D6-D917A45534A8\",\"name\":\"0 - No evidence\",\"type\":\"radio\",\"sortIndex\":5,\"value\":0,\"tip\":\"There is no, or insufficient, evidence of learning to assess the standard at this time.\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"E1\",\"content\":[\"ILCCSSK01\"],\"columnSize\":1}]},{\"id\":\"A09912CD-3474-4FE5-8721-BF76CBC4AFCE\",\"name\":\"1 - Below basic\",\"type\":\"radio\",\"sortIndex\":4,\"value\":1,\"tip\":\"Demonstrates a below basic understanding of the standard; may demonstrate gaps in skills and knowledge.\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"E1\",\"content\":[\"ILCCSSK02\"],\"columnSize\":1}]},{\"id\":\"6FF44F0D-E8FB-48AE-8208-105EE6CF089B\",\"name\":\"2 - Basic\",\"type\":\"radio\",\"sortIndex\":3,\"value\":2,\"tip\":\"Demonstrates a basic understanding of the skills and knowledge of the standard.\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"E1\",\"content\":[\"ILCCSSK31\"],\"columnSize\":1}]},{\"id\":\"E3297408-93AD-43E2-87CF-4CBF59A1E787\",\"name\":\"3 - Proficient\",\"type\":\"radio\",\"sortIndex\":2,\"value\":3,\"tip\":\"Demonstrates skills and knowledge of the standard.\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"E1\",\"content\":[\"ILCCSSK62\"],\"columnSize\":1}]},{\"id\":\"96BB980A-C9B0-43E9-88C7-B5E4E33A6B56\",\"name\":\"4 - Mastery\",\"type\":\"radio\",\"sortIndex\":1,\"value\":4,\"tip\":\"Demonstrates ability to apply extended thinking about the skills and knowledge of the standard.\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"E1\",\"content\":[\"ILCCSSK92\"],\"columnSize\":1}]},{\"id\":\"43F20A31-C8BE-4DA7-BDFE-45959C8D61C5\",\"name\":\"NE - Not evaluated\",\"rateWithoutEvidence\":true,\"type\":\"radio\",\"sortIndex\":6,\"value\":\"NE\"}]}]");
        scoreTemplateEntity.setLevelsJson("[ { \"id\": \"96BB980A-C9B0-43E9-88C7-B5E4E33A6B56\", \"name\": \"4 - Mastery\",\"type\": \"radio\",\"sortIndex\": 1,\"value\": 4},{\"id\": \"E3297408-93AD-43E2-87CF-4CBF59A1E787\",\"name\": \"3 - Proficient\",\"type\": \"radio\",\"sortIndex\": 2,\"value\": 3},{\"id\": \"6FF44F0D-E8FB-48AE-8208-105EE6CF089B\",\"name\": \"2 - Basic\",\"type\": \"radio\",\"sortIndex\": 3,\"value\": 2},{\"id\": \"A09912CD-3474-4FE5-8721-BF76CBC4AFCE\",\"name\": \"1 - Below basic\",\"type\": \"radio\",\"sortIndex\": 4,\"value\": 1},{\"id\": \"DE3E297E-AD26-4DB6-88D6-D917A45534A8\",\"name\": \"0 - No evidence\",\"type\": \"radio\",\"sortIndex\": 5,\"value\": 0},{\"id\": \"43F20A31-C8BE-4DA7-BDFE-45959C8D61C5\",\"name\": \"NE - Not evaluated\",\"type\": \"radio\",\"sortIndex\": 6,\"value\": \"NE\" }]");
        ChildDomainViewResponse childDomainViewResponse = new ChildDomainViewResponse();


        LGSnapshot.RatingRecords ratingRecords = LGSnapshot.RatingRecords.newBuilder()
                .setFrameworkId("frameworkId")
                .setFramework("frameworkName")
                .setPeriodAlias("2023-2024 Fall")
                .buildPartial();
        LGSnapshot.StudentSnapshot snapshot = LGSnapshot.StudentSnapshot.newBuilder()
                .setAge(1)
                .setRatingRecords(ratingRecords)
                .buildPartial();

        ActionPlanEntity actionPlanEntity = new ActionPlanEntity();
        actionPlanEntity.setId("actionPlanId");
        actionPlanEntity.setTeacherResponseRecordId("teacherResponseRecordId");

        HashMap<String, List<DomainEntity>> domainMap = new HashMap<>();
        List<DomainEntity> domainEntityList = new ArrayList<>();
        DomainEntity domainEntity = new DomainEntity();
        domainEntity.setId("domainId");
        domainEntity.setName("domainName");
        domainEntityList.add(domainEntity);
        domainMap.put("domainId", domainEntityList);

        // 模拟方法调用
        when(userProvider.getCurrentLang()).thenReturn("en-us");
        when(studentDao.getCurrentSnapshotBySnapshotId("snapshotId")).thenReturn(studentSnapshotEntity);
        when(analysisService.getSnapshot(studentSnapshotEntity, false, null)).thenReturn(snapshotResponse);
        when(analysisService.getChildDomainReport(snapshotResponse, true)).thenReturn(childDomainViewResponse);
        when(reportDao.getScoreComment("snapshotId")).thenReturn(null);
        when(portfolioService.getScoreTemplate("frameworkId")).thenReturn(scoreTemplateEntity);
        when(portfolioDao.loadScoreTemplate("frameworkId")).thenReturn(scoreTemplateEntity);
        when(scoreProvider.getDomainMeasureMap("frameworkId")).thenReturn(domainMap);
        when(domainDao.getMeasure("domainId")).thenReturn(domainEntity);
        MockedStatic<LGSnapshot.StudentSnapshot> studentSnapshotMockedStatic = mockStatic(LGSnapshot.StudentSnapshot.class);
        studentSnapshotMockedStatic.when(() -> LGSnapshot.StudentSnapshot.parseFrom(studentSnapshotEntity.getData())).thenReturn(snapshot);
        when(reportDao.findActionPlanByChildIdAndFrameworkIdAndPeriodAlias("childId", "2023-2024 Fall", "frameworkId")).thenReturn(actionPlanEntity);
        when(reportDao.findActionPlanDetailByPlanId("actionPlanId")).thenReturn(new ArrayList<>());
        when(mediaDao.getMediaById("teacherResponseRecordId")).thenReturn(null);
        when(noteService.translate(any())).thenReturn(new TranslationResponse());
        when(fileSystem.getPublicUrl(any(), any())).thenReturn("publicUrl");
        ReflectionTestUtils.setField(reportService, "endPoint", "endPoint");
        ReflectionTestUtils.setField(reportService, "pdfEndpoint", "pdfEndpoint");

        // 调用方法
        reportService.getParentReportPDF("snapshotId", "language", "measure", "childId", false, true);

        // 验证方法调用次数
        verify(remoteProvider, times(1)).callPdfService(anyString(), any());

        // 关闭模拟的静态方法
        studentSnapshotMockedStatic.close();
    }

    /**
     * 测试查询 School Readiness Measure Report
     */
    @Test
    public void testGetSchoolReadinessMeasureReport() {
        // 模拟数据
        String requestJson = "{\n" +
                "    \"agencyId\": \"\",\n" +
                "    \"agencyIds\": [\n" +
                "        \"7396EC65-751D-4CE8-A191-33EBE3B97B86\"\n" +
                "    ],\n" +
                "    \"alias\": [\n" +
                "        \"2023-2024 Time4\"\n" +
                "    ],\n" +
                "    \"periodAlias\": \"2023-2024 Time4\",\n" +
                "    \"frameworkIds\": [\n" +
                "        \"********-BDCE-E411-AF66-02C72B94B99B\",\n" +
                "        \"E163164F-BDCE-E411-AF66-02C72B94B99B\"\n" +
                "    ],\n" +
                "    \"centerIds\": [\n" +
                "        \"sc001\"\n" +
                "    ],\n" +
                "    \"centerNames\": [\n" +
                "        \"Aagle Flight School Aagle Flight School Aagle Flight School\"\n" +
                "    ],\n" +
                "    \"groupId\": \"\",\n" +
                "    \"groupIds\": [\n" +
                "        \"sg001\",\n" +
                "        \"457C4E2B-4047-4D62-9CF1-D60708F499CC\"\n" +
                "    ],\n" +
                "    \"groupNames\": [\n" +
                "        \"ITC-Demo\",\n" +
                "        \"KF-TEST\"\n" +
                "    ],\n" +
                "    \"attrFilters\": [],\n" +
                "    \"birthFrom\": \"\",\n" +
                "    \"birthTo\": \"\",\n" +
                "    \"entryFrom\": \"\",\n" +
                "    \"entryTo\": \"\",\n" +
                "    \"merge\": true,\n" +
                "    \"viewIds\": [\n" +
                "        \"807AC7C0-C45D-4B70-A543-3ADBB1D79264\"\n" +
                "    ]\n" +
                "}";
        // 准备请求参数
        GetSchoolReadinessMeasureRequest readinessMeasureRequest = JsonUtil.fromJson(requestJson, GetSchoolReadinessMeasureRequest.class);
        // 用户 ID
        String userId = "userId001";
        String agencyId = "agencyId001";
        com.learninggenie.common.data.entity.UserEntity user = new com.learninggenie.common.data.entity.UserEntity();
        user.setUserName(userId);
        user.setRole("COLLABORATOR");
        AuthUserDetails authUserDetails = new AuthUserDetails();
        authUserDetails.setUsername(userId);
        authUserDetails.setAgencyId(agencyId);
        UserModel userModel = new UserModel();
        userModel.setId(userId);
        userModel.setRole("COLLABORATOR");
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        List<AgencySnapshotEntity> agencySnapshots = new ArrayList<>();
        AgencySnapshotEntity agencySnapshot = new AgencySnapshotEntity();
        Date utcNow = TimeUtil.getUtcNow();
        agencySnapshot.setId("agencySnapshotId");
        agencySnapshot.setAgencyId(agencyId);
        agencySnapshot.setPeriodAlias("Winter 2024");
        agencySnapshot.setUpdateAtUtc(utcNow);
        LGSnapshot.StudentRecordSnapshot studentSnapshot = LGSnapshot.StudentRecordSnapshot.newBuilder()
                .setRecordId("r001")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .build();
        LGSnapshot.ClassSnapshot classSnapshot =LGSnapshot.ClassSnapshot.newBuilder()
                .setId("sg001")
                .setName("sg001")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .addRecords(studentSnapshot)
                .build();
        LGSnapshot.CenterSnapshot centerSnapshot = LGSnapshot.CenterSnapshot.newBuilder()
                .setId("sc001")
                .setName("sc001")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .addClasses(classSnapshot)
                .build();
        LGSnapshot.Properties property = LGSnapshot.Properties.newBuilder()
                .setName("a001")
                .setValue("avalue001")
                .setType("atype001")
                .build();
        LGSnapshot.AgencySnapshot agencySnapshotData = LGSnapshot.AgencySnapshot.newBuilder()
                .setId("a000001")
                .setName("s000001")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .addCenters(centerSnapshot)
                .addProperties(property)
                .build();
        agencySnapshot.setData(agencySnapshotData.toByteArray());
        agencySnapshots.add(agencySnapshot);
        List<CenterModel> centerGroups = new ArrayList<>();
        CenterModel centerModel = new CenterModel();
        centerModel.setAgencyId(agencyId);
        centerModel.setGroupName("GroupName");
        centerModel.setGroupId("groupId001");
        centerGroups.add(centerModel);
        List<String> snapshotIds = new ArrayList<>();
        snapshotIds.add("r001");
        List<StudentSnapshotEntity> studentSnapshots = new ArrayList<>();
        StudentSnapshotEntity studentSnapshotEntity = new StudentSnapshotEntity();
        studentSnapshotEntity.setId("studentSnapshotId001");
        studentSnapshotEntity.setEnrollmentId("enrollmentId001");
        studentSnapshotEntity.setData(studentSnapshot.toByteArray());
        studentSnapshotEntity.setGroupId("groupId001");
        studentSnapshotEntity.setAgencyId(agencyId);
        studentSnapshots.add(studentSnapshotEntity);
        SnapshotResponse response = new SnapshotResponse();
        response.setId("childSnapshot001");
        response.setAgencyId(agencyId);
        StudentAttr attr = new StudentAttr();
        attr.setName(DrdpAttr.IEP.toString());
        attr.setValues(Arrays.asList(new String[]{"Yes"}));
        response.setAttrs(Arrays.asList(attr));
        List<String> groupIds = new ArrayList<>();
        groupIds.add("groupId001");
        groupIds.add("groupId002");
        String itLevelJson = "[{\"id\":\"F88C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"F98CD7D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"FA8C1ED2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"FB8C17DB-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Exploring  Middle\",\"type\":\"radio\",\"sortIndex\":\"3\",\"value\":\"4\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"FC8C17DQ-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"FD8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"FE8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Building Middle\",\"type\":\"text\",\"sortIndex\":\"6\",\"value\":\"7\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"FF8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Building Later\",\"type\":\"text\",\"sortIndex\":\"7\",\"value\":\"8\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"008D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Integrating  Earlier\",\"type\":\"text\",\"sortIndex\":\"8\",\"value\":\"9\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"058D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"048D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]}]";

        String domainJson = "[{\"domainId\":\"4CD0392C-55D3-EB11-9C19-4CCC6ACF6129\",\"measure\":\"ATL-REG1\",\"measureName\":\"Attention Maintenance\",\"levels\":[{\"id\":\"F98C1CD2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Shifts attention frequently from one person or thing to another\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"Example\",\"content\":[\"Turns attention toward an interesting toy, then back to an adult or a child.\",\"Actively shifts interest from one child to another playing close by.\",\"Drops one thing in order to reach for another.\"],\"columnSize\":1}],\"rateWithoutEvidence\":false},{\"id\":\"AA8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"tip\":\"Maintains attention, on own or with adult support, during brief activities\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"Example\",\"content\":[\"Briefly watches other children playing and then resumes play with a toy.\",\"Resumes playing at sand table when an adult joins in digging.\",\"Dumps toy animals from container, puts animals back in the container, and then dumps them out again.\"],\"columnSize\":1}],\"rateWithoutEvidence\":false},{\"id\":\"FC8CC7D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Maintains attention, with adult support, during activities that last for extended periods of time\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"Example\",\"content\":[\"Listens to a book from beginning to end and then gestures for an adult to read it a second time. \",\"Starts working on a simple puzzle with an adult and continues when the adult steps away briefly.\",\"Continues playing with toy cars, adding a bridge offered by an adult sitting nearby.\"],\"columnSize\":1}],\"rateWithoutEvidence\":false},{\"id\":\"FA8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Maintains attention on own during activities that last for extended periods of time\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"Example\",\"content\":[\"Makes a pile of pretend pancakes with play dough on own and then offers them to peers.\",\"Builds multiple towers with interlocking blocks.\",\"Looks through several books on own in library corner during the morning.\",\"Listens to audio books while looking at enlarged pictures related to the story on a screen, on own, during the morning.\"],\"columnSize\":1}],\"rateWithoutEvidence\":false},{\"id\":\"058D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[],\"rateWithoutEvidence\":false},{\"id\":\"048D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[],\"rateWithoutEvidence\":false},{\"id\":\"FE8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"tip\":\"Attends or responds briefly to people, things, or sounds.\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"Example\",\"content\":[\"Pays attention to a moving mobile.\",\"Quiets to the voice of a familiar person.\",\"Gazes at the smiling face of a familiar person.\"],\"columnSize\":1}],\"rateWithoutEvidence\":false}],\"levelMap\":{}}]";

        String psLevelJson = "[{\"id\":\"FD8C17D2-6DD9-EB11-9C19-4CCC6ACF6A29\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"tip\":\"Responds to sensory informationor input (e.g.,visual, auditory, tactile)with basic movementsof body parts\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"F98C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Responding  Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Responds to sensoryinformation by movingbody or limbs to reachfor or move toward\\npeople or objects\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"FA8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"FV8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\" Exploring Middle\",\"type\":\"radio\",\"sortIndex\":\"3\",\"value\":\"4\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"FC8CD7D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\" Exploring  Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"FD8C17D2-6DD9-EB1F-9C19-4CCC6ACF6129\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"FE8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Building Middle\",\"type\":\"radio\",\"sortIndex\":\"6\",\"value\":\"7\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"FF8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Building Later\",\"type\":\"radio\",\"sortIndex\":\"7\",\"value\":\"8\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"008D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Integrating Earlier\",\"type\":\"radio\",\"sortIndex\":\"8\",\"value\":\"9\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"038D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Conditional (not rated)\",\"type\":\"radio\",\"sortIndex\":\"9\",\"value\":\"b\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"048D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"UR (Unable to Rate)\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"058D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"12\",\"value\":\"e\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"068D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Discovering Language\",\"type\":\"radio\",\"sortIndex\":\"15\",\"value\":\"16\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"078D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Discovering English\",\"type\":\"radio\",\"sortIndex\":\"16\",\"value\":\"17\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"088D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Exploring English\",\"type\":\"radio\",\"sortIndex\":\"17\",\"value\":\"18\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"098D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Developing English\",\"type\":\"radio\",\"sortIndex\":\"18\",\"value\":\"19\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"0A8D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Building English\",\"type\":\"radio\",\"sortIndex\":\"19\",\"value\":\"20\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"0B8D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Integrating English\",\"type\":\"radio\",\"sortIndex\":\"20\",\"value\":\"21\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"0C8D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Not Yet\",\"type\":\"radio\",\"sortIndex\":\"21\",\"value\":\"n\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]}]";

        String psDomainJson = "[{\"domainId\":\"DDD0392C-55D3-EB11-9C19-4CCC6ACF6129\",\"measure\":\"LLD8\",\"measureName\":\"Phonological Awareness\",\"levels\":[{\"id\":\"FB8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\" Exploring Middle\",\"type\":\"radio\",\"sortIndex\":\"3\",\"value\":\"4\",\"tip\":\"Attends to sounds or elements of language\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"Example\",\"content\":[\"Turns toward adult when adult sings a song.\",\"Looks at adult’s hands when adult signs \\\"more.\\\"\",\"Attends to adult saying, \\\"bye-bye.\\\"\"],\"columnSize\":1}],\"rateWithoutEvidence\":false},{\"id\":\"FC8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\" Exploring  Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Demonstrates awareness of variations in sounds\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"Example\",\"content\":[\"Whispers a word, and then says it loudly.\",\"Communicates, \\\"No, no, no, no, no,\\\" varying pitch.\",\"Uses sounds or hand movements to play with variations of stress and rhythm.\"],\"columnSize\":1}],\"rateWithoutEvidence\":false},{\"id\":\"FD8C1A7D2-6DD9-EB11-9C1C-4CCC6ACF6129\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Engages actively in play with sounds in words or rhymes, or Sings simple songs, or Repeats simple nursery rhymes\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"Example\",\"content\":[\"Rhymes children’s names with other words during a group sing-along.\",\"Sings \\\"Twinkle, Twinkle, Little Star\\\" with a group.\",\"Communicates the rhyming word \\\"fall,\\\" after an adult says, \\\"Humpty Dumpty sat on a wall. Humpty Dumpty had a great . . .?\\\"\",\"Uses signs to participate in a song such as \\\"The Wheels on the Bus.\\\"\"],\"columnSize\":1}],\"rateWithoutEvidence\":false},{\"id\":\"FE8CG17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Building Middle\",\"type\":\"radio\",\"sortIndex\":\"6\",\"value\":\"7\",\"tip\":\"Demonstrates awareness of larger units of language (e.g., words, syllables)\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"Example\",\"content\":[\"Drums out each word in \\\"I am Matt\\\" in a name game in the classroom, after an adult has modeled drumming while saying single words.\",\"Claps the syllables in familiar words, such as children’s names or days of the week, with adult and peers.\",\"Moves arms each time the word \\\"row\\\" is said in the song \\\"Row, Row, Row Your Boat,\\\" with adult and peers.\"],\"columnSize\":1}],\"rateWithoutEvidence\":false},{\"id\":\"FF8C17D2-6DD9-EB1Y-9C19-4CCC6ACF6129\",\"name\":\"Building Later\",\"type\":\"radio\",\"sortIndex\":\"7\",\"value\":\"8\",\"tip\":\"Blends larger units of lan- guage (e.g., compound words and syllables) with or without the support of pictures or objects; and Segments larger units of language (e.g., compound words and syllables) with or without the support of pictures or objects\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"Example\",\"content\":[\"Answers, \\\"Goldfish,\\\" after an adult asks, \\\"If you put together the words ‘gold’ and ‘fish,’ what word does that make?\\\"\",\"Communicates, \\\"Rain,\\\" after an adult communicates, \\\"There are two words in ‘raincoat.’ What happens when we take away the word ‘coat’?\\\" while moving a picture of a coat away from a picture of rain.\",\"Communicates, \\\"Zebra,\\\" after an adult separates the word into syllables, and says, \\\"Ze–,\\\" and \\\"–bra,\\\" while looking at a wordless picture book about the zoo.\",\"Communicates, \\\"Marker,\\\" after an adult communicates, \\\"What happens when I put the two syllables ‘mark–’ and ‘–er’ together?\\\"\"],\"columnSize\":1}],\"rateWithoutEvidence\":false},{\"id\":\"0T8D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"UR (Unable to Rate)\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[],\"rateWithoutEvidence\":false},{\"id\":\"058D17DL-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"12\",\"value\":\"e\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[],\"rateWithoutEvidence\":false},{\"id\":\"008D17DP-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Integrating Earlier\",\"type\":\"radio\",\"sortIndex\":\"8\",\"value\":\"9\",\"tip\":\"Blends smaller units of language (e.g., onsets and rimes), with or without the support of pictures or objects; and Segments smaller units of language (e.g., onsets and rimes), with or without the support of pictures or objects\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"Example\",\"content\":[\"Communicates, \\\"Cup,\\\" at the snack table, after an adult says, \\\"I have a c–up. What do I have?\\\"\",\"Communicates, \\\"Ice,\\\" after an adult asks what word is left when the m– is removed from the word \\\"mice,\\\" while playing a word game.\",\"Communicates, \\\"d\\\" (letter sound), while looking at a picture of a dog, after an adult says, \\\"What is the first sound you hear when you say ‘dog’?\\\" while looking at pictures of dogs together.\"],\"columnSize\":1}],\"rateWithoutEvidence\":false},{\"id\":\"0C8D17DK-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Not Yet\",\"type\":\"radio\",\"sortIndex\":\"21\",\"value\":\"n\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[],\"rateWithoutEvidence\":false}],\"levelMap\":{}}]";
        ScoreTemplateEntity itScoreTemplate = new ScoreTemplateEntity();
        itScoreTemplate.setDomainLevelsJson(domainJson);
        itScoreTemplate.setLevelsJson(itLevelJson);
        itScoreTemplate.setPortfolioId("portfolioId001");
        ScoreTemplateEntity psScoreTemplate = new ScoreTemplateEntity();
        psScoreTemplate.setDomainLevelsJson(psDomainJson);
        psScoreTemplate.setLevelsJson(psLevelJson);
        psScoreTemplate.setPortfolioId("portfolioId002");
        RatingRecordModel ratingRecordModel = new RatingRecordModel();
        ratingRecordModel.setFrameworkId("********-BDCE-E411-AF66-02C72B94B99B");
        ratingRecordModel.setPeriodAlias("2023-2024 Time4");
        SnapshotResponse snapshotResponse = new SnapshotResponse();
        snapshotResponse.setId("snapshotId");
        snapshotResponse.setAgencyId(agencyId);
        snapshotResponse.setPeriodAlias("2023-2024 Time4");
        snapshotResponse.setFrameworkId("********-BDCE-E411-AF66-02C72B94B99B");
        snapshotResponse.setFrameworkName("frameworkName");
        snapshotResponse.setEnrollmentId("enrollmentId");
        snapshotResponse.setRatingRecord(ratingRecordModel);
        snapshotResponse.setAgeMonth(1);
        MetaDataEntity metaDataEntity = new MetaDataEntity();
        metaDataEntity.setMetaKey("SCHOOL_READINESS_MEASURE_VIEW_GUIDE");
        metaDataEntity.setUserId(userId);
        metaDataEntity.setMetaValue("true");
        MetaDataEntity defaultMetaDataEntity = new MetaDataEntity();
        defaultMetaDataEntity.setMetaKey("SCHOOL_READINESS_MEASURE_VIEW_DO_NOT_ASK_AGAIN");
        defaultMetaDataEntity.setUserId(userId);
        defaultMetaDataEntity.setMetaValue("true");
        List<ReportBenchmarkViewEntity> views = new ArrayList<>();
        ReportBenchmarkViewEntity view = new ReportBenchmarkViewEntity();
        view.setId("807AC7C0-C45D-4B70-A543-3ADBB1D79264");
        view.setAgencyId(agencyId);
        view.setCreateUserId(userId);
        view.setType("DRDP_REPORT_MEASURE");
        view.setId("807AC7C0-C45D-4B70-A543-3ADBB1D79264");
        view.setAgencyId(agencyId);
        view.setName("DRDP Report Measure");
        view.setDeleted(false);
        view.setFrameworkId("********-BDCE-E411-AF66-02C72B94B99B");
        view.setSortIndex(1);
        view.setShowSettingDomain(true);
        views.add(view);

        // 模拟方法
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.checkUser(userId)).thenReturn(user);
        when(userProvider.getCurrentUser()).thenReturn(authUserDetails);
        when(userDao.getUserById(userId)).thenReturn(userModel);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        when(agencyDao.getSnapshot(agencyId, readinessMeasureRequest.getAlias())).thenReturn(agencySnapshots);
        when(centerDao.getCenterAndGroupsByTeacherId(userId)).thenReturn(centerGroups);
        when(studentDao.getSnapshotsByIds(agencyId, agencySnapshot.getPeriodAlias(), utcNow.getTime(), snapshotIds)).thenReturn(studentSnapshots);
        when(agencyDao.isDRDPtech(agencyId)).thenReturn(false);
        when(analysisService.mapSnapshot(studentSnapshotEntity, true, agencySnapshotData, new HashMap<>(),  false,  "en-US",  new HashMap<>(),  false)).thenReturn(snapshotResponse);
        when(benchmarkViewDao.listByAgencyIdAndTypeAndLevel(agencyId, ReportViewTypeEnum.DRDP_REPORT_MEASURE, ReportViewLevelEnum.AGENCY.toString())).thenReturn(new ArrayList<>());
        lenient().when(benchmarkSettingDao.listByViewIdIn(groupIds)).thenReturn(new ArrayList<>());
        when(portfolioService.getScoreTemplate("it2015")).thenReturn(itScoreTemplate);
        when(portfolioService.getScoreTemplate("ps2015")).thenReturn(psScoreTemplate);
        when(usersMetaDataDao.getByUserIdAndMetaKey(userId, UserMetaKey.SCHOOL_READINESS_MEASURE_VIEW_GUIDE.toString())).thenReturn(metaDataEntity);
        when(usersMetaDataDao.getByUserIdAndMetaKey(userId, UserMetaKey.SCHOOL_READINESS_MEASURE_VIEW_DO_NOT_ASK_AGAIN.toString())).thenReturn(defaultMetaDataEntity);
        lenient().when(benchmarkViewDao.listByAgencyIdAndTypeAndLevel(agencyId, ReportViewTypeEnum.DRDP_REPORT_MEASURE, ReportViewLevelEnum.AGENCY.toString())).thenReturn(new ArrayList<>());
        when(benchmarkViewDao.listByAgencyIdAndCreateUserIdInAndTypeAndLevel(agencyId, Collections.singletonList(userId), ReportViewTypeEnum.DRDP_REPORT_MEASURE, ReportViewLevelEnum.USER.toString())).thenReturn(views);
        when(userProvider.getCurrentAgencyId()).thenReturn(agencyId);
        when(userProvider.getAgencyByUser(user)).thenReturn(agencyModel);
        GetSchoolReadinessMeasureResponse measureReport = reportService.getSchoolReadinessMeasureReport(readinessMeasureRequest);

        // 校验结果
        Assert.assertEquals(1, measureReport.getChildCount().longValue());
        Assert.assertEquals(1, measureReport.getPeriodDisplayNames().size());
        Assert.assertEquals("2023-2024 Time4 (4th Assessment)", measureReport.getPeriodDisplayNames().get(0));
        Assert.assertEquals(0, measureReport.getMeasureGroups().size());
        Assert.assertEquals(0, measureReport.getAgeGroups().size());
        Assert.assertEquals(0, measureReport.getIep().size());
    }

    /**
     * 测试生成 School Readiness Measure Report PDF
     */
    @Test
    public void testGenerateSchoolReadinessMeasureReportPdf() {
        // 模拟数据
        String requestJson = "{\n" +
                "    \"agencyId\": \"\",\n" +
                "    \"agencyIds\": [\n" +
                "        \"7396EC65-751D-4CE8-A191-33EBE3B97B86\"\n" +
                "    ],\n" +
                "    \"alias\": [\n" +
                "        \"2023-2024 Time4\"\n" +
                "    ],\n" +
                "    \"periodAlias\": \"2023-2024 Time4\",\n" +
                "    \"frameworkIds\": [\n" +
                "        \"********-BDCE-E411-AF66-02C72B94B99B\",\n" +
                "        \"E163164F-BDCE-E411-AF66-02C72B94B99B\"\n" +
                "    ],\n" +
                "    \"centerIds\": [\n" +
                "        \"sc001\"\n" +
                "    ],\n" +
                "    \"centerNames\": [\n" +
                "        \"Aagle Flight School Aagle Flight School Aagle Flight School\"\n" +
                "    ],\n" +
                "    \"groupId\": \"\",\n" +
                "    \"groupIds\": [\n" +
                "        \"sg001\",\n" +
                "        \"457C4E2B-4047-4D62-9CF1-D60708F499CC\"\n" +
                "    ],\n" +
                "    \"groupNames\": [\n" +
                "        \"ITC-Demo\",\n" +
                "        \"KF-TEST\"\n" +
                "    ],\n" +
                "    \"attrFilters\": [],\n" +
                "    \"birthFrom\": \"\",\n" +
                "    \"birthTo\": \"\",\n" +
                "    \"entryFrom\": \"\",\n" +
                "    \"entryTo\": \"\",\n" +
                "    \"merge\": true,\n" +
                "    \"viewIds\": [\n" +
                "        \"807AC7C0-C45D-4B70-A543-3ADBB1D79264\"\n" +
                "    ]\n" +
                "}";
        // 准备请求参数
        SchoolReadinessMeasureRequest readinessMeasureRequest = JsonUtil.fromJson(requestJson, SchoolReadinessMeasureRequest.class);
        readinessMeasureRequest.setAllGroupCount(1);
        readinessMeasureRequest.setAllCenterCount(1);
        // 用户 ID
        String userId = "userId001";
        String agencyId = "agencyId001";
        com.learninggenie.common.data.entity.UserEntity user = new com.learninggenie.common.data.entity.UserEntity();
        user.setUserName(userId);
        user.setRole("COLLABORATOR");
        AuthUserDetails authUserDetails = new AuthUserDetails();
        authUserDetails.setUsername(userId);
        authUserDetails.setAgencyId(agencyId);
        UserModel userModel = new UserModel();
        userModel.setId(userId);
        userModel.setRole("COLLABORATOR");
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        List<AgencySnapshotEntity> agencySnapshots = new ArrayList<>();
        AgencySnapshotEntity agencySnapshot = new AgencySnapshotEntity();
        Date utcNow = TimeUtil.getUtcNow();
        agencySnapshot.setId("agencySnapshotId");
        agencySnapshot.setAgencyId(agencyId);
        agencySnapshot.setPeriodAlias("Winter 2024");
        agencySnapshot.setUpdateAtUtc(utcNow);
        LGSnapshot.StudentRecordSnapshot studentSnapshot = LGSnapshot.StudentRecordSnapshot.newBuilder()
                .setRecordId("r001")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .build();
        LGSnapshot.ClassSnapshot classSnapshot =LGSnapshot.ClassSnapshot.newBuilder()
                .setId("sg001")
                .setName("sg001")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .addRecords(studentSnapshot)
                .build();
        LGSnapshot.CenterSnapshot centerSnapshot = LGSnapshot.CenterSnapshot.newBuilder()
                .setId("sc001")
                .setName("sc001")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .addClasses(classSnapshot)
                .build();
        LGSnapshot.Properties property = LGSnapshot.Properties.newBuilder()
                .setName("a001")
                .setValue("avalue001")
                .setType("atype001")
                .build();
        LGSnapshot.AgencySnapshot agencySnapshotData = LGSnapshot.AgencySnapshot.newBuilder()
                .setId("a000001")
                .setName("s000001")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .addCenters(centerSnapshot)
                .addProperties(property)
                .build();
        agencySnapshot.setData(agencySnapshotData.toByteArray());
        agencySnapshots.add(agencySnapshot);
        List<CenterModel> centerGroups = new ArrayList<>();
        CenterModel centerModel = new CenterModel();
        centerModel.setAgencyId(agencyId);
        centerModel.setGroupName("GroupName");
        centerModel.setGroupId("groupId001");
        centerGroups.add(centerModel);
        List<String> snapshotIds = new ArrayList<>();
        snapshotIds.add("r001");
        List<StudentSnapshotEntity> studentSnapshots = new ArrayList<>();
        StudentSnapshotEntity studentSnapshotEntity = new StudentSnapshotEntity();
        studentSnapshotEntity.setId("studentSnapshotId001");
        studentSnapshotEntity.setEnrollmentId("enrollmentId001");
        studentSnapshotEntity.setData(studentSnapshot.toByteArray());
        studentSnapshotEntity.setGroupId("groupId001");
        studentSnapshotEntity.setAgencyId(agencyId);
        studentSnapshots.add(studentSnapshotEntity);
        SnapshotResponse response = new SnapshotResponse();
        response.setId("childSnapshot001");
        response.setAgencyId(agencyId);
        StudentAttr attr = new StudentAttr();
        attr.setName(DrdpAttr.IEP.toString());
        attr.setValues(Arrays.asList(new String[]{"Yes"}));
        response.setAttrs(Arrays.asList(attr));
        List<String> groupIds = new ArrayList<>();
        groupIds.add("groupId001");
        groupIds.add("groupId002");
        String itLevelJson = "[{\"id\":\"F88C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"F98CD7D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"FA8C1ED2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"FB8C17DB-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Exploring  Middle\",\"type\":\"radio\",\"sortIndex\":\"3\",\"value\":\"4\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"FC8C17DQ-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"FD8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"FE8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Building Middle\",\"type\":\"text\",\"sortIndex\":\"6\",\"value\":\"7\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"FF8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Building Later\",\"type\":\"text\",\"sortIndex\":\"7\",\"value\":\"8\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"008D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Integrating  Earlier\",\"type\":\"text\",\"sortIndex\":\"8\",\"value\":\"9\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"058D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"048D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]}]";

        String domainJson = "[{\"domainId\":\"4CD0392C-55D3-EB11-9C19-4CCC6ACF6129\",\"measure\":\"ATL-REG1\",\"measureName\":\"Attention Maintenance\",\"levels\":[{\"id\":\"F98C1CD2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Shifts attention frequently from one person or thing to another\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"Example\",\"content\":[\"Turns attention toward an interesting toy, then back to an adult or a child.\",\"Actively shifts interest from one child to another playing close by.\",\"Drops one thing in order to reach for another.\"],\"columnSize\":1}],\"rateWithoutEvidence\":false},{\"id\":\"AA8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"tip\":\"Maintains attention, on own or with adult support, during brief activities\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"Example\",\"content\":[\"Briefly watches other children playing and then resumes play with a toy.\",\"Resumes playing at sand table when an adult joins in digging.\",\"Dumps toy animals from container, puts animals back in the container, and then dumps them out again.\"],\"columnSize\":1}],\"rateWithoutEvidence\":false},{\"id\":\"FC8CC7D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Maintains attention, with adult support, during activities that last for extended periods of time\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"Example\",\"content\":[\"Listens to a book from beginning to end and then gestures for an adult to read it a second time. \",\"Starts working on a simple puzzle with an adult and continues when the adult steps away briefly.\",\"Continues playing with toy cars, adding a bridge offered by an adult sitting nearby.\"],\"columnSize\":1}],\"rateWithoutEvidence\":false},{\"id\":\"FA8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Maintains attention on own during activities that last for extended periods of time\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"Example\",\"content\":[\"Makes a pile of pretend pancakes with play dough on own and then offers them to peers.\",\"Builds multiple towers with interlocking blocks.\",\"Looks through several books on own in library corner during the morning.\",\"Listens to audio books while looking at enlarged pictures related to the story on a screen, on own, during the morning.\"],\"columnSize\":1}],\"rateWithoutEvidence\":false},{\"id\":\"058D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[],\"rateWithoutEvidence\":false},{\"id\":\"048D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[],\"rateWithoutEvidence\":false},{\"id\":\"FE8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"tip\":\"Attends or responds briefly to people, things, or sounds.\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"Example\",\"content\":[\"Pays attention to a moving mobile.\",\"Quiets to the voice of a familiar person.\",\"Gazes at the smiling face of a familiar person.\"],\"columnSize\":1}],\"rateWithoutEvidence\":false}],\"levelMap\":{}}]";

        String psLevelJson = "[{\"id\":\"FD8C17D2-6DD9-EB11-9C19-4CCC6ACF6A29\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"tip\":\"Responds to sensory informationor input (e.g.,visual, auditory, tactile)with basic movementsof body parts\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"F98C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Responding  Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Responds to sensoryinformation by movingbody or limbs to reachfor or move toward\\npeople or objects\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"FA8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"FV8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\" Exploring Middle\",\"type\":\"radio\",\"sortIndex\":\"3\",\"value\":\"4\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"FC8CD7D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\" Exploring  Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"FD8C17D2-6DD9-EB1F-9C19-4CCC6ACF6129\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"FE8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Building Middle\",\"type\":\"radio\",\"sortIndex\":\"6\",\"value\":\"7\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"FF8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Building Later\",\"type\":\"radio\",\"sortIndex\":\"7\",\"value\":\"8\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"008D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Integrating Earlier\",\"type\":\"radio\",\"sortIndex\":\"8\",\"value\":\"9\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"038D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Conditional (not rated)\",\"type\":\"radio\",\"sortIndex\":\"9\",\"value\":\"b\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"048D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"UR (Unable to Rate)\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"058D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"12\",\"value\":\"e\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"068D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Discovering Language\",\"type\":\"radio\",\"sortIndex\":\"15\",\"value\":\"16\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"078D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Discovering English\",\"type\":\"radio\",\"sortIndex\":\"16\",\"value\":\"17\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"088D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Exploring English\",\"type\":\"radio\",\"sortIndex\":\"17\",\"value\":\"18\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"098D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Developing English\",\"type\":\"radio\",\"sortIndex\":\"18\",\"value\":\"19\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"0A8D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Building English\",\"type\":\"radio\",\"sortIndex\":\"19\",\"value\":\"20\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"0B8D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Integrating English\",\"type\":\"radio\",\"sortIndex\":\"20\",\"value\":\"21\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"0C8D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Not Yet\",\"type\":\"radio\",\"sortIndex\":\"21\",\"value\":\"n\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]}]";

        String psDomainJson = "[{\"domainId\":\"DDD0392C-55D3-EB11-9C19-4CCC6ACF6129\",\"measure\":\"LLD8\",\"measureName\":\"Phonological Awareness\",\"levels\":[{\"id\":\"FB8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\" Exploring Middle\",\"type\":\"radio\",\"sortIndex\":\"3\",\"value\":\"4\",\"tip\":\"Attends to sounds or elements of language\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"Example\",\"content\":[\"Turns toward adult when adult sings a song.\",\"Looks at adult’s hands when adult signs \\\"more.\\\"\",\"Attends to adult saying, \\\"bye-bye.\\\"\"],\"columnSize\":1}],\"rateWithoutEvidence\":false},{\"id\":\"FC8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\" Exploring  Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Demonstrates awareness of variations in sounds\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"Example\",\"content\":[\"Whispers a word, and then says it loudly.\",\"Communicates, \\\"No, no, no, no, no,\\\" varying pitch.\",\"Uses sounds or hand movements to play with variations of stress and rhythm.\"],\"columnSize\":1}],\"rateWithoutEvidence\":false},{\"id\":\"FD8C1A7D2-6DD9-EB11-9C1C-4CCC6ACF6129\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Engages actively in play with sounds in words or rhymes, or Sings simple songs, or Repeats simple nursery rhymes\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"Example\",\"content\":[\"Rhymes children’s names with other words during a group sing-along.\",\"Sings \\\"Twinkle, Twinkle, Little Star\\\" with a group.\",\"Communicates the rhyming word \\\"fall,\\\" after an adult says, \\\"Humpty Dumpty sat on a wall. Humpty Dumpty had a great . . .?\\\"\",\"Uses signs to participate in a song such as \\\"The Wheels on the Bus.\\\"\"],\"columnSize\":1}],\"rateWithoutEvidence\":false},{\"id\":\"FE8CG17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Building Middle\",\"type\":\"radio\",\"sortIndex\":\"6\",\"value\":\"7\",\"tip\":\"Demonstrates awareness of larger units of language (e.g., words, syllables)\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"Example\",\"content\":[\"Drums out each word in \\\"I am Matt\\\" in a name game in the classroom, after an adult has modeled drumming while saying single words.\",\"Claps the syllables in familiar words, such as children’s names or days of the week, with adult and peers.\",\"Moves arms each time the word \\\"row\\\" is said in the song \\\"Row, Row, Row Your Boat,\\\" with adult and peers.\"],\"columnSize\":1}],\"rateWithoutEvidence\":false},{\"id\":\"FF8C17D2-6DD9-EB1Y-9C19-4CCC6ACF6129\",\"name\":\"Building Later\",\"type\":\"radio\",\"sortIndex\":\"7\",\"value\":\"8\",\"tip\":\"Blends larger units of lan- guage (e.g., compound words and syllables) with or without the support of pictures or objects; and Segments larger units of language (e.g., compound words and syllables) with or without the support of pictures or objects\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"Example\",\"content\":[\"Answers, \\\"Goldfish,\\\" after an adult asks, \\\"If you put together the words ‘gold’ and ‘fish,’ what word does that make?\\\"\",\"Communicates, \\\"Rain,\\\" after an adult communicates, \\\"There are two words in ‘raincoat.’ What happens when we take away the word ‘coat’?\\\" while moving a picture of a coat away from a picture of rain.\",\"Communicates, \\\"Zebra,\\\" after an adult separates the word into syllables, and says, \\\"Ze–,\\\" and \\\"–bra,\\\" while looking at a wordless picture book about the zoo.\",\"Communicates, \\\"Marker,\\\" after an adult communicates, \\\"What happens when I put the two syllables ‘mark–’ and ‘–er’ together?\\\"\"],\"columnSize\":1}],\"rateWithoutEvidence\":false},{\"id\":\"0T8D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"UR (Unable to Rate)\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[],\"rateWithoutEvidence\":false},{\"id\":\"058D17DL-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"12\",\"value\":\"e\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[],\"rateWithoutEvidence\":false},{\"id\":\"008D17DP-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Integrating Earlier\",\"type\":\"radio\",\"sortIndex\":\"8\",\"value\":\"9\",\"tip\":\"Blends smaller units of language (e.g., onsets and rimes), with or without the support of pictures or objects; and Segments smaller units of language (e.g., onsets and rimes), with or without the support of pictures or objects\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"Example\",\"content\":[\"Communicates, \\\"Cup,\\\" at the snack table, after an adult says, \\\"I have a c–up. What do I have?\\\"\",\"Communicates, \\\"Ice,\\\" after an adult asks what word is left when the m– is removed from the word \\\"mice,\\\" while playing a word game.\",\"Communicates, \\\"d\\\" (letter sound), while looking at a picture of a dog, after an adult says, \\\"What is the first sound you hear when you say ‘dog’?\\\" while looking at pictures of dogs together.\"],\"columnSize\":1}],\"rateWithoutEvidence\":false},{\"id\":\"0C8D17DK-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Not Yet\",\"type\":\"radio\",\"sortIndex\":\"21\",\"value\":\"n\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[],\"rateWithoutEvidence\":false}],\"levelMap\":{}}]";
        ScoreTemplateEntity itScoreTemplate = new ScoreTemplateEntity();
        itScoreTemplate.setDomainLevelsJson(domainJson);
        itScoreTemplate.setLevelsJson(itLevelJson);
        itScoreTemplate.setPortfolioId("portfolioId001");
        ScoreTemplateEntity psScoreTemplate = new ScoreTemplateEntity();
        psScoreTemplate.setDomainLevelsJson(psDomainJson);
        psScoreTemplate.setLevelsJson(psLevelJson);
        psScoreTemplate.setPortfolioId("portfolioId002");
        RatingRecordModel ratingRecordModel = new RatingRecordModel();
        ratingRecordModel.setFrameworkId("********-BDCE-E411-AF66-02C72B94B99B");
        ratingRecordModel.setPeriodAlias("2023-2024 Time4");
        List<DomainLevelResult> scores = new ArrayList<>();
        DomainLevelResult domainLevelResult = new DomainLevelResult();
        domainLevelResult.setCore(false);
        domainLevelResult.setMeasure("ITC");
        domainLevelResult.setDomainId("4CD0392C-55D3-EB11-9C19-4CCC6ACF6129");
        domainLevelResult.setLevelName("Responding Later");
        domainLevelResult.setLevelId("F98CD7D2-6DD9-EB11-9C19-4CCC6ACF6129");
        domainLevelResult.setIep(false);
        scores.add(domainLevelResult);
        ratingRecordModel.setScores(scores);
        SnapshotResponse snapshotResponse = new SnapshotResponse();
        snapshotResponse.setId("snapshotId");
        snapshotResponse.setAgencyId(agencyId);
        snapshotResponse.setPeriodAlias("2023-2024 Time4");
        snapshotResponse.setFrameworkId("********-BDCE-E411-AF66-02C72B94B99B");
        snapshotResponse.setFrameworkName("frameworkName");
        snapshotResponse.setEnrollmentId("enrollmentId");
        snapshotResponse.setRatingRecord(ratingRecordModel);
        snapshotResponse.setAgeMonth(1);
        MetaDataEntity metaDataEntity = new MetaDataEntity();
        metaDataEntity.setMetaKey("SCHOOL_READINESS_MEASURE_VIEW_GUIDE");
        metaDataEntity.setUserId(userId);
        metaDataEntity.setMetaValue("true");
        MetaDataEntity defaultMetaDataEntity = new MetaDataEntity();
        defaultMetaDataEntity.setMetaKey("SCHOOL_READINESS_MEASURE_VIEW_DO_NOT_ASK_AGAIN");
        defaultMetaDataEntity.setUserId(userId);
        defaultMetaDataEntity.setMetaValue("true");
        List<ReportBenchmarkViewEntity> views = new ArrayList<>();
        ReportBenchmarkViewEntity view = new ReportBenchmarkViewEntity();
        view.setId("807AC7C0-C45D-4B70-A543-3ADBB1D79264");
        view.setAgencyId(agencyId);
        view.setCreateUserId(userId);
        view.setType("DRDP_REPORT_MEASURE");
        view.setId("807AC7C0-C45D-4B70-A543-3ADBB1D79264");
        view.setAgencyId(agencyId);
        view.setName("DRDP Report Measure");
        view.setDeleted(false);
        view.setParentId("parentId0001");
        view.setFrameworkId("********-BDCE-E411-AF66-02C72B94B99B");
        view.setSortIndex(1);
        view.setShowSettingDomain(true);
        views.add(view);
        List<BenchmarkViewModel> benchmarkViewModels = new ArrayList<>();
        BenchmarkViewModel benchmarkViewModel = new BenchmarkViewModel();
        benchmarkViewModel.setId("807AC7C0-C45D-4B70-A543-3ADBB1D79264");
        benchmarkViewModel.setName("DRDP Report Measure");
        benchmarkViewModel.setAgencyId(agencyId);
        benchmarkViewModel.setCreateUserId(userId);
        benchmarkViewModel.setFrameworkId("portfolioId002");
        List<BenchmarkViewModel> groups = new ArrayList<>();
        BenchmarkViewModel group = new BenchmarkViewModel();
        group.setId("groupId001");
        group.setAgencyId(agencyId);
        group.setCreateUserId(userId);
        group.setFrameworkId("********-BDCE-E411-AF66-02C72B94B99B");
        group.setName("GroupName");
        group.setParentId("parentId0001");
        group.setSortIndex(1);
        List<ReportBenchmarkSettingEntity> settings = new ArrayList<>();
        ReportBenchmarkSettingEntity setting = new ReportBenchmarkSettingEntity();
        setting.setId("807AC7C0-C45D-4B70-A543-3ADBB1D79264");
        setting.setViewId("807AC7C0-C45D-4B70-A543-3ADBB1D79264");
        setting.setAgencyId(agencyId);
        setting.setTargetPercentage(20);
        setting.setDomainName("Attention Maintenance");
        setting.setDomainKey("ATL-REG1");
        settings.add(setting);
        group.setSettings(settings);
        groups.add(group);
        group.setGroups(groups);
        benchmarkViewModel.setGroups(groups);
        benchmarkViewModels.add(benchmarkViewModel);
        List<ReportBenchmarkSettingEntity> benchmarkSettings = new ArrayList<>();
        ReportBenchmarkSettingEntity benchmarkSetting = new ReportBenchmarkSettingEntity();
        benchmarkSetting.setId("807AC7C0-C45D-4B70-A543-3ADBB1D79264");
        benchmarkSetting.setViewId("807AC7C0-C45D-4B70-A543-3ADBB1D79264");
        benchmarkSetting.setAgencyId(agencyId);
        benchmarkSetting.setDomainName("Attention Maintenance");
        benchmarkSetting.setDomainKey("ATL-REG1");
        benchmarkSetting.setTargetPercentage(49);
        benchmarkSettings.add(benchmarkSetting);

        // 模拟方法
        lenient().when(userProvider.getCurrentUserId()).thenReturn(userId);
        lenient().when(userProvider.checkUser(userId)).thenReturn(user);
        lenient().when(userProvider.getCurrentUser()).thenReturn(authUserDetails);
        lenient().when(userDao.getUserById(userId)).thenReturn(userModel);
        lenient().when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        lenient().when(agencyDao.getSnapshot(agencyId, readinessMeasureRequest.getAlias())).thenReturn(agencySnapshots);
        lenient().when(centerDao.getCenterAndGroupsByTeacherId(userId)).thenReturn(centerGroups);
        lenient().when(studentDao.getSnapshotsByIds(agencyId, agencySnapshot.getPeriodAlias(), utcNow.getTime(), snapshotIds)).thenReturn(studentSnapshots);
        lenient().when(agencyDao.isDRDPtech(agencyId)).thenReturn(false);
        lenient().when(analysisService.mapSnapshot(studentSnapshotEntity, true, agencySnapshotData, new HashMap<>(),  false,  "en-US",  new HashMap<>(),  false)).thenReturn(snapshotResponse);
        lenient().when(benchmarkViewDao.listByAgencyIdAndTypeAndLevel(agencyId, ReportViewTypeEnum.DRDP_REPORT_MEASURE, ReportViewLevelEnum.AGENCY.toString())).thenReturn(new ArrayList<>());
        lenient().when(benchmarkSettingDao.listByViewIdIn(groupIds)).thenReturn(new ArrayList<>());
        lenient().when(portfolioService.getScoreTemplate("it2015")).thenReturn(itScoreTemplate);
        lenient().when(portfolioService.getScoreTemplate("ps2015")).thenReturn(psScoreTemplate);
        lenient().when(usersMetaDataDao.getByUserIdAndMetaKey(userId, UserMetaKey.SCHOOL_READINESS_MEASURE_VIEW_GUIDE.toString())).thenReturn(metaDataEntity);
        lenient().when(usersMetaDataDao.getByUserIdAndMetaKey(userId, UserMetaKey.SCHOOL_READINESS_MEASURE_VIEW_DO_NOT_ASK_AGAIN.toString())).thenReturn(defaultMetaDataEntity);
        lenient().when(benchmarkViewDao.listByAgencyIdAndTypeAndLevel(agencyId, ReportViewTypeEnum.DRDP_REPORT_MEASURE, ReportViewLevelEnum.AGENCY.toString())).thenReturn(new ArrayList<>());
        lenient().when(benchmarkViewDao.listByAgencyIdAndCreateUserIdInAndTypeAndLevel(agencyId, Collections.singletonList(userId), ReportViewTypeEnum.DRDP_REPORT_MEASURE, ReportViewLevelEnum.USER.toString())).thenReturn(views);
        MockedStatic<BenchmarkViewModel> benchmarkViewModelMockedStatic = mockStatic(BenchmarkViewModel.class); // 创建一个 BenchmarkViewModel 的模拟静态方法
        benchmarkViewModelMockedStatic.when(() -> BenchmarkViewModel.tree(views)).thenReturn(benchmarkViewModels);
        MockedStatic<WKHelper> wkHelperMockedStatic = mockStatic(WKHelper.class); // 创建一个 WKHelper 的模拟静态方法
        lenient().when(benchmarkSettingDao.listByViewIdIn(Arrays.asList("groupId001"))).thenReturn(benchmarkSettings);
        wkHelperMockedStatic.when(() -> WKHelper.upload(any(), any(), any())).thenReturn("publicUrl"); // 当调用 WKHelper 的 upload 方法时，返回一个公共 URL
        ReflectionTestUtils.setField(reportService, "endPoint", "endPoint"); // 设置报告服务的 endPoint 字段
        ReflectionTestUtils.setField(reportService, "pdfEndpoint", "pdfEndpoint"); // 设置报告服务的 pdfEndpoint 字段
        when(userProvider.getCurrentAgencyId()).thenReturn(agencyId);
        when(userProvider.getAgencyByUser(user)).thenReturn(agencyModel);
        // 调用方法
        DownFileResponse fileResponse = reportService.generateSchoolReadinessMeasureReportPDF(readinessMeasureRequest);

        // 断言阶段
        verify(reportDao, times(1)).createPdfConvertJob(any(PdfConvertJobEntity.class)); // 验证 reportDao 的 createPdfConvertJob 方法被调用了一次
        verify(remoteProvider, times(1)).callPdfService(anyString(), any()); // 验证 remoteProvider 的 callPdfServer 方法被调用了一次
        wkHelperMockedStatic.close(); // 关闭 WKHelper 的模拟静态方法
    }

    /**
     * 测试生成 School Readiness Measure Report Excel
     * case: 单个测评点下载
     */
    @Test
    public void testDownloadMeasureReportExcel() {
        // 准备请求参数
        DownloadMeasureReportExcelRequest readinessMeasureRequest = new DownloadMeasureReportExcelRequest();
        List<BenchmarkResponse> children = new ArrayList<>();
        BenchmarkResponse child = new BenchmarkResponse();
        child.setStatus("Below");
        child.setBenchmarkName("benchmarkName");
        child.setBenchmark("benchmark");
        child.setMeasure("measure");
        child.setPeriodDisplayName("periodDisplayName");
        child.setCenterName("centerName");
        child.setGroupName("groupName");
        child.setDisplayName("displayName");
        child.setRatingScore("ratingScore");
        children.add(child);
        readinessMeasureRequest.setChildren(children);
        readinessMeasureRequest.setSingleDownload(true);

        // 调用方法
        DownFileResponse fileResponse = reportService.downloadMeasureReportExcel(readinessMeasureRequest);

        // 断言阶段
        Assert.assertEquals(fileResponse.getDataStr().endsWith(".xlsx"), true);
    }

    /**
     * 测试下载 School Readiness Measure 报告 Excel
     * case: 下载全部
     */
    @Test
    public void testDownloadMeasureReportExcel1() {
        // 模拟数据
        String requestJson = "{\n" +
                "    \"agencyId\": \"\",\n" +
                "    \"agencyIds\": [\n" +
                "        \"7396EC65-751D-4CE8-A191-33EBE3B97B86\"\n" +
                "    ],\n" +
                "    \"alias\": [\n" +
                "        \"2023-2024 Time4\"\n" +
                "    ],\n" +
                "    \"periodAlias\": \"2023-2024 Time4\",\n" +
                "    \"frameworkIds\": [\n" +
                "        \"********-BDCE-E411-AF66-02C72B94B99B\",\n" +
                "        \"E163164F-BDCE-E411-AF66-02C72B94B99B\"\n" +
                "    ],\n" +
                "    \"centerIds\": [\n" +
                "        \"sc001\"\n" +
                "    ],\n" +
                "    \"centerNames\": [\n" +
                "        \"Aagle Flight School Aagle Flight School Aagle Flight School\"\n" +
                "    ],\n" +
                "    \"groupId\": \"\",\n" +
                "    \"groupIds\": [\n" +
                "        \"sg001\",\n" +
                "        \"457C4E2B-4047-4D62-9CF1-D60708F499CC\"\n" +
                "    ],\n" +
                "    \"groupNames\": [\n" +
                "        \"ITC-Demo\",\n" +
                "        \"KF-TEST\"\n" +
                "    ],\n" +
                "    \"attrFilters\": [],\n" +
                "    \"birthFrom\": \"\",\n" +
                "    \"birthTo\": \"\",\n" +
                "    \"entryFrom\": \"\",\n" +
                "    \"entryTo\": \"\",\n" +
                "    \"merge\": true,\n" +
                "    \"viewIds\": [\n" +
                "        \"807AC7C0-C45D-4B70-A543-3ADBB1D79264\"\n" +
                "    ]\n" +
                "}";
        // 准备请求参数
        DownloadMeasureReportExcelRequest readinessMeasureRequest = JsonUtil.fromJson(requestJson, DownloadMeasureReportExcelRequest.class);
        List<BenchmarkResponse> children = new ArrayList<>();
        BenchmarkResponse child = new BenchmarkResponse();
        child.setStatus("Below");
        child.setBenchmarkName("benchmarkName");
        child.setBenchmark("benchmark");
        child.setMeasure("measure");
        child.setPeriodDisplayName("periodDisplayName");
        child.setCenterName("centerName");
        child.setGroupName("groupName");
        child.setDisplayName("displayName");
        child.setRatingScore("ratingScore");
        children.add(child);
        readinessMeasureRequest.setChildren(children);
        readinessMeasureRequest.setSingleDownload(false);
        // 用户 ID
        String userId = "userId001";
        String agencyId = "agencyId001";
        com.learninggenie.common.data.entity.UserEntity user = new com.learninggenie.common.data.entity.UserEntity();
        user.setUserName(userId);
        user.setRole("COLLABORATOR");
        AuthUserDetails authUserDetails = new AuthUserDetails();
        authUserDetails.setUsername(userId);
        authUserDetails.setAgencyId(agencyId);
        UserModel userModel = new UserModel();
        userModel.setId(userId);
        userModel.setRole("COLLABORATOR");
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        List<AgencySnapshotEntity> agencySnapshots = new ArrayList<>();
        AgencySnapshotEntity agencySnapshot = new AgencySnapshotEntity();
        Date utcNow = TimeUtil.getUtcNow();
        agencySnapshot.setId("agencySnapshotId");
        agencySnapshot.setAgencyId(agencyId);
        agencySnapshot.setPeriodAlias("Winter 2024");
        agencySnapshot.setUpdateAtUtc(utcNow);
        LGSnapshot.StudentRecordSnapshot studentSnapshot = LGSnapshot.StudentRecordSnapshot.newBuilder()
                .setRecordId("r001")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .build();
        LGSnapshot.ClassSnapshot classSnapshot =LGSnapshot.ClassSnapshot.newBuilder()
                .setId("sg001")
                .setName("sg001")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .addRecords(studentSnapshot)
                .build();
        LGSnapshot.CenterSnapshot centerSnapshot = LGSnapshot.CenterSnapshot.newBuilder()
                .setId("sc001")
                .setName("sc001")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .addClasses(classSnapshot)
                .build();
        LGSnapshot.Properties property = LGSnapshot.Properties.newBuilder()
                .setName("a001")
                .setValue("avalue001")
                .setType("atype001")
                .build();
        LGSnapshot.AgencySnapshot agencySnapshotData = LGSnapshot.AgencySnapshot.newBuilder()
                .setId("a000001")
                .setName("s000001")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .addCenters(centerSnapshot)
                .addProperties(property)
                .build();
        agencySnapshot.setData(agencySnapshotData.toByteArray());
        agencySnapshots.add(agencySnapshot);
        List<CenterModel> centerGroups = new ArrayList<>();
        CenterModel centerModel = new CenterModel();
        centerModel.setAgencyId(agencyId);
        centerModel.setGroupName("GroupName");
        centerModel.setGroupId("groupId001");
        centerGroups.add(centerModel);
        List<String> snapshotIds = new ArrayList<>();
        snapshotIds.add("r001");
        List<StudentSnapshotEntity> studentSnapshots = new ArrayList<>();
        StudentSnapshotEntity studentSnapshotEntity = new StudentSnapshotEntity();
        studentSnapshotEntity.setId("studentSnapshotId001");
        studentSnapshotEntity.setEnrollmentId("enrollmentId001");
        studentSnapshotEntity.setData(studentSnapshot.toByteArray());
        studentSnapshotEntity.setGroupId("groupId001");
        studentSnapshotEntity.setAgencyId(agencyId);
        studentSnapshots.add(studentSnapshotEntity);
        SnapshotResponse response = new SnapshotResponse();
        response.setId("childSnapshot001");
        response.setAgencyId(agencyId);
        StudentAttr attr = new StudentAttr();
        attr.setName(DrdpAttr.IEP.toString());
        attr.setValues(Arrays.asList(new String[]{"Yes"}));
        response.setAttrs(Arrays.asList(attr));
        List<String> groupIds = new ArrayList<>();
        groupIds.add("groupId001");
        groupIds.add("groupId002");
        String itLevelJson = "[{\"id\":\"F88C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"F98CD7D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"FA8C1ED2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"FB8C17DB-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Exploring  Middle\",\"type\":\"radio\",\"sortIndex\":\"3\",\"value\":\"4\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"FC8C17DQ-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"FD8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"FE8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Building Middle\",\"type\":\"text\",\"sortIndex\":\"6\",\"value\":\"7\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"FF8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Building Later\",\"type\":\"text\",\"sortIndex\":\"7\",\"value\":\"8\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"008D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Integrating  Earlier\",\"type\":\"text\",\"sortIndex\":\"8\",\"value\":\"9\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"058D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"048D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]}]";

        String domainJson = "[{\"domainId\":\"4CD0392C-55D3-EB11-9C19-4CCC6ACF6129\",\"measure\":\"ATL-REG1\",\"measureName\":\"Attention Maintenance\",\"levels\":[{\"id\":\"F98C1CD2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Responding Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Shifts attention frequently from one person or thing to another\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"Example\",\"content\":[\"Turns attention toward an interesting toy, then back to an adult or a child.\",\"Actively shifts interest from one child to another playing close by.\",\"Drops one thing in order to reach for another.\"],\"columnSize\":1}],\"rateWithoutEvidence\":false},{\"id\":\"AA8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"tip\":\"Maintains attention, on own or with adult support, during brief activities\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"Example\",\"content\":[\"Briefly watches other children playing and then resumes play with a toy.\",\"Resumes playing at sand table when an adult joins in digging.\",\"Dumps toy animals from container, puts animals back in the container, and then dumps them out again.\"],\"columnSize\":1}],\"rateWithoutEvidence\":false},{\"id\":\"FC8CC7D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Maintains attention, with adult support, during activities that last for extended periods of time\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"Example\",\"content\":[\"Listens to a book from beginning to end and then gestures for an adult to read it a second time. \",\"Starts working on a simple puzzle with an adult and continues when the adult steps away briefly.\",\"Continues playing with toy cars, adding a bridge offered by an adult sitting nearby.\"],\"columnSize\":1}],\"rateWithoutEvidence\":false},{\"id\":\"FA8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Maintains attention on own during activities that last for extended periods of time\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"Example\",\"content\":[\"Makes a pile of pretend pancakes with play dough on own and then offers them to peers.\",\"Builds multiple towers with interlocking blocks.\",\"Looks through several books on own in library corner during the morning.\",\"Listens to audio books while looking at enlarged pictures related to the story on a screen, on own, during the morning.\"],\"columnSize\":1}],\"rateWithoutEvidence\":false},{\"id\":\"058D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"9\",\"value\":\"e\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[],\"rateWithoutEvidence\":false},{\"id\":\"048D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"UR\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[],\"rateWithoutEvidence\":false},{\"id\":\"FE8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"tip\":\"Attends or responds briefly to people, things, or sounds.\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"Example\",\"content\":[\"Pays attention to a moving mobile.\",\"Quiets to the voice of a familiar person.\",\"Gazes at the smiling face of a familiar person.\"],\"columnSize\":1}],\"rateWithoutEvidence\":false}],\"levelMap\":{}}]";

        String psLevelJson = "[{\"id\":\"FD8C17D2-6DD9-EB11-9C19-4CCC6ACF6A29\",\"name\":\"Responding Earlier\",\"type\":\"radio\",\"sortIndex\":\"0\",\"value\":\"1\",\"tip\":\"Responds to sensory informationor input (e.g.,visual, auditory, tactile)with basic movementsof body parts\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"F98C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Responding  Later\",\"type\":\"radio\",\"sortIndex\":\"1\",\"value\":\"2\",\"tip\":\"Responds to sensoryinformation by movingbody or limbs to reachfor or move toward\\npeople or objects\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"FA8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Exploring Earlier\",\"type\":\"radio\",\"sortIndex\":\"2\",\"value\":\"3\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"FV8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\" Exploring Middle\",\"type\":\"radio\",\"sortIndex\":\"3\",\"value\":\"4\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"FC8CD7D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\" Exploring  Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"FD8C17D2-6DD9-EB1F-9C19-4CCC6ACF6129\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"FE8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Building Middle\",\"type\":\"radio\",\"sortIndex\":\"6\",\"value\":\"7\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"FF8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Building Later\",\"type\":\"radio\",\"sortIndex\":\"7\",\"value\":\"8\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"008D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Integrating Earlier\",\"type\":\"radio\",\"sortIndex\":\"8\",\"value\":\"9\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"038D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Conditional (not rated)\",\"type\":\"radio\",\"sortIndex\":\"9\",\"value\":\"b\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"048D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"UR (Unable to Rate)\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"058D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"12\",\"value\":\"e\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"068D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Discovering Language\",\"type\":\"radio\",\"sortIndex\":\"15\",\"value\":\"16\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"078D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Discovering English\",\"type\":\"radio\",\"sortIndex\":\"16\",\"value\":\"17\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"088D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Exploring English\",\"type\":\"radio\",\"sortIndex\":\"17\",\"value\":\"18\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"098D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Developing English\",\"type\":\"radio\",\"sortIndex\":\"18\",\"value\":\"19\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"0A8D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Building English\",\"type\":\"radio\",\"sortIndex\":\"19\",\"value\":\"20\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"0B8D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Integrating English\",\"type\":\"radio\",\"sortIndex\":\"20\",\"value\":\"21\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]},{\"id\":\"0C8D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Not Yet\",\"type\":\"radio\",\"sortIndex\":\"21\",\"value\":\"n\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[]}]";

        String psDomainJson = "[{\"domainId\":\"DDD0392C-55D3-EB11-9C19-4CCC6ACF6129\",\"measure\":\"LLD8\",\"measureName\":\"Phonological Awareness\",\"levels\":[{\"id\":\"FB8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\" Exploring Middle\",\"type\":\"radio\",\"sortIndex\":\"3\",\"value\":\"4\",\"tip\":\"Attends to sounds or elements of language\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"Example\",\"content\":[\"Turns toward adult when adult sings a song.\",\"Looks at adult’s hands when adult signs \\\"more.\\\"\",\"Attends to adult saying, \\\"bye-bye.\\\"\"],\"columnSize\":1}],\"rateWithoutEvidence\":false},{\"id\":\"FC8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\" Exploring  Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Demonstrates awareness of variations in sounds\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"Example\",\"content\":[\"Whispers a word, and then says it loudly.\",\"Communicates, \\\"No, no, no, no, no,\\\" varying pitch.\",\"Uses sounds or hand movements to play with variations of stress and rhythm.\"],\"columnSize\":1}],\"rateWithoutEvidence\":false},{\"id\":\"FD8C1A7D2-6DD9-EB11-9C1C-4CCC6ACF6129\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Engages actively in play with sounds in words or rhymes, or Sings simple songs, or Repeats simple nursery rhymes\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"Example\",\"content\":[\"Rhymes children’s names with other words during a group sing-along.\",\"Sings \\\"Twinkle, Twinkle, Little Star\\\" with a group.\",\"Communicates the rhyming word \\\"fall,\\\" after an adult says, \\\"Humpty Dumpty sat on a wall. Humpty Dumpty had a great . . .?\\\"\",\"Uses signs to participate in a song such as \\\"The Wheels on the Bus.\\\"\"],\"columnSize\":1}],\"rateWithoutEvidence\":false},{\"id\":\"FE8CG17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Building Middle\",\"type\":\"radio\",\"sortIndex\":\"6\",\"value\":\"7\",\"tip\":\"Demonstrates awareness of larger units of language (e.g., words, syllables)\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"Example\",\"content\":[\"Drums out each word in \\\"I am Matt\\\" in a name game in the classroom, after an adult has modeled drumming while saying single words.\",\"Claps the syllables in familiar words, such as children’s names or days of the week, with adult and peers.\",\"Moves arms each time the word \\\"row\\\" is said in the song \\\"Row, Row, Row Your Boat,\\\" with adult and peers.\"],\"columnSize\":1}],\"rateWithoutEvidence\":false},{\"id\":\"FF8C17D2-6DD9-EB1Y-9C19-4CCC6ACF6129\",\"name\":\"Building Later\",\"type\":\"radio\",\"sortIndex\":\"7\",\"value\":\"8\",\"tip\":\"Blends larger units of lan- guage (e.g., compound words and syllables) with or without the support of pictures or objects; and Segments larger units of language (e.g., compound words and syllables) with or without the support of pictures or objects\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"Example\",\"content\":[\"Answers, \\\"Goldfish,\\\" after an adult asks, \\\"If you put together the words ‘gold’ and ‘fish,’ what word does that make?\\\"\",\"Communicates, \\\"Rain,\\\" after an adult communicates, \\\"There are two words in ‘raincoat.’ What happens when we take away the word ‘coat’?\\\" while moving a picture of a coat away from a picture of rain.\",\"Communicates, \\\"Zebra,\\\" after an adult separates the word into syllables, and says, \\\"Ze–,\\\" and \\\"–bra,\\\" while looking at a wordless picture book about the zoo.\",\"Communicates, \\\"Marker,\\\" after an adult communicates, \\\"What happens when I put the two syllables ‘mark–’ and ‘–er’ together?\\\"\"],\"columnSize\":1}],\"rateWithoutEvidence\":false},{\"id\":\"0T8D17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"UR (Unable to Rate)\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[],\"rateWithoutEvidence\":false},{\"id\":\"058D17DL-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"12\",\"value\":\"e\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[],\"rateWithoutEvidence\":false},{\"id\":\"008D17DP-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Integrating Earlier\",\"type\":\"radio\",\"sortIndex\":\"8\",\"value\":\"9\",\"tip\":\"Blends smaller units of language (e.g., onsets and rimes), with or without the support of pictures or objects; and Segments smaller units of language (e.g., onsets and rimes), with or without the support of pictures or objects\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[{\"exampleName\":\"Example\",\"content\":[\"Communicates, \\\"Cup,\\\" at the snack table, after an adult says, \\\"I have a c–up. What do I have?\\\"\",\"Communicates, \\\"Ice,\\\" after an adult asks what word is left when the m– is removed from the word \\\"mice,\\\" while playing a word game.\",\"Communicates, \\\"d\\\" (letter sound), while looking at a picture of a dog, after an adult says, \\\"What is the first sound you hear when you say ‘dog’?\\\" while looking at pictures of dogs together.\"],\"columnSize\":1}],\"rateWithoutEvidence\":false},{\"id\":\"0C8D17DK-6DD9-EB11-9C19-4CCC6ACF6129\",\"name\":\"Not Yet\",\"type\":\"radio\",\"sortIndex\":\"21\",\"value\":\"n\",\"hidden\":false,\"rated\":false,\"scoreExamples\":[],\"rateWithoutEvidence\":false}],\"levelMap\":{}}]";
        ScoreTemplateEntity itScoreTemplate = new ScoreTemplateEntity();
        itScoreTemplate.setDomainLevelsJson(domainJson);
        itScoreTemplate.setLevelsJson(itLevelJson);
        itScoreTemplate.setPortfolioId("portfolioId001");
        ScoreTemplateEntity psScoreTemplate = new ScoreTemplateEntity();
        psScoreTemplate.setDomainLevelsJson(psDomainJson);
        psScoreTemplate.setLevelsJson(psLevelJson);
        psScoreTemplate.setPortfolioId("portfolioId002");
        RatingRecordModel ratingRecordModel = new RatingRecordModel();
        ratingRecordModel.setFrameworkId("********-BDCE-E411-AF66-02C72B94B99B");
        ratingRecordModel.setPeriodAlias("2023-2024 Time4");
        SnapshotResponse snapshotResponse = new SnapshotResponse();
        snapshotResponse.setId("snapshotId");
        snapshotResponse.setAgencyId(agencyId);
        snapshotResponse.setPeriodAlias("2023-2024 Time4");
        snapshotResponse.setFrameworkId("********-BDCE-E411-AF66-02C72B94B99B");
        snapshotResponse.setFrameworkName("frameworkName");
        snapshotResponse.setEnrollmentId("enrollmentId");
        snapshotResponse.setRatingRecord(ratingRecordModel);
        snapshotResponse.setAgeMonth(1);
        MetaDataEntity metaDataEntity = new MetaDataEntity();
        metaDataEntity.setMetaKey("SCHOOL_READINESS_MEASURE_VIEW_GUIDE");
        metaDataEntity.setUserId(userId);
        metaDataEntity.setMetaValue("true");
        MetaDataEntity defaultMetaDataEntity = new MetaDataEntity();
        defaultMetaDataEntity.setMetaKey("SCHOOL_READINESS_MEASURE_VIEW_DO_NOT_ASK_AGAIN");
        defaultMetaDataEntity.setUserId(userId);
        defaultMetaDataEntity.setMetaValue("true");
        List<UserModel> adminUsers = new ArrayList<>();
        UserModel adminUser = new UserModel();
        adminUser.setId("adminUserId001");
        adminUser.setRole("ADMIN");
        adminUsers.add(adminUser);
        List<ReportBenchmarkViewEntity> adminViews = new ArrayList<>();
        ReportBenchmarkViewEntity adminView = new ReportBenchmarkViewEntity();
        adminView.setId("807AC7C0-C45D-4B70-A543-3ADBB1D79264");
        adminView.setAgencyId(agencyId);
        adminView.setCreateUserId("adminUserId001");
        adminView.setType("DRDP_REPORT_MEASURE");
        adminViews.add(adminView);
        List<ReportBenchmarkViewEntity> views = new ArrayList<>();
        ReportBenchmarkViewEntity view = new ReportBenchmarkViewEntity();
        view.setId("807AC7C0-C45D-4B70-A543-3ADBB1D79264");
        view.setAgencyId(agencyId);
        view.setCreateUserId(userId);
        view.setType("DRDP_REPORT_MEASURE");
        view.setId("807AC7C0-C45D-4B70-A543-3ADBB1D79264");
        view.setAgencyId(agencyId);
        view.setName("DRDP Report Measure");
        view.setDeleted(false);
        view.setFrameworkId("********-BDCE-E411-AF66-02C72B94B99B");
        view.setSortIndex(1);
        view.setShowSettingDomain(true);
        views.add(view);

        // 模拟方法
        lenient().when(userProvider.getCurrentUserId()).thenReturn(userId);
        lenient().when(userProvider.checkUser(userId)).thenReturn(user);
        lenient().when(userProvider.getCurrentUser()).thenReturn(authUserDetails);
        lenient().when(userDao.getUserById(userId)).thenReturn(userModel);
        lenient().when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        lenient().when(agencyDao.getSnapshot(agencyId, readinessMeasureRequest.getAlias())).thenReturn(agencySnapshots);
        lenient().when(centerDao.getCenterAndGroupsByTeacherId(userId)).thenReturn(centerGroups);
        lenient().when(studentDao.getSnapshotsByIds(agencyId, agencySnapshot.getPeriodAlias(), utcNow.getTime(), snapshotIds)).thenReturn(studentSnapshots);
        lenient().when(agencyDao.isDRDPtech(agencyId)).thenReturn(false);
        lenient().when(analysisService.mapSnapshot(studentSnapshotEntity, true, agencySnapshotData, new HashMap<>(),  false,  "en-US",  new HashMap<>(),  false)).thenReturn(snapshotResponse);
        lenient().when(benchmarkViewDao.listByAgencyIdAndTypeAndLevel(agencyId, ReportViewTypeEnum.DRDP_REPORT_MEASURE, ReportViewLevelEnum.AGENCY.toString())).thenReturn(new ArrayList<>());
        lenient().when(benchmarkSettingDao.listByViewIdIn(groupIds)).thenReturn(new ArrayList<>());
        lenient().when(portfolioService.getScoreTemplate("it2015")).thenReturn(itScoreTemplate);
        lenient().when(portfolioService.getScoreTemplate("ps2015")).thenReturn(psScoreTemplate);
        lenient().when(usersMetaDataDao.getByUserIdAndMetaKey(userId, UserMetaKey.SCHOOL_READINESS_MEASURE_VIEW_GUIDE.toString())).thenReturn(metaDataEntity);
        lenient().when(usersMetaDataDao.getByUserIdAndMetaKey(userId, UserMetaKey.SCHOOL_READINESS_MEASURE_VIEW_DO_NOT_ASK_AGAIN.toString())).thenReturn(defaultMetaDataEntity);
        lenient().when(userDao.getAgencyAdminsByAgencyId(agencyId)).thenReturn(adminUsers);
        lenient().when(benchmarkViewDao.listByAgencyIdAndTypeAndLevel(agencyId, ReportViewTypeEnum.DRDP_REPORT_MEASURE, ReportViewLevelEnum.AGENCY.toString())).thenReturn(adminViews);
        lenient().when(benchmarkViewDao.listByAgencyIdAndCreateUserIdInAndTypeAndLevel(agencyId, Collections.singletonList(userId), ReportViewTypeEnum.DRDP_REPORT_MEASURE, ReportViewLevelEnum.USER.toString())).thenReturn(views);
        when(userProvider.getCurrentAgencyId()).thenReturn(agencyId);
        when(userProvider.getAgencyByUser(user)).thenReturn(agencyModel);

        // 调用方法
        DownFileResponse fileResponse = reportService.downloadMeasureReportExcel(readinessMeasureRequest);

        // 断言阶段
        Assert.assertEquals(fileResponse.getDataStr().endsWith(".xlsx"), true);
    }

    /**
     * 测评点：一个优势，一个劣势
     */
    @Test
    public void testGenerateChildActionPlanReport() {
        // 准备阶段
        String frameworkId = "32DF6B7B-A5A0-4B73-AF23-193175BC537C"; // 框架 ID
        GeneratorActionPlanRequest request = new GeneratorActionPlanRequest();
        request.setChildName("Leyla Barnes");
        List<MeasureLevelRelation> strengthMeasures = new ArrayList<>();
        strengthMeasures.add(new MeasureLevelRelation("ATL-REG1", "Responding Later"));
//        strengthMeasures.add(new MeasureLevelRelation("COG9", "Exploring Earlier"));
//        strengthMeasures.add(new MeasureLevelRelation("SED2", "Exploring Earlier"));
        List<MeasureLevelRelation> needsImprovementMeasures = new ArrayList<>();
        needsImprovementMeasures.add(new MeasureLevelRelation("ELD1", "Building English"));
//        needsImprovementMeasures.add(new MeasureLevelRelation("ELD2", "Exploring English"));
//        needsImprovementMeasures.add(new MeasureLevelRelation("ELD3", "Building English"));
        request.setNeedsImprovementMeasures(needsImprovementMeasures);
        request.setAgeGroup("2 years 6 months");
        request.setGender("Female");
        request.setFrameworkId(frameworkId);
        request.setReportVersion("Standard");
        request.setFirstRequest(true);

        // 调用方法
        SseEmitter sseEmitter = reportService.generateChildActionPlanReport(request);

        // 结果验证
        assertNull(sseEmitter); // 验证返回值为 null
        verify(frameworkProvider, times(0)).getFrameworkMeasuresTreeWithLevels(frameworkId); // 验证 frameworkProvider 的 getFrameworkMeasuresTreeWithLevels 方法没有被调用

        clearInvocations(); // 清除方法调用记录

        // 设置优势测评点
        request.setStrengthMeasures(strengthMeasures);

        // 数据准备
        // 创建模拟的 DomainEntity 列表
        List<DomainEntity> mockDomains = new ArrayList<>();
        // 添加一些模拟的 DomainEntity 对象到列表中
        DomainEntity domain1 = new DomainEntity();
        domain1.setId("4BD0392C-55D3-EB11-9C19-4CCC6ACF6129");
        domain1.setName("Approaches to Learning–Self-Regulation");
        domain1.setAbbreviation("ATL-REG");
        domain1.setParentId("********-BDCE-E411-AF66-02C72B94B99B");
        domain1.setNodes(new ArrayList<>());
        mockDomains.add(domain1);
        // 创建模拟的 promptEntity 对象
        PromptEntity promptEntity= new PromptEntity();
        // 设置 promptEntity 的属性
        promptEntity.setId("C402E83D-50EA-EE11-AC9E-065376D41387");
        promptEntity.setScene("GENERATE_CHILD_REPORT_V1");
        promptEntity.setActive(true);
        promptEntity.setModel("gpt-3.5-turbo");
        promptEntity.setTemperature(0.7);
        promptEntity.setPromptTemplate("Please create a conclusive and objective summary report for {{ child_name }}");

        // 模拟方法调用
        when(frameworkProvider.getFrameworkMeasuresTreeWithLevels(frameworkId)).thenReturn(mockDomains);
        when(promptDao.getActivePromptByScene(PromptScene.GENERATE_CHILD_REPORT_V7.toString())).thenReturn(promptEntity);

        // 调用方法
        sseEmitter = reportService.generateChildActionPlanReport(request);

        // 验证结果
        verify(promptDao, times(1)).getActivePromptByScene(eq(PromptScene.GENERATE_CHILD_REPORT_V7.toString())); // 验证 promptDao 的 getActivePromptByScene 方法被调用一次，并传递了正确的参数
        verify(frameworkProvider, times(1)).getFrameworkMeasuresTreeWithLevels(frameworkId);  // 验证 frameworkProvider 的 getFrameworkMeasuresTreeWithLevels 方法被调用一次
        assertNotNull(sseEmitter); // 验证返回值不为 null
    }
    /**
     * 测评点：3个优势，3个劣势
     */
    @Test
    @Ignore
    public void testGenerateChildActionPlanReport2() {
        // 准备阶段
        String frameworkId = "32DF6B7B-A5A0-4B73-AF23-193175BC537C"; // 框架 ID
        GeneratorActionPlanRequest request = new GeneratorActionPlanRequest();
        request.setChildName("Leyla Barnes");
        List<MeasureLevelRelation> strengthMeasures = new ArrayList<>();
        strengthMeasures.add(new MeasureLevelRelation("ATL-REG1", "Responding Later"));
        strengthMeasures.add(new MeasureLevelRelation("COG9", "Exploring Earlier"));
        strengthMeasures.add(new MeasureLevelRelation("SED2", "Exploring Earlier"));
        List<MeasureLevelRelation> needsImprovementMeasures = new ArrayList<>();
        needsImprovementMeasures.add(new MeasureLevelRelation("ELD1", "Building English"));
        needsImprovementMeasures.add(new MeasureLevelRelation("ELD2", "Exploring English"));
        needsImprovementMeasures.add(new MeasureLevelRelation("ELD3", "Building English"));
        request.setNeedsImprovementMeasures(needsImprovementMeasures);
        request.setAgeGroup("2 years 6 months");
        request.setGender("Male");
        request.setFrameworkId(frameworkId);
        request.setReportVersion("Simplified");
        request.setFirstRequest(false);
        // 调用方法
        SseEmitter sseEmitter = reportService.generateChildActionPlanReport(request);

        // 设置优势测评点
        request.setStrengthMeasures(strengthMeasures);

        // 数据准备
        // 创建模拟的 DomainEntity 列表
        List<DomainEntity> mockDomains = new ArrayList<>();
        // 添加一些模拟的 DomainEntity 对象到列表中
        DomainEntity domain1 = new DomainEntity();
        domain1.setId("4BD0392C-55D3-EB11-9C19-4CCC6ACF6129");
        domain1.setName("Approaches to Learning–Self-Regulation");
        domain1.setAbbreviation("ATL-REG");
        domain1.setParentId("********-BDCE-E411-AF66-02C72B94B99B");
        domain1.setNodes(new ArrayList<>());
        mockDomains.add(domain1);
        // 创建模拟的 promptEntity 对象
        PromptEntity promptEntity= new PromptEntity();
        // 设置 promptEntity 的属性
        promptEntity.setId("C402E83D-50EA-EE11-AC9E-065376D41387");
        promptEntity.setScene("GENERATE_CHILD_REPORT_V5");
        promptEntity.setActive(true);
        promptEntity.setModel("gpt-3.5-turbo");
        promptEntity.setTemperature(0.7);
        promptEntity.setPromptTemplate("Please create a conclusive and objective summary report for {{ child_name }}");

        // 模拟方法调用
        when(frameworkProvider.getFrameworkMeasuresTreeWithLevels(frameworkId)).thenReturn(mockDomains);
        when(promptDao.getActivePromptByScene(PromptScene.GENERATE_CHILD_REPORT_V5.toString())).thenReturn(promptEntity);

        // 调用方法
        sseEmitter = reportService.generateChildActionPlanReport(request);

        // 验证结果
        verify(promptDao, times(1)).getActivePromptByScene(eq(PromptScene.GENERATE_CHILD_REPORT_V5.toString())); // 验证 promptDao 的 getActivePromptByScene 方法被调用一次，并传递了正确的参数
        verify(frameworkProvider, times(1)).getFrameworkMeasuresTreeWithLevels(frameworkId);  // 验证 frameworkProvider 的 getFrameworkMeasuresTreeWithLevels 方法被调用一次
        assertNotNull(sseEmitter); // 验证返回值不为 null
    }
    /**
     * 测评点：2个优势，2个劣势
     */
    @Test
    @Ignore
    public void testGenerateChildActionPlanReport3() {
        // 准备阶段
        String frameworkId = "32DF6B7B-A5A0-4B73-AF23-193175BC537C"; // 框架 ID
        GeneratorActionPlanRequest request = new GeneratorActionPlanRequest();
        request.setChildName("Leyla Barnes");
        List<MeasureLevelRelation> strengthMeasures = new ArrayList<>();
        strengthMeasures.add(new MeasureLevelRelation("ATL-REG1", "Responding Later"));
        strengthMeasures.add(new MeasureLevelRelation("COG9", "Exploring Earlier"));
//        strengthMeasures.add(new MeasureLevelRelation("SED2", "Exploring Earlier"));
        List<MeasureLevelRelation> needsImprovementMeasures = new ArrayList<>();
        needsImprovementMeasures.add(new MeasureLevelRelation("ELD1", "Building English"));
        needsImprovementMeasures.add(new MeasureLevelRelation("ELD2", "Exploring English"));
//        needsImprovementMeasures.add(new MeasureLevelRelation("ELD3", "Building English"));
        request.setNeedsImprovementMeasures(needsImprovementMeasures);
        request.setAgeGroup("2 years 6 months");
        request.setGender("Male");
        request.setFrameworkId(frameworkId);
        request.setReportVersion("Simplified");
        request.setFirstRequest(false);
        // 调用方法
        SseEmitter sseEmitter = reportService.generateChildActionPlanReport(request);

        // 设置优势测评点
        request.setStrengthMeasures(strengthMeasures);

        // 数据准备
        // 创建模拟的 DomainEntity 列表
        List<DomainEntity> mockDomains = new ArrayList<>();
        // 添加一些模拟的 DomainEntity 对象到列表中
        DomainEntity domain1 = new DomainEntity();
        domain1.setId("4BD0392C-55D3-EB11-9C19-4CCC6ACF6129");
        domain1.setName("Approaches to Learning–Self-Regulation");
        domain1.setAbbreviation("ATL-REG");
        domain1.setParentId("********-BDCE-E411-AF66-02C72B94B99B");
        domain1.setNodes(new ArrayList<>());
        mockDomains.add(domain1);
        // 创建模拟的 promptEntity 对象
        PromptEntity promptEntity= new PromptEntity();
        // 设置 promptEntity 的属性
        promptEntity.setId("C402E83D-50EA-EE11-AC9E-065376D41387");
        promptEntity.setScene("GENERATE_CHILD_REPORT_V4");
        promptEntity.setActive(true);
        promptEntity.setModel("gpt-3.5-turbo");
        promptEntity.setTemperature(0.7);
        promptEntity.setPromptTemplate("Please create a conclusive and objective summary report for {{ child_name }}");

        // 模拟方法调用
        when(frameworkProvider.getFrameworkMeasuresTreeWithLevels(frameworkId)).thenReturn(mockDomains);
        when(promptDao.getActivePromptByScene(PromptScene.GENERATE_CHILD_REPORT_V4.toString())).thenReturn(promptEntity);

        // 调用方法
        sseEmitter = reportService.generateChildActionPlanReport(request);

        // 验证结果
        verify(promptDao, times(1)).getActivePromptByScene(eq(PromptScene.GENERATE_CHILD_REPORT_V4.toString())); // 验证 promptDao 的 getActivePromptByScene 方法被调用一次，并传递了正确的参数
        verify(frameworkProvider, times(1)).getFrameworkMeasuresTreeWithLevels(frameworkId);  // 验证 frameworkProvider 的 getFrameworkMeasuresTreeWithLevels 方法被调用一次
        assertNotNull(sseEmitter); // 验证返回值不为 null
    }
    /**
     * 测评点：1个优势，2个劣势
     */
    @Test
    @Ignore
    public void testGenerateChildActionPlanReport4() {
        // 准备阶段
        String frameworkId = "32DF6B7B-A5A0-4B73-AF23-193175BC537C"; // 框架 ID
        GeneratorActionPlanRequest request = new GeneratorActionPlanRequest();
        request.setChildName("Leyla Barnes");
        List<MeasureLevelRelation> strengthMeasures = new ArrayList<>();
        strengthMeasures.add(new MeasureLevelRelation("ATL-REG1", "Responding Later"));
//        strengthMeasures.add(new MeasureLevelRelation("COG9", "Exploring Earlier"));
//        strengthMeasures.add(new MeasureLevelRelation("SED2", "Exploring Earlier"));
        List<MeasureLevelRelation> needsImprovementMeasures = new ArrayList<>();
        needsImprovementMeasures.add(new MeasureLevelRelation("ELD1", "Building English"));
        needsImprovementMeasures.add(new MeasureLevelRelation("ELD2", "Exploring English"));
//        needsImprovementMeasures.add(new MeasureLevelRelation("ELD3", "Building English"));
        request.setNeedsImprovementMeasures(needsImprovementMeasures);
        request.setAgeGroup("2 years 6 months");
        request.setGender("Male");
        request.setFrameworkId(frameworkId);
        request.setReportVersion("Simplified");
        request.setFirstRequest(false);
        // 调用方法
        SseEmitter sseEmitter = reportService.generateChildActionPlanReport(request);

        // 设置优势测评点
        request.setStrengthMeasures(strengthMeasures);

        // 数据准备
        // 创建模拟的 DomainEntity 列表
        List<DomainEntity> mockDomains = new ArrayList<>();
        // 添加一些模拟的 DomainEntity 对象到列表中
        DomainEntity domain1 = new DomainEntity();
        domain1.setId("4BD0392C-55D3-EB11-9C19-4CCC6ACF6129");
        domain1.setName("Approaches to Learning–Self-Regulation");
        domain1.setAbbreviation("ATL-REG");
        domain1.setParentId("********-BDCE-E411-AF66-02C72B94B99B");
        domain1.setNodes(new ArrayList<>());
        mockDomains.add(domain1);
        // 创建模拟的 promptEntity 对象
        PromptEntity promptEntity= new PromptEntity();
        // 设置 promptEntity 的属性
        promptEntity.setId("C402E83D-50EA-EE11-AC9E-065376D41387");
        promptEntity.setScene("GENERATE_CHILD_REPORT_V2");
        promptEntity.setActive(true);
        promptEntity.setModel("gpt-3.5-turbo");
        promptEntity.setTemperature(0.7);
        promptEntity.setPromptTemplate("Please create a conclusive and objective summary report for {{ child_name }}");

        // 模拟方法调用
        when(frameworkProvider.getFrameworkMeasuresTreeWithLevels(frameworkId)).thenReturn(mockDomains);
        when(promptDao.getActivePromptByScene(PromptScene.GENERATE_CHILD_REPORT_V2.toString())).thenReturn(promptEntity);

        // 调用方法
        sseEmitter = reportService.generateChildActionPlanReport(request);

        // 验证结果
        verify(promptDao, times(1)).getActivePromptByScene(eq(PromptScene.GENERATE_CHILD_REPORT_V2.toString())); // 验证 promptDao 的 getActivePromptByScene 方法被调用一次，并传递了正确的参数
        verify(frameworkProvider, times(1)).getFrameworkMeasuresTreeWithLevels(frameworkId);  // 验证 frameworkProvider 的 getFrameworkMeasuresTreeWithLevels 方法被调用一次
        assertNotNull(sseEmitter); // 验证返回值不为 null
    }
    /**
     * 非二元性别 简化版 测评点：2个优势，1个劣势
     */
    @Test
    @Ignore
    public void testGenerateChildActionPlanReport5() {
        // 准备阶段
        String frameworkId = "32DF6B7B-A5A0-4B73-AF23-193175BC537C"; // 框架 ID
        GeneratorActionPlanRequest request = new GeneratorActionPlanRequest();
        request.setChildName("Leyla Barnes");
        List<MeasureLevelRelation> strengthMeasures = new ArrayList<>();
        strengthMeasures.add(new MeasureLevelRelation("ATL-REG1", "Responding Later"));
        strengthMeasures.add(new MeasureLevelRelation("COG9", "Exploring Earlier"));
//        strengthMeasures.add(new MeasureLevelRelation("SED2", "Exploring Earlier"));
        List<MeasureLevelRelation> needsImprovementMeasures = new ArrayList<>();
        needsImprovementMeasures.add(new MeasureLevelRelation("ELD1", "Building English"));
//        needsImprovementMeasures.add(new MeasureLevelRelation("ELD2", "Exploring English"));
//        needsImprovementMeasures.add(new MeasureLevelRelation("ELD3", "Building English"));
        request.setNeedsImprovementMeasures(needsImprovementMeasures);
        request.setAgeGroup("2 years 6 months");
        request.setGender("Nonbinary");
        request.setFrameworkId(frameworkId);
        request.setReportVersion("Simplified");
        request.setFirstRequest(false);
        // 调用方法
        SseEmitter sseEmitter = new SseEmitter();

        // 设置优势测评点
        request.setStrengthMeasures(strengthMeasures);

        // 数据准备
        // 创建模拟的 DomainEntity 列表
        List<DomainEntity> mockDomains = new ArrayList<>();
        // 添加一些模拟的 DomainEntity 对象到列表中
        DomainEntity domain1 = new DomainEntity();
        domain1.setId("4BD0392C-55D3-EB11-9C19-4CCC6ACF6129");
        domain1.setName("Approaches to Learning–Self-Regulation");
        domain1.setAbbreviation("ATL-REG");
        domain1.setParentId("********-BDCE-E411-AF66-02C72B94B99B");
        domain1.setNodes(new ArrayList<>());
        mockDomains.add(domain1);
        // 创建模拟的 promptEntity 对象
        PromptEntity promptEntity= new PromptEntity();
        // 设置 promptEntity 的属性
        promptEntity.setId("C402E83D-50EA-EE11-AC9E-065376D41387");
        promptEntity.setScene("GENERATE_CHILD_REPORT_V3");
        promptEntity.setActive(true);
        promptEntity.setModel("gpt-3.5-turbo");
        promptEntity.setTemperature(0.7);
        promptEntity.setPromptTemplate("Please create a conclusive and objective summary report for {{ child_name }}");

        // 模拟方法调用
        when(frameworkProvider.getFrameworkMeasuresTreeWithLevels(frameworkId)).thenReturn(mockDomains);
        when(promptDao.getActivePromptByScene(PromptScene.GENERATE_CHILD_REPORT_V6.toString())).thenReturn(promptEntity);

        // 调用方法
        sseEmitter = reportService.generateChildActionPlanReport(request);

        // 验证结果
        verify(promptDao, times(1)).getActivePromptByScene(eq(PromptScene.GENERATE_CHILD_REPORT_V6.toString())); // 验证 promptDao 的 getActivePromptByScene 方法被调用一次，并传递了正确的参数
        verify(frameworkProvider, times(1)).getFrameworkMeasuresTreeWithLevels(frameworkId);  // 验证 frameworkProvider 的 getFrameworkMeasuresTreeWithLevels 方法被调用一次
        assertNotNull(sseEmitter); // 验证返回值不为 null
    }
    /**
     * 非二元性别 标准版 测评点：3个优势，3个劣势
     */
    @Test
    @Ignore
    public void testGenerateChildActionPlanReport6() {
        // 准备阶段
        String frameworkId = "32DF6B7B-A5A0-4B73-AF23-193175BC537C"; // 框架 ID
        GeneratorActionPlanRequest request = new GeneratorActionPlanRequest();
        request.setChildName("Leyla Barnes");
        List<MeasureLevelRelation> strengthMeasures = new ArrayList<>();
        strengthMeasures.add(new MeasureLevelRelation("ATL-REG1", "Responding Later"));
        strengthMeasures.add(new MeasureLevelRelation("COG9", "Exploring Earlier"));
        strengthMeasures.add(new MeasureLevelRelation("SED2", "Exploring Earlier"));
        List<MeasureLevelRelation> needsImprovementMeasures = new ArrayList<>();
        needsImprovementMeasures.add(new MeasureLevelRelation("ELD1", "Building English"));
        needsImprovementMeasures.add(new MeasureLevelRelation("ELD2", "Exploring English"));
        needsImprovementMeasures.add(new MeasureLevelRelation("ELD3", "Building English"));
        request.setNeedsImprovementMeasures(needsImprovementMeasures);
        request.setAgeGroup("2 years 6 months");
        request.setGender("Nonbinary");
        request.setFrameworkId(frameworkId);
        request.setReportVersion("Standard");
        request.setFirstRequest(false);
        // 调用方法
        SseEmitter sseEmitter = reportService.generateChildActionPlanReport(request);

        // 设置优势测评点
        request.setStrengthMeasures(strengthMeasures);

        // 数据准备
        // 创建模拟的 DomainEntity 列表
        List<DomainEntity> mockDomains = new ArrayList<>();
        // 添加一些模拟的 DomainEntity 对象到列表中
        DomainEntity domain1 = new DomainEntity();
        domain1.setId("4BD0392C-55D3-EB11-9C19-4CCC6ACF6129");
        domain1.setName("Approaches to Learning–Self-Regulation");
        domain1.setAbbreviation("ATL-REG");
        domain1.setParentId("********-BDCE-E411-AF66-02C72B94B99B");
        domain1.setNodes(new ArrayList<>());
        mockDomains.add(domain1);
        // 创建模拟的 promptEntity 对象
        PromptEntity promptEntity= new PromptEntity();
        // 设置 promptEntity 的属性
        promptEntity.setId("C402E83D-50EA-EE11-AC9E-065376D41387");
        promptEntity.setScene("GENERATE_CHILD_REPORT_V5");
        promptEntity.setActive(true);
        promptEntity.setModel("gpt-3.5-turbo");
        promptEntity.setTemperature(0.7);
        promptEntity.setPromptTemplate("Please create a conclusive and objective summary report for {{ child_name }}");

        // 模拟方法调用
        when(frameworkProvider.getFrameworkMeasuresTreeWithLevels(frameworkId)).thenReturn(mockDomains);
        when(promptDao.getActivePromptByScene(PromptScene.GENERATE_CHILD_REPORT_V6.toString())).thenReturn(promptEntity);

        // 调用方法
        sseEmitter = reportService.generateChildActionPlanReport(request);

        // 验证结果
        verify(promptDao, times(1)).getActivePromptByScene(eq(PromptScene.GENERATE_CHILD_REPORT_V6.toString())); // 验证 promptDao 的 getActivePromptByScene 方法被调用一次，并传递了正确的参数
        verify(frameworkProvider, times(1)).getFrameworkMeasuresTreeWithLevels(frameworkId);  // 验证 frameworkProvider 的 getFrameworkMeasuresTreeWithLevels 方法被调用一次
        assertNotNull(sseEmitter); // 验证返回值不为 null
    }

    @Test
    public void testGetDRDPCohortReportSlidesImage_WhenAgencyIdIsEmpty_ShouldThrowException() {
        // Arrange
        String emptyAgencyId = "";
        // Act & Assert
        assertThrows(BusinessException.class, () -> {
            reportService.getDRDPCohortReportSlidesImage(emptyAgencyId);
        });
    }

    @Test
    public void testGetDRDPCohortReportSlidesImage_WhenPortfolioIsClosed_ShouldReturnEmptyList() throws InvalidProtocolBufferException {
        // Arrange
        String validAgencyId = "validAgencyId";
        when(userProvider.getAgencyOpenDefaultClose(validAgencyId, AgencyMetaKey.PORTFOLIO_OPEN.toString()))
                .thenReturn(false);

        // Act
        List<ReplaceRelation> result = reportService.getDRDPCohortReportSlidesImage(validAgencyId);

        // Assert
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testGetDRDPCohortReportSlidesImage_WhenPortfolioIsOpenAndDataIsAvailable_ShouldReturnSlidesImages() throws InvalidProtocolBufferException {
        // Arrange
        String validAgencyId = "validAgencyId";
        com.learninggenie.common.data.entity.UserEntity userEntity = new com.learninggenie.common.data.entity.UserEntity();
        String userId = "userId";
        String agencyId = "agencyId001";
        userEntity.setId(userId);
        userEntity.setEmail("Email");
        userEntity.setRole(UserRole.AGENCY_ADMIN.toString());
        userEntity.setFirstName("FirstName");
        userEntity.setLastName("LastName");

        // 周期快照数据 Map
        Map<String, List<SnapshotResponse>> allStudentSnapshots = new HashMap<>();
        List<SnapshotResponse> snapshotResponses = new ArrayList<>();
        SnapshotResponse snapshotResponse = new SnapshotResponse();
        snapshotResponse.setId("snapshotId");
        snapshotResponses.add(snapshotResponse);
        allStudentSnapshots.put("periodAlias", snapshotResponses);
        // 学生快照对象
        List<StudentSnapshotEntity> snapshotByGroup = new ArrayList<>();
        StudentSnapshotEntity studentSnapshotEntity = new StudentSnapshotEntity();
        studentSnapshotEntity.setFrameworkId("frameworkId");
        snapshotByGroup.add(studentSnapshotEntity);
        // 评分模板对象
        ScoreTemplateEntity scoreTemplate = new ScoreTemplateEntity();
        scoreTemplate.setPortfolioId("frameworkId");

        UserModel userModel = new UserModel();
        userModel.setId(userId);
        userModel.setRole(UserRole.AGENCY_ADMIN.toString());

        List<AgencySnapshotEntity> agencySnapshots = new ArrayList<>();
        AgencySnapshotEntity agencySnapshot = new AgencySnapshotEntity();
        Date utcNow = TimeUtil.getUtcNow();
        agencySnapshot.setId("agencySnapshotId");
        agencySnapshot.setAgencyId(agencyId);
        agencySnapshot.setPeriodAlias("Winter 2024");
        agencySnapshot.setUpdateAtUtc(utcNow);
        LGSnapshot.StudentRecordSnapshot studentSnapshot = LGSnapshot.StudentRecordSnapshot.newBuilder()
                .setRecordId("r001")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .build();
        LGSnapshot.ClassSnapshot classSnapshot = LGSnapshot.ClassSnapshot.newBuilder()
                .setId("sg001")
                .setName("sg001")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .addRecords(studentSnapshot)
                .build();
        LGSnapshot.CenterSnapshot centerSnapshot = LGSnapshot.CenterSnapshot.newBuilder()
                .setId("sc001")
                .setName("sc001")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .addClasses(classSnapshot)
                .build();
        LGSnapshot.Properties property = LGSnapshot.Properties.newBuilder()
                .setName("a001")
                .setValue("avalue001")
                .setType("atype001")
                .build();
        LGSnapshot.AgencySnapshot agencySnapshotData = LGSnapshot.AgencySnapshot.newBuilder()
                .setId("a000001")
                .setName("s000001")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .addCenters(centerSnapshot)
                .addProperties(property)
                .build();
        agencySnapshot.setData(agencySnapshotData.toByteArray());
        agencySnapshots.add(agencySnapshot);

        // 方法调用模拟
        Mockito.when(userProvider.getCurrentUserId()).thenReturn(userId); // 当前用户 ID
//        Mockito.when(studentDao.getSnapshotsByIds(anyString())).thenReturn(snapshotByGroup); // 获取学生快照
//        Mockito.when(portfolioDao.loadScoreTemplate("frameworkId")).thenReturn(scoreTemplate); // 获取评分模板
        when(userProvider.getAgencyOpenDefaultClose(validAgencyId, AgencyMetaKey.PORTFOLIO_OPEN.toString()))
                .thenReturn(true);

        // Simulate necessary data retrieval
        // Mock userService.getSnapshotAlias()
        when(userService.getSnapshotAlias(any(), any(), anyBoolean(), anyBoolean()))
                .thenReturn(JsonUtil.fromJsonArray("[{\"name\":\"2023-2024 Time4\",\"displayName\":\"2023-2024 Time4 (4th Assessment)\",\"agencyId\":null,\"frameworks\":[{\"frameworkId\":\"E163164F-BDCE-E411-AF66-02C72B94B99B\",\"frameworkName\":\"DRDP2015-INFANT-TODDLER Comprehensive View\",\"frameworkUrl\":null,\"drdp\":true},{\"frameworkId\":\"********-BDCE-E411-AF66-02C72B94B99B\",\"frameworkName\":\"DRDP2015-PRESCHOOL Comprehensive view\",\"frameworkUrl\":null,\"drdp\":true}],\"selected\":false,\"sortIndex\":66,\"import\":false},{\"name\":\"2023-2024 Time3\",\"displayName\":\"2023-2024 Time3 (3rd Assessment)\",\"agencyId\":null,\"frameworks\":[{\"frameworkId\":\"E163164F-BDCE-E411-AF66-02C72B94B99B\",\"frameworkName\":\"DRDP2015-INFANT-TODDLER Comprehensive View\",\"frameworkUrl\":null,\"drdp\":true}],\"selected\":false,\"sortIndex\":65,\"import\":false},{\"name\":\"2023-2024 Time2\",\"displayName\":\"2023-2024 Time2 (2nd Assessment)\",\"agencyId\":null,\"frameworks\":[{\"frameworkId\":\"********-BDCE-E411-AF66-02C72B94B99B\",\"frameworkName\":\"DRDP2015-PRESCHOOL Comprehensive view\",\"frameworkUrl\":null,\"drdp\":true},{\"frameworkId\":\"E163164F-BDCE-E411-AF66-02C72B94B99B\",\"frameworkName\":\"DRDP2015-INFANT-TODDLER Comprehensive View\",\"frameworkUrl\":null,\"drdp\":true}],\"selected\":false,\"sortIndex\":64,\"import\":false},{\"name\":\"2023-2024 Time1\",\"displayName\":\"2023-2024 Time1 (1st Assessment)\",\"agencyId\":null,\"frameworks\":[{\"frameworkId\":\"********-BDCE-E411-AF66-02C72B94B99B\",\"frameworkName\":\"DRDP2015-PRESCHOOL Comprehensive view\",\"frameworkUrl\":null,\"drdp\":true},{\"frameworkId\":\"3F7D9A2A-32EE-E411-AF66-02C72B94B99B\",\"frameworkName\":\"DRDP2011-SA (complete version)\",\"frameworkUrl\":null,\"drdp\":true}],\"selected\":false,\"sortIndex\":63,\"import\":false},{\"name\":\"2023-2024 Summer\",\"displayName\":\"Summer 2024\",\"agencyId\":null,\"frameworks\":[{\"frameworkId\":\"3F7D9A2A-32EE-E411-AF66-02C72B94B99B\",\"frameworkName\":\"DRDP2011-SA (complete version)\",\"frameworkUrl\":null,\"drdp\":true}],\"selected\":false,\"sortIndex\":62,\"import\":false},{\"name\":\"2023-2024 Spring\",\"displayName\":\"Spring 2024\",\"agencyId\":null,\"frameworks\":[{\"frameworkId\":\"32DF6B7B-A5A0-4B73-AF23-193175BC537C\",\"frameworkName\":\"DRDP2015-INFANT-TODDLER Essential View\",\"frameworkUrl\":null,\"drdp\":true},{\"frameworkId\":\"E83EB44A-BD11-4003-B32A-79B17065A408\",\"frameworkName\":\"IL CCSS-K Rubrics\",\"frameworkUrl\":null,\"drdp\":false},{\"frameworkId\":\"E163164F-BDCE-E411-AF66-02C72B94B99B\",\"frameworkName\":\"DRDP2015-INFANT-TODDLER Comprehensive View\",\"frameworkUrl\":null,\"drdp\":true}],\"selected\":false,\"sortIndex\":61,\"import\":false},{\"name\":\"2023-2024 Winter\",\"displayName\":\"Winter 2023\",\"agencyId\":null,\"frameworks\":[{\"frameworkId\":\"4912A224-32FA-4077-B43B-6788E171B558\",\"frameworkName\":\"Kindergarten Individual Development Survey (KIDS)\",\"frameworkUrl\":null,\"drdp\":false},{\"frameworkId\":\"3F7D9A2A-32EE-E411-AF66-02C72B94B99B\",\"frameworkName\":\"DRDP2011-SA (complete version)\",\"frameworkUrl\":null,\"drdp\":true}],\"selected\":false,\"sortIndex\":60,\"import\":false},{\"name\":\"2023-2024 Fall\",\"displayName\":\"Fall 2023\",\"agencyId\":null,\"frameworks\":[{\"frameworkId\":\"E163164F-BDCE-E411-AF66-02C72B94B99B\",\"frameworkName\":\"DRDP2015-INFANT-TODDLER Comprehensive View\",\"frameworkUrl\":null,\"drdp\":true},{\"frameworkId\":\"********-3B50-E411-837D-02DBFC8648CE\",\"frameworkName\":\"DRDP2010 PS\",\"frameworkUrl\":null,\"drdp\":false},{\"frameworkId\":\"A21BC800-2FF5-E411-AF66-02C72B94B99B\",\"frameworkName\":\"DRDP2015-Kindergarten\",\"frameworkUrl\":null,\"drdp\":true},{\"frameworkId\":\"********-BDCE-E411-AF66-02C72B94B99B\",\"frameworkName\":\"DRDP2015-PRESCHOOL Comprehensive view\",\"frameworkUrl\":null,\"drdp\":true},{\"frameworkId\":\"32DF6B7B-A5A0-4B73-AF23-193175BC537C\",\"frameworkName\":\"DRDP2015-INFANT-TODDLER Essential View\",\"frameworkUrl\":null,\"drdp\":true},{\"frameworkId\":\"3FCFD736-4D4E-431C-A0AC-FCE0C2622B3F\",\"frameworkName\":\"DRDP2015-PRESCHOOL Essential View\",\"frameworkUrl\":null,\"drdp\":true},{\"frameworkId\":\"E028CC1B-000D-461C-AC85-D86A38BF8B0F\",\"frameworkName\":\"DRDP2015-Kindergarten Essential View\",\"frameworkUrl\":null,\"drdp\":true},{\"frameworkId\":\"3F7D9A2A-32EE-E411-AF66-02C72B94B99B\",\"frameworkName\":\"DRDP2011-SA (complete version)\",\"frameworkUrl\":null,\"drdp\":true},{\"frameworkId\":\"832D146E-EB57-E511-B553-0608F0CADB57\",\"frameworkName\":\"DRDP2010-SA (simplified version)\",\"frameworkUrl\":null,\"drdp\":true},{\"frameworkId\":\"DA5D2340-6D56-41B9-A0B6-5F3962043E8E\",\"frameworkName\":\"DRDP2015-Kindergarten Fundamental View\",\"frameworkUrl\":null,\"drdp\":true},{\"frameworkId\":\"E83EB44A-BD11-4003-B32A-79B17065A408\",\"frameworkName\":\"IL CCSS-K Rubrics\",\"frameworkUrl\":null,\"drdp\":false},{\"frameworkId\":\"49DA6264-E437-E611-AB42-06BC895D03FD\",\"frameworkName\":\"DRDP2015-Preschool Fundamental View\",\"frameworkUrl\":null,\"drdp\":true},{\"frameworkId\":\"4912A224-32FA-4077-B43B-6788E171B558\",\"frameworkName\":\"Kindergarten Individual Development Survey (KIDS)\",\"frameworkUrl\":null,\"drdp\":false},{\"frameworkId\":\"1D77084F-70FD-4E24-8A75-DDFCA9F9339D\",\"frameworkName\":\"SETA's Fundamental View PLUS\",\"frameworkUrl\":null,\"drdp\":true}],\"selected\":false,\"sortIndex\":59,\"import\":false},{\"name\":\"2022-2023 Time4\",\"displayName\":\"2022-2023 Time4 (4th Assessment)\",\"agencyId\":null,\"frameworks\":[{\"frameworkId\":\"E163164F-BDCE-E411-AF66-02C72B94B99B\",\"frameworkName\":\"DRDP2015-INFANT-TODDLER Comprehensive View\",\"frameworkUrl\":null,\"drdp\":true}],\"selected\":false,\"sortIndex\":58,\"import\":false},{\"name\":\"2022-2023 Time3\",\"displayName\":\"2022-2023 Time3 (3rd Assessment)\",\"agencyId\":null,\"frameworks\":[{\"frameworkId\":\"E163164F-BDCE-E411-AF66-02C72B94B99B\",\"frameworkName\":\"DRDP2015-INFANT-TODDLER Comprehensive View\",\"frameworkUrl\":null,\"drdp\":true}],\"selected\":true,\"sortIndex\":57,\"import\":false},{\"name\":\"2022-2023 Time2\",\"displayName\":\"2022-2023 Time2 (2nd Assessment)\",\"agencyId\":null,\"frameworks\":[{\"frameworkId\":\"32DF6B7B-A5A0-4B73-AF23-193175BC537C\",\"frameworkName\":\"DRDP2015-INFANT-TODDLER Essential View\",\"frameworkUrl\":null,\"drdp\":true},{\"frameworkId\":\"3FCFD736-4D4E-431C-A0AC-FCE0C2622B3F\",\"frameworkName\":\"DRDP2015-PRESCHOOL Essential View\",\"frameworkUrl\":null,\"drdp\":true}],\"selected\":true,\"sortIndex\":56,\"import\":false},{\"name\":\"2022-2023 Time1\",\"displayName\":\"2022-2023 Time1 (1st Assessment)\",\"agencyId\":null,\"frameworks\":[{\"frameworkId\":\"32DF6B7B-A5A0-4B73-AF23-193175BC537C\",\"frameworkName\":\"DRDP2015-INFANT-TODDLER Essential View\",\"frameworkUrl\":null,\"drdp\":true},{\"frameworkId\":\"3FCFD736-4D4E-431C-A0AC-FCE0C2622B3F\",\"frameworkName\":\"DRDP2015-PRESCHOOL Essential View\",\"frameworkUrl\":null,\"drdp\":true},{\"frameworkId\":\"********-BDCE-E411-AF66-02C72B94B99B\",\"frameworkName\":\"DRDP2015-PRESCHOOL Comprehensive view\",\"frameworkUrl\":null,\"drdp\":true},{\"frameworkId\":\"E163164F-BDCE-E411-AF66-02C72B94B99B\",\"frameworkName\":\"DRDP2015-INFANT-TODDLER Comprehensive View\",\"frameworkUrl\":null,\"drdp\":true}],\"selected\":true,\"sortIndex\":55,\"import\":false},{\"name\":\"2022-2023 Spring\",\"displayName\":\"Spring 2023\",\"agencyId\":null,\"frameworks\":[{\"frameworkId\":\"E163164F-BDCE-E411-AF66-02C72B94B99B\",\"frameworkName\":\"DRDP2015-INFANT-TODDLER Comprehensive View\",\"frameworkUrl\":null,\"drdp\":true},{\"frameworkId\":\"32DF6B7B-A5A0-4B73-AF23-193175BC537C\",\"frameworkName\":\"DRDP2015-INFANT-TODDLER Essential View\",\"frameworkUrl\":null,\"drdp\":true},{\"frameworkId\":\"832D146E-EB57-E511-B553-0608F0CADB57\",\"frameworkName\":\"DRDP2010-SA (simplified version)\",\"frameworkUrl\":null,\"drdp\":true},{\"frameworkId\":\"********-BDCE-E411-AF66-02C72B94B99B\",\"frameworkName\":\"DRDP2015-PRESCHOOL Comprehensive view\",\"frameworkUrl\":null,\"drdp\":true},{\"frameworkId\":\"3FCFD736-4D4E-431C-A0AC-FCE0C2622B3F\",\"frameworkName\":\"DRDP2015-PRESCHOOL Essential View\",\"frameworkUrl\":null,\"drdp\":true}],\"selected\":true,\"sortIndex\":53,\"import\":false}]", new TypeToken<List<SnapshotAliasEntity>>() {
                }.getType()));
        // Mock userService.getSnapshotByAliasAndUser()
        AgencySnapshotResponse mockSnapshotResponse = JsonUtil.fromJson("{\"snapshots\":[{\"id\":\"7396EC65-751D-4CE8-A191-33EBE3B97B86\",\"name\":\"test Stage\",\"snapShotId\":\"C048A668-A383-EE11-AC1D-06533025D9B1\",\"centers\":[{\"id\":\"9BD8A9F1-213E-4513-9EDD-3AA5A2FC16D7\",\"name\":\"Aagle Flight School Aagle Flight School Aagle Flight School\",\"groups\":[{\"id\":\"006D8F19-77E9-4B70-BDB9-5C37552AE24C\",\"name\":\"ITC-Demo\",\"frameworkId\":\"E163164F-BDCE-E411-AF66-02C72B94B99B\",\"records\":[],\"children\":[],\"disabled\":false},{\"id\":\"457C4E2B-4047-4D62-9CF1-D60708F499CC\",\"name\":\"KF-TEST\",\"frameworkId\":\"********-BDCE-E411-AF66-02C72B94B99B\",\"records\":[],\"children\":[],\"disabled\":false},{\"id\":\"12FF4987-3BCA-4C91-A2F0-7E296A335509\",\"name\":\"666\",\"frameworkId\":\"3F7D9A2A-32EE-E411-AF66-02C72B94B99B\",\"records\":[],\"children\":[],\"disabled\":false}],\"disabled\":false}],\"attrs\":[{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Language\",\"displayName\":null,\"values\":[\"Spanish\",\"English\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"IEP/IFSP\",\"displayName\":null,\"values\":[\"Yes\",\"No\",\"Don't know\",\"Don't Know\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Hispanic\",\"displayName\":null,\"values\":[\"Yes\",\"No\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Race\",\"displayName\":null,\"values\":[\"American Indian or Alaska Native\",\"Chinese\",\"Other Asian\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Enrol reducedlunch\",\"displayName\":null,\"values\":[\"Yes\",\"No\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Enrol_subsidized\",\"displayName\":null,\"values\":[\"Yes\",\"No\",\"Don't know\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Adaptations\",\"displayName\":null,\"values\":[\"Assistive equipment or device\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Program Name\",\"displayName\":null,\"values\":[\"Child Care Center\",\"State Infant/Toddler Program full day\",\"Part Day state preschool program\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"ELD\",\"displayName\":null,\"values\":[\"Yes\",\"No\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Special education eligibility\",\"displayName\":null,\"values\":[\"Deaf-Blindness\",\"Autism\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"type1\",\"displayName\":null,\"values\":[\"TK,KK\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Teacher\",\"displayName\":null,\"values\":[\"Other\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Gender\",\"displayName\":null,\"values\":[\"Male\",\"Female\",\"Nonbinary\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Age Group\",\"displayName\":null,\"values\":[\"0 to 8 Months\",\"9 to 18 Months\",\"19 to 36 Months\",\"3 Years-Old\",\"4 Years-Old\",\"5 Years-Old\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]}],\"frameworks\":[{\"periodAlias\":\"2023-2024 Time4\",\"displayAlias\":\"2023-2024 Time4 (4th Assessment)\",\"from\":1693526400000,\"to\":1701302400000,\"framework\":\"DRDP2015-INFANT-TODDLER Comprehensive View\",\"frameworkId\":\"E163164F-BDCE-E411-AF66-02C72B94B99B\",\"scores\":[],\"hasDomainScore\":false,\"agencyId\":null,\"childId\":null},{\"periodAlias\":\"2023-2024 Time4\",\"displayAlias\":\"2023-2024 Time4 (4th Assessment)\",\"from\":1693526400000,\"to\":1701302400000,\"framework\":\"DRDP2015-PRESCHOOL Comprehensive view\",\"frameworkId\":\"********-BDCE-E411-AF66-02C72B94B99B\",\"scores\":[],\"hasDomainScore\":false,\"agencyId\":null,\"childId\":null}],\"allAttrs\":[{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Language\",\"displayName\":null,\"values\":[\"English\",\"Spanish\",\"Vietnamese\",\"Cantonese\",\"Korean\",\"Filipino (Pilipino or Tagalog)\",\"Portuguese\",\"Mandarin (Putonghua)\",\"Japanese\",\"Khmer (Cambodian)\",\"Lao\",\"Arabic\",\"Armenian\",\"Burmese\",\"Dutch\",\"Farsi\",\"French\",\"German\",\"Greek\",\"Chamorro (Guamanian)\",\"Hebrew\",\"Hindi\",\"Hmong\",\"Hungarian\",\"Ilocano\",\"Indonesian\",\"Italian\",\"Punjabi\",\"Russian\",\"Samoan\",\"Thai\",\"Turkish\",\"Tongan\",\"Urdu\",\"Cebuano (Visayan)\",\"Sign Language\",\"Ukranian\",\"Chaozhou (Chiuchow)\",\"Pashto\",\"Polish\",\"Assyrian\",\"Gujarati\",\"Mien (Yao)\",\"Rumanian\",\"Taiwanese\",\"Lahu\",\"Marhallese\",\"Mixteco\",\"Khmu\",\"Kurdish (Kurdi, Kurmanji)\",\"Serbo-Croatian (Bosnian, Croatian, Serbian)\",\"Toishanese\",\"Chaldean\",\"Albanian\",\"Tigrinya\",\"Somali\",\"Bengali\",\"Telugu\",\"Tamil\",\"Marathi\",\"Kannada\",\"Other non-English\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Hispanic\",\"displayName\":null,\"values\":[\"Yes\",\"No\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Adaptations\",\"displayName\":null,\"values\":[\"Augmentative or alternative communication system\",\"Alternative mode for written language\",\"Visual Support\",\"Assistive equipment or device\",\"Functional positioning\",\"Sensory Support\",\"Alternative response mode\",\"None\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Program Name\",\"displayName\":null,\"values\":[\"Part Day Kindergarten\",\"Part Day Transitional Kindergarten\",\"Full Day Kindergarten\",\"Full day Transitional Kindergarten\",\"Part Day state preschool program\",\"Child Care Center\",\"Tribal Head Start\",\"Full Day state preschool program\",\"Tribal Early Head Start\",\"Migrant\",\"First 5\",\"State Infant/Toddler Program full day\",\"Family Child Care Home\",\"Other\",\"Dual Language Immersion Program\",\"Bilingual Program\",\"State Infant/Toddler Program half day\",\"Part Day state preschool program\",\"Home visiting program\",\"Part Day Early Head Start\",\"Full Day Early Head Start\",\"Part Day Head Start Program\",\"Full Day Head Start program\",\"Title 1\",\"Homelessness\",\"Foster Youth\",\"Custom 1\",\"Custom 2\",\"Custom 3\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Special education eligibility\",\"displayName\":null,\"values\":[\"Autism\",\"Deaf-Blindness\",\"Deafness\",\"Emotional Disturbance\",\"Established Medical\",\"Disability\",\"Hard of Hearing\",\"Intellectual Disability\",\"Multiple Disability\",\"Orthopedic\",\"Other Health\",\"Impairment\",\"Specific Learning\",\"Disability\",\"Speech or Language Impairment\",\"Traumatic Brain Injury\",\"Visual Impairment\",\"Established Medical Disability\",\"Orthopedic Impairment\",\"Other Health Impairment\",\"Specific Learning Disability\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Bilingual\",\"displayName\":null,\"values\":[\"Yes\",\"No\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"IEP/IFSP\",\"displayName\":null,\"values\":[\"Yes\",\"No\",\"Don't know\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Race\",\"displayName\":null,\"values\":[\"American Indian or Alaska Native\",\"Asian Indian\",\"Black or African American\",\"Cambodian\",\"Chinese\",\"Filipino\",\"Guamanian\",\"Hawaiian\",\"Hmong\",\"Japanese\",\"Korean\",\"Laotian\",\"Other Asian\",\"Other Pacific Islander\",\"Samoan\",\"Tahitian\",\"Vietnamese\",\"White\",\"Intentionally Left Blank\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Enrol reducedlunch\",\"displayName\":null,\"values\":[\"Yes\",\"No\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Enrol_subsidized\",\"displayName\":null,\"values\":[\"Yes\",\"No\",\"Don't know\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"ELD\",\"displayName\":null,\"values\":[\"Yes\",\"No\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"type1\",\"displayName\":null,\"values\":[\"TK,KK\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Teacher\",\"displayName\":null,\"values\":[\"Other\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]}]},{\"id\":\"7396EC65-751D-4CE8-A191-33EBE3B97B86\",\"name\":\"test Stage\",\"snapShotId\":\"32CB5D3F-B8BA-EE11-AC6E-065376D41387\",\"centers\":[{\"id\":\"9BD8A9F1-213E-4513-9EDD-3AA5A2FC16D7\",\"name\":\"Aagle Flight School\",\"groups\":[{\"id\":\"12FF4987-3BCA-4C91-A2F0-7E296A335509\",\"name\":\"666\",\"frameworkId\":\"3F7D9A2A-32EE-E411-AF66-02C72B94B99B\",\"records\":[],\"children\":[],\"disabled\":false},{\"id\":\"457C4E2B-4047-4D62-9CF1-D60708F499CC\",\"name\":\"KF-TEST\",\"frameworkId\":\"********-BDCE-E411-AF66-02C72B94B99B\",\"records\":[],\"children\":[],\"disabled\":false}],\"disabled\":false}],\"attrs\":[{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Language\",\"displayName\":null,\"values\":[\"Hungarian\",\"English\",\"Spanish\",\"Tongan\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"IEP/IFSP\",\"displayName\":null,\"values\":[\"Yes\",\"No\",\"Don't know\",\"Don't Know\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Hispanic\",\"displayName\":null,\"values\":[\"Yes\",\"No\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Race\",\"displayName\":null,\"values\":[\"American Indian or Alaska Native\",\"Other Asian\",\"Asian Indian\",\"Black or African American\",\"Laotian\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Enrol reducedlunch\",\"displayName\":null,\"values\":[\"Yes\",\"No\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Enrol_subsidized\",\"displayName\":null,\"values\":[\"Yes\",\"No\",\"Don't know\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Adaptations\",\"displayName\":null,\"values\":[\"Alternative mode for written language\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"ELD\",\"displayName\":null,\"values\":[\"Yes\",\"No\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Program Name\",\"displayName\":null,\"values\":[\"Migrant\",\"Bilingual Program\",\"Other\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Special education eligibility\",\"displayName\":null,\"values\":[\"Deaf-Blindness\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"type1\",\"displayName\":null,\"values\":[\"TK,KK\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Teacher\",\"displayName\":null,\"values\":[\"Other\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Gender\",\"displayName\":null,\"values\":[\"Male\",\"Female\",\"Nonbinary\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Age Group\",\"displayName\":null,\"values\":[\"0 to 8 Months\",\"9 to 18 Months\",\"19 to 36 Months\",\"3 Years-Old\",\"4 Years-Old\",\"5 Years-Old\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]}],\"frameworks\":[{\"periodAlias\":\"2023-2024 Time1\",\"displayAlias\":\"2023-2024 Time1 (1st Assessment)\",\"from\":1683590400000,\"to\":1688688000000,\"framework\":\"DRDP2015-PRESCHOOL Comprehensive view\",\"frameworkId\":\"********-BDCE-E411-AF66-02C72B94B99B\",\"scores\":[],\"hasDomainScore\":false,\"agencyId\":null,\"childId\":null},{\"periodAlias\":\"2023-2024 Time1\",\"displayAlias\":\"2023-2024 Time1 (1st Assessment)\",\"from\":1709251200000,\"to\":1711584000000,\"framework\":\"DRDP2011-SA (complete version)\",\"frameworkId\":\"3F7D9A2A-32EE-E411-AF66-02C72B94B99B\",\"scores\":[],\"hasDomainScore\":false,\"agencyId\":null,\"childId\":null}],\"allAttrs\":[{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Language\",\"displayName\":null,\"values\":[\"English\",\"Spanish\",\"Vietnamese\",\"Cantonese\",\"Korean\",\"Filipino (Pilipino or Tagalog)\",\"Portuguese\",\"Mandarin (Putonghua)\",\"Japanese\",\"Khmer (Cambodian)\",\"Lao\",\"Arabic\",\"Armenian\",\"Burmese\",\"Dutch\",\"Farsi\",\"French\",\"German\",\"Greek\",\"Chamorro (Guamanian)\",\"Hebrew\",\"Hindi\",\"Hmong\",\"Hungarian\",\"Ilocano\",\"Indonesian\",\"Italian\",\"Punjabi\",\"Russian\",\"Samoan\",\"Thai\",\"Turkish\",\"Tongan\",\"Urdu\",\"Cebuano (Visayan)\",\"Sign Language\",\"Ukranian\",\"Chaozhou (Chiuchow)\",\"Pashto\",\"Polish\",\"Assyrian\",\"Gujarati\",\"Mien (Yao)\",\"Rumanian\",\"Taiwanese\",\"Lahu\",\"Marhallese\",\"Mixteco\",\"Khmu\",\"Kurdish (Kurdi, Kurmanji)\",\"Serbo-Croatian (Bosnian, Croatian, Serbian)\",\"Toishanese\",\"Chaldean\",\"Albanian\",\"Tigrinya\",\"Somali\",\"Bengali\",\"Telugu\",\"Tamil\",\"Marathi\",\"Kannada\",\"Other non-English\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Hispanic\",\"displayName\":null,\"values\":[\"Yes\",\"No\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Adaptations\",\"displayName\":null,\"values\":[\"Augmentative or alternative communication system\",\"Alternative mode for written language\",\"Visual Support\",\"Assistive equipment or device\",\"Functional positioning\",\"Sensory Support\",\"Alternative response mode\",\"None\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Program Name\",\"displayName\":null,\"values\":[\"Part Day Kindergarten\",\"Part Day Transitional Kindergarten\",\"Full Day Kindergarten\",\"Full day Transitional Kindergarten\",\"Part Day state preschool program\",\"Child Care Center\",\"Tribal Head Start\",\"Full Day state preschool program\",\"Tribal Early Head Start\",\"Migrant\",\"First 5\",\"State Infant/Toddler Program full day\",\"Family Child Care Home\",\"Other\",\"Dual Language Immersion Program\",\"Bilingual Program\",\"State Infant/Toddler Program half day\",\"Part Day state preschool program\",\"Home visiting program\",\"Part Day Early Head Start\",\"Full Day Early Head Start\",\"Part Day Head Start Program\",\"Full Day Head Start program\",\"Title 1\",\"Homelessness\",\"Foster Youth\",\"Custom 1\",\"Custom 2\",\"Custom 3\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Special education eligibility\",\"displayName\":null,\"values\":[\"Autism\",\"Deaf-Blindness\",\"Deafness\",\"Emotional Disturbance\",\"Established Medical\",\"Disability\",\"Hard of Hearing\",\"Intellectual Disability\",\"Multiple Disability\",\"Orthopedic\",\"Other Health\",\"Impairment\",\"Specific Learning\",\"Disability\",\"Speech or Language Impairment\",\"Traumatic Brain Injury\",\"Visual Impairment\",\"Established Medical Disability\",\"Orthopedic Impairment\",\"Other Health Impairment\",\"Specific Learning Disability\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Bilingual\",\"displayName\":null,\"values\":[\"Yes\",\"No\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"IEP/IFSP\",\"displayName\":null,\"values\":[\"Yes\",\"No\",\"Don't know\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Race\",\"displayName\":null,\"values\":[\"American Indian or Alaska Native\",\"Asian Indian\",\"Black or African American\",\"Cambodian\",\"Chinese\",\"Filipino\",\"Guamanian\",\"Hawaiian\",\"Hmong\",\"Japanese\",\"Korean\",\"Laotian\",\"Other Asian\",\"Other Pacific Islander\",\"Samoan\",\"Tahitian\",\"Vietnamese\",\"White\",\"Intentionally Left Blank\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Enrol reducedlunch\",\"displayName\":null,\"values\":[\"Yes\",\"No\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Enrol_subsidized\",\"displayName\":null,\"values\":[\"Yes\",\"No\",\"Don't know\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"ELD\",\"displayName\":null,\"values\":[\"Yes\",\"No\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"type1\",\"displayName\":null,\"values\":[\"TK,KK\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Teacher\",\"displayName\":null,\"values\":[\"Other\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]}]},{\"id\":\"7396EC65-751D-4CE8-A191-33EBE3B97B86\",\"name\":\"Test Stage\",\"snapShotId\":\"CFFF8D79-75C1-ED11-ABF4-0644AD81C721\",\"centers\":[{\"id\":\"7112394d-fcd6-41d8-b8be-47533e56f484\",\"name\":\"ARM11\",\"groups\":[{\"id\":\"30cd3fa7-0e53-4da8-b6e9-742852afe128\",\"name\":\"ITC class11\",\"frameworkId\":\"E163164F-BDCE-E411-AF66-02C72B94B99B\",\"records\":[],\"children\":[],\"disabled\":false}],\"disabled\":false},{\"id\":\"9BD8A9F1-213E-4513-9EDD-3AA5A2FC16D7\",\"name\":\"Aagle Flight School\",\"groups\":[{\"id\":\"006D8F19-77E9-4B70-BDB9-5C37552AE24C\",\"name\":\"ITC-Demo\",\"frameworkId\":\"E163164F-BDCE-E411-AF66-02C72B94B99B\",\"records\":[],\"children\":[],\"disabled\":false}],\"disabled\":false},{\"id\":\"144B73C2-5ABB-4932-B61F-76B2CA75DE2E\",\"name\":\"Assessment 02\",\"groups\":[{\"id\":\"697EEA1B-BABC-40AA-A06C-49C870128BF1\",\"name\":\"PS\",\"frameworkId\":\"********-BDCE-E411-AF66-02C72B94B99B\",\"records\":[],\"children\":[],\"disabled\":false}],\"disabled\":false}],\"attrs\":[{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Language\",\"displayName\":null,\"values\":[\"English\",\"Spanish\",\"Vietnamese\",\"Korean\",\"Cantonese\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"IEP/IFSP\",\"displayName\":null,\"values\":[\"Yes\",\"No\",\"Don't know\",\"Don't Know\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Hispanic\",\"displayName\":null,\"values\":[\"Yes\",\"No\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Race\",\"displayName\":null,\"values\":[\"American Indian or Alaska Native\",\"Guamanian\",\"Tahitian\",\"Black or African American\",\"Cambodian\",\"Korean\",\"Hmong\",\"Japanese\",\"Laotian\",\"Asian Indian\",\"Samoan\",\"Other Pacific Islander\",\"Filipino\",\"Vietnamese\",\"Chinese\",\"Hawaiian\",\"Intentionally Left Blank\",\"Other Asian\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Enrol reducedlunch\",\"displayName\":null,\"values\":[\"Yes\",\"No\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Enrol_subsidized\",\"displayName\":null,\"values\":[\"Yes\",\"No\",\"Don't know\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Program Name\",\"displayName\":null,\"values\":[\"Part Day state preschool program\",\"Migrant\",\"Child Care Center\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"ELD\",\"displayName\":null,\"values\":[\"Yes\",\"No\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"type1\",\"displayName\":null,\"values\":[\"TK,KK\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Teacher\",\"displayName\":null,\"values\":[\"Other\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Gender\",\"displayName\":null,\"values\":[\"Male\",\"Female\",\"Nonbinary\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Age Group\",\"displayName\":null,\"values\":[\"0 to 8 Months\",\"9 to 18 Months\",\"19 to 36 Months\",\"3 Years-Old\",\"4 Years-Old\",\"5 Years-Old\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]}],\"frameworks\":[{\"periodAlias\":\"2023-2024 Time2\",\"displayAlias\":\"2023-2024 Time2 (2nd Assessment)\",\"from\":1674691200000,\"to\":1685491200000,\"framework\":\"DRDP2015-PRESCHOOL Comprehensive view\",\"frameworkId\":\"********-BDCE-E411-AF66-02C72B94B99B\",\"scores\":[],\"hasDomainScore\":false,\"agencyId\":null,\"childId\":null},{\"periodAlias\":\"2023-2024 Time2\",\"displayAlias\":\"2023-2024 Time2 (2nd Assessment)\",\"from\":1685923200000,\"to\":1698710400000,\"framework\":\"DRDP2015-INFANT-TODDLER Comprehensive View\",\"frameworkId\":\"E163164F-BDCE-E411-AF66-02C72B94B99B\",\"scores\":[],\"hasDomainScore\":false,\"agencyId\":null,\"childId\":null}],\"allAttrs\":[{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Language\",\"displayName\":null,\"values\":[\"English\",\"Spanish\",\"Vietnamese\",\"Cantonese\",\"Korean\",\"Filipino (Pilipino or Tagalog)\",\"Portuguese\",\"Mandarin (Putonghua)\",\"Japanese\",\"Khmer (Cambodian)\",\"Lao\",\"Arabic\",\"Armenian\",\"Burmese\",\"Dutch\",\"Farsi\",\"French\",\"German\",\"Greek\",\"Chamorro (Guamanian)\",\"Hebrew\",\"Hindi\",\"Hmong\",\"Hungarian\",\"Ilocano\",\"Indonesian\",\"Italian\",\"Punjabi\",\"Russian\",\"Samoan\",\"Thai\",\"Turkish\",\"Tongan\",\"Urdu\",\"Cebuano (Visayan)\",\"Sign Language\",\"Ukranian\",\"Chaozhou (Chiuchow)\",\"Pashto\",\"Polish\",\"Assyrian\",\"Gujarati\",\"Mien (Yao)\",\"Rumanian\",\"Taiwanese\",\"Lahu\",\"Marhallese\",\"Mixteco\",\"Khmu\",\"Kurdish (Kurdi, Kurmanji)\",\"Serbo-Croatian (Bosnian, Croatian, Serbian)\",\"Toishanese\",\"Chaldean\",\"Albanian\",\"Tigrinya\",\"Somali\",\"Bengali\",\"Telugu\",\"Tamil\",\"Marathi\",\"Kannada\",\"Other non-English\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Hispanic\",\"displayName\":null,\"values\":[\"Yes\",\"No\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Adaptations\",\"displayName\":null,\"values\":[\"Augmentative or alternative communication system\",\"Alternative mode for written language\",\"Visual Support\",\"Assistive equipment or device\",\"Functional positioning\",\"Sensory Support\",\"Alternative response mode\",\"None\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Program Name\",\"displayName\":null,\"values\":[\"Part Day Kindergarten\",\"Part Day Transitional Kindergarten\",\"Full Day Kindergarten\",\"Full day Transitional Kindergarten\",\"Part Day state preschool program\",\"Child Care Center\",\"Tribal Head Start\",\"Full Day state preschool program\",\"Tribal Early Head Start\",\"Migrant\",\"First 5\",\"State Infant/Toddler Program full day\",\"Family Child Care Home\",\"Other\",\"Dual Language Immersion Program\",\"Bilingual Program\",\"State Infant/Toddler Program half day\",\"Part Day state preschool program\",\"Home visiting program\",\"Part Day Early Head Start\",\"Full Day Early Head Start\",\"Part Day Head Start Program\",\"Full Day Head Start program\",\"Title 1\",\"Homelessness\",\"Foster Youth\",\"Custom 1\",\"Custom 2\",\"Custom 3\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Special education eligibility\",\"displayName\":null,\"values\":[\"Autism\",\"Deaf-Blindness\",\"Deafness\",\"Emotional Disturbance\",\"Established Medical\",\"Disability\",\"Hard of Hearing\",\"Intellectual Disability\",\"Multiple Disability\",\"Orthopedic\",\"Other Health\",\"Impairment\",\"Specific Learning\",\"Disability\",\"Speech or Language Impairment\",\"Traumatic Brain Injury\",\"Visual Impairment\",\"Established Medical Disability\",\"Orthopedic Impairment\",\"Other Health Impairment\",\"Specific Learning Disability\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Bilingual\",\"displayName\":null,\"values\":[\"Yes\",\"No\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"IEP/IFSP\",\"displayName\":null,\"values\":[\"Yes\",\"No\",\"Don't know\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Race\",\"displayName\":null,\"values\":[\"American Indian or Alaska Native\",\"Asian Indian\",\"Black or African American\",\"Cambodian\",\"Chinese\",\"Filipino\",\"Guamanian\",\"Hawaiian\",\"Hmong\",\"Japanese\",\"Korean\",\"Laotian\",\"Other Asian\",\"Other Pacific Islander\",\"Samoan\",\"Tahitian\",\"Vietnamese\",\"White\",\"Intentionally Left Blank\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Enrol reducedlunch\",\"displayName\":null,\"values\":[\"Yes\",\"No\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Enrol_subsidized\",\"displayName\":null,\"values\":[\"Yes\",\"No\",\"Don't know\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"ELD\",\"displayName\":null,\"values\":[\"Yes\",\"No\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"type1\",\"displayName\":null,\"values\":[\"TK,KK\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Teacher\",\"displayName\":null,\"values\":[\"Other\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]}]},{\"id\":\"7396EC65-751D-4CE8-A191-33EBE3B97B86\",\"name\":\"test Stage\",\"snapShotId\":\"6EC176CB-CD08-EE11-ABF4-0644AD81C721\",\"centers\":[{\"id\":\"CE7DCFD3-1935-4680-AE33-ED360DA9E3F9\",\"name\":\"ARM\",\"groups\":[{\"id\":\"FD6CB23C-BF61-49A5-B184-192A7F4724D2\",\"name\":\"5.30 new class 1\",\"frameworkId\":\"E163164F-BDCE-E411-AF66-02C72B94B99B\",\"records\":[],\"children\":[],\"disabled\":false}],\"disabled\":false},{\"id\":\"9BD8A9F1-213E-4513-9EDD-3AA5A2FC16D7\",\"name\":\"Aagle Flight School\",\"groups\":[{\"id\":\"006D8F19-77E9-4B70-BDB9-5C37552AE24C\",\"name\":\"ITC-Demo\",\"frameworkId\":\"E163164F-BDCE-E411-AF66-02C72B94B99B\",\"records\":[],\"children\":[],\"disabled\":false}],\"disabled\":false},{\"id\":\"369E9B87-9827-4036-BA71-4DCD212BE2CE\",\"name\":\"Burbank Elementary School\",\"groups\":[{\"id\":\"1A449D93-275A-4D0F-8094-46EFC082B457\",\"name\":\"Haskins-71-TK/K Combo\",\"frameworkId\":\"E163164F-BDCE-E411-AF66-02C72B94B99B\",\"records\":[],\"children\":[],\"disabled\":false}],\"disabled\":false}],\"attrs\":[{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Language\",\"displayName\":null,\"values\":[\"Spanish\",\"English\",\"Other non-English\",\"Taiwanese\",\"Khmer (Cambodian)\",\"Filipino (Pilipino or Tagalog)\",\"Farsi\",\"Lahu\",\"Mien (Yao)\",\"Mandarin (Putonghua)\",\"Turkish\",\"Arabic\",\"Portuguese\",\"Cebuano (Visayan)\",\"Ukranian\",\"Marhallese\",\"Cantonese\",\"Polish\",\"Armenian\",\"Mixteco\",\"Urdu\",\"Tongan\",\"Assyrian\",\"Korean\",\"Japanese\",\"Pashto\",\"Burmese\",\"Chaozhou (Chiuchow)\",\"Gujarati\",\"Vietnamese\",\"Lao\",\"Rumanian\",\"Dutch\",\"Sign Language\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"IEP/IFSP\",\"displayName\":null,\"values\":[\"Yes\",\"No\",\"Don't know\",\"Don't Know\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Hispanic\",\"displayName\":null,\"values\":[\"Yes\",\"No\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Race\",\"displayName\":null,\"values\":[\"Guamanian\",\"Tahitian\",\"Black or African American\",\"Cambodian\",\"Korean\",\"Hmong\",\"Japanese\",\"Laotian\",\"Asian Indian\",\"Samoan\",\"Filipino\",\"Other Pacific Islander\",\"Vietnamese\",\"Hawaiian\",\"Chinese\",\"Intentionally Left Blank\",\"American Indian or Alaska Native\",\"Other Asian\",\"White\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Enrol reducedlunch\",\"displayName\":null,\"values\":[\"Yes\",\"No\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Enrol_subsidized\",\"displayName\":null,\"values\":[\"Yes\",\"No\",\"Don't know\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Adaptations\",\"displayName\":null,\"values\":[\"Sensory support\",\"Functional positioning\",\"Alternative response mode\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Program Name\",\"displayName\":null,\"values\":[\"Migrant\",\"Child Care Center\",\"Other\",\"Part Day Early Head Start\",\"Full Day Early Head Start\",\"Homelessness\",\"Home visiting program\",\"State Infant/Toddler Program full day\",\"Foster Youth\",\"First 5\",\"Custom 1\",\"Tribal Early Head Start\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"ELD\",\"displayName\":null,\"values\":[\"Yes\",\"No\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Teacher\",\"displayName\":null,\"values\":[\"Other\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Gender\",\"displayName\":null,\"values\":[\"Male\",\"Female\",\"Nonbinary\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Age Group\",\"displayName\":null,\"values\":[\"0 to 8 Months\",\"9 to 18 Months\",\"19 to 36 Months\",\"3 Years-Old\",\"4 Years-Old\",\"5 Years-Old\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]}],\"frameworks\":[{\"periodAlias\":\"2023-2024 Time3\",\"displayAlias\":\"2023-2024 Time3 (3rd Assessment)\",\"from\":1685577600000,\"to\":1693440000000,\"framework\":\"DRDP2015-INFANT-TODDLER Comprehensive View\",\"frameworkId\":\"E163164F-BDCE-E411-AF66-02C72B94B99B\",\"scores\":[],\"hasDomainScore\":false,\"agencyId\":null,\"childId\":null}],\"allAttrs\":[{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Language\",\"displayName\":null,\"values\":[\"English\",\"Spanish\",\"Vietnamese\",\"Cantonese\",\"Korean\",\"Filipino (Pilipino or Tagalog)\",\"Portuguese\",\"Mandarin (Putonghua)\",\"Japanese\",\"Khmer (Cambodian)\",\"Lao\",\"Arabic\",\"Armenian\",\"Burmese\",\"Dutch\",\"Farsi\",\"French\",\"German\",\"Greek\",\"Chamorro (Guamanian)\",\"Hebrew\",\"Hindi\",\"Hmong\",\"Hungarian\",\"Ilocano\",\"Indonesian\",\"Italian\",\"Punjabi\",\"Russian\",\"Samoan\",\"Thai\",\"Turkish\",\"Tongan\",\"Urdu\",\"Cebuano (Visayan)\",\"Sign Language\",\"Ukranian\",\"Chaozhou (Chiuchow)\",\"Pashto\",\"Polish\",\"Assyrian\",\"Gujarati\",\"Mien (Yao)\",\"Rumanian\",\"Taiwanese\",\"Lahu\",\"Marhallese\",\"Mixteco\",\"Khmu\",\"Kurdish (Kurdi, Kurmanji)\",\"Serbo-Croatian (Bosnian, Croatian, Serbian)\",\"Toishanese\",\"Chaldean\",\"Albanian\",\"Tigrinya\",\"Somali\",\"Bengali\",\"Telugu\",\"Tamil\",\"Marathi\",\"Kannada\",\"Other non-English\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Hispanic\",\"displayName\":null,\"values\":[\"Yes\",\"No\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Adaptations\",\"displayName\":null,\"values\":[\"Augmentative or alternative communication system\",\"Alternative mode for written language\",\"Visual Support\",\"Assistive equipment or device\",\"Functional positioning\",\"Sensory Support\",\"Alternative response mode\",\"None\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Program Name\",\"displayName\":null,\"values\":[\"Part Day Kindergarten\",\"Part Day Transitional Kindergarten\",\"Full Day Kindergarten\",\"Full day Transitional Kindergarten\",\"Part Day state preschool program\",\"Child Care Center\",\"Tribal Head Start\",\"Full Day state preschool program\",\"Tribal Early Head Start\",\"Migrant\",\"First 5\",\"State Infant/Toddler Program full day\",\"Family Child Care Home\",\"Other\",\"Dual Language Immersion Program\",\"Bilingual Program\",\"State Infant/Toddler Program half day\",\"Part Day state preschool program\",\"Home visiting program\",\"Part Day Early Head Start\",\"Full Day Early Head Start\",\"Part Day Head Start Program\",\"Full Day Head Start program\",\"Title 1\",\"Homelessness\",\"Foster Youth\",\"Custom 1\",\"Custom 2\",\"Custom 3\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Special education eligibility\",\"displayName\":null,\"values\":[\"Autism\",\"Deaf-Blindness\",\"Deafness\",\"Emotional Disturbance\",\"Established Medical\",\"Disability\",\"Hard of Hearing\",\"Intellectual Disability\",\"Multiple Disability\",\"Orthopedic\",\"Other Health\",\"Impairment\",\"Specific Learning\",\"Disability\",\"Speech or Language Impairment\",\"Traumatic Brain Injury\",\"Visual Impairment\",\"Established Medical Disability\",\"Orthopedic Impairment\",\"Other Health Impairment\",\"Specific Learning Disability\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Bilingual\",\"displayName\":null,\"values\":[\"Yes\",\"No\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"IEP/IFSP\",\"displayName\":null,\"values\":[\"Yes\",\"No\",\"Don't know\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Race\",\"displayName\":null,\"values\":[\"American Indian or Alaska Native\",\"Asian Indian\",\"Black or African American\",\"Cambodian\",\"Chinese\",\"Filipino\",\"Guamanian\",\"Hawaiian\",\"Hmong\",\"Japanese\",\"Korean\",\"Laotian\",\"Other Asian\",\"Other Pacific Islander\",\"Samoan\",\"Tahitian\",\"Vietnamese\",\"White\",\"Intentionally Left Blank\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Enrol reducedlunch\",\"displayName\":null,\"values\":[\"Yes\",\"No\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Enrol_subsidized\",\"displayName\":null,\"values\":[\"Yes\",\"No\",\"Don't know\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"ELD\",\"displayName\":null,\"values\":[\"Yes\",\"No\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Teacher\",\"displayName\":null,\"values\":[\"Other\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]}]}],\"snapshotsMergingData\":{\"id\":null,\"name\":null,\"snapShotId\":\"C048A668-A383-EE11-AC1D-06533025D9B132CB5D3F-B8BA-EE11-AC6E-065376D41387CFFF8D79-75C1-ED11-ABF4-0644AD81C7216EC176CB-CD08-EE11-ABF4-0644AD81C721\",\"centers\":[{\"id\":\"9BD8A9F1-213E-4513-9EDD-3AA5A2FC16D7\",\"name\":\"Aagle Flight School Aagle Flight School Aagle Flight School\",\"groups\":[{\"id\":\"006D8F19-77E9-4B70-BDB9-5C37552AE24C\",\"name\":\"ITC-Demo\",\"frameworkId\":\"E163164F-BDCE-E411-AF66-02C72B94B99B\",\"records\":[],\"children\":[],\"disabled\":false},{\"id\":\"457C4E2B-4047-4D62-9CF1-D60708F499CC\",\"name\":\"KF-TEST\",\"frameworkId\":\"********-BDCE-E411-AF66-02C72B94B99B\",\"records\":[],\"children\":[],\"disabled\":false},{\"id\":\"12FF4987-3BCA-4C91-A2F0-7E296A335509\",\"name\":\"666\",\"frameworkId\":\"3F7D9A2A-32EE-E411-AF66-02C72B94B99B\",\"records\":[],\"children\":[],\"disabled\":false}],\"disabled\":false},{\"id\":\"7112394d-fcd6-41d8-b8be-47533e56f484\",\"name\":\"ARM11\",\"groups\":[{\"id\":\"30cd3fa7-0e53-4da8-b6e9-742852afe128\",\"name\":\"ITC class11\",\"frameworkId\":\"E163164F-BDCE-E411-AF66-02C72B94B99B\",\"records\":[],\"children\":[],\"disabled\":false}],\"disabled\":false},{\"id\":\"144B73C2-5ABB-4932-B61F-76B2CA75DE2E\",\"name\":\"Assessment 02\",\"groups\":[{\"id\":\"697EEA1B-BABC-40AA-A06C-49C870128BF1\",\"name\":\"PS\",\"frameworkId\":\"********-BDCE-E411-AF66-02C72B94B99B\",\"records\":[],\"children\":[],\"disabled\":false}],\"disabled\":false},{\"id\":\"CE7DCFD3-1935-4680-AE33-ED360DA9E3F9\",\"name\":\"ARM\",\"groups\":[{\"id\":\"FD6CB23C-BF61-49A5-B184-192A7F4724D2\",\"name\":\"5.30 new class 1\",\"frameworkId\":\"E163164F-BDCE-E411-AF66-02C72B94B99B\",\"records\":[],\"children\":[],\"disabled\":false}],\"disabled\":false},{\"id\":\"369E9B87-9827-4036-BA71-4DCD212BE2CE\",\"name\":\"Burbank Elementary School\",\"groups\":[{\"id\":\"1A449D93-275A-4D0F-8094-46EFC082B457\",\"name\":\"Haskins-71-TK/K Combo\",\"frameworkId\":\"E163164F-BDCE-E411-AF66-02C72B94B99B\",\"records\":[],\"children\":[],\"disabled\":false}],\"disabled\":false}],\"attrs\":[{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Language\",\"displayName\":null,\"values\":[\"Spanish\",\"English\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"IEP/IFSP\",\"displayName\":null,\"values\":[\"Yes\",\"No\",\"Don't know\",\"Don't Know\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Hispanic\",\"displayName\":null,\"values\":[\"Yes\",\"No\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Race\",\"displayName\":null,\"values\":[\"American Indian or Alaska Native\",\"Chinese\",\"Other Asian\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Enrol reducedlunch\",\"displayName\":null,\"values\":[\"Yes\",\"No\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Enrol_subsidized\",\"displayName\":null,\"values\":[\"Yes\",\"No\",\"Don't know\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Adaptations\",\"displayName\":null,\"values\":[\"Assistive equipment or device\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Program Name\",\"displayName\":null,\"values\":[\"Child Care Center\",\"State Infant/Toddler Program full day\",\"Part Day state preschool program\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"ELD\",\"displayName\":null,\"values\":[\"Yes\",\"No\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Special education eligibility\",\"displayName\":null,\"values\":[\"Deaf-Blindness\",\"Autism\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"type1\",\"displayName\":null,\"values\":[\"TK,KK\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Teacher\",\"displayName\":null,\"values\":[\"Other\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Gender\",\"displayName\":null,\"values\":[\"Male\",\"Female\",\"Nonbinary\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Age Group\",\"displayName\":null,\"values\":[\"0 to 8 Months\",\"9 to 18 Months\",\"19 to 36 Months\",\"3 Years-Old\",\"4 Years-Old\",\"5 Years-Old\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]}],\"frameworks\":[{\"periodAlias\":\"2023-2024 Time4\",\"displayAlias\":\"2023-2024 Time4 (4th Assessment)\",\"from\":1693526400000,\"to\":1701302400000,\"framework\":\"DRDP2015-INFANT-TODDLER Comprehensive View\",\"frameworkId\":\"E163164F-BDCE-E411-AF66-02C72B94B99B\",\"scores\":[],\"hasDomainScore\":false,\"agencyId\":null,\"childId\":null},{\"periodAlias\":\"2023-2024 Time4\",\"displayAlias\":\"2023-2024 Time4 (4th Assessment)\",\"from\":1693526400000,\"to\":1701302400000,\"framework\":\"DRDP2015-PRESCHOOL Comprehensive view\",\"frameworkId\":\"********-BDCE-E411-AF66-02C72B94B99B\",\"scores\":[],\"hasDomainScore\":false,\"agencyId\":null,\"childId\":null},{\"periodAlias\":\"2023-2024 Time1\",\"displayAlias\":\"2023-2024 Time1 (1st Assessment)\",\"from\":1709251200000,\"to\":1711584000000,\"framework\":\"DRDP2011-SA (complete version)\",\"frameworkId\":\"3F7D9A2A-32EE-E411-AF66-02C72B94B99B\",\"scores\":[],\"hasDomainScore\":false,\"agencyId\":null,\"childId\":null}],\"allAttrs\":[{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Language\",\"displayName\":null,\"values\":[\"Spanish\",\"English\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"IEP/IFSP\",\"displayName\":null,\"values\":[\"Yes\",\"No\",\"Don't know\",\"Don't Know\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Hispanic\",\"displayName\":null,\"values\":[\"Yes\",\"No\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Race\",\"displayName\":null,\"values\":[\"American Indian or Alaska Native\",\"Chinese\",\"Other Asian\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Enrol reducedlunch\",\"displayName\":null,\"values\":[\"Yes\",\"No\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Enrol_subsidized\",\"displayName\":null,\"values\":[\"Yes\",\"No\",\"Don't know\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Adaptations\",\"displayName\":null,\"values\":[\"Assistive equipment or device\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Program Name\",\"displayName\":null,\"values\":[\"Child Care Center\",\"State Infant/Toddler Program full day\",\"Part Day state preschool program\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"ELD\",\"displayName\":null,\"values\":[\"Yes\",\"No\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Special education eligibility\",\"displayName\":null,\"values\":[\"Deaf-Blindness\",\"Autism\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"type1\",\"displayName\":null,\"values\":[\"TK,KK\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Teacher\",\"displayName\":null,\"values\":[\"Other\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Gender\",\"displayName\":null,\"values\":[\"Male\",\"Female\",\"Nonbinary\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]},{\"id\":null,\"no\":null,\"description\":null,\"typeId\":null,\"typeName\":null,\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"value\":null,\"order\":0,\"name\":\"Age Group\",\"displayName\":null,\"values\":[\"0 to 8 Months\",\"9 to 18 Months\",\"19 to 36 Months\",\"3 Years-Old\",\"4 Years-Old\",\"5 Years-Old\"],\"drdpName\":null,\"refer\":null,\"regex\":null,\"errMsg\":null,\"valueList\":[]}]}}", AgencySnapshotResponse.class);
        when(userService.getSnapshotByAliasAndUser(any(), any(), anyBoolean(), anyBoolean(), anyString()))
                .thenReturn(mockSnapshotResponse);
        // Mock frameworkDao.getById()
        FrameworkEntity mockFrameworkEntity = new FrameworkEntity();
        mockFrameworkEntity.setId("********-BDCE-E411-AF66-02C72B94B99B");
        mockFrameworkEntity.setName("DRDP2015-PRESCHOOL Comprehensive view");

        when(frameworkDao.getById(any())).thenReturn(mockFrameworkEntity);
        // Mock ratingService.isDRDP()
        when(ratingService.isDRDP(any())).thenReturn(true);
        when(userProvider.checkUser(anyString())).thenReturn(userEntity);
        lenient().when(userDao.getUserById(userId)).thenReturn(userModel);
        lenient().when(agencyDao.getSnapshot(anyString(), anyList())).thenReturn(agencySnapshots);

        // 模拟通过框架 Id 获取框架模板数据
        LevelTemplate level = new LevelTemplate();
        level.setClassName("className");
        level.setName("name");
        level.setWidth(new BigDecimal(1));
        DomainData domainData = new DomainData();
        domainData.setClassName("domainClassName");
        domainData.setName("domainName");
        domainData.setLevels(new ArrayList<>(Arrays.asList(level)));
        FrameworkData frameworkData = new FrameworkData();
        frameworkData.setDomainDatas(new ArrayList<>(Arrays.asList(domainData)));

        // 模拟获取模板
        when(domainScoreService.assembleCohortTemplate(anyString(), anyString())).thenReturn(frameworkData);
        List<ViewEntity> benchmarkViews = new ArrayList<>();
        ViewEntity viewEntity = new ViewEntity();
        viewEntity.setId("viewId");
        viewEntity.setName("viewName");
        viewEntity.setShowSettingDomain(true);
        benchmarkViews.add(viewEntity);
        when(reportDao.getBenchmarkViewByFrameworkIdAndUserId(anyString(), anyString())).thenReturn(benchmarkViews);

        // 模拟查询视图设置的基准数据
        List<BenchmarkSettingEntity> benchmarkSettings = new ArrayList<>();
        BenchmarkSettingEntity benchmarkSettingEntity = new BenchmarkSettingEntity();
        benchmarkSettingEntity.setViewId("viewId");
        benchmarkSettingEntity.setDomainKey("domainKey1");
        benchmarkSettings.add(benchmarkSettingEntity);
        BenchmarkSettingEntity benchmarkSettingEntity2 = new BenchmarkSettingEntity();
        benchmarkSettingEntity2.setViewId("viewId");
        benchmarkSettingEntity2.setDomainKey("domainClassName");
        benchmarkSettings.add(benchmarkSettingEntity2);
        when(reportDao.getBenchmarkSettingByViewIds(anyList())).thenReturn(benchmarkSettings);

        DomainEntity framework = new DomainEntity();
        framework.setName("frameworkName");
        when(domainDao.getDomain(anyString())).thenReturn(framework);

        // 模拟使用 wkhtmltoimage.exe 生成图片
        when(mediaService.convertHtmlToImage(anyString(), anyList())).thenReturn("cohortReadinessReportImageUrl");
        when(domainScoreService.checkAllSnapshot(anyList())).thenReturn(true);

        // Act
        List<ReplaceRelation> result = reportService.getDRDPCohortReportSlidesImage(validAgencyId);

        // Assert
        // Add assertions as per your requirements
        assertNotNull(result);
        assertEquals(4, result.size());
        assertEquals("IMAGE", result.get(0).getReplaceType().toString());
    }

    /**
     * 测试下载 Cohort 报告孩子 Excel 方法
     *
     * @throws IOException IO 异常
     */
    @Test
    public void testDownloadCohortReportByChildExcel() throws IOException {
        String userId = "83DF656D-D6A0-4515-BFBA-43C2CCAC817E";
        String agencyId = "7CD64CC8-26A7-4184-8D7E-E7A008415209";
        String frameworkId = "E163164F-BDCE-E411-AF66-02C72B94B99B";
        com.learninggenie.common.data.entity.UserEntity user = new com.learninggenie.common.data.entity.UserEntity();
        user.setUserName(userId);
        user.setRole(UserRole.AGENCY_OWNER.toString());
        AuthUserDetails authUserDetails = new AuthUserDetails();
        authUserDetails.setUsername(userId);
        authUserDetails.setAgencyId(agencyId);
        UserModel userModel = new UserModel();
        userModel.setId(userId);
        userModel.setRole(UserRole.AGENCY_OWNER.toString());
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);

        List<String> snapshotIds = new ArrayList<>();
        snapshotIds.add("DE27723E-8AD0-4071-9370-894B0512F7B6");
        List<AgencySnapshotEntity> agencySnapshots = new ArrayList<>();
        AgencySnapshotEntity agencySnapshot = new AgencySnapshotEntity();
        Date utcNow = TimeUtil.getUtcNow();
        agencySnapshot.setId("43BFBA61-1643-EF11-ACAD-02037858CE37");
        agencySnapshot.setAgencyId(agencyId);
        agencySnapshot.setPeriodAlias("2024-2025 Fall");
        agencySnapshot.setUpdateAtUtc(utcNow);
        LGSnapshot.StudentRecordSnapshot studentSnapshot = LGSnapshot.StudentRecordSnapshot.newBuilder()
                .setRecordId("DE27723E-8AD0-4071-9370-894B0512F7B6")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .build();
        LGSnapshot.ClassSnapshot classSnapshot = LGSnapshot.ClassSnapshot.newBuilder()
                .setId("1B4088B6-624C-4872-98E2-B62D1CA6D6C9")
                .setName("sg001")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .addRecords(studentSnapshot)
                .build();
        LGSnapshot.CenterSnapshot centerSnapshot = LGSnapshot.CenterSnapshot.newBuilder()
                .setId("sc001")
                .setName("sc001")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .addClasses(classSnapshot)
                .build();
        LGSnapshot.AgencySnapshot agencySnapshotData = LGSnapshot.AgencySnapshot.newBuilder()
                .setId("7CD64CC8-26A7-4184-8D7E-E7A008415209")
                .setName("门头沟小学")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .addCenters(centerSnapshot)
                .build();
        agencySnapshot.setData(agencySnapshotData.toByteArray());
        agencySnapshots.add(agencySnapshot);

        // 准备请求参数
        DownloadCohortReportExcelRequest request = JsonUtil.fromJson("{\"agencyId\":\"\",\"attrFilters\":[{\"name\":\"Gender\",\"displayName\":\"Gender\",\"values\":[\"Male\",\"Female\",\"Nonbinary\"]}],\"birthFrom\":\"\",\"birthTo\":\"\",\"entryFrom\":\"\",\"entryTo\":\"\",\"groupId\":\"\",\"agencyIds\":[\"7CD64CC8-26A7-4184-8D7E-E7A008415209\"],\"centerIds\":[\"6BE27D40-38ED-434F-A275-CA3F7EEE6957\"],\"groupIds\":[\"2D1D95B5-7CBF-44A9-A70A-A4DC77B1CDDD\"],\"periodAlias\":\"2024-2025 Fall\",\"portfolioId\":\"E163164F-BDCE-E411-AF66-02C72B94B99B\",\"portfolioName\":\"DRDP2015-INFANT-TODDLER Comprehensive View\",\"merge\":true,\"centerNames\":[\"门头沟小学\"],\"groupNames\":[\"TestClass\"],\"alias\":[\"2024-2025 Fall\"],\"frameworkId\":\"E163164F-BDCE-E411-AF66-02C72B94B99B\",\"showPieChart\":true,\"viewId\":\"048D4354-AD7F-48BE-B2AD-E2EB5117889A\"}", DownloadCohortReportExcelRequest.class);

        List<StudentSnapshotEntity> studentSnapshots1 = new ArrayList<>();
        StudentSnapshotEntity studentSnapshotEntity1 = new StudentSnapshotEntity();
        studentSnapshotEntity1.setId("3147FCE0-28B8-4310-A3AA-081D012B982B");
        studentSnapshotEntity1.setEnrollmentId("DE27723E-8AD0-4071-9370-894B0512F7B6");
        studentSnapshotEntity1.setGroupId("2D1D95B5-7CBF-44A9-A70A-A4DC77B1CDDD");
        studentSnapshotEntity1.setFrameworkId(frameworkId);
        studentSnapshotEntity1.setAgencyId(agencyId);
        studentSnapshots1.add(studentSnapshotEntity1);

        when(userProvider.getCurrentUserId()).thenReturn(userId);

        when(userProvider.checkUser(userId)).thenReturn(user);
        when(userDao.getUserById(userId)).thenReturn(userModel);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        when(agencyDao.getSnapshot(agencyId, request.getAlias())).thenReturn(agencySnapshots);
        when(studentDao.getSnapshotsByIds(anyString(), anyString(), anyLong(), anyList())).thenReturn(studentSnapshots1);

        when(agencyDao.isDRDPtech(agencyId)).thenReturn(false);

        String childSnapshotJson = "{\"active\":true,\"age\":1,\"ageMonth\":17,\"agencyId\":\"7CD64CC8-26A7-4184-8D7E-E7A008415209\",\"agencyName\":\"门头沟小学\",\"attrGroups\":[],\"attrs\":[{\"name\":\"Teacher\",\"order\":0,\"required\":false,\"typeValue\":\"SINGLE_CHOICE\",\"valueList\":[],\"values\":[]},{\"name\":\"Statewide Student Identifier\",\"order\":0,\"required\":false,\"typeValue\":\"TEXT_FIELD\",\"valueList\":[],\"values\":[]},{\"name\":\"Special education eligibility\",\"order\":0,\"required\":false,\"typeValue\":\"MULTIPLE_CHOICES\",\"valueList\":[],\"values\":[]},{\"name\":\"Race\",\"order\":0,\"required\":false,\"typeValue\":\"MULTIPLE_CHOICES\",\"valueList\":[],\"values\":[]},{\"name\":\"Program Name\",\"order\":0,\"required\":false,\"typeValue\":\"MULTIPLE_CHOICES\",\"valueList\":[],\"values\":[]},{\"name\":\"Language\",\"order\":0,\"required\":false,\"typeValue\":\"MULTIPLE_CHOICES\",\"valueList\":[],\"values\":[]},{\"name\":\"IEP/IFSP\",\"order\":0,\"required\":false,\"typeValue\":\"SINGLE_CHOICE\",\"valueList\":[],\"values\":[]},{\"name\":\"IEP Goal\",\"order\":0,\"required\":false,\"typeValue\":\"TEXT_FIELD\",\"valueList\":[],\"values\":[]},{\"name\":\"Hispanic\",\"order\":0,\"required\":false,\"typeValue\":\"SINGLE_CHOICE\",\"valueList\":[],\"values\":[]},{\"name\":\"External ID\",\"order\":0,\"required\":false,\"typeValue\":\"TEXT_FIELD\",\"valueList\":[],\"values\":[]},{\"name\":\"Enrol_subsidized\",\"order\":0,\"required\":false,\"typeValue\":\"SINGLE_CHOICE\",\"valueList\":[],\"values\":[]},{\"name\":\"Enrol reducedlunch\",\"order\":0,\"required\":false,\"typeValue\":\"SINGLE_CHOICE\",\"valueList\":[],\"values\":[]},{\"name\":\"ELD\",\"order\":0,\"required\":false,\"typeValue\":\"SINGLE_CHOICE\",\"valueList\":[],\"values\":[]},{\"name\":\"Comments\",\"order\":0,\"required\":false,\"typeValue\":\"TEXT_FIELD\",\"valueList\":[],\"values\":[]},{\"name\":\"Bilingual\",\"order\":0,\"required\":false,\"typeValue\":\"SINGLE_CHOICE\",\"valueList\":[],\"values\":[]},{\"name\":\"Adaptations\",\"order\":0,\"required\":false,\"typeValue\":\"MULTIPLE_CHOICES\",\"valueList\":[],\"values\":[]}],\"avatarUrl\":\"https://d2urtjxi3o4r5s.cloudfront.net/images/child_avatar_2023.png\",\"birthDate\":\"02/05/2023\",\"centerId\":\"6BE27D40-38ED-434F-A275-CA3F7EEE6957\",\"centerName\":\"门头沟小学\",\"completeDates\":[],\"completedDate\":\"07/19/2024\",\"cutoffMonth\":18,\"dRDP\":false,\"displayAlias\":\"Spring 2025\",\"eld\":false,\"eldDB\":false,\"eldLanguageValid\":false,\"enableMultiFramework\":false,\"enrollmentDate\":\"04/17/2023\",\"enrollmentId\":\"989C26C4-8A65-4310-AAAC-301AB8F2B2FA\",\"firstName\":\"SAD\",\"frameworkName\":\"DRDP2015-INFANT-TODDLER Comprehensive View\",\"gender\":\"Female\",\"genderName\":\"Female\",\"groupId\":\"AFE4DFDD-96A4-499C-BBF4-2EFB9D51A8E8\",\"groupName\":\"BBB\",\"haveChildFolder\":false,\"id\":\"94D8F0F8-7BA8-4F58-A3D7-11BA150D3263\",\"iep\":false,\"inactive\":false,\"lastName\":\"CXZC\",\"lateEnrollment\":false,\"lockDate\":\"07/23/2024\",\"locked\":true,\"middleName\":\"DGS\",\"needFirstPeriod\":false,\"parents\":[],\"periodAlias\":\"2024-2025 Spring\",\"periods\":[],\"ratingRecord\":{\"displayAlias\":\"Spring 2025\",\"framework\":\"DRDP2015-INFANT-TODDLER Comprehensive View\",\"frameworkId\":\"E163164F-BDCE-E411-AF66-02C72B94B99B\",\"from\":1718150400000,\"hasDomainScore\":false,\"iepScores\":[],\"periodAlias\":\"2024-2025 Spring\",\"scores\":[{\"core\":false,\"domainId\":\"4cd0392c-55d3-eb11-9c19-4ccc6acf6129\",\"iep\":false,\"levelId\":\"FA8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"levelName\":\"Exploring Earlier\",\"measure\":\"ATL-REG1\",\"measureName\":\"Attention Maintenance\",\"requireEvidence\":false,\"sortIndex\":2},{\"core\":false,\"domainId\":\"4dd0392c-55d3-eb11-9c19-4ccc6acf6129\",\"iep\":false,\"levelId\":\"FD8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"levelName\":\"Building Earlier\",\"measure\":\"ATL-REG2\",\"measureName\":\"Self-Comforting\",\"requireEvidence\":false,\"sortIndex\":3},{\"core\":false,\"domainId\":\"4ed0392c-55d3-eb11-9c19-4ccc6acf6129\",\"iep\":false,\"levelId\":\"FC8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"levelName\":\"Exploring Later\",\"measure\":\"ATL-REG3\",\"measureName\":\"Imitation\",\"requireEvidence\":false,\"sortIndex\":4},{\"core\":false,\"domainId\":\"4fd0392c-55d3-eb11-9c19-4ccc6acf6129\",\"iep\":false,\"levelId\":\"FA8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"levelName\":\"Exploring Earlier\",\"measure\":\"ATL-REG4\",\"measureName\":\"Curiosity and Initiative in Learning\",\"requireEvidence\":false,\"sortIndex\":5},{\"core\":false,\"domainId\":\"50d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"iep\":false,\"levelId\":\"F98C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"levelName\":\"Responding Later\",\"measure\":\"ATL-REG5\",\"measureName\":\"Self-Control of Feelings and Behavior\",\"requireEvidence\":false,\"sortIndex\":6},{\"core\":false,\"domainId\":\"54d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"iep\":false,\"levelId\":\"FC8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"levelName\":\"Exploring Later\",\"measure\":\"SED1\",\"measureName\":\"Identity of Self in Relation to Others\",\"requireEvidence\":false,\"sortIndex\":10},{\"core\":false,\"domainId\":\"55d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"iep\":false,\"levelId\":\"FA8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"levelName\":\"Exploring Earlier\",\"measure\":\"SED2\",\"measureName\":\"Social and Emotional Understanding\",\"requireEvidence\":false,\"sortIndex\":11},{\"core\":false,\"domainId\":\"56d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"iep\":false,\"levelId\":\"F98C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"levelName\":\"Responding Later\",\"measure\":\"SED3\",\"measureName\":\"Relationships and Social Interactions with Familiar Adults\",\"requireEvidence\":false,\"sortIndex\":12},{\"core\":false,\"domainId\":\"57d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"iep\":false,\"levelId\":\"FC8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"levelName\":\"Exploring Later\",\"measure\":\"SED4\",\"measureName\":\"Relationships and Social Interactions with Peers\",\"requireEvidence\":false,\"sortIndex\":13},{\"core\":false,\"domainId\":\"58d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"iep\":false,\"levelId\":\"F98C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"levelName\":\"Responding Later\",\"measure\":\"SED5\",\"measureName\":\"Symbolic and Sociodramatic Play\",\"requireEvidence\":false,\"sortIndex\":14},{\"core\":false,\"domainId\":\"64d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"iep\":false,\"levelId\":\"FB8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"levelName\":\"Exploring  Middle\",\"measure\":\"LLD1\",\"measureName\":\"Understanding of Language (Receptive)\",\"requireEvidence\":false,\"sortIndex\":16},{\"core\":false,\"domainId\":\"65d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"iep\":false,\"levelId\":\"FD8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"levelName\":\"Building Earlier\",\"measure\":\"LLD2\",\"measureName\":\"Responsiveness to Language\",\"requireEvidence\":false,\"sortIndex\":17},{\"core\":false,\"domainId\":\"66d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"iep\":false,\"levelId\":\"FC8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"levelName\":\"Exploring Later\",\"measure\":\"LLD3\",\"measureName\":\"Communication and Use of Language (Expressive)\",\"requireEvidence\":false,\"sortIndex\":18},{\"core\":false,\"domainId\":\"67d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"iep\":false,\"levelId\":\"FA8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"levelName\":\"Exploring Earlier\",\"measure\":\"LLD4\",\"measureName\":\"Reciprocal Communication and Conversation\",\"requireEvidence\":false,\"sortIndex\":19},{\"core\":false,\"domainId\":\"68d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"iep\":false,\"levelId\":\"FC8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"levelName\":\"Exploring Later\",\"measure\":\"LLD5\",\"measureName\":\"Interest in Literacy\",\"requireEvidence\":false,\"sortIndex\":20},{\"core\":false,\"domainId\":\"7bd0392c-55d3-eb11-9c19-4ccc6acf6129\",\"iep\":false,\"levelId\":\"F98C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"levelName\":\"Responding Later\",\"measure\":\"COG1\",\"measureName\":\"Spatial Relationships\",\"requireEvidence\":false,\"sortIndex\":32},{\"core\":false,\"domainId\":\"7cd0392c-55d3-eb11-9c19-4ccc6acf6129\",\"iep\":false,\"levelId\":\"FD8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"levelName\":\"Building Earlier\",\"measure\":\"COG2\",\"measureName\":\"Classification\",\"requireEvidence\":false,\"sortIndex\":34},{\"core\":false,\"domainId\":\"7dd0392c-55d3-eb11-9c19-4ccc6acf6129\",\"iep\":false,\"levelId\":\"FA8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"levelName\":\"Exploring Earlier\",\"measure\":\"COG3\",\"measureName\":\"Number Sense of Quantity\",\"requireEvidence\":false,\"sortIndex\":35},{\"core\":false,\"domainId\":\"82d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"iep\":false,\"levelId\":\"FD8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"levelName\":\"Building Earlier\",\"measure\":\"COG8\",\"measureName\":\"Cause and Effect\",\"requireEvidence\":false,\"sortIndex\":41},{\"core\":false,\"domainId\":\"83d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"iep\":false,\"levelId\":\"F98C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"levelName\":\"Responding Later\",\"measure\":\"COG9\",\"measureName\":\"Inquiry Through Observation and Investigation\",\"requireEvidence\":false,\"sortIndex\":42},{\"core\":false,\"domainId\":\"85d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"iep\":false,\"levelId\":\"F88C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"levelName\":\"Responding Earlier\",\"measure\":\"COG11\",\"measureName\":\"Knowledge of the Natural World\",\"requireEvidence\":false,\"sortIndex\":44},{\"core\":false,\"domainId\":\"93d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"iep\":false,\"levelId\":\"F98C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"levelName\":\"Responding Later\",\"measure\":\"PD-HLTH1\",\"measureName\":\"Perceptual-Motor Skills and Movement Concepts\",\"requireEvidence\":false,\"sortIndex\":47},{\"core\":false,\"domainId\":\"94d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"iep\":false,\"levelId\":\"FB8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"levelName\":\"Exploring  Middle\",\"measure\":\"PD-HLTH2\",\"measureName\":\"Gross Locomotor Movement Skills\",\"requireEvidence\":false,\"sortIndex\":48},{\"core\":false,\"domainId\":\"95d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"iep\":false,\"levelId\":\"FA8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"levelName\":\"Exploring Earlier\",\"measure\":\"PD-HLTH3\",\"measureName\":\"Gross Motor Manipulative Skills\",\"requireEvidence\":false,\"sortIndex\":49},{\"core\":false,\"domainId\":\"96d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"iep\":false,\"levelId\":\"F98C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"levelName\":\"Responding Later\",\"measure\":\"PD-HLTH4\",\"measureName\":\"Fine Motor Manipulative Skills\",\"requireEvidence\":false,\"sortIndex\":50},{\"core\":false,\"domainId\":\"97d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"iep\":false,\"levelId\":\"FA8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"levelName\":\"Exploring Earlier\",\"measure\":\"PD-HLTH5\",\"measureName\":\"Safety\",\"requireEvidence\":false,\"sortIndex\":52},{\"core\":false,\"domainId\":\"98d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"iep\":false,\"levelId\":\"FD8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"levelName\":\"Building Earlier\",\"measure\":\"PD-HLTH6\",\"measureName\":\"Personal Care Routines: Hygiene\",\"requireEvidence\":false,\"sortIndex\":53},{\"core\":false,\"domainId\":\"99d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"iep\":false,\"levelId\":\"FC8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"levelName\":\"Exploring Later\",\"measure\":\"PD-HLTH7\",\"measureName\":\"Personal Care Routines: Feeding\",\"requireEvidence\":false,\"sortIndex\":54},{\"core\":false,\"domainId\":\"9ad0392c-55d3-eb11-9c19-4ccc6acf6129\",\"iep\":false,\"levelId\":\"FA8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"levelName\":\"Exploring Earlier\",\"measure\":\"PD-HLTH8\",\"measureName\":\"Personal Care Routines: Dressing\",\"requireEvidence\":false,\"sortIndex\":55}],\"to\":1722556800000},\"showEld\":false,\"showEldAdditional\":true,\"showIep\":false,\"status\":\"GENERATED\",\"updateAtUtc\":\"07/23/2024\"}";

        String atlRegDomain ="{\"aliasDataList\":[],\"authorities\":[{\"child\":true,\"cohort\":true,\"framework\":\"ITC\",\"group\":true,\"parent\":true,\"standard\":true}],\"className\":\"atl-reg\",\"description\":\"The Approaches to Learning skills include attention maintenance, engagement and persistence, and curiosity and initiative. The Self-Regulation skills include self-comforting, self-control of feelings and behavior, imitation, and shared use of space and materials.\",\"distributionDataList\":[],\"length\":661,\"levelTemplates\":[],\"levels\":[{\"className\":\"re\",\"message\":\"At the Responding Earlier level, your child attends to and focuses on the people, things, and activities close by. You can support learning and development by watching, talking, and listening to your child. Make eye contact, say a few words, and listen and respond to your child's coos, smiles, sounds, looks, and gestures. Regularly interact with your child for at least a few minutes each time so that your child has a chance to pay attention to your face and voice.\",\"name\":\"Responding Earlier\",\"width\":16.8639831623},{\"className\":\"rl\",\"message\":\"At the Responding Later level, your child is learning how to shift attention from one person or thing to another, seek comfort when upset, and imitate others' sounds and actions. You can support learning and development by making eye contact, smiling, and talking with your child during daily routines (such as diapering and dressing). Using short phrases and sentences, talk about what you are doing as you interact with your child. Use simple words to describe what your child is seeing and touching. Keep the same general schedule to help your child to know what is going to happen next.\",\"name\":\"Responding Later\",\"width\":12.7861089187},{\"className\":\"ee\",\"message\":\"At the Exploring Earlier level, your child is learning how to focus attention on activities for short periods of time, imitate words or gestures (such as waving goodbye), and seek comfort from special people or objects (like a blanket or stuffed animal). You can support learning and development by making time for unhurried play and by commenting on what you are doing and your child’s responses during daily routines. You can also describe what is going to happen next in the routine so that your child can get ready for the next activity. Keep the same general schedule to help your child to know what is going to happen next.\",\"name\":\"Exploring Earlier\",\"width\":14.3383320179},{\"className\":\"el\",\"message\":\"At the Exploring Later level, your child is learning how to pay attention to and explore people, toys, and activities and interact with others for longer periods of time. You can support learning and development by noticing what your child is interested in and providing a variety of experiences and play materials that support your child’s exploration of interesting things. Organize your child's toys in simple ways, for example, a basket for cars, another for blocks, and another for stuffed animals. An organized play environment will help your child focus on exploration and learning.\",\"name\":\"Exploring Later\",\"width\":11.4706656143},{\"className\":\"be\",\"message\":\"At the Building Earlier level, your child is exploring how things work. Your child is learning how to seek an adult's help with challenges, manage emotions when mildly upset, and imitate some of the steps of a routine (such as the steps of handwashing). You can support your child's learning and development by encouraging a natural sense of curiosity. For example, when your child discovers a new idea, you can ask questions such as \\\"What would happen if...?\\\" Be available to provide support when your child faces a challenge or feels upset during play.\",\"name\":\"Building Earlier\",\"width\":6.287818995},{\"className\":\"bm\",\"message\":\"\",\"name\":\"Building Middle\",\"width\":7.**********},{\"className\":\"bl\",\"message\":\"\",\"name\":\"Building Later\",\"width\":11.**********},{\"className\":\"ie\",\"message\":\"\",\"name\":\"Integrating Earlier\",\"width\":18.**********}],\"min\":70,\"name\":\"Approaches to Learning Self-Regulation\"}";
        String pdHlthDomain = "{\"aliasDataList\":[],\"authorities\":[{\"child\":true,\"cohort\":true,\"framework\":\"ITC\",\"group\":true,\"parent\":true,\"standard\":true}],\"className\":\"pd-hlth\",\"description\":\"The Physical Development knowledge or skill areas in this domain include perceptual-motor skills and movement concepts, gross locomotor movement skills, gross motor manipulative skills, fine motor manipulative skills, and active physical play. The Health knowledge or skill areas in this domain include nutrition, safety, and personal care routines (hygiene, feeding, dressing).\",\"distributionDataList\":[],\"length\":655,\"levelTemplates\":[],\"levels\":[{\"className\":\"re\",\"message\":\"At the Responding Earlier level, your child responds to sights, sounds, and touch by moving in simple ways (such as looking toward a caregiver's voice, kicking, or trying to grab the finger of a caregiver). You can support learning and development by providing safe spaces where your child can practice moving parts of the body, such as the hands, arms, feet, and legs, without your help. Place your child tummy side up on a blanket on the floor, and give your child time to explore and build core strength. Stay close by so you can respond with a few positive words and smile when your child looks at you, coos, or makes other playful sounds.\",\"name\":\"Responding Earlier\",\"width\":15.**********},{\"className\":\"rl\",\"message\":\"At the Responding Later level, your child moves purposefully (such as reaching an arm out to grab a toy) and moves several body parts together (such as moving several multiple body parts together or in a sequence to roll over). Your child also shows awareness of routines, such as eating or getting dressed, by trying to do or avoid parts of the routine (such as reaching for the spoon during a meal or turning away during dressing). You can support learning and development by making eye contact, smiling, and talking with your child during daily routines (such as diapering and dressing). Provide simple materials (such as a stuffed toy or a rattle) in the play space that your child can reach for and safe spaces where your child can move around and practice reaching for and grabbing things as well as crawling and rolling.\",\"name\":\"Responding Later\",\"width\":11.305965192},{\"className\":\"ee\",\"message\":\"At the Exploring Earlier level, your child is learning how to be an active participant in routines (such as picking up small pieces of food and eating them). Your child also uses the whole body to move around on the floor or ground (such as creeping, crawling, or scooting), tries to do different movements with the whole body, and repeats actions to explore and learn what will happen (such as banging a cup on the table or splashing in water). You can support learning and development by allowing your child to participate in common routines and activities (such as eating, dressing, and preparing for bed) and by providing safe spaces where your child can explore various items. In play areas, place objects such as couches and large cushions that your child can use to try to pull up to standing.\",\"name\":\"Exploring Earlier\",\"width\":5.9050484921},{\"className\":\"em\",\"message\":\"At the Exploring Middle level, your child is learning to use the whole body to do actions (such as pushing a cart) and move while standing and holding onto a support (called \\\"cruising\\\"). Your child also picks up small objects with a thumb and finger and shows a preference for several favorite foods. You can support learning and development by providing safe spaces with various items that your child can pick up and explore. In play areas, place objects that your child can use to pull up to standing and things that your child can push around while standing(such as boxes, small strollers, and carts). Introduce your child to a variety of foods. Try serving foods prepared in different ways (such as raw, grilled, steamed, or cut in shapes). Give your child choices, and allow your child to taste and eat foods in the order your child prefers.\",\"name\":\"Exploring Middle\",\"width\":6.8623223064},{\"className\":\"el\",\"message\":\"At the Exploring Later level, your child is learning to hold objects while walking and to try out different ways of using the body (such as first using hands to push on a door and then the whole body to push it open). Your child is also learning to follow adults' guidance for safety. You can support learning and development by providing things to carry, such as small baskets or purses with handles, that your child can use to fill with smaller objects (such as recycled water bottles in a basket or plastic bucket or other things your child finds). Provide a variety of soft balls (made out of socks or yarn) that your child can carry and throw. During daily routines, talk about safety activities (such as cleaning up spills, walking indoors, and pushing in chairs), and practice safe activities your child can watch you do. Provide guidance, give gentle reminders, and acknowledge your child's attempts to do safe behaviors.\",\"name\":\"Exploring Later\",\"width\":9.6851335193},{\"className\":\"be\",\"message\":\"At the Building Earlier level, your child is learning how to coordinate multiple parts of the body (such as using two feet to jump very low to the ground or using one hand to hold a bucket in place while scooping sand with the other hand). Your child is also learning to follow safety rules and take part in common routines with help from an adult. You can support your child's learning and development by providing opportunities for your child to practice balancing, jumping, bending, climbing, and running. Provide your child with positive encouragement, and invite your child to talk about ways to stay safe during physical play.\",\"name\":\"Building Earlier\",\"width\":9.**********},{\"className\":\"bm\",\"message\":\"\",\"name\":\"Building Middle\",\"width\":7.**********},{\"className\":\"bl\",\"message\":\"\",\"name\":\"Building Later\",\"width\":9.**********},{\"className\":\"ie\",\"message\":\"\",\"name\":\"Integrating Earlier\",\"width\":24.166334529}],\"min\":70,\"name\":\"Physical Development — Health\"}";
        when(analysisService.mapSnapshot(any(), anyBoolean(), any(), any(), anyBoolean(), any(), any(), anyBoolean())).thenReturn(JsonUtil.fromJson(childSnapshotJson, SnapshotResponse.class));
        when(domainScoreService.checkAllSnapshot(anyList())).thenReturn(true);

        List<DomainScoreEntity> snapshotDomainScore = new ArrayList<>();
        snapshotDomainScore.add(JsonUtil.fromJson("{\"agencyId\":\"7CD64CC8-26A7-4184-8D7E-E7A008415209\",\"createAtUtc\":\"Aug 21, 2024 1:31:55 PM\",\"dRDPDomain\":\"HLTH\",\"deleted\":false,\"domainId\":\"92D0392C-55D3-EB11-9C19-4CCC6ACF6129\",\"domainName\":\"PD-HLTH\",\"domainScore\":331.00,\"frameworkId\":\"E163164F-BDCE-E411-AF66-02C72B94B99B\",\"id\":\"09D6F723-74E4-4151-8B75-C9132A5A1E76\",\"noScoreNum\":0,\"snapshotId\":\"3147FCE0-28B8-4310-A3AA-081D012B982B\",\"stdError\":24.00,\"updateAtUtc\":\"Aug 21, 2024 1:31:55 PM\"}", DomainScoreEntity.class));
        snapshotDomainScore.add(JsonUtil.fromJson("{\"agencyId\":\"7CD64CC8-26A7-4184-8D7E-E7A008415209\",\"createAtUtc\":\"Aug 21, 2024 1:31:55 PM\",\"dRDPDomain\":\"ATL-REG\",\"deleted\":false,\"domainId\":\"4BD0392C-55D3-EB11-9C19-4CCC6ACF6129\",\"domainName\":\"ATL-REG\",\"domainScore\":428.00,\"frameworkId\":\"E163164F-BDCE-E411-AF66-02C72B94B99B\",\"id\":\"2AC8DB21-5D23-4BF1-9F24-823F431FCB92\",\"noScoreNum\":0,\"snapshotId\":\"3147FCE0-28B8-4310-A3AA-081D012B982B\",\"stdError\":24.00,\"updateAtUtc\":\"Aug 21, 2024 1:31:55 PM\"}", DomainScoreEntity.class));
        when(domainDao.getSnapshotDomainScore(anyList())).thenReturn(snapshotDomainScore);

        FrameworkData frameworkData = new FrameworkData();
        frameworkData.getDomainDatas().add(JsonUtil.fromJson(atlRegDomain, DomainData.class));
        frameworkData.getDomainDatas().add(JsonUtil.fromJson(pdHlthDomain, DomainData.class));
        when(domainScoreService.assembleCohortTemplate(frameworkId, "Cohort")).thenReturn(frameworkData);

        DownFileResponse downFileResponse = reportService.downloadCohortReportByChildExcel(request);
        // 断言阶段
        Assert.assertEquals(downFileResponse.getDataStr().endsWith(".xlsx"), true);
    }

    /**
     * 测试下载 Cohort 报告 Excel 方法
     *
     * @throws IOException IO 异常
     */
    @Test
    public void testDownloadCohortReportExcel() throws IOException {
        String userId = "4E8EAB68-A2A8-426C-A2AC-FD2EA6CF05A9";
        String agencyId = "7396EC65-751D-4CE8-A191-33EBE3B97B86";
        String frameworkId = "E163164F-BDCE-E411-AF66-02C72B94B99B";
        com.learninggenie.common.data.entity.UserEntity user = new com.learninggenie.common.data.entity.UserEntity();
        user.setUserName(userId);
        user.setRole(UserRole.AGENCY_OWNER.toString());
        UserModel userModel = new UserModel();
        userModel.setId(userId);
        userModel.setRole(UserRole.AGENCY_OWNER.toString());
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);

        List<String> snapshotIds = new ArrayList<>();
        snapshotIds.add("DA509CFE-F276-4472-A9F6-6A0015E89658");
        List<AgencySnapshotEntity> agencySnapshots = new ArrayList<>();
        AgencySnapshotEntity agencySnapshot = new AgencySnapshotEntity();
        Date utcNow = TimeUtil.getUtcNow();
        agencySnapshot.setId(agencyId);
        agencySnapshot.setAgencyId(agencyId);
        agencySnapshot.setPeriodAlias("2023-2024 Time2");
        agencySnapshot.setUpdateAtUtc(utcNow);
        LGSnapshot.StudentRecordSnapshot studentSnapshot = LGSnapshot.StudentRecordSnapshot.newBuilder()
                .setRecordId("DA509CFE-F276-4472-A9F6-6A0015E89658")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .build();
        LGSnapshot.ClassSnapshot classSnapshot = LGSnapshot.ClassSnapshot.newBuilder()
                .setId("1B4088B6-624C-4872-98E2-B62D1CA6D6C9")
                .setName("sg001")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .addRecords(studentSnapshot)
                .build();
        LGSnapshot.CenterSnapshot centerSnapshot = LGSnapshot.CenterSnapshot.newBuilder()
                .setId("sc001")
                .setName("sc001")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .addClasses(classSnapshot)
                .build();
        LGSnapshot.Properties property = LGSnapshot.Properties.newBuilder()
                .setName("a001")
                .setValue("avalue001")
                .setType("atype001")
                .build();
        LGSnapshot.AgencySnapshot agencySnapshotData = LGSnapshot.AgencySnapshot.newBuilder()
                .setId("a000001")
                .setName("s000001")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .addCenters(centerSnapshot)
                .addProperties(property)
                .build();
        agencySnapshot.setData(agencySnapshotData.toByteArray());
        agencySnapshots.add(agencySnapshot);

        // 准备请求参数
        DownloadCohortReportExcelRequest request = JsonUtil.fromJson("{\"alias\":[\"2023-2024 Time3\",\"2023-2024 Time2\",\"2023-2024 Time1\"],\"frameworkId\":\"E163164F-BDCE-E411-AF66-02C72B94B99B\",\"agencyIds\":[\"7396EC65-751D-4CE8-A191-33EBE3B97B86\",\"7396EC65-751D-4CE8-A191-33EBE3B97B86\",\"7396EC65-751D-4CE8-A191-33EBE3B97B86\"],\"centerIds\":[\"9BD8A9F1-213E-4513-9EDD-3AA5A2FC16D7\",\"CE7DCFD3-1935-4680-AE33-ED360DA9E3F9\",\"7112394d-fcd6-41d8-b8be-47533e56f484\",\"369E9B87-9827-4036-BA71-4DCD212BE2CE\"],\"groupIds\":[\"FD6CB23C-BF61-49A5-B184-192A7F4724D2\",\"1A449D93-275A-4D0F-8094-46EFC082B457\",\"30cd3fa7-0e53-4da8-b6e9-742852afe128\",\"006D8F19-77E9-4B70-BDB9-5C37552AE24C\"],\"agencyId\":\"\",\"attrFilters\":[{\"displayName\":\"Age Group\",\"name\":\"Age Group\",\"values\":[\"0 to 8 Months\",\"9 to 18 Months\",\"19 to 36 Months\",\"3 Years-Old\",\"4 Years-Old\",\"5 Years-Old\"]}],\"birthFrom\":\"2019-05-01\",\"birthTo\":\"2024-05-01\",\"entryFrom\":\"2022-05-01\",\"entryTo\":\"2024-05-01\",\"viewId\":\"652DEAC4-0B27-46A4-A8E6-91E71F277D08\",\"centerNames\":[\"Aagle Flight School\",\"ARM\",\"ARM11\",\"Burbank Elementary School\"],\"groupNames\":[\"5.30 new class 1\",\"Haskins-71-TK/K Combo\",\"ITC class11\",\"ITC-Demo\"],\"childrenNum\":0,\"showPieChart\":true,\"periodAlias\":\"2023-2024 Time3,2023-2024 Time2,2023-2024 Time1\",\"portfolioId\":\"E163164F-BDCE-E411-AF66-02C72B94B99B\",\"portfolioName\":\"DRDP2015-INFANT-TODDLER Comprehensive View\"}", DownloadCohortReportExcelRequest.class);

        List<StudentSnapshotEntity> studentSnapshots1 = new ArrayList<>();
        StudentSnapshotEntity studentSnapshotEntity1 = new StudentSnapshotEntity();
        studentSnapshotEntity1.setId("DA509CFE-F276-4472-A9F6-6A0015E89658");
        studentSnapshotEntity1.setEnrollmentId("4AB7ADE2-C813-411D-B7CF-A1C5EEA13D59");
        studentSnapshotEntity1.setGroupId("1B4088B6-624C-4872-98E2-B62D1CA6D6C9");
        studentSnapshotEntity1.setFrameworkId(frameworkId);
        studentSnapshotEntity1.setAgencyId(agencyId);
        studentSnapshots1.add(studentSnapshotEntity1);

        when(userProvider.getCurrentUserId()).thenReturn(userId);

        when(userProvider.checkUser(userId)).thenReturn(user);
        when(userDao.getUserById(userId)).thenReturn(userModel);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        when(agencyDao.getSnapshot(agencyId, request.getAlias())).thenReturn(agencySnapshots);
        when(studentDao.getSnapshotsByIds(anyString(), anyString(), anyLong(), anyList())).thenReturn(studentSnapshots1);

        when(agencyDao.isDRDPtech(agencyId)).thenReturn(false);

        String childSnapshotJson = "{\"age\":4,\"ageMonth\":49,\"cutoffMonth\":50,\"ratingRecord\":{\"periodAlias\":\"2023-2024 Time2\",\"displayAlias\":\"2023-2024 Time2 (2nd Assessment)\",\"from\":1685923200000,\"to\":1698710400000,\"framework\":\"DRDP2015-INFANT-TODDLER Comprehensive View\",\"frameworkId\":\"E163164F-BDCE-E411-AF66-02C72B94B99B\",\"scores\":[{\"domainId\":\"4cd0392c-55d3-eb11-9c19-4ccc6acf6129\",\"measure\":\"ATL-REG1\",\"measureName\":\"Attention Maintenance\",\"levelName\":\"Exploring Later\",\"levelId\":\"FC8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"sortIndex\":2,\"requireEvidence\":false,\"iep\":false,\"core\":false},{\"domainId\":\"4dd0392c-55d3-eb11-9c19-4ccc6acf6129\",\"measure\":\"ATL-REG2\",\"measureName\":\"Self-Comforting\",\"levelName\":\"Exploring Later\",\"levelId\":\"FC8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"sortIndex\":3,\"requireEvidence\":false,\"iep\":false,\"core\":false},{\"domainId\":\"4ed0392c-55d3-eb11-9c19-4ccc6acf6129\",\"measure\":\"ATL-REG3\",\"measureName\":\"Imitation\",\"levelName\":\"Exploring Later\",\"levelId\":\"FC8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"sortIndex\":4,\"requireEvidence\":false,\"iep\":false,\"core\":false},{\"domainId\":\"4fd0392c-55d3-eb11-9c19-4ccc6acf6129\",\"measure\":\"ATL-REG4\",\"measureName\":\"Curiosity and Initiative in Learning\",\"levelName\":\"Exploring Later\",\"levelId\":\"FC8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"sortIndex\":5,\"requireEvidence\":false,\"iep\":false,\"core\":false},{\"domainId\":\"50d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"measure\":\"ATL-REG5\",\"measureName\":\"Self-Control of Feelings and Behavior\",\"levelName\":\"Exploring Later\",\"levelId\":\"FC8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"sortIndex\":6,\"requireEvidence\":false,\"iep\":false,\"core\":false},{\"domainId\":\"54d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"measure\":\"SED1\",\"measureName\":\"Identity of Self in Relation to Others\",\"levelName\":\"Exploring Later\",\"levelId\":\"FC8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"sortIndex\":10,\"requireEvidence\":false,\"iep\":false,\"core\":false},{\"domainId\":\"55d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"measure\":\"SED2\",\"measureName\":\"Social and Emotional Understanding\",\"levelName\":\"Exploring Later\",\"levelId\":\"FC8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"sortIndex\":11,\"requireEvidence\":false,\"iep\":false,\"core\":false},{\"domainId\":\"56d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"measure\":\"SED3\",\"measureName\":\"Relationships and Social Interactions with Familiar Adults\",\"levelName\":\"Exploring Later\",\"levelId\":\"FC8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"sortIndex\":12,\"requireEvidence\":false,\"iep\":false,\"core\":false},{\"domainId\":\"57d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"measure\":\"SED4\",\"measureName\":\"Relationships and Social Interactions with Peers\",\"levelName\":\"Exploring Later\",\"levelId\":\"FC8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"sortIndex\":13,\"requireEvidence\":false,\"iep\":false,\"core\":false},{\"domainId\":\"58d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"measure\":\"SED5\",\"measureName\":\"Symbolic and Sociodramatic Play\",\"levelName\":\"Exploring Later\",\"levelId\":\"FC8C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\"sortIndex\":14,\"requireEvidence\":false,\"iep\":false,\"core\":false},{\"domainId\":\"64d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"measure\":\"LLD1\",\"measureName\":\"Understanding of Language (Receptive)\",\"levelName\":\"\",\"levelId\":\"\",\"sortIndex\":16,\"requireEvidence\":false,\"iep\":false,\"core\":false},{\"domainId\":\"65d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"measure\":\"LLD2\",\"measureName\":\"Responsiveness to Language\",\"levelName\":\"\",\"levelId\":\"\",\"sortIndex\":17,\"requireEvidence\":false,\"iep\":false,\"core\":false},{\"domainId\":\"66d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"measure\":\"LLD3\",\"measureName\":\"Communication and Use of Language (Expressive)\",\"levelName\":\"\",\"levelId\":\"\",\"sortIndex\":18,\"requireEvidence\":false,\"iep\":false,\"core\":false},{\"domainId\":\"67d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"measure\":\"LLD4\",\"measureName\":\"Reciprocal Communication and Conversation\",\"levelName\":\"\",\"levelId\":\"\",\"sortIndex\":19,\"requireEvidence\":false,\"iep\":false,\"core\":false},{\"domainId\":\"68d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"measure\":\"LLD5\",\"measureName\":\"Interest in Literacy\",\"levelName\":\"\",\"levelId\":\"\",\"sortIndex\":20,\"requireEvidence\":false,\"iep\":false,\"core\":false},{\"domainId\":\"7bd0392c-55d3-eb11-9c19-4ccc6acf6129\",\"measure\":\"COG1\",\"measureName\":\"Spatial Relationships\",\"levelName\":\"\",\"levelId\":\"\",\"sortIndex\":32,\"requireEvidence\":false,\"iep\":false,\"core\":false},{\"domainId\":\"7cd0392c-55d3-eb11-9c19-4ccc6acf6129\",\"measure\":\"COG2\",\"measureName\":\"Classification\",\"levelName\":\"\",\"levelId\":\"\",\"sortIndex\":34,\"requireEvidence\":false,\"iep\":false,\"core\":false},{\"domainId\":\"7dd0392c-55d3-eb11-9c19-4ccc6acf6129\",\"measure\":\"COG3\",\"measureName\":\"Number Sense of Quantity\",\"levelName\":\"\",\"levelId\":\"\",\"sortIndex\":35,\"requireEvidence\":false,\"iep\":false,\"core\":false},{\"domainId\":\"82d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"measure\":\"COG8\",\"measureName\":\"Cause and Effect\",\"levelName\":\"\",\"levelId\":\"\",\"sortIndex\":41,\"requireEvidence\":false,\"iep\":false,\"core\":false},{\"domainId\":\"83d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"measure\":\"COG9\",\"measureName\":\"Inquiry Through Observation and Investigation\",\"levelName\":\"\",\"levelId\":\"\",\"sortIndex\":42,\"requireEvidence\":false,\"iep\":false,\"core\":false},{\"domainId\":\"85d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"measure\":\"COG11\",\"measureName\":\"Knowledge of the Natural World\",\"levelName\":\"\",\"levelId\":\"\",\"sortIndex\":44,\"requireEvidence\":false,\"iep\":false,\"core\":false},{\"domainId\":\"93d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"measure\":\"PD-HLTH1\",\"measureName\":\"Perceptual-Motor Skills and Movement Concepts\",\"levelName\":\"\",\"levelId\":\"\",\"sortIndex\":47,\"requireEvidence\":false,\"iep\":false,\"core\":false},{\"domainId\":\"94d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"measure\":\"PD-HLTH2\",\"measureName\":\"Gross Locomotor Movement Skills\",\"levelName\":\"\",\"levelId\":\"\",\"sortIndex\":48,\"requireEvidence\":false,\"iep\":false,\"core\":false},{\"domainId\":\"95d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"measure\":\"PD-HLTH3\",\"measureName\":\"Gross Motor Manipulative Skills\",\"levelName\":\"\",\"levelId\":\"\",\"sortIndex\":49,\"requireEvidence\":false,\"iep\":false,\"core\":false},{\"domainId\":\"96d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"measure\":\"PD-HLTH4\",\"measureName\":\"Fine Motor Manipulative Skills\",\"levelName\":\"\",\"levelId\":\"\",\"sortIndex\":50,\"requireEvidence\":false,\"iep\":false,\"core\":false},{\"domainId\":\"97d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"measure\":\"PD-HLTH5\",\"measureName\":\"Safety\",\"levelName\":\"\",\"levelId\":\"\",\"sortIndex\":52,\"requireEvidence\":false,\"iep\":false,\"core\":false},{\"domainId\":\"98d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"measure\":\"PD-HLTH6\",\"measureName\":\"Personal Care Routines: Hygiene\",\"levelName\":\"\",\"levelId\":\"\",\"sortIndex\":53,\"requireEvidence\":false,\"iep\":false,\"core\":false},{\"domainId\":\"99d0392c-55d3-eb11-9c19-4ccc6acf6129\",\"measure\":\"PD-HLTH7\",\"measureName\":\"Personal Care Routines: Feeding\",\"levelName\":\"\",\"levelId\":\"\",\"sortIndex\":54,\"requireEvidence\":false,\"iep\":false,\"core\":false},{\"domainId\":\"9ad0392c-55d3-eb11-9c19-4ccc6acf6129\",\"measure\":\"PD-HLTH8\",\"measureName\":\"Personal Care Routines: Dressing\",\"levelName\":\"\",\"levelId\":\"\",\"sortIndex\":55,\"requireEvidence\":false,\"iep\":false,\"core\":false}],\"hasDomainScore\":false},\"lockDate\":\"08/15/2023\",\"periodAlias\":\"2023-2024 Time2\",\"updateAtUtc\":\"03/28/2024\",\"isActive\":true,\"lastSnapshot\":{\"id\":\"********-3D4A-4ADC-99C6-A928E29FAA38\",\"createAt\":1692074437946,\"updateAt\":1692074437946},\"attrGroups\":[],\"status\":\"GENERATED\",\"id\":\"DA509CFE-F276-4472-A9F6-6A0015E89658\",\"enrollmentId\":\"4AB7ADE2-C813-411D-B7CF-A1C5EEA13D59\",\"firstName\":\"itc01\",\"lastName\":\"1\",\"middleName\":\"\",\"avatarUrl\":\"https://d2urtjxi3o4r5s.cloudfront.net/images/child_avatar_2023.png\",\"groupId\":\"30cd3fa7-0e53-4da8-b6e9-742852afe128\",\"groupName\":\"ITC class11\",\"centerId\":\"7112394d-fcd6-41d8-b8be-47533e56f484\",\"centerName\":\"ARM11\",\"agencyId\":\"7396EC65-751D-4CE8-A191-33EBE3B97B86\",\"agencyName\":\"test Stage\",\"birthDate\":\"07/01/2019\",\"gender\":\"Male\",\"genderName\":\"Male\",\"enrollmentDate\":\"09/01/2022\",\"completedDate\":\"08/15/2023\",\"completeDates\":[],\"isLocked\":false,\"isInactive\":false,\"displayAlias\":\"2023-2024 Time2 (2nd Assessment)\",\"frameworkName\":\"DRDP2015-INFANT-TODDLER Comprehensive View\",\"attrs\":[{\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"order\":0,\"name\":\"type1\",\"values\":[],\"valueList\":[]},{\"typeValue\":\"TEXT_FIELD\",\"required\":false,\"order\":0,\"name\":\"Teacher\",\"displayName\":\"Teacher\",\"values\":[\"<EMAIL>\"],\"valueList\":[]},{\"typeValue\":\"TEXT_FIELD\",\"required\":false,\"order\":0,\"name\":\"Statewide Student Identifier\",\"values\":[],\"valueList\":[]},{\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"order\":0,\"name\":\"Special education eligibility\",\"values\":[],\"valueList\":[]},{\"typeValue\":\"CROSS_AGENCY_IDENTIFIER_\",\"required\":false,\"order\":0,\"name\":\"SPECIAL_TEACHER\",\"values\":[],\"valueList\":[]},{\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"order\":0,\"name\":\"Race\",\"displayName\":\"Race\",\"values\":[\"American Indian or Alaska Native\",\"Asian Indian\",\"Black or African American\",\"Cambodian\",\"Chinese\",\"Filipino\",\"Guamanian\",\"Hawaiian\",\"Hmong\",\"Japanese\",\"Korean\",\"Laotian\",\"Other Asian\",\"Other Pacific Islander\",\"Samoan\",\"Tahitian\",\"Vietnamese\",\"Intentionally Left Blank\"],\"valueList\":[]},{\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"order\":0,\"name\":\"Program Name\",\"displayName\":\"Program Name\",\"values\":[\"Migrant\"],\"valueList\":[]},{\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"order\":0,\"name\":\"Language\",\"displayName\":\"Language\",\"values\":[\"Spanish\",\"Vietnamese\",\"Cantonese\",\"Korean\"],\"valueList\":[]},{\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"order\":0,\"name\":\"IEP/IFSP\",\"displayName\":\"IEP/IFSP\",\"values\":[\"Yes\"],\"valueList\":[]},{\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"order\":0,\"name\":\"Hispanic\",\"displayName\":\"Hispanic or Latino\",\"values\":[\"Yes\"],\"valueList\":[]},{\"typeValue\":\"CROSS_AGENCY_IDENTIFIER_\",\"required\":false,\"order\":0,\"name\":\"Grantee Admin\",\"values\":[],\"valueList\":[]},{\"typeValue\":\"TEXT_FIELD\",\"required\":false,\"order\":0,\"name\":\"External ID\",\"displayName\":\"External ID\",\"values\":[\"E100483\"],\"valueList\":[]},{\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"order\":0,\"name\":\"Enrol_subsidized\",\"displayName\":\"Enrol_subsidized\",\"values\":[\"Yes\"],\"valueList\":[]},{\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"order\":0,\"name\":\"Enrol reducedlunch\",\"values\":[],\"valueList\":[]},{\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"order\":0,\"name\":\"ELD\",\"displayName\":\"ELD\",\"values\":[\"Yes\"],\"valueList\":[]},{\"typeValue\":\"SINGLE_CHOICE\",\"required\":false,\"order\":0,\"name\":\"Bilingual\",\"values\":[],\"valueList\":[]},{\"typeValue\":\"MULTIPLE_CHOICES\",\"required\":false,\"order\":0,\"name\":\"Adaptations\",\"values\":[],\"valueList\":[]}],\"parents\":[],\"isDRDP\":false,\"needFirstPeriod\":false,\"periods\":[],\"lateEnrollment\":false,\"iep\":false,\"eld\":false,\"haveChildFolder\":false,\"enableMultiFramework\":false,\"showIep\":false,\"showEld\":false,\"eldDB\":false,\"isShowEldAdditional\":true,\"eldLanguageValid\":false}";

        String atlRegDomain = "{\"className\":\"atl-reg\",\"name\":\"Approaches to Learning Self-Regulation\",\"description\":\"The Approaches to Learning skills include attention maintenance, engagement and persistence, and curiosity and initiative. The Self-Regulation skills include self-comforting, self-control of feelings and behavior, imitation, and shared use of space and materials.\",\"authorities\":[{\"framework\":\"ITC\",\"child\":true,\"parent\":true,\"cohort\":true,\"group\":true,\"standard\":true}],\"aliasDataList\":[],\"distributionDataList\":[{\"alias\":\"2023-2024 Time3 (3rd Assessment)\",\"count\":4,\"notShow\":false,\"scoreLeft\":37.5945537065052950075642965,\"average\":318.**********,\"totalScore\":1149.00,\"levelDataList\":[{\"className\":\"re\",\"name\":\"Responding Earlier\",\"count\":1,\"percentage\":25,\"children\":[{\"enrollmentId\":\"ED6165DF-EEC7-4AE1-AB24-E7D3C72E1D6F\",\"centerName\":\"Burbank Elementary School\",\"groupName\":\"Haskins-71-TK/K Combo\",\"firstName\":\"James\",\"middleName\":\"Christopher\",\"lastName\":\"Brand\",\"showIep\":false,\"showEld\":false,\"domain\":\"Approaches to Learning Self-Regulation\",\"ratingScore\":\"Responding Earlier\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time3 (3rd Assessment)\",\"attrs\":[]}]},{\"className\":\"rl\",\"name\":\"Responding Later\",\"count\":1,\"percentage\":25,\"children\":[{\"enrollmentId\":\"40B76948-6F93-4993-B1AA-55934F8FABF3\",\"centerName\":\"Burbank Elementary School\",\"groupName\":\"Haskins-71-TK/K Combo\",\"firstName\":\"Sebastian\",\"middleName\":\"\",\"lastName\":\"Torres\",\"showIep\":false,\"showEld\":false,\"domain\":\"Approaches to Learning Self-Regulation\",\"ratingScore\":\"Responding Later\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time3 (3rd Assessment)\",\"attrs\":[]}]},{\"className\":\"ee\",\"name\":\"Exploring Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"el\",\"name\":\"Exploring Later\",\"count\":2,\"percentage\":50,\"children\":[{\"enrollmentId\":\"139D4347-E294-4266-9494-8E84B9FEDF41\",\"centerName\":\"Burbank Elementary School\",\"groupName\":\"Haskins-71-TK/K Combo\",\"firstName\":\"Ayden\",\"middleName\":\"Lamar\",\"lastName\":\"Morgan\",\"showIep\":false,\"showEld\":false,\"domain\":\"Approaches to Learning Self-Regulation\",\"ratingScore\":\"Exploring Later\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time3 (3rd Assessment)\",\"attrs\":[]},{\"enrollmentId\":\"BEE0F268-0B85-485C-9B73-141BE4323D19\",\"centerName\":\"Burbank Elementary School\",\"groupName\":\"Haskins-71-TK/K Combo\",\"firstName\":\"Jaxon\",\"middleName\":\"Dean\",\"lastName\":\"Hernandez\",\"showIep\":false,\"showEld\":false,\"domain\":\"Approaches to Learning Self-Regulation\",\"ratingScore\":\"Exploring Later\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time3 (3rd Assessment)\",\"attrs\":[]}]},{\"className\":\"be\",\"name\":\"Building Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bm\",\"name\":\"Building Middle\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bl\",\"name\":\"Building Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"ie\",\"name\":\"Integrating Earlier\",\"count\":0,\"percentage\":0,\"children\":[]}],\"filterCohortList\":[{\"name\":\"3 Years-Old\",\"count\":4,\"scoreLeft\":37.5945537065052950075642965,\"average\":318.**********,\"totalScore\":1149.00,\"levelDataList\":[{\"className\":\"re\",\"name\":\"Responding Earlier\",\"count\":1,\"percentage\":25,\"children\":[{\"enrollmentId\":\"ED6165DF-EEC7-4AE1-AB24-E7D3C72E1D6F\",\"centerName\":\"Burbank Elementary School\",\"groupName\":\"Haskins-71-TK/K Combo\",\"firstName\":\"James\",\"middleName\":\"Christopher\",\"lastName\":\"Brand\",\"showIep\":false,\"showEld\":false,\"domain\":\"Approaches to Learning Self-Regulation\",\"ratingScore\":\"Responding Earlier\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time3 (3rd Assessment)\",\"attrs\":[]}]},{\"className\":\"rl\",\"name\":\"Responding Later\",\"count\":1,\"percentage\":25,\"children\":[{\"enrollmentId\":\"40B76948-6F93-4993-B1AA-55934F8FABF3\",\"centerName\":\"Burbank Elementary School\",\"groupName\":\"Haskins-71-TK/K Combo\",\"firstName\":\"Sebastian\",\"middleName\":\"\",\"lastName\":\"Torres\",\"showIep\":false,\"showEld\":false,\"domain\":\"Approaches to Learning Self-Regulation\",\"ratingScore\":\"Responding Later\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time3 (3rd Assessment)\",\"attrs\":[]}]},{\"className\":\"ee\",\"name\":\"Exploring Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"el\",\"name\":\"Exploring Later\",\"count\":2,\"percentage\":50,\"children\":[{\"enrollmentId\":\"139D4347-E294-4266-9494-8E84B9FEDF41\",\"centerName\":\"Burbank Elementary School\",\"groupName\":\"Haskins-71-TK/K Combo\",\"firstName\":\"Ayden\",\"middleName\":\"Lamar\",\"lastName\":\"Morgan\",\"showIep\":false,\"showEld\":false,\"domain\":\"Approaches to Learning Self-Regulation\",\"ratingScore\":\"Exploring Later\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time3 (3rd Assessment)\",\"attrs\":[]},{\"enrollmentId\":\"BEE0F268-0B85-485C-9B73-141BE4323D19\",\"centerName\":\"Burbank Elementary School\",\"groupName\":\"Haskins-71-TK/K Combo\",\"firstName\":\"Jaxon\",\"middleName\":\"Dean\",\"lastName\":\"Hernandez\",\"showIep\":false,\"showEld\":false,\"domain\":\"Approaches to Learning Self-Regulation\",\"ratingScore\":\"Exploring Later\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time3 (3rd Assessment)\",\"attrs\":[]}]},{\"className\":\"be\",\"name\":\"Building Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bm\",\"name\":\"Building Middle\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bl\",\"name\":\"Building Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"ie\",\"name\":\"Integrating Earlier\",\"count\":0,\"percentage\":0,\"children\":[]}]},{\"name\":\"Birth 5/1/2019 to 5/1/2024\",\"count\":4,\"scoreLeft\":37.5945537065052950075642965,\"average\":318.**********,\"totalScore\":1149.00,\"levelDataList\":[{\"className\":\"re\",\"name\":\"Responding Earlier\",\"count\":1,\"percentage\":25,\"children\":[{\"enrollmentId\":\"ED6165DF-EEC7-4AE1-AB24-E7D3C72E1D6F\",\"centerName\":\"Burbank Elementary School\",\"groupName\":\"Haskins-71-TK/K Combo\",\"firstName\":\"James\",\"middleName\":\"Christopher\",\"lastName\":\"Brand\",\"showIep\":false,\"showEld\":false,\"domain\":\"Approaches to Learning Self-Regulation\",\"ratingScore\":\"Responding Earlier\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time3 (3rd Assessment)\",\"attrs\":[]}]},{\"className\":\"rl\",\"name\":\"Responding Later\",\"count\":1,\"percentage\":25,\"children\":[{\"enrollmentId\":\"40B76948-6F93-4993-B1AA-55934F8FABF3\",\"centerName\":\"Burbank Elementary School\",\"groupName\":\"Haskins-71-TK/K Combo\",\"firstName\":\"Sebastian\",\"middleName\":\"\",\"lastName\":\"Torres\",\"showIep\":false,\"showEld\":false,\"domain\":\"Approaches to Learning Self-Regulation\",\"ratingScore\":\"Responding Later\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time3 (3rd Assessment)\",\"attrs\":[]}]},{\"className\":\"ee\",\"name\":\"Exploring Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"el\",\"name\":\"Exploring Later\",\"count\":2,\"percentage\":50,\"children\":[{\"enrollmentId\":\"139D4347-E294-4266-9494-8E84B9FEDF41\",\"centerName\":\"Burbank Elementary School\",\"groupName\":\"Haskins-71-TK/K Combo\",\"firstName\":\"Ayden\",\"middleName\":\"Lamar\",\"lastName\":\"Morgan\",\"showIep\":false,\"showEld\":false,\"domain\":\"Approaches to Learning Self-Regulation\",\"ratingScore\":\"Exploring Later\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time3 (3rd Assessment)\",\"attrs\":[]},{\"enrollmentId\":\"BEE0F268-0B85-485C-9B73-141BE4323D19\",\"centerName\":\"Burbank Elementary School\",\"groupName\":\"Haskins-71-TK/K Combo\",\"firstName\":\"Jaxon\",\"middleName\":\"Dean\",\"lastName\":\"Hernandez\",\"showIep\":false,\"showEld\":false,\"domain\":\"Approaches to Learning Self-Regulation\",\"ratingScore\":\"Exploring Later\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time3 (3rd Assessment)\",\"attrs\":[]}]},{\"className\":\"be\",\"name\":\"Building Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bm\",\"name\":\"Building Middle\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bl\",\"name\":\"Building Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"ie\",\"name\":\"Integrating Earlier\",\"count\":0,\"percentage\":0,\"children\":[]}]},{\"name\":\"Entry 5/1/2022 to 5/1/2024\",\"count\":4,\"scoreLeft\":37.5945537065052950075642965,\"average\":318.**********,\"totalScore\":1149.00,\"levelDataList\":[{\"className\":\"re\",\"name\":\"Responding Earlier\",\"count\":1,\"percentage\":25,\"children\":[{\"enrollmentId\":\"ED6165DF-EEC7-4AE1-AB24-E7D3C72E1D6F\",\"centerName\":\"Burbank Elementary School\",\"groupName\":\"Haskins-71-TK/K Combo\",\"firstName\":\"James\",\"middleName\":\"Christopher\",\"lastName\":\"Brand\",\"showIep\":false,\"showEld\":false,\"domain\":\"Approaches to Learning Self-Regulation\",\"ratingScore\":\"Responding Earlier\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time3 (3rd Assessment)\",\"attrs\":[]}]},{\"className\":\"rl\",\"name\":\"Responding Later\",\"count\":1,\"percentage\":25,\"children\":[{\"enrollmentId\":\"40B76948-6F93-4993-B1AA-55934F8FABF3\",\"centerName\":\"Burbank Elementary School\",\"groupName\":\"Haskins-71-TK/K Combo\",\"firstName\":\"Sebastian\",\"middleName\":\"\",\"lastName\":\"Torres\",\"showIep\":false,\"showEld\":false,\"domain\":\"Approaches to Learning Self-Regulation\",\"ratingScore\":\"Responding Later\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time3 (3rd Assessment)\",\"attrs\":[]}]},{\"className\":\"ee\",\"name\":\"Exploring Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"el\",\"name\":\"Exploring Later\",\"count\":2,\"percentage\":50,\"children\":[{\"enrollmentId\":\"139D4347-E294-4266-9494-8E84B9FEDF41\",\"centerName\":\"Burbank Elementary School\",\"groupName\":\"Haskins-71-TK/K Combo\",\"firstName\":\"Ayden\",\"middleName\":\"Lamar\",\"lastName\":\"Morgan\",\"showIep\":false,\"showEld\":false,\"domain\":\"Approaches to Learning Self-Regulation\",\"ratingScore\":\"Exploring Later\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time3 (3rd Assessment)\",\"attrs\":[]},{\"enrollmentId\":\"BEE0F268-0B85-485C-9B73-141BE4323D19\",\"centerName\":\"Burbank Elementary School\",\"groupName\":\"Haskins-71-TK/K Combo\",\"firstName\":\"Jaxon\",\"middleName\":\"Dean\",\"lastName\":\"Hernandez\",\"showIep\":false,\"showEld\":false,\"domain\":\"Approaches to Learning Self-Regulation\",\"ratingScore\":\"Exploring Later\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time3 (3rd Assessment)\",\"attrs\":[]}]},{\"className\":\"be\",\"name\":\"Building Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bm\",\"name\":\"Building Middle\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bl\",\"name\":\"Building Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"ie\",\"name\":\"Integrating Earlier\",\"count\":0,\"percentage\":0,\"children\":[]}]}]},{\"alias\":\"2023-2024 Time2 (2nd Assessment)\",\"count\":2,\"notShow\":false,\"scoreLeft\":41.6036308623298033282904690,\"average\":345.**********,\"totalScore\":690.00,\"levelDataList\":[{\"className\":\"re\",\"name\":\"Responding Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"rl\",\"name\":\"Responding Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"ee\",\"name\":\"Exploring Earlier\",\"count\":1,\"percentage\":50,\"children\":[{\"enrollmentId\":\"D81AD1BE-A64F-4FE4-B283-6CBE0F954E0A\",\"centerName\":\"Aagle Flight School\",\"groupName\":\"ITC-Demo\",\"firstName\":\"07\",\"middleName\":\"07\",\"lastName\":\"07\",\"showIep\":false,\"showEld\":false,\"domain\":\"Approaches to Learning Self-Regulation\",\"ratingScore\":\"Exploring Earlier\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time2 (2nd Assessment)\",\"attrs\":[]}]},{\"className\":\"el\",\"name\":\"Exploring Later\",\"count\":1,\"percentage\":50,\"children\":[{\"enrollmentId\":\"4AB7ADE2-C813-411D-B7CF-A1C5EEA13D59\",\"centerName\":\"ARM11\",\"groupName\":\"ITC class11\",\"firstName\":\"itc01\",\"middleName\":\"\",\"lastName\":\"1\",\"showIep\":false,\"showEld\":false,\"domain\":\"Approaches to Learning Self-Regulation\",\"ratingScore\":\"Exploring Later\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time2 (2nd Assessment)\",\"attrs\":[]}]},{\"className\":\"be\",\"name\":\"Building Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bm\",\"name\":\"Building Middle\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bl\",\"name\":\"Building Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"ie\",\"name\":\"Integrating Earlier\",\"count\":0,\"percentage\":0,\"children\":[]}],\"filterCohortList\":[{\"name\":\"0 to 8 Months\",\"count\":1,\"scoreLeft\":31.4674735249621785173978820,\"average\":278.00,\"totalScore\":278.00,\"levelDataList\":[{\"className\":\"re\",\"name\":\"Responding Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"rl\",\"name\":\"Responding Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"ee\",\"name\":\"Exploring Earlier\",\"count\":1,\"percentage\":100,\"children\":[{\"enrollmentId\":\"D81AD1BE-A64F-4FE4-B283-6CBE0F954E0A\",\"centerName\":\"Aagle Flight School\",\"groupName\":\"ITC-Demo\",\"firstName\":\"07\",\"middleName\":\"07\",\"lastName\":\"07\",\"showIep\":false,\"showEld\":false,\"domain\":\"Approaches to Learning Self-Regulation\",\"ratingScore\":\"Exploring Earlier\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time2 (2nd Assessment)\",\"attrs\":[]}]},{\"className\":\"el\",\"name\":\"Exploring Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"be\",\"name\":\"Building Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bm\",\"name\":\"Building Middle\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bl\",\"name\":\"Building Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"ie\",\"name\":\"Integrating Earlier\",\"count\":0,\"percentage\":0,\"children\":[]}]},{\"name\":\"4 Years-Old\",\"count\":1,\"scoreLeft\":51.7397881996974281391830560,\"average\":412.00,\"totalScore\":412.00,\"levelDataList\":[{\"className\":\"re\",\"name\":\"Responding Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"rl\",\"name\":\"Responding Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"ee\",\"name\":\"Exploring Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"el\",\"name\":\"Exploring Later\",\"count\":1,\"percentage\":100,\"children\":[{\"enrollmentId\":\"4AB7ADE2-C813-411D-B7CF-A1C5EEA13D59\",\"centerName\":\"ARM11\",\"groupName\":\"ITC class11\",\"firstName\":\"itc01\",\"middleName\":\"\",\"lastName\":\"1\",\"showIep\":false,\"showEld\":false,\"domain\":\"Approaches to Learning Self-Regulation\",\"ratingScore\":\"Exploring Later\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time2 (2nd Assessment)\",\"attrs\":[]}]},{\"className\":\"be\",\"name\":\"Building Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bm\",\"name\":\"Building Middle\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bl\",\"name\":\"Building Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"ie\",\"name\":\"Integrating Earlier\",\"count\":0,\"percentage\":0,\"children\":[]}]},{\"name\":\"Birth 5/1/2019 to 5/1/2024\",\"count\":2,\"scoreLeft\":41.6036308623298033282904690,\"average\":345.**********,\"totalScore\":690.00,\"levelDataList\":[{\"className\":\"re\",\"name\":\"Responding Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"rl\",\"name\":\"Responding Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"ee\",\"name\":\"Exploring Earlier\",\"count\":1,\"percentage\":50,\"children\":[{\"enrollmentId\":\"D81AD1BE-A64F-4FE4-B283-6CBE0F954E0A\",\"centerName\":\"Aagle Flight School\",\"groupName\":\"ITC-Demo\",\"firstName\":\"07\",\"middleName\":\"07\",\"lastName\":\"07\",\"showIep\":false,\"showEld\":false,\"domain\":\"Approaches to Learning Self-Regulation\",\"ratingScore\":\"Exploring Earlier\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time2 (2nd Assessment)\",\"attrs\":[]}]},{\"className\":\"el\",\"name\":\"Exploring Later\",\"count\":1,\"percentage\":50,\"children\":[{\"enrollmentId\":\"4AB7ADE2-C813-411D-B7CF-A1C5EEA13D59\",\"centerName\":\"ARM11\",\"groupName\":\"ITC class11\",\"firstName\":\"itc01\",\"middleName\":\"\",\"lastName\":\"1\",\"showIep\":false,\"showEld\":false,\"domain\":\"Approaches to Learning Self-Regulation\",\"ratingScore\":\"Exploring Later\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time2 (2nd Assessment)\",\"attrs\":[]}]},{\"className\":\"be\",\"name\":\"Building Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bm\",\"name\":\"Building Middle\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bl\",\"name\":\"Building Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"ie\",\"name\":\"Integrating Earlier\",\"count\":0,\"percentage\":0,\"children\":[]}]},{\"name\":\"Entry 5/1/2022 to 5/1/2024\",\"count\":2,\"scoreLeft\":41.6036308623298033282904690,\"average\":345.**********,\"totalScore\":690.00,\"levelDataList\":[{\"className\":\"re\",\"name\":\"Responding Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"rl\",\"name\":\"Responding Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"ee\",\"name\":\"Exploring Earlier\",\"count\":1,\"percentage\":50,\"children\":[{\"enrollmentId\":\"D81AD1BE-A64F-4FE4-B283-6CBE0F954E0A\",\"centerName\":\"Aagle Flight School\",\"groupName\":\"ITC-Demo\",\"firstName\":\"07\",\"middleName\":\"07\",\"lastName\":\"07\",\"showIep\":false,\"showEld\":false,\"domain\":\"Approaches to Learning Self-Regulation\",\"ratingScore\":\"Exploring Earlier\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time2 (2nd Assessment)\",\"attrs\":[]}]},{\"className\":\"el\",\"name\":\"Exploring Later\",\"count\":1,\"percentage\":50,\"children\":[{\"enrollmentId\":\"4AB7ADE2-C813-411D-B7CF-A1C5EEA13D59\",\"centerName\":\"ARM11\",\"groupName\":\"ITC class11\",\"firstName\":\"itc01\",\"middleName\":\"\",\"lastName\":\"1\",\"showIep\":false,\"showEld\":false,\"domain\":\"Approaches to Learning Self-Regulation\",\"ratingScore\":\"Exploring Later\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time2 (2nd Assessment)\",\"attrs\":[]}]},{\"className\":\"be\",\"name\":\"Building Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bm\",\"name\":\"Building Middle\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bl\",\"name\":\"Building Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"ie\",\"name\":\"Integrating Earlier\",\"count\":0,\"percentage\":0,\"children\":[]}]}]},{\"alias\":\"2023-2024 Time1 (1st Assessment)\",\"count\":0,\"notShow\":false,\"scoreLeft\":0,\"average\":0,\"totalScore\":0,\"levelDataList\":[{\"className\":\"re\",\"name\":\"Responding Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"rl\",\"name\":\"Responding Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"ee\",\"name\":\"Exploring Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"el\",\"name\":\"Exploring Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"be\",\"name\":\"Building Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bm\",\"name\":\"Building Middle\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bl\",\"name\":\"Building Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"ie\",\"name\":\"Integrating Earlier\",\"count\":0,\"percentage\":0,\"children\":[]}],\"filterCohortList\":[]}],\"levelTemplates\":[],\"levels\":[{\"className\":\"re\",\"name\":\"Responding Earlier\",\"message\":\"At the Responding Earlier level, your child attends to and focuses on the people, things, and activities close by. You can support learning and development by watching, talking, and listening to your child. Make eye contact, say a few words, and listen and respond to your child\\u0027s coos, smiles, sounds, looks, and gestures. Regularly interact with your child for at least a few minutes each time so that your child has a chance to pay attention to your face and voice.\",\"width\":16.8639831623},{\"className\":\"rl\",\"name\":\"Responding Later\",\"message\":\"At the Responding Later level, your child is learning how to shift attention from one person or thing to another, seek comfort when upset, and imitate others\\u0027 sounds and actions. You can support learning and development by making eye contact, smiling, and talking with your child during daily routines (such as diapering and dressing). Using short phrases and sentences, talk about what you are doing as you interact with your child. Use simple words to describe what your child is seeing and touching. Keep the same general schedule to help your child to know what is going to happen next.\",\"width\":12.7861089187},{\"className\":\"ee\",\"name\":\"Exploring Earlier\",\"message\":\"At the Exploring Earlier level, your child is learning how to focus attention on activities for short periods of time, imitate words or gestures (such as waving goodbye), and seek comfort from special people or objects (like a blanket or stuffed animal). You can support learning and development by making time for unhurried play and by commenting on what you are doing and your child’s responses during daily routines. You can also describe what is going to happen next in the routine so that your child can get ready for the next activity. Keep the same general schedule to help your child to know what is going to happen next.\",\"width\":14.3383320179},{\"className\":\"el\",\"name\":\"Exploring Later\",\"message\":\"At the Exploring Later level, your child is learning how to pay attention to and explore people, toys, and activities and interact with others for longer periods of time. You can support learning and development by noticing what your child is interested in and providing a variety of experiences and play materials that support your child’s exploration of interesting things. Organize your child\\u0027s toys in simple ways, for example, a basket for cars, another for blocks, and another for stuffed animals. An organized play environment will help your child focus on exploration and learning.\",\"width\":11.4706656143},{\"className\":\"be\",\"name\":\"Building Earlier\",\"message\":\"At the Building Earlier level, your child is exploring how things work. Your child is learning how to seek an adult\\u0027s help with challenges, manage emotions when mildly upset, and imitate some of the steps of a routine (such as the steps of handwashing). You can support your child\\u0027s learning and development by encouraging a natural sense of curiosity. For example, when your child discovers a new idea, you can ask questions such as \\\"What would happen if...?\\\" Be available to provide support when your child faces a challenge or feels upset during play.\",\"width\":6.287818995},{\"className\":\"bm\",\"name\":\"Building Middle\",\"message\":\"\",\"width\":7.**********},{\"className\":\"bl\",\"name\":\"Building Later\",\"message\":\"\",\"width\":11.**********},{\"className\":\"ie\",\"name\":\"Integrating Earlier\",\"message\":\"\",\"width\":18.**********}],\"min\":70,\"length\":661}";
        String pdHlthDomain = "{\"className\":\"pd-hlth\",\"name\":\"Physical Development — Health\",\"description\":\"The Physical Development knowledge or skill areas in this domain include perceptual-motor skills and movement concepts, gross locomotor movement skills, gross motor manipulative skills, fine motor manipulative skills, and active physical play. The Health knowledge or skill areas in this domain include nutrition, safety, and personal care routines (hygiene, feeding, dressing).\",\"authorities\":[{\"framework\":\"ITC\",\"child\":true,\"parent\":true,\"cohort\":true,\"group\":true,\"standard\":true}],\"aliasDataList\":[],\"distributionDataList\":[{\"alias\":\"2023-2024 Time3 (3rd Assessment)\",\"count\":4,\"notShow\":false,\"scoreLeft\":38.0152671755725190839694656,\"average\":319.**********,\"totalScore\":1209.00,\"levelDataList\":[{\"className\":\"re\",\"name\":\"Responding Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"rl\",\"name\":\"Responding Later\",\"count\":1,\"percentage\":25,\"children\":[{\"enrollmentId\":\"BEE0F268-0B85-485C-9B73-141BE4323D19\",\"centerName\":\"Burbank Elementary School\",\"groupName\":\"Haskins-71-TK/K Combo\",\"firstName\":\"Jaxon\",\"middleName\":\"Dean\",\"lastName\":\"Hernandez\",\"showIep\":false,\"showEld\":false,\"domain\":\"Physical Development — Health\",\"ratingScore\":\"Responding Later\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time3 (3rd Assessment)\",\"attrs\":[]}]},{\"className\":\"ee\",\"name\":\"Exploring Earlier\",\"count\":1,\"percentage\":25,\"children\":[{\"enrollmentId\":\"139D4347-E294-4266-9494-8E84B9FEDF41\",\"centerName\":\"Burbank Elementary School\",\"groupName\":\"Haskins-71-TK/K Combo\",\"firstName\":\"Ayden\",\"middleName\":\"Lamar\",\"lastName\":\"Morgan\",\"showIep\":false,\"showEld\":false,\"domain\":\"Physical Development — Health\",\"ratingScore\":\"Exploring Earlier\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time3 (3rd Assessment)\",\"attrs\":[]}]},{\"className\":\"em\",\"name\":\"Exploring Middle\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"el\",\"name\":\"Exploring Later\",\"count\":2,\"percentage\":50,\"children\":[{\"enrollmentId\":\"ED6165DF-EEC7-4AE1-AB24-E7D3C72E1D6F\",\"centerName\":\"Burbank Elementary School\",\"groupName\":\"Haskins-71-TK/K Combo\",\"firstName\":\"James\",\"middleName\":\"Christopher\",\"lastName\":\"Brand\",\"showIep\":false,\"showEld\":false,\"domain\":\"Physical Development — Health\",\"ratingScore\":\"Exploring Later\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time3 (3rd Assessment)\",\"attrs\":[]},{\"enrollmentId\":\"40B76948-6F93-4993-B1AA-55934F8FABF3\",\"centerName\":\"Burbank Elementary School\",\"groupName\":\"Haskins-71-TK/K Combo\",\"firstName\":\"Sebastian\",\"middleName\":\"\",\"lastName\":\"Torres\",\"showIep\":false,\"showEld\":false,\"domain\":\"Physical Development — Health\",\"ratingScore\":\"Exploring Later\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time3 (3rd Assessment)\",\"attrs\":[]}]},{\"className\":\"be\",\"name\":\"Building Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bm\",\"name\":\"Building Middle\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bl\",\"name\":\"Building Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"ie\",\"name\":\"Integrating Earlier\",\"count\":0,\"percentage\":0,\"children\":[]}],\"filterCohortList\":[{\"name\":\"3 Years-Old\",\"count\":4,\"scoreLeft\":38.0152671755725190839694656,\"average\":319.**********,\"totalScore\":1209.00,\"levelDataList\":[{\"className\":\"re\",\"name\":\"Responding Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"rl\",\"name\":\"Responding Later\",\"count\":1,\"percentage\":25,\"children\":[{\"enrollmentId\":\"BEE0F268-0B85-485C-9B73-141BE4323D19\",\"centerName\":\"Burbank Elementary School\",\"groupName\":\"Haskins-71-TK/K Combo\",\"firstName\":\"Jaxon\",\"middleName\":\"Dean\",\"lastName\":\"Hernandez\",\"showIep\":false,\"showEld\":false,\"domain\":\"Physical Development — Health\",\"ratingScore\":\"Responding Later\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time3 (3rd Assessment)\",\"attrs\":[]}]},{\"className\":\"ee\",\"name\":\"Exploring Earlier\",\"count\":1,\"percentage\":25,\"children\":[{\"enrollmentId\":\"139D4347-E294-4266-9494-8E84B9FEDF41\",\"centerName\":\"Burbank Elementary School\",\"groupName\":\"Haskins-71-TK/K Combo\",\"firstName\":\"Ayden\",\"middleName\":\"Lamar\",\"lastName\":\"Morgan\",\"showIep\":false,\"showEld\":false,\"domain\":\"Physical Development — Health\",\"ratingScore\":\"Exploring Earlier\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time3 (3rd Assessment)\",\"attrs\":[]}]},{\"className\":\"em\",\"name\":\"Exploring Middle\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"el\",\"name\":\"Exploring Later\",\"count\":2,\"percentage\":50,\"children\":[{\"enrollmentId\":\"ED6165DF-EEC7-4AE1-AB24-E7D3C72E1D6F\",\"centerName\":\"Burbank Elementary School\",\"groupName\":\"Haskins-71-TK/K Combo\",\"firstName\":\"James\",\"middleName\":\"Christopher\",\"lastName\":\"Brand\",\"showIep\":false,\"showEld\":false,\"domain\":\"Physical Development — Health\",\"ratingScore\":\"Exploring Later\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time3 (3rd Assessment)\",\"attrs\":[]},{\"enrollmentId\":\"40B76948-6F93-4993-B1AA-55934F8FABF3\",\"centerName\":\"Burbank Elementary School\",\"groupName\":\"Haskins-71-TK/K Combo\",\"firstName\":\"Sebastian\",\"middleName\":\"\",\"lastName\":\"Torres\",\"showIep\":false,\"showEld\":false,\"domain\":\"Physical Development — Health\",\"ratingScore\":\"Exploring Later\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time3 (3rd Assessment)\",\"attrs\":[]}]},{\"className\":\"be\",\"name\":\"Building Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bm\",\"name\":\"Building Middle\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bl\",\"name\":\"Building Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"ie\",\"name\":\"Integrating Earlier\",\"count\":0,\"percentage\":0,\"children\":[]}]},{\"name\":\"Birth 5/1/2019 to 5/1/2024\",\"count\":4,\"scoreLeft\":38.0152671755725190839694656,\"average\":319.**********,\"totalScore\":1209.00,\"levelDataList\":[{\"className\":\"re\",\"name\":\"Responding Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"rl\",\"name\":\"Responding Later\",\"count\":1,\"percentage\":25,\"children\":[{\"enrollmentId\":\"BEE0F268-0B85-485C-9B73-141BE4323D19\",\"centerName\":\"Burbank Elementary School\",\"groupName\":\"Haskins-71-TK/K Combo\",\"firstName\":\"Jaxon\",\"middleName\":\"Dean\",\"lastName\":\"Hernandez\",\"showIep\":false,\"showEld\":false,\"domain\":\"Physical Development — Health\",\"ratingScore\":\"Responding Later\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time3 (3rd Assessment)\",\"attrs\":[]}]},{\"className\":\"ee\",\"name\":\"Exploring Earlier\",\"count\":1,\"percentage\":25,\"children\":[{\"enrollmentId\":\"139D4347-E294-4266-9494-8E84B9FEDF41\",\"centerName\":\"Burbank Elementary School\",\"groupName\":\"Haskins-71-TK/K Combo\",\"firstName\":\"Ayden\",\"middleName\":\"Lamar\",\"lastName\":\"Morgan\",\"showIep\":false,\"showEld\":false,\"domain\":\"Physical Development — Health\",\"ratingScore\":\"Exploring Earlier\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time3 (3rd Assessment)\",\"attrs\":[]}]},{\"className\":\"em\",\"name\":\"Exploring Middle\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"el\",\"name\":\"Exploring Later\",\"count\":2,\"percentage\":50,\"children\":[{\"enrollmentId\":\"ED6165DF-EEC7-4AE1-AB24-E7D3C72E1D6F\",\"centerName\":\"Burbank Elementary School\",\"groupName\":\"Haskins-71-TK/K Combo\",\"firstName\":\"James\",\"middleName\":\"Christopher\",\"lastName\":\"Brand\",\"showIep\":false,\"showEld\":false,\"domain\":\"Physical Development — Health\",\"ratingScore\":\"Exploring Later\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time3 (3rd Assessment)\",\"attrs\":[]},{\"enrollmentId\":\"40B76948-6F93-4993-B1AA-55934F8FABF3\",\"centerName\":\"Burbank Elementary School\",\"groupName\":\"Haskins-71-TK/K Combo\",\"firstName\":\"Sebastian\",\"middleName\":\"\",\"lastName\":\"Torres\",\"showIep\":false,\"showEld\":false,\"domain\":\"Physical Development — Health\",\"ratingScore\":\"Exploring Later\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time3 (3rd Assessment)\",\"attrs\":[]}]},{\"className\":\"be\",\"name\":\"Building Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bm\",\"name\":\"Building Middle\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bl\",\"name\":\"Building Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"ie\",\"name\":\"Integrating Earlier\",\"count\":0,\"percentage\":0,\"children\":[]}]},{\"name\":\"Entry 5/1/2022 to 5/1/2024\",\"count\":4,\"scoreLeft\":38.0152671755725190839694656,\"average\":319.**********,\"totalScore\":1209.00,\"levelDataList\":[{\"className\":\"re\",\"name\":\"Responding Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"rl\",\"name\":\"Responding Later\",\"count\":1,\"percentage\":25,\"children\":[{\"enrollmentId\":\"BEE0F268-0B85-485C-9B73-141BE4323D19\",\"centerName\":\"Burbank Elementary School\",\"groupName\":\"Haskins-71-TK/K Combo\",\"firstName\":\"Jaxon\",\"middleName\":\"Dean\",\"lastName\":\"Hernandez\",\"showIep\":false,\"showEld\":false,\"domain\":\"Physical Development — Health\",\"ratingScore\":\"Responding Later\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time3 (3rd Assessment)\",\"attrs\":[]}]},{\"className\":\"ee\",\"name\":\"Exploring Earlier\",\"count\":1,\"percentage\":25,\"children\":[{\"enrollmentId\":\"139D4347-E294-4266-9494-8E84B9FEDF41\",\"centerName\":\"Burbank Elementary School\",\"groupName\":\"Haskins-71-TK/K Combo\",\"firstName\":\"Ayden\",\"middleName\":\"Lamar\",\"lastName\":\"Morgan\",\"showIep\":false,\"showEld\":false,\"domain\":\"Physical Development — Health\",\"ratingScore\":\"Exploring Earlier\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time3 (3rd Assessment)\",\"attrs\":[]}]},{\"className\":\"em\",\"name\":\"Exploring Middle\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"el\",\"name\":\"Exploring Later\",\"count\":2,\"percentage\":50,\"children\":[{\"enrollmentId\":\"ED6165DF-EEC7-4AE1-AB24-E7D3C72E1D6F\",\"centerName\":\"Burbank Elementary School\",\"groupName\":\"Haskins-71-TK/K Combo\",\"firstName\":\"James\",\"middleName\":\"Christopher\",\"lastName\":\"Brand\",\"showIep\":false,\"showEld\":false,\"domain\":\"Physical Development — Health\",\"ratingScore\":\"Exploring Later\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time3 (3rd Assessment)\",\"attrs\":[]},{\"enrollmentId\":\"40B76948-6F93-4993-B1AA-55934F8FABF3\",\"centerName\":\"Burbank Elementary School\",\"groupName\":\"Haskins-71-TK/K Combo\",\"firstName\":\"Sebastian\",\"middleName\":\"\",\"lastName\":\"Torres\",\"showIep\":false,\"showEld\":false,\"domain\":\"Physical Development — Health\",\"ratingScore\":\"Exploring Later\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time3 (3rd Assessment)\",\"attrs\":[]}]},{\"className\":\"be\",\"name\":\"Building Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bm\",\"name\":\"Building Middle\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bl\",\"name\":\"Building Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"ie\",\"name\":\"Integrating Earlier\",\"count\":0,\"percentage\":0,\"children\":[]}]}]},{\"alias\":\"2023-2024 Time2 (2nd Assessment)\",\"count\":1,\"notShow\":false,\"scoreLeft\":30.5343511450381679389312977,\"average\":270.00,\"totalScore\":270.00,\"levelDataList\":[{\"className\":\"re\",\"name\":\"Responding Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"rl\",\"name\":\"Responding Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"ee\",\"name\":\"Exploring Earlier\",\"count\":1,\"percentage\":100,\"children\":[{\"enrollmentId\":\"D81AD1BE-A64F-4FE4-B283-6CBE0F954E0A\",\"centerName\":\"Aagle Flight School\",\"groupName\":\"ITC-Demo\",\"firstName\":\"07\",\"middleName\":\"07\",\"lastName\":\"07\",\"showIep\":false,\"showEld\":false,\"domain\":\"Physical Development — Health\",\"ratingScore\":\"Exploring Earlier\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time2 (2nd Assessment)\",\"attrs\":[]}]},{\"className\":\"em\",\"name\":\"Exploring Middle\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"el\",\"name\":\"Exploring Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"be\",\"name\":\"Building Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bm\",\"name\":\"Building Middle\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bl\",\"name\":\"Building Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"ie\",\"name\":\"Integrating Earlier\",\"count\":0,\"percentage\":0,\"children\":[]}],\"filterCohortList\":[{\"name\":\"0 to 8 Months\",\"count\":1,\"scoreLeft\":30.5343511450381679389312977,\"average\":270.00,\"totalScore\":270.00,\"levelDataList\":[{\"className\":\"re\",\"name\":\"Responding Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"rl\",\"name\":\"Responding Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"ee\",\"name\":\"Exploring Earlier\",\"count\":1,\"percentage\":100,\"children\":[{\"enrollmentId\":\"D81AD1BE-A64F-4FE4-B283-6CBE0F954E0A\",\"centerName\":\"Aagle Flight School\",\"groupName\":\"ITC-Demo\",\"firstName\":\"07\",\"middleName\":\"07\",\"lastName\":\"07\",\"showIep\":false,\"showEld\":false,\"domain\":\"Physical Development — Health\",\"ratingScore\":\"Exploring Earlier\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time2 (2nd Assessment)\",\"attrs\":[]}]},{\"className\":\"em\",\"name\":\"Exploring Middle\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"el\",\"name\":\"Exploring Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"be\",\"name\":\"Building Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bm\",\"name\":\"Building Middle\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bl\",\"name\":\"Building Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"ie\",\"name\":\"Integrating Earlier\",\"count\":0,\"percentage\":0,\"children\":[]}]},{\"name\":\"4 Years-Old\",\"count\":0,\"scoreLeft\":0,\"average\":0,\"totalScore\":0,\"levelDataList\":[{\"className\":\"re\",\"name\":\"Responding Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"rl\",\"name\":\"Responding Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"ee\",\"name\":\"Exploring Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"em\",\"name\":\"Exploring Middle\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"el\",\"name\":\"Exploring Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"be\",\"name\":\"Building Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bm\",\"name\":\"Building Middle\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bl\",\"name\":\"Building Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"ie\",\"name\":\"Integrating Earlier\",\"count\":0,\"percentage\":0,\"children\":[]}]},{\"name\":\"Birth 5/1/2019 to 5/1/2024\",\"count\":1,\"scoreLeft\":30.5343511450381679389312977,\"average\":270.00,\"totalScore\":270.00,\"levelDataList\":[{\"className\":\"re\",\"name\":\"Responding Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"rl\",\"name\":\"Responding Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"ee\",\"name\":\"Exploring Earlier\",\"count\":1,\"percentage\":100,\"children\":[{\"enrollmentId\":\"D81AD1BE-A64F-4FE4-B283-6CBE0F954E0A\",\"centerName\":\"Aagle Flight School\",\"groupName\":\"ITC-Demo\",\"firstName\":\"07\",\"middleName\":\"07\",\"lastName\":\"07\",\"showIep\":false,\"showEld\":false,\"domain\":\"Physical Development — Health\",\"ratingScore\":\"Exploring Earlier\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time2 (2nd Assessment)\",\"attrs\":[]}]},{\"className\":\"em\",\"name\":\"Exploring Middle\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"el\",\"name\":\"Exploring Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"be\",\"name\":\"Building Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bm\",\"name\":\"Building Middle\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bl\",\"name\":\"Building Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"ie\",\"name\":\"Integrating Earlier\",\"count\":0,\"percentage\":0,\"children\":[]}]},{\"name\":\"Entry 5/1/2022 to 5/1/2024\",\"count\":1,\"scoreLeft\":30.5343511450381679389312977,\"average\":270.00,\"totalScore\":270.00,\"levelDataList\":[{\"className\":\"re\",\"name\":\"Responding Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"rl\",\"name\":\"Responding Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"ee\",\"name\":\"Exploring Earlier\",\"count\":1,\"percentage\":100,\"children\":[{\"enrollmentId\":\"D81AD1BE-A64F-4FE4-B283-6CBE0F954E0A\",\"centerName\":\"Aagle Flight School\",\"groupName\":\"ITC-Demo\",\"firstName\":\"07\",\"middleName\":\"07\",\"lastName\":\"07\",\"showIep\":false,\"showEld\":false,\"domain\":\"Physical Development — Health\",\"ratingScore\":\"Exploring Earlier\",\"status\":\"Below\",\"periodDisplayName\":\"2023-2024 Time2 (2nd Assessment)\",\"attrs\":[]}]},{\"className\":\"em\",\"name\":\"Exploring Middle\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"el\",\"name\":\"Exploring Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"be\",\"name\":\"Building Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bm\",\"name\":\"Building Middle\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bl\",\"name\":\"Building Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"ie\",\"name\":\"Integrating Earlier\",\"count\":0,\"percentage\":0,\"children\":[]}]}]},{\"alias\":\"2023-2024 Time1 (1st Assessment)\",\"count\":0,\"notShow\":false,\"scoreLeft\":0,\"average\":0,\"totalScore\":0,\"levelDataList\":[{\"className\":\"re\",\"name\":\"Responding Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"rl\",\"name\":\"Responding Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"ee\",\"name\":\"Exploring Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"em\",\"name\":\"Exploring Middle\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"el\",\"name\":\"Exploring Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"be\",\"name\":\"Building Earlier\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bm\",\"name\":\"Building Middle\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"bl\",\"name\":\"Building Later\",\"count\":0,\"percentage\":0,\"children\":[]},{\"className\":\"ie\",\"name\":\"Integrating Earlier\",\"count\":0,\"percentage\":0,\"children\":[]}],\"filterCohortList\":[]}],\"levelTemplates\":[],\"levels\":[{\"className\":\"re\",\"name\":\"Responding Earlier\",\"message\":\"At the Responding Earlier level, your child responds to sights, sounds, and touch by moving in simple ways (such as looking toward a caregiver\\u0027s voice, kicking, or trying to grab the finger of a caregiver). You can support learning and development by providing safe spaces where your child can practice moving parts of the body, such as the hands, arms, feet, and legs, without your help. Place your child tummy side up on a blanket on the floor, and give your child time to explore and build core strength. Stay close by so you can respond with a few positive words and smile when your child looks at you, coos, or makes other playful sounds.\",\"width\":15.**********},{\"className\":\"rl\",\"name\":\"Responding Later\",\"message\":\"At the Responding Later level, your child moves purposefully (such as reaching an arm out to grab a toy) and moves several body parts together (such as moving several multiple body parts together or in a sequence to roll over). Your child also shows awareness of routines, such as eating or getting dressed, by trying to do or avoid parts of the routine (such as reaching for the spoon during a meal or turning away during dressing). You can support learning and development by making eye contact, smiling, and talking with your child during daily routines (such as diapering and dressing). Provide simple materials (such as a stuffed toy or a rattle) in the play space that your child can reach for and safe spaces where your child can move around and practice reaching for and grabbing things as well as crawling and rolling.\",\"width\":11.305965192},{\"className\":\"ee\",\"name\":\"Exploring Earlier\",\"message\":\"At the Exploring Earlier level, your child is learning how to be an active participant in routines (such as picking up small pieces of food and eating them). Your child also uses the whole body to move around on the floor or ground (such as creeping, crawling, or scooting), tries to do different movements with the whole body, and repeats actions to explore and learn what will happen (such as banging a cup on the table or splashing in water). You can support learning and development by allowing your child to participate in common routines and activities (such as eating, dressing, and preparing for bed) and by providing safe spaces where your child can explore various items. In play areas, place objects such as couches and large cushions that your child can use to try to pull up to standing.\",\"width\":5.9050484921},{\"className\":\"em\",\"name\":\"Exploring Middle\",\"message\":\"At the Exploring Middle level, your child is learning to use the whole body to do actions (such as pushing a cart) and move while standing and holding onto a support (called \\\"cruising\\\"). Your child also picks up small objects with a thumb and finger and shows a preference for several favorite foods. You can support learning and development by providing safe spaces with various items that your child can pick up and explore. In play areas, place objects that your child can use to pull up to standing and things that your child can push around while standing(such as boxes, small strollers, and carts). Introduce your child to a variety of foods. Try serving foods prepared in different ways (such as raw, grilled, steamed, or cut in shapes). Give your child choices, and allow your child to taste and eat foods in the order your child prefers.\",\"width\":6.8623223064},{\"className\":\"el\",\"name\":\"Exploring Later\",\"message\":\"At the Exploring Later level, your child is learning to hold objects while walking and to try out different ways of using the body (such as first using hands to push on a door and then the whole body to push it open). Your child is also learning to follow adults\\u0027 guidance for safety. You can support learning and development by providing things to carry, such as small baskets or purses with handles, that your child can use to fill with smaller objects (such as recycled water bottles in a basket or plastic bucket or other things your child finds). Provide a variety of soft balls (made out of socks or yarn) that your child can carry and throw. During daily routines, talk about safety activities (such as cleaning up spills, walking indoors, and pushing in chairs), and practice safe activities your child can watch you do. Provide guidance, give gentle reminders, and acknowledge your child\\u0027s attempts to do safe behaviors.\",\"width\":9.6851335193},{\"className\":\"be\",\"name\":\"Building Earlier\",\"message\":\"At the Building Earlier level, your child is learning how to coordinate multiple parts of the body (such as using two feet to jump very low to the ground or using one hand to hold a bucket in place while scooping sand with the other hand). Your child is also learning to follow safety rules and take part in common routines with help from an adult. You can support your child\\u0027s learning and development by providing opportunities for your child to practice balancing, jumping, bending, climbing, and running. Provide your child with positive encouragement, and invite your child to talk about ways to stay safe during physical play.\",\"width\":9.**********},{\"className\":\"bm\",\"name\":\"Building Middle\",\"message\":\"\",\"width\":7.**********},{\"className\":\"bl\",\"name\":\"Building Later\",\"message\":\"\",\"width\":9.**********},{\"className\":\"ie\",\"name\":\"Integrating Earlier\",\"message\":\"\",\"width\":24.166334529}],\"min\":70,\"length\":655}";
        when(analysisService.mapSnapshot(any(), anyBoolean(), any(), any(), anyBoolean(), any(), any(), anyBoolean())).thenReturn(JsonUtil.fromJson(childSnapshotJson, SnapshotResponse.class));
        when(domainScoreService.checkAllSnapshot(anyList())).thenReturn(true);

        List<DomainScoreEntity> snapshotDomainScore = new ArrayList<>();
        snapshotDomainScore.add(JsonUtil.fromJson("{\"id\":\"3C2CBDB8-5C86-436E-81D3-590D729A1808\",\"snapshotId\":\"DA509CFE-F276-4472-A9F6-6A0015E89658\",\"domainId\":\"92D0392C-55D3-EB11-9C19-4CCC6ACF6129\",\"domainName\":\"PD-HLTH\",\"DRDPDomain\":\"error\",\"domainScore\":0.00,\"stdError\":0.00,\"noScoreNum\":0,\"frameworkId\":\"E163164F-BDCE-E411-AF66-02C72B94B99B\",\"agencyId\":\"7396EC65-751D-4CE8-A191-33EBE3B97B86\",\"createAtUtc\":\"Apr 1, 2024 1:33:30 PM\",\"updateAtUtc\":\"Apr 1, 2024 1:33:30 PM\",\"isDeleted\":false}", DomainScoreEntity.class));
        snapshotDomainScore.add(JsonUtil.fromJson("{\"id\":\"2604EFB4-9DD5-4B34-B49B-610D638EFA36\",\"snapshotId\":\"DA509CFE-F276-4472-A9F6-6A0015E89658\",\"domainId\":\"4BD0392C-55D3-EB11-9C19-4CCC6ACF6129\",\"domainName\":\"ATL-REG\",\"DRDPDomain\":\"ATL-REG\",\"domainScore\":412.00,\"stdError\":24.00,\"noScoreNum\":0,\"frameworkId\":\"E163164F-BDCE-E411-AF66-02C72B94B99B\",\"agencyId\":\"7396EC65-751D-4CE8-A191-33EBE3B97B86\",\"createAtUtc\":\"Apr 1, 2024 1:33:30 PM\",\"updateAtUtc\":\"Apr 1, 2024 1:33:30 PM\",\"isDeleted\":false}", DomainScoreEntity.class));
        when(domainDao.getSnapshotDomainScore(anyList())).thenReturn(snapshotDomainScore);

        FrameworkData frameworkData = new FrameworkData();
        frameworkData.getDomainDatas().add(JsonUtil.fromJson(atlRegDomain, DomainData.class));
        frameworkData.getDomainDatas().add(JsonUtil.fromJson(pdHlthDomain, DomainData.class));
        when(domainScoreService.assembleCohortTemplate(frameworkId, "Cohort")).thenReturn(frameworkData);

        DownFileResponse downFileResponse = reportService.downloadCohortReportExcel(request);
        // 断言阶段
        Assert.assertEquals(downFileResponse.getDataStr().endsWith(".xlsx"), true);
    }

    /**
     * 测试发送家长进度报告邮件方法
     */
    @Test
    public void testSendParentProgressReportEmail() {
        // 准备请求参数
        String childId = "FE8FED28-9812-41DD-9C1F-62CA5AC13511";
        String pdfJobId = "FD74B03D-C587-411E-BA13-384C6C485B3E";
        String viewType = "all";
        SendParentProgressReportEmailRequest request = new SendParentProgressReportEmailRequest();
        request.setChildId(childId);
        request.setPdfJobId(pdfJobId);
        request.setViewType(viewType);

        // 模拟 UserProvider 行为
        when(userProvider.getTimezoneOffsetNum()).thenReturn(8);
        PdfConvertJobEntity pdfJob = new PdfConvertJobEntity();
        pdfJob.setPdfName("Parent_Progress_Report_Domain_and_Measure_View_PSC 2");
        pdfJob.setUrl("https://s3-us-west-1.amazonaws.com/com.learning-genie.prod.pdf/html/b32de285-41bd-4bcc-a2e2-7f3436888ce6.html");
        pdfJob.setId("078E04E0-5852-4539-BDAB-F894E274577C");
        pdfJob.setPdfUrl("https://s3-us-west-1.amazonaws.com/com.learning-genie.prod.pdf/pdf/f0b4aa0c-cea4-4cd7-8dd7-5c0491548d96.pdf");
        when(reportDao.getPdfJob(request.getPdfJobId())).thenReturn(pdfJob);
        String currentUserId = "D555EA4D-F110-42A0-8247-AE3768DCCD86";
        when(userProvider.getCurrentUserId()).thenReturn(currentUserId);
        UserEntity currentUser = new UserEntity();
        currentUser.setUserName("In-kind Parent");
        currentUser.setEmail("<EMAIL>");
        when(userProvider.getUser(currentUserId)).thenReturn(currentUser);

        // 模拟 StudentDao 行为
        EnrollmentModel enrollmentModel = new EnrollmentModel();
        enrollmentModel.setDisplayName("PSC 2");
        when(studentDao.getEnrollmentWithDelete(request.getChildId())).thenReturn(enrollmentModel);
        String emailTemplate = "<html><body>Dear @userName, here is your report: @pdfDownloadUrl</body></html>";
        MockedStatic<ResourceUtil> resourceUtil = Mockito.mockStatic(ResourceUtil.class);
        resourceUtil.when(() -> ResourceUtil.getEmailTemplateAsString(Mockito.any(), Mockito.any())).thenReturn(emailTemplate);

        // 模拟 FileSystem 行为
        String pdfLogoUrl = "https://s3.amazonaws.com//com.learning-genie.prod.us/7ead3134-b29f-4f97-af49-44a453c826ac.png";
        when(fileSystem.getPublicUrl("7ead3134-b29f-4f97-af49-44a453c826ac.png")).thenReturn(pdfLogoUrl);

        // 模拟 EmailService 行为
        doNothing().when(emailService).sendAsync(any(EmailModel.class));

        // 调用被测试方法
        SendEmailSuccessResponse response = reportService.sendParentProgressReportEmail(request);

        // 释放资源
        resourceUtil.close();

        // 验证结果
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals("<EMAIL>", response.getReceiverEmail());
    }
    /**
     * 测试处理合并年龄组数据
     */
    @Test
    public void testAddMergedAgeGroup() {
        String requestJson = "{\n" +
                "    \"ageGroups\": [\n" +
                "        {\n" +
                "            \"ageGroupName\": \"19 to 36 months\",\n" +
                "            \"childCount\": null,\n" +
                "            \"levelKey\": null,\n" +
                "            \"levelName\": null,\n" +
                "            \"targetPercentage\": 1,\n" +
                "            \"periods\": [\n" +
                "                {\n" +
                "                    \"name\": \"2023-2024 Summer\",\n" +
                "                    \"periodDisplayName\": \"Summer 2024\",\n" +
                "                    \"childCount\": 1000,\n" +
                "                    \"atOrAboveChildCount\": 1000,\n" +
                "                    \"children\": []\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        {\n" +
                "            \"ageGroupName\": \"9 to 18 months\",\n" +
                "            \"childCount\": null,\n" +
                "            \"levelKey\": null,\n" +
                "            \"levelName\": null,\n" +
                "            \"targetPercentage\": 12,\n" +
                "            \"periods\": [\n" +
                "                {\n" +
                "                    \"name\": \"2023-2024 Summer\",\n" +
                "                    \"periodDisplayName\": \"Summer 2024\",\n" +
                "                    \"childCount\": 1000,\n" +
                "                    \"atOrAboveChildCount\": 1000,\n" +
                "                    \"children\": []\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        {\n" +
                "            \"ageGroupName\": \"3 years old\",\n" +
                "            \"childCount\": null,\n" +
                "            \"levelKey\": null,\n" +
                "            \"levelName\": null,\n" +
                "            \"targetPercentage\": 7,\n" +
                "            \"periods\": [\n" +
                "                {\n" +
                "                    \"name\": \"2023-2024 Summer\",\n" +
                "                    \"periodDisplayName\": \"Summer 2024\",\n" +
                "                    \"childCount\": 0,\n" +
                "                    \"atOrAboveChildCount\": -1,\n" +
                "                    \"children\": []\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ]\n" +
                "}";
        JsonObject jsonObject = JsonUtil.fromJson(requestJson, JsonObject.class);
        JsonArray ageGroupsArray = jsonObject.getAsJsonArray("ageGroups");
        Type ageGroupListType = new TypeToken<List<StatisticsAgeGroupModel>>() {}.getType();
        List<StatisticsAgeGroupModel> ageGroups = JsonUtil.fromJson(String.valueOf(ageGroupsArray), ageGroupListType);
        // 准备请求参数
        String mergedAgeGroupName = "9 to 36 months";
        List<String> includeAgeGroupNames = new ArrayList<>();
        includeAgeGroupNames.add("9 to 18 months");
        includeAgeGroupNames.add("19 to 36 months");
        List<String> selectedAgeGroupNames = new ArrayList<>();
        boolean hasAtOrAboveChildCount = false;

        // 调用被测试方法
        reportService.addMergedAgeGroup(ageGroups, mergedAgeGroupName, includeAgeGroupNames, selectedAgeGroupNames, hasAtOrAboveChildCount);

        // 验证结果
        assertEquals(2, ageGroups.size());
        assertEquals("9 to 36 months", ageGroups.get(0).getAgeGroupName());
        assertNull(ageGroups.get(0).getPeriods().get(0).getAtOrAboveChildCount());

        // 准备请求参数
        ageGroups = JsonUtil.fromJson(String.valueOf(ageGroupsArray), ageGroupListType);
        selectedAgeGroupNames.add("9 to 18 months");
        // 调用被测试方法
        reportService.addMergedAgeGroup(ageGroups, mergedAgeGroupName, includeAgeGroupNames, selectedAgeGroupNames, hasAtOrAboveChildCount);
        // 验证结果
        assertEquals(3, ageGroups.size());
        assertEquals("9 to 18 months", ageGroups.get(0).getAgeGroupName());
        assertEquals("9 to 36 months", ageGroups.get(1).getAgeGroupName());
        assertNull(ageGroups.get(1).getPeriods().get(0).getAtOrAboveChildCount());

        // 准备请求参数
        ageGroups = JsonUtil.fromJson(String.valueOf(ageGroupsArray), ageGroupListType);
        hasAtOrAboveChildCount = true;

        // 调用被测试方法
        reportService.addMergedAgeGroup(ageGroups, mergedAgeGroupName, includeAgeGroupNames, selectedAgeGroupNames, hasAtOrAboveChildCount);

        // 验证结果
        assertEquals(3, ageGroups.size());
        assertEquals("9 to 36 months", ageGroups.get(1).getAgeGroupName());
        assertEquals(2000, (long) ageGroups.get(1).getPeriods().get(0).getAtOrAboveChildCount());
    }

    /**
     * 测试处理合并年龄组数据
     */
    @Test
    public void testProcessMergedAgeGroupData() {
        String requestJson = "{\n" +
                "    \"ageGroups\": [\n" +
                "        {\n" +
                "            \"ageGroupName\": \"9 to 18 months\",\n" +
                "            \"childCount\": null,\n" +
                "            \"levelKey\": null,\n" +
                "            \"levelName\": null,\n" +
                "            \"targetPercentage\": null,\n" +
                "            \"periods\": [\n" +
                "                {\n" +
                "                    \"name\": \"2023-2024 Summer\",\n" +
                "                    \"periodDisplayName\": \"Summer 2024\",\n" +
                "                    \"childCount\": 1,\n" +
                "                    \"atOrAboveChildCount\": null,\n" +
                "                    \"children\": []\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        {\n" +
                "            \"ageGroupName\": \"19 to 36 months\",\n" +
                "            \"childCount\": null,\n" +
                "            \"levelKey\": null,\n" +
                "            \"levelName\": null,\n" +
                "            \"targetPercentage\": null,\n" +
                "            \"periods\": [\n" +
                "                {\n" +
                "                    \"name\": \"2023-2024 Summer\",\n" +
                "                    \"periodDisplayName\": \"Summer 2024\",\n" +
                "                    \"childCount\": 1,\n" +
                "                    \"atOrAboveChildCount\": null,\n" +
                "                    \"children\": []\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        {\n" +
                "            \"ageGroupName\": \"3 years old\",\n" +
                "            \"childCount\": null,\n" +
                "            \"levelKey\": null,\n" +
                "            \"levelName\": null,\n" +
                "            \"targetPercentage\": null,\n" +
                "            \"periods\": [\n" +
                "                {\n" +
                "                    \"name\": \"2023-2024 Summer\",\n" +
                "                    \"periodDisplayName\": \"Summer 2024\",\n" +
                "                    \"childCount\": 0,\n" +
                "                    \"atOrAboveChildCount\": null,\n" +
                "                    \"children\": []\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ],\n" +
                "    \"iep\": [\n" +
                "        {\n" +
                "            \"ageGroupName\": \"9 to 18 months\",\n" +
                "            \"childCount\": null,\n" +
                "            \"levelKey\": null,\n" +
                "            \"levelName\": null,\n" +
                "            \"targetPercentage\": null,\n" +
                "            \"periods\": [\n" +
                "                {\n" +
                "                    \"name\": \"2023-2024 Summer\",\n" +
                "                    \"periodDisplayName\": \"Summer 2024\",\n" +
                "                    \"childCount\": 0,\n" +
                "                    \"atOrAboveChildCount\": null,\n" +
                "                    \"children\": []\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ],\n" +
                "    \"measureGroups\": [\n" +
                "        {\n" +
                "            \"groupName\": \"ATL-REG\",\n" +
                "            \"measures\": [\n" +
                "                {\n" +
                "                    \"domainKey\": \"4CD0392C-55D3-EB11-9C19-4CCC6ACF6129\",\n" +
                "                    \"domainName\": \"Attention Maintenance\",\n" +
                "                    \"ageGroups\": [\n" +
                "                        {\n" +
                "                            \"ageGroupName\": \"9 to 18 months\",\n" +
                "                            \"childCount\": 0,\n" +
                "                            \"levelKey\": \"F98C17D2-6DD9-EB11-9C19-4CCC6ACF6129\",\n" +
                "                            \"levelName\": \"Responding Later\",\n" +
                "                            \"targetPercentage\": 1,\n" +
                "                            \"periods\": [\n" +
                "                                {\n" +
                "                                    \"name\": \"2023-2024 Summer\",\n" +
                "                                    \"periodDisplayName\": \"Summer 2024\",\n" +
                "                                    \"childCount\": 0,\n" +
                "                                    \"atOrAboveChildCount\": 0,\n" +
                "                                    \"children\": []\n" +
                "                                }\n" +
                "                            ]\n" +
                "                        }\n" +
                "                    ]\n" +
                "                }\n" +
                "            ],\n" +
                "            \"average\": [\n" +
                "                {\n" +
                "                    \"ageGroupName\": \"19 to 36 months\",\n" +
                "                    \"childCount\": null,\n" +
                "                    \"levelKey\": null,\n" +
                "                    \"levelName\": null,\n" +
                "                    \"targetPercentage\": 1,\n" +
                "                    \"periods\": [\n" +
                "                        {\n" +
                "                            \"name\": \"2023-2024 Summer\",\n" +
                "                            \"periodDisplayName\": \"Summer 2024\",\n" +
                "                            \"childCount\": 1000,\n" +
                "                            \"atOrAboveChildCount\": 1000,\n" +
                "                            \"children\": []\n" +
                "                        }\n" +
                "                    ]\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ],\n" +
                "    \"snapshots\": null\n" +
                "}";
        // 准备请求参数
        GetSchoolReadinessMeasureResponse response = JsonUtil.fromJson(requestJson, GetSchoolReadinessMeasureResponse.class);
        Map<String, List<String>> ageGroupNameMap = new HashMap<>();
        ageGroupNameMap.put("9 to 36 months", Arrays.asList("9 to 18 months", "19 to 36 months"));
        List<String> selectedAgeGroupNames = new ArrayList<>();

        // 调用被测试方法
        reportService.processMergedAgeGroupData(response, ageGroupNameMap, selectedAgeGroupNames);

        // 验证结果
        assertEquals(2, response.getAgeGroups().size());
        assertEquals("9 to 36 months", response.getAgeGroups().get(0).getAgeGroupName());
        assertEquals(2, (long) response.getAgeGroups().get(0).getPeriods().get(0).getChildCount());

        // 准备请求参数
        response = JsonUtil.fromJson(requestJson, GetSchoolReadinessMeasureResponse.class);
        selectedAgeGroupNames.add("9 to 18 months");

        // 调用被测试方法
        reportService.processMergedAgeGroupData(response, ageGroupNameMap, selectedAgeGroupNames);

        // 验证结果
        assertEquals(3, response.getAgeGroups().size());
        assertEquals("9 to 18 months", response.getAgeGroups().get(0).getAgeGroupName());
        assertEquals("9 to 36 months", response.getAgeGroups().get(1).getAgeGroupName());
        assertEquals(2, (long) response.getAgeGroups().get(1).getPeriods().get(0).getChildCount());

        // 准备请求参数
        response = JsonUtil.fromJson(requestJson, GetSchoolReadinessMeasureResponse.class);
        ageGroupNameMap.put("3 to 5 years old", Collections.singletonList("3 years old"));
        selectedAgeGroupNames.add("3 years old");

        // 调用被测试方法
        reportService.processMergedAgeGroupData(response, ageGroupNameMap, selectedAgeGroupNames);

        // 验证结果
        assertEquals(4, response.getAgeGroups().size());
        assertEquals("3 to 5 years old", response.getAgeGroups().get(3).getAgeGroupName());
    }
}

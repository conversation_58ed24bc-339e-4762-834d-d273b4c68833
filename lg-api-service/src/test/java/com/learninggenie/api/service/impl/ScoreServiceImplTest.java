package com.learninggenie.api.service.impl;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.toolkit.ChainWrappers;
import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.model.BatchCheckRequest;
import com.learninggenie.api.model.GetStateCountiesResponse;
import com.learninggenie.api.model.agency.FrameworkRequest;
import com.learninggenie.api.model.drdp.AgencyNameErrorModel;
import com.learninggenie.api.model.drdp.UploadFailedResponse;
import com.learninggenie.api.model.drdp.VerifyDrdpOnlineAgencyAliasRequest;
import com.learninggenie.api.model.drdp.VerifyDrdpOnlineAgencyAliasResponse;
import com.learninggenie.api.model.drdp.VerifyRatingViewRequest;
import com.learninggenie.api.model.drdp.VerifyRatingViewResponse;
import com.learninggenie.common.data.model.score.BatchLockResponse;
import com.learninggenie.common.data.model.score.CheckedChild;
import com.learninggenie.common.data.model.score.CheckedGroup;
import com.learninggenie.common.data.model.score.LockJobArgs;
import com.learninggenie.common.data.model.score.LockJobResult;
import com.learninggenie.api.provider.RemoteProvider;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.service.CenterService;
import com.learninggenie.api.service.PortfolioService;
import com.learninggenie.common.cache.CacheModel;
import com.learninggenie.common.cache.CacheService;
import com.learninggenie.common.constant.CacheKey;
import com.learninggenie.common.data.dao.AgencyDao;
import com.learninggenie.common.data.dao.CenterDao;
import com.learninggenie.common.data.dao.DashboardDao;
import com.learninggenie.common.data.dao.DomainDao;
import com.learninggenie.common.data.dao.GroupDao;
import com.learninggenie.common.data.dao.JobDao;
import com.learninggenie.common.data.dao.MetaDao;
import com.learninggenie.common.data.dao.PortfolioDao;
import com.learninggenie.common.data.dao.RecordDao;
import com.learninggenie.common.data.dao.ScoreDao;
import com.learninggenie.common.data.dao.StudentDao;
import com.learninggenie.common.data.dao.drdp.UploadErrorRecordDao;
import com.learninggenie.common.data.dao.enrollment.impl.EnrollmentSnapshotDaoImpl;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.entity.AgencyEntity;
import com.learninggenie.common.data.entity.AgencyMetaDataEntity;
import com.learninggenie.common.data.entity.EnrollmentEntity;
import com.learninggenie.common.data.entity.GroupEntity;
import com.learninggenie.common.data.entity.GroupMetaDataEntity;
import com.learninggenie.common.data.entity.RatingPeriodEntity;
import com.learninggenie.common.data.entity.StudentAttrEntity;
import com.learninggenie.common.data.entity.enrollment.SnapshotEntity;
import com.learninggenie.common.data.enums.AgencyMetaKey;
import com.learninggenie.common.data.enums.AppMetaKey;
import com.learninggenie.common.data.enums.CenterMetaKey;
import com.learninggenie.common.data.enums.DrdpSettingKey;
import com.learninggenie.common.data.enums.DrdpSettingValue;
import com.learninggenie.common.data.enums.JobType;
import com.learninggenie.common.data.enums.StatusType;
import com.learninggenie.common.data.enums.UserRole;
import com.learninggenie.common.data.enums.drdp.ErrorType;
import com.learninggenie.common.data.enums.drdp.Role;
import com.learninggenie.common.data.mapper.dynamo.AgencyMetadataMapper;
import com.learninggenie.common.data.mapper.mybatisplus.EnrollmentSnapshotMapper;
import com.learninggenie.common.data.model.AgencyModel;
import com.learninggenie.common.data.model.CenterEntity;
import com.learninggenie.common.data.model.CenterModel;
import com.learninggenie.common.data.model.DomainLevelsEntity;
import com.learninggenie.common.data.model.EnrollmentModel;
import com.learninggenie.common.data.model.GroupEntry;
import com.learninggenie.common.data.model.JobEntity;
import com.learninggenie.common.data.model.LevelEntity;
import com.learninggenie.common.data.model.ScoreExample;
import com.learninggenie.common.data.model.ScoreTemplateEntity;
import com.learninggenie.common.data.model.StudentScoreEntity;
import com.learninggenie.common.data.model.StudentScoreModel;
import com.learninggenie.common.data.model.StudentSnapshotEntity;
import com.learninggenie.common.data.model.UserEntity;
import com.learninggenie.common.data.model.UserModel;
import com.learninggenie.common.data.model.drdp.error.BatchScoringError;
import com.learninggenie.common.data.model.drdp.error.ChildError;
import com.learninggenie.common.data.model.drdp.error.ErrorModel;
import com.learninggenie.common.data.model.drdp.setting.CenterSetting;
import com.learninggenie.common.data.model.drdp.setting.CenterSettingData;
import com.learninggenie.common.data.model.drdp.setting.DRDPSetting;
import com.learninggenie.common.data.model.drdp.setting.FrameworkType;
import com.learninggenie.common.data.model.drdp2.AddDrdpUserRoleResponse;
import com.learninggenie.common.data.model.drdp2.CreateDrdpAgencyUserModel;
import com.learninggenie.common.data.model.drdp2.CreateDrdpClassModel;
import com.learninggenie.common.data.model.drdp2.CreateDrdpClassResponse;
import com.learninggenie.common.data.model.drdp2.CreateDrdpClassroomModel;
import com.learninggenie.common.data.model.drdp2.CreateDrdpClassroomResponse;
import com.learninggenie.common.data.model.drdp2.CreateDrdpSiteModel;
import com.learninggenie.common.data.model.drdp2.CreateDrdpSiteResponse;
import com.learninggenie.common.data.model.drdp2.CreateDrdpUserResponse;
import com.learninggenie.common.data.model.drdp2.CreateOrUpdateDrdpSiteModel;
import com.learninggenie.common.data.model.drdp2.DrdpAgencyModel;
import com.learninggenie.common.data.model.drdp2.DrdpBatchScoreAgencyModel;
import com.learninggenie.common.data.model.drdp2.DrdpBatchScoringModel;
import com.learninggenie.common.data.model.drdp2.DrdpBatchScoringResponse;
import com.learninggenie.common.data.model.drdp2.DrdpClassModel;
import com.learninggenie.common.data.model.drdp2.DrdpClassroomModel;
import com.learninggenie.common.data.model.drdp2.DrdpCountyModel;
import com.learninggenie.common.data.model.drdp2.DrdpRatingPeriodModel;
import com.learninggenie.common.data.model.drdp2.DrdpRoleModel;
import com.learninggenie.common.data.model.drdp2.DrdpSiteModel;
import com.learninggenie.common.data.model.drdp2.DrdpStandardRatingPeriodModel;
import com.learninggenie.common.data.model.drdp2.DrdpStateModel;
import com.learninggenie.common.data.model.drdp2.DrdpTermModel;
import com.learninggenie.common.data.model.drdp2.DrdpUserModel;
import com.learninggenie.common.data.model.drdp2.GetAgencySitesResponse;
import com.learninggenie.common.data.model.drdp2.GetAgencyTermsResponse;
import com.learninggenie.common.data.model.drdp2.GetAllAgencyResponse;
import com.learninggenie.common.data.model.drdp2.GetClassroomClassesResponse;
import com.learninggenie.common.data.model.drdp2.GetDrdpCountiesResponse;
import com.learninggenie.common.data.model.drdp2.GetDrdpStatesResponse;
import com.learninggenie.common.data.model.drdp2.GetDrdpUsersResponse;
import com.learninggenie.common.data.model.drdp2.GetSiteClassroomsResponse;
import com.learninggenie.common.data.model.drdp2.UpdateDrdpClassModel;
import com.learninggenie.common.data.model.drdp2.UpdateDrdpClassResponse;
import com.learninggenie.common.data.model.drdp2.UpdateDrdpUserModel;
import com.learninggenie.common.data.model.drdp2.UpdateDrdpUserResponse;
import com.learninggenie.common.data.model.record.UploadDRDPRecord;
import com.learninggenie.common.data.model.sftp.HasScoreStudent;
import com.learninggenie.common.data.repository.EnrollmentRepository;
import com.learninggenie.common.filesystem.FileSystem;
import com.learninggenie.common.report.AnalysisService;
import com.learninggenie.common.report.LGSnapshot;
import com.learninggenie.common.score.RatingService;
import com.learninggenie.common.sharding.ShardingProvider;
import com.learninggenie.common.sync.DrdpV2Service;
import com.learninggenie.common.utils.JsonUtil;
import com.learninggenie.common.utils.PeriodUtil;
import com.learninggenie.common.utils.TimeUtil;
import com.learninggenie.common.utils.drdp.DRDPApiUtil;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;
import org.springframework.http.HttpStatus;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;


@RunWith(MockitoJUnitRunner.class)
public class ScoreServiceImplTest {

    private static final String BASE_MAPPER = "baseMapper";

    @Mock
    private DashboardDao dashboardDao;

    @Mock
    private ShardingProvider shardingProvider;
    @Mock
    private ScoreDao scoreDao;
    @Mock
    private EnrollmentRepository enrollmentRepository;
    @Mock
    private GroupDao groupDao;
    @Mock
    private PortfolioDao portfolioDao;
    @Mock
    private JobDao jobDao;
    @Mock
    private AgencyDao agencyDao;
    @Mock
    private CenterDao centerDao;
    @Mock
    private UserDaoImpl userDao;
    @Mock
    private DomainDao domainDao;
    @Mock
    private StudentDao studentDao;
    @Mock
    private PortfolioService portfolioService;
    @Mock
    private AnalysisService analysisService;
    @Mock
    private CommonServiceImpl commonService;
    @Mock
    private UserProvider userProvider;
    @Mock
    private RatingService ratingService;
    @Mock
    private RecordDao recordDao;
    @Mock
    private DrdpV2Service drdpService;
    @Mock
    private CenterService centerService;
    @Mock
    private MetaDao metaDao;
    @Mock
    private FileSystem fileSystem;
    @InjectMocks
    private ScoreServiceImpl scoreService;

    @Mock
    private RemoteProvider remoteProvider;

    @Mock
    private CacheService cacheService;

    @Mock
    private EnrollmentSnapshotDaoImpl enrollmentSnapshotDao;

    @Mock
    private UploadErrorRecordDao uploadErrorRecordDao;

    @Mock
    private AgencyMetadataMapper agencyMetadataMapper;
    
    @Mock
    private EnrollmentSnapshotMapper enrollmentSnapshotMapper;

    private static MockedStatic<ChainWrappers> chainWrappersMockedStatic;

    @BeforeClass
    public static void beforeClass() {
        chainWrappersMockedStatic = mockStatic(ChainWrappers.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), SnapshotEntity.class);
    }

    /**
     * 关闭 chainWrappersMockedStatic.
     */
    @AfterClass
    public static void afterClass() {
        if (chainWrappersMockedStatic != null) {
            chainWrappersMockedStatic.close();
        }
    }

    /**
     * 测试获取学生的评分记录，如果学生没找到，应抛出异常
     * 秦浩然 创建于2016/02/22
     */
    @Test(expected = BusinessException.class)
    public void testGetByStudentIdWhenStudentNotFound() {
        when(enrollmentRepository.findById("123")).thenReturn(Optional.ofNullable(null));
        scoreService.getByStudentId(("123"));
    }

    /**
     * 测试获取学生的评分记录，如果学生没有班级，应抛出异常
     * 秦浩然 创建于2016/02/22
     */
    @Test(expected = BusinessException.class)
    public void testGetByStudentIdWhenGroupIsNull() {
        EnrollmentEntity student = new EnrollmentEntity();
        when(enrollmentRepository.findById("123")).thenReturn(Optional.ofNullable(student));
        scoreService.getByStudentId(("123"));
    }

    /**
     * 测试获取学生的评分记录
     * 秦浩然 创建于2016/02/22
     */
    @Ignore
    @Test
    public void testGetByStudentId() {
        EnrollmentEntity student = new EnrollmentEntity();
        GroupEntity group = new GroupEntity();
        student.setGroup(group);
        when(enrollmentRepository.findById("1")).thenReturn(Optional.ofNullable(student));

        List<StudentScoreEntity> scores = new ArrayList<>();
        StudentScoreEntity score1 = new StudentScoreEntity();
        score1.setId("1");
        score1.setLevelId("1");
        scores.add(score1);
        StudentScoreEntity score2 = new StudentScoreEntity();
        score2.setId("2");
        score2.setLevelId("2");
        scores.add(score2);
        when(scoreDao.get("1")).thenReturn(scores);

        List<StudentScoreEntity> result = scoreService.getByStudentId("1");
        assertEquals(scores, result);
        assertEquals(score1.getLevelId(), "1");
        assertEquals(score2.getLevelId(), "2");
    }

    /**
     * 获取班级的评分记录
     * 秦浩然 创建于2016/02/22
     */
    @Test
    public void testGetStudentScoreStates() {
        List<StudentScoreEntity> scores = new ArrayList<>();
        StudentScoreEntity score1 = new StudentScoreEntity();
        score1.setId("1");
        score1.setLevelId("1");
        scores.add(score1);
        StudentScoreEntity score2 = new StudentScoreEntity();
        score2.setId("2");
        score2.setLevelId("2");
        scores.add(score2);
        when(scoreDao.getByGroup("1")).thenReturn(scores);

        List<StudentScoreEntity> result = scoreService.getStudentScoreStates("1");
        assertEquals(scores, result);
        assertEquals(score1.getLevelId(), "1");
        assertEquals(score2.getLevelId(), "2");
    }

    /**
     * 获取学生成绩
     * <p>
     * 传入班级Id
     * <p>
     * 提取班级学生信息，根据评分模版计算学生成绩，
     * 并返回
     */
    @Ignore
    @Test
    public void testGetStudentScoreStates1() throws Exception {
        List<StudentScoreEntity> scoreRecords = new ArrayList<>();
        StudentScoreEntity studentScoreEntity = new StudentScoreEntity();
        studentScoreEntity.setId("123");
        studentScoreEntity.setDomainId("456");
        scoreRecords.add(studentScoreEntity);
        scoreRecords.add(studentScoreEntity);
        //提取班级学生成绩
        Mockito.when(scoreDao.getByGroup(anyString())).thenReturn(scoreRecords);
        GroupEntry group = new GroupEntry();
        //获取班级信息
        Mockito.when(groupDao.getGroup(anyString())).thenReturn(group);
        ScoreTemplateEntity scoreTemplateEntry = new ScoreTemplateEntity();
        //提取评分模版
        Mockito.when(portfolioDao.loadScoreTemplate(anyString())).thenReturn(scoreTemplateEntry);
        LevelEntity levelEntity = new LevelEntity();
        levelEntity.setId("1111");
        levelEntity.setValue("1222");
        //获取评分level
        Mockito.when(ratingService.getBestScore(Mockito.anyList(), Mockito.anyList())).thenReturn(levelEntity);
        //获取评分level
        Mockito.when(commonService.getLevel(anyString(), Mockito.anyList())).thenReturn(levelEntity);
        ////执行有评分模版的scoreService.getStudentScoreStates//
        List<StudentScoreEntity> studentScoreEntityList = scoreService.getStudentScoreStates("123");
        Assert.assertTrue(studentScoreEntityList != null);
        Assert.assertEquals("123", studentScoreEntityList.get(0).getId());
        Mockito.when(portfolioDao.loadScoreTemplate(anyString())).thenReturn(null);
        Mockito.when(ratingService.getBestScore(Mockito.anyList(), Mockito.anyList())).thenReturn(null);
        ////执行没有评分模版的scoreService.getStudentScoreStates//
        List<StudentScoreEntity> studentScoreEntityList1 = scoreService.getStudentScoreStates("123");
        Assert.assertTrue(studentScoreEntityList1 != null);
        Assert.assertEquals("123", studentScoreEntityList1.get(0).getId());
        Mockito.when(portfolioDao.loadScoreTemplate(anyString())).thenReturn(scoreTemplateEntry);
        levelEntity = new LevelEntity();
        levelEntity.setValue("e");
        Mockito.when(commonService.getLevel(anyString(), Mockito.anyList())).thenReturn(levelEntity);
        ////执行 levelEntity的value为e的scoreService.getStudentScoreStates//
        List<StudentScoreEntity> studentScoreEntityList2 = scoreService.getStudentScoreStates("123");
        Assert.assertTrue(studentScoreEntityList2 != null);
        Assert.assertEquals("123", studentScoreEntityList2.get(0).getId());
    }

    /**
     * 获取学生评分
     * <p>
     * 传入学生id
     * 获取学生评分
     */
    @Ignore
    @Test
    public void testGetByStudentId1() throws Exception {
        EnrollmentEntity student = new EnrollmentEntity();
        student.setGroup(new GroupEntity());
        Mockito.when(enrollmentRepository.findById(anyString()).orElse(null)).thenReturn(student);
        List<StudentScoreEntity> scoreRecords = new ArrayList<>();
        StudentScoreEntity studentScoreEntity = new StudentScoreEntity();
        studentScoreEntity.setId("123");
        scoreRecords.add(studentScoreEntity);
        GroupEntry group = new GroupEntry();
        group.setId("123");
        Mockito.when(groupDao.getGroup(anyString())).thenReturn(group);
        ScoreTemplateEntity scoreTemplateEntry = new ScoreTemplateEntity();
        Mockito.when(portfolioDao.loadScoreTemplate(anyString())).thenReturn(scoreTemplateEntry);
        LevelEntity levelEntity = new LevelEntity();
        levelEntity.setValue("e");
        Mockito.when(ratingService.getBestScore(Mockito.anyList(), Mockito.anyList())).thenReturn(levelEntity);
        Mockito.when(commonService.getLevel(anyString(), Mockito.anyList())).thenReturn(levelEntity);
        Mockito.when(scoreDao.get(anyString())).thenReturn(scoreRecords);
        List<StudentScoreEntity> studentScoreEntityList = scoreService.getByStudentId("");
        Assert.assertTrue(studentScoreEntityList != null);
        Assert.assertEquals("123", studentScoreEntityList.get(0).getId());
        Mockito.when(enrollmentRepository.findById(anyString()).orElse(null)).thenReturn(null);
    }

    /**
     * 获取学生score
     */
    @Test
    public void testGet() throws Exception {
        List<StudentScoreEntity> studentScoreEntity = new ArrayList<>();
        StudentScoreEntity studentScoreEntity1 = new StudentScoreEntity();
        studentScoreEntity1.setId("123");
        studentScoreEntity.add(studentScoreEntity1);
        Mockito.when(scoreDao.get(anyString(), anyString())).thenReturn(studentScoreEntity);
        List<StudentScoreEntity> studentScoreEntityList = scoreService.get("", "");
        Assert.assertTrue(studentScoreEntityList != null);
        Assert.assertEquals("123", studentScoreEntityList.get(0).getId());
    }

    private class SpiceAnswer implements Answer {
        @Override
        public String answer(InvocationOnMock invocation) throws Throwable {
            String arg = "123";
            return arg;
        }
    }

    /**
     * 存储学生score
     * 正常保存
     * zjj 2016.3.4
     */
    @Test
    @Ignore
    public void testBatchSave() throws Exception {
        List<StudentScoreEntity> records = new ArrayList<>();
        StudentScoreEntity scoreEntity = new StudentScoreEntity();
        scoreEntity.setId("score1");
        scoreEntity.setNoteId("noteid1");
        scoreEntity.setLevelId("levelid1");
        scoreEntity.setDomainId("domainid1");
        records.add(scoreEntity);

        when(userProvider.getCurrentUserId()).thenReturn("u001");
        Mockito.doAnswer(new SpiceAnswer()).when(scoreDao).batchSave(eq(records), eq("u001"), anyString());

        //存储学生成绩
        scoreService.batchSave(records);
        //确保存储
        verify(scoreDao, times(1)).batchSave(eq(records), eq("u001"), anyString());
    }

    @Test
    public void testBatchLockAndUpload() {
        // 任务 ID
        final String jobId = UUID.randomUUID().toString();
        // 锁定周期
        final String lockAlias = "2021-2022 Fall";
        // 用户 ID
        final String userId = UUID.randomUUID().toString();
        // 机构 ID
        final String agencyId = UUID.randomUUID().toString();
        // 学校 ID
        final String centerId = UUID.randomUUID().toString();
        // 班级 ID
        final String groupId = UUID.randomUUID().toString();
        // 框架 ID
        final String frameworkId = "domain1";
        // 锁定参数
        final LockJobArgs lockJobArgs = new LockJobArgs();
        lockJobArgs.setAgencyId(agencyId);
        lockJobArgs.setPeriodAlias(lockAlias);
        lockJobArgs.setUserId(userId);
        lockJobArgs.setUploadLockedData(true);
        // 检查结果
        final LockJobResult lockJobResult = new LockJobResult();
        final List<CheckedGroup> checkedGroups = new ArrayList<>();
        // 班级信息
        final CheckedGroup checkedGroup = new CheckedGroup();
        checkedGroup.setId(groupId);
        checkedGroup.setPeriodAlias(lockAlias);
        checkedGroup.setFrameworkId(frameworkId);
        checkedGroups.add(checkedGroup);
        lockJobResult.setCheckedGroups(checkedGroups);

        // 学生信息
        final List<CheckedChild> checkedChildren = new ArrayList<>();
        checkedGroup.setCheckedChildren(checkedChildren);
        // 评分、属性都完成
        final String child1Id = "child-1";
        final CheckedChild checkedChild1 = new CheckedChild();
        checkedChild1.setId(child1Id);
        checkedChildren.add(checkedChild1);

        // 任务信息
        final JobEntity job = new JobEntity();
        job.setArgs(JsonUtil.toJson(lockJobArgs));
        job.setResult(JsonUtil.toJson(lockJobResult));
        when(jobDao.getJobById(jobId)).thenReturn(job);

        // 功能开关为开
        when(userProvider.getAgencyOpenDefaultOpen(agencyId, AgencyMetaKey.DRDP_UPLOAD.toString())).thenReturn(true);

        // 上传 DRDP 设置为打开
        final AgencyMetaDataEntity uploadMeta = new AgencyMetaDataEntity();
        uploadMeta.setMetaValue(DrdpSettingValue.DRDP_UPLOAD_OPEN.toString());
        when(agencyDao.getMeta(agencyId, DrdpSettingKey.UPLOAD_DRDP_SETTING.toString())).thenReturn(uploadMeta);

        // 框架设置
        final List<FrameworkType> frameworkTypes = new ArrayList<>();
        frameworkTypes.add(new FrameworkType("IT", "ITC"));
        frameworkTypes.add(new FrameworkType("PS", "PSC"));
        frameworkTypes.add(new FrameworkType("K", "KC"));
        frameworkTypes.add(new FrameworkType("SA", "SAC"));

        // 上传设置信息
        final List<DRDPSetting> drdpSettings = new ArrayList<>();
        final DRDPSetting drdpSetting = new DRDPSetting();
        drdpSetting.setId(UUID.randomUUID().toString());
        drdpSetting.setComplete(true);
        drdpSetting.setState("CA");
        // 上传学校、班级设置
        final List<CenterSetting> centerSettings = new ArrayList<>();
        final CenterSetting centerSetting = new CenterSetting();
        centerSetting.getGroupIds().add(groupId);
        centerSettings.add(centerSetting);
        drdpSetting.setCenterSettings(centerSettings);
        final CenterSettingData centerSettingData = new CenterSettingData();
        centerSettingData.setCenterSettings(centerSettings);
        centerSettingData.setFrameworkTypes(frameworkTypes);
        drdpSetting.setCenterSetting(JsonUtil.toJson(centerSettingData));
        drdpSettings.add(drdpSetting);
//        when(agencyDao.getDRDPSettingByAgency(agencyId)).thenReturn(drdpSettings);

        Map<String, DRDPSetting> drdpSettingMap = new HashMap<>();
        drdpSettingMap.put(groupId.toUpperCase(), drdpSetting);
        when(analysisService.getGroupDRDPSettingMap(agencyId)).thenReturn(drdpSettingMap);

        // 时区
        final String timezone = "America/Los_Angeles";
        when(centerDao.getFirstCenterTimeZoneByAgencyId(agencyId)).thenReturn(timezone);

        // 机构信息
        final AgencyEntity agency = new AgencyEntity();
        agency.setId(agencyId);
        agency.setName("agency");
        agency.setState("CA");
        when(agencyDao.getById(agencyId)).thenReturn(agency);

        // 管理员
        final UserModel owner = new UserModel();
        owner.setEmail("<EMAIL>");
        owner.setRole(UserRole.AGENCY_OWNER.toString());
        owner.setDisplayName("micro owner");
        when(userDao.getAgencyAdminsByAgencyId(agencyId)).thenReturn(Collections.singletonList(owner));

        // 学校
        final CenterEntity center = new CenterEntity();
        center.setId(centerId);
        center.setName("center");

        // 班级
        final com.learninggenie.common.data.model.GroupEntity group = new com.learninggenie.common.data.model.GroupEntity();
        group.setId(groupId);
        group.setName("group");
        group.setCenter(center);
        when(groupDao.getGroupWithCenter(groupId)).thenReturn(group);

        // 框架
        when(ratingService.isPSFramework(frameworkId)).thenReturn(true);

        // 学校无别名
        when(centerDao.getMeta(centerId, CenterMetaKey.DRDP_CENTER_ALIAS.toString() + "_" + drdpSetting.getId().toUpperCase())).thenReturn(null);

        // 小孩
        for (CheckedChild checkedChild : checkedChildren) {
            final String childId = checkedChild.getId();
            // 基本信息
            final EnrollmentModel child = new EnrollmentModel();
            child.setFirstName("first");
            child.setLastName("last");
            child.setBirthDate("2020-03-15");
            child.setEnrollmentDate("01/01/2022");
            child.setGender("Male");
            when(studentDao.getEnrollmentWithDelete(childId)).thenReturn(child);

            // 属性
            List<StudentAttrEntity> attrs = new ArrayList<>();
            StudentAttrEntity attr = new StudentAttrEntity();
            attr.setAttrId(UUID.randomUUID().toString());
            attr.setAttrName("Teacher");
            attr.setAttrValue("<EMAIL>");
            attr.setAttrTypeValue("attrType");
            attr.setEnrollmentId("enrollmentId");

            attrs.add(attr);

            when(studentDao.getAttrsByChildIds(Collections.singletonList(childId))).thenReturn(attrs);

            // 评分
            when(scoreDao.gerChildScore(eq(childId), anyString(), anyString())).thenReturn(new ArrayList<>());

            // 快照
            final StudentSnapshotEntity studentSnapshotEntity = new StudentSnapshotEntity();
            studentSnapshotEntity.setId(UUID.randomUUID().toString());
            studentSnapshotEntity.setFrameworkId(frameworkId);
            when(studentDao.getSnapshotByChildIdAndLockGroupIdAndAlias(childId, groupId, lockAlias)).thenReturn(studentSnapshotEntity);

            // portfolioService.getScoreTemplate(frameworkId) 生成对应的 ScoreTemplateEntity
            final ScoreTemplateEntity scoreTemplate = new ScoreTemplateEntity();
            scoreTemplate.setPortfolioId(frameworkId);

            // LevelsJson attribute
            final List<LevelEntity> levelsList = new ArrayList<>();
            final LevelEntity level1 = new LevelEntity();
            level1.setId("level-1");
            level1.setName("Level 1");
            level1.setType("type-1");
            level1.setSortIndex("1");
            level1.setValue("value-1");
            level1.setFinalValue("final-value-1");
            level1.setFinalName("Final Name 1");
            level1.setTip("Tip 1");
            level1.setHidden(false);
            level1.setRated(false);
            level1.setLevelIndex(1);
            final ScoreExample scoreExample = new ScoreExample();
            scoreExample.setExampleName("Example 1");
            scoreExample.setContent(Arrays.asList("Content 1", "Content 2"));
            scoreExample.setColumnSize(2);
            level1.setScoreExamples(Collections.singletonList(scoreExample));
            levelsList.add(level1);
            scoreTemplate.setLevelsJson(new Gson().toJson(levelsList));

            // DomainLevelsJson attribute
            final List<DomainLevelsEntity> domainLevelsList = new ArrayList<>();
            final DomainLevelsEntity domain1 = new DomainLevelsEntity();
            final String domainId = "domain-1";
            domain1.setDomainId(domainId);
            domain1.setMeasure("measure-1");
            domain1.setMeasureName("Measure 1");
            domain1.setLevels(Collections.singletonList(level1));
            final Map<String, LevelEntity> levelMap = new HashMap<>();
            levelMap.put("level-1", level1);
            domain1.setLevelMap(levelMap);
            domainLevelsList.add(domain1);
            scoreTemplate.setDomainLevelsJson(new Gson().toJson(domainLevelsList));
            when(portfolioService.getScoreTemplate(any())).thenReturn(scoreTemplate);

            // 增加评分周期
            final List<RatingPeriodEntity> ratingPeriodEntityList = new ArrayList<>();

            // 创建第一个 RatingPeriodEntity 对象
            final RatingPeriodEntity ratingPeriodEntity1 = new RatingPeriodEntity();
            ratingPeriodEntity1.setId("1");
            ratingPeriodEntity1.setAlias("RP1");
            ratingPeriodEntity1.setDisplayAlias("Rating Period 1");
            ratingPeriodEntity1.setFromAtLocal(new Date());
            ratingPeriodEntity1.setToAtLocal(new Date(System.currentTimeMillis() + 86400000)); // 1 day later
            ratingPeriodEntity1.setCreateAtUtc(new Date());
            ratingPeriodEntity1.setUpdateAtUtc(new Date());
            ratingPeriodEntity1.setEnrollmentId("enrollment1");
            ratingPeriodEntity1.setActived(true);
            ratingPeriodEntity1.setCompletedAtUtc(new Date());
            ratingPeriodEntity1.setCompletedAtLocal(new Date());
            ratingPeriodEntity1.setRealCompletedAtLocal(new Date());
            ratingPeriodEntity1.setSortIndex(1);
            ratingPeriodEntity1.setFromAtLocalString("2022-01-01 00:00:00");
            ratingPeriodEntity1.setToAtLocalString("2022-01-31 23:59:59");
            ratingPeriodEntity1.setCompletionDate("2022-01-31");
            ratingPeriodEntity1.setNotEdit(false);
            ratingPeriodEntity1.setNew(true);
            ratingPeriodEntity1.setDomainId("domain1");
            ratingPeriodEntity1.setFrameWorkName("framework1");
            ratingPeriodEntity1.setMinDateString("2022-01-01");
            ratingPeriodEntity1.setMaxDateString("2022-01-31");
            ratingPeriodEntity1.setLocked(false);
            ratingPeriodEntity1.setLockPeriod(false);
            ratingPeriodEntity1.setLockUserId("user1");
            ratingPeriodEntity1.setLockAtLocal(new Date());
            ratingPeriodEntity1.setAgencyId("agency1");
            ratingPeriodEntity1.setChangeAlias(false);
            ratingPeriodEntity1.setAfterAlias(false);
            ratingPeriodEntity1.setPreAlias(false);
            ratingPeriodEntity1.setPreAliasStr("Previous Alias");
            ratingPeriodEntity1.setEntrysDate("2022-01-01");
            ratingPeriodEntity1.setHasRatingFrom("2022-01-01");
            ratingPeriodEntity1.setHasRatingTo("2022-01-31");
            ratingPeriodEntityList.add(ratingPeriodEntity1);

            // 创建第二个 RatingPeriodEntity 对象
            final RatingPeriodEntity ratingPeriodEntity2 = new RatingPeriodEntity();
            ratingPeriodEntity2.setId("2");
            ratingPeriodEntity2.setAlias("RP2");
            ratingPeriodEntity2.setDisplayAlias("Rating Period 2");
            ratingPeriodEntity2.setFromAtLocal(new Date(System.currentTimeMillis() + 86400000)); // 1 day later
            ratingPeriodEntity2.setToAtLocal(new Date(System.currentTimeMillis() + 172800000)); // 2 days later
            ratingPeriodEntity2.setCreateAtUtc(new Date());
            ratingPeriodEntity2.setUpdateAtUtc(new Date());
            ratingPeriodEntity2.setEnrollmentId("enrollment2");
            ratingPeriodEntity2.setActived(true);
            ratingPeriodEntity2.setCompletedAtUtc(new Date());
            ratingPeriodEntity2.setCompletedAtLocal(new Date());
            ratingPeriodEntity2.setRealCompletedAtLocal(new Date());
            ratingPeriodEntity2.setSortIndex(2);
            ratingPeriodEntity2.setFromAtLocalString("2022-02-01 00:00:00");
            ratingPeriodEntity2.setToAtLocalString("2022-02-02 23:59:59");
            ratingPeriodEntity2.setCompletionDate("2022-02-02");
            ratingPeriodEntity2.setNotEdit(false);
            ratingPeriodEntity2.setNew(true);
            ratingPeriodEntity2.setDomainId("domain2");
            ratingPeriodEntity2.setFrameWorkName("framework2");
            ratingPeriodEntity2.setMinDateString("2022-02-01");
            ratingPeriodEntity2.setMaxDateString("2022-02-02");
            ratingPeriodEntity2.setLocked(false);
            ratingPeriodEntity2.setLockPeriod(false);
            ratingPeriodEntity2.setLockUserId("user2");
            ratingPeriodEntity2.setLockAtLocal(new Date());
            ratingPeriodEntity2.setAgencyId("agency2");
            ratingPeriodEntity2.setChangeAlias(false);
            ratingPeriodEntity2.setAfterAlias(false);
            ratingPeriodEntity2.setPreAlias(false);
            ratingPeriodEntity2.setPreAliasStr("Previous Alias 2");
            ratingPeriodEntity2.setEntrysDate("2022-02-01");
            ratingPeriodEntity2.setHasRatingFrom("2022-02-01");
            ratingPeriodEntity2.setHasRatingTo("2022-02-02");
            ratingPeriodEntityList.add(ratingPeriodEntity2);
            when(studentDao.getAllPeriodsByAlias(childId, lockAlias)).thenReturn(ratingPeriodEntityList);

            // 循环 ratingPeriodEntityList
            for (RatingPeriodEntity ratingPeriodEntity : ratingPeriodEntityList) {
                // 生成 ScoreList
                final List<StudentScoreEntity> studentScoreEntityList = new ArrayList<>();

                // 创建第一个StudentScoreEntity对象, domainId 符合并且有评分
                final StudentScoreEntity studentScoreEntity1 = new StudentScoreEntity();
                studentScoreEntity1.setId("1");
                studentScoreEntity1.setStudentId(childId);
                studentScoreEntity1.setNoteId("1001");
                studentScoreEntity1.setDomainId(domainId);
                studentScoreEntity1.setOldDomainId("20");
                studentScoreEntity1.setLevelId("1");
                studentScoreEntity1.setComment("good job");
                studentScoreEntity1.setCreateByUserId("user1");
                studentScoreEntity1.setCreateAtUtc("2021-11-01 10:00:00");
                studentScoreEntity1.setCreateAtUtcDate(TimeUtil.parse("2021-11-01 10:00:00", TimeUtil.dateFormat));
                studentScoreEntity1.setGroupId("group1");
                studentScoreEntity1.setCreateAtLocal("2021-11-01 18:00:00");
                studentScoreEntity1.setCreateAtLocalDate(TimeUtil.parse("2021-11-01 18:00:00", TimeUtil.dateFormat));
                studentScoreEntityList.add(studentScoreEntity1);

                // 创建第二个StudentScoreEntity对象 domainId 符合但是没有评分
                final StudentScoreEntity studentScoreEntity2 = new StudentScoreEntity();
                studentScoreEntity2.setId("2");
                studentScoreEntity2.setStudentId(childId);
                studentScoreEntity2.setNoteId("1002");
                studentScoreEntity2.setDomainId(domainId);
                studentScoreEntity2.setOldDomainId("30");
                studentScoreEntity2.setLevelId("");
                studentScoreEntity2.setComment("well done");
                studentScoreEntity2.setCreateByUserId("user2");
                studentScoreEntity2.setCreateAtUtc("2021-11-02 10:00:00");
                studentScoreEntity2.setCreateAtUtcDate(TimeUtil.parse("2021-11-01 10:00:00", TimeUtil.dateFormat));
                studentScoreEntity2.setGroupId("group2");
                studentScoreEntity2.setCreateAtLocal("2021-11-02 18:00:00");
                studentScoreEntity2.setCreateAtLocalDate(TimeUtil.parse("2021-11-01 18:00:00", TimeUtil.dateFormat));
                studentScoreEntityList.add(studentScoreEntity2);

                // 创建第三个StudentScoreEntity对象 domainId 不符合, 补充 ur
                final StudentScoreEntity studentScoreEntity3 = new StudentScoreEntity();
                studentScoreEntity3.setId("3");
                studentScoreEntity3.setStudentId("10003");
                studentScoreEntity3.setNoteId("1003");
                studentScoreEntity3.setDomainId("30");
                studentScoreEntity3.setOldDomainId("40");
                studentScoreEntity3.setLevelId("3");
                studentScoreEntity3.setComment("excellent");
                studentScoreEntity3.setCreateByUserId("user3");
                studentScoreEntity3.setCreateAtUtc("2021-11-03 10:00:00");
                studentScoreEntity3.setCreateAtUtcDate(TimeUtil.parse("2021-11-01 10:00:00", TimeUtil.dateFormat));
                studentScoreEntity3.setGroupId("group3");
                studentScoreEntity3.setCreateAtLocal("2021-11-03 18:00:00");
                studentScoreEntity3.setCreateAtLocalDate(TimeUtil.parse("2021-11-03 18:00:00", TimeUtil.dateFormat));
                studentScoreEntityList.add(studentScoreEntity3);
                when(scoreDao.gerChildScore(childId, TimeUtil.format(ratingPeriodEntity.getFromAtLocal()), TimeUtil.format(ratingPeriodEntity.getToAtLocal())))
                        .thenReturn(studentScoreEntityList);
            }
        }

        // 老师列表
        final List<UserEntity> teachers = new ArrayList<>();
        final UserEntity teacher1 = new UserEntity();
        teacher1.setEmail("<EMAIL>");
        teacher1.setRole(UserRole.TEACHER.toString());
        teacher1.setFirstName("micro");
        teacher1.setLastName("teacher");
        teachers.add(teacher1);
        when(groupDao.getTeacherByGroupId(groupId)).thenReturn(teachers);


        GetAllAgencyResponse getAllAgencyResponse = new GetAllAgencyResponse();
        List<DrdpAgencyModel> drdpAgencyModels = Lists.newArrayList();
        DrdpAgencyModel drdpAgency = new DrdpAgencyModel();
        drdpAgency.setId(1);
        drdpAgency.setAgencyName("agency");

        drdpAgencyModels.add(drdpAgency);
        getAllAgencyResponse.setAgencies(drdpAgencyModels);

        when(drdpService.getAllAgencies()).thenReturn(getAllAgencyResponse);

        GetAgencyTermsResponse agencyTerms = new GetAgencyTermsResponse();
        List<DrdpTermModel> terms = Lists.newArrayList();
        DrdpTermModel drdpTerm1 = new DrdpTermModel();
        drdpTerm1.setId(1);
        drdpTerm1.setName("Fall 2021");
        drdpTerm1.setStartDate("2023-10-01T00:00:00");
        drdpTerm1.setEndDate(TimeUtil.format(TimeUtil.addDays(new Date(), 1), TimeUtil.format25));

        terms.add(drdpTerm1);

        agencyTerms.setTerms(terms);
        when(drdpService.getAgencyTerms(anyString(), anyString())).thenReturn(agencyTerms);

        GetAgencySitesResponse getAgencySiteResponse = new GetAgencySitesResponse();
        List<DrdpSiteModel> drdpSites = Lists.newArrayList();
        DrdpSiteModel drdpSite = new DrdpSiteModel();
        drdpSite.setId(1);
        drdpSite.setAgencyId(1);
        drdpSite.setSiteName("center");
        drdpSites.add(drdpSite);
        getAgencySiteResponse.setSites(drdpSites);
        when(drdpService.getAgencySites(anyString(), eq(null), eq(null))).thenReturn(getAgencySiteResponse);

        GetStateCountiesResponse stateCounties = new GetStateCountiesResponse();
        List<String> countyNames = Lists.newArrayList();
        countyNames.add("Los Angeles");
        stateCounties.setCountyNames(countyNames);

        when(centerService.getStateCounties(anyString())).thenReturn(stateCounties);

        GetDrdpStatesResponse caStateResponse = new GetDrdpStatesResponse();
        List<DrdpStateModel> drdpStates = Lists.newArrayList();
        DrdpStateModel drdpState = new DrdpStateModel();
        drdpState.setId(1);
        drdpState.setStateName("CA");
        drdpStates.add(drdpState);

        caStateResponse.setStates(drdpStates);
        caStateResponse.setStatus(200);

        when(drdpService.getStates(anyString())).thenReturn(caStateResponse);

        GetDrdpUsersResponse drdpServiceUsers = new GetDrdpUsersResponse();
        List<DrdpUserModel> drdpUsers = Lists.newArrayList();
        DrdpUserModel drdpUser = new DrdpUserModel();
        drdpUser.setId(1);
        drdpUser.setEmail("<EMAIL>");
        drdpUser.setRoles(Lists.newArrayList());
        drdpUsers.add(drdpUser);
        drdpServiceUsers.setUsers(drdpUsers);
        drdpServiceUsers.setStatus(200);
        when(drdpService.getUsers(anyString(), anyString())).thenReturn(drdpServiceUsers);

        UpdateDrdpUserResponse updateDrdpUserResponse = new UpdateDrdpUserResponse();
        updateDrdpUserResponse.setId(1);
        updateDrdpUserResponse.setStatus(200);
        when(drdpService.updateUser(anyString(), any(UpdateDrdpUserModel.class))).thenReturn(updateDrdpUserResponse);

        when(drdpService.getUsers(anyString(), anyString())).thenReturn(drdpServiceUsers);

        AddDrdpUserRoleResponse addDrdpUserRoleResponse = new AddDrdpUserRoleResponse();
        addDrdpUserRoleResponse.setId(1);
        addDrdpUserRoleResponse.setStatus(200);
        when(drdpService.addUserRole(anyString(), anyString())).thenReturn(addDrdpUserRoleResponse);

        GetSiteClassroomsResponse classroomModelResponse = new GetSiteClassroomsResponse();
        List<DrdpClassroomModel> drdpClassrooms = Lists.newArrayList();
        DrdpClassroomModel drdpClassroom = new DrdpClassroomModel();
        drdpClassroom.setId(1);
        drdpClassroom.setClassroomName("group");
        drdpClassrooms.add(drdpClassroom);
        classroomModelResponse.setClassrooms(drdpClassrooms);
        when(drdpService.getSiteClassrooms(anyString(), anyString(), anyString())).thenReturn(classroomModelResponse);

        GetClassroomClassesResponse classroomClasses = new GetClassroomClassesResponse();
        List<DrdpClassModel> drdpClasses = Lists.newArrayList();
        DrdpClassModel drdpClass = new DrdpClassModel();
        drdpClass.setId(1);
        drdpClass.setClassroomName("group");
        drdpClass.setTermId(1);
        drdpClasses.add(drdpClass);
        classroomClasses.setClasses(drdpClasses);
        classroomClasses.setStatus(200);
        when(drdpService.getClassroomClasses(anyString(), anyString())).thenReturn(classroomClasses);

        UpdateDrdpClassResponse updateDrdpClassResponse = new UpdateDrdpClassResponse();
        updateDrdpClassResponse.setId(1);
        updateDrdpClassResponse.setStatus(200);
        when(drdpService.updateClass(anyString(), any(UpdateDrdpClassModel.class))).thenReturn(updateDrdpClassResponse);

        // 设置批量评分的返回值
        DrdpBatchScoringResponse drdpBatchScoringResponse = new DrdpBatchScoringResponse();
        drdpBatchScoringResponse.setId(1);
        drdpBatchScoringResponse.setStatus(HttpStatus.OK.value());

        when(drdpService.batchScoring(any(DrdpBatchScoringModel.class), anyBoolean())).thenReturn(drdpBatchScoringResponse);

        // 锁定并上传
        final BatchLockResponse response = scoreService.batchLockAndUpload(jobId);

        // 有老师时不会查询学校下其他老师
        verify(userDao, times(0)).getTeachersByCenterId(centerId);
        // 有老师时不会查询园长
        verify(userDao, times(0)).getSiteAdminsByCenterIds(anyList());
        // 验证 response.getLockedCount();
        // 对Response对象的属性和方法进行断言，验证其返回值是否与预期相同
        assertEquals(0, response.getLockedCount());
        assertEquals(0, response.getOnlyLockedCount());
        assertEquals(0, response.getErrorChildCount());
        assertEquals(0, response.getNeedFixChildCount());
        assertEquals(0, response.getAliasErrorCount());
        assertEquals(0, response.getClassErrorCount());


        // 清空
        Mockito.reset(groupDao, userDao, jobDao);
        when(userDao.getAgencyAdminsByAgencyId(agencyId)).thenReturn(Collections.singletonList(owner));
        // 班级没有老师
        when(groupDao.getTeacherByGroupId(groupId)).thenReturn(new ArrayList<>());

        // 锁定参数
        lockJobArgs.setUploadLockedData(false);

        // 任务信息
        final JobEntity job2 = new JobEntity();
        job2.setArgs(JsonUtil.toJson(lockJobArgs));
        job2.setResult(JsonUtil.toJson(lockJobResult));
        when(jobDao.getJobById(jobId)).thenReturn(job2);

        // 锁定并上传
        scoreService.batchLockAndUpload(jobId);

        // 学校有其他老师时不会查询园长
        verify(userDao, times(0)).getSiteAdminsByCenterIds(anyList());

        // 清空
        Mockito.reset(groupDao, userDao);
        // 学校有园长
        when(userDao.getAgencyAdminsByAgencyId(agencyId)).thenReturn(Collections.singletonList(owner));

        // 锁定并上传
        scoreService.batchLockAndUpload(jobId);

        // 查询机构管理员
        verify(userDao, times(1)).getAgencyAdminsByAgencyId(agencyId);
    }

    @Test
    public void testBatchLockAndUpload_noAlias() {
        // 任务 ID
        final String jobId = UUID.randomUUID().toString();
        // 锁定周期
        final String lockAlias = "";
        // 用户 ID
        final String userId = UUID.randomUUID().toString();
        // 机构 ID
        final String agencyId = UUID.randomUUID().toString();
        // 学校 ID
        final String centerId = UUID.randomUUID().toString();
        // 班级 ID
        final String groupId = UUID.randomUUID().toString();
        // 框架 ID
        final String frameworkId = "domain1";
        // 锁定参数
        final LockJobArgs lockJobArgs = new LockJobArgs();
        lockJobArgs.setAgencyId(agencyId);
        lockJobArgs.setPeriodAlias(lockAlias);
        lockJobArgs.setUserId(userId);
        lockJobArgs.setUploadLockedData(true);
        // 检查结果
        final LockJobResult lockJobResult = new LockJobResult();
        final List<CheckedGroup> checkedGroups = new ArrayList<>();
        // 班级信息
        final CheckedGroup checkedGroup = new CheckedGroup();
        checkedGroup.setId(groupId);
        checkedGroup.setPeriodAlias(lockAlias);
        checkedGroup.setFrameworkId(frameworkId);
        checkedGroups.add(checkedGroup);
        lockJobResult.setCheckedGroups(checkedGroups);

        // 学生信息
        final List<CheckedChild> checkedChildren = new ArrayList<>();
        checkedGroup.setCheckedChildren(checkedChildren);
        // 评分、属性都完成
        final String child1Id = "child-2";
        final CheckedChild checkedChild1 = new CheckedChild();
        checkedChild1.setId(child1Id);
        checkedChildren.add(checkedChild1);

        // 任务信息
        final JobEntity job = new JobEntity();
        job.setArgs(JsonUtil.toJson(lockJobArgs));
        job.setResult(JsonUtil.toJson(lockJobResult));
        when(jobDao.getJobById(jobId)).thenReturn(job);

        // 功能开关为开
        when(userProvider.getAgencyOpenDefaultOpen(agencyId, AgencyMetaKey.DRDP_UPLOAD.toString())).thenReturn(true);

        // 上传 DRDP 设置为打开
        final AgencyMetaDataEntity uploadMeta = new AgencyMetaDataEntity();
        uploadMeta.setMetaValue(DrdpSettingValue.DRDP_UPLOAD_OPEN.toString());
        when(agencyDao.getMeta(agencyId, DrdpSettingKey.UPLOAD_DRDP_SETTING.toString())).thenReturn(uploadMeta);

        // 框架设置
        final List<FrameworkType> frameworkTypes = new ArrayList<>();
        frameworkTypes.add(new FrameworkType("IT", "ITC"));
        frameworkTypes.add(new FrameworkType("PS", "PSC"));
        frameworkTypes.add(new FrameworkType("K", "KC"));
        frameworkTypes.add(new FrameworkType("SA", "SAC"));

        // 上传设置信息
        final List<DRDPSetting> drdpSettings = new ArrayList<>();
        final DRDPSetting drdpSetting = new DRDPSetting();
        drdpSetting.setId(UUID.randomUUID().toString());
        drdpSetting.setComplete(true);
        drdpSetting.setState("CA");
        // 上传学校、班级设置
        final List<CenterSetting> centerSettings = new ArrayList<>();
        final CenterSetting centerSetting = new CenterSetting();
        centerSetting.getGroupIds().add(groupId);
        centerSettings.add(centerSetting);
        drdpSetting.setCenterSettings(centerSettings);
        final CenterSettingData centerSettingData = new CenterSettingData();
        centerSettingData.setCenterSettings(centerSettings);
        centerSettingData.setFrameworkTypes(frameworkTypes);
        drdpSetting.setCenterSetting(JsonUtil.toJson(centerSettingData));
        drdpSettings.add(drdpSetting);

        Map<String, DRDPSetting> drdpSettingMap = new HashMap<>();
        drdpSettingMap.put(groupId.toUpperCase(), drdpSetting);
        when(analysisService.getGroupDRDPSettingMap(agencyId)).thenReturn(drdpSettingMap);

        // 时区
        final String timezone = "America/Los_Angeles";
        when(centerDao.getFirstCenterTimeZoneByAgencyId(agencyId)).thenReturn(timezone);

        // 机构信息
        final AgencyEntity agency = new AgencyEntity();
        agency.setId(agencyId);
        agency.setName("agency");
        agency.setState("CA");
        when(agencyDao.getById(agencyId)).thenReturn(agency);

        // 管理员
        final UserModel owner = new UserModel();
        owner.setEmail("<EMAIL>");
        owner.setRole(UserRole.AGENCY_OWNER.toString());
        owner.setDisplayName("micro owner");
        when(userDao.getAgencyAdminsByAgencyId(agencyId)).thenReturn(Collections.singletonList(owner));

        // 学校
        final CenterEntity center = new CenterEntity();
        center.setId(centerId);
        center.setName("center");

        // 班级
        final com.learninggenie.common.data.model.GroupEntity group = new com.learninggenie.common.data.model.GroupEntity();
        group.setId(groupId);
        group.setName("group");
        group.setCenter(center);
        when(groupDao.getGroupWithCenter(groupId)).thenReturn(group);

        // 框架
        when(ratingService.isPSFramework(frameworkId)).thenReturn(true);

        // 学校无别名
        when(centerDao.getMeta(centerId, CenterMetaKey.DRDP_CENTER_ALIAS.toString() + "_" + drdpSetting.getId().toUpperCase())).thenReturn(null);

        // 小孩
        for (CheckedChild checkedChild : checkedChildren) {
            final String childId = checkedChild.getId();
            // 基本信息
            final EnrollmentModel child = new EnrollmentModel();
            child.setFirstName("first");
            child.setLastName("last");
            child.setBirthDate("2020-03-15");
            child.setEnrollmentDate("01/01/2022");
            child.setGender("MALE");
            when(studentDao.getEnrollmentWithDelete(childId)).thenReturn(child);

            // 属性
            List<StudentAttrEntity> attrs = new ArrayList<>();
            StudentAttrEntity attr = new StudentAttrEntity();
            attr.setAttrId(UUID.randomUUID().toString());
            attr.setAttrName("Teacher");
            attr.setAttrValue("<EMAIL>");
            attr.setAttrTypeValue("attrType");
            attr.setEnrollmentId("enrollmentId");

            attrs.add(attr);
            when(studentDao.getAttrsByChildIds(Collections.singletonList(childId))).thenReturn(attrs);

            // 评分
            when(scoreDao.gerChildScore(eq(childId), anyString(), anyString())).thenReturn(new ArrayList<>());

            // 快照
            final StudentSnapshotEntity studentSnapshotEntity = new StudentSnapshotEntity();
            studentSnapshotEntity.setId(UUID.randomUUID().toString());
            studentSnapshotEntity.setFrameworkId(frameworkId);
            when(studentDao.getSnapshotByChildIdAndLockGroupIdAndAlias(childId, groupId, lockAlias)).thenReturn(null);
            when(studentDao.getSnapshot(childId, lockAlias)).thenReturn(studentSnapshotEntity);

            // 增加评分周期
            final List<RatingPeriodEntity> ratingPeriodEntityList = new ArrayList<>();

            // 创建第一个 RatingPeriodEntity 对象
            final RatingPeriodEntity ratingPeriodEntity1 = new RatingPeriodEntity();
            ratingPeriodEntity1.setId("1");
            ratingPeriodEntity1.setAlias("RP1");
            ratingPeriodEntity1.setDisplayAlias("Rating Period 1");
            ratingPeriodEntity1.setFromAtLocal(new Date());
            ratingPeriodEntity1.setToAtLocal(new Date(System.currentTimeMillis() + 86400000)); // 1 day later
            ratingPeriodEntity1.setCreateAtUtc(new Date());
            ratingPeriodEntity1.setUpdateAtUtc(new Date());
            ratingPeriodEntity1.setEnrollmentId("enrollment1");
            ratingPeriodEntity1.setActived(true);
            ratingPeriodEntity1.setCompletedAtUtc(new Date());
            ratingPeriodEntity1.setCompletedAtLocal(new Date());
            ratingPeriodEntity1.setRealCompletedAtLocal(new Date());
            ratingPeriodEntity1.setSortIndex(1);
            ratingPeriodEntity1.setFromAtLocalString("2022-01-01 00:00:00");
            ratingPeriodEntity1.setToAtLocalString("2022-01-31 23:59:59");
            ratingPeriodEntity1.setCompletionDate("2022-01-31");
            ratingPeriodEntity1.setNotEdit(false);
            ratingPeriodEntity1.setNew(true);
            ratingPeriodEntity1.setDomainId("domain1");
            ratingPeriodEntity1.setFrameWorkName("framework1");
            ratingPeriodEntity1.setMinDateString("2022-01-01");
            ratingPeriodEntity1.setMaxDateString("2022-01-31");
            ratingPeriodEntity1.setLocked(false);
            ratingPeriodEntity1.setLockPeriod(false);
            ratingPeriodEntity1.setLockUserId("user1");
            ratingPeriodEntity1.setLockAtLocal(new Date());
            ratingPeriodEntity1.setAgencyId("agency1");
            ratingPeriodEntity1.setChangeAlias(false);
            ratingPeriodEntity1.setAfterAlias(false);
            ratingPeriodEntity1.setPreAlias(false);
            ratingPeriodEntity1.setPreAliasStr("Previous Alias");
            ratingPeriodEntity1.setEntrysDate("2022-01-01");
            ratingPeriodEntity1.setHasRatingFrom("2022-01-01");
            ratingPeriodEntity1.setHasRatingTo("2022-01-31");
            ratingPeriodEntityList.add(ratingPeriodEntity1);

            // 创建第二个 RatingPeriodEntity 对象
            final RatingPeriodEntity ratingPeriodEntity2 = new RatingPeriodEntity();
            ratingPeriodEntity2.setId("2");
            ratingPeriodEntity2.setAlias("RP2");
            ratingPeriodEntity2.setDisplayAlias("Rating Period 2");
            ratingPeriodEntity2.setFromAtLocal(new Date(System.currentTimeMillis() + 86400000)); // 1 day later
            ratingPeriodEntity2.setToAtLocal(new Date(System.currentTimeMillis() + 172800000)); // 2 days later
            ratingPeriodEntity2.setCreateAtUtc(new Date());
            ratingPeriodEntity2.setUpdateAtUtc(new Date());
            ratingPeriodEntity2.setEnrollmentId("enrollment2");
            ratingPeriodEntity2.setActived(true);
            ratingPeriodEntity2.setCompletedAtUtc(new Date());
            ratingPeriodEntity2.setCompletedAtLocal(new Date());
            ratingPeriodEntity2.setRealCompletedAtLocal(new Date());
            ratingPeriodEntity2.setSortIndex(2);
            ratingPeriodEntity2.setFromAtLocalString("2022-02-01 00:00:00");
            ratingPeriodEntity2.setToAtLocalString("2022-02-02 23:59:59");
            ratingPeriodEntity2.setCompletionDate("2022-02-02");
            ratingPeriodEntity2.setNotEdit(false);
            ratingPeriodEntity2.setNew(true);
            ratingPeriodEntity2.setDomainId("domain2");
            ratingPeriodEntity2.setFrameWorkName("framework2");
            ratingPeriodEntity2.setMinDateString("2022-02-01");
            ratingPeriodEntity2.setMaxDateString("2022-02-02");
            ratingPeriodEntity2.setLocked(false);
            ratingPeriodEntity2.setLockPeriod(false);
            ratingPeriodEntity2.setLockUserId("user2");
            ratingPeriodEntity2.setLockAtLocal(new Date());
            ratingPeriodEntity2.setAgencyId("agency2");
            ratingPeriodEntity2.setChangeAlias(false);
            ratingPeriodEntity2.setAfterAlias(false);
            ratingPeriodEntity2.setPreAlias(false);
            ratingPeriodEntity2.setPreAliasStr("Previous Alias 2");
            ratingPeriodEntity2.setEntrysDate("2022-02-01");
            ratingPeriodEntity2.setHasRatingFrom("2022-02-01");
            ratingPeriodEntity2.setHasRatingTo("2022-02-02");
            ratingPeriodEntityList.add(ratingPeriodEntity2);
            when(studentDao.getAllPeriodsByAlias(childId, lockAlias)).thenReturn(ratingPeriodEntityList);

            // 循环 ratingPeriodEntityList
            for (RatingPeriodEntity ratingPeriodEntity : ratingPeriodEntityList) {
                // 生成 ScoreList
                final List<StudentScoreEntity> studentScoreEntityList = new ArrayList<>();

                // 创建第一个StudentScoreEntity对象
                final StudentScoreEntity studentScoreEntity1 = new StudentScoreEntity();
                studentScoreEntity1.setId("1");
                studentScoreEntity1.setStudentId(childId);
                studentScoreEntity1.setNoteId("1001");
                studentScoreEntity1.setDomainId("10");
                studentScoreEntity1.setOldDomainId("20");
                studentScoreEntity1.setLevelId("1");
                studentScoreEntity1.setComment("good job");
                studentScoreEntity1.setCreateByUserId("user1");
                studentScoreEntity1.setCreateAtUtc("2022-01-01 10:00:00");
                studentScoreEntity1.setCreateAtUtcDate(TimeUtil.parse("2022-01-01 10:00:00", TimeUtil.dateFormat));
                studentScoreEntity1.setGroupId("group1");
                studentScoreEntity1.setCreateAtLocal("2022-01-01 18:00:00");
                studentScoreEntity1.setCreateAtLocalDate(TimeUtil.parse("2022-01-01 18:00:00", TimeUtil.dateFormat));
                studentScoreEntityList.add(studentScoreEntity1);

                // 创建第二个StudentScoreEntity对象
                final StudentScoreEntity studentScoreEntity2 = new StudentScoreEntity();
                studentScoreEntity2.setId("2");
                studentScoreEntity2.setStudentId(childId);
                studentScoreEntity2.setNoteId("1002");
                studentScoreEntity2.setDomainId("20");
                studentScoreEntity2.setOldDomainId("30");
                studentScoreEntity2.setLevelId("2");
                studentScoreEntity2.setComment("well done");
                studentScoreEntity2.setCreateByUserId("user2");
                studentScoreEntity2.setCreateAtUtc("2022-01-02 10:00:00");
                studentScoreEntity2.setCreateAtUtcDate(TimeUtil.parse("2022-01-02 10:00:00", TimeUtil.dateFormat));
                studentScoreEntity2.setGroupId("group2");
                studentScoreEntity2.setCreateAtLocal("2022-01-02 18:00:00");
                studentScoreEntity2.setCreateAtLocalDate(TimeUtil.parse("2022-01-02 18:00:00", TimeUtil.dateFormat));
                studentScoreEntityList.add(studentScoreEntity2);

                // 创建第三个StudentScoreEntity对象
                final StudentScoreEntity studentScoreEntity3 = new StudentScoreEntity();
                studentScoreEntity3.setId("3");
                studentScoreEntity3.setStudentId("10003");
                studentScoreEntity3.setNoteId("1003");
                studentScoreEntity3.setDomainId("30");
                studentScoreEntity3.setOldDomainId("40");
                studentScoreEntity3.setLevelId("3");
                studentScoreEntity3.setComment("excellent");
                studentScoreEntity3.setCreateByUserId("user3");
                studentScoreEntity3.setCreateAtUtc("2022-01-03 10:00:00");
                studentScoreEntity3.setCreateAtUtcDate(TimeUtil.parse("2022-01-03 10:00:00", TimeUtil.dateFormat));
                studentScoreEntity3.setGroupId("group3");
                studentScoreEntity3.setCreateAtLocal("2022-01-03 18:00:00");
                studentScoreEntity3.setCreateAtLocalDate(TimeUtil.parse("2022-01-03 18:00:00", TimeUtil.dateFormat));
                studentScoreEntityList.add(studentScoreEntity3);
                when(scoreDao.gerChildScore(childId, TimeUtil.format(ratingPeriodEntity.getFromAtLocal()), TimeUtil.format(ratingPeriodEntity.getToAtLocal())))
                        .thenReturn(studentScoreEntityList);
            }
        }

        // 老师列表
        final List<UserEntity> teachers = new ArrayList<>();
        final UserEntity teacher1 = new UserEntity();
        teacher1.setEmail("<EMAIL>");
        teacher1.setRole(UserRole.TEACHER.toString());
        teacher1.setFirstName("micro");
        teacher1.setLastName("teacher");
        teachers.add(teacher1);
        when(groupDao.getTeacherByGroupId(groupId)).thenReturn(teachers);

        GetAllAgencyResponse getAllAgencyResponse = new GetAllAgencyResponse();
        List<DrdpAgencyModel> drdpAgencyModels = Lists.newArrayList();
        DrdpAgencyModel drdpAgency = new DrdpAgencyModel();
        drdpAgency.setId(1);
        drdpAgency.setAgencyName("agency");

        drdpAgencyModels.add(drdpAgency);
        getAllAgencyResponse.setAgencies(drdpAgencyModels);

        when(drdpService.getAllAgencies()).thenReturn(getAllAgencyResponse);

        // 锁定并上传
        final BatchLockResponse response = scoreService.batchLockAndUpload(jobId);

        // 有老师时不会查询学校下其他老师
        verify(userDao, times(0)).getTeachersByCenterId(centerId);
        // 有老师时不会查询园长
        verify(userDao, times(0)).getSiteAdminsByCenterIds(anyList());
        // 验证 response.getLockedCount();
        // 对Response对象的属性和方法进行断言，验证其返回值是否与预期相同
        assertEquals(0, response.getLockedCount());
        assertEquals(0, response.getOnlyLockedCount());
        assertEquals(0, response.getErrorChildCount());
        assertEquals(0, response.getNeedFixChildCount());
        assertEquals(0, response.getAliasErrorCount());
        assertEquals(0, response.getClassErrorCount());
    }

    @Test
    public void testBatchLockAndUpload_missBaseInfo() {
        // 任务 ID
        final String jobId = UUID.randomUUID().toString();
        // 锁定周期
        final String lockAlias = "2021-2022 Fall";
        // 用户 ID
        final String userId = UUID.randomUUID().toString();
        // 机构 ID
        final String agencyId = UUID.randomUUID().toString();
        // 学校 ID
        final String centerId = UUID.randomUUID().toString();
        // 班级 ID
        final String groupId = UUID.randomUUID().toString();
        // 框架 ID
        final String frameworkId = "domain1";
        // 锁定参数
        final LockJobArgs lockJobArgs = new LockJobArgs();
        lockJobArgs.setAgencyId(agencyId);
        lockJobArgs.setPeriodAlias(lockAlias);
        lockJobArgs.setUserId(userId);
        lockJobArgs.setUploadLockedData(true);
        // 检查结果
        final LockJobResult lockJobResult = new LockJobResult();
        final List<CheckedGroup> checkedGroups = new ArrayList<>();
        // 班级信息
        final CheckedGroup checkedGroup = new CheckedGroup();
        checkedGroup.setId(groupId);
        checkedGroup.setPeriodAlias(lockAlias);
        checkedGroup.setFrameworkId(frameworkId);
        checkedGroups.add(checkedGroup);
        lockJobResult.setCheckedGroups(checkedGroups);

        // 学生信息
        final List<CheckedChild> checkedChildren = new ArrayList<>();
        checkedGroup.setCheckedChildren(checkedChildren);
        // 评分、属性都完成
        final String child1Id = "child-2";
        final CheckedChild checkedChild1 = new CheckedChild();
        checkedChild1.setId(child1Id);
        checkedChildren.add(checkedChild1);

        // 任务信息
        final JobEntity job = new JobEntity();
        job.setArgs(JsonUtil.toJson(lockJobArgs));
        job.setResult(JsonUtil.toJson(lockJobResult));
        when(jobDao.getJobById(jobId)).thenReturn(job);

        // 功能开关为开
        when(userProvider.getAgencyOpenDefaultOpen(agencyId, AgencyMetaKey.DRDP_UPLOAD.toString())).thenReturn(true);

        // 上传 DRDP 设置为打开
        final AgencyMetaDataEntity uploadMeta = new AgencyMetaDataEntity();
        uploadMeta.setMetaValue(DrdpSettingValue.DRDP_UPLOAD_OPEN.toString());
        when(agencyDao.getMeta(agencyId, DrdpSettingKey.UPLOAD_DRDP_SETTING.toString())).thenReturn(uploadMeta);

        // 框架设置
        final List<FrameworkType> frameworkTypes = new ArrayList<>();
        frameworkTypes.add(new FrameworkType("IT", "ITC"));
        frameworkTypes.add(new FrameworkType("PS", "PSC"));
        frameworkTypes.add(new FrameworkType("K", "KC"));
        frameworkTypes.add(new FrameworkType("SA", "SAC"));

        // 上传设置信息
        final List<DRDPSetting> drdpSettings = new ArrayList<>();
        final DRDPSetting drdpSetting = new DRDPSetting();
        drdpSetting.setId(UUID.randomUUID().toString());
        drdpSetting.setComplete(true);
        drdpSetting.setState("CA");
        // 上传学校、班级设置
        final List<CenterSetting> centerSettings = new ArrayList<>();
        final CenterSetting centerSetting = new CenterSetting();
        centerSetting.getGroupIds().add(groupId);
        centerSettings.add(centerSetting);
        drdpSetting.setCenterSettings(centerSettings);
        final CenterSettingData centerSettingData = new CenterSettingData();
        centerSettingData.setCenterSettings(centerSettings);
        centerSettingData.setFrameworkTypes(frameworkTypes);
        drdpSetting.setCenterSetting(JsonUtil.toJson(centerSettingData));
        drdpSettings.add(drdpSetting);

        Map<String, DRDPSetting> drdpSettingMap = new HashMap<>();
        drdpSettingMap.put(groupId.toUpperCase(), drdpSetting);
        when(analysisService.getGroupDRDPSettingMap(agencyId)).thenReturn(drdpSettingMap);

        // 时区
        final String timezone = "America/Los_Angeles";
        when(centerDao.getFirstCenterTimeZoneByAgencyId(agencyId)).thenReturn(timezone);

        // 机构信息
        final AgencyEntity agency = new AgencyEntity();
        agency.setId(agencyId);
        agency.setName("agency");
        agency.setState("CA");
        when(agencyDao.getById(agencyId)).thenReturn(agency);

        // 管理员
        final UserModel owner = new UserModel();
        owner.setEmail("<EMAIL>");
        owner.setRole(UserRole.AGENCY_OWNER.toString());
        owner.setDisplayName("micro owner");
        when(userDao.getAgencyAdminsByAgencyId(agencyId)).thenReturn(Collections.singletonList(owner));

        // 学校
        final CenterEntity center = new CenterEntity();
        center.setId(centerId);
        center.setName("center");

        // 班级
        final com.learninggenie.common.data.model.GroupEntity group = new com.learninggenie.common.data.model.GroupEntity();
        group.setId(groupId);
        group.setName("group");
        group.setCenter(center);
        when(groupDao.getGroupWithCenter(groupId)).thenReturn(group);

        // 框架
        when(ratingService.isPSFramework(frameworkId)).thenReturn(true);

        // 学校无别名
        when(centerDao.getMeta(centerId, CenterMetaKey.DRDP_CENTER_ALIAS.toString() + "_" + drdpSetting.getId().toUpperCase())).thenReturn(null);

        // 小孩
        for (CheckedChild checkedChild : checkedChildren) {
            final String childId = checkedChild.getId();
            // 基本信息
            final EnrollmentModel child = new EnrollmentModel();
//            child.setFirstName("first");
//            child.setLastName("last");
//            child.setBirthDate("2020-03-15");
//            child.setEnrollmentDate("01/01/2022");
//            child.setGender("MALE");
            when(studentDao.getEnrollmentWithDelete(childId)).thenReturn(child);

            // 属性
            List<StudentAttrEntity> attrs = new ArrayList<>();
            StudentAttrEntity attr = new StudentAttrEntity();
            attr.setAttrId(UUID.randomUUID().toString());
            attr.setAttrName("Teacher");
            attr.setAttrValue("<EMAIL>");
            attr.setAttrTypeValue("attrType");
            attr.setEnrollmentId("enrollmentId");

            attrs.add(attr);


            // 快照
            final StudentSnapshotEntity studentSnapshotEntity = new StudentSnapshotEntity();
            studentSnapshotEntity.setId(UUID.randomUUID().toString());
            studentSnapshotEntity.setFrameworkId(frameworkId);
            when(studentDao.getSnapshotByChildIdAndLockGroupIdAndAlias(childId, groupId, lockAlias)).thenReturn(null);
            when(studentDao.getSnapshot(childId, lockAlias)).thenReturn(studentSnapshotEntity);

            // 增加评分周期
            final List<RatingPeriodEntity> ratingPeriodEntityList = new ArrayList<>();

            // 创建第一个 RatingPeriodEntity 对象
            final RatingPeriodEntity ratingPeriodEntity1 = new RatingPeriodEntity();
            ratingPeriodEntity1.setId("1");
            ratingPeriodEntity1.setAlias("RP1");
            ratingPeriodEntity1.setDisplayAlias("Rating Period 1");
            ratingPeriodEntity1.setFromAtLocal(new Date());
            ratingPeriodEntity1.setToAtLocal(new Date(System.currentTimeMillis() + 86400000)); // 1 day later
            ratingPeriodEntity1.setCreateAtUtc(new Date());
            ratingPeriodEntity1.setUpdateAtUtc(new Date());
            ratingPeriodEntity1.setEnrollmentId("enrollment1");
            ratingPeriodEntity1.setActived(true);
            ratingPeriodEntity1.setCompletedAtUtc(new Date());
            ratingPeriodEntity1.setCompletedAtLocal(new Date());
            ratingPeriodEntity1.setRealCompletedAtLocal(new Date());
            ratingPeriodEntity1.setSortIndex(1);
            ratingPeriodEntity1.setFromAtLocalString("2022-01-01 00:00:00");
            ratingPeriodEntity1.setToAtLocalString("2022-01-31 23:59:59");
            ratingPeriodEntity1.setCompletionDate("2022-01-31");
            ratingPeriodEntity1.setNotEdit(false);
            ratingPeriodEntity1.setNew(true);
            ratingPeriodEntity1.setDomainId("domain1");
            ratingPeriodEntity1.setFrameWorkName("framework1");
            ratingPeriodEntity1.setMinDateString("2022-01-01");
            ratingPeriodEntity1.setMaxDateString("2022-01-31");
            ratingPeriodEntity1.setLocked(false);
            ratingPeriodEntity1.setLockPeriod(false);
            ratingPeriodEntity1.setLockUserId("user1");
            ratingPeriodEntity1.setLockAtLocal(new Date());
            ratingPeriodEntity1.setAgencyId("agency1");
            ratingPeriodEntity1.setChangeAlias(false);
            ratingPeriodEntity1.setAfterAlias(false);
            ratingPeriodEntity1.setPreAlias(false);
            ratingPeriodEntity1.setPreAliasStr("Previous Alias");
            ratingPeriodEntity1.setEntrysDate("2022-01-01");
            ratingPeriodEntity1.setHasRatingFrom("2022-01-01");
            ratingPeriodEntity1.setHasRatingTo("2022-01-31");
            ratingPeriodEntityList.add(ratingPeriodEntity1);

            // 创建第二个 RatingPeriodEntity 对象
            final RatingPeriodEntity ratingPeriodEntity2 = new RatingPeriodEntity();
            ratingPeriodEntity2.setId("2");
            ratingPeriodEntity2.setAlias("RP2");
            ratingPeriodEntity2.setDisplayAlias("Rating Period 2");
            ratingPeriodEntity2.setFromAtLocal(new Date(System.currentTimeMillis() + 86400000)); // 1 day later
            ratingPeriodEntity2.setToAtLocal(new Date(System.currentTimeMillis() + 172800000)); // 2 days later
            ratingPeriodEntity2.setCreateAtUtc(new Date());
            ratingPeriodEntity2.setUpdateAtUtc(new Date());
            ratingPeriodEntity2.setEnrollmentId("enrollment2");
            ratingPeriodEntity2.setActived(true);
            ratingPeriodEntity2.setCompletedAtUtc(new Date());
            ratingPeriodEntity2.setCompletedAtLocal(new Date());
            ratingPeriodEntity2.setRealCompletedAtLocal(new Date());
            ratingPeriodEntity2.setSortIndex(2);
            ratingPeriodEntity2.setFromAtLocalString("2022-02-01 00:00:00");
            ratingPeriodEntity2.setToAtLocalString("2022-02-02 23:59:59");
            ratingPeriodEntity2.setCompletionDate("2022-02-02");
            ratingPeriodEntity2.setNotEdit(false);
            ratingPeriodEntity2.setNew(true);
            ratingPeriodEntity2.setDomainId("domain2");
            ratingPeriodEntity2.setFrameWorkName("framework2");
            ratingPeriodEntity2.setMinDateString("2022-02-01");
            ratingPeriodEntity2.setMaxDateString("2022-02-02");
            ratingPeriodEntity2.setLocked(false);
            ratingPeriodEntity2.setLockPeriod(false);
            ratingPeriodEntity2.setLockUserId("user2");
            ratingPeriodEntity2.setLockAtLocal(new Date());
            ratingPeriodEntity2.setAgencyId("agency2");
            ratingPeriodEntity2.setChangeAlias(false);
            ratingPeriodEntity2.setAfterAlias(false);
            ratingPeriodEntity2.setPreAlias(false);
            ratingPeriodEntity2.setPreAliasStr("Previous Alias 2");
            ratingPeriodEntity2.setEntrysDate("2022-02-01");
            ratingPeriodEntity2.setHasRatingFrom("2022-02-01");
            ratingPeriodEntity2.setHasRatingTo("2022-02-02");
            ratingPeriodEntityList.add(ratingPeriodEntity2);

            // 循环 ratingPeriodEntityList
            for (RatingPeriodEntity ratingPeriodEntity : ratingPeriodEntityList) {
                // 生成 ScoreList
                final List<StudentScoreEntity> studentScoreEntityList = new ArrayList<>();

                // 创建第一个StudentScoreEntity对象
                final StudentScoreEntity studentScoreEntity1 = new StudentScoreEntity();
                studentScoreEntity1.setId("1");
                studentScoreEntity1.setStudentId(childId);
                studentScoreEntity1.setNoteId("1001");
                studentScoreEntity1.setDomainId("10");
                studentScoreEntity1.setOldDomainId("20");
                studentScoreEntity1.setLevelId("1");
                studentScoreEntity1.setComment("good job");
                studentScoreEntity1.setCreateByUserId("user1");
                studentScoreEntity1.setCreateAtUtc("2022-01-01 10:00:00");
                studentScoreEntity1.setCreateAtUtcDate(TimeUtil.parse("2022-01-01 10:00:00", TimeUtil.dateFormat));
                studentScoreEntity1.setGroupId("group1");
                studentScoreEntity1.setCreateAtLocal("2022-01-01 18:00:00");
                studentScoreEntity1.setCreateAtLocalDate(TimeUtil.parse("2022-01-01 18:00:00", TimeUtil.dateFormat));
                studentScoreEntityList.add(studentScoreEntity1);

                // 创建第二个StudentScoreEntity对象
                final StudentScoreEntity studentScoreEntity2 = new StudentScoreEntity();
                studentScoreEntity2.setId("2");
                studentScoreEntity2.setStudentId(childId);
                studentScoreEntity2.setNoteId("1002");
                studentScoreEntity2.setDomainId("20");
                studentScoreEntity2.setOldDomainId("30");
                studentScoreEntity2.setLevelId("2");
                studentScoreEntity2.setComment("well done");
                studentScoreEntity2.setCreateByUserId("user2");
                studentScoreEntity2.setCreateAtUtc("2022-01-02 10:00:00");
                studentScoreEntity2.setCreateAtUtcDate(TimeUtil.parse("2022-01-02 10:00:00", TimeUtil.dateFormat));
                studentScoreEntity2.setGroupId("group2");
                studentScoreEntity2.setCreateAtLocal("2022-01-02 18:00:00");
                studentScoreEntity2.setCreateAtLocalDate(TimeUtil.parse("2022-01-02 18:00:00", TimeUtil.dateFormat));
                studentScoreEntityList.add(studentScoreEntity2);

                // 创建第三个StudentScoreEntity对象
                final StudentScoreEntity studentScoreEntity3 = new StudentScoreEntity();
                studentScoreEntity3.setId("3");
                studentScoreEntity3.setStudentId("10003");
                studentScoreEntity3.setNoteId("1003");
                studentScoreEntity3.setDomainId("30");
                studentScoreEntity3.setOldDomainId("40");
                studentScoreEntity3.setLevelId("3");
                studentScoreEntity3.setComment("excellent");
                studentScoreEntity3.setCreateByUserId("user3");
                studentScoreEntity3.setCreateAtUtc("2022-01-03 10:00:00");
                studentScoreEntity3.setCreateAtUtcDate(TimeUtil.parse("2022-01-03 10:00:00", TimeUtil.dateFormat));
                studentScoreEntity3.setGroupId("group3");
                studentScoreEntity3.setCreateAtLocal("2022-01-03 18:00:00");
                studentScoreEntity3.setCreateAtLocalDate(TimeUtil.parse("2022-01-03 18:00:00", TimeUtil.dateFormat));

            }
        }

        // 老师列表
        final List<UserEntity> teachers = new ArrayList<>();
        final UserEntity teacher1 = new UserEntity();
        teacher1.setEmail("<EMAIL>");
        teacher1.setRole(UserRole.TEACHER.toString());
        teacher1.setFirstName("micro");
        teacher1.setLastName("teacher");
        teachers.add(teacher1);
        when(groupDao.getTeacherByGroupId(groupId)).thenReturn(teachers);

        GetAllAgencyResponse getAllAgencyResponse = new GetAllAgencyResponse();
        List<DrdpAgencyModel> drdpAgencyModels = Lists.newArrayList();
        DrdpAgencyModel drdpAgency = new DrdpAgencyModel();
        drdpAgency.setId(1);
        drdpAgency.setAgencyName("agency");

        drdpAgencyModels.add(drdpAgency);
        getAllAgencyResponse.setAgencies(drdpAgencyModels);

        GetAgencyTermsResponse agencyTerms = new GetAgencyTermsResponse();
        List<DrdpTermModel> terms = Lists.newArrayList();
        DrdpTermModel drdpTerm1 = new DrdpTermModel();
        drdpTerm1.setId(1);
        drdpTerm1.setName("Spring 2022");
        drdpTerm1.setStartDate("2023-10-01T00:00:00");
        drdpTerm1.setEndDate(TimeUtil.format(TimeUtil.addDays(new Date(), 1), TimeUtil.format25));

        terms.add(drdpTerm1);

        agencyTerms.setTerms(terms);

        GetAgencySitesResponse getAgencySiteResponse = new GetAgencySitesResponse();
        List<DrdpSiteModel> drdpSites = Lists.newArrayList();
        DrdpSiteModel drdpSite = new DrdpSiteModel();
        drdpSite.setId(1);
        drdpSite.setAgencyId(1);
        drdpSite.setSiteName("center");
        drdpSites.add(drdpSite);
        getAgencySiteResponse.setSites(drdpSites);

        GetStateCountiesResponse stateCounties = new GetStateCountiesResponse();
        List<String> countyNames = Lists.newArrayList();
        countyNames.add("Los Angeles");
        stateCounties.setCountyNames(countyNames);


        GetDrdpStatesResponse caStateResponse = new GetDrdpStatesResponse();
        List<DrdpStateModel> drdpStates = Lists.newArrayList();
        DrdpStateModel drdpState = new DrdpStateModel();
        drdpState.setId(1);
        drdpState.setStateName("CA");
        drdpStates.add(drdpState);

        caStateResponse.setStates(drdpStates);
        caStateResponse.setStatus(200);


        GetDrdpUsersResponse drdpServiceUsers = new GetDrdpUsersResponse();
        List<DrdpUserModel> drdpUsers = Lists.newArrayList();
        DrdpUserModel drdpUser = new DrdpUserModel();
        drdpUser.setId(1);
        drdpUser.setEmail("<EMAIL>");
        drdpUser.setRoles(Lists.newArrayList());
        drdpUsers.add(drdpUser);
        drdpServiceUsers.setUsers(drdpUsers);
        drdpServiceUsers.setStatus(200);

        UpdateDrdpUserResponse updateDrdpUserResponse = new UpdateDrdpUserResponse();
        updateDrdpUserResponse.setId(1);
        updateDrdpUserResponse.setStatus(200);

        AddDrdpUserRoleResponse addDrdpUserRoleResponse = new AddDrdpUserRoleResponse();
        addDrdpUserRoleResponse.setId(1);
        addDrdpUserRoleResponse.setStatus(200);

        GetSiteClassroomsResponse classroomModelResponse = new GetSiteClassroomsResponse();
        List<DrdpClassroomModel> drdpClassrooms = Lists.newArrayList();
        DrdpClassroomModel drdpClassroom = new DrdpClassroomModel();
        drdpClassroom.setId(1);
        drdpClassroom.setClassroomName("group");
        drdpClassrooms.add(drdpClassroom);
        classroomModelResponse.setClassrooms(drdpClassrooms);

        GetClassroomClassesResponse classroomClasses = new GetClassroomClassesResponse();
        List<DrdpClassModel> drdpClasses = Lists.newArrayList();
        DrdpClassModel drdpClass = new DrdpClassModel();
        drdpClass.setId(1);
        drdpClass.setClassroomName("group");
        drdpClass.setTermId(1);
        drdpClasses.add(drdpClass);
        classroomClasses.setClasses(drdpClasses);
        classroomClasses.setStatus(200);

        UpdateDrdpClassResponse updateDrdpClassResponse = new UpdateDrdpClassResponse();
        updateDrdpClassResponse.setId(1);
        updateDrdpClassResponse.setStatus(200);

        // 设置批量评分的返回值
        DrdpBatchScoringResponse drdpBatchScoringResponse = new DrdpBatchScoringResponse();
        drdpBatchScoringResponse.setId(1);
        drdpBatchScoringResponse.setStatus(HttpStatus.OK.value());

        // 锁定并上传
        final BatchLockResponse response = scoreService.batchLockAndUpload(jobId);

        // 有老师时不会查询学校下其他老师
        verify(userDao, times(0)).getTeachersByCenterId(centerId);
        // 有老师时不会查询园长
        verify(userDao, times(0)).getSiteAdminsByCenterIds(anyList());
        // 验证 response.getLockedCount();
        // 对Response对象的属性和方法进行断言，验证其返回值是否与预期相同
        assertEquals(0, response.getLockedCount());
        assertEquals(0, response.getOnlyLockedCount());
        assertEquals(0, response.getErrorChildCount());
        assertEquals(1, response.getNeedFixChildCount());
        assertEquals(0, response.getAliasErrorCount());
        assertEquals(0, response.getClassErrorCount());
    }

    @Test
    public void testBatchLockAndUploadInGetSnapshotIsNull() {
        // 任务 ID
        final String jobId = UUID.randomUUID().toString();
        // 锁定周期
        final String lockAlias = "2021-2022 Spring";
        // 用户 ID
        final String userId = UUID.randomUUID().toString();
        // 机构 ID
        final String agencyId = UUID.randomUUID().toString();
        // 学校 ID
        final String centerId = UUID.randomUUID().toString();
        // 班级 ID
        final String groupId = UUID.randomUUID().toString();
        // 框架 ID
        final String frameworkId = "domain1";
        // 锁定参数
        final LockJobArgs lockJobArgs = new LockJobArgs();
        lockJobArgs.setAgencyId(agencyId);
        lockJobArgs.setPeriodAlias(lockAlias);
        lockJobArgs.setUserId(userId);
        lockJobArgs.setUploadLockedData(true);
        // 检查结果
        final LockJobResult lockJobResult = new LockJobResult();
        final List<CheckedGroup> checkedGroups = new ArrayList<>();
        // 班级信息
        final CheckedGroup checkedGroup = new CheckedGroup();
        checkedGroup.setId(groupId);
        checkedGroup.setPeriodAlias(lockAlias);
        checkedGroup.setFrameworkId(frameworkId);
        checkedGroups.add(checkedGroup);
        lockJobResult.setCheckedGroups(checkedGroups);

        // 学生信息
        final List<CheckedChild> checkedChildren = new ArrayList<>();
        checkedGroup.setCheckedChildren(checkedChildren);
        // 评分、属性都完成
        final String child1Id = "child-2";
        final CheckedChild checkedChild1 = new CheckedChild();
        checkedChild1.setId(child1Id);
        checkedChildren.add(checkedChild1);

        // 任务信息
        final JobEntity job = new JobEntity();
        job.setArgs(JsonUtil.toJson(lockJobArgs));
        job.setResult(JsonUtil.toJson(lockJobResult));
        when(jobDao.getJobById(jobId)).thenReturn(job);

        // 功能开关为开
        when(userProvider.getAgencyOpenDefaultOpen(agencyId, AgencyMetaKey.DRDP_UPLOAD.toString())).thenReturn(true);

        // 上传 DRDP 设置为打开
        final AgencyMetaDataEntity uploadMeta = new AgencyMetaDataEntity();
        uploadMeta.setMetaValue(DrdpSettingValue.DRDP_UPLOAD_OPEN.toString());
        when(agencyDao.getMeta(agencyId, DrdpSettingKey.UPLOAD_DRDP_SETTING.toString())).thenReturn(uploadMeta);

        // 框架设置
        final List<FrameworkType> frameworkTypes = new ArrayList<>();
        frameworkTypes.add(new FrameworkType("IT", "ITC"));
        frameworkTypes.add(new FrameworkType("PS", "PSC"));
        frameworkTypes.add(new FrameworkType("K", "KC"));
        frameworkTypes.add(new FrameworkType("SA", "SAC"));

        // 上传设置信息
        final List<DRDPSetting> drdpSettings = new ArrayList<>();
        final DRDPSetting drdpSetting = new DRDPSetting();
        drdpSetting.setId(UUID.randomUUID().toString());
        drdpSetting.setComplete(true);
        drdpSetting.setState("CA");
        // 上传学校、班级设置
        final List<CenterSetting> centerSettings = new ArrayList<>();
        final CenterSetting centerSetting = new CenterSetting();
        centerSetting.getGroupIds().add(groupId);
        centerSettings.add(centerSetting);
        drdpSetting.setCenterSettings(centerSettings);
        final CenterSettingData centerSettingData = new CenterSettingData();
        centerSettingData.setCenterSettings(centerSettings);
        centerSettingData.setFrameworkTypes(frameworkTypes);
        drdpSetting.setCenterSetting(JsonUtil.toJson(centerSettingData));
        drdpSettings.add(drdpSetting);

        Map<String, DRDPSetting> drdpSettingMap = new HashMap<>();
        drdpSettingMap.put(groupId.toUpperCase(), drdpSetting);
        when(analysisService.getGroupDRDPSettingMap(agencyId)).thenReturn(drdpSettingMap);

        // 时区
        final String timezone = "America/Los_Angeles";
        when(centerDao.getFirstCenterTimeZoneByAgencyId(agencyId)).thenReturn(timezone);

        // 机构信息
        final AgencyEntity agency = new AgencyEntity();
        agency.setId(agencyId);
        agency.setName("agency");
        agency.setState("CA");
        when(agencyDao.getById(agencyId)).thenReturn(agency);

        // 管理员
        final UserModel owner = new UserModel();
        owner.setEmail("<EMAIL>");
        owner.setRole(UserRole.AGENCY_OWNER.toString());
        owner.setDisplayName("micro owner");
        when(userDao.getAgencyAdminsByAgencyId(agencyId)).thenReturn(Collections.singletonList(owner));

        // 学校
        final CenterEntity center = new CenterEntity();
        center.setId(centerId);
        center.setName("center");

        // 班级
        final com.learninggenie.common.data.model.GroupEntity group = new com.learninggenie.common.data.model.GroupEntity();
        group.setId(groupId);
        group.setName("group");
        group.setCenter(center);
        when(groupDao.getGroupWithCenter(groupId)).thenReturn(group);

        // 框架
        when(ratingService.isPSFramework(frameworkId)).thenReturn(true);

        // 学校无别名
        when(centerDao.getMeta(centerId, CenterMetaKey.DRDP_CENTER_ALIAS.toString() + "_" + drdpSetting.getId().toUpperCase())).thenReturn(null);

        // 小孩
        for (CheckedChild checkedChild : checkedChildren) {
            final String childId = checkedChild.getId();
            // 基本信息
            final EnrollmentModel child = new EnrollmentModel();
            child.setFirstName("first");
            child.setLastName("last");
            child.setBirthDate("2020-03-15");
            child.setEnrollmentDate("01/01/2022");
            child.setGender("MALE");
            when(studentDao.getEnrollmentWithDelete(childId)).thenReturn(child);

            // 属性
            List<StudentAttrEntity> attrs = new ArrayList<>();
            StudentAttrEntity attr = new StudentAttrEntity();
            attr.setAttrId(UUID.randomUUID().toString());
            attr.setAttrName("Teacher");
            attr.setAttrValue("<EMAIL>");
            attr.setAttrTypeValue("attrType");
            attr.setEnrollmentId("enrollmentId");

            attrs.add(attr);
            when(studentDao.getAttrsByChildIds(Collections.singletonList(childId))).thenReturn(attrs);

            // 评分
            when(scoreDao.gerChildScore(eq(childId), anyString(), anyString())).thenReturn(new ArrayList<>());

            // 快照
            final StudentSnapshotEntity studentSnapshotEntity = new StudentSnapshotEntity();
            studentSnapshotEntity.setId(UUID.randomUUID().toString());
            studentSnapshotEntity.setFrameworkId(frameworkId);
            when(studentDao.getSnapshotByChildIdAndLockGroupIdAndAlias(childId, groupId, lockAlias)).thenReturn(null);
            when(studentDao.getSnapshot(childId, lockAlias)).thenReturn(studentSnapshotEntity);

            // 增加评分周期
            final List<RatingPeriodEntity> ratingPeriodEntityList = new ArrayList<>();

            // 创建第一个 RatingPeriodEntity 对象
            final RatingPeriodEntity ratingPeriodEntity1 = new RatingPeriodEntity();
            ratingPeriodEntity1.setId("1");
            ratingPeriodEntity1.setAlias("RP1");
            ratingPeriodEntity1.setDisplayAlias("Rating Period 1");
            ratingPeriodEntity1.setFromAtLocal(new Date());
            ratingPeriodEntity1.setToAtLocal(new Date(System.currentTimeMillis() + 86400000)); // 1 day later
            ratingPeriodEntity1.setCreateAtUtc(new Date());
            ratingPeriodEntity1.setUpdateAtUtc(new Date());
            ratingPeriodEntity1.setEnrollmentId("enrollment1");
            ratingPeriodEntity1.setActived(true);
            ratingPeriodEntity1.setCompletedAtUtc(new Date());
            ratingPeriodEntity1.setCompletedAtLocal(new Date());
            ratingPeriodEntity1.setRealCompletedAtLocal(new Date());
            ratingPeriodEntity1.setSortIndex(1);
            ratingPeriodEntity1.setFromAtLocalString("2022-01-01 00:00:00");
            ratingPeriodEntity1.setToAtLocalString("2022-01-31 23:59:59");
            ratingPeriodEntity1.setCompletionDate("2022-01-31");
            ratingPeriodEntity1.setNotEdit(false);
            ratingPeriodEntity1.setNew(true);
            ratingPeriodEntity1.setDomainId("domain1");
            ratingPeriodEntity1.setFrameWorkName("framework1");
            ratingPeriodEntity1.setMinDateString("2022-01-01");
            ratingPeriodEntity1.setMaxDateString("2022-01-31");
            ratingPeriodEntity1.setLocked(false);
            ratingPeriodEntity1.setLockPeriod(false);
            ratingPeriodEntity1.setLockUserId("user1");
            ratingPeriodEntity1.setLockAtLocal(new Date());
            ratingPeriodEntity1.setAgencyId("agency1");
            ratingPeriodEntity1.setChangeAlias(false);
            ratingPeriodEntity1.setAfterAlias(false);
            ratingPeriodEntity1.setPreAlias(false);
            ratingPeriodEntity1.setPreAliasStr("Previous Alias");
            ratingPeriodEntity1.setEntrysDate("2022-01-01");
            ratingPeriodEntity1.setHasRatingFrom("2022-01-01");
            ratingPeriodEntity1.setHasRatingTo("2022-01-31");
            ratingPeriodEntityList.add(ratingPeriodEntity1);

            // 创建第二个 RatingPeriodEntity 对象
            final RatingPeriodEntity ratingPeriodEntity2 = new RatingPeriodEntity();
            ratingPeriodEntity2.setId("2");
            ratingPeriodEntity2.setAlias("RP2");
            ratingPeriodEntity2.setDisplayAlias("Rating Period 2");
            ratingPeriodEntity2.setFromAtLocal(new Date(System.currentTimeMillis() + 86400000)); // 1 day later
            ratingPeriodEntity2.setToAtLocal(new Date(System.currentTimeMillis() + 172800000)); // 2 days later
            ratingPeriodEntity2.setCreateAtUtc(new Date());
            ratingPeriodEntity2.setUpdateAtUtc(new Date());
            ratingPeriodEntity2.setEnrollmentId("enrollment2");
            ratingPeriodEntity2.setActived(true);
            ratingPeriodEntity2.setCompletedAtUtc(new Date());
            ratingPeriodEntity2.setCompletedAtLocal(new Date());
            ratingPeriodEntity2.setRealCompletedAtLocal(new Date());
            ratingPeriodEntity2.setSortIndex(2);
            ratingPeriodEntity2.setFromAtLocalString("2022-02-01 00:00:00");
            ratingPeriodEntity2.setToAtLocalString("2022-02-02 23:59:59");
            ratingPeriodEntity2.setCompletionDate("2022-02-02");
            ratingPeriodEntity2.setNotEdit(false);
            ratingPeriodEntity2.setNew(true);
            ratingPeriodEntity2.setDomainId("domain2");
            ratingPeriodEntity2.setFrameWorkName("framework2");
            ratingPeriodEntity2.setMinDateString("2022-02-01");
            ratingPeriodEntity2.setMaxDateString("2022-02-02");
            ratingPeriodEntity2.setLocked(false);
            ratingPeriodEntity2.setLockPeriod(false);
            ratingPeriodEntity2.setLockUserId("user2");
            ratingPeriodEntity2.setLockAtLocal(new Date());
            ratingPeriodEntity2.setAgencyId("agency2");
            ratingPeriodEntity2.setChangeAlias(false);
            ratingPeriodEntity2.setAfterAlias(false);
            ratingPeriodEntity2.setPreAlias(false);
            ratingPeriodEntity2.setPreAliasStr("Previous Alias 2");
            ratingPeriodEntity2.setEntrysDate("2022-02-01");
            ratingPeriodEntity2.setHasRatingFrom("2022-02-01");
            ratingPeriodEntity2.setHasRatingTo("2022-02-02");
            ratingPeriodEntityList.add(ratingPeriodEntity2);
            when(studentDao.getAllPeriodsByAlias(childId, lockAlias)).thenReturn(ratingPeriodEntityList);

            // 循环 ratingPeriodEntityList
            for (RatingPeriodEntity ratingPeriodEntity : ratingPeriodEntityList) {
                // 生成 ScoreList
                final List<StudentScoreEntity> studentScoreEntityList = new ArrayList<>();

                // 创建第一个StudentScoreEntity对象
                final StudentScoreEntity studentScoreEntity1 = new StudentScoreEntity();
                studentScoreEntity1.setId("1");
                studentScoreEntity1.setStudentId(childId);
                studentScoreEntity1.setNoteId("1001");
                studentScoreEntity1.setDomainId("10");
                studentScoreEntity1.setOldDomainId("20");
                studentScoreEntity1.setLevelId("1");
                studentScoreEntity1.setComment("good job");
                studentScoreEntity1.setCreateByUserId("user1");
                studentScoreEntity1.setCreateAtUtc("2022-01-01 10:00:00");
                studentScoreEntity1.setCreateAtUtcDate(TimeUtil.parse("2022-01-01 10:00:00", TimeUtil.dateFormat));
                studentScoreEntity1.setGroupId("group1");
                studentScoreEntity1.setCreateAtLocal("2022-01-01 18:00:00");
                studentScoreEntity1.setCreateAtLocalDate(TimeUtil.parse("2022-01-01 18:00:00", TimeUtil.dateFormat));
                studentScoreEntityList.add(studentScoreEntity1);

                // 创建第二个StudentScoreEntity对象
                final StudentScoreEntity studentScoreEntity2 = new StudentScoreEntity();
                studentScoreEntity2.setId("2");
                studentScoreEntity2.setStudentId(childId);
                studentScoreEntity2.setNoteId("1002");
                studentScoreEntity2.setDomainId("20");
                studentScoreEntity2.setOldDomainId("30");
                studentScoreEntity2.setLevelId("2");
                studentScoreEntity2.setComment("well done");
                studentScoreEntity2.setCreateByUserId("user2");
                studentScoreEntity2.setCreateAtUtc("2022-01-02 10:00:00");
                studentScoreEntity2.setCreateAtUtcDate(TimeUtil.parse("2022-01-02 10:00:00", TimeUtil.dateFormat));
                studentScoreEntity2.setGroupId("group2");
                studentScoreEntity2.setCreateAtLocal("2022-01-02 18:00:00");
                studentScoreEntity2.setCreateAtLocalDate(TimeUtil.parse("2022-01-02 18:00:00", TimeUtil.dateFormat));
                studentScoreEntityList.add(studentScoreEntity2);

                // 创建第三个StudentScoreEntity对象
                final StudentScoreEntity studentScoreEntity3 = new StudentScoreEntity();
                studentScoreEntity3.setId("3");
                studentScoreEntity3.setStudentId("10003");
                studentScoreEntity3.setNoteId("1003");
                studentScoreEntity3.setDomainId("30");
                studentScoreEntity3.setOldDomainId("40");
                studentScoreEntity3.setLevelId("3");
                studentScoreEntity3.setComment("excellent");
                studentScoreEntity3.setCreateByUserId("user3");
                studentScoreEntity3.setCreateAtUtc("2022-01-03 10:00:00");
                studentScoreEntity3.setCreateAtUtcDate(TimeUtil.parse("2022-01-03 10:00:00", TimeUtil.dateFormat));
                studentScoreEntity3.setGroupId("group3");
                studentScoreEntity3.setCreateAtLocal("2022-01-03 18:00:00");
                studentScoreEntity3.setCreateAtLocalDate(TimeUtil.parse("2022-01-03 18:00:00", TimeUtil.dateFormat));
                studentScoreEntityList.add(studentScoreEntity3);
                when(scoreDao.gerChildScore(childId, TimeUtil.format(ratingPeriodEntity.getFromAtLocal()), TimeUtil.format(ratingPeriodEntity.getToAtLocal())))
                        .thenReturn(studentScoreEntityList);
            }
        }

        // 老师列表
        final List<UserEntity> teachers = new ArrayList<>();
        final UserEntity teacher1 = new UserEntity();
        teacher1.setEmail("<EMAIL>");
        teacher1.setRole(UserRole.TEACHER.toString());
        teacher1.setFirstName("micro");
        teacher1.setLastName("teacher");
        teachers.add(teacher1);
        when(groupDao.getTeacherByGroupId(groupId)).thenReturn(teachers);

        GetAllAgencyResponse getAllAgencyResponse = new GetAllAgencyResponse();
        List<DrdpAgencyModel> drdpAgencyModels = Lists.newArrayList();
        DrdpAgencyModel drdpAgency = new DrdpAgencyModel();
        drdpAgency.setId(1);
        drdpAgency.setAgencyName("agency");

        drdpAgencyModels.add(drdpAgency);
        getAllAgencyResponse.setAgencies(drdpAgencyModels);

        when(drdpService.getAllAgencies()).thenReturn(getAllAgencyResponse);

        GetAgencyTermsResponse agencyTerms = new GetAgencyTermsResponse();
        List<DrdpTermModel> terms = Lists.newArrayList();
        DrdpTermModel drdpTerm1 = new DrdpTermModel();
        drdpTerm1.setId(1);
        drdpTerm1.setName("Spring 2022");
        drdpTerm1.setStartDate("2023-10-01T00:00:00");
        drdpTerm1.setEndDate(TimeUtil.format(TimeUtil.addDays(new Date(), 1), TimeUtil.format25));

        terms.add(drdpTerm1);

        agencyTerms.setTerms(terms);
        when(drdpService.getAgencyTerms(anyString(), anyString())).thenReturn(agencyTerms);

        GetAgencySitesResponse getAgencySiteResponse = new GetAgencySitesResponse();
        List<DrdpSiteModel> drdpSites = Lists.newArrayList();
        DrdpSiteModel drdpSite = new DrdpSiteModel();
        drdpSite.setId(1);
        drdpSite.setAgencyId(1);
        drdpSite.setSiteName("center");
        drdpSites.add(drdpSite);
        getAgencySiteResponse.setSites(drdpSites);
        when(drdpService.getAgencySites(anyString(), eq(null), eq(null))).thenReturn(getAgencySiteResponse);

        GetStateCountiesResponse stateCounties = new GetStateCountiesResponse();
        List<String> countyNames = Lists.newArrayList();
        countyNames.add("Los Angeles");
        stateCounties.setCountyNames(countyNames);

        when(centerService.getStateCounties(anyString())).thenReturn(stateCounties);

        GetDrdpStatesResponse caStateResponse = new GetDrdpStatesResponse();
        List<DrdpStateModel> drdpStates = Lists.newArrayList();
        DrdpStateModel drdpState = new DrdpStateModel();
        drdpState.setId(1);
        drdpState.setStateName("CA");
        drdpStates.add(drdpState);

        caStateResponse.setStates(drdpStates);
        caStateResponse.setStatus(200);

        when(drdpService.getStates(anyString())).thenReturn(caStateResponse);

        GetDrdpUsersResponse drdpServiceUsers = new GetDrdpUsersResponse();
        List<DrdpUserModel> drdpUsers = Lists.newArrayList();
        DrdpUserModel drdpUser = new DrdpUserModel();
        drdpUser.setId(1);
        drdpUser.setEmail("<EMAIL>");
        drdpUser.setRoles(Lists.newArrayList());
        drdpUsers.add(drdpUser);
        drdpServiceUsers.setUsers(drdpUsers);
        drdpServiceUsers.setStatus(200);
        when(drdpService.getUsers(anyString(), anyString())).thenReturn(drdpServiceUsers);

        UpdateDrdpUserResponse updateDrdpUserResponse = new UpdateDrdpUserResponse();
        updateDrdpUserResponse.setId(1);
        updateDrdpUserResponse.setStatus(200);
        when(drdpService.updateUser(anyString(), any(UpdateDrdpUserModel.class))).thenReturn(updateDrdpUserResponse);

        AddDrdpUserRoleResponse addDrdpUserRoleResponse = new AddDrdpUserRoleResponse();
        addDrdpUserRoleResponse.setId(1);
        addDrdpUserRoleResponse.setStatus(200);
        when(drdpService.addUserRole(anyString(), anyString())).thenReturn(addDrdpUserRoleResponse);

        GetSiteClassroomsResponse classroomModelResponse = new GetSiteClassroomsResponse();
        List<DrdpClassroomModel> drdpClassrooms = Lists.newArrayList();
        DrdpClassroomModel drdpClassroom = new DrdpClassroomModel();
        drdpClassroom.setId(1);
        drdpClassroom.setClassroomName("group");
        drdpClassrooms.add(drdpClassroom);
        classroomModelResponse.setClassrooms(drdpClassrooms);
        when(drdpService.getSiteClassrooms(anyString(), anyString(), anyString())).thenReturn(classroomModelResponse);

        GetClassroomClassesResponse classroomClasses = new GetClassroomClassesResponse();
        List<DrdpClassModel> drdpClasses = Lists.newArrayList();
        DrdpClassModel drdpClass = new DrdpClassModel();
        drdpClass.setId(1);
        drdpClass.setClassroomName("group");
        drdpClass.setTermId(1);
        drdpClasses.add(drdpClass);
        classroomClasses.setClasses(drdpClasses);
        classroomClasses.setStatus(200);
        when(drdpService.getClassroomClasses(anyString(), anyString())).thenReturn(classroomClasses);

        UpdateDrdpClassResponse updateDrdpClassResponse = new UpdateDrdpClassResponse();
        updateDrdpClassResponse.setId(1);
        updateDrdpClassResponse.setStatus(200);
        when(drdpService.updateClass(anyString(), any(UpdateDrdpClassModel.class))).thenReturn(updateDrdpClassResponse);

        // 设置批量评分的返回值
        DrdpBatchScoringResponse drdpBatchScoringResponse = new DrdpBatchScoringResponse();
        drdpBatchScoringResponse.setId(1);
        drdpBatchScoringResponse.setStatus(HttpStatus.OK.value());

        when(drdpService.batchScoring(any(DrdpBatchScoringModel.class), anyBoolean())).thenReturn(drdpBatchScoringResponse);
        // 锁定并上传
        final BatchLockResponse response = scoreService.batchLockAndUpload(jobId);

        // 有老师时不会查询学校下其他老师
        verify(userDao, times(0)).getTeachersByCenterId(centerId);
        // 有老师时不会查询园长
        verify(userDao, times(0)).getSiteAdminsByCenterIds(anyList());
        // 验证 response.getLockedCount();
        // 对Response对象的属性和方法进行断言，验证其返回值是否与预期相同
        assertEquals(0, response.getLockedCount());
        assertEquals(0, response.getOnlyLockedCount());
        assertEquals(0, response.getErrorChildCount());
        assertEquals(0, response.getNeedFixChildCount());
        assertEquals(0, response.getAliasErrorCount());
        assertEquals(0, response.getClassErrorCount());
    }

    @Test
    public void testBatchLockAndUploadOld() {
        // 任务 ID
        final String jobId = UUID.randomUUID().toString();
        // 锁定周期
        final String lockAlias = "2021-2022 Fall";
        // 用户 ID
        final String userId = UUID.randomUUID().toString();
        // 机构 ID
        final String agencyId = UUID.randomUUID().toString();
        // 学校 ID
        final String centerId = UUID.randomUUID().toString();
        // 班级 ID
        final String groupId = UUID.randomUUID().toString();
        // 框架 ID
        final String frameworkId = "domainId1";
        // 锁定参数
        final LockJobArgs lockJobArgs = new LockJobArgs();
        lockJobArgs.setAgencyId(agencyId);
        lockJobArgs.setPeriodAlias(lockAlias);
        lockJobArgs.setUserId(userId);
        lockJobArgs.setUploadLockedData(true);
        // 检查结果
        final LockJobResult lockJobResult = new LockJobResult();
        final List<CheckedGroup> checkedGroups = new ArrayList<>();
        // 班级信息
        final CheckedGroup checkedGroup = new CheckedGroup();
        checkedGroup.setId(groupId);
        checkedGroup.setPeriodAlias(lockAlias);
        checkedGroup.setFrameworkId(frameworkId);
        checkedGroups.add(checkedGroup);
        lockJobResult.setCheckedGroups(checkedGroups);

        // 学生信息
        final List<CheckedChild> checkedChildren = new ArrayList<>();
        checkedGroup.setCheckedChildren(checkedChildren);
        // 评分、属性都完成
        final String child1Id = "child-1";
        final CheckedChild checkedChild1 = new CheckedChild();
        checkedChild1.setId(child1Id);
        checkedChildren.add(checkedChild1);

        // 任务信息
        final JobEntity job = new JobEntity();
        job.setArgs(JsonUtil.toJson(lockJobArgs));
        job.setResult(JsonUtil.toJson(lockJobResult));
        when(jobDao.getJobById(jobId)).thenReturn(job);

        // 功能开关为开
        when(userProvider.getAgencyOpenDefaultOpen(agencyId, AgencyMetaKey.DRDP_UPLOAD.toString())).thenReturn(true);

        // 上传 DRDP 设置为打开
        final AgencyMetaDataEntity uploadMeta = new AgencyMetaDataEntity();
        uploadMeta.setMetaValue(DrdpSettingValue.DRDP_UPLOAD_OPEN.toString());
        when(agencyDao.getMeta(agencyId, DrdpSettingKey.UPLOAD_DRDP_SETTING.toString())).thenReturn(uploadMeta);

        // 框架设置
        final List<FrameworkType> frameworkTypes = new ArrayList<>();
        frameworkTypes.add(new FrameworkType("IT", "ITC"));
        frameworkTypes.add(new FrameworkType("PS", "PSC"));
        frameworkTypes.add(new FrameworkType("K", "KC"));
        frameworkTypes.add(new FrameworkType("SA", "SAC"));

        // 上传设置信息
        final List<DRDPSetting> drdpSettings = new ArrayList<>();
        final DRDPSetting drdpSetting = new DRDPSetting();
        drdpSetting.setId(UUID.randomUUID().toString());
        drdpSetting.setComplete(true);
        drdpSetting.setState("CA");
        // 上传学校、班级设置
        final List<CenterSetting> centerSettings = new ArrayList<>();
        final CenterSetting centerSetting = new CenterSetting();
        centerSetting.getGroupIds().add(groupId);
        centerSettings.add(centerSetting);
        drdpSetting.setCenterSettings(centerSettings);
        final CenterSettingData centerSettingData = new CenterSettingData();
        centerSettingData.setCenterSettings(centerSettings);
        centerSettingData.setFrameworkTypes(frameworkTypes);
        drdpSetting.setCenterSetting(JsonUtil.toJson(centerSettingData));
        drdpSettings.add(drdpSetting);
//        when(agencyDao.getDRDPSettingByAgency(agencyId)).thenReturn(drdpSettings);

        Map<String, DRDPSetting> drdpSettingMap = new HashMap<>();
        drdpSettingMap.put(groupId.toUpperCase(), drdpSetting);
        when(analysisService.getGroupDRDPSettingMap(agencyId)).thenReturn(drdpSettingMap);

        // 时区
        final String timezone = "America/Los_Angeles";
        when(centerDao.getFirstCenterTimeZoneByAgencyId(agencyId)).thenReturn(timezone);

        // 机构信息
        final AgencyEntity agency = new AgencyEntity();
        agency.setId(agencyId);
        agency.setName("agency");
        agency.setState("CA");
        when(agencyDao.getById(agencyId)).thenReturn(agency);

        // 管理员
        final UserModel owner = new UserModel();
        owner.setEmail("<EMAIL>");
        owner.setRole(UserRole.AGENCY_OWNER.toString());
        owner.setDisplayName("micro owner");
        when(userDao.getAgencyAdminsByAgencyId(agencyId)).thenReturn(Collections.singletonList(owner));

        // 学校
        final CenterEntity center = new CenterEntity();
        center.setId(centerId);
        center.setName("center");

        // 班级
        final com.learninggenie.common.data.model.GroupEntity group = new com.learninggenie.common.data.model.GroupEntity();
        group.setId(groupId);
        group.setName("group");
        group.setCenter(center);
        when(groupDao.getGroupWithCenter(groupId)).thenReturn(group);

        // 框架
        when(ratingService.isPSFramework(frameworkId)).thenReturn(true);

        // 学校无别名
//        when(centerDao.getMeta(centerId, CenterMetaKey.DRDP_CENTER_ALIAS.toString())).thenReturn(null);

        // 小孩
        for (CheckedChild checkedChild : checkedChildren) {
            final String childId = checkedChild.getId();
            // 基本信息
            final EnrollmentModel child = new EnrollmentModel();
            child.setFirstName("first");
            child.setLastName("last");
            child.setBirthDate("2020-03-15");
            child.setEnrollmentDate("01/01/2022");
            child.setGender("female");
            when(studentDao.getEnrollmentWithDelete(childId)).thenReturn(child);

            // 属性
            List<StudentAttrEntity> attrs = new ArrayList<>();
            StudentAttrEntity attr = new StudentAttrEntity();
            attr.setAttrId(UUID.randomUUID().toString());
            attr.setAttrName("Teacher");
            attr.setAttrValue("<EMAIL>");
            attr.setAttrTypeValue("attrType");
            attr.setEnrollmentId("enrollmentId");

            attrs.add(attr);
            when(studentDao.getAttrsByChildIds(Collections.singletonList(childId))).thenReturn(attrs);

            // 评分
            when(scoreDao.gerChildScore(eq(childId), anyString(), anyString())).thenReturn(new ArrayList<>());

            // 增加评分周期
            final List<RatingPeriodEntity> ratingPeriodEntityList = new ArrayList<>();

            // 创建第一个 RatingPeriodEntity 对象
            final RatingPeriodEntity ratingPeriodEntity1 = new RatingPeriodEntity();
            ratingPeriodEntity1.setId("1");
            ratingPeriodEntity1.setAlias("RP1");
            ratingPeriodEntity1.setDisplayAlias("Rating Period 1");
            ratingPeriodEntity1.setFromAtLocal(new Date());
            ratingPeriodEntity1.setToAtLocal(new Date(System.currentTimeMillis() + 86400000)); // 1 day later
            ratingPeriodEntity1.setCreateAtUtc(new Date());
            ratingPeriodEntity1.setUpdateAtUtc(new Date());
            ratingPeriodEntity1.setEnrollmentId("enrollment1");
            ratingPeriodEntity1.setActived(true);
            ratingPeriodEntity1.setCompletedAtUtc(new Date());
            ratingPeriodEntity1.setCompletedAtLocal(new Date());
            ratingPeriodEntity1.setRealCompletedAtLocal(new Date());
            ratingPeriodEntity1.setSortIndex(1);
            ratingPeriodEntity1.setFromAtLocalString("2022-01-01 00:00:00");
            ratingPeriodEntity1.setToAtLocalString("2022-01-31 23:59:59");
            ratingPeriodEntity1.setCompletionDate("2022-01-31");
            ratingPeriodEntity1.setNotEdit(false);
            ratingPeriodEntity1.setNew(true);
            ratingPeriodEntity1.setDomainId("domainId1");
            ratingPeriodEntity1.setFrameWorkName("framework1");
            ratingPeriodEntity1.setMinDateString("2022-01-01");
            ratingPeriodEntity1.setMaxDateString("2022-01-31");
            ratingPeriodEntity1.setLocked(false);
            ratingPeriodEntity1.setLockPeriod(false);
            ratingPeriodEntity1.setLockUserId("user1");
            ratingPeriodEntity1.setLockAtLocal(new Date());
            ratingPeriodEntity1.setAgencyId("agency1");
            ratingPeriodEntity1.setChangeAlias(false);
            ratingPeriodEntity1.setAfterAlias(false);
            ratingPeriodEntity1.setPreAlias(false);
            ratingPeriodEntity1.setPreAliasStr("Previous Alias");
            ratingPeriodEntity1.setEntrysDate("2022-01-01");
            ratingPeriodEntity1.setHasRatingFrom("2022-01-01");
            ratingPeriodEntity1.setHasRatingTo("2022-01-31");
            ratingPeriodEntityList.add(ratingPeriodEntity1);

            // 创建第二个 RatingPeriodEntity 对象
            final RatingPeriodEntity ratingPeriodEntity2 = new RatingPeriodEntity();
            ratingPeriodEntity2.setId("2");
            ratingPeriodEntity2.setAlias("RP2");
            ratingPeriodEntity2.setDisplayAlias("Rating Period 2");
            ratingPeriodEntity2.setFromAtLocal(new Date(System.currentTimeMillis() + 86400000)); // 1 day later
            ratingPeriodEntity2.setToAtLocal(new Date(System.currentTimeMillis() + 172800000)); // 2 days later
            ratingPeriodEntity2.setCreateAtUtc(new Date());
            ratingPeriodEntity2.setUpdateAtUtc(new Date());
            ratingPeriodEntity2.setEnrollmentId("enrollment2");
            ratingPeriodEntity2.setActived(true);
            ratingPeriodEntity2.setCompletedAtUtc(new Date());
            ratingPeriodEntity2.setCompletedAtLocal(new Date());
            ratingPeriodEntity2.setRealCompletedAtLocal(new Date());
            ratingPeriodEntity2.setSortIndex(2);
            ratingPeriodEntity2.setFromAtLocalString("2022-02-01 00:00:00");
            ratingPeriodEntity2.setToAtLocalString("2022-02-02 23:59:59");
            ratingPeriodEntity2.setCompletionDate("2022-02-02");
            ratingPeriodEntity2.setNotEdit(false);
            ratingPeriodEntity2.setNew(true);
            ratingPeriodEntity2.setDomainId("domainId2");
            ratingPeriodEntity2.setFrameWorkName("framework2");
            ratingPeriodEntity2.setMinDateString("2022-02-01");
            ratingPeriodEntity2.setMaxDateString("2022-02-02");
            ratingPeriodEntity2.setLocked(false);
            ratingPeriodEntity2.setLockPeriod(false);
            ratingPeriodEntity2.setLockUserId("user2");
            ratingPeriodEntity2.setLockAtLocal(new Date());
            ratingPeriodEntity2.setAgencyId("agency2");
            ratingPeriodEntity2.setChangeAlias(false);
            ratingPeriodEntity2.setAfterAlias(false);
            ratingPeriodEntity2.setPreAlias(false);
            ratingPeriodEntity2.setPreAliasStr("Previous Alias 2");
            ratingPeriodEntity2.setEntrysDate("2022-02-01");
            ratingPeriodEntity2.setHasRatingFrom("2022-02-01");
            ratingPeriodEntity2.setHasRatingTo("2022-02-02");
            ratingPeriodEntityList.add(ratingPeriodEntity2);
            when(studentDao.getAllPeriodsByAlias(childId, lockAlias)).thenReturn(ratingPeriodEntityList);

            // 循环 ratingPeriodEntityList
            for (RatingPeriodEntity ratingPeriodEntity : ratingPeriodEntityList) {
                // 生成 ScoreList
                final List<StudentScoreEntity> studentScoreEntityList = new ArrayList<>();

                // 创建第一个StudentScoreEntity对象
                final StudentScoreEntity studentScoreEntity1 = new StudentScoreEntity();
                studentScoreEntity1.setId("1");
                studentScoreEntity1.setStudentId(childId);
                studentScoreEntity1.setNoteId("1001");
                studentScoreEntity1.setDomainId("10");
                studentScoreEntity1.setOldDomainId("20");
                studentScoreEntity1.setLevelId("1");
                studentScoreEntity1.setComment("good job");
                studentScoreEntity1.setCreateByUserId("user1");
                studentScoreEntity1.setCreateAtUtc("2021-11-01 10:00:00");
                studentScoreEntity1.setCreateAtUtcDate(TimeUtil.parse("2021-11-01 10:00:00", TimeUtil.dateFormat));
                studentScoreEntity1.setGroupId("group1");
                studentScoreEntity1.setCreateAtLocal("2021-11-01 18:00:00");
                studentScoreEntity1.setCreateAtLocalDate(TimeUtil.parse("2021-11-01 18:00:00", TimeUtil.dateFormat));
                studentScoreEntityList.add(studentScoreEntity1);

                // 创建第二个StudentScoreEntity对象
                final StudentScoreEntity studentScoreEntity2 = new StudentScoreEntity();
                studentScoreEntity2.setId("2");
                studentScoreEntity2.setStudentId(childId);
                studentScoreEntity2.setNoteId("1002");
                studentScoreEntity2.setDomainId("20");
                studentScoreEntity2.setOldDomainId("30");
                studentScoreEntity2.setLevelId("2");
                studentScoreEntity2.setComment("well done");
                studentScoreEntity2.setCreateByUserId("user2");
                studentScoreEntity2.setCreateAtUtc("2021-11-02 10:00:00");
                studentScoreEntity2.setCreateAtUtcDate(TimeUtil.parse("2021-11-02 10:00:00", TimeUtil.dateFormat));
                studentScoreEntity2.setGroupId("group2");
                studentScoreEntity2.setCreateAtLocal("2021-11-02 18:00:00");
                studentScoreEntity2.setCreateAtLocalDate(TimeUtil.parse("2021-11-02 18:00:00", TimeUtil.dateFormat));
                studentScoreEntityList.add(studentScoreEntity2);

                // 创建第三个StudentScoreEntity对象
                final StudentScoreEntity studentScoreEntity3 = new StudentScoreEntity();
                studentScoreEntity3.setId("3");
                studentScoreEntity3.setStudentId("10003");
                studentScoreEntity3.setNoteId("1003");
                studentScoreEntity3.setDomainId("30");
                studentScoreEntity3.setOldDomainId("40");
                studentScoreEntity3.setLevelId("3");
                studentScoreEntity3.setComment("excellent");
                studentScoreEntity3.setCreateByUserId("user3");
                studentScoreEntity3.setCreateAtUtc("2021-11-03 10:00:00");
                studentScoreEntity3.setCreateAtUtcDate(TimeUtil.parse("2021-11-03 10:00:00", TimeUtil.dateFormat));
                studentScoreEntity3.setGroupId("group3");
                studentScoreEntity3.setCreateAtLocal("2021-11-03 18:00:00");
                studentScoreEntity3.setCreateAtLocalDate(TimeUtil.parse("2021-11-03 18:00:00", TimeUtil.dateFormat));
                studentScoreEntityList.add(studentScoreEntity3);
                when(scoreDao.gerChildScore(childId, TimeUtil.format(ratingPeriodEntity.getFromAtLocal()), TimeUtil.format(ratingPeriodEntity.getToAtLocal())))
                        .thenReturn(studentScoreEntityList);
            }
        }

        DrdpStandardRatingPeriodModel standardRatingPeriodModel = new DrdpStandardRatingPeriodModel();
        Map<String, List<DrdpRatingPeriodModel>> standardRatingPeriod = new HashMap<>();
        standardRatingPeriod.put("2021-2022", Arrays.asList(
                new DrdpRatingPeriodModel("Fall 2021", "08/01/2021", "12/31/2021"),
                new DrdpRatingPeriodModel("Winter 2021-22", "11/01/2021", "03/31/2021"),
                new DrdpRatingPeriodModel("Spring 2022", "01/01/2022", "06/30/2022"),
                new DrdpRatingPeriodModel("Summer 2022", "04/01/2022", "08/31/2022")
        ));
        standardRatingPeriodModel.setStandardRatingPeriod(standardRatingPeriod);
        when(metaDao.getAppMeta(AppMetaKey.DRDP_ONLINE_STANDARD_RATING_PERIOD_DATE.toString())).thenReturn(JsonUtil.toJson(standardRatingPeriodModel));


        // 老师列表
        final List<UserEntity> teachers = new ArrayList<>();
        final UserEntity teacher1 = new UserEntity();
        teacher1.setEmail("<EMAIL>");
        teacher1.setRole(UserRole.TEACHER.toString());
        teacher1.setFirstName("micro");
        teacher1.setLastName("teacher");
        teachers.add(teacher1);
        when(groupDao.getTeacherByGroupId(groupId)).thenReturn(teachers);

        GetAllAgencyResponse getAllAgencyResponse = new GetAllAgencyResponse();
        List<DrdpAgencyModel> drdpAgencyModels = Lists.newArrayList();
        DrdpAgencyModel drdpAgency = new DrdpAgencyModel();
        drdpAgency.setId(1);
        drdpAgency.setAgencyName("agency");

        drdpAgencyModels.add(drdpAgency);

        getAllAgencyResponse.setAgencies(drdpAgencyModels);
        when(drdpService.getAllAgencies()).thenReturn(getAllAgencyResponse);

        GetAgencyTermsResponse agencyTerms = new GetAgencyTermsResponse();
        List<DrdpTermModel> terms = Lists.newArrayList();
        DrdpTermModel drdpTerm1 = new DrdpTermModel();
        drdpTerm1.setId(1);
        drdpTerm1.setName("Fall 2021");
        drdpTerm1.setStartDate("2023-10-01T00:00:00");
        drdpTerm1.setEndDate(TimeUtil.format(TimeUtil.addDays(new Date(), 1), TimeUtil.format25));

        terms.add(drdpTerm1);

        agencyTerms.setTerms(terms);
        when(drdpService.getAgencyTerms(anyString(), anyString())).thenReturn(agencyTerms);

        GetAgencySitesResponse getAgencySiteResponse = new GetAgencySitesResponse();
        List<DrdpSiteModel> drdpSites = Lists.newArrayList();
        DrdpSiteModel drdpSite = new DrdpSiteModel();
        drdpSite.setId(1);
        drdpSite.setAgencyId(1);
        drdpSite.setSiteName("center");
        drdpSites.add(drdpSite);
        getAgencySiteResponse.setSites(drdpSites);
        when(drdpService.getAgencySites(anyString(), eq(null), eq(null))).thenReturn(getAgencySiteResponse);

        GetStateCountiesResponse stateCounties = new GetStateCountiesResponse();
        List<String> countyNames = Lists.newArrayList();
        countyNames.add("Los Angeles");
        stateCounties.setCountyNames(countyNames);

        when(centerService.getStateCounties(anyString())).thenReturn(stateCounties);

        GetDrdpStatesResponse caStateResponse = new GetDrdpStatesResponse();
        List<DrdpStateModel> drdpStates = Lists.newArrayList();
        DrdpStateModel drdpState = new DrdpStateModel();
        drdpState.setId(1);
        drdpState.setStateName("CA");
        drdpStates.add(drdpState);

        caStateResponse.setStates(drdpStates);
        caStateResponse.setStatus(200);

        when(drdpService.getStates(anyString())).thenReturn(caStateResponse);

        GetDrdpUsersResponse drdpServiceUsers = new GetDrdpUsersResponse();
        List<DrdpUserModel> drdpUsers = Lists.newArrayList();
        DrdpUserModel drdpUser = new DrdpUserModel();
        drdpUser.setId(1);
        drdpUser.setEmail("<EMAIL>");

        List<DrdpRoleModel> roles = Lists.newArrayList();
        DrdpRoleModel teacherOfRecordRole = new DrdpRoleModel();
        teacherOfRecordRole.setId(Role.TEACHER_OF_RECORD.getRoleId());
        teacherOfRecordRole.setName(Role.TEACHER_OF_RECORD.getName());
        roles.add(teacherOfRecordRole);

        DrdpRoleModel teacherRole = new DrdpRoleModel();
        teacherRole.setId(Role.TEACHER.getRoleId());
        teacherRole.setName(Role.TEACHER.getName());
        roles.add(teacherRole);


        drdpUser.setRoles(Lists.newArrayList());
        drdpUsers.add(drdpUser);
        drdpServiceUsers.setUsers(drdpUsers);
        drdpServiceUsers.setStatus(200);
        when(drdpService.getUsers(anyString(), anyString())).thenReturn(drdpServiceUsers);

        UpdateDrdpUserResponse updateDrdpUserResponse = new UpdateDrdpUserResponse();
        updateDrdpUserResponse.setId(1);
        updateDrdpUserResponse.setStatus(200);
        when(drdpService.updateUser(anyString(), any(UpdateDrdpUserModel.class))).thenReturn(updateDrdpUserResponse);

        AddDrdpUserRoleResponse addDrdpUserRoleResponse = new AddDrdpUserRoleResponse();
        addDrdpUserRoleResponse.setId(1);
        addDrdpUserRoleResponse.setStatus(200);
        when(drdpService.addUserRole(anyString(), anyString())).thenReturn(addDrdpUserRoleResponse);

        GetSiteClassroomsResponse classroomModelResponse = new GetSiteClassroomsResponse();
        List<DrdpClassroomModel> drdpClassrooms = Lists.newArrayList();
        DrdpClassroomModel drdpClassroom = new DrdpClassroomModel();
        drdpClassroom.setId(1);
        drdpClassroom.setClassroomName("group");
        drdpClassrooms.add(drdpClassroom);
        classroomModelResponse.setClassrooms(drdpClassrooms);
        when(drdpService.getSiteClassrooms(anyString(), anyString(), anyString())).thenReturn(classroomModelResponse);

        GetClassroomClassesResponse classroomClasses = new GetClassroomClassesResponse();
        List<DrdpClassModel> drdpClasses = Lists.newArrayList();
        DrdpClassModel drdpClass = new DrdpClassModel();
        drdpClass.setId(1);
        drdpClass.setClassroomName("group");
        drdpClass.setTermId(1);
        drdpClasses.add(drdpClass);
        classroomClasses.setClasses(drdpClasses);
        classroomClasses.setStatus(200);
        when(drdpService.getClassroomClasses(anyString(), anyString())).thenReturn(classroomClasses);

        UpdateDrdpClassResponse updateDrdpClassResponse = new UpdateDrdpClassResponse();
        updateDrdpClassResponse.setId(1);
        updateDrdpClassResponse.setStatus(200);
        when(drdpService.updateClass(anyString(), any(UpdateDrdpClassModel.class))).thenReturn(updateDrdpClassResponse);

        // 设置批量评分的返回值
        DrdpBatchScoringResponse drdpBatchScoringResponse = new DrdpBatchScoringResponse();
        drdpBatchScoringResponse.setId(1);
        drdpBatchScoringResponse.setStatus(HttpStatus.OK.value());

        when(drdpService.batchScoring(any(DrdpBatchScoringModel.class), anyBoolean())).thenReturn(drdpBatchScoringResponse);

        // 锁定并上传
        final BatchLockResponse response = scoreService.batchLockAndUpload(jobId);

        // 有老师时不会查询学校下其他老师
        verify(userDao, times(0)).getTeachersByCenterId(centerId);
        // 有老师时不会查询园长
        verify(userDao, times(0)).getSiteAdminsByCenterIds(anyList());
        // 验证 response.getLockedCount();
        // 对Response对象的属性和方法进行断言，验证其返回值是否与预期相同
        assertEquals(1, response.getLockedCount());
        assertEquals(0, response.getOnlyLockedCount());
        assertEquals(0, response.getErrorChildCount());
        assertEquals(0, response.getNeedFixChildCount());
        assertEquals(0, response.getAliasErrorCount());
        assertEquals(0, response.getClassErrorCount());


        // 清空
        Mockito.reset(groupDao, userDao, jobDao);
        when(userDao.getAgencyAdminsByAgencyId(agencyId)).thenReturn(Collections.singletonList(owner));
        // 班级没有老师
        when(groupDao.getTeacherByGroupId(groupId)).thenReturn(new ArrayList<>());

        // 锁定参数
        lockJobArgs.setUploadLockedData(false);

        // 任务信息
        final JobEntity job2 = new JobEntity();
        job2.setArgs(JsonUtil.toJson(lockJobArgs));
        job2.setResult(JsonUtil.toJson(lockJobResult));
        when(jobDao.getJobById(jobId)).thenReturn(job2);

        // 锁定并上传
        scoreService.batchLockAndUpload(jobId);

        // 学校有其他老师时不会查询园长
        verify(userDao, times(0)).getSiteAdminsByCenterIds(anyList());

        // 清空
        Mockito.reset(groupDao, userDao);
        // 学校有园长
        when(userDao.getAgencyAdminsByAgencyId(agencyId)).thenReturn(Collections.singletonList(owner));

        // 锁定并上传
        scoreService.batchLockAndUpload(jobId);

        // 查询机构管理员
        verify(userDao, times(1)).getAgencyAdminsByAgencyId(agencyId);

    }

    @Test
    public void testGetHasScoreStudent() {
        // 准备测试数据
        List<CenterModel> centerModels = Arrays.asList(new CenterModel(/* 数据参数 */));
        String currentSchoolYear = "2023";
        List<String> stageIds = Arrays.asList("stage1", "stage2");
        String agencyId = "agency1";
        boolean exportClassId = true;
        boolean exportChildId = false;
        List<String> frameworkIds = Arrays.asList("framework1", "framework2");
        List<String> allEnrollmentIds = Arrays.asList("enrollmentId1");

        // 模拟scoreDao.getHasScoreStudent方法的返回值
        HasScoreStudent hasScoreStudent1 = new HasScoreStudent();
        hasScoreStudent1.setEnrollmentId("enrollmentId1");
        hasScoreStudent1.setGroupId("groupId1");
        hasScoreStudent1.setCenterId("centerId1");
        hasScoreStudent1.setLevelId("levelId1");
        hasScoreStudent1.setGroupName("groupName1");
        hasScoreStudent1.setCenterName("centerName1");
        hasScoreStudent1.setStageId("stageId1");
        hasScoreStudent1.setSnapshotId("snapshotId1");
        when(scoreDao.getHasScoreStudent(centerModels, currentSchoolYear, stageIds, agencyId, exportClassId, exportChildId, frameworkIds, Collections.emptyList(), allEnrollmentIds))
                .thenReturn(hasScoreStudent1);

        // 调用被测试的方法
        HasScoreStudent result = scoreService.getHasScoreStudent(centerModels, currentSchoolYear, stageIds, agencyId, exportClassId, exportChildId, frameworkIds, Collections.emptyList(), allEnrollmentIds);

        // 验证调用和断言结果
        verify(scoreDao).getHasScoreStudent(centerModels, currentSchoolYear, stageIds, agencyId, exportClassId, exportChildId, frameworkIds, Collections.emptyList(), allEnrollmentIds);
        assertEquals(hasScoreStudent1, result);
    }

    @Test
    public void testGetHasSnapshotStudent() {
        // 准备测试数据
        List<CenterModel> centerModels = Arrays.asList(new CenterModel(/* 数据参数 */));
        String currentSchoolYear = "2023";
        List<String> stageIds = Arrays.asList("stage1", "stage2");
        String agencyId = "agency1";
        boolean exportClassId = true;
        boolean exportChildId = false;
        List<String> frameworkIds = Arrays.asList("framework1", "framework2");
        List<String> allEnrollmentIds = Arrays.asList("enrollmentId2");

        // 模拟scoreDao.getHasSnapshotStudent方法的返回值
        HasScoreStudent hasScoreStudent2 = new HasScoreStudent();
        hasScoreStudent2.setEnrollmentId("enrollmentId2");
        hasScoreStudent2.setGroupId("groupId2");
        hasScoreStudent2.setCenterId("centerId2");
        hasScoreStudent2.setLevelId("levelId2");
        hasScoreStudent2.setGroupName("groupName2");
        hasScoreStudent2.setCenterName("centerName2");
        hasScoreStudent2.setStageId("stageId2");
        hasScoreStudent2.setSnapshotId("snapshotId2");
        when(scoreDao.getHasSnapshotStudent(centerModels, currentSchoolYear, stageIds, agencyId, exportClassId, exportChildId, frameworkIds, allEnrollmentIds))
                .thenReturn(hasScoreStudent2);

        // 调用被测试的方法
        HasScoreStudent result = scoreService.getHasSnapshotStudent(centerModels, currentSchoolYear, stageIds, agencyId, exportClassId, exportChildId, frameworkIds, allEnrollmentIds);

        // 验证调用和断言结果
        verify(scoreDao).getHasSnapshotStudent(centerModels, currentSchoolYear, stageIds, agencyId, exportClassId, exportChildId, frameworkIds, allEnrollmentIds);
        assertEquals(hasScoreStudent2, result);
    }

    @Test
    public void testGetStudentScoreByEnrollmentIds_EnrollmentIdsEmpty_ReturnsEmptyList() {
        // Arrange
        List<String> enrollmentIds = new ArrayList<>();
        String schoolYear = "2021";

        // Act
        List<StudentScoreModel> result = scoreService.getStudentScoreByEnrollmentIds(enrollmentIds, schoolYear);

        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetStudentScoreByEnrollmentIds_EnrollmentIdsNotEmpty_ReturnsScoreList() {
        // Arrange
        List<String> enrollmentIds = Arrays.asList("enrollmentId1", "enrollmentId2");
        String schoolYear = "2021";
        StudentScoreModel student1 = new StudentScoreModel();
        // set 数据
        student1.setId("1");
        student1.setStudentId("studentId1");
        student1.setLevelId("levelId1");
        StudentScoreModel student2 = new StudentScoreModel();
        student2.setId("2");
        student2.setStudentId("studentId2");
        student2.setLevelId("levelId2");
        List<StudentScoreModel> expectedScores = Arrays.asList(
                student1,
                student2
        );

        // Mock scoreDao
        when(scoreDao.getScoreByEnrollmentIds(enrollmentIds, schoolYear)).thenReturn(expectedScores);

        // Act
        List<StudentScoreModel> result = scoreService.getStudentScoreByEnrollmentIds(enrollmentIds, schoolYear);

        // Assert
        assertEquals(expectedScores, result);
    }

    @Test
    public void testMergeSnapshotAttrs() {
        // 创建输入数据
        LGSnapshot.StudentSnapshot snapshot = Mockito.mock(LGSnapshot.StudentSnapshot.class);
        String childId = "childId";
        List<StudentAttrEntity> attrs = new ArrayList<>();
        StudentAttrEntity attr = new StudentAttrEntity();
        attr.setAttrId(UUID.randomUUID().toString());
        attr.setAttrName("Teacher");
        attr.setAttrValue("<EMAIL>");
        attr.setAttrTypeValue("attrType");
        attr.setEnrollmentId("enrollmentId");

        attrs.add(attr);
        String defaultTeacherOfRecordEmail = "<EMAIL>";

        // 创建快照中属性列表
        List<LGSnapshot.Properties> propertiesList = new ArrayList<>();
        LGSnapshot.Properties property1 = Mockito.mock(LGSnapshot.Properties.class);
        Mockito.when(property1.getName()).thenReturn("Teacher");
        Mockito.when(property1.getValue()).thenReturn("<EMAIL>;<EMAIL>");
        propertiesList.add(property1);
        // 添加更多属性...

        // 设置快照中属性列表的返回值
        Mockito.when(snapshot.getPropertiesList()).thenReturn(propertiesList);

        // 创建测试对象
        // 调用方法进行测试
        List<StudentAttrEntity> mergedAttrs = scoreService.mergeSnapshotAttrs(snapshot, childId, attrs, defaultTeacherOfRecordEmail);

        // 进行断言
        Assert.assertNotNull(mergedAttrs);
        // 添加更多的断言...
    }

    @Test
    public void testMergeSnapshotAttrs_EmptySnapshot() {
        // 创建输入数据
        LGSnapshot.StudentSnapshot snapshot = Mockito.mock(LGSnapshot.StudentSnapshot.class);
        String childId = "childId";
        List<StudentAttrEntity> attrs = new ArrayList<>();
        StudentAttrEntity attr = new StudentAttrEntity();
        attr.setAttrId(UUID.randomUUID().toString());
        attr.setAttrName("Teacher");
        attr.setAttrValue("<EMAIL>");
        attr.setAttrTypeValue("attrType");
        attr.setEnrollmentId("enrollmentId");

        attrs.add(attr);
        String defaultTeacherOfRecordEmail = "<EMAIL>";

        // 创建快照中属性列表
        List<LGSnapshot.Properties> propertiesList = new ArrayList<>();
        LGSnapshot.Properties property1 = Mockito.mock(LGSnapshot.Properties.class);
        Mockito.when(property1.getName()).thenReturn("test");
        Mockito.when(property1.getValue()).thenReturn("<EMAIL>;<EMAIL>");
        propertiesList.add(property1);
        // 添加更多属性...

        // 设置快照中属性列表的返回值
        Mockito.when(snapshot.getPropertiesList()).thenReturn(propertiesList);

        // 创建测试对象
        // 调用方法进行测试
        List<StudentAttrEntity> mergedAttrs = scoreService.mergeSnapshotAttrs(snapshot, childId, attrs, defaultTeacherOfRecordEmail);

        // 进行断言
        Assert.assertNotNull(mergedAttrs);
        // 添加更多的断言...
    }

    @Test
    public void testCreateOrUpdateDrdpUser() {
        // 创建输入数据
        Integer drdpAgencyId = 123;
        String state = "California";
        UserEntity teacher = Mockito.mock(UserEntity.class);
        String teacherOfRecord = "<EMAIL>";
        DrdpUserModel drdpUser = new DrdpUserModel();
        drdpUser.setId(123);
        drdpUser.setFirstName("first name");
        drdpUser.setRoles(Lists.newArrayList());
        UploadDRDPRecord uploadDRDPRecord = Mockito.mock(UploadDRDPRecord.class);
        Boolean needUpdate = true;

        // 创建返回数据
        CreateDrdpUserResponse createDrdpUserResponse = new CreateDrdpUserResponse();
        // 设置返回数据的属性...

        // 创建依赖的模拟对象

        // 设置模拟对象的方法调用返回值...
        GetDrdpStatesResponse statesResponse = mock(GetDrdpStatesResponse.class);
        when(statesResponse.getStatus()).thenReturn(200);
        Mockito.when(drdpService.getStates(Mockito.anyString())).thenReturn(statesResponse);
        UpdateDrdpUserResponse updateDrdpUserResponse = mock(UpdateDrdpUserResponse.class);
        when(updateDrdpUserResponse.getId()).thenReturn(1);
        when(updateDrdpUserResponse.getStatus()).thenReturn(200);
        when(updateDrdpUserResponse.getErrors()).thenReturn(new HashMap<>());
        Mockito.when(drdpService.updateUser(anyString(), Mockito.any(UpdateDrdpUserModel.class)))
                .thenReturn(updateDrdpUserResponse);

        GetDrdpUsersResponse usersResponse = new GetDrdpUsersResponse();
        usersResponse.setStatus(200);
        List<DrdpUserModel> users = new ArrayList<>();
        DrdpUserModel user = new DrdpUserModel();
        user.setId(1);
        user.setRoles(new ArrayList<>());
        users.add(user);

        usersResponse.setUsers(users);
        when(drdpService.getUsers(anyString(), anyString())).thenReturn(usersResponse);


        AddDrdpUserRoleResponse roleResponse = new AddDrdpUserRoleResponse();
        roleResponse.setId(1);
        roleResponse.setStatus(200);
        when(drdpService.addUserRole(anyString(), anyString())).thenReturn(roleResponse);
        // 调用方法进行测试
        CreateDrdpUserResponse result = scoreService.createOrUpdateDrdpUser(drdpAgencyId, state, teacher, teacherOfRecord, drdpUser, uploadDRDPRecord, needUpdate);

        // 进行断言
        Assert.assertNotNull(result);
        // 添加更多的断言...
    }


    @Test
    public void testCreateOrUpdateDrdpUser_Create() {
        // 创建输入数据
        Integer drdpAgencyId = 123;
        String state = "California";
        UserEntity teacher = Mockito.mock(UserEntity.class);
        String teacherOfRecord = "<EMAIL>";
        DrdpUserModel drdpUser = Mockito.mock(DrdpUserModel.class);
        UploadDRDPRecord uploadDRDPRecord = Mockito.mock(UploadDRDPRecord.class);
        Boolean needUpdate = false;

        // 创建返回数据
        CreateDrdpUserResponse createDrdpUserResponse = new CreateDrdpUserResponse();
        // 设置返回数据的属性...

        // 创建依赖的模拟对象
        int userId = 123;
        // 设置模拟对象的方法调用返回值...
        GetDrdpStatesResponse statesResponse = mock(GetDrdpStatesResponse.class);
        when(statesResponse.getStatus()).thenReturn(200);
        Mockito.when(drdpService.getStates(Mockito.anyString())).thenReturn(statesResponse);
        CreateDrdpUserResponse updateDrdpUserResponse = mock(CreateDrdpUserResponse.class);
        when(updateDrdpUserResponse.getId()).thenReturn(1);
        when(updateDrdpUserResponse.getErrors()).thenReturn(new HashMap<>());
        when(updateDrdpUserResponse.getStatus()).thenReturn(200);
        Mockito.when(drdpService.createAgencyUser(anyString(), Mockito.any(CreateDrdpAgencyUserModel.class)))
                .thenReturn(updateDrdpUserResponse);

        GetDrdpUsersResponse usersResponse = new GetDrdpUsersResponse();
        List<DrdpUserModel> users = new ArrayList<>();
        DrdpUserModel user = new DrdpUserModel();
        user.setId(1);
        user.setRoles(new ArrayList<>());
        users.add(user);
        usersResponse.setStatus(200);
        usersResponse.setUsers(users);
        when(drdpService.getUsers(updateDrdpUserResponse.getId().toString(), "")).thenReturn(usersResponse);

        AddDrdpUserRoleResponse roleResponse = new AddDrdpUserRoleResponse();
        roleResponse.setId(1);
        roleResponse.setStatus(200);
        when(drdpService.addUserRole(anyString(), anyString())).thenReturn(roleResponse);
        // 调用方法进行测试
        CreateDrdpUserResponse result = scoreService.createOrUpdateDrdpUser(drdpAgencyId, state, teacher, teacherOfRecord, drdpUser, uploadDRDPRecord, needUpdate);

        // 进行断言
        Assert.assertNotNull(result);
        // 添加更多的断言...
    }

    @Test
    public void testGetDrdpSiteByCenterName() {
        // 创建输入数据
        Integer drdpAgencyId = 123;
        String centerName = "Center Name";
        UploadDRDPRecord uploadDRDPRecord = Mockito.mock(UploadDRDPRecord.class);

        // 创建返回数据
        GetAgencySitesResponse response = new GetAgencySitesResponse();
        List<DrdpSiteModel> sites = new ArrayList<>();
        DrdpSiteModel drdpSiteModel = new DrdpSiteModel();
        drdpSiteModel.setAgencyId(1);
        drdpSiteModel.setSiteCodeTypeId(1);
        drdpSiteModel.setSiteTypeId(1);
        drdpSiteModel.setSiteName("site name");
        drdpSiteModel.setIsActive(true);
        drdpSiteModel.setId(1);
        drdpSiteModel.setIsDeleted(false);

        sites.add(drdpSiteModel);
        response.setSites(sites);

        // 设置返回数据的属性...
        // 创建依赖的模拟对象
        // 设置模拟对象的方法调用返回值...
        Mockito.when(drdpService.getAgencySites(Mockito.anyString(), Mockito.isNull(), Mockito.isNull())).thenReturn(response);

        // 调用方法进行测试
        DrdpSiteModel result = scoreService.getDrdpSiteByCenterName(drdpAgencyId, centerName, uploadDRDPRecord);

        // 进行断言
        Assert.assertNull(result);
        // 添加更多的断言...
    }

    @Test
    public void testGetDrdpSiteByCenterName_WithError() {
        // 创建输入数据
        Integer drdpAgencyId = 123;
        String centerName = "Center Name";
        UploadDRDPRecord uploadDRDPRecord = Mockito.mock(UploadDRDPRecord.class);

        // 创建返回数据
        GetAgencySitesResponse response = new GetAgencySitesResponse();

        HashMap<String, List<String>> errors = new HashMap<>();
        errors.put("error", Arrays.asList("error"));
        response.setErrors(errors);
        response.setStatus(200);

        // 设置返回数据的属性...
        // 创建依赖的模拟对象
        // 设置模拟对象的方法调用返回值...
        Mockito.when(drdpService.getAgencySites(Mockito.anyString(), Mockito.isNull(), Mockito.isNull())).thenReturn(response);

        // 调用方法进行测试
        DrdpSiteModel result = scoreService.getDrdpSiteByCenterName(drdpAgencyId, centerName, uploadDRDPRecord);

        // 进行断言
        Assert.assertNull(result);
        // 添加更多的断言...
    }

    @Test
    public void testCreateDrdpAgencySite() {
        // 创建输入数据
        Integer drdpAgencyId = 123;
        CreateDrdpSiteModel createSiteRequest = Mockito.mock(CreateDrdpSiteModel.class);
        UploadDRDPRecord uploadDRDPRecord = Mockito.mock(UploadDRDPRecord.class);

        // 创建返回数据
        CreateDrdpSiteResponse createSiteResponse = new CreateDrdpSiteResponse();
        // 设置返回数据的属性...
        createSiteResponse.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
        HashMap<String, List<String>> errors = new HashMap<>();
        errors.put("error", Arrays.asList("error"));
        createSiteResponse.setErrors(errors);


        // 创建依赖的模拟对象
        // 设置模拟对象的方法调用返回值...
        Mockito.when(drdpService.createAgencySite(Mockito.anyString(), Mockito.any(CreateDrdpSiteModel.class))).thenReturn(createSiteResponse);

        // 调用方法进行测试
        CreateDrdpSiteResponse result = scoreService.createDrdpAgencySite(drdpAgencyId, createSiteRequest, uploadDRDPRecord);

        // 进行断言
        Assert.assertNotNull(result);
        // 添加更多的断言...
    }

    @Test
    public void testCreateOrUpdateDrdpAgencySite_WhenNeedUpdate() {
        // 创建输入数据
        Integer drdpAgencyId = 123;
        CreateOrUpdateDrdpSiteModel createOrUpdateDrdpSiteModel = Mockito.mock(CreateOrUpdateDrdpSiteModel.class);
        DrdpSiteModel drdpSiteModel = Mockito.mock(DrdpSiteModel.class);
        UploadDRDPRecord uploadDRDPRecord = Mockito.mock(UploadDRDPRecord.class);
        Boolean needUpdate = true;

        // 创建返回数据
        CreateDrdpSiteResponse createOrUpdateSiteResponse = new CreateDrdpSiteResponse();
        createOrUpdateSiteResponse.setId(drdpSiteModel.getId());

        // 调用方法进行测试
        CreateDrdpSiteResponse result = scoreService.createOrUpdateDrdpAgencySite(drdpAgencyId, createOrUpdateDrdpSiteModel, drdpSiteModel, uploadDRDPRecord, needUpdate);

        // 进行断言
        Assert.assertNotNull(result);
        // 添加更多的断言...
    }

    @Test
    public void testCreateOrUpdateDrdpAgencySite_WhenNeedCreate() {
        // 创建输入数据
        Integer drdpAgencyId = 123;
        CreateOrUpdateDrdpSiteModel createOrUpdateDrdpSiteModel = Mockito.mock(CreateOrUpdateDrdpSiteModel.class);
        DrdpSiteModel drdpSiteModel = Mockito.mock(DrdpSiteModel.class);
        UploadDRDPRecord uploadDRDPRecord = Mockito.mock(UploadDRDPRecord.class);
        Boolean needUpdate = false;

        // 创建返回数据
        CreateDrdpSiteResponse createOrUpdateSiteResponse = new CreateDrdpSiteResponse();
        createOrUpdateSiteResponse.setStatus(200);
        // 设置返回数据的属性...

        // 创建依赖的模拟对象
        when(drdpService.createAgencySite(anyString(), any(CreateDrdpSiteModel.class))).thenReturn(createOrUpdateSiteResponse);

        // 调用方法进行测试
        CreateDrdpSiteResponse result = scoreService.createOrUpdateDrdpAgencySite(drdpAgencyId, createOrUpdateDrdpSiteModel, drdpSiteModel, uploadDRDPRecord, needUpdate);

        // 进行断言
        Assert.assertNotNull(result);
        // 添加更多的断言...
    }


    @Test
    public void testGetDrdpSiteByCenterName_WhenSitesNotEmpty() {
        // 创建输入数据
        Integer drdpAgencyId = 123;
        String centerName = "Center Name";

        // 创建返回数据
        GetAgencySitesResponse response = new GetAgencySitesResponse();
        List<DrdpSiteModel> sites = new ArrayList<>();
        DrdpSiteModel site1 = new DrdpSiteModel();
        site1.setSiteName("Center Name");
        // 设置site1的其他属性...
        sites.add(site1);
        DrdpSiteModel site2 = new DrdpSiteModel();
        // 设置site2的属性...
        sites.add(site2);
        response.setSites(sites);

        // 创建依赖的模拟对象
        Mockito.when(drdpService.getAgencySites(Mockito.anyString(), Mockito.isNull(), Mockito.anyString())).thenReturn(response);

        // 调用方法进行测试
        DrdpSiteModel result = scoreService.getDrdpSiteByCenterName(drdpAgencyId, centerName);

        // 进行断言
        Assert.assertNotNull(result);
        Assert.assertEquals(centerName, result.getSiteName());
        // 添加更多的断言...
    }

    @Test
    public void testGetDrdpSiteByCenterName_WhenSitesEmpty() {
        // 创建输入数据
        Integer drdpAgencyId = 123;
        String centerName = "Center Name";

        // 创建返回数据
        GetAgencySitesResponse response = new GetAgencySitesResponse();
        response.setSites(new ArrayList<>());

        // 创建依赖的模拟对象
        Mockito.when(drdpService.getAgencySites(Mockito.anyString(), Mockito.isNull(), Mockito.anyString())).thenReturn(response);

        // 调用方法进行测试
        DrdpSiteModel result = scoreService.getDrdpSiteByCenterName(drdpAgencyId, centerName);

        // 进行断言
        Assert.assertNull(result);
        // 添加更多的断言...
    }

    @Test
    public void testGetDrdpSiteBySIteId() {
        Integer drdpAgencyId = 123;
        GetAgencySitesResponse response = new GetAgencySitesResponse();
        response.setSites(new ArrayList<>());
        // 创建依赖的模拟对象
        Mockito.when(drdpService.getAgencySites(Mockito.anyString(), Mockito.anyString(), Mockito.isNull())).thenReturn(response);

        // 调用方法进行测试
        DrdpSiteModel result = scoreService.getDrdpSiteByDrdpSiteId(drdpAgencyId,"123");

        // 进行断言
        Assert.assertNull(result);
    }


    @Test
    public void testCreateDrdpClass_WhenParametersNotNull() {
        // 创建输入数据
        Integer drdpClassroomId = 123;
        CreateDrdpClassModel createDrdpClassModel = Mockito.mock(CreateDrdpClassModel.class);

        // 创建返回数据
        CreateDrdpClassResponse response = new CreateDrdpClassResponse();
        // 设置response的属性...

        // 创建依赖的模拟对象
        Mockito.when(drdpService.createClassroomClass(Mockito.anyString(), Mockito.any(CreateDrdpClassModel.class))).thenReturn(response);

        // 调用方法进行测试
        CreateDrdpClassResponse result = scoreService.createDrdpClass(drdpClassroomId, createDrdpClassModel);

        // 进行断言
        Assert.assertNotNull(result);
        // 添加更多的断言...
    }

    @Test
    public void testCreateDrdpClass_WhenParametersNull() {
        // 创建输入数据
        Integer drdpClassroomId = null;
        CreateDrdpClassModel createDrdpClassModel = null;

        // 调用方法进行测试
        CreateDrdpClassResponse result = scoreService.createDrdpClass(drdpClassroomId, createDrdpClassModel);

        // 进行断言
        Assert.assertNotNull(result);
        // 添加更多的断言...
    }


    @Test
    public void testGetDrdpCountyId_WhenIsCACountyAndCountiesNotEmpty() {
        // 创建输入数据
        String countyName = "Los Angeles";
        String settingCountyName = "Setting County Name";

        // 创建返回数据
        GetDrdpStatesResponse caStateResponse = new GetDrdpStatesResponse();
        List<DrdpStateModel> states = new ArrayList<>();
        DrdpStateModel state1 = new DrdpStateModel();
        state1.setId(1);
        // 设置state1的其他属性...
        states.add(state1);
        DrdpStateModel state2 = new DrdpStateModel();
        // 设置state2的属性...
        states.add(state2);
        caStateResponse.setStates(states);

        GetDrdpCountiesResponse countiesResponse = new GetDrdpCountiesResponse();
        List<DrdpCountyModel> counties = new ArrayList<>();
        DrdpCountyModel county1 = new DrdpCountyModel();
        county1.setId(1);
        // 设置county1的其他属性...
        counties.add(county1);
        DrdpCountyModel county2 = new DrdpCountyModel();
        // 设置county2的属性...
        counties.add(county2);
        countiesResponse.setCounties(counties);

        caStateResponse.setStatus(200);
        // 创建依赖的模拟对象
        Mockito.when(drdpService.getStates(Mockito.eq(DRDPApiUtil.DEFAULT_SITE_STATE))).thenReturn(caStateResponse);
        Mockito.when(drdpService.getCounties(Mockito.eq("1"), Mockito.eq(countyName))).thenReturn(countiesResponse);

        GetStateCountiesResponse stateCountiesResponse = new GetStateCountiesResponse();
        ArrayList<String> names = new ArrayList<>();
        names.add("Los Angeles");

        stateCountiesResponse.setCountyNames(names);


        Mockito.when(centerService.getStateCounties(DRDPApiUtil.DEFAULT_SITE_STATE)).thenReturn(stateCountiesResponse);
        // 调用方法进行测试
        String result = scoreService.getDrdpCountyId(countyName, settingCountyName);

        // 进行断言
        Assert.assertEquals("1", result);
        // 添加更多的断言...
    }

    @Test
    public void testGetDrdpCountyId_WhenNotCACountyOrCountiesEmpty() {
        // 创建输入数据
        String countyName = "County Name";
        String settingCountyName = "Setting County Name";

        // 创建返回数据
        GetDrdpStatesResponse caStateResponse = new GetDrdpStatesResponse();
        caStateResponse.setStates(new ArrayList<>());

        GetDrdpCountiesResponse countiesResponse = new GetDrdpCountiesResponse();
        countiesResponse.setCounties(new ArrayList<>());

        caStateResponse.setStatus(200);
        // 创建依赖的模拟对象
        Mockito.when(drdpService.getStates(Mockito.eq(DRDPApiUtil.DEFAULT_SITE_STATE))).thenReturn(caStateResponse);

        GetStateCountiesResponse stateCountiesResponse = new GetStateCountiesResponse();
        ArrayList<String> names = new ArrayList<>();
        names.add("Los Angeles");

        stateCountiesResponse.setCountyNames(names);


        Mockito.when(centerService.getStateCounties(DRDPApiUtil.DEFAULT_SITE_STATE)).thenReturn(stateCountiesResponse);

        // 调用方法进行测试
        String result = scoreService.getDrdpCountyId(countyName, settingCountyName);

        // 进行断言
        Assert.assertEquals(DRDPApiUtil.DEFAULT_SITE_COUNTY_ID, result);
        // 添加更多的断言...
    }


    @Test
    public void testGetDrdpClassroomBySiteId_EmptyGroupId_ReturnsEmptyDrdpClassroomModel() {
        // Arrange
        GroupMetaDataEntity groupMetaDataEntity = new GroupMetaDataEntity();
        groupMetaDataEntity.setId(UUID.randomUUID().toString());
        String roomId = "1";
        groupMetaDataEntity.setMetaValue(roomId);
        GroupEntity group = new GroupEntity();
        group.setId(UUID.randomUUID().toString());
        groupMetaDataEntity.setGroup(group);

        GetSiteClassroomsResponse classroomsResponse = new GetSiteClassroomsResponse();
        ArrayList<DrdpClassroomModel> classrooms = new ArrayList<>();
        DrdpClassroomModel classroomModel = new DrdpClassroomModel();
        classroomModel.setClassroomName("classroom1");
        classroomModel.setAgencyId(1);
        classroomModel.setSiteId(1);
        classroomModel.setId(1);
        classroomModel.setIsDeleted(false);

        classroomsResponse.setClassrooms(classrooms);

        when(drdpService.getSiteClassrooms(anyString(), eq(""), anyString())).thenReturn(classroomsResponse);

        CreateDrdpClassroomResponse classroomResponse = new CreateDrdpClassroomResponse();
        classroomResponse.setId(1);
        when(drdpService.createSiteClassroom(anyString(), any(CreateDrdpClassroomModel.class))).thenReturn(classroomResponse);

        com.learninggenie.common.data.model.GroupEntity groupEntity = new com.learninggenie.common.data.model.GroupEntity();
        groupEntity.setId(UUID.randomUUID().toString());
        groupEntity.setName("groupName");

        UploadDRDPRecord uploadDRDPRecord = new UploadDRDPRecord();
        // Act
        DrdpClassroomModel result = scoreService.getDrdpClassroomBySiteId(123, groupEntity, uploadDRDPRecord);

        // Assert
        Assert.assertNotNull(result);
        assertEquals(groupEntity.getName(), result.getClassroomName());
    }

    @Test
    public void testGetDrdpClassroomBySiteId_NullSiteModelId_ReturnsEmptyDrdpClassroomModel() {
        // 定义测试数据
        com.learninggenie.common.data.model.GroupEntity groupEntity = new com.learninggenie.common.data.model.GroupEntity();
        groupEntity.setId(UUID.randomUUID().toString());
        groupEntity.setName("groupName");

        UploadDRDPRecord uploadDRDPRecord = new UploadDRDPRecord();

        // Act
        DrdpClassroomModel result = scoreService.getDrdpClassroomBySiteId(null, groupEntity, uploadDRDPRecord);

        // Assert
        Assert.assertNotNull(result);
        assertEquals(new DrdpClassroomModel(), result);
    }

    @Test
    public void testGetDrdpClassroomBySiteId_MetadataNotNullAndNotEmpty_ReturnsClassroomModelFromService() {
        // Arrange
        GroupMetaDataEntity groupMetaDataEntity = new GroupMetaDataEntity();
        groupMetaDataEntity.setId(UUID.randomUUID().toString());
        String roomId = "2";
        groupMetaDataEntity.setMetaValue(roomId);
        GroupEntity group = new GroupEntity();
        group.setId(UUID.randomUUID().toString());
        groupMetaDataEntity.setGroup(group);


        GetSiteClassroomsResponse classroomsResponse = new GetSiteClassroomsResponse();
        ArrayList<DrdpClassroomModel> classrooms = new ArrayList<>();
        DrdpClassroomModel classroomModel = new DrdpClassroomModel();
        classroomModel.setClassroomName("groupName");
        classroomModel.setAgencyId(1);
        classroomModel.setSiteId(1);
        classroomModel.setId(1);
        classroomModel.setIsDeleted(false);

        classroomsResponse.setClassrooms(classrooms);

        String siteModelId = "123";
        com.learninggenie.common.data.model.GroupEntity groupEntity = new com.learninggenie.common.data.model.GroupEntity();
        groupEntity.setId(UUID.randomUUID().toString());
        groupEntity.setName("groupName");

        when(drdpService.getSiteClassrooms(siteModelId, "", "")).thenReturn(classroomsResponse);

        CreateDrdpClassroomResponse roomResponse = new CreateDrdpClassroomResponse();
        when(drdpService.createSiteClassroom(anyString(), any(CreateDrdpClassroomModel.class))).thenReturn(roomResponse);

        UploadDRDPRecord uploadDRDPRecord = new UploadDRDPRecord();

        // Act
        DrdpClassroomModel result = scoreService.getDrdpClassroomBySiteId(123, groupEntity, uploadDRDPRecord);

        DrdpClassroomModel expected = new DrdpClassroomModel();
        // Assert
        Assert.assertNotNull(result);
        assertEquals(expected, result);
    }

    @Test
    public void testGetDrdpClassroomBySiteId_MetadataNull_ReturnsClassroomModelFromService() {
        // Arrange
        // Arrange
        GroupMetaDataEntity groupMetaDataEntity = new GroupMetaDataEntity();

        com.learninggenie.common.data.model.GroupEntity groupEntity = new com.learninggenie.common.data.model.GroupEntity();
        groupEntity.setId(UUID.randomUUID().toString());
        groupEntity.setName("groupName");

        UploadDRDPRecord uploadDRDPRecord = new UploadDRDPRecord();

        GetSiteClassroomsResponse classroomsResponse = new GetSiteClassroomsResponse();
        ArrayList<DrdpClassroomModel> classrooms = new ArrayList<>();
        DrdpClassroomModel classroomModel = new DrdpClassroomModel();
        classroomModel.setClassroomName("classroom1");
        classroomModel.setAgencyId(1);
        classroomModel.setSiteId(1);
        classroomModel.setId(1);
        classroomModel.setIsDeleted(false);
        classroomsResponse.setClassrooms(classrooms);

        when(drdpService.getSiteClassrooms(anyString(), anyString(), anyString())).thenReturn(classroomsResponse);

        CreateDrdpClassroomResponse createDrdpClassroomResponse = new CreateDrdpClassroomResponse();
        createDrdpClassroomResponse.setId(1);

        when(drdpService.createSiteClassroom(anyString(), any(CreateDrdpClassroomModel.class))).thenReturn(createDrdpClassroomResponse);
        // Act
        DrdpClassroomModel result = scoreService.getDrdpClassroomBySiteId(123, groupEntity, uploadDRDPRecord);

        // Assert
        DrdpClassroomModel expected = new DrdpClassroomModel();
        expected.setClassroomName("groupName");
        expected.setId(1);
        expected.setIsDeleted(false);
        Assert.assertNotNull(result);
        assertEquals(expected, result);
    }

    @Test
    public void testGetDrdpClassroomBySiteId_MetadataNullAndServiceReturnsEmptyResponse_ReturnsEmptyDrdpClassroomModel() {
        // Arrange
        GroupMetaDataEntity groupMetaDataEntity = new GroupMetaDataEntity();

        com.learninggenie.common.data.model.GroupEntity groupEntity = new com.learninggenie.common.data.model.GroupEntity();
        groupEntity.setId(UUID.randomUUID().toString());
        groupEntity.setName("groupName");

        UploadDRDPRecord uploadDRDPRecord = new UploadDRDPRecord();

        GetSiteClassroomsResponse classroomsResponse = new GetSiteClassroomsResponse();
        ArrayList<DrdpClassroomModel> classrooms = new ArrayList<>();
        DrdpClassroomModel classroomModel = new DrdpClassroomModel();
        classroomModel.setClassroomName("classroom1");
        classroomModel.setAgencyId(1);
        classroomModel.setSiteId(1);
        classroomModel.setId(1);
        classroomModel.setIsDeleted(false);
        classroomsResponse.setClassrooms(classrooms);

        when(drdpService.getSiteClassrooms(anyString(), anyString(), anyString())).thenReturn(classroomsResponse);

        CreateDrdpClassroomResponse createDrdpClassroomResponse = new CreateDrdpClassroomResponse();
        HashMap<String, List<String>> errors = new HashMap<>();
        errors.put("error", Arrays.asList("error"));
        createDrdpClassroomResponse.setErrors(errors);
        createDrdpClassroomResponse.setStatus(200);


        when(drdpService.createSiteClassroom(anyString(), any(CreateDrdpClassroomModel.class))).thenReturn(createDrdpClassroomResponse);

        // Act
        DrdpClassroomModel result = scoreService.getDrdpClassroomBySiteId(123, groupEntity, uploadDRDPRecord);

        // Assert
        Assert.assertNotNull(result);
        assertEquals(new DrdpClassroomModel(), result);
    }


    @Test
    public void createOrUpdateDrdpClass_WhenNeedUpdateIsFalse_ShouldCreateNewDrdpClass() {
        // 创建Mock对象

        // 设置输入参数
        DrdpClassroomModel drdpClassroomModel = new DrdpClassroomModel();
        DrdpTermModel drdpTermModel = new DrdpTermModel();
        List<String> teacherIdList = new ArrayList<>();
        List<String> teacherOfRecordIdList = new ArrayList<>();
        teacherOfRecordIdList.add("1");

        teacherIdList.add("1");
        teacherIdList.add("2");
        teacherIdList.add("3");
        DrdpClassModel drdpClassModel = new DrdpClassModel();
        UploadDRDPRecord uploadDRDPRecord = new UploadDRDPRecord();
        Boolean needUpdate = false;

        CreateDrdpClassResponse expected = new CreateDrdpClassResponse();
        // 调用被测试方法
        CreateDrdpClassResponse result = scoreService.createOrUpdateDrdpClass(drdpClassroomModel, drdpTermModel,
                teacherIdList, teacherOfRecordIdList, drdpClassModel, uploadDRDPRecord, needUpdate);

        // 验证依赖方法是否被调用

        // 验证结果是否符合预期
        assertEquals(expected, result);
    }

    @Test
    public void createOrUpdateDrdpClass_WhenNeedUpdateIsTrue_ShouldUpdateExistingDrdpClass() {
        // 创建Mock对象
        // 设置输入参数
        DrdpClassroomModel drdpClassroomModel = new DrdpClassroomModel();
        DrdpTermModel drdpTermModel = new DrdpTermModel();
        List<String> teacherIdList = new ArrayList<>();
        List<String> teacherOfRecordIdList = new ArrayList<>();
        teacherOfRecordIdList.add("1");

        teacherIdList.add("1");
        teacherIdList.add("2");
        teacherIdList.add("3");
        DrdpClassModel drdpClassModel = new DrdpClassModel();
        UploadDRDPRecord uploadDRDPRecord = new UploadDRDPRecord();
        Boolean needUpdate = true;

        UpdateDrdpClassResponse expected = new UpdateDrdpClassResponse();
        expected.setStatus(200);

        // 调用被测试方法
        CreateDrdpClassResponse result = scoreService.createOrUpdateDrdpClass(drdpClassroomModel, drdpTermModel,
                teacherIdList, teacherOfRecordIdList, drdpClassModel, uploadDRDPRecord, needUpdate);

        // 验证依赖方法是否被调用

        // 验证结果是否符合预期
        assertEquals(expected.getId(), result.getId());
    }

    /**
     * Case: 批量锁定机构开启了完成所有评分点才能锁定的开关
     * 结果: 传参中 requireCompleteRating 为 true
     */
    @Test
    public void testBatchCheck() {
        // 准备数据
        // 批量锁定的数据
        BatchCheckRequest request = new BatchCheckRequest();
        String periodAlias = UUID.randomUUID().toString(); // 周期
        String displayAlias = UUID.randomUUID().toString(); // 显示别名
        String frameworkId = UUID.randomUUID().toString(); // 框架 Id
        String[] groupIds = new String[]{UUID.randomUUID().toString(), UUID.randomUUID().toString()}; // 班级 Id
        request.setPeriodAlias(periodAlias); // 设置周期
        request.setDisplayAlias(displayAlias); // 设置显示别名
        request.setFrameworkId(frameworkId); // 设置框架 Id
        request.setGroupIds(groupIds); // 设置班级 Id
        String userId = UUID.randomUUID().toString(); // 用户 Id
        // 机构信息
        AgencyModel agencyModel = new AgencyModel();
        String agencyId = UUID.randomUUID().toString(); // 机构 Id
        agencyModel.setId(agencyId);
        // mock 机构信息
        lenient().when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        // mock 上传功能开关
        lenient().when(userProvider.getAgencyOpenDefaultOpen(agencyId, AgencyMetaKey.DRDP_UPLOAD.toString())).thenReturn(false);
        // 机构 meta 信息
        AgencyMetaDataEntity meta = new AgencyMetaDataEntity();
        meta.setMetaValue("true");
        // mock 机构 meta 信息
        lenient().when(agencyDao.getMeta(agencyId, AgencyMetaKey.COMPLETE_RATING_REQUIRED_FOR_CHILD_LOCK.toString())).thenReturn(meta);
        lenient().when(userProvider.getCurrentAgencyId()).thenReturn(agencyId);
        // 调用方法
        JobEntity jobEntity = scoreService.batchCheck(request, userId);

        // 验证结果 验证状态为 PENDING
        assertEquals(StatusType.PENDING.toString(), jobEntity.getStatus());

    }

    @Test
    public void testVerifyDrdpOnlineAgencyAlias_EmptyAlias() {
        // 准备参数
        VerifyDrdpOnlineAgencyAliasRequest request = new VerifyDrdpOnlineAgencyAliasRequest();
        String userId = "1";
        when(userProvider.getCurrentUserId()).thenReturn(userId);

        AgencyModel agency = new AgencyModel();
        agency.setId("1");
        agency.setName("agency");
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);

        List<DRDPSetting> drdpSettings = Lists.newArrayList();
        DRDPSetting drdpSetting = new DRDPSetting();
        drdpSetting.setId("1");
        drdpSetting.setAgencyId(agency.getId());
        drdpSetting.setAgencyAlias("agency");
        drdpSetting.setComplete(true);
        drdpSettings.add(drdpSetting);
        when(agencyDao.getDRDPSettingByAgency(agency.getId())).thenReturn(drdpSettings);
        // 发起请求
        VerifyDrdpOnlineAgencyAliasResponse response = scoreService.verifyDrdpOnlineAgencyAlias(request);

        // 断言
        assertTrue(response.isAgencyNameExists());
        assertFalse(response.isAgencyAliasExists());
        assertFalse(response.isAgencyAliasError());
    }

    @Test
    public void testVerifyDrdpOnlineAgencyAlias_AgencyAliasExists() {
        // 准备参数
        String userId = "1";
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        AgencyModel agency = new AgencyModel();
        agency.setId("1");
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);

        List<DRDPSetting> drdpSettings = Lists.newArrayList();
        DRDPSetting drdpSetting = new DRDPSetting();
        drdpSetting.setId("1");
        drdpSetting.setAgencyId(agency.getId());
        drdpSetting.setAgencyAlias("alias");
        drdpSetting.setComplete(true);
        drdpSettings.add(drdpSetting);
        when(agencyDao.getDRDPSettingByAgency(agency.getId())).thenReturn(drdpSettings);

        VerifyDrdpOnlineAgencyAliasRequest request = new VerifyDrdpOnlineAgencyAliasRequest();
        request.setSettingId("2");
        request.setAgencyAlias("alias");
        // 发起请求
        VerifyDrdpOnlineAgencyAliasResponse response = scoreService.verifyDrdpOnlineAgencyAlias(request);

        // 断言
        assertTrue(response.isAgencyAliasExists());
    }

    @Test
    public void testVerifyDrdpOnlineAgencyAlias_AgencyAliasError() {
        // 准备参数
        String userId = "1";
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        AgencyModel agency = new AgencyModel();
        agency.setId("1");
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);

        List<DRDPSetting> drdpSettings = Lists.newArrayList();
        when(agencyDao.getDRDPSettingByAgency(agency.getId())).thenReturn(drdpSettings);

        GetAllAgencyResponse getAllAgencyResponse = new GetAllAgencyResponse();
        List<DrdpAgencyModel> drdpAgencyModels = Lists.newArrayList();
        DrdpAgencyModel drdpAgencyModel = new DrdpAgencyModel();
        drdpAgencyModel.setAgencyName("alias1");
        drdpAgencyModel.setId(1);
        drdpAgencyModel.setIsDeleted(false);
        drdpAgencyModels.add(drdpAgencyModel);

        getAllAgencyResponse.setAgencies(drdpAgencyModels);
        when(drdpService.getAllAgencies()).thenReturn(getAllAgencyResponse);

        // 发起请求
        VerifyDrdpOnlineAgencyAliasRequest request = new VerifyDrdpOnlineAgencyAliasRequest();
        request.setSettingId("2");
        request.setAgencyAlias("alias");
        VerifyDrdpOnlineAgencyAliasResponse response = scoreService.verifyDrdpOnlineAgencyAlias(request);

        // 断言
        assertTrue(response.isAgencyAliasError());
    }

    @Test
    public void testGetUploadFailedChildren_agencyNameError() {
        String userId = "1";
        when(userProvider.getCurrentUserId()).thenReturn(userId);

        // 当前用户信息
        com.learninggenie.common.data.entity.UserEntity currentUser = new com.learninggenie.common.data.entity.UserEntity();
        currentUser.setId(userId);
        when(userProvider.getUser(userId)).thenReturn(currentUser);

        AgencyModel agency = new AgencyModel();
        agency.setId("1");

        // 设置用户能使用 DRDP 上传
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        when(userProvider.getAgencyOpenDefaultClose(agency.getId(), AgencyMetaKey.PAID.toString())).thenReturn(true);
        when(userProvider.getAgencyOpenDefaultOpen(agency.getId(), AgencyMetaKey.DRDP_UPLOAD.toString())).thenReturn(true);

        AgencyMetaDataEntity uploadMeta = new AgencyMetaDataEntity();
        uploadMeta.setMetaValue("DRDP_UPLOAD_OPEN");
        when(agencyDao.getMeta(agency.getId(), DrdpSettingKey.UPLOAD_DRDP_SETTING.toString())).thenReturn(uploadMeta);

        List<UploadDRDPRecord> agencyNameErrorRecords = Lists.newArrayList();
        UploadDRDPRecord agencyNameErrorRecord = new UploadDRDPRecord();
        agencyNameErrorRecord.setAgencyId(agency.getId());
        agencyNameErrorRecord.setErrorType(ErrorType.AGENCY_NAME_ERROR.toString());
        agencyNameErrorRecord.setPeriodAlias(PeriodUtil.getCurrentSchoolYear() + " Fall");

        AgencyNameErrorModel agencyNameErrorModel = new AgencyNameErrorModel();
        agencyNameErrorModel.setSettingId("1");
        agencyNameErrorRecord.setErrorData(JsonUtil.toJson(agencyNameErrorModel));
        agencyNameErrorRecords.add(agencyNameErrorRecord);
        when(recordDao.getFailedUploadDrdpRecordByTypeAgency(agency.getId(), ErrorType.AGENCY_NAME_ERROR.toString())).thenReturn(agencyNameErrorRecords);

        List<UploadDRDPRecord> childErrorRecords = new ArrayList<>();
        UploadDRDPRecord childErrorRecord = new UploadDRDPRecord();
        childErrorRecord.setAgencyId(agency.getId());
        childErrorRecord.setErrorType(ErrorType.CHILD_ERROR.toString());
        childErrorRecord.setPeriodAlias(PeriodUtil.getLastSchoolYear() + " Fall");
        childErrorRecords.add(childErrorRecord);
        when(recordDao.getFailedUploadDrdpRecordByTypeAgency(agency.getId(), ErrorType.CHILD_ERROR.toString())).thenReturn(childErrorRecords);

        List<UploadDRDPRecord> completeDateIsFutureErrorRecords = new ArrayList<>();
        UploadDRDPRecord completeDateIsFutureErrorRecord = new UploadDRDPRecord();
        completeDateIsFutureErrorRecord.setAgencyId(agency.getId());
        completeDateIsFutureErrorRecord.setErrorType(ErrorType.COMPLETE_DATE_IS_FUTURE_ERROR.toString());
        completeDateIsFutureErrorRecord.setPeriodAlias(PeriodUtil.getLastSchoolYear() + " Fall");
        completeDateIsFutureErrorRecords.add(completeDateIsFutureErrorRecord);
        when(recordDao.getFailedUploadDrdpRecordByTypeAgency(agency.getId(), ErrorType.COMPLETE_DATE_IS_FUTURE_ERROR.toString())).thenReturn(completeDateIsFutureErrorRecords);


        List<UploadDRDPRecord> completeDateOutPeriodRecords = new ArrayList<>();
        UploadDRDPRecord completeDateOutPeriodRecord = new UploadDRDPRecord();
        completeDateOutPeriodRecord.setAgencyId(agency.getId());
        completeDateOutPeriodRecord.setErrorType(ErrorType.COMPLETE_DATE_ERROR.toString());
        completeDateOutPeriodRecord.setPeriodAlias(PeriodUtil.getLastSchoolYear() + " Fall");
        completeDateOutPeriodRecords.add(completeDateOutPeriodRecord);
        when(recordDao.getFailedUploadDrdpRecordByTypeAgency(agency.getId(), ErrorType.COMPLETE_DATE_ERROR.toString())).thenReturn(completeDateOutPeriodRecords);

        List<UploadDRDPRecord> frameworkErrorRecords = new ArrayList<>();
        UploadDRDPRecord frameworkErrorRecord = new UploadDRDPRecord();
        frameworkErrorRecord.setAgencyId(agency.getId());
        frameworkErrorRecord.setErrorType(ErrorType.RATING_VIEW_ERROR.toString());
        frameworkErrorRecord.setPeriodAlias(PeriodUtil.getLastSchoolYear() + " Fall");
        frameworkErrorRecords.add(frameworkErrorRecord);
        when(recordDao.getFailedUploadDrdpRecordByTypeAgency(agency.getId(), ErrorType.RATING_VIEW_ERROR.toString())).thenReturn(frameworkErrorRecords);

        List<UploadDRDPRecord> centerErrorRecords = new ArrayList<>();
        UploadDRDPRecord centerErrorRecord = new UploadDRDPRecord();
        centerErrorRecord.setAgencyId(agency.getId());
        centerErrorRecord.setErrorType(ErrorType.CREATE_CENTER_ERROR.toString());
        centerErrorRecord.setPeriodAlias(PeriodUtil.getLastSchoolYear() + " Fall");
        centerErrorRecords.add(centerErrorRecord);
        when(recordDao.getFailedUploadDrdpRecordByTypeAgency(agency.getId(), ErrorType.CREATE_CENTER_ERROR.toString())).thenReturn(centerErrorRecords);

        List<UploadDRDPRecord> classroomErrorRecords = new ArrayList<>();
        UploadDRDPRecord classroomErrorRecord = new UploadDRDPRecord();
        classroomErrorRecord.setAgencyId(agency.getId());
        classroomErrorRecord.setErrorType(ErrorType.CREATE_CLASSROOM_ERROR.toString());
        classroomErrorRecord.setPeriodAlias(PeriodUtil.getLastSchoolYear() + " Fall");
        classroomErrorRecords.add(classroomErrorRecord);
        when(recordDao.getFailedUploadDrdpRecordByTypeAgency(agency.getId(), ErrorType.CREATE_CLASSROOM_ERROR.toString())).thenReturn(classroomErrorRecords);

        List<DRDPSetting> agencyDrdpSetting = new ArrayList<>();
        DRDPSetting drdpSetting = new DRDPSetting();
        drdpSetting.setId("1");
        drdpSetting.setAgencyAlias("Alias");
        drdpSetting.setComplete(true);

        agencyDrdpSetting.add(drdpSetting);
        when(agencyDao.getDRDPSettingByAgency(agency.getId())).thenReturn(agencyDrdpSetting);

        EnrollmentSnapshotDaoImpl enrollmentSnapshotDaoSpy = spy(enrollmentSnapshotDao);

        ReflectionTestUtils.setField(enrollmentSnapshotDaoSpy, BASE_MAPPER, enrollmentSnapshotMapper);
        final LambdaQueryChainWrapper<SnapshotEntity> lambdaUpdate = new LambdaQueryChainWrapper<>(enrollmentSnapshotMapper);
        lambdaUpdate.setEntity(new SnapshotEntity());
        lambdaUpdate.setEntityClass(SnapshotEntity.class);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(enrollmentSnapshotMapper)).thenReturn(lambdaUpdate);
        ReflectionTestUtils.setField(scoreService, "enrollmentSnapshotDao", enrollmentSnapshotDaoSpy);

        // 获取 URL
        String publicUrl = "https://www.baidu.com";
        when(fileSystem.getPublicUrl(anyString())).thenReturn(publicUrl);
        // 保存到 APP META 中，以避免重复生成相同的错误 Excel

        UploadFailedResponse response = scoreService.getUploadFailedChildren(true, false);

        assertEquals(publicUrl, response.getExcelUrl());
    }

    @Test
    public void testVerifyRatingView() {
        // 当前用户
        String currentUserId = "1";
        when(userProvider.getCurrentUserId()).thenReturn(currentUserId);
        // 用户所属机构
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("1");
        when(userProvider.getAgencyByUserId(currentUserId)).thenReturn(agencyModel);


        String settingId = "1";
        List<DRDPSetting> drdpSettings = Lists.newArrayList();
        DRDPSetting drdpSetting = new DRDPSetting();
        drdpSetting.setId(settingId);
        drdpSetting.setState("CA");
        List<FrameworkType> frameworkTypes = Lists.newArrayList();
        frameworkTypes.add(new FrameworkType("IT", "ITE"));
        drdpSetting.setFrameworkTypes(frameworkTypes);
        drdpSettings.add(drdpSetting);

        when(agencyDao.getDRDPSettingByAgency(agencyModel.getId())).thenReturn(drdpSettings);

        DrdpBatchScoringResponse drdpBatchScoringResponse = new DrdpBatchScoringResponse();
        drdpBatchScoringResponse.setStatus(401);
        BatchScoringError batchScoringError = new BatchScoringError();
        batchScoringError.setAgeGradeTemplateError(true);
        List<ChildError> childErrors = Lists.newArrayList();
        ChildError childError = new ChildError();
        childError.setIndex(2);
        List<ErrorModel> errorModels = Lists.newArrayList();
        ErrorModel errorModel = new ErrorModel();
        errorModel.setKey("age grade template");
        errorModels.add(errorModel);
        childError.setErrors(errorModels);

        childErrors.add(childError);
        batchScoringError.setChildErrors(childErrors);
        drdpBatchScoringResponse.setBatchScoringError(batchScoringError);


        DrdpBatchScoringModel drdpBatchScoringModel = new DrdpBatchScoringModel();
        DrdpBatchScoreAgencyModel agencyInfo = new DrdpBatchScoreAgencyModel();
        agencyInfo.setAgency(drdpSetting.getAgencyAlias());
        agencyInfo.setState("CA");
        agencyInfo.setSite("verify_test_site");
        agencyInfo.setGroup("verify_test_class");
        agencyInfo.setTeacher("<EMAIL>");
        String currentSchoolYear = PeriodUtil.getCurrentSchoolYear();
        agencyInfo.setTermId("Fall " + currentSchoolYear.split("-")[0]);

        drdpBatchScoringModel.setAgencyInfo(agencyInfo);
        List<FrameworkRequest> frameworks = Lists.newArrayList();
        frameworks.add(new FrameworkRequest("IT", "ITC"));

        // 获取设置的框架类型
        List<String> verifyFrameworkTypes = frameworks.stream().map(FrameworkRequest::getFrameworkType).filter(item -> !"N/A".equalsIgnoreCase(item)).collect(Collectors.toList());
        List<Map<String, String>> ratings = verifyFrameworkTypes.stream().map(item -> {
            Map<String, String> rating = Maps.newHashMap();
            rating.put("age_grade_instrument_id", item);
            // 设置 localId
            rating.put("localId", "verify_test-localId");
            rating.put("icode", "verify_test-localId");
            // ssid 设置为空
            rating.put("ssid", "");
            rating.put("firstName", "verify_test_child_first_name");
            rating.put("lastName", "verify_test_child_last_name");
            rating.put("gender", "M");
            rating.put("allowUpload", "1");
            rating.put("dob", "10/01/2019");
            return rating;
        }).collect(Collectors.toList());
        drdpBatchScoringModel.setRatings(ratings);
        when(drdpService.batchScoring(drdpBatchScoringModel, false, true)).thenReturn(drdpBatchScoringResponse);


        VerifyRatingViewRequest request = new VerifyRatingViewRequest();
        request.setFrameworks(frameworks);

        VerifyRatingViewResponse response = scoreService.verifyDrdpOnlineRatingView(settingId, request);
        assertFalse(response.isSuccess());
    }

    @Test
    public void testUploadLockedData_no_uploaded_cache() {
        String settingId = UUID.randomUUID().toString();
        DRDPSetting drdpSetting = new DRDPSetting();
        drdpSetting.setId(settingId);

        String userId = UUID.randomUUID().toString();;
        when(userProvider.getCurrentUserId()).thenReturn(userId);

        String agencyId = UUID.randomUUID().toString();;
        AgencyModel agency = new AgencyModel();
        agency.setId(agencyId);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);

        String groupId = UUID.randomUUID().toString();
        List<StudentSnapshotEntity> processingSnapshots = new ArrayList<>();
        StudentSnapshotEntity studentSnapshotEntity = new StudentSnapshotEntity();
        studentSnapshotEntity.setId(UUID.randomUUID().toString());
        studentSnapshotEntity.setAgencyId(agencyId);
        studentSnapshotEntity.setGroupId(groupId);
        studentSnapshotEntity.setUploadId("1");
        studentSnapshotEntity.setUploadStatus(StatusType.PROCESSING.toString());

        processingSnapshots.add(studentSnapshotEntity);
        when(studentDao.getProcessingSnapshots(agencyId, PeriodUtil.getCurrentSchoolYear())).thenReturn(processingSnapshots);

        // when(cacheService.get(anyString())).thenReturn(null);

        List<DRDPSetting> drdpSettings = new ArrayList<>();
        drdpSettings.add(drdpSetting);
        when(agencyDao.getDRDPSettingByAgency(agencyId)).thenReturn(drdpSettings);

        scoreService.uploadLockedData(settingId, true, JobType.BACKEND_UPLOAD);
    }

    @Test
    public void testUploadLockedData_upload_cache_more_3() {
        String settingId = UUID.randomUUID().toString();
        DRDPSetting drdpSetting = new DRDPSetting();
        drdpSetting.setId(settingId);

        String userId = UUID.randomUUID().toString();;
        when(userProvider.getCurrentUserId()).thenReturn(userId);

        String agencyId = UUID.randomUUID().toString();;
        AgencyModel agency = new AgencyModel();
        agency.setId(agencyId);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);

        String groupId = UUID.randomUUID().toString();
        List<StudentSnapshotEntity> processingSnapshots = new ArrayList<>();
        StudentSnapshotEntity studentSnapshotEntity = new StudentSnapshotEntity();
        studentSnapshotEntity.setId(UUID.randomUUID().toString());
        studentSnapshotEntity.setAgencyId(agencyId);
        studentSnapshotEntity.setGroupId(groupId);
        studentSnapshotEntity.setUploadId("1");
        studentSnapshotEntity.setUploadStatus(StatusType.PROCESSING.toString());

        processingSnapshots.add(studentSnapshotEntity);
        when(studentDao.getProcessingSnapshots(agencyId, PeriodUtil.getCurrentSchoolYear())).thenReturn(processingSnapshots);

        CacheModel cacheModel = new CacheModel();
        List<Long> uploadTimes = new ArrayList<>();
        uploadTimes.add(System.currentTimeMillis());
        uploadTimes.add(System.currentTimeMillis());
        uploadTimes.add(System.currentTimeMillis());

        cacheModel.setValue(JsonUtil.toJson(uploadTimes));
        // when(cacheService.get(anyString())).thenReturn(cacheModel);

        List<DRDPSetting> drdpSettings = new ArrayList<>();
        drdpSettings.add(drdpSetting);
        when(agencyDao.getDRDPSettingByAgency(agencyId)).thenReturn(drdpSettings);

        scoreService.uploadLockedData(settingId, true, JobType.BACKEND_UPLOAD);
    }

    @Test
    public void testUploadLockedData_last_upload_time_is_yesterday() {
        String settingId = UUID.randomUUID().toString();
        DRDPSetting drdpSetting = new DRDPSetting();
        drdpSetting.setId(settingId);

        String userId = UUID.randomUUID().toString();;
        when(userProvider.getCurrentUserId()).thenReturn(userId);

        String agencyId = UUID.randomUUID().toString();;
        AgencyModel agency = new AgencyModel();
        agency.setId(agencyId);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);

        String groupId = UUID.randomUUID().toString();
        List<StudentSnapshotEntity> processingSnapshots = new ArrayList<>();
        StudentSnapshotEntity studentSnapshotEntity = new StudentSnapshotEntity();
        studentSnapshotEntity.setId(UUID.randomUUID().toString());
        studentSnapshotEntity.setAgencyId(agencyId);
        studentSnapshotEntity.setGroupId(groupId);
        studentSnapshotEntity.setUploadId("1");
        studentSnapshotEntity.setUploadStatus(StatusType.PROCESSING.toString());

        processingSnapshots.add(studentSnapshotEntity);
        when(studentDao.getProcessingSnapshots(agencyId, PeriodUtil.getCurrentSchoolYear())).thenReturn(processingSnapshots);

        // when(cacheService.get(anyString())).thenReturn(null);

        Date uploadedTime = TimeUtil.minusDays(new Date(), 1);
        when(recordDao.getUploadTimeByUploadId(studentSnapshotEntity.getUploadId())).thenReturn(uploadedTime);

        List<DRDPSetting> drdpSettings = new ArrayList<>();
        drdpSettings.add(drdpSetting);
        when(agencyDao.getDRDPSettingByAgency(agencyId)).thenReturn(drdpSettings);

        scoreService.uploadLockedData(settingId, true, JobType.BACKEND_UPLOAD);
    }

    @Test
    public void testUploadLockedData_snapshot_is_success() {
        String settingId = UUID.randomUUID().toString();
        DRDPSetting drdpSetting = new DRDPSetting();
        drdpSetting.setId(settingId);

        String userId = UUID.randomUUID().toString();;
        when(userProvider.getCurrentUserId()).thenReturn(userId);

        String agencyId = UUID.randomUUID().toString();;
        AgencyModel agency = new AgencyModel();
        agency.setId(agencyId);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);

        String groupId = UUID.randomUUID().toString();
        List<StudentSnapshotEntity> processingSnapshots = new ArrayList<>();
        StudentSnapshotEntity studentSnapshotEntity = new StudentSnapshotEntity();
        studentSnapshotEntity.setId(UUID.randomUUID().toString());
        studentSnapshotEntity.setAgencyId(agencyId);
        studentSnapshotEntity.setGroupId(groupId);
        studentSnapshotEntity.setUploadId("1");
        studentSnapshotEntity.setUploadStatus(StatusType.SUCCESS.toString());

        processingSnapshots.add(studentSnapshotEntity);
        when(studentDao.getProcessingSnapshots(agencyId, PeriodUtil.getCurrentSchoolYear())).thenReturn(processingSnapshots);

        List<DRDPSetting> drdpSettings = new ArrayList<>();
        drdpSettings.add(drdpSetting);
        when(agencyDao.getDRDPSettingByAgency(agencyId)).thenReturn(drdpSettings);

        scoreService.uploadLockedData(settingId, true, JobType.BACKEND_UPLOAD);
    }

    @Test
    public void testUploadLockedData_no_snapshot() {
        String settingId = UUID.randomUUID().toString();
        DRDPSetting drdpSetting = new DRDPSetting();
        drdpSetting.setId(settingId);

        String userId = UUID.randomUUID().toString();;
        when(userProvider.getCurrentUserId()).thenReturn(userId);

        String agencyId = UUID.randomUUID().toString();;
        AgencyModel agency = new AgencyModel();
        agency.setId(agencyId);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);

        String groupId = UUID.randomUUID().toString();
        List<StudentSnapshotEntity> processingSnapshots = new ArrayList<>();
        processingSnapshots.add(null);
        when(studentDao.getProcessingSnapshots(agencyId, PeriodUtil.getCurrentSchoolYear())).thenReturn(processingSnapshots);

        List<DRDPSetting> drdpSettings = new ArrayList<>();
        drdpSettings.add(drdpSetting);
        when(agencyDao.getDRDPSettingByAgency(agencyId)).thenReturn(drdpSettings);

        scoreService.uploadLockedData(settingId, true, JobType.BACKEND_UPLOAD);
    }

    @Test
    public void testCacheUploadedTime_noCache() {
        String snapshotId = UUID.randomUUID().toString().toUpperCase();
        // 缓存上传时间的 KEY
        String uploadedTimeKey = CacheKey.SNAPSHOT_UPLOAD_TIME + snapshotId.toUpperCase();
        when(cacheService.get(uploadedTimeKey)).thenReturn(null);

        scoreService.cacheUploadedTime(snapshotId);

        verify(cacheService).set(anyString(), anyString(), anyInt());
    }

    @Test
    public void testCacheUploadedTime_hasCache() {
        String snapshotId = UUID.randomUUID().toString().toUpperCase();
        // 缓存上传时间的 KEY
        String uploadedTimeKey = CacheKey.SNAPSHOT_UPLOAD_TIME + snapshotId.toUpperCase();
        CacheModel cacheModel = new CacheModel();
        List<Long> uploadedTimes = new ArrayList<>();
        uploadedTimes.add(System.currentTimeMillis());
        cacheModel.setValue(JsonUtil.toJson(uploadedTimes));
        when(cacheService.get(uploadedTimeKey)).thenReturn(cacheModel);

        scoreService.cacheUploadedTime(snapshotId);

        verify(cacheService).update(anyString(), anyString(), anyInt());
    }
}


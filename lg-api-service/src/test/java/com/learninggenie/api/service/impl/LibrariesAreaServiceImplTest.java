package com.learninggenie.api.service.impl;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.toolkit.ChainWrappers;
import com.learninggenie.common.data.entity.LibrariesArea;
import com.learninggenie.common.data.mapper.mybatisplus.LibrariesAreaMapper;
import com.learninggenie.common.library.LibrariesAreaServiceImpl;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;

import static org.mockito.Mockito.mockStatic;

/**
 * Create by hxl 2023/09/17.
 * LibrariesAreaServiceImpl 的测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class LibrariesAreaServiceImplTest {

    private static final String BASE_MAPPER = "baseMapper";

    private static MockedStatic<ChainWrappers> chainWrappersMockedStatic;

    @InjectMocks
    private LibrariesAreaServiceImpl librariesAreaService;

    @Mock
    private LibrariesAreaMapper librariesAreaMapper;

    @BeforeClass
    public static void beforeClass() {
        chainWrappersMockedStatic = mockStatic(ChainWrappers.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), LibrariesArea.class);
    }

    /**
     * 关闭 chainWrappersMockedStatic.
     */
    @AfterClass
    public static void afterClass() {
        if (chainWrappersMockedStatic != null) {
            chainWrappersMockedStatic.close();
        }
    }

    @Before
    public void setup() {
        // 调用待测试的方法
        ReflectionTestUtils.setField(librariesAreaService, BASE_MAPPER, librariesAreaMapper);
        final LambdaQueryChainWrapper<LibrariesArea> lambdaUpdate = new LambdaQueryChainWrapper<>(librariesAreaMapper);
        lambdaUpdate.setEntity(new LibrariesArea());
        lambdaUpdate.setEntityClass(LibrariesArea.class);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(librariesAreaMapper)).thenReturn(lambdaUpdate);
    }

    @Test
    public void testGetStateAbbreviations() {
        List<String> result = librariesAreaService.getStateAbbreviations();

        Assert.assertEquals(0, result.size());
    }

    @Test
    public void testGetCountiesByStateCode() {
        List<String> result = librariesAreaService.getCountiesByStateCode("CA");

        Assert.assertEquals(0, result.size());
    }

}

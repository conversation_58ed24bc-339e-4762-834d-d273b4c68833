package com.learninggenie.api.service.impl;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.toolkit.ChainWrappers;
import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.model.healthcheck.*;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.common.data.dao.*;
import com.learninggenie.common.data.dao.impl.HealthStatisticsHistoryDaoImpl;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.entity.*;
import com.learninggenie.common.data.entity.UserEntity;
import com.learninggenie.common.data.enums.AgencyMetaKey;
import com.learninggenie.common.data.enums.FormType;
import com.learninggenie.common.data.enums.UserRole;
import com.learninggenie.common.data.enums.form.QuestionType;
import com.learninggenie.common.data.mapper.mybatisplus.dhc.HealthStatisticsHistoryChildMapper;
import com.learninggenie.common.data.model.*;
import com.learninggenie.common.data.repository.EnrollmentRepository;
import com.learninggenie.common.encryption.EncryptionService;
import com.learninggenie.common.sharding.ShardingProvider;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class HealthCheckServiceImplTest {
    @Spy
    @InjectMocks
    HealthCheckServiceImpl healthCheckService;
    @Mock
    UserProvider userProvider;
    @Mock
    private AgencyDao agencyDao;
    @Mock
    private EncryptionService encryptionService;
    @Mock
    private FormsFormDao formsFormDao;
    @Mock
    private CenterDao centerDao;
    @Mock
    private StudentDao studentDao;
    @Mock
    private HealthCheckFormDao healthCheckFormDao;
    @Mock
    private AttendanceEntityDao attendanceEntityDao;
    @Mock
    private HealthQuarantineDao quarantineDao;
    @Mock
    private FormsResponseRecordDao formsResponseRecordDao;
    @Mock
    private HealthStatisticsHistoryDao healthStatisticsHistoryDao;
    @Mock
    private GroupDao groupDao;
    @Mock
    private UserDaoImpl userDao;
    @Mock
    EnrollmentRepository enrollmentRepository;
    @Mock
    MessageDao messageDao;
    @Mock
    ShardingProvider shardingProvider;

    @Mock
    private HealthStatisticsHistoryDaoImpl healthStatisticsHistoryDaoImpl;

    @Mock
    private HealthStatisticsHistoryChildMapper healthStatisticsHistoryChildMapper;

    private MockedStatic<ChainWrappers> chainWrappersMockedStatic;

    /**
     * 初始化 TableInfoHelper
     */
    @BeforeAll
    public static void beforeClass() {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), HealthStatisticsHistoryChildEntity.class);
    }

    /**
     * 初始化 chainWrappersMockedStatic
     */
    @BeforeEach
    public void beforeMethod() {
        chainWrappersMockedStatic = mockStatic(ChainWrappers.class);
    }

    /**
     * 关闭 chainWrappersMockedStatic
     */
    @AfterEach
    public void afterMethod() {
        chainWrappersMockedStatic.close();
    }

    private HealthStatisticsHistoryDaoImpl getHealthStatisticsHistoryDaoSpy() {
        HealthStatisticsHistoryDaoImpl spy = spy(healthStatisticsHistoryDaoImpl);
        HealthStatisticsHistoryChildMapper mapper = this.healthStatisticsHistoryChildMapper;
        ReflectionTestUtils.setField(spy, "baseMapper", mapper);
        ReflectionTestUtils.setField(healthCheckService, "healthStatisticsHistoryDao", spy);  // 注入属性

        final LambdaUpdateChainWrapper<HealthStatisticsHistoryChildEntity> lambdaUpdate = new LambdaUpdateChainWrapper<>(mapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaUpdateChain(mapper)).thenReturn(lambdaUpdate);

        final LambdaQueryChainWrapper<HealthStatisticsHistoryChildEntity> lambdaQuery = new LambdaQueryChainWrapper<>(mapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(mapper)).thenReturn(lambdaQuery);
        return spy;
    }

    @Test
    void testNotifyTeacherArrival() {
        NotifyTeacherArrivalRequest notifyTeacherArrivalRequest = new NotifyTeacherArrivalRequest();
        notifyTeacherArrivalRequest.setChildIdList(Arrays.asList("1", "2"));
        notifyTeacherArrivalRequest.setMsg("Msg");

        when(userProvider.getTimezoneOffsetNum()).thenReturn(1);
        when(enrollmentRepository.findById(any())).thenReturn(Optional.of(new EnrollmentEntity()));
        when(userDao.getTeachersByStudentIds(any())).thenReturn(new ArrayList<>());
        when(userDao.getSiteAdminByChild(any())).thenReturn(new ArrayList<>());
        when(userDao.getAgencyAdminsByStudentId(any())).thenReturn(new ArrayList<>());

        // 没有老师,没有园长，则发送给机构管理员
        healthCheckService.notifyTeacherArrival(notifyTeacherArrivalRequest, "42");
        // 两个小孩，调取两次
        verify(userDao, times(2)).getAgencyAdminsByStudentId(any());
    }

    @Test
    public void test_getFormDefaultData() {
        // 数据准备
        String formId = "1";

        // 没有表单
        when(formsFormDao.selectById(formId)).thenReturn(null);
        assertThrows(BusinessException.class, () -> healthCheckService.getFormDefaultData(formId));
        // 清除 mockito 预设值，保障下个用例没有数据污染
        Mockito.reset(formsFormDao);

        // 表单有数据类型为 DHC 的
        // 模拟数据
        FormsFormEntity formsFormEntity = new FormsFormEntity();
        formsFormEntity.setType("CHILD_HEALTH");
        when(formsFormDao.selectById(formId)).thenReturn(formsFormEntity);
        List<HealthCheckFormsQuestion> questions = new ArrayList<>();
        HealthCheckFormsQuestion question = new HealthCheckFormsQuestion();
        List<HealthCheckFormsOptionModel> options = new ArrayList<>();
        HealthCheckFormsOptionModel option = new HealthCheckFormsOptionModel();
        option.setEffectAttendance(true);
        options.add(option);
        question.setOptions(options);
        questions.add(question);
        doReturn(questions).when(healthCheckService).setFormsQuestionsModel(any());
        lenient().when(encryptionService.decryptData(any())).thenReturn(new byte[]{});
        FormDefaultDataResponse response = healthCheckService.getFormDefaultData(formId);
        assertTrue(response.getQuestions().get(0).isEffectAttendance());
        // 清除 mockito 预设值，保障下个用例没有数据污染
        Mockito.reset(formsFormDao, encryptionService, healthCheckService);

        // 表单有数据类型不为 DHC 的
        // 模拟数据
        FormsFormEntity formsFormEntity2 = new FormsFormEntity();
        formsFormEntity2.setType("VHC");
        when(formsFormDao.selectById(formId)).thenReturn(formsFormEntity2);
        List<HealthCheckFormsQuestion> questions2 = new ArrayList<>();
        HealthCheckFormsQuestion question2 = new HealthCheckFormsQuestion();
        List<HealthCheckFormsOptionModel> options2 = new ArrayList<>();
        HealthCheckFormsOptionModel option2 = new HealthCheckFormsOptionModel();
        option2.setEffectAttendance(false);
        options2.add(option2);
        question2.setOptions(options2);
        questions2.add(question2);
        doReturn(questions2).when(healthCheckService).setFormsQuestionsModel(any());
        lenient().when(encryptionService.decryptData(any())).thenReturn(new byte[]{});
        FormDefaultDataResponse response2 = healthCheckService.getFormDefaultData(formId);
        assertFalse(response2.getQuestions().get(0).isEffectAttendance());
        // 清除 mockito 预设值，保障下个用例没有数据污染
        Mockito.reset(formsFormDao);
    }

    @Test
    public void test_setFormDefaultData() {
        // 准备数据
        DefaultFormRequest request = new DefaultFormRequest();
        request.setFormId("1");

        // 没有表单
        when(formsFormDao.selectById(any())).thenReturn(null);
        assertThrows(BusinessException.class, () -> healthCheckService.setFormDefaultData(request));
        // 清除 mockito 预设值，保障下个用例没有数据污染
        Mockito.reset(formsFormDao);

        // 开关打开时, 没弹窗过
        FormsFormEntity formsFormEntity = new FormsFormEntity();
        formsFormEntity.setType("child health");
        request.setOpen(true);
        when(formsFormDao.selectById(any())).thenReturn(formsFormEntity);
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("1");
        when(userProvider.getAgencyByUserId(any())).thenReturn(agencyModel);
        when(agencyDao.getMeta(any(), any())).thenReturn(null);
        healthCheckService.setFormDefaultData(request);
        verify(agencyDao, times(1)).setMeta("1", AgencyMetaKey.TIPS_SET_VHC_DEFAULT.toString(), "END");
        // 清除 mockito 预设值，保障下个用例没有数据污染
        Mockito.reset(agencyDao);

        // 开关打开, 弹过
        when(agencyDao.getMeta(any(), any())).thenReturn(new AgencyMetaDataEntity());
        healthCheckService.setFormDefaultData(request);
        verify(agencyDao, times(0)).setMeta("1", AgencyMetaKey.TIPS_SET_VHC_DEFAULT.toString(), "END");
        Mockito.reset(formsFormDao);

        // 有表单
        when(formsFormDao.selectById(any())).thenReturn(formsFormEntity);
        healthCheckService.setFormDefaultData(request);
        verify(formsFormDao, times(1)).updateById(Mockito.any(FormsFormEntity.class));
    }

    @Test
    public void test_getSignInCentersAndGroups() {
        String userId = "1";
        UserEntity user = new UserEntity();
        AgencyModel agency = new AgencyModel();

        // 当前用户没有机构
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.checkUser(userId)).thenReturn(user);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(null);
        List<CenterModel> response = healthCheckService.getSignInCentersAndGroups();
        assertEquals(0, response.size());
        Mockito.reset(userProvider);

        // 用户是机构管理员级别
        user.setRole(UserRole.AGENCY_OWNER.toString());
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.checkUser(userId)).thenReturn(user);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        healthCheckService.getSignInCentersAndGroups();
        verify(centerDao, times(1)).getCenterAndGroupsByAgencyUserId(userId);
        Mockito.reset(userProvider, centerDao);

        // 用户是院长级别
        user.setRole(UserRole.SITE_ADMIN.toString());
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.checkUser(userId)).thenReturn(user);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        healthCheckService.getSignInCentersAndGroups();
        verify(centerDao, times(1)).getCenterAndGroupsBySiteAdminId(userId);
        Mockito.reset(userProvider, centerDao);

        // 用户是老师级别
        user.setRole(UserRole.COLLABORATOR.toString());
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.checkUser(userId)).thenReturn(user);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        healthCheckService.getSignInCentersAndGroups();
        verify(centerDao, times(1)).getCenterAndGroupsByTeacherId(userId);
        Mockito.reset(userProvider, centerDao);

    }

    @Test
    public void test_listChildrenVHCFillInRecord() {
        ListChildrenVHCFillInRecordRequest request = new ListChildrenVHCFillInRecordRequest();
        List<String> centerIds = new ArrayList<>();
        centerIds.add("1");
        request.setCenterIds(centerIds);
        request.setShowFillIn(true);
        request.setCurrentDate(LocalDate.of(2022, 10, 12));
        request.setPageNum(1);
        request.setPageSize(1);

        String userId = "1";
        AgencyModel agency = new AgencyModel();
        agency.setId("1");
        HealthStatisticsHistoryDaoImpl healthStatisticsHistoryDaoSpy = getHealthStatisticsHistoryDaoSpy();

        when(healthStatisticsHistoryDaoSpy.lambdaQuery().list()).thenReturn(new ArrayList<>());
        doReturn(new FormDefaultDataResponse()).when(healthCheckService).getFormDefaultData(any());
        List<FormsQuestionEntity> formsQuestionEntities = new ArrayList<>();
        FormsQuestionEntity formsQuestionEntity = new FormsQuestionEntity();
        formsQuestionEntity.setId("1");
        formsQuestionEntity.setType(QuestionType.SINGLE_SELECT.toString());
        when(healthCheckFormDao.getFormsQuestions(any())).thenReturn(formsQuestionEntities);
        List<FormsOptionEntity> formsOptionEntities = new ArrayList<>();
        FormsOptionEntity formsOptionEntity = new FormsOptionEntity();
        formsOptionEntity.setQuestionId("1");
        formsOptionEntity.setName("No");
        String string = "{\"responses\":[{\"id\":\"69a28cb3-52bc-4dd8-bbd3-0f6583d9f57e\",\"formId\":\"F8C1E48B-BC49-429E-B3E1-890A0E9E5D4F\",\"questionId\":\"B0FE8AA4-CB45-4D95-81EA-12C3B642796D\",\"optionId\":\"4AA0EF7B-9E2E-48D4-96E8-DC1ED21D992D\",\"mediaKeys\":[]},{\"id\":\"b171850b-2eb2-42e8-9198-c39e2e67825d\",\"formId\":\"F8C1E48B-BC49-429E-B3E1-890A0E9E5D4F\",\"questionId\":\"1AED994E-63E8-45FD-B3E2-6914ABD1DABF\",\"optionId\":\"847DF13E-78E7-4E72-B1EA-D4B32F445EBC\",\"mediaKeys\":[]},{\"id\":\"9aaad012-c3f7-445f-b9f1-042410bbd393\",\"formId\":\"F8C1E48B-BC49-429E-B3E1-890A0E9E5D4F\",\"questionId\":\"8C9DC9D9-2C9F-4AFA-B8D3-6EC5E136E780\",\"optionId\":\"4AC25238-448A-4CF8-93E9-85E3DD045133\",\"mediaKeys\":[]},{\"id\":\"fa4511c1-d112-494b-ba6c-56c0b6b99c10\",\"formId\":\"F8C1E48B-BC49-429E-B3E1-890A0E9E5D4F\",\"questionId\":\"34E39981-8B86-4E2C-8FFE-775103192C2D\",\"optionId\":\"7477D56B-07B8-4B9B-9295-10537CF8C32F\",\"mediaKeys\":[]},{\"id\":\"0814e283-e28f-4bae-bdc3-93f3aadd9172\",\"formId\":\"F8C1E48B-BC49-429E-B3E1-890A0E9E5D4F\",\"questionId\":\"92D10940-127B-4A9C-86A5-A9A0D3A992EF\",\"optionId\":\"984C90E1-7E9E-4663-98C1-F1A9F40CD824\",\"mediaKeys\":[]},{\"id\":\"c12514f9-8abe-42ae-96bf-9fe1f9b2cc99\",\"formId\":\"F8C1E48B-BC49-429E-B3E1-890A0E9E5D4F\",\"questionId\":\"B4402000-E656-4D37-951B-E592467FDD87\",\"optionId\":\"6DA74A6E-6EE7-4C9F-8499-2094AF429B45\",\"mediaKeys\":[]},{\"id\":\"9cbee9c8-fd7d-454c-a365-3909e18114d9\",\"formId\":\"F8C1E48B-BC49-429E-B3E1-890A0E9E5D4F\",\"questionId\":\"4760A971-E157-4F4A-B0C1-EA5922C087C8\",\"optionId\":\"9A8BB582-9C6D-41DC-B578-FEB05A2CB6FB\",\"mediaKeys\":[]}],\"fillRecords\":[]}";
        byte[] bytes = string.getBytes();
        when(healthCheckFormDao.getFormsOptions(any())).thenReturn(formsOptionEntities);
        when(encryptionService.decryptData(any())).thenReturn(bytes);

        // 班级页面没传班级 ID
        request.setGroupIds(new ArrayList<>());
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        FormsFormEntity currentFormEntity = new FormsFormEntity();
        currentFormEntity.setId("1");
        when(formsFormDao.selectByAgencyIdAndType(agency.getId(), FormType.HEALTH_CHECK_QUESTION.toString())).thenReturn(currentFormEntity);
        assertThrows(BusinessException.class, () -> healthCheckService.listChildrenVHCFillInRecord(request));

        // 是班级请求
        List<String> groupIds = new ArrayList<>();
        groupIds.add("1");
        request.setGroupIds(groupIds);
        when(studentDao.getChildrenByGroup("1")).thenReturn(new ArrayList<>());
        when(attendanceEntityDao.listSignAttendanceByChildIds(anyList(), anyString())).thenReturn(new ArrayList<>());
        when(formsResponseRecordDao.getRecordsByChildIdsAndDateAndType(anyList(), anyString(), anyString(), anyList())).thenReturn(new ArrayList<>());
        healthCheckService.listChildrenVHCFillInRecord(request);
        verify(studentDao, times(1)).getChildrenByGroup("1");

        // 是学校请求没有传学校 ID 和班级 ID
        request.setShowFillIn(false);
        request.setGroupIds(new ArrayList<>());
        request.setCenterIds(new ArrayList<>());
        ListChildrenVHCFillInRecordResponse response = healthCheckService.listChildrenVHCFillInRecord(request);
        List<VHCFormFillInRercod> list = response.getFormFillInRercodList();
        assertEquals(0, list.size());

        // 是学校请求
        request.setCenterIds(centerIds);
        request.setGroupIds(groupIds);
        when(groupDao.getChildrenByGroups(anyString())).thenReturn(new ArrayList<>());
        when(formsResponseRecordDao.getRecordsByChildIdsAndDateAndType(anyList(), anyString(), anyString(), anyList())).thenReturn(new ArrayList<>());
        lenient().when(attendanceEntityDao.listSignAttendanceByChildIds(anyList(), anyString())).thenReturn(new ArrayList<>());
//        when(attendanceEntityDao.getChildInfo(anyList(), anyString(), anyString(), anyInt(), anyInt())).thenReturn(new ArrayList<>());
        healthCheckService.listChildrenVHCFillInRecord(request);
        verify(attendanceEntityDao, times(0)).getChildInfo(anyList(), anyString(), anyString(), anyInt(), anyInt());
    }

    @Test
    public void test_batchFillInVHC() {
        // 请求数据
        FillInVHCRequest request = new FillInVHCRequest();
        request.setHealthCheckFormId("1");
        String userId = "1";

        // 模拟数据
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userDao.getUserById(userId)).thenReturn(new UserModel());
        when(formsFormDao.selectById(userId)).thenReturn(new FormsFormEntity());
        when(formsResponseRecordDao.getRecordsByChildIdsAndDateAndType(anyList(), anyString(), anyString())).thenReturn(new ArrayList<>());
        String byteString = "123123213";
        byte[] bytes = byteString.getBytes();
        doReturn(bytes).when(healthCheckService).getMySetDefaultData(anyString());

        // 是全选
        request.setClickSelectAll(true);
        healthCheckService.batchFillInVHC(request);
        verify(attendanceEntityDao, times(1)).listSignAttendance(anyList(), anyList(), anyString());
        Mockito.reset(attendanceEntityDao);

        // 不是全选
        request.setClickSelectAll(false);
        when(attendanceEntityDao.listByIds(anyList())).thenReturn(new ArrayList<>());
        healthCheckService.batchFillInVHC(request);
        verify(attendanceEntityDao, times(1)).listByIds(anyList());

    }
}
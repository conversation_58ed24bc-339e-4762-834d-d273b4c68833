package com.learninggenie.api.service.impl;

import com.amazonaws.services.lambda.model.InvokeResult;
import com.learninggenie.api.provider.RemoteProvider;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.common.data.dao.ReportDao;
import com.learninggenie.common.data.entity.ContentsQrCode;
import com.learninggenie.common.data.enums.PdfConvertStatus;
import com.learninggenie.common.data.model.PdfConvertJobEntity;
import com.learninggenie.common.filesystem.FileSystem;
import com.microtripit.mandrillapp.lutung.view.MandrillMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.ByteArrayInputStream;
import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

public class ContentsQrCodeServiceImplTest {

    @Mock
    private FileSystem fileSystem;

    @Mock
    private UserProvider userProvider;

    @Mock
    private ReportDao reportDao;

    @Mock
    private RemoteProvider remoteProvider;

    @InjectMocks
    private ContentsQrCodeServiceImpl contentsQrCodeService;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testGetQrCodeHtml() {
        // 创建一个 ContentsQrCode 对象，并设置其属性
        ContentsQrCode contentsQrCode = new ContentsQrCode();
        contentsQrCode.setCenterName("testCenterName");
        contentsQrCode.setGroupName("testGroupName");
        contentsQrCode.setQrCodeURL("testQrCodeURL");

        // 当调用 fileSystem 的 getPublicUrl 方法时，返回 "testPublicUrl"
        when(fileSystem.getPublicUrl(anyString())).thenReturn("testPublicUrl");
        // 当调用 remoteProvider 的 callPdfService 方法时，返回一个状态码为 200 的 InvokeResult
        when(remoteProvider.callPdfService(anyString(), anyList())).thenReturn(CompletableFuture.completedFuture(new InvokeResult().withStatusCode(200)));

        // 创建一个 PdfConvertJobEntity 对象，并设置其属性
        PdfConvertJobEntity pdfConvertJobEntity = new PdfConvertJobEntity();
        pdfConvertJobEntity.setId("jobId");
        pdfConvertJobEntity.setStatus(PdfConvertStatus.SUCCEED.toString());
        pdfConvertJobEntity.setPdfUrl("testPdfUrl");

        // 当调用 reportDao 的 getPdfJob 方法时，返回上面创建的 PdfConvertJobEntity 对象
        when(reportDao.getPdfJob(anyString())).thenReturn(pdfConvertJobEntity);
        // 当调用 fileSystem 的 getFileStream 方法时，返回一个新的 ByteArrayInputStream 对象
        when(fileSystem.getFileStream(anyString())).thenReturn(new ByteArrayInputStream("testContent".getBytes()));

        // 使用反射设置 contentsQrCodeService 的字段值
        ReflectionTestUtils.setField(contentsQrCodeService, "emailTemplateVersion", "v1");

        // 调用被测试的方法
        MandrillMessage.MessageContent result = contentsQrCodeService.getQrCodeHtml(contentsQrCode);

        // 断言结果
        // 验证返回的类型是否为 "application/pdf"
        assertEquals("application/pdf", result.getType());
        // 验证返回的名称是否为 "testGroupName Sign in/out QR Code.pdf"
        assertEquals("testGroupName Sign in/out QR Code.pdf", result.getName());
    }
}
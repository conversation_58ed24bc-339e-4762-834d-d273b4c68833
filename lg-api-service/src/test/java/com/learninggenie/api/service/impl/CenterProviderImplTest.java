package com.learninggenie.api.service.impl;

import com.learninggenie.api.provider.impl.CenterProviderImpl;
import com.learninggenie.common.data.dao.CentersMetaDataDao;
import com.learninggenie.common.data.dao.UsersMetaDataDao;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.entity.CenterEntity;
import com.learninggenie.common.data.entity.CenterMetaDataEntity;
import com.learninggenie.common.data.enums.CenterMetaKey;
import com.learninggenie.common.data.model.UserModel;
import com.learninggenie.common.data.model.UsersMetaDataEntity;
import com.learninggenie.common.data.repository.CenterRepository;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.when;

/**
 * 2018-05-21
 */
@RunWith(MockitoJUnitRunner.class)
public class CenterProviderImplTest {
    @InjectMocks
    private CenterProviderImpl centerProvider;
    @Mock
    private UserDaoImpl userDao;
    @Mock
    private UsersMetaDataDao userMetaDao;
    @Mock
    private CentersMetaDataDao centersMetaDataDao;
    @Mock
    private CenterRepository centerRepository;

    /**
     * siteAdmin的metaValue为true
     */
    @Test
    public void testhasNoAcademySiteAdmin() {
        String centerId = "c001";
        //sites
        List<UserModel> sites = new ArrayList<>();
        UserModel user = new UserModel();
        user.setId("site001");
        sites.add(user);
        //metaDatas
        UsersMetaDataEntity metaData = new UsersMetaDataEntity();
        ArrayList<UsersMetaDataEntity> metaDatas = new ArrayList<>();
        metaData.setMetaKey(CenterMetaKey.ACADEMY_OPEN_FLAG.toString());
        metaData.setMetaValue("true");
        metaDatas.add(metaData);

        when(userDao.getSiteAdminsByCenterIds(Arrays.asList(centerId))).thenReturn(sites);
        when(userMetaDao.getMetas(CenterMetaKey.ACADEMY_OPEN_FLAG.toString(), sites.get(0).getId())).thenReturn(metaDatas);

        boolean result = centerProvider.hasNoAcademySiteAdmin(centerId);

        Assert.assertEquals(false,result);
    }

    /**
     * Case:center的学院模式开启
     * 结果:返回true
     */
    @Test
    public void testCheckAcamedyOpen_centerAcamedyIsOff() {
        String centerId = "c001";
        List<CenterMetaDataEntity> metaDatas = new ArrayList<>();
        CenterMetaDataEntity metaData = new CenterMetaDataEntity();
        metaData.setId("m001");
        metaData.setMetaKey("ACADEMY_OPEN_FLAG");
        metaData.setMetaValue("true");
        metaDatas.add(metaData);
        CenterEntity center = new CenterEntity();
        when(centersMetaDataDao.getMetas(CenterMetaKey.ACADEMY_OPEN_FLAG.toString(), centerId)).thenReturn(metaDatas);
        boolean open = centerProvider.checkAcamedyOpen(centerId);
        Assert.assertEquals(true,open);
    }

    /**
     * Case:center的学院模式未开启
     * 结果:返回false
     */
    @Test
    public void testCheckAcamedyOpen_centerAcamedyIsOpen() {
        String centerId = "c001";
        List<CenterMetaDataEntity> metaDatas = new ArrayList<>();
        CenterMetaDataEntity metaData = new CenterMetaDataEntity();
        metaData.setId("m001");
        metaData.setMetaKey("ACADEMY_OPEN_FLAG");
        metaData.setMetaValue("0");
        metaDatas.add(metaData);
        CenterEntity center = new CenterEntity();
        when(centersMetaDataDao.getMetas(CenterMetaKey.ACADEMY_OPEN_FLAG.toString(), centerId)).thenReturn(metaDatas);
        boolean open = centerProvider.checkAcamedyOpen(centerId);
        Assert.assertEquals(false,open);
    }

    /**
     * Case:center的学院模式未设置
     * 结果:返回false
     */
    @Test
    public void testCheckAcamedyOpen_metaDataIsNull() {
        String centerId = "c001";
        CenterEntity center = new CenterEntity();
        when(centersMetaDataDao.getMetas(CenterMetaKey.ACADEMY_OPEN_FLAG.toString(), centerId)).thenReturn(null);
        boolean open = centerProvider.checkAcamedyOpen(centerId);
        Assert.assertEquals(false,open);
    }
}

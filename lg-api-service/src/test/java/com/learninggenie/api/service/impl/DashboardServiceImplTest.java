package com.learninggenie.api.service.impl;

import com.learninggenie.api.constant.RedisKeyPrefix;
import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.model.DashboardDataRequest;
import com.learninggenie.api.model.InvitationStateConstants;
import com.learninggenie.api.model.center.CenterGroup;
import com.learninggenie.api.model.center.CenterResponse;
import com.learninggenie.api.model.dashboard.EngagementsStatsModel;
import com.learninggenie.api.model.dashboard.PortfolioDashboardRequest;
import com.learninggenie.api.model.googleslides.ReplaceRelation;
import com.learninggenie.api.model.report.*;
import com.learninggenie.api.provider.EnrollmentProvider;
import com.learninggenie.api.provider.PortfolioProvider;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.security.AuthUserDetails;
import com.learninggenie.api.service.FilterService;
import com.learninggenie.api.service.GroupService;
import com.learninggenie.api.service.MediaService;
import com.learninggenie.api.service.UserService;
import com.learninggenie.common.cache.CacheModel;
import com.learninggenie.common.cache.CacheService;
import com.learninggenie.common.data.dao.*;
import com.learninggenie.common.data.dao.analysis.FilterViewDao;
import com.learninggenie.common.data.dao.analysis.FilterViewGroupDao;
import com.learninggenie.common.data.dao.enrollment.EnrollmentPeriodDao;
import com.learninggenie.common.data.dao.frameworks.FrameworkDao;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.dto.EnrollmentDTO;
import com.learninggenie.common.data.dto.GroupWithCenter;
import com.learninggenie.common.data.entity.GroupEntity;
import com.learninggenie.common.data.entity.UserEntity;
import com.learninggenie.common.data.entity.*;
import com.learninggenie.common.data.entity.analysis.FilterViewEntity;
import com.learninggenie.common.data.entity.analysis.FilterViewGroupEntity;
import com.learninggenie.common.data.entity.frameworks.FrameworkEntity;
import com.learninggenie.common.data.enums.AgencyMetaKey;
import com.learninggenie.common.data.enums.UserRole;
import com.learninggenie.common.data.enums.analysis.FilterViewType;
import com.learninggenie.common.data.model.DomainEntity;
import com.learninggenie.common.data.model.*;
import com.learninggenie.common.data.model.dashboard.*;
import com.learninggenie.common.data.repository.UserRepository;
import com.learninggenie.common.filesystem.FileSystem;
import com.learninggenie.common.score.RatingService;
import com.learninggenie.common.utils.StringUtil;
import com.learninggenie.common.utils.TimeUtil;
import com.learninggenie.common.weekly.WeeklyServiceImpl;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.RoundingMode;
import java.text.NumberFormat;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.*;

/**
 * Created by Sena on 2017/5/2.
 */
@RunWith(MockitoJUnitRunner.class)
public class DashboardServiceImplTest {
    @Mock
    private UserProvider userProvider;

    @Mock
    private CacheService cacheService;

    @InjectMocks
    private DashboardServiceImpl dashboardService;

    @Mock
    private UserDaoImpl userDao;

    @Mock
    private DashboardDao dashboardDao;

    @Mock
    private DomainDao domainDao;

    @Mock
    private AgencyDao agencyDao;

    @Mock
    private UserRepository userRepository;

    @Mock
    private GroupDao groupDao;
    @Mock

    private StudentDao studentDao;

    @Mock
    private CenterDao centerDao;

    @Mock
    private WeeklyServiceImpl weeklyService;

    @Mock
    private GroupService groupService;

    @Mock
    private FilterViewDao filterViewDao;

    @Mock
    private FilterViewGroupDao filterViewGroupDao;

    @Mock
    private EnrollmentProvider enrollmentProvider;

    @Mock
    private EnrollmentDao enrollmentDao;

    @Mock
    private PortfolioProvider portfolioProvider;
    @Mock
    private EnrollmentPeriodDao enrollmentPeriodDao;
    @Mock
    private RatingService ratingService;
    @Mock
    private FrameworkDao frameworkDao;
    @Mock
    private UserService userService;

    @Mock
    private FileSystem fileSystem;
    @Mock
    private MediaService mediaService;
    @Mock
    private MessageDao messageDao;
    @Mock
    private EventDao eventDao;
    @Mock
    private InkindDao inkindDao;
    @Mock
    private FilterService filterService;

    /**
     * Case : 统计学校数据精确数据
     * 结果 : 通过
     *
     * @throws Exception
     */
    @Test
    @Ignore
    public void mockCenterDashboard() throws Exception {
        String userId = "U123";
        String agencyId = "A123";
        String domainId = "D123";
        String domainName = "DN123";
        String centerId = "C123";
        String centerName = "CN123";
        String groupId = "G123";
        String groupName = "GN123";

        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setRole("SITE_ADMIN");
        Mockito.when(userProvider.checkUser(userId)).thenReturn(user);

        DomainEntity domain = new DomainEntity();
        domain.setId(domainId);
        domain.setName(domainName);
        Mockito.when(domainDao.getDomain(domainId)).thenReturn(domain);

        /**
         * 封装了5个学校, 5个班级 10个孩子
         */
        List<CenterGroupChildCountModel> groupChildCountModels = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            CenterGroupChildCountModel groupChildCountModel = new CenterGroupChildCountModel();
            groupChildCountModel.setGroupId(groupId + i);
            groupChildCountModel.setGroupName(groupName + i);
            groupChildCountModel.setCenterId(centerId + i);
            groupChildCountModel.setCenterName(centerName + i);
            groupChildCountModel.setChildCount(i);//循环五次  10个孩子
            groupChildCountModel.setDomainId(domainId);
            groupChildCountModels.add(groupChildCountModel);
        }

        List<DomainEntity> domains = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            domains.add(domain);
        }
        Mockito.when(domainDao.getAllChildDomains(Mockito.anyString())).thenReturn(domains);
        Mockito.when(dashboardDao.getCenterGroupChildCount(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(groupChildCountModels);
        Mockito.when(weeklyService.hasScoreTemplate(Mockito.anyString())).thenReturn(true);

        /**
         * 封装Child的时候封装的是5个学校, 加载Note时只获取4个学校, 另外一个 默认没note
         */
        List<CenterGroupNotesCountModel> notesCounts = new ArrayList<>();
        for (int i = 0; i < 4; i++) {
            CenterGroupNotesCountModel groupNotesCount = new CenterGroupNotesCountModel();
            groupNotesCount.setCenterId(centerId + i);
            groupNotesCount.setCenterName(centerName + i);
            groupNotesCount.setGroupId(groupId + i);
            groupNotesCount.setCenterName(groupName + i);
            groupNotesCount.setNotesCount(i);  //6个note
            notesCounts.add(groupNotesCount);
        }
        Mockito.when(dashboardDao.getCenterGroupNotesCount(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(notesCounts);

        List<CenterGroupScoreMeasureCountModel> scoreMeasureCounts = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            CenterGroupScoreMeasureCountModel groupScoreMeasureCountModel = new CenterGroupScoreMeasureCountModel();
            groupScoreMeasureCountModel.setCenterId(centerId + i);
            groupScoreMeasureCountModel.setCenterName(centerName + i);
            groupScoreMeasureCountModel.setGroupId(groupId + i);
            groupScoreMeasureCountModel.setGroupName(groupName + i);
            groupScoreMeasureCountModel.setDistinctScoreMeasureCount(i); //10个Measure

            scoreMeasureCounts.add(groupScoreMeasureCountModel);
        }
        Mockito.when(dashboardDao.getCenterGroupScoreMeasureCount(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(scoreMeasureCounts);

        List<CenterGroupMeasureCountModel> measureCounts = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            CenterGroupMeasureCountModel groupMeasureCount = new CenterGroupMeasureCountModel();
            groupMeasureCount.setCenterId(centerId + i);
            groupMeasureCount.setCenterName(centerName + i);
            groupMeasureCount.setDistinctCenterMeasureCount(i);
            groupMeasureCount.setDomainId(domainId + i);
            groupMeasureCount.setGroupId(groupId + i);
            groupMeasureCount.setGroupName(groupName + i);
            groupMeasureCount.setMeasureNotesCount(i);
            measureCounts.add(groupMeasureCount);
        }
        List<EnrollmentSnapshotCount> snapshotCounts = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            EnrollmentSnapshotCount snapshot = new EnrollmentSnapshotCount();
            snapshot.setChildId(i + "");
            snapshot.setSnapshotCount(1);
            snapshotCounts.add(snapshot);
        }
        Mockito.when(dashboardDao.getCenterGroupMeasureCount(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(measureCounts);
        Mockito.when(dashboardDao.getCenterGroupChildCount(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(groupChildCountModels);
        Mockito.when(weeklyService.hasScoreTemplate(Mockito.anyString())).thenReturn(true);
        Mockito.when(studentDao.getChildSnapshotCountByGroupId(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(snapshotCounts);
        PortfolioDashboardRequest paRequest = new PortfolioDashboardRequest();
        paRequest.setAgencyId(agencyId);
        paRequest.setAlias(null);
        paRequest.setFrameworkId(domainId);
        paRequest.setCenterId(centerId);
        paRequest.setGroupId(null);
        paRequest.setAllGroup(false);
        paRequest.setCenterIds("");
        paRequest.setAllCenter(false);
        List<DashboardDataRequest> requests = dashboardService.getDashboardData(userId, paRequest);
        DashboardDataRequest request = requests.get(0);
        Assert.assertTrue(request.getChildCount() == 10);
        int groupsCanUserMeasure = 0;
        for (int i = 0; i < 5; i++) {
            Assert.assertTrue(request.getGroupDataModels().get(i).getMeasureCanUseCount() == request.getGroupDataModels().get(i).getChildCount() * 10);
            groupsCanUserMeasure += request.getGroupDataModels().get(i).getMeasureCanUseCount();
        }

        Assert.assertTrue(request.getTotalNotes() == 6);
        Assert.assertEquals(request.getRatedMeasures(), formatDouble(10 * 100.00 / groupsCanUserMeasure));
    }

    /**
     * Case : 统计班级数据精确数据
     * 结果 : 通过
     *
     * @throws Exception
     */
    @Test
    @Ignore
    public void mockGroupDashboard() throws Exception {
        String userId = "U123";
        String agencyId = "A123";
        String domainId = "D123";
        String domainName = "DN123";
        String centerId = "C123";
        String centerName = "CN123";
        String groupId = "G123";
        String groupName = "GN123";

        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setRole("SITE_ADMIN");
        Mockito.when(userProvider.checkUser(userId)).thenReturn(user);

        DomainEntity domain = new DomainEntity();
        domain.setId(domainId);
        domain.setName(domainName);
        Mockito.when(domainDao.getDomain(domainId)).thenReturn(domain);

        /**
         * 封装了5个学校, 5个班级 10个孩子
         */
        List<CenterGroupChildCountModel> groupChildCountModels = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            CenterGroupChildCountModel groupChildCountModel = new CenterGroupChildCountModel();
            groupChildCountModel.setCenterId(centerId + i);
            groupChildCountModel.setCenterName(centerName + i);
            groupChildCountModel.setGroupId(groupId + i);
            groupChildCountModel.setGroupName(groupName + i);
            groupChildCountModel.setChildCount(i);//循环五次  10个孩子
            groupChildCountModel.setDomainId(domainId);

            groupChildCountModels.add(groupChildCountModel);
        }

        List<DomainEntity> domains = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            domains.add(domain);
        }
        Mockito.when(domainDao.getAllChildDomains(Mockito.anyString())).thenReturn(domains);
        Mockito.when(dashboardDao.getCenterGroupChildCount(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(groupChildCountModels);
        Mockito.when(weeklyService.hasScoreTemplate(Mockito.anyString())).thenReturn(true);

        /**
         * 封装Child的时候封装的是5个学校, 加载Note时只获取4个学校, 另外一个 默认没note
         */
        List<CenterGroupNotesCountModel> notesCounts = new ArrayList<>();
        for (int i = 0; i < 4; i++) {
            CenterGroupNotesCountModel groupNotesCount = new CenterGroupNotesCountModel();
            groupNotesCount.setCenterId(centerId + i);
            groupNotesCount.setCenterName(centerName + i);
            groupNotesCount.setCenterName(groupName + i);
            groupNotesCount.setGroupId(groupId + i);
            groupNotesCount.setNotesCount(i);  //6个note
            notesCounts.add(groupNotesCount);
        }
        Mockito.when(dashboardDao.getCenterGroupNotesCount(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(notesCounts);

        List<GroupWithCenter> groupWithCenters = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            GroupWithCenter groupWithCenter = new GroupWithCenter();
            groupWithCenter.setGroupId(groupId + i);
            groupWithCenter.setGroupName(groupName + i);
            groupWithCenter.setCenterId(centerId + i);
            groupWithCenter.setCenterName(centerName + i);
            groupWithCenters.add(groupWithCenter);
        }
        Mockito.when(groupDao.getGroupByTeacherId(user.getId())).thenReturn(groupWithCenters);

        List<CenterGroupScoreMeasureCountModel> scoreMeasureCounts = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            CenterGroupScoreMeasureCountModel groupScoreMeasureCountModel = new CenterGroupScoreMeasureCountModel();
            groupScoreMeasureCountModel.setGroupId(groupId + i);
            groupScoreMeasureCountModel.setGroupName(groupName + i);
            groupScoreMeasureCountModel.setCenterId(centerId + i);
            groupScoreMeasureCountModel.setCenterName(centerName + i);
            groupScoreMeasureCountModel.setDistinctScoreMeasureCount(i); //10个Measure

            scoreMeasureCounts.add(groupScoreMeasureCountModel);
        }
        Mockito.when(dashboardDao.getCenterGroupScoreMeasureCount(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(scoreMeasureCounts);

        List<CenterGroupMeasureCountModel> measureCounts = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            CenterGroupMeasureCountModel groupMeasureCount = new CenterGroupMeasureCountModel();
            groupMeasureCount.setCenterId(centerId + i);
            groupMeasureCount.setCenterName(centerName + i);
            groupMeasureCount.setDistinctCenterMeasureCount(i);
            groupMeasureCount.setGroupId(groupId + i);
            groupMeasureCount.setGroupName(groupName + i);
            groupMeasureCount.setDomainId(domainId + i);
            groupMeasureCount.setMeasureNotesCount(i);
            measureCounts.add(groupMeasureCount);
        }
        Mockito.when(dashboardDao.getCenterGroupMeasureCount(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(measureCounts);

        List<EnrollmentCenterGroupModel> centerGroupLocks = new ArrayList<>();
        EnrollmentCenterGroupModel centerGroupLock = new EnrollmentCenterGroupModel();
        centerGroupLock.setCenterId(centerId);
        centerGroupLocks.add(centerGroupLock);
        Mockito.when(dashboardDao.getLockedEnrollment(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(centerGroupLocks);
        PortfolioDashboardRequest paRequest = new PortfolioDashboardRequest();
        paRequest.setAgencyId(agencyId);
        paRequest.setAlias(null);
        paRequest.setFrameworkId(domainId);
        paRequest.setCenterId(centerId);
        paRequest.setGroupId(null);
        paRequest.setAllGroup(false);
        paRequest.setCenterIds("");
        paRequest.setAllCenter(false);
        List<DashboardDataRequest> requests = dashboardService.getDashboardData(userId, paRequest);
        DashboardDataRequest request = requests.get(0);
        Assert.assertTrue(request.getChildCount() == 10);
        int groupsCanUserMeasure = 0;
        for (int i = 0; i < 5; i++) {
            Assert.assertTrue(request.getGroupDataModels().get(i).getMeasureCanUseCount() == request.getGroupDataModels().get(i).getChildCount() * 10);
            groupsCanUserMeasure += request.getGroupDataModels().get(i).getMeasureCanUseCount();
        }

        Assert.assertTrue(request.getTotalNotes() == 6);
        Assert.assertEquals(request.getRatedMeasures(), formatDouble(10 * 100.00 / groupsCanUserMeasure));
        Assert.assertEquals(request.getLockedChildren(), formatDouble(5 * 100.00 / request.getChildCount()));
    }

    /**
     * Case : 统计老师下面每个孩子的数据
     * 结果 : 通过
     *
     * @throws Exception
     */
    @Test
    @Ignore
    public void mockTeacherChildDashboard() throws Exception {
        String userId = "U123";
        String agencyId = "A123";
        String domainId = "D123";
        String domainName = "DN123";
        String centerId = "C123";
        String centerName = "CN123";
        String groupId = "G123";
        String groupName = "GN123";
        String childId = "E123";
        String childName = "EN123";

        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setRole("COLLABORATOR");
        Mockito.when(userProvider.checkUser(userId)).thenReturn(user);

        DomainEntity domain = new DomainEntity();
        domain.setId(domainId);
        domain.setName(domainName);
        Mockito.when(domainDao.getDomain(domainId)).thenReturn(domain);
        String groupIds = "";
        List<GroupWithCenter> groupWithCenters = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            GroupWithCenter groupWithCenter = new GroupWithCenter();
            groupWithCenter.setCenterId(centerId + i);
            groupWithCenter.setCenterName(centerName + i);
            groupWithCenter.setGroupId(groupId + i);
            groupWithCenter.setGroupName(groupName + i);
            groupWithCenters.add(groupWithCenter);
            groupIds = groupIds + groupId + ",";
        }
        if (!StringUtil.isEmptyOrBlank(groupIds)) {
            groupIds = groupIds.substring(0, groupIds.length() - 1);
        }
        Mockito.when(groupDao.getGroupByTeacherId(user.getId())).thenReturn(groupWithCenters);

        List<EnrollmentNotesCountModel> notesCountModels = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            EnrollmentNotesCountModel notesCount = new EnrollmentNotesCountModel();
            notesCount.setChildId(childId + i);
            notesCount.setChildName(childName + i);
            notesCount.setDomainId(domainId);
            notesCount.setNotesCount(i);
            notesCount.setSnapshotCount(i);
            notesCountModels.add(notesCount);
        }
        Mockito.when(dashboardDao.getChildNotesCount(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(notesCountModels);


        List<DomainEntity> domains = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            domains.add(domain);
        }
        Mockito.when(domainDao.getAllChildDomains(Mockito.anyString())).thenReturn(domains);

        List<EnrollmentScoreMeasureCountModel> scoreMeasureCountModels = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            EnrollmentScoreMeasureCountModel scoreMeasureCount = new EnrollmentScoreMeasureCountModel();
            scoreMeasureCount.setChildId(childId + i);
            scoreMeasureCount.setChildName(childName + i);
            scoreMeasureCount.setScoreMeasureCount(i);
            scoreMeasureCountModels.add(scoreMeasureCount);
        }
        Mockito.when(dashboardDao.getChildScoreMeasureCount(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(scoreMeasureCountModels);

        List<EnrollmentEntity> enrollmentEntities = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            com.learninggenie.common.data.entity.DomainEntity domainEntity = new com.learninggenie.common.data.entity.DomainEntity();
            domainEntity.setId(domainId);
            domainEntity.setName(domainName);
            com.learninggenie.common.data.entity.GroupEntity groupEntity = new com.learninggenie.common.data.entity.GroupEntity();
            groupEntity.setId(groupId + i);
            groupEntity.setName(groupName + i);
            groupEntity.setDomain(domainEntity);
            EnrollmentEntity enrollmentEntity = new EnrollmentEntity();
            enrollmentEntity.setId(childId + i);
            enrollmentEntity.setDisplayName(childName + i);
            enrollmentEntity.setGroup(groupEntity);

            enrollmentEntities.add(enrollmentEntity);
        }

        List<EnrollmentMeasureCountModel> measureCountModels = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            EnrollmentMeasureCountModel measureCount = new EnrollmentMeasureCountModel();
            measureCount.setChildId(childId + i);
            measureCount.setChildName(childName + i);
            measureCount.setDistinctMeasureCount(i);
            measureCount.setDomainId(domainId + i);
            measureCount.setMeasureNotesCount(i);
            measureCountModels.add(measureCount);
        }
        Mockito.when(dashboardDao.getChildMeasureCount(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(measureCountModels);
        Mockito.when(studentDao.getChildByGroupId(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(enrollmentEntities);
        Mockito.when(weeklyService.hasScoreTemplate(Mockito.anyString())).thenReturn(true);
        GroupEntry group = new GroupEntry();
        group.setDomainId(domainId);
        Mockito.when(groupDao.getGroup(Mockito.anyString())).thenReturn(group);
        PortfolioDashboardRequest paRequest = new PortfolioDashboardRequest();
        paRequest.setAgencyId(agencyId);
        paRequest.setAlias(null);
        paRequest.setFrameworkId(domainId);
        paRequest.setCenterId(centerId);
        paRequest.setGroupId("66");
        paRequest.setAllGroup(false);
        paRequest.setCenterIds("");
        paRequest.setAllCenter(false);
        List<DashboardDataRequest> requests = dashboardService.getDashboardData(userId, paRequest);
        DashboardDataRequest request = requests.get(0);

        Assert.assertTrue(request.getChildCount() == 10);
        Assert.assertTrue(request.getTotalNotes() == 45);
        Assert.assertTrue(request.getObservedMeasures().equalsIgnoreCase("10"));
        Assert.assertTrue(request.getAvgNotesPerMeasure().equalsIgnoreCase("1"));
        for (int i = 0; i < 10; i++) {
            //Assert.assertEquals(request.getChildDataModels().get(i).getRatedMeasures(), formatDouble(request.getChildDataModels().get(i).getRatedMeasureUseCount() * 100.00 / 10));
        }

    }

    /**
     * Case : 用户Agency为找到
     * 结果 : 跑出异常
     *
     * @throws Exception
     */
    @Test(expected = BusinessException.class)
    public void getDashboardFilterUserAgencyIsNull() throws Exception {
//        String userId = "123456";
        String agencyId = "a21";
//        Mockito.when(userProvider.getAgencyByUserId(userId)).thenReturn(null);

        UserEntity user = new UserEntity();
        user.setId("U123");
        user.setRole("SITE_ADMIN");
        Mockito.lenient().when(userProvider.checkUser(Mockito.anyString())).thenReturn(user);
        Mockito.lenient().when(cacheService.exist(RedisKeyPrefix.DASHBOARD_FRAMEWORK + "u123" + agencyId + false + "")).thenReturn(true);
        CacheModel cacheModel = new CacheModel();
        cacheModel.setValue("");
        Mockito.lenient().when(cacheService.get(RedisKeyPrefix.DASHBOARD_FRAMEWORK + "u123" + agencyId + false + "")).thenReturn(cacheModel);


        dashboardService.getDashboardFilter("u123", agencyId,  false);
    }

    /**
     * Case : 角色为SiteAdmin Filter数据统计
     * 结果 : 通过
     */
    @Test
    @Ignore
    public void checkUserIsSiteAdmin() throws Exception {
        UserEntity user = new UserEntity();
        user.setId("U123");
        user.setRole("SITE_ADMIN");

        String agencyId = "A123";
        AgencyModel agency = new AgencyModel();
        agency.setId("A123");
        Mockito.when(userProvider.getAgencyByUserId(user.getId())).thenReturn(agency);

        Mockito.when(userProvider.checkUser(Mockito.anyString())).thenReturn(user);
        List<AgencyIdentifierEntity> agencyIdentifiers = new ArrayList<>();
        AgencyIdentifierEntity agencyIdentifier = new AgencyIdentifierEntity();
        agencyIdentifier.setId("I123");
        agencyIdentifier.setName("IName");
        agencyIdentifiers.add(agencyIdentifier);

        Mockito.when(agencyDao.getIdentifierByUserId(Mockito.anyString(), Mockito.anyString())).thenReturn(agencyIdentifiers);

        List<DashboardFilterRequest> dashboardFilterRequests = dashboardDao.getDashboardFilter(agencyId, null, false);
        for (int i = 0; i < 3; i++) {
            DashboardFilterRequest dashboardFilterRequest = new DashboardFilterRequest();
            dashboardFilterRequest.setAgencyId("A123");
            dashboardFilterRequest.setAgencyName("AName" + i);
            dashboardFilterRequest.setCenterId("C123" + i);
            dashboardFilterRequest.setCenterName("CName" + i);
            dashboardFilterRequest.setGroupId("G123" + i);
            dashboardFilterRequest.setGroupName("GName" + i);
            dashboardFilterRequest.setDomainId("D123");
            dashboardFilterRequest.setDomainName("DName" + i);
            dashboardFilterRequests.add(dashboardFilterRequest);
        }
        Mockito.when(dashboardDao.getDashboardFilterByGrantee(Mockito.anyList(), Mockito.anyString(), anyBoolean(), Mockito.anyList())).thenReturn(dashboardFilterRequests);
        List<DashboardFilterView> dashboardFilterViews = dashboardService.getDashboardFilter("", agencyId,  false);
        Assert.assertTrue(dashboardFilterViews.size() == 2);
        Assert.assertTrue(dashboardFilterViews.get(0).getFrameworkName().equalsIgnoreCase("All Frameworks"));
        Assert.assertTrue(dashboardFilterViews.get(0).getDashboardFilterAgencyViews().get(0).getCenterEntities().size() == 3);
    }

    @Test
    public void getDashboardFilterUser() throws Exception {
        String userId = "123456";
//        String agencyId = "a001";
        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setRole("SITE_ADMIN");
//        Mockito.when(userProvider.checkUser(userId)).thenReturn(user);

        List<CenterModel> centerModels = new ArrayList<>();
        CenterModel center = new CenterModel();
        center.setId("c001");
        centerModels.add(center);
//        Mockito.when(userDao.getCentersBySiteAdminId(user.getId())).thenReturn(centerModels);

        List<DashboardFilterRequest> dashboardFilterRequests = new ArrayList<>();
        DashboardFilterRequest filterRequest = new DashboardFilterRequest();

        AgencyModel agency = new AgencyModel();
        agency.setId("a001");
//        Mockito.when(userProvider.getAgencyByUserId(Mockito.anyString())).thenReturn(agency);

        filterRequest.setCenterId("c001");
        filterRequest.setAgencyId("a001");
        dashboardFilterRequests.add(filterRequest);
//        Mockito.when(dashboardDao.getDashboardFilter(Mockito.anyString(), Mockito.anyString(), anyBoolean())).thenReturn(dashboardFilterRequests);

//        dashboardService.getDashboardFilter(userId, agencyId,  false);
    }

    @Test
    @Ignore
    public void getDashboardDataUserIsSiteAdmin() throws Exception {
        String userId = "U123";
        String agencyId = "A123";
        String frameworkId = "D123";
        String centerId = "C123";
        String centerName = "CN123";
        String groupId = "G123";
        String groupName = "GN123";

        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setRole("SITE_ADMIN");

        Mockito.when(userProvider.checkUser(userId)).thenReturn(user);

        List<CenterModel> centerModels = new ArrayList<>();
        CenterModel center = new CenterModel();
        center.setId("C123");
        centerModels.add(center);
        Mockito.when(userDao.getCentersBySiteAdminId(userId)).thenReturn(centerModels);

        List<DomainEntity> domainEntities = new ArrayList<>();
        DomainEntity domain = new DomainEntity();
        domain.setId("D123");
        domainEntities.add(domain);
        Mockito.when(domainDao.getAllChildDomains("D123")).thenReturn(domainEntities);

//        String domainIds = "D123";
        String domainId = "D123";
//        String groupIds = "'G123','G1234'";

        String childId = "E1345";
        String childName = "E88877";
        int noteCount = 6;
        int snapshotCount = 6;

        DomainEntity domainEntity = new DomainEntity();
        domainEntity.setId(domainId);
        domainEntity.setName("DName");
        Mockito.when(domainDao.getDomain(domainId)).thenReturn(domainEntity);

        List<EnrollmentNotesCountModel> enrollmentNotesCounts = new ArrayList<>();
        EnrollmentNotesCountModel notesCountModel = new EnrollmentNotesCountModel();
        notesCountModel.setChildId(childId);
        notesCountModel.setChildName(childName);
        notesCountModel.setNotesCount(noteCount);
        notesCountModel.setSnapshotCount(snapshotCount);
        enrollmentNotesCounts.add(notesCountModel);
        Mockito.when(dashboardDao.getChildNotesCount(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(enrollmentNotesCounts);

        List<CenterGroupNotesCountModel> notesCountModels = new CopyOnWriteArrayList<>();
        for (int i = 0; i < 5; i++) {
            CenterGroupNotesCountModel centerGroupNotesCountModel = new CenterGroupNotesCountModel();
            centerGroupNotesCountModel.setCenterId(centerId + i);
            centerGroupNotesCountModel.setNotesCount(i);
            centerGroupNotesCountModel.setGroupId(i + "");
            notesCountModels.add(centerGroupNotesCountModel);
        }
        List<EnrollmentScoreMeasureCountModel> enrollmentScoreMeasureCounts = new ArrayList<>();
        EnrollmentScoreMeasureCountModel scoreMeasureCount = new EnrollmentScoreMeasureCountModel();
        enrollmentScoreMeasureCounts.add(scoreMeasureCount);

        List<EnrollmentSnapshotCount> snapshotCounts = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            EnrollmentSnapshotCount snapshot = new EnrollmentSnapshotCount();
            snapshot.setChildId(i + "");
            snapshot.setSnapshotCount(1);
            snapshotCounts.add(snapshot);
        }
        /**
         * 封装了5个学校, 5个班级 10个孩子
         */
        List<CenterGroupChildCountModel> groupChildCountModels = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            CenterGroupChildCountModel groupChildCountModel = new CenterGroupChildCountModel();
            groupChildCountModel.setCenterId(centerId + i);
            groupChildCountModel.setCenterName(centerName + i);
            groupChildCountModel.setGroupId(groupId + i);
            groupChildCountModel.setGroupName(groupName + i);
            groupChildCountModel.setChildCount(i);//循环五次  10个孩子
            groupChildCountModel.setDomainId(domainId);

            groupChildCountModels.add(groupChildCountModel);
        }
        List<CenterGroupMeasureCountModel> measureCounts = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            CenterGroupMeasureCountModel groupMeasureCount = new CenterGroupMeasureCountModel();
            groupMeasureCount.setCenterId(centerId + i);
            groupMeasureCount.setCenterName(centerName + i);
            groupMeasureCount.setDistinctCenterMeasureCount(i);
            groupMeasureCount.setGroupId(groupId + i);
            groupMeasureCount.setGroupName(groupName + i);
            groupMeasureCount.setDomainId(domainId + i);
            groupMeasureCount.setMeasureNotesCount(i);
            measureCounts.add(groupMeasureCount);
        }
        Mockito.when(dashboardDao.getCenterGroupNotesCount(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(notesCountModels);
        Mockito.when(dashboardDao.getChildScoreMeasureCount(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(enrollmentScoreMeasureCounts);
        Mockito.when(dashboardDao.getCenterGroupMeasureCount(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(measureCounts);
        Mockito.when(dashboardDao.getCenterGroupChildCount(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(groupChildCountModels);
        Mockito.when(weeklyService.hasScoreTemplate(Mockito.anyString())).thenReturn(true);
        Mockito.when(studentDao.getChildSnapshotCountByGroupId(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(snapshotCounts);
        List<EnrollmentMeasureCountModel> enrollmentMeasureCounts = new ArrayList<>();
        EnrollmentMeasureCountModel measureCount = new EnrollmentMeasureCountModel();
        enrollmentMeasureCounts.add(measureCount);
        Mockito.when(dashboardDao.getChildMeasureCount(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(enrollmentMeasureCounts);

        PortfolioDashboardRequest paRequest = new PortfolioDashboardRequest();
        paRequest.setAgencyId(agencyId);
        paRequest.setAlias(null);
        paRequest.setFrameworkId(frameworkId);
        paRequest.setCenterId(null);
        paRequest.setGroupId(null);
        paRequest.setAllGroup(false);
        paRequest.setCenterIds("");
        paRequest.setAllCenter(false);
        List<DashboardDataRequest> dataRequests = dashboardService.getDashboardData(userId, paRequest);
        Assert.assertTrue(dataRequests.get(0).getNoAliasChildrenCount() == 0);
        Assert.assertTrue(dataRequests.get(0).getChildCount() == 10);
        Assert.assertTrue(dataRequests.get(0).getTotalNotes() == 10);
    }

    /**
     * Case : 如果角色是老师, 但是Mock的数据不属于他的班级
     * 结果 : 统计结果为0
     *
     * @throws Exception
     */
    @Test
    @Ignore
    public void getDashboardDataUserIsTeacher() throws Exception {
        String userId = "U123";
        String agencyId = "A123";
        String frameworkId = "D123";

        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setRole("COLLABORATOR");

        Mockito.when(userProvider.checkUser(userId)).thenReturn(user);

        List<GroupWithCenter> groupWithCenters = new ArrayList<>();
        GroupWithCenter groupWithCenter = new GroupWithCenter();
        groupWithCenter.setCenterId("G123");
        groupWithCenter.setGroupId("C123");
        groupWithCenters.add(groupWithCenter);
        Mockito.when(groupDao.getGroupByTeacherId(user.getId())).thenReturn(groupWithCenters);

//        String domainIds = "D123";
        String domainId = "GD123";
//        String groupIds = "'G123','G1234'";
        String childId = "E1345";
        String childName = "E88877";
        int noteCount = 6;
        int snapshotCount = 6;

        DomainEntity domainEntity = new DomainEntity();
        domainEntity.setId(domainId);
        domainEntity.setName("DName");
        Mockito.when(domainDao.getDomain(Mockito.anyString())).thenReturn(domainEntity);

        List<EnrollmentCenterGroupModel> enrollmentCenterGroupModels = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            EnrollmentCenterGroupModel enrollmentCenterGroupModel = new EnrollmentCenterGroupModel();
            enrollmentCenterGroupModel.setCenterId("C12");
            enrollmentCenterGroupModel.setCenterName("CName");
            enrollmentCenterGroupModel.setGroupId("G12");
            enrollmentCenterGroupModel.setGroupName("GName");
            enrollmentCenterGroupModel.setEnrollmentId("E" + i);
            enrollmentCenterGroupModel.setEnrollmentName("E" + i);
            enrollmentCenterGroupModels.add(enrollmentCenterGroupModel);
        }
        Mockito.when(dashboardDao.getCenterGroupChildWithIdName(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(enrollmentCenterGroupModels);

        List<EnrollmentNotesCountModel> enrollmentNotesCounts = new ArrayList<>();
        EnrollmentNotesCountModel notesCountModel = new EnrollmentNotesCountModel();
        notesCountModel.setChildId(childId);
        notesCountModel.setChildName(childName);
        notesCountModel.setNotesCount(noteCount);
        notesCountModel.setSnapshotCount(snapshotCount);
        enrollmentNotesCounts.add(notesCountModel);
        Mockito.when(dashboardDao.getChildNotesCount(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(enrollmentNotesCounts);

        List<EnrollmentScoreMeasureCountModel> enrollmentScoreMeasureCounts = new ArrayList<>();
        EnrollmentScoreMeasureCountModel scoreMeasureCount = new EnrollmentScoreMeasureCountModel();
        enrollmentScoreMeasureCounts.add(scoreMeasureCount);
        Mockito.when(dashboardDao.getChildScoreMeasureCount(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(enrollmentScoreMeasureCounts);

        List<EnrollmentMeasureCountModel> enrollmentMeasureCounts = new ArrayList<>();
        EnrollmentMeasureCountModel measureCount = new EnrollmentMeasureCountModel();
        enrollmentMeasureCounts.add(measureCount);
        Mockito.when(dashboardDao.getChildMeasureCount(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(enrollmentMeasureCounts);

        PortfolioDashboardRequest paRequest = new PortfolioDashboardRequest();
        paRequest.setAgencyId(agencyId);
        paRequest.setAlias(null);
        paRequest.setFrameworkId(frameworkId);
        paRequest.setCenterId(null);
        paRequest.setGroupId("66");
        paRequest.setAllGroup(false);
        paRequest.setCenterIds("");
        paRequest.setAllCenter(false);
        List<DashboardDataRequest> dataRequests = dashboardService.getDashboardData(userId, paRequest);

        Assert.assertTrue(dataRequests.get(0).getChildCount() == 0);
        Assert.assertTrue(dataRequests.get(0).getNoAliasChildrenCount() == 0);
        Assert.assertTrue(dataRequests.get(0).getTotalNotes() == 0);
    }

    @Test
    @Ignore
    public void getUsageStatistics() throws Exception {
        String userId = "U123";
        String agencyId = "A123";
        String agencyIdentifierId = "ai123";
        List<AgencyIdentifierEntity> agencyIdentifiers = new ArrayList<>();
        AgencyIdentifierEntity agencyIdentifier = new AgencyIdentifierEntity();
        agencyIdentifier.setId(agencyIdentifierId);
        agencyIdentifiers.add(agencyIdentifier);

        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setRole("SPECIAL_EDUCATION");
        List<GroupEntity> groups = new ArrayList<>();
        GroupEntity group = new GroupEntity();
        group.setId("G123");
        groups.add(group);
        Mockito.when(userProvider.checkUser(userId)).thenReturn(user);
        Mockito.when(groupDao.getGroupByChildIds(Mockito.anyString())).thenReturn(groups);

        Mockito.when(agencyDao.getIdentifierByUserId(userId)).thenReturn(agencyIdentifiers);
        PortfolioDashboardRequest portfolioDashboardRequest = new PortfolioDashboardRequest();
        portfolioDashboardRequest.setAgencyId(agencyId);
        UsageStatistics usageStatistics = dashboardService.getUsageStatistics(userId, portfolioDashboardRequest);
        Assert.assertTrue(usageStatistics.getChildCount() == 0);
        Assert.assertTrue(usageStatistics.getCenterCount() == 0);
        Assert.assertTrue(usageStatistics.getParentCount() == 0);
        Assert.assertTrue(usageStatistics.getGroupCount() == 0);

    }

    private static String formatDouble(double d) {
        NumberFormat nf = NumberFormat.getNumberInstance();
        // 保留两位小数
        nf.setMaximumFractionDigits(1);
        // 如果不需要四舍五入，可以使用RoundingMode.DOWN
        nf.setRoundingMode(RoundingMode.DOWN);
        return nf.format(d);
    }

    /**
     * 获取设置过滤视图的学校班级列表，非 PROGRAM 类型
     */
    @Test
    public void testListFilterViewGroups_otherType() {
        String type = "OTHER";
        String viewId = UUID.randomUUID().toString();

        // 用户 ID
        String userId = UUID.randomUUID().toString();
        // 机构 ID
        String agencyId = UUID.randomUUID().toString();
        // 当前用户
        AuthUserDetails user = new AuthUserDetails();
        user.setUsername(userId);
        user.setAgencyId(agencyId);
        when(userProvider.checkCurrentUser()).thenReturn(user);

        // 学校列表
        List<CenterResponse> centers = new ArrayList<>();
        when(groupService.getCenterGroups(userId)).thenReturn(centers);

        // 执行
        ListFilterViewGroupsResponse response = dashboardService.listFilterViewGroups(type, viewId);

        // 学校数为 0
        Assert.assertEquals(0, response.getCenters().size());
        // Program 类型的过滤不会执行
        verify(filterViewGroupDao, times(0)).listFilterViewGroupsByAgencyAndType(type, agencyId);
    }

    /**
     * 获取设置过滤视图的学校班级列表，PROGRAM 类型
     */
    @Test
    public void testListFilterViewGroups_programType() {
        String type = FilterViewType.PROGRAM.toString();
        String viewId = UUID.randomUUID().toString();

        // 用户 ID
        String userId = UUID.randomUUID().toString();
        // 机构 ID
        String agencyId = UUID.randomUUID().toString();
        // 当前用户
        AuthUserDetails user = new AuthUserDetails();
        user.setUsername(userId);
        user.setAgencyId(agencyId);
        when(userProvider.checkCurrentUser()).thenReturn(user);

        // 学校列表
        List<CenterResponse> centers = new ArrayList<>();
        CenterResponse center1 = new CenterResponse();
        centers.add(center1);
        // 班级列表
        List<CenterGroup> groups = new ArrayList<>();
        center1.setGroups(groups);
        // 班级1，未设置过 Program
        CenterGroup group1 = new CenterGroup();
        String group1Id = UUID.randomUUID().toString();
        group1.setId(group1Id);
        groups.add(group1);
        // 班级2，已设置过 Program
        CenterGroup group2 = new CenterGroup();
        String group2Id = UUID.randomUUID().toString();
        group2.setId(group2Id);
        groups.add(group2);
        when(groupService.getCenterGroups(userId)).thenReturn(centers);
        // 已设置 Program 列表
        List<FilterViewGroupEntity> viewGroups = new ArrayList<>();
        FilterViewGroupEntity viewGroup = new FilterViewGroupEntity();
        viewGroup.setViewId(UUID.randomUUID().toString());
        viewGroup.setGroupId(group2Id);
        viewGroups.add(viewGroup);
        when(filterViewGroupDao.listFilterViewGroupsByAgencyAndType(type, agencyId)).thenReturn(viewGroups);

        // 执行
        ListFilterViewGroupsResponse response = dashboardService.listFilterViewGroups(type, viewId);

        // 学校数为 1
        Assert.assertEquals(1, response.getCenters().size());
        // 班级数为 2, 包含了已设置和未设置的班级
        Assert.assertEquals(2, response.getCenters().get(0).getGroups().size());
        // Program 类型的过滤会执行一次
        verify(filterViewGroupDao, times(1)).listFilterViewGroupsByAgencyAndType(type, agencyId);
    }

    /**
     * 测试添加过滤视图
     */
    @Test
    public void testCreateFilterView() {
        String type = FilterViewType.PROGRAM.toString();
        // 请求内容
        CreateFilterViewRequest request = new CreateFilterViewRequest();
        request.setName("FV");
        request.setType(type);
        // 班级 ID
        List<String> groupIds = new ArrayList<>();
        String group1Id = UUID.randomUUID().toString();
        groupIds.add(group1Id);
        request.setGroupIds(groupIds);

        // 用户 ID
        String userId = UUID.randomUUID().toString();
        // 机构 ID
        String agencyId = UUID.randomUUID().toString();
        // 当前用户
        AuthUserDetails user = new AuthUserDetails();
        user.setUsername(userId);
        user.setAgencyId(agencyId);
        user.setRole(UserRole.AGENCY_OWNER.toString());
        when(userProvider.checkCurrentUser()).thenReturn(user);

        // 获取视图列表用户 ID
        String getViewUserId = UUID.randomUUID().toString();
        UserEntity getViewUser = new UserEntity();
        getViewUser.setRole(UserRole.COLLABORATOR.toString());
        when(userProvider.getCurrentUserId()).thenReturn(getViewUserId);
        when(userProvider.checkUser(getViewUserId)).thenReturn(getViewUser);
        when(userProvider.checkCurrentUser()).thenReturn(user);

        // 执行
        dashboardService.createFilterView(request);

        // 保存视图
        verify(filterViewDao, times(1)).save(any());
        // 保持关系
        verify(filterViewGroupDao, times(1)).saveBatch(anyList());
    }

    /**
     * 测试更新过滤视图
     */
    @Test
    public void testUpdateFilterView() {
        String type = FilterViewType.PROGRAM.toString();
        String filterViewId = UUID.randomUUID().toString();
        // 请求内容
        UpdateFilterViewRequest request = new UpdateFilterViewRequest();
        request.setName("FV");
        request.setType(type);
        request.setId(filterViewId);
        // 班级 ID
        List<String> groupIds = new ArrayList<>();
        String group1Id = UUID.randomUUID().toString();
        groupIds.add(group1Id);
        request.setGroupIds(groupIds);

        // 用户 ID
        String userId = UUID.randomUUID().toString();
        // 机构 ID
        String agencyId = UUID.randomUUID().toString();
        // 当前用户
        AuthUserDetails user = new AuthUserDetails();
        user.setUsername(userId);
        user.setAgencyId(agencyId);
        user.setRole(UserRole.AGENCY_OWNER.toString());
        when(userProvider.checkCurrentUser()).thenReturn(user);

        // 要更新的视图信息
        FilterViewEntity filterViewEntity = new FilterViewEntity();
        filterViewEntity.setId(filterViewId);
        filterViewEntity.setName("FV");
        when(filterViewDao.getById(filterViewId)).thenReturn(filterViewEntity);

        // 获取视图列表用户 ID
        String getViewUserId = UUID.randomUUID().toString();
        UserEntity getViewUser = new UserEntity();
        getViewUser.setRole(UserRole.COLLABORATOR.toString());
        when(userProvider.getCurrentUserId()).thenReturn(getViewUserId);
        when(userProvider.checkUser(getViewUserId)).thenReturn(getViewUser);
        when(userProvider.checkCurrentUser()).thenReturn(user);

        // 视图当前班级信息
        List<FilterViewGroupEntity> viewGroups = new ArrayList<>();
        when(filterViewGroupDao.listFilterViewGroupsByViewId(filterViewId)).thenReturn(viewGroups);

        // 执行
        dashboardService.updateFilterView(request);

        // 不更新基本信息
        verify(filterViewDao, times(0)).updateById(any());
        // 保持关系
        verify(filterViewGroupDao, times(1)).saveBatch(anyList());
        // 没有删除的关系
        verify(filterViewGroupDao, times(1)).deleteFilterViewGroupByIds(new ArrayList<>());
    }

    /**
     * 测试删除过滤视图
     */
    @Test
    public void testDeleteFilterView() {
        String filterViewId = UUID.randomUUID().toString();
        // 请求内容
        DeleteFilterViewRequest request = new DeleteFilterViewRequest();
        request.setViewId(filterViewId);

        // 用户 ID
        String userId = UUID.randomUUID().toString();
        // 机构 ID
        String agencyId = UUID.randomUUID().toString();
        // 当前用户
        AuthUserDetails user = new AuthUserDetails();
        user.setUsername(userId);
        user.setAgencyId(agencyId);
        user.setRole(UserRole.AGENCY_OWNER.toString());
        when(userProvider.checkCurrentUser()).thenReturn(user);

        // 执行
        dashboardService.deleteFilterView(request);

        // 不更新基本信息
        verify(filterViewDao, times(1)).delete(filterViewId);
        // 保持关系
        verify(filterViewGroupDao, times(1)).deleteFilterViewGroupByViewId(filterViewId);
    }

    /**
     * 测试获取过滤视图
     */
    @Test
    public void testListFilterView() {
        String type = FilterViewType.PROGRAM.toString();

        // 用户 ID
        String userId = UUID.randomUUID().toString();
        // 机构 ID
        String agencyId = UUID.randomUUID().toString();
        // 当前用户
        UserEntity user = new UserEntity();
        user.setRole(UserRole.AGENCY_OWNER.toString());
        AuthUserDetails userDetails = new AuthUserDetails();
        userDetails.setAgencyId(agencyId);
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.checkUser(userId)).thenReturn(user);
//        when(userProvider.checkCurrentUser()).thenReturn(userDetails);

        // 过滤视图列表
        List<FilterViewGroupEntity> viewGroups = new ArrayList<>();
        FilterViewGroupEntity viewGroup = new FilterViewGroupEntity();
        viewGroup.setGroupId(UUID.randomUUID().toString());
        viewGroup.setViewId(UUID.randomUUID().toString());
        viewGroups.add(viewGroup);
        when(filterViewGroupDao.listFilterViewGroupsByAgencyAndType(type, agencyId)).thenReturn(viewGroups);

        // 执行
        ListFilterViewsResponse response = dashboardService.listFilterViews(type, agencyId);

        // 查询机构下过滤视图
        verify(filterViewGroupDao, times(1)).listFilterViewGroupsByAgencyAndType(type, agencyId);
        // 非 Grantee 不会查询多个机构
        verify(filterViewGroupDao, times(0)).listFilterViewGroupsByAgenciesAndType(anyString(), anyList());
        // 非园长，不会查询用户班级
        verify(groupDao, times(0)).getGroupIdsBySiteAdminId(anyString());
        // 获取结果数量为 1
        Assert.assertEquals(1, response.getViews().size());
    }

    /**
     * 测试按照机构级别统计 Engagement 活动类型
     */
    @Test
    public void testGetEngagementsStats_agencyLevel() {
        // 请求信息
        EngagementDashboardRequest request = new EngagementDashboardRequest();
        // 机构 ID
        String agencyId = UUID.randomUUID().toString();
        request.setAgencyId(agencyId);
        // 日期范围
        String fromDateStr = "2022-01-01";
        String toDateStr = "2022-02-01";
        request.setFromDate(fromDateStr);
        request.setToDate(toDateStr);

        // 用户信息
        String userId = UUID.randomUUID().toString();
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        UserModel user = new UserModel();
        user.setId(userId);
        user.setRole(UserRole.AGENCY_OWNER.toString());
        when(userDao.getUserById(userId)).thenReturn(user);

        UserEntity userEntity = new UserEntity();
        userEntity.setRole(UserRole.AGENCY_OWNER.toString());
        userEntity.setId(userId);
        when(userProvider.checkUser(userId)).thenReturn(userEntity);

        // 统计结果
        List<EngagementTotalModel> results = new ArrayList<>();
        EngagementTotalModel result = new EngagementTotalModel();
        results.add(result);
        result.setVideoBooks(100);
        result.setLearningMedia(100);
        result.setNap(100);
        result.setPotty(100);
        result.setMeal(100);
        result.setReminder(100);
        result.setDiapers(100);
        result.setPhotos(100);
        result.setVideos(100);
        when(dashboardDao.getTotalByAgencyId(agencyId, fromDateStr + " 00:00:00.000", toDateStr + " 23:59:59.000")).thenReturn(results);

        // 调用方法
        EngagementsStatsModel stats = dashboardService.getEngagementsStats(null, request);

        // 验证数据
        assertEquals(100, stats.getPhotos().intValue());
        assertEquals(100, stats.getVideos().intValue());
        assertEquals(100, stats.getVideoBooks().intValue());
        assertEquals(100, stats.getLearningMedia().intValue());
        assertEquals(100, (int) stats.getNap());
        assertEquals(100, (int) stats.getPotty());
        assertEquals(100, (int) stats.getMeals());
        assertEquals(100, stats.getReminder().intValue());
        assertEquals(100, (int) stats.getDiapers());
        assertEquals(20, stats.getNapRate().intValue());
        assertEquals(20, stats.getPottyRate().intValue());
        assertEquals(20, stats.getMealsRate().intValue());
        assertEquals(20, (int) stats.getReminderRate());
        assertEquals(20, stats.getDiapersRate().intValue());
    }

    /**
     * 测试按照学校级别统计 Engagement 活动类型
     */
    @Test
    public void testGetEngagementsStats_centerLevel() {
        // 请求信息
        EngagementDashboardRequest request = new EngagementDashboardRequest();
        // 机构 ID
        String agencyId = UUID.randomUUID().toString();
        request.setAgencyId(agencyId);
        // 学校 ID
        String centerId1 = UUID.randomUUID().toString();
        request.getCenterIds().add(centerId1);
        // 日期范围
        String fromDateStr = "2022-01-01";
        String toDateStr = "2022-02-01";
        request.setFromDate(fromDateStr);
        request.setToDate(toDateStr);

        // 用户信息
        String userId = UUID.randomUUID().toString();
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        UserModel user = new UserModel();
        user.setId(userId);
        user.setRole(UserRole.AGENCY_OWNER.toString());
        when(userDao.getUserById(userId)).thenReturn(user);

        UserEntity userEntity = new UserEntity();
        userEntity.setRole(UserRole.AGENCY_OWNER.toString());
        userEntity.setId(userId);
        when(userProvider.checkUser(userId)).thenReturn(userEntity);

        // 统计结果
        List<EngagementTotalModel> results = new ArrayList<>();
        EngagementTotalModel result = new EngagementTotalModel();
        results.add(result);
        result.setVideoBooks(100);
        result.setLearningMedia(100);
        result.setNap(100);
        result.setPotty(100);
        result.setMeal(100);
        result.setReminder(100);
        result.setDiapers(100);
        result.setPhotos(100);
        result.setVideos(100);
        when(dashboardDao.getTotalByCenterIds(StringUtil.convertIdsToString(Collections.singletonList(centerId1)), fromDateStr + " 00:00:00.000", toDateStr + " 23:59:59.000")).thenReturn(results);

        // 调用方法
        EngagementsStatsModel stats = dashboardService.getEngagementsStats(null, request);

        // 验证数据
        assertEquals(100, stats.getPhotos().intValue());
        assertEquals(100, stats.getVideos().intValue());
        assertEquals(100, stats.getVideoBooks().intValue());
        assertEquals(100, stats.getLearningMedia().intValue());
        assertEquals(100, (int) stats.getNap());
        assertEquals(100, (int) stats.getPotty());
        assertEquals(100, (int) stats.getMeals());
        assertEquals(100, stats.getReminder().intValue());
        assertEquals(100, (int) stats.getDiapers());
        assertEquals(20, stats.getNapRate().intValue());
        assertEquals(20, stats.getPottyRate().intValue());
        assertEquals(20, stats.getMealsRate().intValue());
        assertEquals(20, (int) stats.getReminderRate());
        assertEquals(20, stats.getDiapersRate().intValue());
    }

    /**
     * 测试按照班级级别统计 Engagement 活动类型
     */
    @Test
    public void testGetEngagementsStats_groupLevel() {
        // 请求信息
        EngagementDashboardRequest request = new EngagementDashboardRequest();
        // 机构 ID
        String agencyId = UUID.randomUUID().toString();
        request.setAgencyId(agencyId);
        // 学校 ID
        String centerId1 = UUID.randomUUID().toString();
        request.getCenterIds().add(centerId1);
        // 班级 ID
        String groupId1 = UUID.randomUUID().toString();
        request.getGroupIds().add(groupId1);
        // 日期范围
        String fromDateStr = "2022-01-01";
        String toDateStr = "2022-02-01";
        request.setFromDate(fromDateStr);
        request.setToDate(toDateStr);

        String userId = "U001";

        when(userProvider.getCurrentUserId()).thenReturn(userId);

        UserEntity userEntity = new UserEntity();
        userEntity.setRole(UserRole.AGENCY_OWNER.toString());
        userEntity.setId(userId);
        when(userProvider.checkUser(userId)).thenReturn(userEntity);

        when(userProvider.isGranteeTeacher(userEntity)).thenReturn(false);

        // 统计结果
        List<EngagementTotalModel> results = new ArrayList<>();
        EngagementTotalModel result = new EngagementTotalModel();
        results.add(result);
        result.setVideoBooks(100);
        result.setLearningMedia(100);
        result.setNap(100);
        result.setPotty(100);
        result.setMeal(100);
        result.setReminder(100);
        result.setDiapers(100);
        result.setPhotos(100);
        result.setVideos(100);
        when(dashboardDao.getTotalByGroupIds(StringUtil.convertIdsToString(Collections.singletonList(groupId1)), fromDateStr + " 00:00:00.000", toDateStr + " 23:59:59.000")).thenReturn(results);

//        when(userProvider.isGranteeTeacher(userId)).thenReturn(false);
//
//        UserEntity user = new UserEntity();
//        userEntity.setRole(UserRole.AGENCY_OWNER.toString());
//        userEntity.setId(userId);
//        when(userProvider.checkUser(userId)).thenReturn(user);
        // 调用方法
        EngagementsStatsModel stats = dashboardService.getEngagementsStats(null, request);

        // 验证数据
        assertEquals(100, stats.getPhotos().intValue());
        assertEquals(100, stats.getVideos().intValue());
        assertEquals(100, stats.getVideoBooks().intValue());
        assertEquals(100, stats.getLearningMedia().intValue());
        assertEquals(100, (int) stats.getNap());
        assertEquals(100, (int) stats.getPotty());
        assertEquals(100, (int) stats.getMeals());
        assertEquals(100, stats.getReminder().intValue());
        assertEquals(100, (int) stats.getDiapers());
        assertEquals(20, stats.getNapRate().intValue());
        assertEquals(20, stats.getPottyRate().intValue());
        assertEquals(20, stats.getMealsRate().intValue());
        assertEquals(20, (int) stats.getReminderRate());
        assertEquals(20, stats.getDiapersRate().intValue());
    }

    /**
     * 测试统计家长注册上线周报信息，正常情况(6小孩，2个邀请中，2个已链接）
     */
    @Test
    public void testGetParentWeeklyDataByAgencyId_nomal() {
        // 小孩信息
        List<EnrollmentModel> enrollments = new ArrayList<>();
        for (int i = 0; i < 6; i++) {
            EnrollmentModel enrollment = new EnrollmentModel();
            enrollment.setId(String.valueOf(i));
            enrollment.setCenterId(String.valueOf(1));
            if (i % 2 == 0) {
                enrollment.setGroupId(String.valueOf(1));
            } else {
                enrollment.setGroupId(String.valueOf(2));
            }
            enrollments.add(enrollment);
        }
        when(studentDao.getChildrenByAgencyExcludeTraining(anyString())).thenReturn(enrollments);

        // 邀请信息
        // 1-5 一个状态为空，一个未邀请，两个邀请中，一个已连接
        List<EnrollmentDTO> invitations = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            EnrollmentDTO dto = new EnrollmentDTO();
            dto.setEnrollmentId(String.valueOf(i));
            if (i < 1) {

            } else if (i < 2) {
                dto.setState(InvitationStateConstants.NOINVITATION.toString());
            } else if (i < 4) {
                dto.setState(InvitationStateConstants.NOLINK.toString());
            } else {
                dto.setState(InvitationStateConstants.LINK.toString());
            }
            invitations.add(dto);
        }
        // 6 一个已连接
        List<EnrollmentDTO> invitationCodes = new ArrayList<>();
        EnrollmentDTO dto = new EnrollmentDTO();
        dto.setEnrollmentId(String.valueOf(5));
        invitationCodes.add(dto);
        when(enrollmentDao.getInvitationsByAgencyId(anyString())).thenReturn(invitations);
        when(enrollmentDao.getInvitationsCodeByAgencyId(anyString())).thenReturn(invitationCodes);

        Map<String, Map<String, StatisticsParentOnboardWeeklyEntity>> result = dashboardService.getParentWeeklyDataByAgencyId(anyString(), new Date());
        // 一个center，两个group
        // group 1 ： 0 2 4 group 1 3 5
        assertEquals(result.size(), 1);
        assertEquals(result.get(String.valueOf(1)).size(), 2);
        StatisticsParentOnboardWeeklyEntity entityGroup1 = result.get(String.valueOf(1)).get(String.valueOf(1));
        StatisticsParentOnboardWeeklyEntity entityGroup2 = result.get(String.valueOf(1)).get(String.valueOf(2));
        assertEquals(entityGroup1.getChildrenNum(), Integer.valueOf(3));
        assertEquals(entityGroup2.getChildrenNum(), Integer.valueOf(3));
        assertEquals(entityGroup1.getInvitationNeedParentNum(), Integer.valueOf(1));
        assertEquals(entityGroup2.getInvitationNeedParentNum(),Integer.valueOf(1));
        assertEquals(entityGroup1.getPendingRegistrationParentNum(), Integer.valueOf(1));
        assertEquals(entityGroup2.getPendingRegistrationParentNum(), Integer.valueOf(1));
        assertEquals(entityGroup1.getOnboardParentNum(), Integer.valueOf(1));
        assertEquals(entityGroup2.getOnboardParentNum(), Integer.valueOf(1));
    }

    /**
     * 测试统计家长注册上线周报信息，无小孩
     */
    @Test
    public void testGetParentWeeklyDataByAgencyId_noneChild() {
        // 小孩信息
        when(studentDao.getChildrenByAgencyExcludeTraining(anyString())).thenReturn(new ArrayList<>());

        Map<String, Map<String, StatisticsParentOnboardWeeklyEntity>> result = dashboardService.getParentWeeklyDataByAgencyId(anyString(), new Date());

        assertNull(result);
    }

    /**
     * 测试统计家长注册上线周报信息，6个小孩，没有邀请信息
     */
    @Test
    public void testGetParentWeeklyDataByAgencyId_noneInvitations() {
        // 小孩信息
        List<EnrollmentModel> enrollments = new ArrayList<>();
        for (int i = 0; i < 6; i++) {
            EnrollmentModel enrollment = new EnrollmentModel();
            enrollment.setId(String.valueOf(i));
            enrollment.setCenterId(String.valueOf(1));
            enrollment.setGroupId(String.valueOf(1));
            enrollments.add(enrollment);
        }
        when(studentDao.getChildrenByAgencyExcludeTraining(anyString())).thenReturn(enrollments);

        when(enrollmentDao.getInvitationsByAgencyId(anyString())).thenReturn(new ArrayList<>());
        when(enrollmentDao.getInvitationsCodeByAgencyId(anyString())).thenReturn(new ArrayList<>());

        Map<String, Map<String, StatisticsParentOnboardWeeklyEntity>> result = dashboardService.getParentWeeklyDataByAgencyId(anyString(), new Date());

        assertEquals(result.size(), 1);
        assertEquals(result.get(String.valueOf(1)).size(), 1);
        StatisticsParentOnboardWeeklyEntity entity = result.get(String.valueOf(1)).get(String.valueOf(1));
        assertEquals(entity.getInvitationNeedParentNum(), Integer.valueOf(6));
        assertEquals(entity.getPendingRegistrationParentNum(), Integer.valueOf(0));
        assertEquals(entity.getOnboardParentNum(), Integer.valueOf(0));
    }

    /**
     * 测试统计家长注册上线周报信息，6个小孩，所有都是邀请中
     */
    @Test
    public void testGetParentWeeklyDataByAgencyId_allPending() {
        // 小孩信息
        List<EnrollmentModel> enrollments = new ArrayList<>();
        for (int i = 0; i < 6; i++) {
            EnrollmentModel enrollment = new EnrollmentModel();
            enrollment.setId(String.valueOf(i));
            enrollment.setCenterId(String.valueOf(1));
            enrollment.setGroupId(String.valueOf(1));
            enrollments.add(enrollment);
        }
        when(studentDao.getChildrenByAgencyExcludeTraining(anyString())).thenReturn(enrollments);

        List<EnrollmentDTO> invitations = new ArrayList<>();
        for (int i = 0; i < 6; i++) {
            EnrollmentDTO dto = new EnrollmentDTO();
            dto.setEnrollmentId(String.valueOf(i));
            dto.setState(InvitationStateConstants.NOLINK.toString());
            invitations.add(dto);
        }
        when(enrollmentDao.getInvitationsByAgencyId(anyString())).thenReturn(invitations);
        when(enrollmentDao.getInvitationsCodeByAgencyId(anyString())).thenReturn(new ArrayList<>());

        Map<String, Map<String, StatisticsParentOnboardWeeklyEntity>> result = dashboardService.getParentWeeklyDataByAgencyId(anyString(), new Date());

        assertEquals(result.size(), 1);
        assertEquals(result.get(String.valueOf(1)).size(), 1);
        StatisticsParentOnboardWeeklyEntity entity = result.get(String.valueOf(1)).get(String.valueOf(1));
        assertEquals(entity.getInvitationNeedParentNum(), Integer.valueOf(0));
        assertEquals(entity.getPendingRegistrationParentNum(), Integer.valueOf(6));
        assertEquals(entity.getOnboardParentNum(), Integer.valueOf(0));
    }

    /**
     * 测试统计家长注册上线周报信息，6个小孩，全部都是已连接
     */
    @Test
    public void testGetParentWeeklyDataByAgencyId_allOnboard() {
        // 小孩信息
        List<EnrollmentModel> enrollments = new ArrayList<>();
        for (int i = 0; i < 6; i++) {
            EnrollmentModel enrollment = new EnrollmentModel();
            enrollment.setId(String.valueOf(i));
            enrollment.setCenterId(String.valueOf(1));
            enrollment.setGroupId(String.valueOf(1));
            enrollments.add(enrollment);
        }
        when(studentDao.getChildrenByAgencyExcludeTraining(anyString())).thenReturn(enrollments);

        List<EnrollmentDTO> invitations = new ArrayList<>();
        for (int i = 0; i < 4; i++) {
            EnrollmentDTO dto = new EnrollmentDTO();
            dto.setEnrollmentId(String.valueOf(i));
            dto.setState(InvitationStateConstants.LINK.toString());
            invitations.add(dto);
        }
        List<EnrollmentDTO> invitationCodes = new ArrayList<>();
        for (int i = 4; i < 6; i++) {
            EnrollmentDTO dto = new EnrollmentDTO();
            dto.setEnrollmentId(String.valueOf(i));
            invitationCodes.add(dto);
        }
        when(enrollmentDao.getInvitationsByAgencyId(anyString())).thenReturn(invitations);
        when(enrollmentDao.getInvitationsCodeByAgencyId(anyString())).thenReturn(invitationCodes);

        Map<String, Map<String, StatisticsParentOnboardWeeklyEntity>> result = dashboardService.getParentWeeklyDataByAgencyId(anyString(), new Date());

        assertEquals(result.size(), 1);
        assertEquals(result.get(String.valueOf(1)).size(), 1);
        StatisticsParentOnboardWeeklyEntity entity = result.get(String.valueOf(1)).get(String.valueOf(1));
        assertEquals(entity.getInvitationNeedParentNum(), Integer.valueOf(0));
        assertEquals(entity.getPendingRegistrationParentNum(), Integer.valueOf(0));
        assertEquals(entity.getOnboardParentNum(), Integer.valueOf(6));
    }

    /**
     * 测试统计家长注册上线周报信息，6个小孩，一个小孩多个邀请信息
     */
    @Test
    public void testGetParentWeeklyDataByAgencyId_multipleInvations() {
        int id = 0;
        // 小孩信息
        List<EnrollmentModel> enrollments = new ArrayList<>();
        for (int i = 0; i < 6; i++) {
            EnrollmentModel enrollment = new EnrollmentModel();
            enrollment.setId(String.valueOf(i));
            enrollment.setCenterId(String.valueOf(1));
            enrollment.setGroupId(String.valueOf(1));
            enrollments.add(enrollment);
        }
        when(studentDao.getChildrenByAgencyExcludeTraining(anyString())).thenReturn(enrollments);

        // 0、1、2小孩，3个邀请信息，两个待邀请，一个邀请中   ——> 0/2 邀请中
        List<EnrollmentDTO> invitations = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            for (int j = 0; j < 3; j++) {
                EnrollmentDTO dto = new EnrollmentDTO();
                dto.setEnrollmentId(String.valueOf(i));
                dto.setInvitationId(String.valueOf(id++));
                if (j % 2 == 0) {
                    dto.setState(InvitationStateConstants.NOINVITATION.toString());
                } else {
                    dto.setState(InvitationStateConstants.NOLINK.toString());
                }
                invitations.add(dto);
            }
        }
        // 3、4 一个邀请信息 待邀请 -> 3 邀请中
        for (int i = 3; i < 5; i++) {
            EnrollmentDTO dto = new EnrollmentDTO();
            dto.setEnrollmentId(String.valueOf(i));
            dto.setState(InvitationStateConstants.NOLINK.toString());
            dto.setInvitationId(String.valueOf(id++));
            invitations.add(dto);
        }
        // 小孩 4 加一个已连接 -> 4 已连接
        List<EnrollmentDTO> invitationCodes = new ArrayList<>();
        EnrollmentDTO dto4 = new EnrollmentDTO();
        dto4.setEnrollmentId(String.valueOf(4));
        dto4.setInvitationId(String.valueOf(id++));
        invitationCodes.add(dto4);

        // 小孩 1 加一个已连接 1 -> 已连接
        EnrollmentDTO dto1 = new EnrollmentDTO();
        dto1.setEnrollmentId(String.valueOf(1));
        dto1.setInvitationId(String.valueOf(id++));
        invitationCodes.add(dto1);

        when(enrollmentDao.getInvitationsByAgencyId(anyString())).thenReturn(invitations);
        when(enrollmentDao.getInvitationsCodeByAgencyId(anyString())).thenReturn(invitationCodes);
        // 结果 3 待邀请 0、2、5 邀请中  1、4 已连接
        Map<String, Map<String, StatisticsParentOnboardWeeklyEntity>> result = dashboardService.getParentWeeklyDataByAgencyId(anyString(), new Date());

        assertEquals(result.size(), 1);
        assertEquals(result.get(String.valueOf(1)).size(), 1);
        StatisticsParentOnboardWeeklyEntity entity = result.get(String.valueOf(1)).get(String.valueOf(1));
        assertEquals(entity.getInvitationNeedParentNum(), Integer.valueOf(1));
        assertEquals(entity.getPendingRegistrationParentNum(), Integer.valueOf(3));
        assertEquals(entity.getOnboardParentNum(), Integer.valueOf(2));
    }

    /**
     * 测试获取仪表板数据 V2 方法
     */
    @Test
    public void testGetDashboardDataByFrameworkV2() {
        // 数据准备
        String domainId = "D00001";
        String AgencyId = "A00001";
        String enrollmentId = "E00001";
        UserEntity user = new UserEntity();
        String userId = "U00001";
        List<AgencyModel> agencies = new ArrayList<>();
        AgencyModel agency = new AgencyModel();
        agency.setId(AgencyId);
        agencies.add(agency);
        List<String> domainIds = new ArrayList<>();
        domainIds.add(domainId);
        String centerId = "C00001";
        String groupId = "G00001";
        boolean isAllGroup = true;
        String alias = "2023-3024";
        String centerIds = "[C00001]";
        Map<String, Boolean> lockFrameworkMap = new HashMap<>();
        Boolean isAllCenter = true;
        boolean existSearch = true;

        List<DomainEntity> domains = new ArrayList<>();
        AgencyModel userAgency = new AgencyModel();
        userAgency.setId(AgencyId);
        List<EnrollmentCenterGroupModel> enrollmentCenterGroupModels = new ArrayList<>();
        EnrollmentCenterGroupModel enrollmentCenterGroupModel = new EnrollmentCenterGroupModel();
        enrollmentCenterGroupModel.setEnrollmentId(enrollmentId);
        enrollmentCenterGroupModel.setCenterId(centerId);
        enrollmentCenterGroupModel.setGroupId(groupId);
        enrollmentCenterGroupModel.setDomainId(domainId);
        enrollmentCenterGroupModel.setAgencyId(AgencyId);
        enrollmentCenterGroupModels.add(enrollmentCenterGroupModel);
        List<AgencyIdentifierEntity> agencyIdentifierEntities = new ArrayList<>();
        Map<String, Boolean> agenciesOpenDefaultClose = new HashMap<>();
        List<KeyMeasureEntity> keyMeasureEntities = new ArrayList<>();
        Map<String, Map<String, Boolean>> childId2IepAndEldMap = new HashMap<>();
        Map<String,Boolean> map = new HashMap<>();
        map.put("IEP", true);
        map.put("ELD", true);
        childId2IepAndEldMap.put(enrollmentId, map);
        DomainEntity domain = new DomainEntity();
        // 接口模拟
        when(domainDao.getDomains(any())).thenReturn(domains);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(userAgency);
        when(dashboardDao.batchGetCenterGroupChildWithIdName(anyList(), anyList(), anyString(), anyString())).thenReturn(enrollmentCenterGroupModels);
//        when(agencyDao.getIdentifierByUserIdAndAgencyIds(any(), anyList())).thenReturn(agencyIdentifierEntities);
//        when(dashboardDao.batchGetNoAliasByIdentifier(anyList(), anyList(), anyString(), anyString(), anyMap())).thenReturn(enrollmentCenterGroupModels);
        when(domainDao.getKeyMeasuresSetting(anyList(), anyList())).thenReturn(keyMeasureEntities);
        when(domainDao.getDomain(anyString())).thenReturn(domain);
        // 获取小孩 Id 到 IEP 和 ELD 属性的映射
        when(enrollmentProvider.getIEPAndELDAttrsByEnrollmentIds(anyList())).thenReturn(childId2IepAndEldMap);

        // 接口调用
        dashboardService.getDashboardDataByFrameworkV2(user, userId, agencies, domainIds, centerId, groupId, isAllGroup, alias, centerIds, lockFrameworkMap, isAllCenter, existSearch, null);

        // 验证
        verify(enrollmentProvider, times(1)).getIEPAndELDAttrsByEnrollmentIds(anyList());
    }

    /**
     * Case: 机构开启了完成所有测评点才能锁定开关
     * 结果: 返回 true
     */
    @Test
    public void testGetDashboardFrameworkList() {
        // 数据准备
        String frameworkId = UUID.randomUUID().toString(); // 框架 Id
        String agencyId = UUID.randomUUID().toString(); // 机构 Id
        // mock 是否开启了核心测评点
        when(userProvider.getAgencyOpenDefaultClose(agencyId, AgencyMetaKey.DRDP_KEY_MEASURE_SETTING.toString())).thenReturn(true);
        // 核心测评点设置信息列表
        List<KeyMeasureSettingEntity> settingFramework = new ArrayList<>();
        // 核心测评点设置信息
        KeyMeasureSettingEntity keyMeasureSettingEntity = new KeyMeasureSettingEntity();
        keyMeasureSettingEntity.setId(frameworkId); // 设置 Id
        keyMeasureSettingEntity.setFrameworkId(frameworkId); // 设置框架 Id
        settingFramework.add(keyMeasureSettingEntity);
        // mock 核心测评点设置信息列表
        when(domainDao.getSettingFramework(agencyId)).thenReturn(settingFramework);
        // 核心测评点信息列表
        List<KeyMeasureEntity> keyMeasuresSetting = new ArrayList<>();
        // 核心测评点信息
        KeyMeasureEntity keyMeasureEntity = new KeyMeasureEntity();
        keyMeasureEntity.setId(frameworkId); // 设置框架 Id
        keyMeasureEntity.setKeyMeasureSettingId(frameworkId); // 测评点 Id
        keyMeasuresSetting.add(keyMeasureEntity);
        // mock 核心测评点信息列表
        when(domainDao.getKeyMeasuresSetting(anyList())).thenReturn(keyMeasuresSetting);
        // Dashboard 列表
        List<DashboardFrameworkListModel> dashboardFrameworkListModels = new ArrayList<>();
        // Dashboard 信息
        DashboardFrameworkListModel dashboardFrameworkListModel = new DashboardFrameworkListModel();
        dashboardFrameworkListModel.setFrameworkId(frameworkId); // 设置框架 Id
        dashboardFrameworkListModels.add(dashboardFrameworkListModel);
        // mock Dashboard 列表
        when(dashboardDao.getFrameworkListByFrameworkIdsAndAgencyId(anyString(), anyString())).thenReturn(dashboardFrameworkListModels);
        // 机构的 Meta 信息
        AgencyMetaDataEntity meta = new AgencyMetaDataEntity();
        meta.setMetaValue("true");
        when(agencyDao.getMeta(agencyId, AgencyMetaKey.COMPLETE_RATING_REQUIRED_FOR_CHILD_LOCK.toString())).thenReturn(meta);

        // 调用方法
        List<DashboardFrameworkListModel> result = dashboardService.getDashboardFrameworkList(frameworkId, agencyId);

        // 验证返回了该机构下的该框架是否必须完成所有测评点才能锁定对象，且为 true
        Assert.assertTrue(result.get(0).isRequireCompleteRating());
    }

    /**
     * Case: 机构未开启了完成所有测评点才能锁定开关
     * 结果: 返回 false
     */
    @Test
    public void testGetDashboardFrameworkList1() {
        // 数据准备
        String frameworkId = UUID.randomUUID().toString(); // 框架 Id
        String agencyId = UUID.randomUUID().toString(); // 机构 Id
        // mock 是否开启了核心测评点
        when(userProvider.getAgencyOpenDefaultClose(agencyId, AgencyMetaKey.DRDP_KEY_MEASURE_SETTING.toString())).thenReturn(true);
        // 核心测评点设置信息列表
        List<KeyMeasureSettingEntity> settingFramework = new ArrayList<>();
        // 核心测评点设置信息
        KeyMeasureSettingEntity keyMeasureSettingEntity = new KeyMeasureSettingEntity();
        keyMeasureSettingEntity.setId(frameworkId); // 设置 Id
        keyMeasureSettingEntity.setFrameworkId(frameworkId); // 设置框架 Id
        settingFramework.add(keyMeasureSettingEntity);
        // mock 核心测评点设置信息列表
        when(domainDao.getSettingFramework(agencyId)).thenReturn(settingFramework);
        // Dashboard 列表
        List<DashboardFrameworkListModel> dashboardFrameworkListModels = new ArrayList<>();
        // Dashboard 信息
        DashboardFrameworkListModel dashboardFrameworkListModel = new DashboardFrameworkListModel();
        dashboardFrameworkListModel.setFrameworkId(frameworkId); // 设置框架 Id
        dashboardFrameworkListModels.add(dashboardFrameworkListModel);
        // mock Dashboard 列表
        when(dashboardDao.getFrameworkListByFrameworkIdsAndAgencyId(anyString(), anyString())).thenReturn(dashboardFrameworkListModels);
        // 机构的 Meta 信息
        AgencyMetaDataEntity meta = new AgencyMetaDataEntity();
        meta.setMetaValue("false");
        when(agencyDao.getMeta(agencyId, AgencyMetaKey.COMPLETE_RATING_REQUIRED_FOR_CHILD_LOCK.toString())).thenReturn(meta);

        // 调用方法
        List<DashboardFrameworkListModel> result = dashboardService.getDashboardFrameworkList(frameworkId, agencyId);

        // 验证返回了该机构未开启完成所有测评点评分才能锁定开关
        Assert.assertFalse(result.get(0).isRequireCompleteRating());
    }

    /**
     * Case: 机构未开启了完成所有测评点才能锁定开关
     * 结果: 返回 true
     */
    @Test
    public void testGetDashboardFrameworkList2() {
        // 数据准备
        String frameworkId = UUID.randomUUID().toString(); // 框架 Id
        String agencyId = UUID.randomUUID().toString(); // 机构 Id
        // mock 是否开启了核心测评点
        when(userProvider.getAgencyOpenDefaultClose(agencyId, AgencyMetaKey.DRDP_KEY_MEASURE_SETTING.toString())).thenReturn(true);
        // 核心测评点设置信息列表
        List<KeyMeasureSettingEntity> settingFramework = new ArrayList<>();
        // 核心测评点设置信息
        KeyMeasureSettingEntity keyMeasureSettingEntity = new KeyMeasureSettingEntity();
        keyMeasureSettingEntity.setId(frameworkId); // 设置 Id
        keyMeasureSettingEntity.setFrameworkId(frameworkId); // 设置框架 Id
        settingFramework.add(keyMeasureSettingEntity);
        // mock 核心测评点设置信息列表
        when(domainDao.getSettingFramework(agencyId)).thenReturn(settingFramework);
        // 核心测评点信息列表
        List<KeyMeasureEntity> keyMeasuresSetting = new ArrayList<>();
        // 核心测评点信息
        KeyMeasureEntity keyMeasureEntity = new KeyMeasureEntity();
        keyMeasureEntity.setId(frameworkId); // 设置框架 Id
        keyMeasureEntity.setKeyMeasureSettingId(frameworkId); // 测评点 Id
        keyMeasuresSetting.add(keyMeasureEntity);
        // mock 核心测评点信息列表
        when(domainDao.getKeyMeasuresSetting(anyList())).thenReturn(keyMeasuresSetting);
        // Dashboard 列表
        List<DashboardFrameworkListModel> dashboardFrameworkListModels = new ArrayList<>();
        // Dashboard 信息
        DashboardFrameworkListModel dashboardFrameworkListModel = new DashboardFrameworkListModel();
        dashboardFrameworkListModel.setFrameworkId(frameworkId); // 设置框架 Id
        dashboardFrameworkListModels.add(dashboardFrameworkListModel);
        // mock Dashboard 列表
        when(dashboardDao.getFrameworkListByFrameworkIdsAndAgencyId(anyString(), anyString())).thenReturn(dashboardFrameworkListModels);
        // 机构的 Meta 信息
        AgencyMetaDataEntity meta = new AgencyMetaDataEntity();
        meta.setMetaValue("true");
        when(agencyDao.getMeta(agencyId, AgencyMetaKey.COMPLETE_RATING_REQUIRED_FOR_CHILD_LOCK.toString())).thenReturn(meta);

        // 调用方法
        List<DashboardFrameworkListModel> result = dashboardService.getDashboardFrameworkList(frameworkId, agencyId, null, null, null);

        // 验证返回了该机构下开启必须完成所有测评点评分才能锁定开关
        Assert.assertTrue(result.get(0).isRequireCompleteRating());
    }

    /**
     * Case: 机构未开启了完成所有测评点才能锁定开关
     * 结果: 返回 false
     */
    @Test
    public void testGetDashboardFrameworkList3() {
        // 数据准备
        String frameworkId = UUID.randomUUID().toString(); // 框架 Id
        String agencyId = UUID.randomUUID().toString(); // 机构 Id
        // mock 是否开启了核心测评点
        when(userProvider.getAgencyOpenDefaultClose(agencyId, AgencyMetaKey.DRDP_KEY_MEASURE_SETTING.toString())).thenReturn(true);
        // 核心测评点设置信息列表
        List<KeyMeasureSettingEntity> settingFramework = new ArrayList<>();
        // 核心测评点设置信息
        KeyMeasureSettingEntity keyMeasureSettingEntity = new KeyMeasureSettingEntity();
        keyMeasureSettingEntity.setId(frameworkId); // 设置 Id
        keyMeasureSettingEntity.setFrameworkId(frameworkId); // 设置框架 Id
        settingFramework.add(keyMeasureSettingEntity);
        // mock 核心测评点设置信息列表
        when(domainDao.getSettingFramework(agencyId)).thenReturn(settingFramework);
        // 核心测评点信息列表
        List<KeyMeasureEntity> keyMeasuresSetting = new ArrayList<>();
        // 核心测评点信息
        KeyMeasureEntity keyMeasureEntity = new KeyMeasureEntity();
        keyMeasureEntity.setId(frameworkId); // 设置框架 Id
        keyMeasureEntity.setKeyMeasureSettingId(frameworkId); // 测评点 Id
        keyMeasuresSetting.add(keyMeasureEntity);
        // mock 核心测评点信息列表
        when(domainDao.getKeyMeasuresSetting(anyList())).thenReturn(keyMeasuresSetting);
        // Dashboard 列表
        List<DashboardFrameworkListModel> dashboardFrameworkListModels = new ArrayList<>();
        // Dashboard 信息
        DashboardFrameworkListModel dashboardFrameworkListModel = new DashboardFrameworkListModel();
        dashboardFrameworkListModel.setFrameworkId(frameworkId); // 设置框架 Id
        dashboardFrameworkListModels.add(dashboardFrameworkListModel);
        // mock Dashboard 列表
        when(dashboardDao.getFrameworkListByFrameworkIdsAndAgencyId(anyString(), anyString())).thenReturn(dashboardFrameworkListModels);
        // 机构的 Meta 信息
        AgencyMetaDataEntity meta = new AgencyMetaDataEntity();
        meta.setMetaValue("false");
        when(agencyDao.getMeta(agencyId, AgencyMetaKey.COMPLETE_RATING_REQUIRED_FOR_CHILD_LOCK.toString())).thenReturn(meta);

        // 调用方法
        List<DashboardFrameworkListModel> result = dashboardService.getDashboardFrameworkList(frameworkId, agencyId, null, null, null);

        // 验证返回了该机构下未开启必须完成所有测评点评分才能锁定开关
        Assert.assertFalse(result.get(0).isRequireCompleteRating());
    }

    @Test
    public void getDashboardDataSlidesImageTest() {
        String userId = UUID.randomUUID().toString();
        String agencyId = UUID.randomUUID().toString();
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.getAgencyOpenDefaultClose(anyString(), anyString())).thenReturn(true);

        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId(UUID.randomUUID().toString());
        List<com.learninggenie.common.data.entity.GroupEntity> allGroups = new ArrayList<>();
        allGroups.add(groupEntity);
        // 通过机构 Id 获取所有的学校和班级
        when(groupDao.getGroupWithCenterByAgency(anyString())).thenReturn(allGroups);

        EnrollmentEntity enrollmentEntity = new EnrollmentEntity();
        enrollmentEntity.setId(UUID.randomUUID().toString());
        List<EnrollmentEntity> enrollmentEntityList = new ArrayList<>();
        enrollmentEntityList.add(enrollmentEntity);
        when(studentDao.getChildrenByGroupIds(anyList())).thenReturn(enrollmentEntityList);

        List<com.learninggenie.common.data.entity.enrollment.EnrollmentPeriodEntity> enrollmentPeriodList = new ArrayList<>();
        com.learninggenie.common.data.entity.enrollment.EnrollmentPeriodEntity entity = new com.learninggenie.common.data.entity.enrollment.EnrollmentPeriodEntity();
        entity.setAlias("2024-2025 Time2");
        entity.setDomainId("DomainId");
        enrollmentPeriodList.add(entity);
        // 查找班级中的小孩的周期 enrollmentPeriodDao.selectByEnrollmentIdsAndAliasLike(allEnrollmentIds, schoolYear)
        when(enrollmentPeriodDao.selectByEnrollmentIdsAndAliasLike(anyList(), anyString())).thenReturn(enrollmentPeriodList);

        List<FrameworkEntity> list = new ArrayList<>();
        FrameworkEntity framework = new FrameworkEntity();
        framework.setId("frameworkId");

        List<ReplaceRelation> dashboardDataSlidesImage = dashboardService.getDashboardDataSlidesImage(agencyId);
        assertEquals(0, dashboardDataSlidesImage.size());
    }

    @Test
    public void generateEngagementSlideDataTest() {
        when(userProvider.getAgencyOpenDefaultClose(anyString(), anyString())).thenReturn(true);

        List<CenterModel> centers = new ArrayList<>();
        CenterModel centerModel = new CenterModel();
        centerModel.setTraining(false);
        centerModel.setId("centerModelId");
        GroupModel groupModel = new GroupModel();
        centerModel.setGroups(new ArrayList<>(Arrays.asList(groupModel)));
        centers.add(centerModel);

        String agencyId = UUID.randomUUID().toString();
        when(userProvider.getCurrentUserId()).thenReturn("userId");
        when(userService.getCenterGroupByUserId("userId", false, agencyId)).thenReturn(centers);

        // 时区
        when(centerDao.getFirstCenterTimeZoneByAgencyId(anyString())).thenReturn("360000000");
        UserEntity userEntity = new UserEntity();
        when(userProvider.checkUser(anyString())).thenReturn(userEntity);
        AgencyModel agency = new AgencyModel();
        agency.setId("agencyId");
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agency);

        List<EnrollmentModel> children = new ArrayList<>();
        EnrollmentModel enrollmentModel = new EnrollmentModel();
        children.add(enrollmentModel);
        when(studentDao.getChildrenByCenterIds(new ArrayList<>(Arrays.asList("centerModelId")), null)).thenReturn(children);
        // 生成家长活跃度统计图片
        when(mediaService.convertHtmlToImage(anyString(), anyString())).thenReturn("parentOnboardingChartImageUrl");

        when(userProvider.getCurrentLang()).thenReturn("100");
        UserModel user = new UserModel();
        when(userDao.getUserById(anyString())).thenReturn(user);
        when(messageDao.getUserMessageCountByCenterIds(anyString(), anyString(), anyList())).thenReturn(2);
        List<MessageCountModel> repeatMessageCount = new ArrayList<>();
        MessageCountModel messageCountModel = new MessageCountModel();
        messageCountModel.setMessageId("MessageId");
        messageCountModel.setCount(2);
        repeatMessageCount.add(messageCountModel);
        when(messageDao.getRepeatMessageCountByCenterIds(anyString(), anyString(), anyList())).thenReturn(repeatMessageCount);
        when(messageDao.getHaveReadMessageCountByCenterIds(anyString(), anyString(), anyList())).thenReturn(repeatMessageCount);
        when(eventDao.getEventCountByCenterIds(anyString(), anyString(), anyString())).thenReturn(2);
        when(filterService.getFilterAttrStr(any())).thenReturn("All");

        List<EventCountModel> eventCountWithRoleAndType = new ArrayList<>();
        when(eventDao.getEventRepeatCountByCenterIds(anyString(), anyString(), anyString())).thenReturn(eventCountWithRoleAndType);
        List<InkindSchoolYearEntity> schoolYears = new ArrayList<>();
        when(inkindDao.getSchoolYearByAgencyId(anyString())).thenReturn(schoolYears);

        List<ReplaceRelation> replaceRelations = dashboardService.generateEngagementSlideData(agencyId, TimeUtil.getUtcNow(), TimeUtil.getUtcNow());
        assertEquals(28, replaceRelations.size());
        verify(eventDao, times(3)).getEventCountByCenterIds(anyString(), anyString(), anyString());

    }

}

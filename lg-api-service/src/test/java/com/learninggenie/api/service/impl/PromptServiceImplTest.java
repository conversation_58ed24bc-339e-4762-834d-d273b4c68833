package com.learninggenie.api.service.impl;

import com.learninggenie.api.model.IdResponse;
import com.learninggenie.api.model.PageResponse;
import com.learninggenie.api.model.prompt.*;
import com.learninggenie.api.provider.PromptProvider;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.security.AuthUserDetails;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.dao.prompts.*;
import com.learninggenie.common.data.dao.users.MetaDataDao;
import com.learninggenie.common.data.entity.PageList;
import com.learninggenie.common.data.entity.UserProfileEntity;
import com.learninggenie.common.data.entity.prompt.*;
import com.learninggenie.common.data.entity.users.MetaDataEntity;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.Silent.class)
public class PromptServiceImplTest {

    @InjectMocks
    private PromptServiceImpl promptServiceImpl;

    @Mock
    private PromptDao promptDao;

    @Mock
    private PromptUsageRecordDao promptUsageRecordDao;

    @Mock
    private UserProvider userProvider;

    @Mock
    private UserDaoImpl userDao;

    @Mock
    private MetaDataDao userMetaDao;

    @Mock
    private PromptFeedBackDao promptFeedBackDao;

    @Mock
    private PromptTestRecordDao promptTestRecordDao;

    @Mock
    private PromptUsageObjectDao promptUsageObjectDao;

    @Mock
    private PromptProvider promptProvider;


    /**
     * 测试获取模块列表
     */
    @Test
    public void listPromptsMock() {
        // 组装数据
        List<PromptEntity> promptEntityList = new ArrayList<>();
        PromptEntity promptEntity = new PromptEntity();
        promptEntity.setModule("UNIT");
        promptEntity.setScene("UNIT_OVERVIEW");
        promptEntityList.add(promptEntity);
        when(promptDao.getModulePrompt()).thenReturn(promptEntityList);
        // 调用方法
        CreateSystemPromptResponse response = promptServiceImpl.listPrompts();
        // 断言
        Assertions.assertNotNull(response.getListModules());
        // 验证数据
        Assertions.assertEquals("UNIT", response.getListModules().get(0).getModule());
    }

    /**
     * 测试根据场景获取当前使用版本的 Prompt
     */
    @Test
    public void listPromptsBySceneMock() {
        // 组装数据
        // 用户信息
        AuthUserDetails user = new AuthUserDetails();
        user.setUsername("875dd41c-e0c5-4745-bed6-ff1e9a5955b1");
        when(userProvider.getCurrentUser()).thenReturn(user);
        // 用户当前使用的 Metadata 信息
        MetaDataEntity metaData = new MetaDataEntity();
        metaData.setUserId("875dd41c-e0c5-4745-bed6-ff1e9a5955b1");
        metaData.setMetaKey("UNIT_OVERVIEW");
        metaData.setMetaValue("733f4ce0-9bf9-4ba4-b350-e58d11891ec1");
        when(userMetaDao.getByUserIdAndMetaKey(anyString(), anyString())).thenReturn(metaData);
        // Prompt 列表
        PageList<PromptEntity> activePromptListScene = new PageList<>();
        List<PromptEntity> promptEntityList = new ArrayList<>();
        // 系统 Prompt
        PromptEntity promptEntity = new PromptEntity();
        promptEntity.setModule("UNIT");
        promptEntity.setScene("UNIT_OVERVIEW");
        promptEntity.setId("733f4ce0-9bf9-4ba4-b350-e58d11891ec1");
        promptEntity.setCreateUserId("875dd41c-e0c5-4745-bed6-ff1e9a5955b1");
        promptEntity.setActive(true);
        promptEntity.setPromptSource("SYSTEM");
        promptEntity.setTemperature(10.00);
        promptEntityList.add(promptEntity);
        // 用户 Prompt
        PromptEntity userPromptEntity = new PromptEntity();
        userPromptEntity.setModule("UNIT");
        userPromptEntity.setScene("UNIT_OVERVIEW");
        userPromptEntity.setId("1780bc7a-9068-49bf-9368-b938d6b0b7ff");
        userPromptEntity.setCreateUserId("875dd41c-e0c5-4745-bed6-ff1e9a5955b1");
        userPromptEntity.setActive(true);
        userPromptEntity.setPromptSource("USER");
        userPromptEntity.setTemperature(10.00);
        promptEntityList.add(userPromptEntity);
        // 分数为0 的 Prompt
        PromptEntity zeroPromptEntity = new PromptEntity();
        zeroPromptEntity.setModule("UNIT");
        zeroPromptEntity.setScene("UNIT_OVERVIEW");
        zeroPromptEntity.setId("28e6e08c-68b5-4fbc-a8fe-644b7691f987");
        zeroPromptEntity.setCreateUserId("875dd41c-e0c5-4745-bed6-ff1e9a5955b1");
        zeroPromptEntity.setActive(true);
        zeroPromptEntity.setPromptSource("USER");
        zeroPromptEntity.setTemperature(10.00);
        promptEntityList.add(zeroPromptEntity);
        activePromptListScene.setRecords(promptEntityList);
        activePromptListScene.setTotal((long) promptEntityList.size());
        activePromptListScene.setPageNum(1L);
        activePromptListScene.setPageSize((long) promptEntityList.size());
        when(promptDao.getPromptPageScene(anyInt(), anyInt(), anyString())).thenReturn(activePromptListScene);
        when(promptDao.getSystemAndUserPromptPageByScene(anyInt(), anyInt(), anyString(), anyString())).thenReturn(activePromptListScene);
        // 用户姓名信息
        List<UserProfileEntity> userEntityList = new ArrayList<>();
        UserProfileEntity userProfileEntity = new UserProfileEntity();
        userProfileEntity.setUserId("875dd41c-e0c5-4745-bed6-ff1e9a5955b1");
        userProfileEntity.setDisplayName("Admin");
        userEntityList.add(userProfileEntity);
        when(userDao.getUserProfileByIds(anyList())).thenReturn(userEntityList);
        // PromptUsageRecordEntity 列表
        List<PromptUsageRecordEntity> promptUsageRecordList = new ArrayList<>();
        // 有分数的 PromptUsageRecordEntity
        PromptUsageRecordEntity promptUsageRecordEntity = new PromptUsageRecordEntity();
        promptUsageRecordEntity.setPromptId("733f4ce0-9bf9-4ba4-b350-e58d11891ec1");
        promptUsageRecordEntity.setExecuteCost(100.00);
        promptUsageRecordEntity.setExecuteDuration(100.00);
        promptUsageRecordEntity.setScore(100.00);
        promptUsageRecordList.add(promptUsageRecordEntity);
        // 无分数的 PromptUsageRecordEntity
        PromptUsageRecordEntity zeroPromptUsageRecordEntity = new PromptUsageRecordEntity();
        zeroPromptUsageRecordEntity.setPromptId("28e6e08c-68b5-4fbc-a8fe-644b7691f987");
        zeroPromptUsageRecordEntity.setExecuteCost(null);
        zeroPromptUsageRecordEntity.setExecuteDuration(null);
        zeroPromptUsageRecordEntity.setScore(null);
        promptUsageRecordList.add(zeroPromptUsageRecordEntity);
        when(promptUsageRecordDao.getPromptUsageRecordByPromptIdsAndTypeSource(anyList(), anyString(),anyString())).thenReturn(promptUsageRecordList);
        // 调用方法
        PageResponse<PromptModel> unitOverview = promptServiceImpl.listPromptsByScene(10, 1, "UNIT_OVERVIEW");
        // 断言
        Assert.assertNotNull(unitOverview);
        // 验证数据
        Assert.assertEquals(3,unitOverview.getTotal().intValue());
        Assert.assertEquals(3,unitOverview.getItems().size());
    }

    /**
     * 测试根据场景获取用户默认使用版本的 Prompt
     */
    @Test
    public void getDefaultPromptMock() {
        // 组装数据
        PromptEntity promptEntity = commonPrompt();
        when(promptProvider.getActivePromptByScene(anyString())).thenReturn(promptEntity);
        // 调用方法
        PromptModel defaultPrompt = promptServiceImpl.getDefaultPrompt("UNIT_OVERVIEW");
        // 断言
        Assert.assertNotNull(defaultPrompt);
        // 验证数据
        Assert.assertEquals("733f4ce0-9bf9-4ba4-b350-e58d11891ec1", defaultPrompt.getId());
        Assert.assertEquals("UNIT_OVERVIEW", defaultPrompt.getScene());
    }


    /**
     * 测试根据 Prompt ID获获取 Prompt 详情
     */
    @Test
    public void getPromptMock() {
        // 组装数据
        PromptEntity promptEntity = commonPrompt();
        when(promptDao.getById(anyString())).thenReturn(promptEntity);
        // 调用方法
        PromptModel prompt = promptServiceImpl.getPrompt("733f4ce0-9bf9-4ba4-b350-e58d11891ec1");
        // 断言
        Assert.assertNotNull(prompt);
        // 验证数据
        Assert.assertEquals(promptEntity.getId(), prompt.getId());
        Assert.assertEquals("UNIT_OVERVIEW", prompt.getScene());
    }

    @Test
    public void getTestPromptMock() {
        // 组装数据
        // 用户信息
        AuthUserDetails user = new AuthUserDetails();
        user.setUsername("875dd41c-e0c5-4745-bed6-ff1e9a5955b1");
        when(userProvider.getCurrentUser()).thenReturn(user);
        // prompt 信息
        PromptEntity promptEntity = commonPrompt();
        when(promptDao.getById(anyString())).thenReturn(promptEntity);
        List<PromptUsageRecordEntity> promptUsageRecordEntityList = new ArrayList<>();
        PromptUsageRecordEntity promptUsageRecordEntity = new PromptUsageRecordEntity();
        promptUsageRecordEntity.setPromptId("733f4ce0-9bf9-4ba4-b350-e58d11891ec1");
        promptUsageRecordEntity.setExecuteCost(100.00);
        promptUsageRecordEntity.setExecuteDuration(100.00);
        promptUsageRecordEntity.setScore(100.00);
        promptUsageRecordEntityList.add(promptUsageRecordEntity);
        when(promptUsageRecordDao.getByPromptIdAndUserId(anyString(),anyString(),anyString(),anyString())).thenReturn(promptUsageRecordEntityList);
        // 调用方法
        PromptModel prompt = promptServiceImpl.getTestPrompt("733f4ce0-9bf9-4ba4-b350-e58d11891ec1");
        // 断言
        Assert.assertNotNull(prompt);
        // 验证数据
        Assert.assertEquals(promptEntity.getId(), prompt.getId());
        Assert.assertEquals("UNIT_OVERVIEW", prompt.getScene());
        Assert.assertEquals("100.00", prompt.getAverageScore());
    }

    /**
     * 测试创建 Prompt 版本
     */
    @Test
    public void createPromptVersionMock() {
        // 组装数据
        // 请求信息
        CreatePromptVersionRequest request = new CreatePromptVersionRequest();
        request.setId("733f4ce0-9bf9-4ba4-b350-e58d11891ec1");
        request.setVersion("V1.0");
        request.setPromptTemplate("test mock");
        request.setCreateSource("USER");
        request.setDraft(true);
        request.setModule("UNIT");
        request.setModel("gpt3.5");
        request.setTemperature(10.00);
        request.setEvaluatePromptTemplate("test mock");
        // 原始 Prompt 信息
        PromptEntity promptEntity = commonPrompt();
        when(promptDao.getById(anyString())).thenReturn(promptEntity);
        // 用户信息
        String userId = "875dd41c-e0c5-4745-bed6-ff1e9a5955b1";
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        // 调用方法
        PromptModel promptVersion = promptServiceImpl.createPromptVersion(request);
        // 断言
        Assert.assertNotNull(promptVersion);
        // 验证数据
        Assert.assertEquals(promptEntity.getId(), promptVersion.getId());
    }

    /**
     * 测试创建 Prompt 版本
     */
    @Test
    public void createPromptMock() {
        // 组装数据
        // 请求信息
        CreatePromptVersionRequest request = new CreatePromptVersionRequest();
        request.setId("733f4ce0-9bf9-4ba4-b350-e58d11891ec1");
        request.setVersion("V1.0");
        request.setPromptTemplate("test mock");
        request.setCreateSource("USER");
        request.setDraft(false);
        request.setModule("UNIT");
        request.setModel("gpt3.5");
        request.setTemperature(10.00);
        request.setEvaluatePromptTemplate("test mock");
        // 原始 Prompt 信息
        PromptEntity promptEntity = commonPrompt();
        when(promptDao.getById(anyString())).thenReturn(promptEntity);
        // 用户信息
        String userId = "875dd41c-e0c5-4745-bed6-ff1e9a5955b1";
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        // 调用方法
        PromptModel promptVersion = promptServiceImpl.createPromptVersion(request);
        // 断言
        Assert.assertNotNull(promptVersion);
        // 验证数据
        Assert.assertEquals(promptEntity.getId(), promptVersion.getId());
    }

    /**
     * 测试创建 Prompt 版本
     */
    @Test
    public void createPromptVersionFlagMock() {
        // 组装数据
        // 请求信息
        CreatePromptVersionRequest request = new CreatePromptVersionRequest();
        request.setId("733f4ce0-9bf9-4ba4-b350-e58d11891ec1");
        request.setPromptTemplate("test mock");
        request.setCreateSource("USER");
        request.setDraft(false);
        request.setModule("UNIT");
        request.setModel("gpt3.6");
        request.setTemperature(10.00);
        request.setEvaluatePromptTemplate("test mock");
        // 原始 Prompt 信息
        PromptEntity promptEntity = commonPrompt();
        when(promptDao.getById(anyString())).thenReturn(promptEntity);
        // 用户信息
        String userId = "875dd41c-e0c5-4745-bed6-ff1e9a5955b1";
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        // 用户信息
        AuthUserDetails user = new AuthUserDetails();
        user.setUsername("875dd41c-e0c5-4745-bed6-ff1e9a5955b1");
        when(userProvider.getCurrentUser()).thenReturn(user);
        // 用户当前使用的 Metadata 信息
        MetaDataEntity metaData = new MetaDataEntity();
        metaData.setUserId("875dd41c-e0c5-4745-bed6-ff1e9a5955b1");
        metaData.setMetaKey("UNIT_OVERVIEW");
        metaData.setMetaValue("733f4ce0-9bf9-4ba4-b350-e58d11891ec1");
        when(userMetaDao.getByUserIdAndMetaKey(anyString(), anyString())).thenReturn(metaData);
        // 最新的版本号信息
        PromptEntity latestPrompt = new PromptEntity();
        latestPrompt.setVersion("V1.0");
        when(promptDao.getLatestPromptByScene(anyString())).thenReturn(latestPrompt);
        // 调用方法
        PromptModel promptVersion = promptServiceImpl.createPromptVersion(request);
        // 断言
        Assert.assertNotNull(promptVersion);
        // 验证数据
        Assert.assertEquals("V1.1", promptVersion.getVersion());
    }

    /**
     * 测试创建 Prompt 版本
     */
    @Test
    public void createPromptMetaMock() {
        // 组装数据
        // 请求信息
        CreatePromptVersionRequest request = new CreatePromptVersionRequest();
        request.setId("733f4ce0-9bf9-4ba4-b350-e58d11891ec1");
        request.setVersion("V1.0");
        request.setPromptTemplate("test mock");
        request.setCreateSource("USER");
        request.setDraft(false);
        request.setModule("UNIT");
        request.setModel("gpt3.6");
        request.setTemperature(10.00);
        request.setEvaluatePromptTemplate("test mock");
        // 原始 Prompt 信息
        PromptEntity promptEntity = commonPrompt();
        when(promptDao.getById(anyString())).thenReturn(promptEntity);
        // 用户信息
        String userId = "875dd41c-e0c5-4745-bed6-ff1e9a5955b1";
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        // 用户信息
        AuthUserDetails user = new AuthUserDetails();
        user.setUsername("875dd41c-e0c5-4745-bed6-ff1e9a5955b1");
        when(userProvider.getCurrentUser()).thenReturn(user);
        when(userMetaDao.getByUserIdAndMetaKey(anyString(), anyString())).thenReturn(null);
        // 最新的版本号信息
        PromptEntity latestPrompt = new PromptEntity();
        latestPrompt.setVersion("V1.0");
        when(promptDao.getLatestPromptByScene(anyString())).thenReturn(latestPrompt);
        // 调用方法
        PromptModel promptVersion = promptServiceImpl.createPromptVersion(request);
        // 断言
        Assert.assertNotNull(promptVersion);
        // 验证数据
        Assert.assertEquals("V1.1", promptVersion.getVersion());
    }

    /**
     * 测试创建 Prompt 版本
     */
    @Test
    public void createSystemPromptMock() {
        // 组装数据
        // 请求信息
        CreatePromptVersionRequest request = new CreatePromptVersionRequest();
        request.setId("733f4ce0-9bf9-4ba4-b350-e58d11891ec1");
        request.setPromptTemplate("test mock");
        request.setCreateSource("SYSTEM");
        request.setDraft(false);
        request.setModule("UNIT");
        request.setModel("gpt3.6");
        request.setTemperature(10.00);
        request.setEvaluatePromptTemplate("test mock");
        // 原始 Prompt 信息
        PromptEntity promptEntity = new PromptEntity();
        promptEntity.setModule("UNIT");
        promptEntity.setScene("UNIT_OVERVIEW");
        promptEntity.setId("733f4ce0-9bf9-4ba4-b350-e58d11891ec1");
        promptEntity.setCreateUserId("875dd41c-e0c5-4745-bed6-ff1e9a5955b1");
        promptEntity.setActive(true);
        promptEntity.setDraft(true);
        promptEntity.setPromptSource("SYSTEM");
        promptEntity.setTemperature(10.00);
        promptEntity.setModel("gpt3.5");
        promptEntity.setVersion("V1.0");
        promptEntity.setPromptTemplate("test mock");
        promptEntity.setEvaluatePromptTemplate("test mock");
        when(promptDao.getById(anyString())).thenReturn(promptEntity);
        // 用户信息
        String userId = "875dd41c-e0c5-4745-bed6-ff1e9a5955b1";
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        // 用户信息
        AuthUserDetails user = new AuthUserDetails();
        user.setUsername("875dd41c-e0c5-4745-bed6-ff1e9a5955b1");
        when(userProvider.getCurrentUser()).thenReturn(user);
        when(userMetaDao.getByUserIdAndMetaKey(anyString(), anyString())).thenReturn(null);
        // 最新的版本号信息
        PromptEntity latestPrompt = new PromptEntity();
        latestPrompt.setVersion("V1.0");
        when(promptDao.getLatestPromptByScene(anyString())).thenReturn(latestPrompt);
        // 调用方法
        PromptModel promptVersion = promptServiceImpl.createPromptVersion(request);
        // 断言
        Assert.assertNotNull(promptVersion);
        // 验证数据
        Assert.assertEquals("V1.1", promptVersion.getVersion());
    }

    /**
     * 测试设置用户当前 Prompt 版本
     */
    @Test
    public void setUserCurrentPromptMock() {
        // 组装数据
        // 请求信息
        SetCurrentPromptRequest request = getSetCurrentPromptRequest();
        request.setId("733f4ce0-9bf9-4ba4-b350-e58d11891ec1");
        // 调用方法
        IdResponse promptVersion = promptServiceImpl.setUserCurrentPrompt(request);
        // 断言
        Assert.assertNotNull(promptVersion);
        // 验证数据
        Assert.assertEquals(request.getId(), promptVersion.getId());
    }

    /**
     * 测试设置用户认为最好的 Prompt 版本
     */
    @Test
    public void setUserBestPromptMock() {
        // 组装数据
        // 请求信息
        SetCurrentPromptRequest request = getSetCurrentPromptRequest();
        // 调用方法
        IdResponse promptVersion = promptServiceImpl.setUserBestPrompt(request);
        // 断言
        Assert.assertNotNull(promptVersion);
        // 验证数据
        Assert.assertEquals(request.getId(), promptVersion.getId());
    }

    /**
     * 测试设置系统当前 Prompt 版本
     */
    @Test
    public void setSystemCurrentPromptMock() {
        SetCurrentPromptRequest request = getSetCurrentPromptRequest();
        // 调用方法
        IdResponse promptVersion = promptServiceImpl.setSystemCurrentPrompt(request);
        // 断言
        Assert.assertNotNull(promptVersion);
        // 验证数据
        Assert.assertEquals(request.getId(), promptVersion.getId());
    }

    /**
     * 测试删除 Prompt 版本
     */
    @Test
    public void deletePromptMock() {
        DeletePromptRequest request = new DeletePromptRequest();
        request.setId("733f4ce0-9bf9-4ba4-b350-e58d11891ec1");
        // 调用方法
        promptServiceImpl.deletePrompt(request);
        // 断言
        Mockito.verify(promptDao, times(1)).updateById(any());// save应该被调用了一次
    }

    /**
     * 测试获取 Prompt 使用记录列表
     */
    @Test
    public void listPromptRecordsMock() {
        // 组装数据
        // 用户信息
        PageList<PromptUsageRecordEntity> byPageTestPromptId = getEntityPageList();
        when(promptUsageRecordDao.getByPagePromptId(anyInt(), anyInt(), anyString(), anyString())).thenReturn(byPageTestPromptId);
        // 调用方法
        PageResponse<PromptRecordModel> promptRecordModelPageResponse = promptServiceImpl.listPromptRecords(10, 1, "733f4ce0-9bf9-4ba4-b350-e58d11891ec1");
        // 断言
        Assert.assertNotNull(promptRecordModelPageResponse);
        // 验证数据
        Assert.assertEquals(2, promptRecordModelPageResponse.getTotal().intValue());
        Assert.assertEquals(2, promptRecordModelPageResponse.getItems().size());
        Assert.assertEquals("100.00", promptRecordModelPageResponse.getItems().get(0).getScore());

    }

    /**
     * 测试获取测试 Prompt 使用记录列表
     */
    @Test
    public void listTestPromptRecordsMock() {
        // 组装数据
        // 用户信息
        List<PromptUsageRecordEntity> promptUsageRecordList = new ArrayList<>();
        // 有分数的 PromptUsageRecordEntity
        PromptUsageRecordEntity promptUsageRecordEntity = new PromptUsageRecordEntity();
        promptUsageRecordEntity.setPromptId("733f4ce0-9bf9-4ba4-b350-e58d11891ec1");
        promptUsageRecordEntity.setExecuteCost(100.00);
        promptUsageRecordEntity.setExecuteDuration(100.00);
        promptUsageRecordEntity.setScore(100.00);
        promptUsageRecordEntity.setExecuteStatus("COMPLETED");
        promptUsageRecordEntity.setEvaluatePrompt("test");
        promptUsageRecordList.add(promptUsageRecordEntity);
        // 无分数的 PromptUsageRecordEntity
        PromptUsageRecordEntity zeroPromptUsageRecordEntity = new PromptUsageRecordEntity();
        zeroPromptUsageRecordEntity.setPromptId("733f4ce0-9bf9-4ba4-b350-e58d11891ec1");
        zeroPromptUsageRecordEntity.setExecuteCost(null);
        zeroPromptUsageRecordEntity.setExecuteDuration(null);
        zeroPromptUsageRecordEntity.setScore(null);
        zeroPromptUsageRecordEntity.setExecuteStatus("PENDING");
        promptUsageRecordList.add(zeroPromptUsageRecordEntity);
        when(promptUsageRecordDao.listPromptTestUsageRecordsWithEvaluateCompletion(anyString())).thenReturn(promptUsageRecordList);
        // prompt 信息
        PromptEntity promptEntity = new PromptEntity();
        promptEntity.setModel("gpt-3.5-turbo");
        promptEntity.setScene("test");
        promptEntity.setTemperature(10.0);
        when(promptDao.getById(anyString())).thenReturn(promptEntity);
        // 调用方法
        ListPromptTestRecordsResponse promptRecordModelPageResponse = promptServiceImpl.listPromptTestRecords("733f4ce0-9bf9-4ba4-b350-e58d11891ec1");
        // 断言
        Assert.assertNotNull(promptRecordModelPageResponse);
        // 验证数据
        Assert.assertEquals(2, promptRecordModelPageResponse.getUsageRecords().size());
        Assert.assertEquals("test", promptRecordModelPageResponse.getUsageRecords().get(0).getEvaluatePrompt());
        Assert.assertEquals("PENDING", promptRecordModelPageResponse.getUsageRecords().get(1).getExecuteStatus());

    }


    private PageList<PromptUsageRecordEntity> getEntityPageList() {
        AuthUserDetails user = new AuthUserDetails();
        user.setUsername("875dd41c-e0c5-4745-bed6-ff1e9a5955b1");
        when(userProvider.getCurrentUser()).thenReturn(user);
        // PromptUsageRecordEntity 列表
        PageList<PromptUsageRecordEntity> byPageTestPromptId = new PageList<>();
        List<PromptUsageRecordEntity> promptUsageRecordList = new ArrayList<>();
        // 有分数的 PromptUsageRecordEntity
        PromptUsageRecordEntity promptUsageRecordEntity = new PromptUsageRecordEntity();
        promptUsageRecordEntity.setPromptId("733f4ce0-9bf9-4ba4-b350-e58d11891ec1");
        promptUsageRecordEntity.setExecuteCost(100.00);
        promptUsageRecordEntity.setExecuteDuration(100.00);
        promptUsageRecordEntity.setScore(100.00);
        promptUsageRecordEntity.setExecuteStatus("PENDING");
        promptUsageRecordList.add(promptUsageRecordEntity);
        // 无分数的 PromptUsageRecordEntity
        PromptUsageRecordEntity zeroPromptUsageRecordEntity = new PromptUsageRecordEntity();
        zeroPromptUsageRecordEntity.setPromptId("733f4ce0-9bf9-4ba4-b350-e58d11891ec1");
        zeroPromptUsageRecordEntity.setExecuteCost(null);
        zeroPromptUsageRecordEntity.setExecuteDuration(null);
        zeroPromptUsageRecordEntity.setScore(null);
        zeroPromptUsageRecordEntity.setExecuteStatus("PENDING");
        promptUsageRecordList.add(zeroPromptUsageRecordEntity);
        byPageTestPromptId.setRecords(promptUsageRecordList);
        byPageTestPromptId.setPageNum(1L);
        byPageTestPromptId.setPageSize(10L);
        byPageTestPromptId.setTotal(2L);
        return byPageTestPromptId;
    }

    /**
     * 测试查询 Prompt 使用的详细信息
     */
    @Test
    public void getUsagePromptRecordMock() {
        // 组装数据
        // prompt 使用记录
        PromptUsageRecordEntity promptUsageRecordEntity = new PromptUsageRecordEntity();
        promptUsageRecordEntity.setPromptId("733f4ce0-9bf9-4ba4-b350-e58d11891ec1");
        promptUsageRecordEntity.setExecuteCost(100.00);
        promptUsageRecordEntity.setExecuteDuration(100.00);
        promptUsageRecordEntity.setScore(100.00);
        when(promptUsageRecordDao.getById(anyString())).thenReturn(promptUsageRecordEntity);
        PromptUsageObjectEntity promptUsageObjectEntity = new PromptUsageObjectEntity();
        promptUsageObjectEntity.setUsageRecordId("733f4ce0-9bf9-4ba4-b350-e58d11891ec1");
        when(promptUsageObjectDao.getByUseObjectId(anyString())).thenReturn(promptUsageObjectEntity);
        PromptUsageRecordEntity evaluatePromptUsageRecord = new PromptUsageRecordEntity();
        when(promptUsageRecordDao.getEvaluatePromptUsageRecord(anyString())).thenReturn(evaluatePromptUsageRecord);
        // 调用方法
        PromptRecordModel usagePromptRecord = promptServiceImpl.getUsagePromptRecord("733f4ce0-9bf9-4ba4-b350-e58d11891ec1");
        // 断言
        Assert.assertNotNull(usagePromptRecord);
        // 验证数据
        Assert.assertEquals("100.00", usagePromptRecord.getScore());
        Assert.assertEquals("100.000", usagePromptRecord.getExecuteCost());
        Assert.assertEquals("100.00", usagePromptRecord.getExecuteDuration());
    }

    /**
     * 测试获取用户测试最近记录的状态
     */
    @Test
    public void getTestPromptRecordStatusMock() {
        // 组装数据
        // 用户信息
        String userId = "875dd41c-e0c5-4745-bed6-ff1e9a5955b1";
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        // promptTestRecordEntity 信息
        PromptTestRecordEntity promptTestRecordEntity = new PromptTestRecordEntity();
        promptTestRecordEntity.setId(UUID.randomUUID().toString());
        promptTestRecordEntity.setStatus("COMPLETED");
        when(promptTestRecordDao.getLatestByUserId(anyString())).thenReturn(promptTestRecordEntity);
        // 调用方法
        PromptTestRecordModel model = promptServiceImpl.getTestPromptRecordStatus();
        // 断言
        Assert.assertNotNull(model);
        // 验证数据
        Assert.assertEquals("COMPLETED", model.getStatus());
        Assert.assertEquals(promptTestRecordEntity.getId(), model.getId());
    }

    /**
     * 测试获取用户是否具有 prompt 管理页面权限
     */
    @Test
    public void getCurriculumNotifyMock() {
        // 组装数据
        // 用户信息
        AuthUserDetails user = new AuthUserDetails();
        user.setUsername("875dd41c-e0c5-4745-bed6-ff1e9a5955b1");
        when(userProvider.getCurrentUser()).thenReturn(user);
        // 用户所有的 Metadata 信息
        List<MetaDataEntity> metaDataEntities = new ArrayList<>();
        MetaDataEntity metaDataEntity = new MetaDataEntity();
        metaDataEntity.setMetaKey("CURRICULUM_GENIE_OPEN_FLAG");
        metaDataEntity.setMetaValue("true");
        metaDataEntities.add(metaDataEntity);
        MetaDataEntity metaDataEntity1 = new MetaDataEntity();
        metaDataEntity1.setMetaKey("PROMPT_MANAGEMENT_OPEN_FLAG");
        metaDataEntity1.setMetaValue("true");
        metaDataEntities.add(metaDataEntity1);
        MetaDataEntity metaDataEntity2 = new MetaDataEntity();
        metaDataEntity2.setMetaKey("PROMPT_DEBUGGING_OPEN_FLAG");
        metaDataEntity2.setMetaValue("true");
        metaDataEntities.add(metaDataEntity2);
        metaDataEntities.add(null);
        when(userMetaDao.getByUserId(anyString())).thenReturn(metaDataEntities);
        // 调用方法
        CurriculumNotifyResponse curriculumNotify = promptServiceImpl.getCurriculumNotify();
        // 断言
        Assert.assertNotNull(curriculumNotify);
        // 验证数据
        Assert.assertTrue(curriculumNotify.getCurriculumGenie());
        Assert.assertTrue(curriculumNotify.getPromptManagement());
        Assert.assertTrue(curriculumNotify.getPromptDebugging());
    }


    private SetCurrentPromptRequest getSetCurrentPromptRequest() {
        // 组装数据
        // 请求信息
        SetCurrentPromptRequest request = new SetCurrentPromptRequest();
        request.setId("733f4ce0-9bf9-4ba4-b350-e58d11891ec1");
        // prompt 信息
        PromptEntity promptEntity = commonPrompt();
        when(promptDao.getById(anyString())).thenReturn(promptEntity);
        // 用户信息
        AuthUserDetails user = new AuthUserDetails();
        user.setUsername("875dd41c-e0c5-4745-bed6-ff1e9a5955b1");
        when(userProvider.getCurrentUser()).thenReturn(user);
        // 用户当前使用的 Metadata 信息
        MetaDataEntity metaData = new MetaDataEntity();
        metaData.setUserId("875dd41c-e0c5-4745-bed6-ff1e9a5955b1");
        metaData.setMetaKey("UNIT_OVERVIEW");
        metaData.setMetaValue("733f4ce0-9bf9-4ba4-b350-e58d11891ec1");
        when(userMetaDao.getByUserIdAndMetaKey(anyString(), anyString())).thenReturn(metaData);
        return request;
    }


    /**
     * 组装 Prompt 数据方法提取
     */
    private PromptEntity commonPrompt() {
        PromptEntity promptEntity = new PromptEntity();
        promptEntity.setModule("UNIT");
        promptEntity.setScene("UNIT_OVERVIEW");
        promptEntity.setId("733f4ce0-9bf9-4ba4-b350-e58d11891ec1");
        promptEntity.setCreateUserId("875dd41c-e0c5-4745-bed6-ff1e9a5955b1");
        promptEntity.setActive(true);
        promptEntity.setDraft(true);
        promptEntity.setPromptSource("SYSTEM");
        promptEntity.setTemperature(10.00);
        promptEntity.setModel("gpt3.5");
        promptEntity.setVersion("V1.0");
        promptEntity.setPromptTemplate("test mock");
        promptEntity.setEvaluatePromptTemplate("test mock");
        return promptEntity;
    }

    /**
     * 测试保存 Prompt 使用记录列表，保存的数据是 null
     */
    @Test
    public void testSaveFeedbackWithNullData() {
        // 创建一个带有空数据的请求
        CreatePromptFeedbackRequest request = new CreatePromptFeedbackRequest();
        // 设置数据为空
        request.setFeedbackData(null);
        // 设置使用记录为空
        request.setPromptUsageRecordIds(null);
        // 定义用户 ID
        String userId = "testUserId";

        // 模拟行为
        IdResponse result = promptServiceImpl.saveFeedBack(request, userId);

        // 断言结果为 null，因为数据为空
        Assert.assertNull(result);

        // 验证 promptFeedBackDao 的方法未被调用
        verifyNoMoreInteractions(promptFeedBackDao);
    }

    /**
     * 测试保存 Prompt 使用记录列表，保存的数据是空的
     */
    @Test
    public void testSaveFeedbackWithEmptyData() {
        // 创建一个带有空数据的请求
        CreatePromptFeedbackRequest request = new CreatePromptFeedbackRequest();
        // 设置数据为空对象
        request.setFeedbackData(new PromptFeedbackDataModel());
        // 设置使用记录为空
        request.setPromptUsageRecordIds(new ArrayList<>());
        // 定义用户 ID
        String userId = "testUserId";

        // 模拟行为
        IdResponse result = promptServiceImpl.saveFeedBack(request, userId);

        // 断言结果为 null，因为数据为空
        Assert.assertNull(result);

        // 验证 promptFeedBackDao 的方法未被调用
        verifyNoMoreInteractions(promptFeedBackDao);
    }

    /**
     * 测试保存 Prompt 使用记录列表，保存的数据是已经存在的
     */
    @Test
    public void testSaveFeedbackWithExistingFeedbackEntities() {
        // 创建一个带有有效数据的请求
        CreatePromptFeedbackRequest request = createValidRequest();
        String userId = "testUserId";

        // 模拟已存在的反馈实体的行为
        when(promptFeedBackDao.getPromptFeedBackByUserIdAndPromptUsageRecordIds(anyString(), anyList()))
                .thenReturn(createMockPromptFeedbackEntities());

        // 模拟 updateBatchById 方法
        when(promptFeedBackDao.updateBatchById(anyList())).thenReturn(true);

        // 调用对应的方法
        IdResponse result = promptServiceImpl.saveFeedBack(request, userId);

        // 断言反馈实体已更新
        Assert.assertEquals("EXPECTEDID", result.getId());

        // 验证 updateBatchById 被调用
        verify(promptFeedBackDao).updateBatchById(anyList());
    }

    /**
     * 测试保存 Prompt 使用记录列表，批量保存
     */
    @Test
    public void testSaveFeedbackWithNewFeedbackEntities() {
        // 创建一个带有有效数据的请求
        CreatePromptFeedbackRequest request = createValidRequest();
        String userId = "testUserId";

        // 模拟不存在的反馈实体的行为
        when(promptFeedBackDao.getPromptFeedBackByUserIdAndPromptUsageRecordIds(anyString(), anyList()))
                .thenReturn(new ArrayList<>());

        // 模拟 saveBatch 方法
        when(promptFeedBackDao.saveBatch(anyList())).thenReturn(true);

        // 调用对应的方法
        IdResponse result = promptServiceImpl.saveFeedBack(request, userId);

        // 断言新的反馈实体已保存
        Assert.assertNotNull(result.getId());

        // 验证 saveBatch 被调用
        verify(promptFeedBackDao).saveBatch(anyList());
    }


    /**
     * 创建一个 CreatePromptFeedbackRequest 请求
     */
    private CreatePromptFeedbackRequest createValidRequest() {
        // 创建一个有效的请求
        PromptFeedbackDataModel feedbackData = new PromptFeedbackDataModel();
        // 定义 promptUsageRecordIds 的数据
        List<String> promptUsageRecordIds = Arrays.asList("recordId1", "recordId2");
        // 定义一个创建请求
        CreatePromptFeedbackRequest feedbackRequest = new CreatePromptFeedbackRequest();
        // 设置请求的数据
        feedbackRequest.setFeedbackData(feedbackData);
        // 设置请求的使用记录 ID
        feedbackRequest.setPromptUsageRecordIds(promptUsageRecordIds);
        // 返回请求
        return feedbackRequest;
    }

    /**
     * 创建一个 PromptFeedBackEntity 列表
     */
    private List<PromptFeedBackEntity> createMockPromptFeedbackEntities() {
        // 创建模拟的反馈实体列表
        PromptFeedBackEntity entity = new PromptFeedBackEntity();
        // 设置 ID
        entity.setId("expectedId");
        // 设置 feedbackData
        entity.setFeedbackData("expectedData");
        // 设置使用记录 ID
        return Collections.singletonList(entity);
    }

    /**
     * 测试获取 Prompt 使用记录列表，使用记录 ID 为空
     */
    @Test
    public void testGetLastFeedbackWithEmptyPromptUsageRecordIds() {
        // 创建一个带有空数据的请求
        GetPromptFeedbackRequest request = new GetPromptFeedbackRequest();
        // 设置使用记录为空
        request.setPromptUsageRecordIds(Collections.emptyList());
        // 定义用户 ID
        String userId = "testUserId";

        // 模拟行为
        PromptFeedbackResponse result = promptServiceImpl.getLastFeedBackByUserId(request, userId);

        // 断言结果为 null，因为数据为空
        Assert.assertNull(result);

        // 验证 promptFeedBackDao 的方法未被调用
        verifyNoInteractions(promptFeedBackDao);
    }

    /**
     * 测试获取 Prompt 使用记录列表，使用记录 ID 为 recordId
     */
    @Test
    public void testGetLastFeedbackWithNoMatchingFeedback() {
        // 创建一个带有有效数据的请求
        GetPromptFeedbackRequest request = createGetPromptFeedbackRequestRequest();
        // 定义用户 ID
        String userId = "testUserId";

        // 模拟行为，返回一个空的反馈列表
        when(promptFeedBackDao.getPromptFeedBackByUserIdAndPromptUsageRecordIds(anyString(), anyList()))
                .thenReturn(new ArrayList<>());

        // 调用对应的方法
        PromptFeedbackResponse result = promptServiceImpl.getLastFeedBackByUserId(request, userId);

        // 断言结果为null，因为没有匹配的反馈
        Assert.assertNull(result);

        // 验证promptFeedBackDao的方法被调用
        verify(promptFeedBackDao).getPromptFeedBackByUserIdAndPromptUsageRecordIds(anyString(), anyList());
    }

    @Test
    public void testGetLastFeedbackWithMatchingFeedback() {
        // 创建一个带有有效数据的请求
        GetPromptFeedbackRequest request = createGetPromptFeedbackRequestRequest();
        String userId = "testUserId";

        // 模拟行为，返回一个包含有效反馈的列表
        when(promptFeedBackDao.getPromptFeedBackByUserIdAndPromptUsageRecordIds(anyString(), anyList()))
                .thenReturn(createMockPromptFeedbackEntities());

        PromptFeedbackResponse result = promptServiceImpl.getLastFeedBackByUserId(request, userId);

        // 断言结果为有效的 PromptFeedbackResponse
        Assert.assertNotNull(result);
        Assert.assertEquals("expectedData", result.getFeedbackData());

        // 验证 promptFeedBackDao 的方法被调用
        verify(promptFeedBackDao).getPromptFeedBackByUserIdAndPromptUsageRecordIds(anyString(), anyList());
    }


    /**
     * 创建一个 GetPromptFeedbackRequest 请求
     */
    private GetPromptFeedbackRequest createGetPromptFeedbackRequestRequest() {
        // 创建一个有效的请求
        GetPromptFeedbackRequest getPromptFeedbackRequest = new GetPromptFeedbackRequest();
        // 设置请求的使用记录 ID
        getPromptFeedbackRequest.setPromptUsageRecordIds(Collections.singletonList("recordId"));
        // 返回请求
        return getPromptFeedbackRequest;
    }

}

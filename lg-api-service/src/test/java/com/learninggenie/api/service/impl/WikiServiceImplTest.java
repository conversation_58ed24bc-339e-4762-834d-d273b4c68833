package com.learninggenie.api.service.impl;

import com.learninggenie.api.model.WikiSearchResultModel;
import com.learninggenie.common.data.model.ResultPojo;
import com.learninggenie.common.utils.RestApiUtil;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpMethod;

import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mockStatic;

@RunWith(MockitoJUnitRunner.class)
public class WikiServiceImplTest {

    @InjectMocks
    private WikiServiceImpl wikiService;

    @Test
    public void testSearch() {
        try (MockedStatic<RestApiUtil> restApiUtil = mockStatic(RestApiUtil.class)) {
            ResultPojo resultPojo = new ResultPojo();
            resultPojo.setData("{\n" +
                    "  \"batchcomplete\": \"\",\n" +
                    "  \"continue\": {\n" +
                    "    \"sroffset\": 10,\n" +
                    "    \"continue\": \"-||\"\n" +
                    "  },\n" +
                    "  \"query\": {\n" +
                    "    \"searchinfo\": {\n" +
                    "      \"totalhits\": 306504,\n" +
                    "      \"suggestion\": \"text\",\n" +
                    "      \"suggestionsnippet\": \"text\"\n" +
                    "    },\n" +
                    "    \"search\": [\n" +
                    "      {\n" +
                    "        \"ns\": 0,\n" +
                    "        \"title\": \"Test\",\n" +
                    "        \"pageid\": 11089416,\n" +
                    "        \"size\": 2915,\n" +
                    "        \"wordcount\": 316,\n" +
                    "        \"snippet\": \"First result\",\n" +
                    "        \"timestamp\": \"2024-01-06T23:30:07Z\"\n" +
                    "      }\n" +
                    "    ]\n" +
                    "  }\n" +
                    "}");
            resultPojo.setStatus(200);
            restApiUtil.when(() -> RestApiUtil.exchange(any(), anyString(), eq(null), eq(null), eq(HttpMethod.GET), eq(2), eq(null), eq(true), eq(null))).thenReturn(resultPojo);

            WikiSearchResultModel wikiSearchResultModel = new WikiSearchResultModel();
            wikiSearchResultModel.setTitle("test");
            wikiSearchResultModel.setSnippet("test");

            List<WikiSearchResultModel> resultModels = wikiService.getSearch("test");

            Assert.assertNotNull(resultModels);
            Assert.assertEquals(1, resultModels.size());
        }
    }

}

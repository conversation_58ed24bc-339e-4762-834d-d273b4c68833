package com.learninggenie.api.service.impl;

import com.google.api.services.drive.Drive;
import com.google.api.services.drive.model.File;
import com.google.api.services.slides.v1.Slides;
import com.google.api.services.slides.v1.model.Presentation;
import com.google.auth.oauth2.AccessToken;
import com.google.auth.oauth2.GoogleCredentials;
import com.learninggenie.api.provider.GoogleAuthProvider;
import com.learninggenie.common.data.enums.googleslides.SlideType;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import com.google.api.services.slides.v1.model.Page;
import org.junit.Test;

import java.io.IOException;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;


@RunWith(MockitoJUnitRunner.class)
public class GoogleSlidesServiceImplTest {

    @InjectMocks
    @Spy
    private GoogleSlidesServiceImpl googleSlidesService;
    @Mock
    private GoogleAuthProvider googleAuthProvider;

    @Test
    public void getPresentationPagesReturnsSlidesWhenSlideTypeIsSlides() throws IOException {
        // Mock 数据
        GoogleCredentials credentials = new GoogleCredentials(new AccessToken("token", null));
        Slides.Presentations presentationsMock = Mockito.mock(Slides.Presentations.class);
        Slides.Presentations.Get presentationGetMock = Mockito.mock(Slides.Presentations.Get.class);
        Presentation presentation = Mockito.mock(Presentation.class);
        Page page = Mockito.mock(Page.class);
        Slides slidesMock = Mockito.mock(Slides.class);
        // 当 google 组件执行的时候，返回模拟数据
        when(googleAuthProvider.getGoogleCredentialsByFile(any(String.class))).thenReturn(credentials);
        when(presentation.getSlides()).thenReturn(Collections.singletonList(page));
        when(googleSlidesService.buildService(credentials)).thenReturn(slidesMock);
        when(slidesMock.presentations()).thenReturn(presentationsMock);
        when(presentationsMock.get(anyString())).thenReturn(presentationGetMock);
        when(presentationGetMock.execute()).thenReturn(presentation);
        // 调用方法
        List<Page> result = googleSlidesService.getPresentationPages("presentationId", SlideType.SIDES);

        // 断言参数
        assertEquals(1, result.size());
        assertEquals(page, result.get(0));
    }

    @Test
    public void getPresentationPagesReturnsEmptyListWhenNoSlides() throws IOException {
        // Mock 数据
        GoogleCredentials credentials = new GoogleCredentials(new AccessToken("token", null));
        Slides.Presentations presentationsMock = Mockito.mock(Slides.Presentations.class);
        Slides.Presentations.Get presentationGetMock = Mockito.mock(Slides.Presentations.Get.class);
        Presentation presentation = Mockito.mock(Presentation.class);
        Slides slidesMock = Mockito.mock(Slides.class);
        // 当 google 组件执行的时候，返回模拟数据
        when(googleAuthProvider.getGoogleCredentialsByFile(any(String.class))).thenReturn(credentials);
        when(presentation.getSlides()).thenReturn(Collections.emptyList());
        when(googleSlidesService.buildService(credentials)).thenReturn(slidesMock);
        when(slidesMock.presentations()).thenReturn(presentationsMock);
        when(presentationsMock.get(anyString())).thenReturn(presentationGetMock);
        when(presentationGetMock.execute()).thenReturn(presentation);
        // 调用方法
        List<Page> result = googleSlidesService.getPresentationPages("presentationId", SlideType.SIDES);

        // 断言参数
        assertEquals(0, result.size());
    }

    @Test(expected = IOException.class)
    public void getPresentationPagesThrowsIOException() throws IOException {
        // Mock 一个 google 认证的对象
        GoogleCredentials credentials = Mockito.mock(GoogleCredentials.class);

        // 当获取的时候抛出异常
        when(googleAuthProvider.getGoogleCredentialsByFile(any(String.class))).thenThrow(new IOException());

        // 调用方法
        googleSlidesService.getPresentationPages("presentationId", SlideType.SIDES);
    }

    @Test
    public void copyPresentationReturnsPresentationId() throws IOException {
        // Mock 数据
        GoogleCredentials credentials = new GoogleCredentials(new AccessToken("token", null));
        Drive driveServiceMock = Mockito.mock(Drive.class);
        Drive.Files driveFilesMock = Mockito.mock(Drive.Files.class);
        Drive.Permissions permissions = Mockito.mock(Drive.Permissions.class);
        Drive.Files.Copy driveFilesCopyMock = Mockito.mock(Drive.Files.Copy.class);
        Drive.Permissions.Create permissionsCreate = Mockito.mock(Drive.Permissions.Create.class);
        File presentationCopyFileMock = Mockito.mock(File.class);
        String presentationId = "presentationId";
        String copyTitle = "Copy Title";
        String presentationCopyId = "presentationCopyId";

        // 设置期望行为
        when(googleAuthProvider.getGoogleCredentialsByFile(any(String.class))).thenReturn(credentials);
        when(googleSlidesService.buildDriveService(credentials)).thenReturn(driveServiceMock);
        when(driveServiceMock.files()).thenReturn(driveFilesMock);
        when(driveServiceMock.permissions()).thenReturn(permissions);
        when(permissions.create(anyString(), any(com.google.api.services.drive.model.Permission.class))).thenReturn(permissionsCreate);
        when(driveFilesMock.copy(eq(presentationId), any(File.class))).thenReturn(driveFilesCopyMock);
        when(driveFilesCopyMock.setSupportsAllDrives(true)).thenReturn(driveFilesCopyMock);
        when(driveFilesCopyMock.execute()).thenReturn(presentationCopyFileMock);
        when(presentationCopyFileMock.getId()).thenReturn(presentationCopyId);
        when(permissionsCreate.setSupportsAllDrives(true)).thenReturn(permissionsCreate);
        when(permissionsCreate.execute()).thenReturn(null);

        // 调用方法
        String result = googleSlidesService.copyPresentation(presentationId, copyTitle);

        // 断言结果
        assertEquals(presentationCopyId, result);
    }

}

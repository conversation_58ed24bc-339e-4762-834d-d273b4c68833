package com.learninggenie.api.service.impl;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.toolkit.ChainWrappers;
import com.google.common.collect.Lists;
import com.learninggenie.api.constant.RedisKeyPrefix;
import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.exception.ErrorCode;
import com.learninggenie.api.model.ExportSftpAccountInfoResponse;
import com.learninggenie.api.model.ExportSftpCenterModel;
import com.learninggenie.api.model.ExportSftpDataModel;
import com.learninggenie.api.model.ExportSftpExportDataResponse;
import com.learninggenie.api.model.ExportSftpGetCenterGroupResponse;
import com.learninggenie.api.model.ExportSftpGradeRatingMapModel;
import com.learninggenie.api.model.ExportSftpGroupModel;
import com.learninggenie.api.model.ExportSftpRecordDetailResponse;
import com.learninggenie.api.model.ExportSftpRecordResponse;
import com.learninggenie.api.model.ExportSftpSettingCenterModel;
import com.learninggenie.api.model.ExportSftpSettingMapModel;
import com.learninggenie.api.model.ExportSftpSettingResponse;
import com.learninggenie.api.model.GetAgencyGradeStagesResponse;
import com.learninggenie.api.model.GetExportSftpOpenResponse;
import com.learninggenie.api.model.GetExportStatusResponse;
import com.learninggenie.api.model.GetOtherFrameworkExportOpenResponse;
import com.learninggenie.api.model.IdResponse;
import com.learninggenie.api.model.SaveExportSftpSettingRequest;
import com.learninggenie.api.provider.PortfolioProvider;
import com.learninggenie.api.provider.RemoteProvider;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.service.AutoExportSftpService;
import com.learninggenie.api.service.PortfolioService;
import com.learninggenie.api.service.ScoreService;
import com.learninggenie.api.service.impl.helper.ReportHelper;
import com.learninggenie.common.cache.CacheService;
import com.learninggenie.common.data.dao.*;
import com.learninggenie.common.data.dao.agencies.AgenciesSftpuserDao;
import com.learninggenie.common.data.dao.dashboard.impl.StatisticsPaUpdateRecordDaoImpl;
import com.learninggenie.common.data.dao.enrollment.EnrollmentPeriodDao;
import com.learninggenie.common.data.dao.enrollment.EnrollmentSnapshotDao;
import com.learninggenie.common.data.dao.export.ExportSftpRecordDao;
import com.learninggenie.common.data.dao.export.ExportSftpSettingDao;
import com.learninggenie.common.data.dao.frameworks.DomainsMapDao;
import com.learninggenie.common.data.dao.frameworks.FrameworkDao;
import com.learninggenie.common.data.dao.frameworks.FrameworkViewDao;
import com.learninggenie.common.data.dao.frameworks.MeasureDao;
import com.learninggenie.common.data.dao.groups.ContentsGroupStageDao;
import com.learninggenie.common.data.dao.impl.StudentDaoImpl;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.entity.*;
import com.learninggenie.common.data.entity.dashbord.StatisticsPaUpdateRecordEntity;
import com.learninggenie.common.data.entity.enrollment.EnrollmentPeriodEntity;
import com.learninggenie.common.data.entity.enrollment.SnapshotEntity;
import com.learninggenie.common.data.entity.export.AgenciesSftpuser;
import com.learninggenie.common.data.entity.export.ExportSftpRecord;
import com.learninggenie.common.data.entity.export.ExportSftpSetting;
import com.learninggenie.common.data.entity.frameworks.DomainsMapEntity;
import com.learninggenie.common.data.entity.frameworks.FrameworkEntity;
import com.learninggenie.common.data.entity.frameworks.FrameworkViewEntity;
import com.learninggenie.common.data.entity.frameworks.MeasureEntity;
import com.learninggenie.common.data.entity.groups.ContentsGroupStage;
import com.learninggenie.common.data.enums.*;
import com.learninggenie.common.data.mapper.mybatisplus.dashboard.StatisticsPaUpdateRecordMapper;
import com.learninggenie.common.data.model.AgencyModel;
import com.learninggenie.common.data.model.CenterGroupModel;
import com.learninggenie.common.data.model.CenterModel;
import com.learninggenie.common.data.model.EnrollmentModel;
import com.learninggenie.common.data.model.GroupModel;
import com.learninggenie.common.data.model.PeriodsGroupPeriodGroupEntity;
import com.learninggenie.common.data.model.ScoreTemplateEntity;
import com.learninggenie.common.data.model.StudentScoreModel;
import com.learninggenie.common.data.model.UserModel;
import com.learninggenie.common.data.model.UsersMetaDataEntity;
import com.learninggenie.common.data.model.dashboard.CenterGroupChildCount;
import com.learninggenie.common.data.model.sftp.HasScoreStudent;
import com.learninggenie.common.filesystem.FileSystem;
import com.learninggenie.common.report.AnalysisService;
import com.learninggenie.common.report.LGSnapshot;
import com.learninggenie.common.score.RatingService;
import com.learninggenie.common.score.model.RatingStatisticModel;
import com.learninggenie.common.sharding.ShardingProvider;
import com.learninggenie.common.utils.JsonUtil;
import com.learninggenie.common.utils.RateUtil;
import com.learninggenie.common.utils.TimeUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.*;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.mock;


@RunWith(MockitoJUnitRunner.class)
public class ExportSftpServiceImplTest {

    @InjectMocks
    private ExportSftpServiceImpl exportSftpService;
    @Mock
    private UserProvider userProvider;
    @Mock
    private UserDaoImpl userDao;
    @Mock
    private ExportSftpSettingDao exportSftpSettingDao;
    @Mock
    private ExportSftpRecordDao exportSftpRecordDao;
    @Mock
    private ContentsGroupStageDao contentsGroupStageDao;
    @Mock
    private GroupDao groupDao;
    @Mock
    private RemoteProvider remoteProvider;
    @Mock
    private CenterDao centerDao;
    @Mock
    private CacheService cacheService;
    @Mock
    private AgenciesSftpuserDao agenciesSftpuserDao;
    @Mock
    private AgencyDao agencyDao;
    @Mock
    private PortfolioProvider portfolioProvider;
    @Mock
    private EnrollmentPeriodDao enrollmentPeriodDao;
    @Mock
    private PeriodsGroupDao periodsGroupDao;
    @Mock
    private StudentDaoImpl studentDao;
    @Mock
    private EnrollmentSnapshotDao enrollmentSnapshotDao;
    @Mock
    private RatingService ratingService;
    @Mock
    private FrameworkDao frameworkDao;
    @Mock
    private FrameworkViewDao frameworkViewDao;
    @Mock
    private DomainsMapDao domainsMapDao;
    @Mock
    private AnalysisService analysisService;
    @Mock
    private MeasureDao measureDao;
    @Mock
    private DomainDao domainDao;
    @Mock
    private ScoreService scoreService;
    @Mock
    private UsersMetaDataDao userMetaDao;
    @Mock
    private com.learninggenie.common.data.dao.contents.GroupDao mpGroupDao;
    @Mock
    private PortfolioService portfolioService;

    @Mock
    private DashboardDao dashboardDao;

    @Mock
    private ShardingProvider shardingProvider;

    @Mock
    private StatisticsPaUpdateRecordDaoImpl statisticsPaUpdateRecordDao;

    @Mock
    private StatisticsPaUpdateRecordMapper statisticsPaUpdateRecordMapper;

    @Mock
    private AutoExportSftpService autoExportSftpService;

    @Mock
    private FileSystem fileSystem;

    @Mock
    private MediaDao mediaDao;

    private MockedStatic<ChainWrappers> chainWrappersMockedStatic;

    /**
     * 初始化 TableInfoHelper
     */
    @BeforeClass
    public static void beforeClass() {
//        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), InKindReportApprove.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), StatisticsPaUpdateRecordEntity.class);
    }

    /**
     * 初始化 chainWrappersMockedStatic
     */
    @Before
    public void beforeMethod() {
        chainWrappersMockedStatic = mockStatic(ChainWrappers.class);
    }

    /**
     * 关闭 chainWrappersMockedStatic
     */
    @After
    public void afterMethod() {
        chainWrappersMockedStatic.close();
    }



    private final String mapJson = "{\"columnMapModels\":[{\"propertyKey\":\"CLASS_ID\",\"lgProperty\":\"Class ID\",\"sftpProperty\":\"Class ID\",\"hidden\":false,\"inOption\":true,\"alwaysShow\":true,\"options\":[{\"key\":\"CLASS_ID\",\"label\":\"Class ID\",\"value\":\"Class ID\"},{\"key\":\"CLASS_NAME\",\"label\":\"Class Name\",\"value\":\"Class Name\"}]},{\"propertyKey\":\"GRADE_CODE\",\"lgProperty\":\"Grade Code\",\"sftpProperty\":\"Grade Code\",\"hidden\":false,\"inOption\":true,\"alwaysShow\":true,\"options\":[{\"key\":\"GRADE_CODE\",\"label\":\"Grade Code\",\"value\":\"Grade Code\"},{\"key\":\"GRADE\",\"label\":\"Grade\",\"value\":\"Grade\"}]},{\"propertyKey\":\"CHILD_ID\",\"lgProperty\":\"Child ID\",\"sftpProperty\":\"Child ID\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":true},{\"propertyKey\":\"MEASURE\",\"lgProperty\":\"Measure\",\"sftpProperty\":\"测评点\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":true},{\"propertyKey\":\"M1\",\"lgProperty\":\"Fall 2023/Time1\",\"sftpProperty\":\"F\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":true},{\"propertyKey\":\"M2\",\"lgProperty\":\"Winter 2023/Time2\",\"sftpProperty\":\"W\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":true},{\"propertyKey\":\"M3\",\"lgProperty\":\"Spring 2024/Time3\",\"sftpProperty\":\"SP\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":true},{\"propertyKey\":\"M4\",\"lgProperty\":\"Summer 2024/Time4\",\"sftpProperty\":\"SU\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":true},{\"lgProperty\":\"Child Name\",\"sftpProperty\":\"Child Name\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":false},{\"lgProperty\":\"ELD\",\"sftpProperty\":\"ELD\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":false},{\"lgProperty\":\"IEP/IFSP\",\"sftpProperty\":\"IEP\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":false},{\"lgProperty\":\"Class Name\",\"sftpProperty\":\"Class Name\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":false},{\"lgProperty\":\"Grade\",\"sftpProperty\":\"Grade\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":false},{\"lgProperty\":\"MT\",\"sftpProperty\":\"MT\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":false},{\"lgProperty\":\"Agency Name\",\"sftpProperty\":\"Agency Name\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":false},{\"lgProperty\":\"Site Name\",\"sftpProperty\":\"Site Name\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":false},{\"lgProperty\":\"Teacher Name\",\"sftpProperty\":\"Teacher Name\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":false},{\"lgProperty\":\"Teacher ID\",\"sftpProperty\":\"Teacher ID\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":false},{\"lgProperty\":\"Birthday\",\"sftpProperty\":\"Birthday\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":false},{\"lgProperty\":\"Gender\",\"sftpProperty\":\"Gender\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":false},{\"lgProperty\":\"Entry Date\",\"sftpProperty\":\"Entry Date\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":false},{\"lgProperty\":\"Statewide Student Identifier\",\"sftpProperty\":\"Statewide Student IdentifierStatewide Student IdentifierStatewide Student IdentifierStatewide Student Identifier\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":false},{\"propertyKey\":null,\"lgProperty\":\"Child's Rating Completion Status\",\"sftpProperty\":\"Child's Rating CS\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":false,\"options\":null},{\"propertyKey\":null,\"lgProperty\":\"Child's Observation Completion Percentage\",\"sftpProperty\":\"Child's Observation CP\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":false,\"options\":null},{\"propertyKey\":null,\"lgProperty\":\"Child's Rating Completion Percentage\",\"sftpProperty\":\"Child's Rating CP\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":false,\"options\":null},{\"propertyKey\":null,\"lgProperty\":\"Child's Rating Completion Date\",\"sftpProperty\":\"Child's Rating CD\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":false,\"options\":null},{\"propertyKey\":null,\"lgProperty\":\"Grantee Agency (Funding Agency)\",\"sftpProperty\":\"Grantee\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":false,\"options\":null}],\"gradeRatingMapModels\":[{\"gradeDefinition\":\"Infant/toddler\",\"ageGroup\":\"0-3\",\"gradeCode\":\"-3\",\"stageId\":\"72516154-3B50-E411-837D-02DBFC8648CE\",\"hidden\":false,\"ratingMapModels\":[{\"lgValue\":\"Responding Earlier\",\"sftpValue\":\"RE\"},{\"lgValue\":\"Responding Later\",\"sftpValue\":\"RL\"},{\"lgValue\":\"Exploring Earlier\",\"sftpValue\":\"EE\"},{\"lgValue\":\"Exploring Middle\",\"sftpValue\":\"EM\"},{\"lgValue\":\"Exploring Later\",\"sftpValue\":\"EL\"},{\"lgValue\":\"Building Earlier\",\"sftpValue\":\"BE\"},{\"lgValue\":\"Building Middle\",\"sftpValue\":\"BM\"},{\"lgValue\":\"Building Later\",\"sftpValue\":\"BL\"},{\"lgValue\":\"Integrating Earlier\",\"sftpValue\":\"IE\"},{\"lgValue\":\"Integrating middle\",\"sftpValue\":\"IM\"},{\"lgValue\":\"Integrating later\",\"sftpValue\":\"IL\"},{\"lgValue\":\"All UR or not selected fields\",\"sftpValue\":\"U\"}]},{\"gradeDefinition\":\"Prekindergarten\",\"ageGroup\":\"3-4\",\"gradeCode\":\"-2\",\"stageId\":\"75516154-3B50-E411-837D-02DBFC8648CE\",\"hidden\":false,\"ratingMapModels\":[{\"lgValue\":\"Responding Earlier\",\"sftpValue\":\"RE\"},{\"lgValue\":\"Responding Later\",\"sftpValue\":\"RL\"},{\"lgValue\":\"Exploring Earlier\",\"sftpValue\":\"EE\"},{\"lgValue\":\"Exploring Middle\",\"sftpValue\":\"EM\"},{\"lgValue\":\"Exploring Later\",\"sftpValue\":\"EL\"},{\"lgValue\":\"Building Earlier\",\"sftpValue\":\"BE\"},{\"lgValue\":\"Building Middle\",\"sftpValue\":\"BM\"},{\"lgValue\":\"Building Later\",\"sftpValue\":\"BL\"},{\"lgValue\":\"Integrating Earlier\",\"sftpValue\":\"IE\"},{\"lgValue\":\"Integrating middle\",\"sftpValue\":\"IM\"},{\"lgValue\":\"Integrating later\",\"sftpValue\":\"IL\"},{\"lgValue\":\"All UR or not selected fields\",\"sftpValue\":\"U\"}]},{\"gradeDefinition\":\"Transitional Kindergarten\",\"ageGroup\":\"4-5\",\"gradeCode\":\"-1\",\"stageId\":\"77516154-3B50-E411-837D-02DBFC8648CE\",\"hidden\":false,\"ratingMapModels\":[{\"lgValue\":\"Responding Earlier\",\"sftpValue\":\"RE\"},{\"lgValue\":\"Responding Later\",\"sftpValue\":\"RL\"},{\"lgValue\":\"Exploring Earlier\",\"sftpValue\":\"EE\"},{\"lgValue\":\"Exploring Middle\",\"sftpValue\":\"EM\"},{\"lgValue\":\"Exploring Later\",\"sftpValue\":\"EL\"},{\"lgValue\":\"Building Earlier\",\"sftpValue\":\"BE\"},{\"lgValue\":\"Building Middle\",\"sftpValue\":\"BM\"},{\"lgValue\":\"Building Later\",\"sftpValue\":\"BL\"},{\"lgValue\":\"Integrating Earlier\",\"sftpValue\":\"IE\"},{\"lgValue\":\"Integrating middle\",\"sftpValue\":\"IM\"},{\"lgValue\":\"Integrating later\",\"sftpValue\":\"IL\"},{\"lgValue\":\"All UR or not selected fields\",\"sftpValue\":\"U\"}]},{\"gradeDefinition\":\"Kindergarten\",\"ageGroup\":\"5-6\",\"gradeCode\":\"0\",\"stageId\":\"78516154-3B50-E411-837D-02DBFC8648CE\",\"hidden\":false,\"ratingMapModels\":[{\"lgValue\":\"Responding Earlier\",\"sftpValue\":\"RE\"},{\"lgValue\":\"Responding Later\",\"sftpValue\":\"RL\"},{\"lgValue\":\"Exploring Earlier\",\"sftpValue\":\"EE\"},{\"lgValue\":\"Exploring Middle\",\"sftpValue\":\"EM\"},{\"lgValue\":\"Exploring Later\",\"sftpValue\":\"EL\"},{\"lgValue\":\"Building Earlier\",\"sftpValue\":\"BE\"},{\"lgValue\":\"Building Middle\",\"sftpValue\":\"BM\"},{\"lgValue\":\"Building Later\",\"sftpValue\":\"BL\"},{\"lgValue\":\"Integrating Earlier\",\"sftpValue\":\"IE\"},{\"lgValue\":\"Integrating middle\",\"sftpValue\":\"IM\"},{\"lgValue\":\"Integrating later\",\"sftpValue\":\"IL\"},{\"lgValue\":\"All UR or not selected fields\",\"sftpValue\":\"U\"}]},{\"gradeDefinition\":\"First grade\",\"ageGroup\":\"6 or older\",\"gradeCode\":\"1\",\"stageId\":\"80516154-3B50-E411-837D-02DBFC8648CE\",\"hidden\":false,\"ratingMapModels\":[{\"lgValue\":\"Responding Earlier\",\"sftpValue\":\"A\"},{\"lgValue\":\"Responding Later\",\"sftpValue\":\"A\"},{\"lgValue\":\"Exploring Earlier\",\"sftpValue\":\"A\"},{\"lgValue\":\"Exploring Middle\",\"sftpValue\":\"EM\"},{\"lgValue\":\"Exploring Later\",\"sftpValue\":\"EL\"},{\"lgValue\":\"Building Earlier\",\"sftpValue\":\"BE\"},{\"lgValue\":\"Building Middle\",\"sftpValue\":\"BM\"},{\"lgValue\":\"Building Later\",\"sftpValue\":\"BL\"},{\"lgValue\":\"Integrating Earlier\",\"sftpValue\":\"IE\"},{\"lgValue\":\"Integrating middle\",\"sftpValue\":\"IM\"},{\"lgValue\":\"Integrating later\",\"sftpValue\":\"IL\"},{\"lgValue\":\"All UR or not selected fields\",\"sftpValue\":\"U\"}]},{\"gradeDefinition\":\"Other\",\"ageGroup\":\"Mixed age group\",\"gradeCode\":\"Other\",\"stageId\":\"81516154-3B50-E411-837D-02DBFC8648CE\",\"hidden\":false,\"ratingMapModels\":[{\"lgValue\":\"Responding Earlier\",\"sftpValue\":\"RE\"},{\"lgValue\":\"Responding Later\",\"sftpValue\":\"RL\"},{\"lgValue\":\"Exploring Earlier\",\"sftpValue\":\"EE\"},{\"lgValue\":\"Exploring Middle\",\"sftpValue\":\"EM\"},{\"lgValue\":\"Exploring Later\",\"sftpValue\":\"EL\"},{\"lgValue\":\"Building Earlier\",\"sftpValue\":\"BE\"},{\"lgValue\":\"Building Middle\",\"sftpValue\":\"BM\"},{\"lgValue\":\"Building Later\",\"sftpValue\":\"BL\"},{\"lgValue\":\"Integrating Earlier\",\"sftpValue\":\"IE\"},{\"lgValue\":\"Integrating middle\",\"sftpValue\":\"IM\"},{\"lgValue\":\"Integrating later\",\"sftpValue\":\"IL\"},{\"lgValue\":\"All UR or not selected fields\",\"sftpValue\":\"U\"}]}]}";
    private final String groupJson = "{\"settingId\":\"4C2D0E14-6255-4B0F-AD1D-A6F6E76E5560\",\"isExportAll\":false," +
            "\"centerModels\":[{\"centerId\":\"1\",\"centerName\":\"//Frame\",\"isSelectAllGroups\":false," +
            "\"groupModels\":[{\"groupId\":\"1\",\"groupName\":\"ITE\",\"groupIsDeleted\":false,\"groupIsInactive\":false,\"groupIsOutside\":false}," +
            "{\"groupId\":\"2\",\"groupName\":\"KE\",\"groupIsDeleted\":false,\"groupIsInactive\":false,\"groupIsOutside\":false}," +
            "{\"groupId\":\"3\",\"groupName\":\"PSE\",\"groupIsDeleted\":false,\"groupIsInactive\":false,\"groupIsOutside\":false}]}," +
            "{\"centerId\":\"2\",\"centerName\":\"AC Prep Elem\",\"isSelectAllGroups\":false," +
            "\"groupModels\":[{\"groupId\":\"4\",\"groupName\":\"Brown, Michelle\",\"groupIsDeleted\":false,\"groupIsInactive\":false,\"groupIsOutside\":false}]}]," +
            "\"exportGradeStageIds\":[\"1\",\"77516154-3B50-E411-837D-02DBFC8648CE\",\"78516154-3B50-E411-837D-02DBFC8648CE\",\"72516154-3B50-E411-837D-02DBFC8648CE\",\"81516154-3B50-E411-837D-02DBFC8648CE\",\"80516154-3B50-E411-837D-02DBFC8648CE\"]}";


    private final String exportAllGroupJson = "{\"settingId\":\"4C2D0E14-6255-4B0F-AD1D-A6F6E76E5560\",\"isExportAll\":true," +
            "\"centerModels\":[{\"centerId\":\"1\",\"centerName\":\"//Frame\",\"isSelectAllGroups\":false," +
            "\"groupModels\":[{\"groupId\":\"1\",\"groupName\":\"ITE\",\"groupIsDeleted\":false,\"groupIsInactive\":false,\"groupIsOutside\":false}," +
            "{\"groupId\":\"2\",\"groupName\":\"KE\",\"groupIsDeleted\":false,\"groupIsInactive\":false,\"groupIsOutside\":false}," +
            "{\"groupId\":\"3\",\"groupName\":\"PSE\",\"groupIsDeleted\":false,\"groupIsInactive\":false,\"groupIsOutside\":false}]}," +
            "{\"centerId\":\"2\",\"centerName\":\"AC Prep Elem\",\"isSelectAllGroups\":false," +
            "\"groupModels\":[{\"groupId\":\"4\",\"groupName\":\"Brown, Michelle\",\"groupIsDeleted\":false,\"groupIsInactive\":false,\"groupIsOutside\":false}]}]," +
            "\"exportGradeStageIds\":[\"1\",\"77516154-3B50-E411-837D-02DBFC8648CE\",\"78516154-3B50-E411-837D-02DBFC8648CE\",\"72516154-3B50-E411-837D-02DBFC8648CE\",\"81516154-3B50-E411-837D-02DBFC8648CE\",\"80516154-3B50-E411-837D-02DBFC8648CE\"]}";

    /**
     * 查询学校班级列表
     */
    @Test
    public void testGetCenterGroups() {
        //创建测试数据
        String userId = "1";
        String agencyId = "2";
        List<String> groupStageIds = new ArrayList<>();
        groupStageIds.add("1");
        groupStageIds.add("2");

//        authUserDetails
        UserEntity user = new UserEntity();
        user.setRole(UserRole.AGENCY_ADMIN.toString());

        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        List<CenterGroupModel> centerGroupList = new ArrayList<>();
        CenterGroupModel centerGroup1 = new CenterGroupModel();
        centerGroup1.setGroupId("1");
        centerGroup1.setGroupName("group1");
        centerGroup1.setCenterId("1");
        centerGroup1.setCenterName("center1");

        CenterGroupModel centerGroup2 = new CenterGroupModel();

        centerGroup2.setCenterId("1");
        centerGroup2.setCenterName("center1");

        centerGroupList.add(centerGroup1);
        centerGroupList.add(centerGroup2);
        List<CenterModel> centerModelList = new ArrayList<>();
        CenterModel centerModel = new CenterModel();
        centerModel.setId("1");
        centerModel.setName("center1");

        centerModelList.add(centerModel);
        ExportSftpGetCenterGroupResponse expectedResponse = new ExportSftpGetCenterGroupResponse();
        expectedResponse.setAgencyId("2");
        expectedResponse.setModels(centerModelList);
        //设置 Mock 对象的行为
        when(userProvider.getUser(userId)).thenReturn(user);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        when(userDao.getCenterGroupByUserIdWithGroups(agencyModel.getId(), groupStageIds)).thenReturn(centerGroupList);
        when(userProvider.convertCenterGroup(user.getRole(), centerGroupList, false)).thenReturn(centerModelList);

        //调用被测试的方法
        ExportSftpGetCenterGroupResponse groups = exportSftpService.getCenterGroups(userId, groupStageIds);
        Assertions.assertNotNull(groups);
        //验证结果是否符合预期
        Assertions.assertEquals(expectedResponse, groups);
        verify(userProvider, Mockito.times(1)).getAgencyByUserId(userId);
        verify(userDao, Mockito.times(1)).getCenterGroupByUserIdWithGroups(agencyId, groupStageIds);
        verify(userProvider, Mockito.times(1)).convertCenterGroup(user.getRole(), centerGroupList, false);
    }

    /**
     * 获取 SFTP 导出的可映射字段信息
     */
    @Test
    public void testGetAlignmentFields() {
        String userId = "1";
        AgencyModel agency = new AgencyModel();
        agency.setId("1");

        List<ContentsGroupStage> groupStages = new ArrayList<>();
        ContentsGroupStage stage1 = new ContentsGroupStage();
        stage1.setGradeName("Infant/toddler");
        groupStages.add(stage1);

        ContentsGroupStage stage2 = new ContentsGroupStage();
        stage2.setGradeName("Prekindergarten");
        groupStages.add(stage2);

        ContentsGroupStage stage3 = new ContentsGroupStage();
        stage3.setGradeName("Transitional Kindergarten");
        groupStages.add(stage3);

        ContentsGroupStage stage4 = new ContentsGroupStage();
        stage4.setGradeName("Kindergarten");
        groupStages.add(stage4);

        ContentsGroupStage stage5 = new ContentsGroupStage();
        stage5.setGradeName("First grade");
        groupStages.add(stage5);

        ContentsGroupStage stage6 = new ContentsGroupStage();
        stage6.setGradeName("Other");
        groupStages.add(stage6);

        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        when(contentsGroupStageDao.list()).thenReturn(groupStages);


        exportSftpService.getColumnMaps(userId);

        verify(remoteProvider, times(1)).callExportSftpServer();
    }

    @Test
    public void testGetCurrentYear() {
        String currentSchoolYear = exportSftpService.getCurrentSchoolYear();

        Assert.assertEquals(2, currentSchoolYear.split("-").length);
    }

    @Test
    public void getSftpAccountInfo() {
        String userId = "1";
        String tenantId = "1";
        String sftpHost = "123";
        String sftpPort = "22";
        String username = "EX_" + tenantId.replace("-", "");
        String password = "123456";

        AgencyModel mockAgency = new AgencyModel();
        mockAgency.setId(tenantId);


        ExportSftpAccountInfoResponse expectedResponse = new ExportSftpAccountInfoResponse();
        expectedResponse.setHost(sftpHost);
        expectedResponse.setPort(sftpPort);
        expectedResponse.setUsername(username);
        expectedResponse.setPassword(password);

        AgenciesSftpuser mockSftpUser = new AgenciesSftpuser();
        mockSftpUser.setUserName(username);
        mockSftpUser.setPassword(password);

        when(userProvider.getAgencyByUserId(userId)).thenReturn(mockAgency);
        when(agenciesSftpuserDao.getExportUserByUsername(username, tenantId)).thenReturn(mockSftpUser);

        // Act
        ExportSftpAccountInfoResponse actualResponse = exportSftpService.getSftpAccountInfo(userId);

        Assert.assertEquals(expectedResponse.getUsername(), actualResponse.getUsername());
        Assert.assertEquals(expectedResponse.getPassword(), actualResponse.getPassword());
        verify(userProvider, times(1)).getAgencyByUserId(userId);
        verify(agenciesSftpuserDao, times(1)).getExportUserByUsername(username, tenantId);
    }

    @Test
    public void testExport() {
        // Arrange
        String userId = "1";
        String settingId = "1";
        String recordId = "1";
        String tenantId = "1";

        AgencyModel mockAgency = new AgencyModel();
        mockAgency.setId(tenantId);


        when(userProvider.getAgencyByUserId(userId)).thenReturn(mockAgency);

        // Act
        IdResponse actualResponse = exportSftpService.export(userId, settingId);

        verify(userProvider, atLeastOnce()).getAgencyByUserId(userId);
        Assert.assertNotNull(actualResponse.getId());
    }

    @Test
    public void testGetExportStatus() {
        String recordId = "1";

        ExportSftpRecord mockRecord = new ExportSftpRecord();
        mockRecord.setExportStatus(ExportSftpExportStatus.SUCCESS.toString());
        mockRecord.setSampleJson("{\"centerId\":\"144B73C2-5ABB-4932-B61F-76B2CA75DE2E\",\"centerName\":\"Assessment 02\",\"groupGrade\":\"Infant/toddler\",\"stageId\":\"72516154-3B50-E411-837D-02DBFC8648CE\",\"groupIds\":[\"697EEA1B-BABC-40AA-A06C-49C870128BF1\"],\"frameworkId\":\"********-BDCE-E411-AF66-02C72B94B99B\",\"frameworkName\":\"DRDP2015-PRESCHOOL Comprehensive view\",\"tableHeader\":[\"CI\",\"GC\",\"C\",\"M\",\"M1\",\"M2\",\"M3\",\"M4\"],\"tableData\":[{\"C\":\"E100156\",\"M1\":\"Ff\",\"M2\":\"\",\"CI\":\"G100004\",\"M3\":\"\",\"M4\":\"\",\"GC\":\"-3\",\"M\":\"ATL-REG3\"}]}");
        mockRecord.setSettingId("1");

        ExportSftpSetting mockSetting = new ExportSftpSetting();
        mockSetting.setMapJson("{\"columnMapModels\":[{\"propertyKey\":\"CLASS_ID\",\"lgProperty\":\"Class ID\",\"sftpProperty\":\"Class ID\",\"hidden\":false,\"inOption\":true,\"alwaysShow\":true,\"options\":[{\"key\":\"CLASS_ID\",\"label\":\"Class ID\",\"value\":\"Class ID\"},{\"key\":\"CLASS_NAME\",\"label\":\"Class Name\",\"value\":\"Class Name\"}]},{\"propertyKey\":\"GRADE_CODE\",\"lgProperty\":\"Grade Code\",\"sftpProperty\":\"Grade Code\",\"hidden\":false,\"inOption\":true,\"alwaysShow\":true,\"options\":[{\"key\":\"GRADE_CODE\",\"label\":\"Grade Code\",\"value\":\"Grade Code\"},{\"key\":\"GRADE\",\"label\":\"Grade\",\"value\":\"Grade\"}]},{\"propertyKey\":\"CHILD_ID\",\"lgProperty\":\"Child ID\",\"sftpProperty\":\"Child ID\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":true},{\"propertyKey\":\"MEASURE\",\"lgProperty\":\"Measure\",\"sftpProperty\":\"测评点\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":true},{\"propertyKey\":\"M1\",\"lgProperty\":\"Fall 2023/Time1\",\"sftpProperty\":\"F\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":true},{\"propertyKey\":\"M2\",\"lgProperty\":\"Winter 2023/Time2\",\"sftpProperty\":\"W\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":true},{\"propertyKey\":\"M3\",\"lgProperty\":\"Spring 2024/Time3\",\"sftpProperty\":\"SP\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":true},{\"propertyKey\":\"M4\",\"lgProperty\":\"Summer 2024/Time4\",\"sftpProperty\":\"SU\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":true},{\"lgProperty\":\"Child Name\",\"sftpProperty\":\"Child Name\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":false},{\"lgProperty\":\"ELD\",\"sftpProperty\":\"ELD\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":false},{\"lgProperty\":\"IEP/IFSP\",\"sftpProperty\":\"IEP\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":false},{\"lgProperty\":\"Class Name\",\"sftpProperty\":\"Class Name\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":false},{\"lgProperty\":\"Grade\",\"sftpProperty\":\"Grade\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":false},{\"lgProperty\":\"MT\",\"sftpProperty\":\"MT\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":false},{\"lgProperty\":\"Agency Name\",\"sftpProperty\":\"Agency Name\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":false},{\"lgProperty\":\"Site Name\",\"sftpProperty\":\"Site Name\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":false},{\"lgProperty\":\"Teacher Name\",\"sftpProperty\":\"Teacher Name\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":false},{\"lgProperty\":\"Teacher ID\",\"sftpProperty\":\"Teacher ID\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":false},{\"lgProperty\":\"Birthday\",\"sftpProperty\":\"Birthday\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":false},{\"lgProperty\":\"Gender\",\"sftpProperty\":\"Gender\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":false},{\"lgProperty\":\"Entry Date\",\"sftpProperty\":\"Entry Date\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":false},{\"lgProperty\":\"Statewide Student Identifier\",\"sftpProperty\":\"Statewide Student IdentifierStatewide Student IdentifierStatewide Student IdentifierStatewide Student Identifier\",\"hidden\":false,\"inOption\":false,\"alwaysShow\":false}],\"gradeRatingMapModels\":[{\"gradeDefinition\":\"Infant/toddler\",\"ageGroup\":\"0-3\",\"gradeCode\":\"-3\",\"stageId\":\"72516154-3B50-E411-837D-02DBFC8648CE\",\"hidden\":false,\"ratingMapModels\":[{\"lgValue\":\"Responding Earlier\",\"sftpValue\":\"RE\"},{\"lgValue\":\"Responding Later\",\"sftpValue\":\"RL\"},{\"lgValue\":\"Exploring Earlier\",\"sftpValue\":\"EE\"},{\"lgValue\":\"Exploring Middle\",\"sftpValue\":\"EM\"},{\"lgValue\":\"Exploring Later\",\"sftpValue\":\"EL\"},{\"lgValue\":\"Building Earlier\",\"sftpValue\":\"BE\"},{\"lgValue\":\"Building Middle\",\"sftpValue\":\"BM\"},{\"lgValue\":\"Building Later\",\"sftpValue\":\"BL\"},{\"lgValue\":\"Integrating Earlier\",\"sftpValue\":\"IE\"},{\"lgValue\":\"Integrating middle\",\"sftpValue\":\"IM\"},{\"lgValue\":\"Integrating later\",\"sftpValue\":\"IL\"},{\"lgValue\":\"All UR or not selected fields\",\"sftpValue\":\"U\"}]},{\"gradeDefinition\":\"Prekindergarten\",\"ageGroup\":\"3-4\",\"gradeCode\":\"-2\",\"stageId\":\"75516154-3B50-E411-837D-02DBFC8648CE\",\"hidden\":false,\"ratingMapModels\":[{\"lgValue\":\"Responding Earlier\",\"sftpValue\":\"RE\"},{\"lgValue\":\"Responding Later\",\"sftpValue\":\"RL\"},{\"lgValue\":\"Exploring Earlier\",\"sftpValue\":\"EE\"},{\"lgValue\":\"Exploring Middle\",\"sftpValue\":\"EM\"},{\"lgValue\":\"Exploring Later\",\"sftpValue\":\"EL\"},{\"lgValue\":\"Building Earlier\",\"sftpValue\":\"BE\"},{\"lgValue\":\"Building Middle\",\"sftpValue\":\"BM\"},{\"lgValue\":\"Building Later\",\"sftpValue\":\"BL\"},{\"lgValue\":\"Integrating Earlier\",\"sftpValue\":\"IE\"},{\"lgValue\":\"Integrating middle\",\"sftpValue\":\"IM\"},{\"lgValue\":\"Integrating later\",\"sftpValue\":\"IL\"},{\"lgValue\":\"All UR or not selected fields\",\"sftpValue\":\"U\"}]},{\"gradeDefinition\":\"Transitional Kindergarten\",\"ageGroup\":\"4-5\",\"gradeCode\":\"-1\",\"stageId\":\"77516154-3B50-E411-837D-02DBFC8648CE\",\"hidden\":false,\"ratingMapModels\":[{\"lgValue\":\"Responding Earlier\",\"sftpValue\":\"RE\"},{\"lgValue\":\"Responding Later\",\"sftpValue\":\"RL\"},{\"lgValue\":\"Exploring Earlier\",\"sftpValue\":\"EE\"},{\"lgValue\":\"Exploring Middle\",\"sftpValue\":\"EM\"},{\"lgValue\":\"Exploring Later\",\"sftpValue\":\"EL\"},{\"lgValue\":\"Building Earlier\",\"sftpValue\":\"BE\"},{\"lgValue\":\"Building Middle\",\"sftpValue\":\"BM\"},{\"lgValue\":\"Building Later\",\"sftpValue\":\"BL\"},{\"lgValue\":\"Integrating Earlier\",\"sftpValue\":\"IE\"},{\"lgValue\":\"Integrating middle\",\"sftpValue\":\"IM\"},{\"lgValue\":\"Integrating later\",\"sftpValue\":\"IL\"},{\"lgValue\":\"All UR or not selected fields\",\"sftpValue\":\"U\"}]},{\"gradeDefinition\":\"Kindergarten\",\"ageGroup\":\"5-6\",\"gradeCode\":\"0\",\"stageId\":\"78516154-3B50-E411-837D-02DBFC8648CE\",\"hidden\":false,\"ratingMapModels\":[{\"lgValue\":\"Responding Earlier\",\"sftpValue\":\"RE\"},{\"lgValue\":\"Responding Later\",\"sftpValue\":\"RL\"},{\"lgValue\":\"Exploring Earlier\",\"sftpValue\":\"EE\"},{\"lgValue\":\"Exploring Middle\",\"sftpValue\":\"EM\"},{\"lgValue\":\"Exploring Later\",\"sftpValue\":\"EL\"},{\"lgValue\":\"Building Earlier\",\"sftpValue\":\"BE\"},{\"lgValue\":\"Building Middle\",\"sftpValue\":\"BM\"},{\"lgValue\":\"Building Later\",\"sftpValue\":\"BL\"},{\"lgValue\":\"Integrating Earlier\",\"sftpValue\":\"IE\"},{\"lgValue\":\"Integrating middle\",\"sftpValue\":\"IM\"},{\"lgValue\":\"Integrating later\",\"sftpValue\":\"IL\"},{\"lgValue\":\"All UR or not selected fields\",\"sftpValue\":\"U\"}]},{\"gradeDefinition\":\"First grade\",\"ageGroup\":\"6 or older\",\"gradeCode\":\"1\",\"stageId\":\"80516154-3B50-E411-837D-02DBFC8648CE\",\"hidden\":false,\"ratingMapModels\":[{\"lgValue\":\"Responding Earlier\",\"sftpValue\":\"RE\"},{\"lgValue\":\"Responding Later\",\"sftpValue\":\"RL\"},{\"lgValue\":\"Exploring Earlier\",\"sftpValue\":\"EE\"},{\"lgValue\":\"Exploring Middle\",\"sftpValue\":\"EM\"},{\"lgValue\":\"Exploring Later\",\"sftpValue\":\"EL\"},{\"lgValue\":\"Building Earlier\",\"sftpValue\":\"BE\"},{\"lgValue\":\"Building Middle\",\"sftpValue\":\"BM\"},{\"lgValue\":\"Building Later\",\"sftpValue\":\"BL\"},{\"lgValue\":\"Integrating Earlier\",\"sftpValue\":\"IE\"},{\"lgValue\":\"Integrating middle\",\"sftpValue\":\"IM\"},{\"lgValue\":\"Integrating later\",\"sftpValue\":\"IL\"},{\"lgValue\":\"All UR or not selected fields\",\"sftpValue\":\"U\"}]},{\"gradeDefinition\":\"Other\",\"ageGroup\":\"Mixed age group\",\"gradeCode\":\"Other\",\"stageId\":\"81516154-3B50-E411-837D-02DBFC8648CE\",\"hidden\":false,\"ratingMapModels\":[{\"lgValue\":\"Responding Earlier\",\"sftpValue\":\"RE\"},{\"lgValue\":\"Responding Later\",\"sftpValue\":\"RL\"},{\"lgValue\":\"Exploring Earlier\",\"sftpValue\":\"EE\"},{\"lgValue\":\"Exploring Middle\",\"sftpValue\":\"EM\"},{\"lgValue\":\"Exploring Later\",\"sftpValue\":\"EL\"},{\"lgValue\":\"Building Earlier\",\"sftpValue\":\"BE\"},{\"lgValue\":\"Building Middle\",\"sftpValue\":\"BM\"},{\"lgValue\":\"Building Later\",\"sftpValue\":\"BL\"},{\"lgValue\":\"Integrating Earlier\",\"sftpValue\":\"IE\"},{\"lgValue\":\"Integrating middle\",\"sftpValue\":\"IM\"},{\"lgValue\":\"Integrating later\",\"sftpValue\":\"IL\"},{\"lgValue\":\"All UR or not selected fields\",\"sftpValue\":\"U\"}]}]}");


        when(exportSftpRecordDao.getById(recordId))
                .thenReturn(mockRecord);
        when(exportSftpSettingDao.getById(mockRecord.getSettingId()))
                .thenReturn(mockSetting);

        // Act
        GetExportStatusResponse actualResponse = exportSftpService.getExportStatus(recordId);

        Assert.assertEquals(ExportSftpExportStatus.SUCCESS.toString(), actualResponse.getExportStatus());
        verify(exportSftpRecordDao, times(1)).getById(recordId);
        verify(exportSftpSettingDao, times(1)).getById(mockRecord.getSettingId());
    }

    @Test
    public void testGetExportSftpOpen() {
        // 创建模拟数据
        String userId = "1";
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("1");

        List<AgencyMetaDataEntity> agencyMetas = new ArrayList<>();

        AgencyMetaDataEntity entity1 = new AgencyMetaDataEntity();
        entity1.setMetaKey(AgencyMetaKey.PORTFOLIO_OPEN.toString());
        entity1.setMetaValue("true");

        agencyMetas.add(entity1);

        AgencyMetaDataEntity entity2 = new AgencyMetaDataEntity();
        entity2.setMetaKey(AgencyMetaKey.EXPORT_RATINGS_TO_SIS.toString());
        entity2.setMetaValue("true");

        agencyMetas.add(entity2);

        List<String> frameworksIds = Lists.newArrayList("1", "2");

        // 设置模拟对象行为
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        when(agencyDao.getMetas(eq(agencyModel.getId()), anyList())).thenReturn(agencyMetas);
        when(enrollmentPeriodDao.getFrameworkIdsByTenantId(agencyModel.getId())).thenReturn(frameworksIds);
        when(portfolioProvider.hasScoreTemplate(anyString())).thenReturn(true);
        when(enrollmentPeriodDao.getCountBySchoolYearAndFrameworkIds(anyString(), anyList(), eq(agencyModel.getId()))).thenReturn(1L);
        when(periodsGroupDao.getGroupPeriodGroupByAgencyYear(eq(agencyModel.getId()), anyString())).thenReturn(Collections.singletonList(new PeriodsGroupPeriodGroupEntity()));

        // 调用被测试方法
        GetExportSftpOpenResponse response = exportSftpService.getExportSftpOpen(userId);

        // 验证结果
        Assert.assertTrue(response.getShow());
        Assert.assertTrue(response.getEnable());
        verify(userProvider).getAgencyByUserId(userId);
        verify(agencyDao).getMetas(eq(agencyModel.getId()), anyList());
        verify(portfolioProvider, times(2)).hasScoreTemplate(anyString());
        verify(enrollmentPeriodDao).getCountBySchoolYearAndFrameworkIds(anyString(), anyList(), eq(agencyModel.getId()));
        verify(periodsGroupDao).getGroupPeriodGroupByAgencyYear(eq(agencyModel.getId()), anyString());
    }

    @Test
    public void testGetAgencyGradeStages() {
        // 创建模拟数据
        String userId = "1";
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("2");
        List<GroupEntity> groups = Lists.newArrayList();
        GroupEntity groupEntity = new GroupEntity();
        GroupStageEntity stage = new GroupStageEntity();
        stage.setId("1");
        groupEntity.setStage(stage);
        groups.add(groupEntity);


        // 设置模拟对象行为
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        when(groupDao.getGroupByAgency(agencyModel.getId())).thenReturn(groups);

        // 调用被测试方法
        GetAgencyGradeStagesResponse response = exportSftpService.getAgencyGradeStages(userId);

        // 验证结果
        Assert.assertNotNull(response.getStageIds());

        // 进一步验证模拟对象的方法调用
        verify(userProvider).getAgencyByUserId(userId);
        verify(groupDao).getGroupByAgency(agencyModel.getId());
    }

    @Test
    public void testGetExportDataNonCenterModel() {
        String userId = "1";
        String tenantId = "2";
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("2");

        ExportSftpSettingResponse settingResponse = new ExportSftpSettingResponse();
        ExportSftpSettingMapModel mapModel = JsonUtil.fromJson(mapJson, ExportSftpSettingMapModel.class);
        settingResponse.setMapModel(mapModel);

        ExportSftpSetting exportSftpSetting = new ExportSftpSetting();
        exportSftpSetting.setMapJson(mapJson);
        exportSftpSetting.setTenantId(tenantId);

        AgenciesSftpuser sftpUser = new AgenciesSftpuser();


        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        when(exportSftpSettingDao.getSettingByTenantId(agencyModel.getId())).thenReturn(exportSftpSetting);
        when(agenciesSftpuserDao.getExportUserByUsername(anyString(), eq(tenantId))).thenReturn(sftpUser);


        ExportSftpExportDataResponse actualResponse = exportSftpService.getExportData(userId, null, ExportSftpExportType.IMMEDIATELY);

        Assert.assertNotNull(actualResponse);
    }

    @Test
    public void testGetExportDataNotNonCenterModel() {
        String userId = "1";
        String tenantId = "2";
        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole("Teacher");
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("2");

        ExportSftpSettingResponse settingResponse = new ExportSftpSettingResponse();
        ExportSftpSettingMapModel mapModel = JsonUtil.fromJson(mapJson, ExportSftpSettingMapModel.class);
//        settingResponse.setMapModel(mapModel);
        ExportSftpSettingCenterModel centerModel = JsonUtil.fromJson(groupJson, ExportSftpSettingCenterModel.class);
        settingResponse.setCenterModel(centerModel);

        ExportSftpGetCenterGroupResponse centerGroups = new ExportSftpGetCenterGroupResponse();
        List<CenterModel> models = new ArrayList<>();
        CenterModel model = new CenterModel();
        model.setId("1");
        model.setName("CenterName");
        List<GroupModel> groupModels = new ArrayList<>();
        GroupModel gModel = new GroupModel();
        groupModels.add(gModel);

        model.setGroups(groupModels);

        centerGroups.setModels(models);

        ExportSftpSetting exportSftpSetting = new ExportSftpSetting();
        exportSftpSetting.setGroupJson(groupJson);
        exportSftpSetting.setMapJson(mapJson);
        exportSftpSetting.setTenantId(tenantId);
        exportSftpSetting.setExportStageIds("75516154-3B50-E411-837D-02DBFC8648CE,77516154-3B50-E411-837D-02DBFC8648CE,78516154-3B50-E411-837D-02DBFC8648CE,72516154-3B50-E411-837D-02DBFC8648CE,81516154-3B50-E411-837D-02DBFC8648CE,80516154-3B50-E411-837D-02DBFC8648CE");

        AgenciesSftpuser sftpUser = new AgenciesSftpuser();

        List<String> lockFrameworkIds = new ArrayList<>();

        List<CenterEntity> byCenterIds = new ArrayList<>();
        CenterEntity centerEntity1 = new CenterEntity();
        centerEntity1.setId("1");
        centerEntity1.setName("Center1");
        byCenterIds.add(centerEntity1);

        List<EnrollmentModel> allEnrollmentModelLists = new ArrayList<>();
        EnrollmentModel enrollmentModel1 = new EnrollmentModel();
        enrollmentModel1.setId("1");
        enrollmentModel1.setGroupId("1");
        allEnrollmentModelLists.add(enrollmentModel1);

        List<EnrollmentEntity> enrollmentEntityList = new ArrayList<>();
        EnrollmentEntity enrollmentEntity1 = new EnrollmentEntity();
        enrollmentEntity1.setId("1");
        GroupEntity groupEntity1 = new GroupEntity();
        groupEntity1.setId("1");
        enrollmentEntity1.setGroup(groupEntity1);

        enrollmentEntityList.add(enrollmentEntity1);

        List<EnrollmentMetaDataEntity> enrollmentMetaDataLists = new ArrayList<>();
        EnrollmentMetaDataEntity enrollmentMetaDataEntity1 = new EnrollmentMetaDataEntity();
        enrollmentMetaDataEntity1.setId("1");
        enrollmentMetaDataEntity1.setEnrollment(enrollmentEntity1);
        enrollmentMetaDataEntity1.setMetaKey("External ID");
        enrollmentMetaDataEntity1.setMetaValue("EXTERNAL_ID");
        enrollmentMetaDataEntity1.setChildId("1");

        enrollmentMetaDataLists.add(enrollmentMetaDataEntity1);


        EnrollmentMetaDataEntity enrollmentMetaDataEntity2 = new EnrollmentMetaDataEntity();
        enrollmentMetaDataEntity2.setId("2");
        enrollmentMetaDataEntity2.setEnrollment(enrollmentEntity1);
        enrollmentMetaDataEntity2.setMetaKey("IEP/IFSP");
        enrollmentMetaDataEntity2.setMetaValue("1");
        enrollmentMetaDataEntity2.setChildId("1");

        enrollmentMetaDataLists.add(enrollmentMetaDataEntity2);

        List<EnrollmentPeriodEntity> enrollmentPeriodList = new ArrayList<>();
        EnrollmentPeriodEntity enrollmentPeriodEntity1 = new EnrollmentPeriodEntity();
        enrollmentPeriodEntity1.setEnrollmentId("1");
        enrollmentPeriodEntity1.setDomainId("1");
        enrollmentPeriodEntity1.setAlias("1");
        enrollmentPeriodEntity1.setFromAtLocal(new Date());
        enrollmentPeriodEntity1.setToAtLocal(new Date());
        enrollmentPeriodList.add(enrollmentPeriodEntity1);

        List<ContentsGroupStage> exportGradeStageIdList = new ArrayList<>();
        ContentsGroupStage contentsGroupStage1 = new ContentsGroupStage();
        contentsGroupStage1.setId("1");
        contentsGroupStage1.setGradeName("IT");

        exportGradeStageIdList.add(contentsGroupStage1);

        List<com.learninggenie.common.data.entity.contents.GroupEntity> allGroupList = new ArrayList<>();
        com.learninggenie.common.data.entity.contents.GroupEntity groupEntity = new com.learninggenie.common.data.entity.contents.GroupEntity();
        groupEntity.setCenterId("1");
        groupEntity.setId("1");
        groupEntity.setStageId("1");

        allGroupList.add(groupEntity);

        List<FrameworkViewEntity> frameworkViewList = new ArrayList<>();
        FrameworkViewEntity frameworkViewEntity = new FrameworkViewEntity();
        frameworkViewEntity.setFrameworkId("1");
        frameworkViewEntity.setMeasureId("1");
        frameworkViewEntity.setId("1");

        frameworkViewList.add(frameworkViewEntity);

        List<MeasureEntity> frameworkMeasureList = new ArrayList<>();
        MeasureEntity measureEntity = new MeasureEntity();
        measureEntity.setId("1");
        measureEntity.setName("LTD-1");
        measureEntity.setSortIndex(1);
        measureEntity.setIsNode("0");
        frameworkMeasureList.add(measureEntity);

        ScoreTemplateEntity scoreTemplateEntry = new ScoreTemplateEntity();
        scoreTemplateEntry.setDomainLevelsJson("[{\"domainId\":\"measureId\",\"measure\":\"LLD8\",\"measureName\":\"Phonological Awareness\",\"levels\":[{\"id\":\"77F413F8-1EC3-4B70-B6E6-2F153CD936EE\",\"name\":\" Exploring Middle\",\"type\":\"radio\",\"sortIndex\":\"3\",\"value\":\"4\",\"tip\":\"Attends to sounds or elements of language\"},{\"id\":\"976C1402-59B1-4B21-8F16-EAB81A329989\",\"name\":\" Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Demonstrates awareness of variations in sounds\"},{\"id\":\"CFC998E8-3D19-44DB-BE98-664395648330\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Engages actively in play with sounds in words or rhymes, or sings simple songs, or\\nrepeats simple nursery rhymes\"},{\"id\":\"405BA949-F8FF-4CB0-A384-766174446ADF\",\"name\":\"Building Middle\",\"type\":\"radio\",\"sortIndex\":\"6\",\"value\":\"7\",\"tip\":\"Demonstrates awareness of larger units of language (e.g., words, syllables)\"},{\"id\":\"790D37D5-FF13-4DCD-84A3-5847C81323B2\",\"name\":\"Building Later\",\"type\":\"radio\",\"sortIndex\":\"7\",\"value\":\"8\",\"tip\":\"Blends larger units of language (e.g., compound words and syllables) with or without the support of pictures or objects;and segments larger units of language (e.g.\\ncompound words and syllables) with or without the support of pictures or objects\"},{\"id\":\"B16025FA-6DEF-47B4-808E-FAD1C0748308\",\"name\":\"UR (Unable to Rate)\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"},{\"id\":\"63BBC32F-686E-4859-81C5-463C366CD4F7\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"12\",\"value\":\"e\"},{\"id\":\"E72D58A8-167D-4068-8D25-216A174AAEE0\",\"name\":\"Integrating Earlier\",\"type\":\"radio\",\"sortIndex\":\"8\",\"value\":\"9\",\"tip\":\"Segments larger units of language (e.g., compound words and syllables) with or without the support of pictures or objectsBlends smaller units of language (e.g., onsetsand rimes), with or without the support of pictures or objects; and Segments smaller units of language (e.g., onsets and rimes), with or without the support of pictures or objects\"},{\"id\":\"F2A37092-00A9-4592-A182-625B59DEA07B\",\"name\":\"Not yet\",\"type\":\"radio\",\"sortIndex\":\"21\",\"value\":\"n\"}]}]");

        List<UsersMetaDataEntity> teacherSourceIds = new ArrayList<>();
        UsersMetaDataEntity usersMetaDataEntity1 = new UsersMetaDataEntity();
        usersMetaDataEntity1.setId("1");
        usersMetaDataEntity1.setMetaKey("SOURCE_ID");
        usersMetaDataEntity1.setMetaValue("1");
        usersMetaDataEntity1.setUserId("1");
        teacherSourceIds.add(usersMetaDataEntity1);

        List<SnapshotEntity> snapshotEntities = new ArrayList<>();
        SnapshotEntity snapshotEntity1 = new SnapshotEntity();
        snapshotEntity1.setGroupId("1");
        snapshotEntity1.setEnrollmentId("1");
        snapshotEntity1.setFrameworkId("1");
        snapshotEntity1.setPeriodAlias("2023 Fall");
//        snapshotEntities.add(snapshotEntity1);

        List<StudentScoreModel> enrollmentIdMeasureIdScoreMap = new ArrayList<>();
        StudentScoreModel scoreModel1 = new StudentScoreModel();
        scoreModel1.setFrameworkId("1");
        scoreModel1.setStudentId("1");
        scoreModel1.setDomainId("1");
        scoreModel1.setAlias("2023-2024 Fall");
        scoreModel1.setGroupId("1");
        enrollmentIdMeasureIdScoreMap.add(scoreModel1);


        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        when(exportSftpSettingDao.getSettingByTenantId(agencyModel.getId())).thenReturn(exportSftpSetting);
        when(agenciesSftpuserDao.getExportUserByUsername(anyString(), eq(tenantId))).thenReturn(sftpUser);
        when(userProvider.getUser(userId)).thenReturn(userEntity);
        when(enrollmentSnapshotDao.getDomainIdsByGroupIds(anyString())).thenReturn(lockFrameworkIds);
        when(centerDao.getCentersByCenterIds(anyList())).thenReturn(byCenterIds);
        when(studentDao.getAllNotInactiveChildrenByIds(anyList())).thenReturn(allEnrollmentModelLists);
        when(studentDao.getChildrenByGroupIds(anyList())).thenReturn(enrollmentEntityList);
        when(studentDao.getChildrenAllMeta(anyList())).thenReturn(enrollmentMetaDataLists);
        when(enrollmentPeriodDao.selectByEnrollmentIdsAndAliasLike(anyList(), anyString())).thenReturn(enrollmentPeriodList);
        when(contentsGroupStageDao.listByIds(anyList())).thenReturn(exportGradeStageIdList);
        when(mpGroupDao.listByIds(anyList())).thenReturn(allGroupList);
        when(ratingService.isDRDP(anyString())).thenReturn(true);
        when(frameworkViewDao.listByFrameworkIdIn(anyList())).thenReturn(frameworkViewList);
        when(measureDao.getByIdIn(anyList())).thenReturn(frameworkMeasureList);
        when(portfolioService.getScoreTemplate(anyString())).thenReturn(scoreTemplateEntry);
        when(userMetaDao.getMetasByUserList(anyString(), anyList())).thenReturn(teacherSourceIds);
        when(enrollmentSnapshotDao.listByEnrollmentIdsAndLikePeriod(anyList(), anyString())).thenReturn(snapshotEntities);


        try (MockedStatic<LGSnapshot.StudentSnapshot> studentSnapshot = mockStatic(LGSnapshot.StudentSnapshot.class);
             MockedStatic<RateUtil> rateUtil = mockStatic(RateUtil.class);) {
            studentSnapshot.when(() -> LGSnapshot.StudentSnapshot.parseFrom(any(byte[].class))).thenReturn(null);

            rateUtil.when(() -> RateUtil.isIEP(any(LGSnapshot.StudentSnapshot.class))).thenReturn(false);
            rateUtil.when(() -> RateUtil.isPSEFramework(anyString())).thenReturn(false);
            rateUtil.when(() -> RateUtil.isITEFramework(anyString())).thenReturn(false);
        }

        when(scoreService.getStudentScoreByEnrollmentIds(anyList(), anyString())).thenReturn(enrollmentIdMeasureIdScoreMap);

        List<CenterGroupChildCount> dashboardDataList = new ArrayList<>();
        CenterGroupChildCount centerGroupChildCount = new CenterGroupChildCount();
        centerGroupChildCount.setCenterId("1");
        centerGroupChildCount.setGroupId("1");
        centerGroupChildCount.setEnrollmentId("1");
        centerGroupChildCount.setCenterName("Center1");
        centerGroupChildCount.setGroupName("Group1");
        centerGroupChildCount.setDomainId("1");
        centerGroupChildCount.setAlias("2023-2024 Fall");

        dashboardDataList.add(centerGroupChildCount);

        when(dashboardDao.getCenterGroupChildByCenterIds(anyList(), anyList(), anyString())).thenReturn(dashboardDataList);
        RatingStatisticModel ratingStatisticModel = new RatingStatisticModel();
        ratingStatisticModel.setCompleted(true);
        ratingStatisticModel.setLastRateAtUtc(new Date());
        when(ratingService.isCompleted(any(), any(), any(), any(), any(), any())).thenReturn(ratingStatisticModel);

        ExportSftpExportDataResponse actualResponse = exportSftpService.getExportData(userId, null, ExportSftpExportType.IMMEDIATELY);

        Assert.assertNotNull(actualResponse);
    }

    @Test
    public void testGetExportDataVerifyData() {
        String userId = "1";
        String tenantId = "2";
        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole("Teacher");
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("2");

        ExportSftpSettingResponse settingResponse = new ExportSftpSettingResponse();
        ExportSftpSettingMapModel mapModel = JsonUtil.fromJson(mapJson, ExportSftpSettingMapModel.class);
//        settingResponse.setMapModel(mapModel);
        ExportSftpSettingCenterModel centerModel = JsonUtil.fromJson(groupJson, ExportSftpSettingCenterModel.class);
        settingResponse.setCenterModel(centerModel);

        ExportSftpGetCenterGroupResponse centerGroups = new ExportSftpGetCenterGroupResponse();
        List<CenterModel> models = new ArrayList<>();
        CenterModel model = new CenterModel();
        model.setId("1");
        model.setName("CenterName");
        List<GroupModel> groupModels = new ArrayList<>();
        GroupModel gModel = new GroupModel();
        groupModels.add(gModel);

        model.setGroups(groupModels);

        centerGroups.setModels(models);

        ExportSftpSetting exportSftpSetting = new ExportSftpSetting();
        exportSftpSetting.setGroupJson(groupJson);
        exportSftpSetting.setMapJson(mapJson);
        exportSftpSetting.setTenantId(tenantId);
        exportSftpSetting.setExportStageIds("75516154-3B50-E411-837D-02DBFC8648CE,77516154-3B50-E411-837D-02DBFC8648CE,78516154-3B50-E411-837D-02DBFC8648CE,72516154-3B50-E411-837D-02DBFC8648CE,81516154-3B50-E411-837D-02DBFC8648CE,80516154-3B50-E411-837D-02DBFC8648CE");

        AgenciesSftpuser sftpUser = new AgenciesSftpuser();

        List<String> lockFrameworkIds = new ArrayList<>();

        List<CenterEntity> byCenterIds = new ArrayList<>();
        CenterEntity centerEntity1 = new CenterEntity();
        centerEntity1.setId("1");
        centerEntity1.setName("Center1");
        byCenterIds.add(centerEntity1);

        List<EnrollmentModel> allEnrollmentModelLists = new ArrayList<>();
        EnrollmentModel enrollmentModel1 = new EnrollmentModel();
        enrollmentModel1.setId("1");
        enrollmentModel1.setGroupId("1");
        allEnrollmentModelLists.add(enrollmentModel1);

        List<EnrollmentEntity> enrollmentEntityList = new ArrayList<>();
        EnrollmentEntity enrollmentEntity1 = new EnrollmentEntity();
        enrollmentEntity1.setId("1");
        GroupEntity groupEntity1 = new GroupEntity();
        groupEntity1.setId("1");
        enrollmentEntity1.setGroup(groupEntity1);

        enrollmentEntityList.add(enrollmentEntity1);

        List<EnrollmentMetaDataEntity> enrollmentMetaDataLists = new ArrayList<>();
        EnrollmentMetaDataEntity enrollmentMetaDataEntity1 = new EnrollmentMetaDataEntity();
        enrollmentMetaDataEntity1.setId("1");
        enrollmentMetaDataEntity1.setEnrollment(enrollmentEntity1);
        enrollmentMetaDataEntity1.setMetaKey("External ID");
        enrollmentMetaDataEntity1.setMetaValue("EXTERNAL_ID");
        enrollmentMetaDataEntity1.setChildId("1");

        enrollmentMetaDataLists.add(enrollmentMetaDataEntity1);


        EnrollmentMetaDataEntity enrollmentMetaDataEntity2 = new EnrollmentMetaDataEntity();
        enrollmentMetaDataEntity2.setId("2");
        enrollmentMetaDataEntity2.setEnrollment(enrollmentEntity1);
        enrollmentMetaDataEntity2.setMetaKey("IEP/IFSP");
        enrollmentMetaDataEntity2.setMetaValue("1");
        enrollmentMetaDataEntity2.setChildId("1");

        enrollmentMetaDataLists.add(enrollmentMetaDataEntity2);

        List<EnrollmentPeriodEntity> enrollmentPeriodList = new ArrayList<>();
        EnrollmentPeriodEntity enrollmentPeriodEntity1 = new EnrollmentPeriodEntity();
        enrollmentPeriodEntity1.setEnrollmentId("1");
        enrollmentPeriodEntity1.setDomainId("1");
        enrollmentPeriodEntity1.setAlias("2023-2024 Fall");
        enrollmentPeriodEntity1.setFromAtLocal(new Date());
        enrollmentPeriodEntity1.setToAtLocal(new Date());
        enrollmentPeriodList.add(enrollmentPeriodEntity1);

        List<ContentsGroupStage> exportGradeStageIdList = new ArrayList<>();
        ContentsGroupStage contentsGroupStage1 = new ContentsGroupStage();
        contentsGroupStage1.setId("1");
        contentsGroupStage1.setGradeName("IT");

        exportGradeStageIdList.add(contentsGroupStage1);

        List<com.learninggenie.common.data.entity.contents.GroupEntity> allGroupList = new ArrayList<>();
        com.learninggenie.common.data.entity.contents.GroupEntity groupEntity = new com.learninggenie.common.data.entity.contents.GroupEntity();
        groupEntity.setCenterId("1");
        groupEntity.setId("1");
        groupEntity.setStageId("1");

        allGroupList.add(groupEntity);

        List<FrameworkViewEntity> frameworkViewList = new ArrayList<>();
        FrameworkViewEntity frameworkViewEntity = new FrameworkViewEntity();
        frameworkViewEntity.setFrameworkId("1");
        frameworkViewEntity.setMeasureId("1");
        frameworkViewEntity.setId("1");

        frameworkViewList.add(frameworkViewEntity);

        List<MeasureEntity> frameworkMeasureList = new ArrayList<>();
        MeasureEntity measureEntity = new MeasureEntity();
        measureEntity.setId("1");
        measureEntity.setName("LTD-1");
        measureEntity.setSortIndex(1);
        measureEntity.setIsNode("0");
        frameworkMeasureList.add(measureEntity);

        ScoreTemplateEntity scoreTemplateEntry = new ScoreTemplateEntity();
        scoreTemplateEntry.setDomainLevelsJson("[{\"domainId\":\"measureId\",\"measure\":\"LLD8\",\"measureName\":\"Phonological Awareness\",\"levels\":[{\"id\":\"77F413F8-1EC3-4B70-B6E6-2F153CD936EE\",\"name\":\" Exploring Middle\",\"type\":\"radio\",\"sortIndex\":\"3\",\"value\":\"4\",\"tip\":\"Attends to sounds or elements of language\"},{\"id\":\"976C1402-59B1-4B21-8F16-EAB81A329989\",\"name\":\" Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Demonstrates awareness of variations in sounds\"},{\"id\":\"CFC998E8-3D19-44DB-BE98-664395648330\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Engages actively in play with sounds in words or rhymes, or sings simple songs, or\\nrepeats simple nursery rhymes\"},{\"id\":\"405BA949-F8FF-4CB0-A384-766174446ADF\",\"name\":\"Building Middle\",\"type\":\"radio\",\"sortIndex\":\"6\",\"value\":\"7\",\"tip\":\"Demonstrates awareness of larger units of language (e.g., words, syllables)\"},{\"id\":\"790D37D5-FF13-4DCD-84A3-5847C81323B2\",\"name\":\"Building Later\",\"type\":\"radio\",\"sortIndex\":\"7\",\"value\":\"8\",\"tip\":\"Blends larger units of language (e.g., compound words and syllables) with or without the support of pictures or objects;and segments larger units of language (e.g.\\ncompound words and syllables) with or without the support of pictures or objects\"},{\"id\":\"B16025FA-6DEF-47B4-808E-FAD1C0748308\",\"name\":\"UR (Unable to Rate)\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"},{\"id\":\"63BBC32F-686E-4859-81C5-463C366CD4F7\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"12\",\"value\":\"e\"},{\"id\":\"E72D58A8-167D-4068-8D25-216A174AAEE0\",\"name\":\"Integrating Earlier\",\"type\":\"radio\",\"sortIndex\":\"8\",\"value\":\"9\",\"tip\":\"Segments larger units of language (e.g., compound words and syllables) with or without the support of pictures or objectsBlends smaller units of language (e.g., onsetsand rimes), with or without the support of pictures or objects; and Segments smaller units of language (e.g., onsets and rimes), with or without the support of pictures or objects\"},{\"id\":\"F2A37092-00A9-4592-A182-625B59DEA07B\",\"name\":\"Not yet\",\"type\":\"radio\",\"sortIndex\":\"21\",\"value\":\"n\"}]}]");

        List<UsersMetaDataEntity> teacherSourceIds = new ArrayList<>();
        UsersMetaDataEntity usersMetaDataEntity1 = new UsersMetaDataEntity();
        usersMetaDataEntity1.setId("1");
        usersMetaDataEntity1.setMetaKey("SOURCE_ID");
        usersMetaDataEntity1.setMetaValue("1");
        usersMetaDataEntity1.setUserId("1");
        teacherSourceIds.add(usersMetaDataEntity1);

        List<SnapshotEntity> snapshotEntities = new ArrayList<>();
        SnapshotEntity snapshotEntity1 = new SnapshotEntity();
        snapshotEntity1.setId("1");
        snapshotEntity1.setGroupId("1");
        snapshotEntity1.setEnrollmentId("1");
        snapshotEntity1.setFrameworkId("1");
        snapshotEntity1.setPeriodAlias("2023-2024 Fall");
        LGSnapshot.StudentSnapshot studentSnapshot = LGSnapshot.StudentSnapshot.newBuilder()
                .setFirstName("John")
                .setLastName("Doe")
                .setEnrollmentDate(TimeUtil.parse("2023-01-01", TimeUtil.format10).getTime())
                .setBirthday(TimeUtil.parse("2018-01-01", TimeUtil.format10).getTime())
                .setGender(LGSnapshot.StudentSnapshot.GenderType.FEMALE)
                .setId("1")
                .addProperties(LGSnapshot.Properties.newBuilder()
                        .setName("IEP/IFSP")
                        .setValue("true")
                        .setType("type")
                        .build())
                .setGroup(LGSnapshot.ClassSnapshot.newBuilder().setId("1")
                        .setName("group001")
                        .setCreatedAt(TimeUtil.parse("2023-01-01", TimeUtil.format10).getTime())
                        .setUpdatedAt(TimeUtil.parse("2023-01-01", TimeUtil.format10).getTime())
                        .setCenter(LGSnapshot.CenterSnapshot.newBuilder().setId("1")
                                .setName("center001")
                                .setCreatedAt(TimeUtil.parse("2023-01-01", TimeUtil.format10).getTime())
                                .setUpdatedAt(TimeUtil.parse("2023-01-01", TimeUtil.format10).getTime())
                                .setAgency(LGSnapshot.AgencySnapshot.newBuilder().setId("agencyId001")
                                        .setName("agency001")
                                        .setCreatedAt(TimeUtil.parse("2023-01-01", TimeUtil.format10).getTime())
                                        .setUpdatedAt(TimeUtil.parse("2023-01-01", TimeUtil.format10).getTime())
                                        .build())
                                .build())
                        .build())
                .build();

        snapshotEntity1.setLockScore("[{\"childCount\":0,\"notesCount\":4,\"measureNotesCount\":79,\"distinctCenterMeasureCount\":29,\"distinctScoreMeasureCount\":29,\"distinctCenterKeyMeasureCount\":0,\"distinctScoreKeyMeasureCount\":0,\"iepDomainCount\":8,\"distinctIEPDomainCount\":8,\"distinctIEPRatingCount\":8,\"keyMeasureNotesCount\":0,\"openUpload\":false,\"iep\":true,\"eld\":true,\"keyMeasureNum\":0}]");
        snapshotEntity1.setData(studentSnapshot.toByteArray());
        snapshotEntities.add(snapshotEntity1);

        List<StudentScoreModel> enrollmentIdMeasureIdScoreMap = new ArrayList<>();
        StudentScoreModel scoreModel1 = new StudentScoreModel();
        scoreModel1.setFrameworkId("1");
        scoreModel1.setStudentId("1");
        scoreModel1.setDomainId("1");
        scoreModel1.setAlias("2023-2024 Fall");
        scoreModel1.setGroupId("1");
        enrollmentIdMeasureIdScoreMap.add(scoreModel1);


        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        when(exportSftpSettingDao.getSettingByTenantId(agencyModel.getId())).thenReturn(exportSftpSetting);
        when(agenciesSftpuserDao.getExportUserByUsername(anyString(), eq(tenantId))).thenReturn(sftpUser);
        when(userProvider.getUser(userId)).thenReturn(userEntity);
        when(enrollmentSnapshotDao.getDomainIdsByGroupIds(anyString())).thenReturn(lockFrameworkIds);
        when(centerDao.getCentersByCenterIds(anyList())).thenReturn(byCenterIds);
        when(studentDao.getAllNotInactiveChildrenByIds(anyList())).thenReturn(allEnrollmentModelLists);
        when(studentDao.getChildrenByGroupIds(anyList())).thenReturn(enrollmentEntityList);
        when(studentDao.getChildrenAllMeta(anyList())).thenReturn(enrollmentMetaDataLists);
        when(enrollmentPeriodDao.selectByEnrollmentIdsAndAliasLike(anyList(), anyString())).thenReturn(enrollmentPeriodList);
        when(contentsGroupStageDao.listByIds(anyList())).thenReturn(exportGradeStageIdList);
        when(mpGroupDao.listByIds(anyList())).thenReturn(allGroupList);
        when(ratingService.isDRDP(anyString())).thenReturn(true);
        when(frameworkViewDao.listByFrameworkIdIn(anyList())).thenReturn(frameworkViewList);
        when(measureDao.getByIdIn(anyList())).thenReturn(frameworkMeasureList);
        when(portfolioService.getScoreTemplate(anyString())).thenReturn(scoreTemplateEntry);
        when(userMetaDao.getMetasByUserList(anyString(), anyList())).thenReturn(teacherSourceIds);
        when(enrollmentSnapshotDao.listByEnrollmentIdsAndLikePeriod(anyList(), anyString())).thenReturn(snapshotEntities);



        when(scoreService.getStudentScoreByEnrollmentIds(anyList(), anyString())).thenReturn(enrollmentIdMeasureIdScoreMap);

        List<CenterGroupChildCount> dashboardDataList = new ArrayList<>();
        CenterGroupChildCount centerGroupChildCount = new CenterGroupChildCount();
        centerGroupChildCount.setCenterId("1");
        centerGroupChildCount.setGroupId("1");
        centerGroupChildCount.setEnrollmentId("1");
        centerGroupChildCount.setCenterName("Center1");
        centerGroupChildCount.setGroupName("Group1");
        centerGroupChildCount.setDomainId("1");
        centerGroupChildCount.setAlias("2023-2024 Fall");

        dashboardDataList.add(centerGroupChildCount);

        when(dashboardDao.getCenterGroupChildByCenterIds(anyList(), anyList(), anyString())).thenReturn(dashboardDataList);
        RatingStatisticModel ratingStatisticModel = new RatingStatisticModel();
        ratingStatisticModel.setCompleted(true);
        ratingStatisticModel.setLastRateAtUtc(new Date());
        lenient().when(ratingService.isCompleted(any(), any(), any(), any(), any(), any())).thenReturn(ratingStatisticModel);

        ExportSftpExportDataResponse actualResponse = exportSftpService.getExportData(userId, null, ExportSftpExportType.VERIFY_DATA);

        Assert.assertNotNull(actualResponse);
    }

    @Test
    public void testGetExportDataVerifyData2() {
        String userId = "1";
        String tenantId = "2";
        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole("Teacher");
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("2");

        ExportSftpSettingResponse settingResponse = new ExportSftpSettingResponse();
        ExportSftpSettingMapModel mapModel = JsonUtil.fromJson(mapJson, ExportSftpSettingMapModel.class);
//        settingResponse.setMapModel(mapModel);
        ExportSftpSettingCenterModel centerModel = JsonUtil.fromJson(groupJson, ExportSftpSettingCenterModel.class);
        settingResponse.setCenterModel(centerModel);

        ExportSftpGetCenterGroupResponse centerGroups = new ExportSftpGetCenterGroupResponse();
        List<CenterModel> models = new ArrayList<>();
        CenterModel model = new CenterModel();
        model.setId("1");
        model.setName("CenterName");
        List<GroupModel> groupModels = new ArrayList<>();
        GroupModel gModel = new GroupModel();
        groupModels.add(gModel);

        model.setGroups(groupModels);

        centerGroups.setModels(models);

        ExportSftpSetting exportSftpSetting = new ExportSftpSetting();
        exportSftpSetting.setGroupJson(groupJson);
        exportSftpSetting.setMapJson(mapJson);
        exportSftpSetting.setTenantId(tenantId);
        exportSftpSetting.setExportStageIds("75516154-3B50-E411-837D-02DBFC8648CE,77516154-3B50-E411-837D-02DBFC8648CE,78516154-3B50-E411-837D-02DBFC8648CE,72516154-3B50-E411-837D-02DBFC8648CE,81516154-3B50-E411-837D-02DBFC8648CE,80516154-3B50-E411-837D-02DBFC8648CE");

        AgenciesSftpuser sftpUser = new AgenciesSftpuser();

        List<String> lockFrameworkIds = new ArrayList<>();

        List<CenterEntity> byCenterIds = new ArrayList<>();
        CenterEntity centerEntity1 = new CenterEntity();
        centerEntity1.setId("1");
        centerEntity1.setName("Center1");
        byCenterIds.add(centerEntity1);

        List<EnrollmentModel> allEnrollmentModelLists = new ArrayList<>();
        EnrollmentModel enrollmentModel1 = new EnrollmentModel();
        enrollmentModel1.setId("1");
        enrollmentModel1.setGroupId("1");
        allEnrollmentModelLists.add(enrollmentModel1);

        List<EnrollmentEntity> enrollmentEntityList = new ArrayList<>();
        EnrollmentEntity enrollmentEntity1 = new EnrollmentEntity();
        enrollmentEntity1.setId("1");
        GroupEntity groupEntity1 = new GroupEntity();
        groupEntity1.setId("1");
        enrollmentEntity1.setGroup(groupEntity1);

        enrollmentEntityList.add(enrollmentEntity1);

        List<EnrollmentMetaDataEntity> enrollmentMetaDataLists = new ArrayList<>();
        EnrollmentMetaDataEntity enrollmentMetaDataEntity1 = new EnrollmentMetaDataEntity();
        enrollmentMetaDataEntity1.setId("1");
        enrollmentMetaDataEntity1.setEnrollment(enrollmentEntity1);
        enrollmentMetaDataEntity1.setMetaKey("External ID");
        enrollmentMetaDataEntity1.setMetaValue("EXTERNAL_ID");
        enrollmentMetaDataEntity1.setChildId("1");

        enrollmentMetaDataLists.add(enrollmentMetaDataEntity1);


        EnrollmentMetaDataEntity enrollmentMetaDataEntity2 = new EnrollmentMetaDataEntity();
        enrollmentMetaDataEntity2.setId("2");
        enrollmentMetaDataEntity2.setEnrollment(enrollmentEntity1);
        enrollmentMetaDataEntity2.setMetaKey("IEP/IFSP");
        enrollmentMetaDataEntity2.setMetaValue("1");
        enrollmentMetaDataEntity2.setChildId("1");

        enrollmentMetaDataLists.add(enrollmentMetaDataEntity2);

        List<EnrollmentPeriodEntity> enrollmentPeriodList = new ArrayList<>();
        EnrollmentPeriodEntity enrollmentPeriodEntity1 = new EnrollmentPeriodEntity();
        enrollmentPeriodEntity1.setEnrollmentId("1");
        enrollmentPeriodEntity1.setDomainId("1");
        enrollmentPeriodEntity1.setAlias("2023-2024 Fall");
        enrollmentPeriodEntity1.setFromAtLocal(new Date());
        enrollmentPeriodEntity1.setToAtLocal(new Date());
        enrollmentPeriodList.add(enrollmentPeriodEntity1);

        List<ContentsGroupStage> exportGradeStageIdList = new ArrayList<>();
        ContentsGroupStage contentsGroupStage1 = new ContentsGroupStage();
        contentsGroupStage1.setId("1");
        contentsGroupStage1.setGradeName("IT");

        exportGradeStageIdList.add(contentsGroupStage1);

        List<com.learninggenie.common.data.entity.contents.GroupEntity> allGroupList = new ArrayList<>();
        com.learninggenie.common.data.entity.contents.GroupEntity groupEntity = new com.learninggenie.common.data.entity.contents.GroupEntity();
        groupEntity.setCenterId("1");
        groupEntity.setId("1");
        groupEntity.setStageId("1");

        allGroupList.add(groupEntity);

        List<FrameworkViewEntity> frameworkViewList = new ArrayList<>();
        FrameworkViewEntity frameworkViewEntity = new FrameworkViewEntity();
        frameworkViewEntity.setFrameworkId("1");
        frameworkViewEntity.setMeasureId("1");
        frameworkViewEntity.setId("1");

        frameworkViewList.add(frameworkViewEntity);

        List<MeasureEntity> frameworkMeasureList = new ArrayList<>();
        MeasureEntity measureEntity = new MeasureEntity();
        measureEntity.setId("1");
        measureEntity.setName("LTD-1");
        measureEntity.setSortIndex(1);
        measureEntity.setIsNode("0");
        frameworkMeasureList.add(measureEntity);

        ScoreTemplateEntity scoreTemplateEntry = new ScoreTemplateEntity();
        scoreTemplateEntry.setDomainLevelsJson("[{\"domainId\":\"measureId\",\"measure\":\"LLD8\",\"measureName\":\"Phonological Awareness\",\"levels\":[{\"id\":\"77F413F8-1EC3-4B70-B6E6-2F153CD936EE\",\"name\":\" Exploring Middle\",\"type\":\"radio\",\"sortIndex\":\"3\",\"value\":\"4\",\"tip\":\"Attends to sounds or elements of language\"},{\"id\":\"976C1402-59B1-4B21-8F16-EAB81A329989\",\"name\":\" Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Demonstrates awareness of variations in sounds\"},{\"id\":\"CFC998E8-3D19-44DB-BE98-664395648330\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Engages actively in play with sounds in words or rhymes, or sings simple songs, or\\nrepeats simple nursery rhymes\"},{\"id\":\"405BA949-F8FF-4CB0-A384-766174446ADF\",\"name\":\"Building Middle\",\"type\":\"radio\",\"sortIndex\":\"6\",\"value\":\"7\",\"tip\":\"Demonstrates awareness of larger units of language (e.g., words, syllables)\"},{\"id\":\"790D37D5-FF13-4DCD-84A3-5847C81323B2\",\"name\":\"Building Later\",\"type\":\"radio\",\"sortIndex\":\"7\",\"value\":\"8\",\"tip\":\"Blends larger units of language (e.g., compound words and syllables) with or without the support of pictures or objects;and segments larger units of language (e.g.\\ncompound words and syllables) with or without the support of pictures or objects\"},{\"id\":\"B16025FA-6DEF-47B4-808E-FAD1C0748308\",\"name\":\"UR (Unable to Rate)\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"},{\"id\":\"63BBC32F-686E-4859-81C5-463C366CD4F7\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"12\",\"value\":\"e\"},{\"id\":\"E72D58A8-167D-4068-8D25-216A174AAEE0\",\"name\":\"Integrating Earlier\",\"type\":\"radio\",\"sortIndex\":\"8\",\"value\":\"9\",\"tip\":\"Segments larger units of language (e.g., compound words and syllables) with or without the support of pictures or objectsBlends smaller units of language (e.g., onsetsand rimes), with or without the support of pictures or objects; and Segments smaller units of language (e.g., onsets and rimes), with or without the support of pictures or objects\"},{\"id\":\"F2A37092-00A9-4592-A182-625B59DEA07B\",\"name\":\"Not yet\",\"type\":\"radio\",\"sortIndex\":\"21\",\"value\":\"n\"}]}]");

        List<UsersMetaDataEntity> teacherSourceIds = new ArrayList<>();
        UsersMetaDataEntity usersMetaDataEntity1 = new UsersMetaDataEntity();
        usersMetaDataEntity1.setId("1");
        usersMetaDataEntity1.setMetaKey("SOURCE_ID");
        usersMetaDataEntity1.setMetaValue("1");
        usersMetaDataEntity1.setUserId("1");
        teacherSourceIds.add(usersMetaDataEntity1);

        List<SnapshotEntity> snapshotEntities = new ArrayList<>();
        SnapshotEntity snapshotEntity1 = new SnapshotEntity();
        snapshotEntity1.setId("1");
        snapshotEntity1.setGroupId("2");
        snapshotEntity1.setEnrollmentId("1");
        snapshotEntity1.setFrameworkId("1");
        snapshotEntity1.setPeriodAlias("2023-2024 Fall");
        LGSnapshot.StudentSnapshot studentSnapshot = LGSnapshot.StudentSnapshot.newBuilder()
                .setFirstName("John")
                .setLastName("Doe")
                .setEnrollmentDate(TimeUtil.parse("2023-01-01", TimeUtil.format10).getTime())
                .setBirthday(TimeUtil.parse("2018-01-01", TimeUtil.format10).getTime())
                .setGender(LGSnapshot.StudentSnapshot.GenderType.FEMALE)
                .setId("1")
                .addProperties(LGSnapshot.Properties.newBuilder()
                        .setName("IEP/IFSP")
                        .setValue("true")
                        .setType("type")
                        .build())
                .setGroup(LGSnapshot.ClassSnapshot.newBuilder().setId("2")
                        .setName("group001")
                        .setCreatedAt(TimeUtil.parse("2023-01-01", TimeUtil.format10).getTime())
                        .setUpdatedAt(TimeUtil.parse("2023-01-01", TimeUtil.format10).getTime())
                        .setCenter(LGSnapshot.CenterSnapshot.newBuilder().setId("1")
                                .setName("center001")
                                .setCreatedAt(TimeUtil.parse("2023-01-01", TimeUtil.format10).getTime())
                                .setUpdatedAt(TimeUtil.parse("2023-01-01", TimeUtil.format10).getTime())
                                .setAgency(LGSnapshot.AgencySnapshot.newBuilder().setId("agencyId001")
                                        .setName("agency001")
                                        .setCreatedAt(TimeUtil.parse("2023-01-01", TimeUtil.format10).getTime())
                                        .setUpdatedAt(TimeUtil.parse("2023-01-01", TimeUtil.format10).getTime())
                                        .build())
                                .build())
                        .build())
                .build();

        snapshotEntity1.setData(studentSnapshot.toByteArray());
        snapshotEntity1.setLockScore("[{\"childCount\":0,\"notesCount\":4,\"measureNotesCount\":79,\"distinctCenterMeasureCount\":29,\"distinctScoreMeasureCount\":29,\"distinctCenterKeyMeasureCount\":0,\"distinctScoreKeyMeasureCount\":0,\"iepDomainCount\":8,\"distinctIEPDomainCount\":8,\"distinctIEPRatingCount\":8,\"keyMeasureNotesCount\":0,\"openUpload\":false,\"iep\":true,\"eld\":true,\"keyMeasureNum\":0}]");

        snapshotEntities.add(snapshotEntity1);

        List<StudentScoreModel> enrollmentIdMeasureIdScoreMap = new ArrayList<>();
        StudentScoreModel scoreModel1 = new StudentScoreModel();
        scoreModel1.setFrameworkId("1");
        scoreModel1.setStudentId("1");
        scoreModel1.setDomainId("1");
        scoreModel1.setAlias("2023-2024 Fall");
        scoreModel1.setGroupId("1");
        enrollmentIdMeasureIdScoreMap.add(scoreModel1);


        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        when(exportSftpSettingDao.getSettingByTenantId(agencyModel.getId())).thenReturn(exportSftpSetting);
        when(agenciesSftpuserDao.getExportUserByUsername(anyString(), eq(tenantId))).thenReturn(sftpUser);
        when(userProvider.getUser(userId)).thenReturn(userEntity);
        when(enrollmentSnapshotDao.getDomainIdsByGroupIds(anyString())).thenReturn(lockFrameworkIds);
        when(centerDao.getCentersByCenterIds(anyList())).thenReturn(byCenterIds);
        when(studentDao.getAllNotInactiveChildrenByIds(anyList())).thenReturn(allEnrollmentModelLists);
        when(studentDao.getChildrenByGroupIds(anyList())).thenReturn(enrollmentEntityList);
        when(studentDao.getChildrenAllMeta(anyList())).thenReturn(enrollmentMetaDataLists);
        when(enrollmentPeriodDao.selectByEnrollmentIdsAndAliasLike(anyList(), anyString())).thenReturn(enrollmentPeriodList);
        when(contentsGroupStageDao.listByIds(anyList())).thenReturn(exportGradeStageIdList);
        when(mpGroupDao.listByIds(anyList())).thenReturn(allGroupList);
        when(ratingService.isDRDP(anyString())).thenReturn(true);
        when(frameworkViewDao.listByFrameworkIdIn(anyList())).thenReturn(frameworkViewList);
        when(measureDao.getByIdIn(anyList())).thenReturn(frameworkMeasureList);
        when(portfolioService.getScoreTemplate(anyString())).thenReturn(scoreTemplateEntry);
        when(userMetaDao.getMetasByUserList(anyString(), anyList())).thenReturn(teacherSourceIds);
        when(enrollmentSnapshotDao.listByEnrollmentIdsAndLikePeriod(anyList(), anyString())).thenReturn(snapshotEntities);



        when(scoreService.getStudentScoreByEnrollmentIds(anyList(), anyString())).thenReturn(enrollmentIdMeasureIdScoreMap);

        List<CenterGroupChildCount> dashboardDataList = new ArrayList<>();
        CenterGroupChildCount centerGroupChildCount = new CenterGroupChildCount();
        centerGroupChildCount.setCenterId("1");
        centerGroupChildCount.setGroupId("1");
        centerGroupChildCount.setEnrollmentId("1");
        centerGroupChildCount.setCenterName("Center1");
        centerGroupChildCount.setGroupName("Group1");
        centerGroupChildCount.setDomainId("1");
        centerGroupChildCount.setAlias("2023-2024 Fall");

        dashboardDataList.add(centerGroupChildCount);

        when(dashboardDao.getCenterGroupChildByCenterIds(anyList(), anyList(), anyString())).thenReturn(dashboardDataList);
        RatingStatisticModel ratingStatisticModel = new RatingStatisticModel();
        ratingStatisticModel.setCompleted(true);
        ratingStatisticModel.setLastRateAtUtc(new Date());
        lenient().when(ratingService.isCompleted(any(), any(), any(), any(), any(), any())).thenReturn(ratingStatisticModel);

        ExportSftpExportDataResponse actualResponse = exportSftpService.getExportData(userId, null, ExportSftpExportType.VERIFY_DATA);

        Assert.assertNotNull(actualResponse);
    }


    @Test
    public void testGetExportDataImmediately() {
        String userId = "1";
        String tenantId = "2";
        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole("Teacher");
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("2");

        ExportSftpSettingResponse settingResponse = new ExportSftpSettingResponse();
        ExportSftpSettingMapModel mapModel = JsonUtil.fromJson(mapJson, ExportSftpSettingMapModel.class);
//        settingResponse.setMapModel(mapModel);
        ExportSftpSettingCenterModel centerModel = JsonUtil.fromJson(groupJson, ExportSftpSettingCenterModel.class);
        settingResponse.setCenterModel(centerModel);

        ExportSftpGetCenterGroupResponse centerGroups = new ExportSftpGetCenterGroupResponse();
        List<CenterModel> models = new ArrayList<>();
        CenterModel model = new CenterModel();
        model.setId("1");
        model.setName("CenterName");
        List<GroupModel> groupModels = new ArrayList<>();
        GroupModel gModel = new GroupModel();
        groupModels.add(gModel);

        model.setGroups(groupModels);

        centerGroups.setModels(models);

        ExportSftpSetting exportSftpSetting = new ExportSftpSetting();
        exportSftpSetting.setGroupJson(groupJson);
        exportSftpSetting.setMapJson(mapJson);
        exportSftpSetting.setTenantId(tenantId);
        exportSftpSetting.setExportStageIds("75516154-3B50-E411-837D-02DBFC8648CE,77516154-3B50-E411-837D-02DBFC8648CE,78516154-3B50-E411-837D-02DBFC8648CE,72516154-3B50-E411-837D-02DBFC8648CE,81516154-3B50-E411-837D-02DBFC8648CE,80516154-3B50-E411-837D-02DBFC8648CE");

        AgenciesSftpuser sftpUser = new AgenciesSftpuser();

        List<String> lockFrameworkIds = new ArrayList<>();

        List<CenterEntity> byCenterIds = new ArrayList<>();
        CenterEntity centerEntity1 = new CenterEntity();
        centerEntity1.setId("1");
        centerEntity1.setName("Center1");
        byCenterIds.add(centerEntity1);

        List<EnrollmentModel> allEnrollmentModelLists = new ArrayList<>();
        EnrollmentModel enrollmentModel1 = new EnrollmentModel();
        enrollmentModel1.setId("1");
        enrollmentModel1.setGroupId("1");
        allEnrollmentModelLists.add(enrollmentModel1);

        List<EnrollmentEntity> enrollmentEntityList = new ArrayList<>();
        EnrollmentEntity enrollmentEntity1 = new EnrollmentEntity();
        enrollmentEntity1.setId("1");
        GroupEntity groupEntity1 = new GroupEntity();
        groupEntity1.setId("1");
        enrollmentEntity1.setGroup(groupEntity1);

        enrollmentEntityList.add(enrollmentEntity1);

        List<EnrollmentMetaDataEntity> enrollmentMetaDataLists = new ArrayList<>();
        EnrollmentMetaDataEntity enrollmentMetaDataEntity1 = new EnrollmentMetaDataEntity();
        enrollmentMetaDataEntity1.setId("1");
        enrollmentMetaDataEntity1.setEnrollment(enrollmentEntity1);
        enrollmentMetaDataEntity1.setMetaKey("External ID");
        enrollmentMetaDataEntity1.setMetaValue("EXTERNAL_ID");
        enrollmentMetaDataEntity1.setChildId("1");

        enrollmentMetaDataLists.add(enrollmentMetaDataEntity1);


        EnrollmentMetaDataEntity enrollmentMetaDataEntity2 = new EnrollmentMetaDataEntity();
        enrollmentMetaDataEntity2.setId("2");
        enrollmentMetaDataEntity2.setEnrollment(enrollmentEntity1);
        enrollmentMetaDataEntity2.setMetaKey("IEP/IFSP");
        enrollmentMetaDataEntity2.setMetaValue("1");
        enrollmentMetaDataEntity2.setChildId("1");

        enrollmentMetaDataLists.add(enrollmentMetaDataEntity2);

        List<EnrollmentPeriodEntity> enrollmentPeriodList = new ArrayList<>();
        EnrollmentPeriodEntity enrollmentPeriodEntity1 = new EnrollmentPeriodEntity();
        enrollmentPeriodEntity1.setEnrollmentId("1");
        enrollmentPeriodEntity1.setDomainId("1");
        enrollmentPeriodEntity1.setAlias("2023-2024 Fall");
        enrollmentPeriodEntity1.setFromAtLocal(new Date());
        enrollmentPeriodEntity1.setToAtLocal(new Date());
        enrollmentPeriodList.add(enrollmentPeriodEntity1);

        List<ContentsGroupStage> exportGradeStageIdList = new ArrayList<>();
        ContentsGroupStage contentsGroupStage1 = new ContentsGroupStage();
        contentsGroupStage1.setId("1");
        contentsGroupStage1.setGradeName("IT");

        exportGradeStageIdList.add(contentsGroupStage1);

        List<com.learninggenie.common.data.entity.contents.GroupEntity> allGroupList = new ArrayList<>();
        com.learninggenie.common.data.entity.contents.GroupEntity groupEntity = new com.learninggenie.common.data.entity.contents.GroupEntity();
        groupEntity.setCenterId("1");
        groupEntity.setId("1");
        groupEntity.setStageId("1");

        allGroupList.add(groupEntity);

        List<FrameworkViewEntity> frameworkViewList = new ArrayList<>();
        FrameworkViewEntity frameworkViewEntity = new FrameworkViewEntity();
        frameworkViewEntity.setFrameworkId("1");
        frameworkViewEntity.setMeasureId("1");
        frameworkViewEntity.setId("1");

        frameworkViewList.add(frameworkViewEntity);

        List<MeasureEntity> frameworkMeasureList = new ArrayList<>();
        MeasureEntity measureEntity = new MeasureEntity();
        measureEntity.setId("1");
        measureEntity.setName("LTD-1");
        measureEntity.setSortIndex(1);
        measureEntity.setIsNode("0");
        frameworkMeasureList.add(measureEntity);

        ScoreTemplateEntity scoreTemplateEntry = new ScoreTemplateEntity();
        scoreTemplateEntry.setDomainLevelsJson("[{\"domainId\":\"measureId\",\"measure\":\"LLD8\",\"measureName\":\"Phonological Awareness\",\"levels\":[{\"id\":\"77F413F8-1EC3-4B70-B6E6-2F153CD936EE\",\"name\":\" Exploring Middle\",\"type\":\"radio\",\"sortIndex\":\"3\",\"value\":\"4\",\"tip\":\"Attends to sounds or elements of language\"},{\"id\":\"976C1402-59B1-4B21-8F16-EAB81A329989\",\"name\":\" Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Demonstrates awareness of variations in sounds\"},{\"id\":\"CFC998E8-3D19-44DB-BE98-664395648330\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Engages actively in play with sounds in words or rhymes, or sings simple songs, or\\nrepeats simple nursery rhymes\"},{\"id\":\"405BA949-F8FF-4CB0-A384-766174446ADF\",\"name\":\"Building Middle\",\"type\":\"radio\",\"sortIndex\":\"6\",\"value\":\"7\",\"tip\":\"Demonstrates awareness of larger units of language (e.g., words, syllables)\"},{\"id\":\"790D37D5-FF13-4DCD-84A3-5847C81323B2\",\"name\":\"Building Later\",\"type\":\"radio\",\"sortIndex\":\"7\",\"value\":\"8\",\"tip\":\"Blends larger units of language (e.g., compound words and syllables) with or without the support of pictures or objects;and segments larger units of language (e.g.\\ncompound words and syllables) with or without the support of pictures or objects\"},{\"id\":\"B16025FA-6DEF-47B4-808E-FAD1C0748308\",\"name\":\"UR (Unable to Rate)\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"},{\"id\":\"63BBC32F-686E-4859-81C5-463C366CD4F7\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"12\",\"value\":\"e\"},{\"id\":\"E72D58A8-167D-4068-8D25-216A174AAEE0\",\"name\":\"Integrating Earlier\",\"type\":\"radio\",\"sortIndex\":\"8\",\"value\":\"9\",\"tip\":\"Segments larger units of language (e.g., compound words and syllables) with or without the support of pictures or objectsBlends smaller units of language (e.g., onsetsand rimes), with or without the support of pictures or objects; and Segments smaller units of language (e.g., onsets and rimes), with or without the support of pictures or objects\"},{\"id\":\"F2A37092-00A9-4592-A182-625B59DEA07B\",\"name\":\"Not yet\",\"type\":\"radio\",\"sortIndex\":\"21\",\"value\":\"n\"}]}]");

        List<UsersMetaDataEntity> teacherSourceIds = new ArrayList<>();
        UsersMetaDataEntity usersMetaDataEntity1 = new UsersMetaDataEntity();
        usersMetaDataEntity1.setId("1");
        usersMetaDataEntity1.setMetaKey("SOURCE_ID");
        usersMetaDataEntity1.setMetaValue("1");
        usersMetaDataEntity1.setUserId("1");
        teacherSourceIds.add(usersMetaDataEntity1);

        List<SnapshotEntity> snapshotEntities = new ArrayList<>();
        SnapshotEntity snapshotEntity1 = new SnapshotEntity();
        snapshotEntity1.setId("1");
        snapshotEntity1.setGroupId("1");
        snapshotEntity1.setEnrollmentId("1");
        snapshotEntity1.setFrameworkId("1");
        snapshotEntity1.setPeriodAlias("2023-2024 Fall");
        LGSnapshot.StudentSnapshot studentSnapshot = LGSnapshot.StudentSnapshot.newBuilder()
                .setFirstName("John")
                .setLastName("Doe")
                .setEnrollmentDate(TimeUtil.parse("2023-01-01", TimeUtil.format10).getTime())
                .setBirthday(TimeUtil.parse("2018-01-01", TimeUtil.format10).getTime())
                .setGender(LGSnapshot.StudentSnapshot.GenderType.FEMALE)
                .setId("1")
                .addProperties(LGSnapshot.Properties.newBuilder()
                        .setName("IEP/IFSP")
                        .setValue("true")
                        .setType("type")
                        .build())
                .setGroup(LGSnapshot.ClassSnapshot.newBuilder().setId("1")
                        .setName("group001")
                        .setCreatedAt(TimeUtil.parse("2023-01-01", TimeUtil.format10).getTime())
                        .setUpdatedAt(TimeUtil.parse("2023-01-01", TimeUtil.format10).getTime())
                        .setCenter(LGSnapshot.CenterSnapshot.newBuilder().setId("1")
                                .setName("center001")
                                .setCreatedAt(TimeUtil.parse("2023-01-01", TimeUtil.format10).getTime())
                                .setUpdatedAt(TimeUtil.parse("2023-01-01", TimeUtil.format10).getTime())
                                .setAgency(LGSnapshot.AgencySnapshot.newBuilder().setId("agencyId001")
                                        .setName("agency001")
                                        .setCreatedAt(TimeUtil.parse("2023-01-01", TimeUtil.format10).getTime())
                                        .setUpdatedAt(TimeUtil.parse("2023-01-01", TimeUtil.format10).getTime())
                                        .build())
                                .build())
                        .build())
                .build();

        snapshotEntity1.setLockScore("[{\"childCount\":0,\"notesCount\":4,\"measureNotesCount\":79,\"distinctCenterMeasureCount\":29,\"distinctScoreMeasureCount\":29,\"distinctCenterKeyMeasureCount\":0,\"distinctScoreKeyMeasureCount\":0,\"iepDomainCount\":8,\"distinctIEPDomainCount\":8,\"distinctIEPRatingCount\":8,\"keyMeasureNotesCount\":0,\"openUpload\":false,\"iep\":true,\"eld\":true,\"keyMeasureNum\":0}]");

        snapshotEntity1.setData(studentSnapshot.toByteArray());
        snapshotEntities.add(snapshotEntity1);

        List<StudentScoreModel> enrollmentIdMeasureIdScoreMap = new ArrayList<>();
        StudentScoreModel scoreModel1 = new StudentScoreModel();
        scoreModel1.setFrameworkId("1");
        scoreModel1.setStudentId("1");
        scoreModel1.setDomainId("1");
        scoreModel1.setAlias("2023-2024 Fall");
        scoreModel1.setGroupId("1");
        enrollmentIdMeasureIdScoreMap.add(scoreModel1);


        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        when(exportSftpSettingDao.getSettingByTenantId(agencyModel.getId())).thenReturn(exportSftpSetting);
        when(agenciesSftpuserDao.getExportUserByUsername(anyString(), eq(tenantId))).thenReturn(sftpUser);
        when(userProvider.getUser(userId)).thenReturn(userEntity);
        when(enrollmentSnapshotDao.getDomainIdsByGroupIds(anyString())).thenReturn(lockFrameworkIds);
        when(centerDao.getCentersByCenterIds(anyList())).thenReturn(byCenterIds);
        when(studentDao.getAllNotInactiveChildrenByIds(anyList())).thenReturn(allEnrollmentModelLists);
        when(studentDao.getChildrenByGroupIds(anyList())).thenReturn(enrollmentEntityList);
        when(studentDao.getChildrenAllMeta(anyList())).thenReturn(enrollmentMetaDataLists);
        when(enrollmentPeriodDao.selectByEnrollmentIdsAndAliasLike(anyList(), anyString())).thenReturn(enrollmentPeriodList);
        when(contentsGroupStageDao.listByIds(anyList())).thenReturn(exportGradeStageIdList);
        when(mpGroupDao.listByIds(anyList())).thenReturn(allGroupList);
        when(ratingService.isDRDP(anyString())).thenReturn(true);
        when(frameworkViewDao.listByFrameworkIdIn(anyList())).thenReturn(frameworkViewList);
        when(measureDao.getByIdIn(anyList())).thenReturn(frameworkMeasureList);
        when(portfolioService.getScoreTemplate(anyString())).thenReturn(scoreTemplateEntry);
        when(userMetaDao.getMetasByUserList(anyString(), anyList())).thenReturn(teacherSourceIds);
        when(enrollmentSnapshotDao.listByEnrollmentIdsAndLikePeriod(anyList(), anyString())).thenReturn(snapshotEntities);



        when(scoreService.getStudentScoreByEnrollmentIds(anyList(), anyString())).thenReturn(enrollmentIdMeasureIdScoreMap);

        List<CenterGroupChildCount> dashboardDataList = new ArrayList<>();
        CenterGroupChildCount centerGroupChildCount = new CenterGroupChildCount();
        centerGroupChildCount.setCenterId("1");
        centerGroupChildCount.setGroupId("1");
        centerGroupChildCount.setEnrollmentId("1");
        centerGroupChildCount.setCenterName("Center1");
        centerGroupChildCount.setGroupName("Group1");
        centerGroupChildCount.setDomainId("1");
        centerGroupChildCount.setAlias("2023-2024 Fall");

        dashboardDataList.add(centerGroupChildCount);

        when(dashboardDao.getCenterGroupChildByCenterIds(anyList(), anyList(), anyString())).thenReturn(dashboardDataList);
        RatingStatisticModel ratingStatisticModel = new RatingStatisticModel();
        ratingStatisticModel.setCompleted(true);
        ratingStatisticModel.setLastRateAtUtc(new Date());
        lenient().when(ratingService.isCompleted(any(), any(), any(), any(), any(), any())).thenReturn(ratingStatisticModel);

        ExportSftpExportDataResponse actualResponse = exportSftpService.getExportData(userId, null, ExportSftpExportType.IMMEDIATELY);

        Assert.assertNotNull(actualResponse);
    }

    @Test
    public void testGetExportDataImmediately2() {
        String userId = "1";
        String tenantId = "2";
        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole("Teacher");
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("2");

        ExportSftpSettingResponse settingResponse = new ExportSftpSettingResponse();
        ExportSftpSettingMapModel mapModel = JsonUtil.fromJson(mapJson, ExportSftpSettingMapModel.class);
//        settingResponse.setMapModel(mapModel);
        ExportSftpSettingCenterModel centerModel = JsonUtil.fromJson(groupJson, ExportSftpSettingCenterModel.class);
        settingResponse.setCenterModel(centerModel);

        ExportSftpGetCenterGroupResponse centerGroups = new ExportSftpGetCenterGroupResponse();
        List<CenterModel> models = new ArrayList<>();
        CenterModel model = new CenterModel();
        model.setId("1");
        model.setName("CenterName");
        List<GroupModel> groupModels = new ArrayList<>();
        GroupModel gModel = new GroupModel();
        groupModels.add(gModel);

        model.setGroups(groupModels);

        centerGroups.setModels(models);

        ExportSftpSetting exportSftpSetting = new ExportSftpSetting();
        exportSftpSetting.setGroupJson(groupJson);
        exportSftpSetting.setMapJson(mapJson);
        exportSftpSetting.setTenantId(tenantId);
        exportSftpSetting.setExportStageIds("75516154-3B50-E411-837D-02DBFC8648CE,77516154-3B50-E411-837D-02DBFC8648CE,78516154-3B50-E411-837D-02DBFC8648CE,72516154-3B50-E411-837D-02DBFC8648CE,81516154-3B50-E411-837D-02DBFC8648CE,80516154-3B50-E411-837D-02DBFC8648CE");

        AgenciesSftpuser sftpUser = new AgenciesSftpuser();

        List<String> lockFrameworkIds = new ArrayList<>();

        List<CenterEntity> byCenterIds = new ArrayList<>();
        CenterEntity centerEntity1 = new CenterEntity();
        centerEntity1.setId("1");
        centerEntity1.setName("Center1");
        byCenterIds.add(centerEntity1);

        List<EnrollmentModel> allEnrollmentModelLists = new ArrayList<>();
        EnrollmentModel enrollmentModel1 = new EnrollmentModel();
        enrollmentModel1.setId("1");
        enrollmentModel1.setGroupId("1");
        allEnrollmentModelLists.add(enrollmentModel1);

        List<EnrollmentEntity> enrollmentEntityList = new ArrayList<>();
        EnrollmentEntity enrollmentEntity1 = new EnrollmentEntity();
        enrollmentEntity1.setId("1");
        GroupEntity groupEntity1 = new GroupEntity();
        groupEntity1.setId("1");
        enrollmentEntity1.setGroup(groupEntity1);

        enrollmentEntityList.add(enrollmentEntity1);

        List<EnrollmentMetaDataEntity> enrollmentMetaDataLists = new ArrayList<>();
        EnrollmentMetaDataEntity enrollmentMetaDataEntity1 = new EnrollmentMetaDataEntity();
        enrollmentMetaDataEntity1.setId("1");
        enrollmentMetaDataEntity1.setEnrollment(enrollmentEntity1);
        enrollmentMetaDataEntity1.setMetaKey("External ID");
        enrollmentMetaDataEntity1.setMetaValue("EXTERNAL_ID");
        enrollmentMetaDataEntity1.setChildId("1");

        enrollmentMetaDataLists.add(enrollmentMetaDataEntity1);


        EnrollmentMetaDataEntity enrollmentMetaDataEntity2 = new EnrollmentMetaDataEntity();
        enrollmentMetaDataEntity2.setId("2");
        enrollmentMetaDataEntity2.setEnrollment(enrollmentEntity1);
        enrollmentMetaDataEntity2.setMetaKey("IEP/IFSP");
        enrollmentMetaDataEntity2.setMetaValue("1");
        enrollmentMetaDataEntity2.setChildId("1");

        enrollmentMetaDataLists.add(enrollmentMetaDataEntity2);

        List<EnrollmentPeriodEntity> enrollmentPeriodList = new ArrayList<>();
        EnrollmentPeriodEntity enrollmentPeriodEntity1 = new EnrollmentPeriodEntity();
        enrollmentPeriodEntity1.setEnrollmentId("1");
        enrollmentPeriodEntity1.setDomainId("1");
        enrollmentPeriodEntity1.setAlias("2023-2024 Fall");
        enrollmentPeriodEntity1.setFromAtLocal(new Date());
        enrollmentPeriodEntity1.setToAtLocal(new Date());
        enrollmentPeriodList.add(enrollmentPeriodEntity1);

        List<ContentsGroupStage> exportGradeStageIdList = new ArrayList<>();
        ContentsGroupStage contentsGroupStage1 = new ContentsGroupStage();
        contentsGroupStage1.setId("1");
        contentsGroupStage1.setGradeName("IT");

        exportGradeStageIdList.add(contentsGroupStage1);

        List<com.learninggenie.common.data.entity.contents.GroupEntity> allGroupList = new ArrayList<>();
        com.learninggenie.common.data.entity.contents.GroupEntity groupEntity = new com.learninggenie.common.data.entity.contents.GroupEntity();
        groupEntity.setCenterId("1");
        groupEntity.setId("1");
        groupEntity.setStageId("1");

        allGroupList.add(groupEntity);

        List<FrameworkViewEntity> frameworkViewList = new ArrayList<>();
        FrameworkViewEntity frameworkViewEntity = new FrameworkViewEntity();
        frameworkViewEntity.setFrameworkId("1");
        frameworkViewEntity.setMeasureId("1");
        frameworkViewEntity.setId("1");

        frameworkViewList.add(frameworkViewEntity);

        List<MeasureEntity> frameworkMeasureList = new ArrayList<>();
        MeasureEntity measureEntity = new MeasureEntity();
        measureEntity.setId("1");
        measureEntity.setName("LTD-1");
        measureEntity.setSortIndex(1);
        measureEntity.setIsNode("0");
        frameworkMeasureList.add(measureEntity);

        ScoreTemplateEntity scoreTemplateEntry = new ScoreTemplateEntity();
        scoreTemplateEntry.setDomainLevelsJson("[{\"domainId\":\"measureId\",\"measure\":\"LLD8\",\"measureName\":\"Phonological Awareness\",\"levels\":[{\"id\":\"77F413F8-1EC3-4B70-B6E6-2F153CD936EE\",\"name\":\" Exploring Middle\",\"type\":\"radio\",\"sortIndex\":\"3\",\"value\":\"4\",\"tip\":\"Attends to sounds or elements of language\"},{\"id\":\"976C1402-59B1-4B21-8F16-EAB81A329989\",\"name\":\" Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Demonstrates awareness of variations in sounds\"},{\"id\":\"CFC998E8-3D19-44DB-BE98-664395648330\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Engages actively in play with sounds in words or rhymes, or sings simple songs, or\\nrepeats simple nursery rhymes\"},{\"id\":\"405BA949-F8FF-4CB0-A384-766174446ADF\",\"name\":\"Building Middle\",\"type\":\"radio\",\"sortIndex\":\"6\",\"value\":\"7\",\"tip\":\"Demonstrates awareness of larger units of language (e.g., words, syllables)\"},{\"id\":\"790D37D5-FF13-4DCD-84A3-5847C81323B2\",\"name\":\"Building Later\",\"type\":\"radio\",\"sortIndex\":\"7\",\"value\":\"8\",\"tip\":\"Blends larger units of language (e.g., compound words and syllables) with or without the support of pictures or objects;and segments larger units of language (e.g.\\ncompound words and syllables) with or without the support of pictures or objects\"},{\"id\":\"B16025FA-6DEF-47B4-808E-FAD1C0748308\",\"name\":\"UR (Unable to Rate)\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"},{\"id\":\"63BBC32F-686E-4859-81C5-463C366CD4F7\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"12\",\"value\":\"e\"},{\"id\":\"E72D58A8-167D-4068-8D25-216A174AAEE0\",\"name\":\"Integrating Earlier\",\"type\":\"radio\",\"sortIndex\":\"8\",\"value\":\"9\",\"tip\":\"Segments larger units of language (e.g., compound words and syllables) with or without the support of pictures or objectsBlends smaller units of language (e.g., onsetsand rimes), with or without the support of pictures or objects; and Segments smaller units of language (e.g., onsets and rimes), with or without the support of pictures or objects\"},{\"id\":\"F2A37092-00A9-4592-A182-625B59DEA07B\",\"name\":\"Not yet\",\"type\":\"radio\",\"sortIndex\":\"21\",\"value\":\"n\"}]}]");

        List<UsersMetaDataEntity> teacherSourceIds = new ArrayList<>();
        UsersMetaDataEntity usersMetaDataEntity1 = new UsersMetaDataEntity();
        usersMetaDataEntity1.setId("1");
        usersMetaDataEntity1.setMetaKey("SOURCE_ID");
        usersMetaDataEntity1.setMetaValue("1");
        usersMetaDataEntity1.setUserId("1");
        teacherSourceIds.add(usersMetaDataEntity1);

        List<SnapshotEntity> snapshotEntities = new ArrayList<>();
        SnapshotEntity snapshotEntity1 = new SnapshotEntity();
        snapshotEntity1.setId("1");
        snapshotEntity1.setGroupId("2");
        snapshotEntity1.setEnrollmentId("1");
        snapshotEntity1.setFrameworkId("1");
        snapshotEntity1.setPeriodAlias("2023-2024 Fall");
        LGSnapshot.StudentSnapshot studentSnapshot = LGSnapshot.StudentSnapshot.newBuilder()
                .setFirstName("John")
                .setLastName("Doe")
                .setEnrollmentDate(TimeUtil.parse("2023-01-01", TimeUtil.format10).getTime())
                .setBirthday(TimeUtil.parse("2018-01-01", TimeUtil.format10).getTime())
                .setGender(LGSnapshot.StudentSnapshot.GenderType.FEMALE)
                .setId("1")
                .addProperties(LGSnapshot.Properties.newBuilder()
                        .setName("IEP/IFSP")
                        .setValue("true")
                        .setType("type")
                        .build())
                .setGroup(LGSnapshot.ClassSnapshot.newBuilder().setId("2")
                        .setName("group001")
                        .setCreatedAt(TimeUtil.parse("2023-01-01", TimeUtil.format10).getTime())
                        .setUpdatedAt(TimeUtil.parse("2023-01-01", TimeUtil.format10).getTime())
                        .setCenter(LGSnapshot.CenterSnapshot.newBuilder().setId("1")
                                .setName("center001")
                                .setCreatedAt(TimeUtil.parse("2023-01-01", TimeUtil.format10).getTime())
                                .setUpdatedAt(TimeUtil.parse("2023-01-01", TimeUtil.format10).getTime())
                                .setAgency(LGSnapshot.AgencySnapshot.newBuilder().setId("agencyId001")
                                        .setName("agency001")
                                        .setCreatedAt(TimeUtil.parse("2023-01-01", TimeUtil.format10).getTime())
                                        .setUpdatedAt(TimeUtil.parse("2023-01-01", TimeUtil.format10).getTime())
                                        .build())
                                .build())
                        .build())
                .build();

        snapshotEntity1.setLockScore("[{\"childCount\":0,\"notesCount\":4,\"measureNotesCount\":79,\"distinctCenterMeasureCount\":29,\"distinctScoreMeasureCount\":29,\"distinctCenterKeyMeasureCount\":0,\"distinctScoreKeyMeasureCount\":0,\"iepDomainCount\":8,\"distinctIEPDomainCount\":8,\"distinctIEPRatingCount\":8,\"keyMeasureNotesCount\":0,\"openUpload\":false,\"iep\":true,\"eld\":true,\"keyMeasureNum\":0}]");

        snapshotEntity1.setData(studentSnapshot.toByteArray());
        snapshotEntities.add(snapshotEntity1);

        List<StudentScoreModel> enrollmentIdMeasureIdScoreMap = new ArrayList<>();
        StudentScoreModel scoreModel1 = new StudentScoreModel();
        scoreModel1.setFrameworkId("1");
        scoreModel1.setStudentId("1");
        scoreModel1.setDomainId("1");
        scoreModel1.setAlias("2023-2024 Fall");
        scoreModel1.setGroupId("1");
        enrollmentIdMeasureIdScoreMap.add(scoreModel1);


        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        when(exportSftpSettingDao.getSettingByTenantId(agencyModel.getId())).thenReturn(exportSftpSetting);
        when(agenciesSftpuserDao.getExportUserByUsername(anyString(), eq(tenantId))).thenReturn(sftpUser);
        when(userProvider.getUser(userId)).thenReturn(userEntity);
        when(enrollmentSnapshotDao.getDomainIdsByGroupIds(anyString())).thenReturn(lockFrameworkIds);
        when(centerDao.getCentersByCenterIds(anyList())).thenReturn(byCenterIds);
        when(studentDao.getAllNotInactiveChildrenByIds(anyList())).thenReturn(allEnrollmentModelLists);
        when(studentDao.getChildrenByGroupIds(anyList())).thenReturn(enrollmentEntityList);
        when(studentDao.getChildrenAllMeta(anyList())).thenReturn(enrollmentMetaDataLists);
        when(enrollmentPeriodDao.selectByEnrollmentIdsAndAliasLike(anyList(), anyString())).thenReturn(enrollmentPeriodList);
        when(contentsGroupStageDao.listByIds(anyList())).thenReturn(exportGradeStageIdList);
        when(mpGroupDao.listByIds(anyList())).thenReturn(allGroupList);
        when(ratingService.isDRDP(anyString())).thenReturn(true);
        when(frameworkViewDao.listByFrameworkIdIn(anyList())).thenReturn(frameworkViewList);
        when(measureDao.getByIdIn(anyList())).thenReturn(frameworkMeasureList);
        when(portfolioService.getScoreTemplate(anyString())).thenReturn(scoreTemplateEntry);
        when(userMetaDao.getMetasByUserList(anyString(), anyList())).thenReturn(teacherSourceIds);
        when(enrollmentSnapshotDao.listByEnrollmentIdsAndLikePeriod(anyList(), anyString())).thenReturn(snapshotEntities);



        when(scoreService.getStudentScoreByEnrollmentIds(anyList(), anyString())).thenReturn(enrollmentIdMeasureIdScoreMap);

        List<CenterGroupChildCount> dashboardDataList = new ArrayList<>();
        CenterGroupChildCount centerGroupChildCount = new CenterGroupChildCount();
        centerGroupChildCount.setCenterId("1");
        centerGroupChildCount.setGroupId("1");
        centerGroupChildCount.setEnrollmentId("1");
        centerGroupChildCount.setCenterName("Center1");
        centerGroupChildCount.setGroupName("Group1");
        centerGroupChildCount.setDomainId("1");
        centerGroupChildCount.setAlias("2023-2024 Fall");

        dashboardDataList.add(centerGroupChildCount);

        when(dashboardDao.getCenterGroupChildByCenterIds(anyList(), anyList(), anyString())).thenReturn(dashboardDataList);
        RatingStatisticModel ratingStatisticModel = new RatingStatisticModel();
        ratingStatisticModel.setCompleted(true);
        ratingStatisticModel.setLastRateAtUtc(new Date());
        lenient().when(ratingService.isCompleted(any(), any(), any(), any(), any(), any())).thenReturn(ratingStatisticModel);

        ExportSftpExportDataResponse actualResponse = exportSftpService.getExportData(userId, null, ExportSftpExportType.IMMEDIATELY);

        Assert.assertNotNull(actualResponse);
    }

    @Test
    public void testGetSampleExportDataNotNonCenterModel() {
        String userId = "1";
        String tenantId = "2";
        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole("Teacher");
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("2");

        ExportSftpSettingResponse settingResponse = new ExportSftpSettingResponse();
        ExportSftpSettingMapModel mapModel = JsonUtil.fromJson(mapJson, ExportSftpSettingMapModel.class);
//        settingResponse.setMapModel(mapModel);
        ExportSftpSettingCenterModel centerModel = JsonUtil.fromJson(groupJson, ExportSftpSettingCenterModel.class);
        settingResponse.setCenterModel(centerModel);

        ExportSftpGetCenterGroupResponse centerGroups = new ExportSftpGetCenterGroupResponse();
        List<CenterModel> models = new ArrayList<>();
        CenterModel model = new CenterModel();
        model.setId("1");
        model.setName("CenterName");
        List<GroupModel> groupModels = new ArrayList<>();
        GroupModel gModel = new GroupModel();
        groupModels.add(gModel);

        model.setGroups(groupModels);
        models.add(model);

        centerGroups.setModels(models);

        ExportSftpSetting exportSftpSetting = new ExportSftpSetting();
        exportSftpSetting.setGroupJson(groupJson);
        exportSftpSetting.setMapJson(mapJson);
        exportSftpSetting.setTenantId(tenantId);
        exportSftpSetting.setExportStageIds("75516154-3B50-E411-837D-02DBFC8648CE,77516154-3B50-E411-837D-02DBFC8648CE,78516154-3B50-E411-837D-02DBFC8648CE,72516154-3B50-E411-837D-02DBFC8648CE,81516154-3B50-E411-837D-02DBFC8648CE,80516154-3B50-E411-837D-02DBFC8648CE");
        exportSftpSetting.setUserId("1");

        ScoreTemplateEntity scoreTemplateEntry = new ScoreTemplateEntity();
        scoreTemplateEntry.setDomainLevelsJson("[{\"domainId\":\"measureId\",\"measure\":\"LLD8\",\"measureName\":\"Phonological Awareness\",\"levels\":[{\"id\":\"77F413F8-1EC3-4B70-B6E6-2F153CD936EE\",\"name\":\" Exploring Middle\",\"type\":\"radio\",\"sortIndex\":\"3\",\"value\":\"4\",\"tip\":\"Attends to sounds or elements of language\"},{\"id\":\"976C1402-59B1-4B21-8F16-EAB81A329989\",\"name\":\" Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Demonstrates awareness of variations in sounds\"},{\"id\":\"CFC998E8-3D19-44DB-BE98-664395648330\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Engages actively in play with sounds in words or rhymes, or sings simple songs, or\\nrepeats simple nursery rhymes\"},{\"id\":\"405BA949-F8FF-4CB0-A384-766174446ADF\",\"name\":\"Building Middle\",\"type\":\"radio\",\"sortIndex\":\"6\",\"value\":\"7\",\"tip\":\"Demonstrates awareness of larger units of language (e.g., words, syllables)\"},{\"id\":\"790D37D5-FF13-4DCD-84A3-5847C81323B2\",\"name\":\"Building Later\",\"type\":\"radio\",\"sortIndex\":\"7\",\"value\":\"8\",\"tip\":\"Blends larger units of language (e.g., compound words and syllables) with or without the support of pictures or objects;and segments larger units of language (e.g.\\ncompound words and syllables) with or without the support of pictures or objects\"},{\"id\":\"B16025FA-6DEF-47B4-808E-FAD1C0748308\",\"name\":\"UR (Unable to Rate)\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"},{\"id\":\"63BBC32F-686E-4859-81C5-463C366CD4F7\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"12\",\"value\":\"e\"},{\"id\":\"E72D58A8-167D-4068-8D25-216A174AAEE0\",\"name\":\"Integrating Earlier\",\"type\":\"radio\",\"sortIndex\":\"8\",\"value\":\"9\",\"tip\":\"Segments larger units of language (e.g., compound words and syllables) with or without the support of pictures or objectsBlends smaller units of language (e.g., onsetsand rimes), with or without the support of pictures or objects; and Segments smaller units of language (e.g., onsets and rimes), with or without the support of pictures or objects\"},{\"id\":\"F2A37092-00A9-4592-A182-625B59DEA07B\",\"name\":\"Not yet\",\"type\":\"radio\",\"sortIndex\":\"21\",\"value\":\"n\"}]}]");

        List<CenterGroupModel> centerGroupList = new ArrayList<>();
        CenterGroupModel centerGroupModel = new CenterGroupModel();
        centerGroupModel.setCenterId("1");
        centerGroupModel.setCenterName("Center1");
        centerGroupModel.setGroupId("1");
        centerGroupModel.setGroupName("Group1");

        centerGroupList.add(centerGroupModel);


        HasScoreStudent hasScoreStudent = new HasScoreStudent();
        hasScoreStudent.setEnrollmentId("1");
        hasScoreStudent.setCenterId("1");
        hasScoreStudent.setGroupId("1");
        hasScoreStudent.setStageId("1");
        hasScoreStudent.setLevelId("1");
        List<EnrollmentEntity> enrollmentEntityList = new ArrayList<>();
        EnrollmentEntity enrollmentEntity = new EnrollmentEntity();
        enrollmentEntity.setId("1");
        GroupEntity groupEntity1 = new GroupEntity();
        groupEntity1.setId("1");
        enrollmentEntity.setGroup(groupEntity1);
        enrollmentEntityList.add(enrollmentEntity);

        when(studentDao.getChildrenByGroupIds(anyList())).thenReturn(enrollmentEntityList);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        when(exportSftpSettingDao.getSettingByTenantId(agencyModel.getId())).thenReturn(exportSftpSetting);
        when(userProvider.getUser(userId)).thenReturn(userEntity);
        when(userDao.getCenterGroupByUserIdWithGroups(anyString(), anyList())).thenReturn(centerGroupList);
        when(userProvider.convertCenterGroup(anyString(), anyList(), anyBoolean())).thenReturn(models);
        when(scoreService.getHasScoreStudent(anyList(), anyString(), anyList(), anyString(), anyBoolean(), anyBoolean(), anyList(), anyList(), anyList())).thenReturn(hasScoreStudent);
        List<CenterGroupChildCount> dashboardDataList = new ArrayList<>();
        CenterGroupChildCount centerGroupChildCount = new CenterGroupChildCount();
        centerGroupChildCount.setCenterId("1");
        centerGroupChildCount.setGroupId("1");
        centerGroupChildCount.setChildCount(1);
        centerGroupChildCount.setCenterName("Center1");
        centerGroupChildCount.setGroupName("Group1");

        dashboardDataList.add(centerGroupChildCount);

        when(dashboardDao.getCenterGroupChildByCenterIds(anyList(), anyList(), anyString())).thenReturn(dashboardDataList);

        ExportSftpExportDataResponse actualResponse = exportSftpService.getExportData(userId, null, ExportSftpExportType.SAMPLE);

        Assert.assertNotNull(actualResponse);
    }

    @Test
    public void getSampleData() {
        ExportSftpExportDataResponse exportData = new ExportSftpExportDataResponse();
        List<ExportSftpDataModel> dataModels = new ArrayList<>();
        exportData.setModels(dataModels);

        String userId = "1";
        String tenantId = "2";
        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole("Teacher");
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("2");

        ExportSftpSettingResponse settingResponse = new ExportSftpSettingResponse();
        ExportSftpSettingCenterModel centerModel = JsonUtil.fromJson(groupJson, ExportSftpSettingCenterModel.class);
        settingResponse.setCenterModel(centerModel);

        ExportSftpGetCenterGroupResponse centerGroups = new ExportSftpGetCenterGroupResponse();
        List<CenterModel> models = new ArrayList<>();
        CenterModel model = new CenterModel();
        model.setId("1");
        model.setName("CenterName");
        List<GroupModel> groupModels = new ArrayList<>();
        GroupModel gModel = new GroupModel();
        groupModels.add(gModel);

        model.setGroups(groupModels);
        models.add(model);

        centerGroups.setModels(models);

        ExportSftpSetting exportSftpSetting = new ExportSftpSetting();
        exportSftpSetting.setGroupJson(groupJson);
        exportSftpSetting.setMapJson(mapJson);
        exportSftpSetting.setTenantId(tenantId);
        exportSftpSetting.setExportStageIds("75516154-3B50-E411-837D-02DBFC8648CE,77516154-3B50-E411-837D-02DBFC8648CE,78516154-3B50-E411-837D-02DBFC8648CE,72516154-3B50-E411-837D-02DBFC8648CE,81516154-3B50-E411-837D-02DBFC8648CE,80516154-3B50-E411-837D-02DBFC8648CE");
        exportSftpSetting.setUserId("1");

        ScoreTemplateEntity scoreTemplateEntry = new ScoreTemplateEntity();
        scoreTemplateEntry.setDomainLevelsJson("[{\"domainId\":\"measureId\",\"measure\":\"LLD8\",\"measureName\":\"Phonological Awareness\",\"levels\":[{\"id\":\"77F413F8-1EC3-4B70-B6E6-2F153CD936EE\",\"name\":\" Exploring Middle\",\"type\":\"radio\",\"sortIndex\":\"3\",\"value\":\"4\",\"tip\":\"Attends to sounds or elements of language\"},{\"id\":\"976C1402-59B1-4B21-8F16-EAB81A329989\",\"name\":\" Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Demonstrates awareness of variations in sounds\"},{\"id\":\"CFC998E8-3D19-44DB-BE98-664395648330\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Engages actively in play with sounds in words or rhymes, or sings simple songs, or\\nrepeats simple nursery rhymes\"},{\"id\":\"405BA949-F8FF-4CB0-A384-766174446ADF\",\"name\":\"Building Middle\",\"type\":\"radio\",\"sortIndex\":\"6\",\"value\":\"7\",\"tip\":\"Demonstrates awareness of larger units of language (e.g., words, syllables)\"},{\"id\":\"790D37D5-FF13-4DCD-84A3-5847C81323B2\",\"name\":\"Building Later\",\"type\":\"radio\",\"sortIndex\":\"7\",\"value\":\"8\",\"tip\":\"Blends larger units of language (e.g., compound words and syllables) with or without the support of pictures or objects;and segments larger units of language (e.g.\\ncompound words and syllables) with or without the support of pictures or objects\"},{\"id\":\"B16025FA-6DEF-47B4-808E-FAD1C0748308\",\"name\":\"UR (Unable to Rate)\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"},{\"id\":\"63BBC32F-686E-4859-81C5-463C366CD4F7\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"12\",\"value\":\"e\"},{\"id\":\"E72D58A8-167D-4068-8D25-216A174AAEE0\",\"name\":\"Integrating Earlier\",\"type\":\"radio\",\"sortIndex\":\"8\",\"value\":\"9\",\"tip\":\"Segments larger units of language (e.g., compound words and syllables) with or without the support of pictures or objectsBlends smaller units of language (e.g., onsetsand rimes), with or without the support of pictures or objects; and Segments smaller units of language (e.g., onsets and rimes), with or without the support of pictures or objects\"},{\"id\":\"F2A37092-00A9-4592-A182-625B59DEA07B\",\"name\":\"Not yet\",\"type\":\"radio\",\"sortIndex\":\"21\",\"value\":\"n\"}]}]");

        List<CenterGroupModel> centerGroupList = new ArrayList<>();
        CenterGroupModel centerGroupModel = new CenterGroupModel();
        centerGroupModel.setCenterId("1");
        centerGroupModel.setCenterName("Center1");
        centerGroupModel.setGroupId("1");
        centerGroupModel.setGroupName("Group1");

        centerGroupList.add(centerGroupModel);

        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        when(exportSftpSettingDao.getSettingByTenantId(agencyModel.getId())).thenReturn(exportSftpSetting);
        when(userProvider.getUser(userId)).thenReturn(userEntity);
        when(userDao.getCenterGroupByUserIdWithGroups(anyString(), anyList())).thenReturn(centerGroupList);
        when(userProvider.convertCenterGroup(anyString(), anyList(), anyBoolean())).thenReturn(models);


        ExportSftpDataModel sampleData = exportSftpService.getSampleData("1", "1");
        Assert.assertNotNull(sampleData);
    }

    @Test
    public void testGetExportSetting() {
        String userId = "1";

        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("1");

        ExportSftpSetting exportSftpSetting = new ExportSftpSetting();
        exportSftpSetting.setId("1");
        exportSftpSetting.setMapJson(mapJson);
        exportSftpSetting.setGroupJson(groupJson);
        exportSftpSetting.setTenantId(agencyModel.getId());
        exportSftpSetting.setExportStageIds("1,2,3");
        exportSftpSetting.setAutoExportDate("2023-08-16,2023-08-15");

        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole("Teacher");

        List<CenterModel> models = new ArrayList<>();
        CenterModel model = new CenterModel();
        model.setId("1");
        model.setName("CenterName");
        List<GroupModel> groupModels = new ArrayList<>();
        GroupModel gModel = new GroupModel();
        groupModels.add(gModel);

        model.setGroups(groupModels);
        models.add(model);

        List<ExportSftpRecord> exportRecords = new ArrayList<>();
        ExportSftpRecord record1 = new ExportSftpRecord();
        record1.setScheduledExportTime(new Date());

        exportRecords.add(record1);

        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        when(exportSftpSettingDao.getSettingByTenantId(agencyModel.getId())).thenReturn(exportSftpSetting);
        when(userProvider.getUser(userId)).thenReturn(userEntity);
        when(userProvider.convertCenterGroup(anyString(), anyList(), anyBoolean())).thenReturn(models);
//        when(exportSftpRecordDao.getScheduleRecordsBySettingIdAndExportDates(exportSftpSetting.getId(),
//                Arrays.stream(exportSftpSetting.getAutoExportDate().split(",")).collect(Collectors.toList())))
//                .thenReturn(exportRecords);


        ExportSftpSettingResponse exportSetting = exportSftpService.getExportSetting(userId, ExportSftpExportType.SCHEDULE);
        Assert.assertNotNull(exportSetting);
    }


    @Test
    public void testGetExportSettingExportAll() {
        String userId = "1";

        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("1");

        ExportSftpSetting exportSftpSetting = new ExportSftpSetting();
        exportSftpSetting.setMapJson(mapJson);
        exportSftpSetting.setGroupJson(exportAllGroupJson);
        exportSftpSetting.setTenantId(agencyModel.getId());
        exportSftpSetting.setExportStageIds("1,2,3");

        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole("Teacher");

        List<CenterModel> models = new ArrayList<>();
        CenterModel model = new CenterModel();
        model.setId("1");
        model.setName("CenterName");
        List<GroupModel> groupModels = new ArrayList<>();
        GroupModel gModel = new GroupModel();
        groupModels.add(gModel);

        model.setGroups(groupModels);
        models.add(model);

        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        when(exportSftpSettingDao.getSettingByTenantId(agencyModel.getId())).thenReturn(exportSftpSetting);
        when(userProvider.getUser(userId)).thenReturn(userEntity);
        when(userProvider.convertCenterGroup(anyString(), anyList(), anyBoolean())).thenReturn(models);


        ExportSftpSettingResponse exportSetting = exportSftpService.getExportSetting(userId, ExportSftpExportType.SCHEDULE);

        Assert.assertNotNull(exportSetting);
    }

    @Test
    public void testExportSampleData() {
        String userId = "1";
        ExportSftpSettingResponse exportSetting = new ExportSftpSettingResponse();

        ExportSftpExportDataResponse exportData = new ExportSftpExportDataResponse();
        List<ExportSftpDataModel> dataModels = new ArrayList<>();
        exportData.setModels(dataModels);

        String tenantId = "2";
        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole("Teacher");
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("2");

        ExportSftpSettingResponse settingResponse = new ExportSftpSettingResponse();
        ExportSftpSettingCenterModel centerModel = JsonUtil.fromJson(groupJson, ExportSftpSettingCenterModel.class);
        settingResponse.setCenterModel(centerModel);

        ExportSftpGetCenterGroupResponse centerGroups = new ExportSftpGetCenterGroupResponse();
        List<CenterModel> models = new ArrayList<>();
        CenterModel model = new CenterModel();
        model.setId("1");
        model.setName("CenterName");
        List<GroupModel> groupModels = new ArrayList<>();
        GroupModel gModel = new GroupModel();
        groupModels.add(gModel);

        model.setGroups(groupModels);
        models.add(model);

        centerGroups.setModels(models);

        ExportSftpSetting exportSftpSetting = new ExportSftpSetting();
        exportSftpSetting.setGroupJson(groupJson);
        exportSftpSetting.setMapJson(mapJson);
        exportSftpSetting.setTenantId(tenantId);
        exportSftpSetting.setExportStageIds("75516154-3B50-E411-837D-02DBFC8648CE,77516154-3B50-E411-837D-02DBFC8648CE,78516154-3B50-E411-837D-02DBFC8648CE,72516154-3B50-E411-837D-02DBFC8648CE,81516154-3B50-E411-837D-02DBFC8648CE,80516154-3B50-E411-837D-02DBFC8648CE");
        exportSftpSetting.setUserId("1");

        ScoreTemplateEntity scoreTemplateEntry = new ScoreTemplateEntity();
        scoreTemplateEntry.setDomainLevelsJson("[{\"domainId\":\"measureId\",\"measure\":\"LLD8\",\"measureName\":\"Phonological Awareness\",\"levels\":[{\"id\":\"77F413F8-1EC3-4B70-B6E6-2F153CD936EE\",\"name\":\" Exploring Middle\",\"type\":\"radio\",\"sortIndex\":\"3\",\"value\":\"4\",\"tip\":\"Attends to sounds or elements of language\"},{\"id\":\"976C1402-59B1-4B21-8F16-EAB81A329989\",\"name\":\" Exploring Later\",\"type\":\"radio\",\"sortIndex\":\"4\",\"value\":\"5\",\"tip\":\"Demonstrates awareness of variations in sounds\"},{\"id\":\"CFC998E8-3D19-44DB-BE98-664395648330\",\"name\":\"Building Earlier\",\"type\":\"radio\",\"sortIndex\":\"5\",\"value\":\"6\",\"tip\":\"Engages actively in play with sounds in words or rhymes, or sings simple songs, or\\nrepeats simple nursery rhymes\"},{\"id\":\"405BA949-F8FF-4CB0-A384-766174446ADF\",\"name\":\"Building Middle\",\"type\":\"radio\",\"sortIndex\":\"6\",\"value\":\"7\",\"tip\":\"Demonstrates awareness of larger units of language (e.g., words, syllables)\"},{\"id\":\"790D37D5-FF13-4DCD-84A3-5847C81323B2\",\"name\":\"Building Later\",\"type\":\"radio\",\"sortIndex\":\"7\",\"value\":\"8\",\"tip\":\"Blends larger units of language (e.g., compound words and syllables) with or without the support of pictures or objects;and segments larger units of language (e.g.\\ncompound words and syllables) with or without the support of pictures or objects\"},{\"id\":\"B16025FA-6DEF-47B4-808E-FAD1C0748308\",\"name\":\"UR (Unable to Rate)\",\"type\":\"radio\",\"sortIndex\":\"10\",\"value\":\"u\"},{\"id\":\"63BBC32F-686E-4859-81C5-463C366CD4F7\",\"name\":\"EM\",\"type\":\"checkbox\",\"sortIndex\":\"12\",\"value\":\"e\"},{\"id\":\"E72D58A8-167D-4068-8D25-216A174AAEE0\",\"name\":\"Integrating Earlier\",\"type\":\"radio\",\"sortIndex\":\"8\",\"value\":\"9\",\"tip\":\"Segments larger units of language (e.g., compound words and syllables) with or without the support of pictures or objectsBlends smaller units of language (e.g., onsetsand rimes), with or without the support of pictures or objects; and Segments smaller units of language (e.g., onsets and rimes), with or without the support of pictures or objects\"},{\"id\":\"F2A37092-00A9-4592-A182-625B59DEA07B\",\"name\":\"Not yet\",\"type\":\"radio\",\"sortIndex\":\"21\",\"value\":\"n\"}]}]");

        List<CenterGroupModel> centerGroupList = new ArrayList<>();
        CenterGroupModel centerGroupModel = new CenterGroupModel();
        centerGroupModel.setCenterId("1");
        centerGroupModel.setCenterName("Center1");
        centerGroupModel.setGroupId("1");
        centerGroupModel.setGroupName("Group1");

        centerGroupList.add(centerGroupModel);

        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        when(exportSftpSettingDao.getSettingByTenantId(agencyModel.getId())).thenReturn(exportSftpSetting);
        when(userProvider.getUser(userId)).thenReturn(userEntity);
        when(userDao.getCenterGroupByUserIdWithGroups(anyString(), anyList())).thenReturn(centerGroupList);
        when(userProvider.convertCenterGroup(anyString(), anyList(), anyBoolean())).thenReturn(models);


        IdResponse idResponse = exportSftpService.exportSampleData(userId, null);
        Assert.assertNotNull(idResponse.getId());
    }

    /**
     * 保存 SFTP 导出配置
     * //
     */
//    @Test
//    public void testSaveSettingNotHaveID() {
//        // 模拟请求参数
//        SaveExportSftpSettingRequest request = new SaveExportSftpSettingRequest();
//        request.setMapModel(new ExportSftpSettingMapModel());
//
//        // 模拟 userDao.getUserById() 方法的返回值
//        UserModel user = new UserModel();
//        user.setEmail("<EMAIL>");
//        when(userDao.getUserById(anyString())).thenReturn(user);
//
//        // 模拟 userProvider.getAgencyByUserId() 方法的返回值
//        AgencyModel agency = new AgencyModel();
//        agency.setId("testAgencyId");
//        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agency);
//
//        // 模拟 exportSftpSettingDao.save() 方法的返回值
//        when(exportSftpSettingDao.save(any())).thenReturn(true);
//
//        // 调用 exportSftpService.saveSetting() 方法
//        IdResponse idResponse = exportSftpService.saveExportSftpSetting(userId, request);
//
//        // 验证 exportSftpSettingDao.save() 方法是否被正确地调用了一次
//        verify(exportSftpSettingDao, Mockito.times(1)).save(any());
//
//        // 验证返回的 idResponse 对象是否包含了正确的 ID 值
//        assertNotNull(idResponse.getId());
//    }
    @Test
    public void testSaveExportSftpSetting_newSetting() {
        // Arrange
        String userId = "testUserId";
        AgencyModel agency = new AgencyModel();
        agency.setId("testAgencyId");
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agency);

        SaveExportSftpSettingRequest request = new SaveExportSftpSettingRequest();
        // 设置框架 id
        request.setExportFrameworkId(RateUtil.FRAMEWORKS_OUSD_ID);
        ExportSftpSettingMapModel mapModel = new ExportSftpSettingMapModel();
        request.setMapModel(mapModel);
        request.setAutoExportDateType(AutoExportDateType.CUSTOM_DATE_RANGE.toString());
        request.setAutoExportDate(Arrays.asList("2024-01-01", "2024-01-02"));

        String expectedSettingId = UUID.randomUUID().toString();

        ExportSftpSetting savedSetting = new ExportSftpSetting();
        savedSetting.setId(expectedSettingId);
        savedSetting.setMapJson(JsonUtil.toJson(mapModel));
        savedSetting.setCreateAtUtc(TimeUtil.getUtcNow());
        savedSetting.setUserId(userId);
        savedSetting.setTenantId(agency.getId());
        when(exportSftpSettingDao.getSettingByTenantId(agency.getId())).thenReturn(null);

        // 设置 agency meta data
        AgencyMetaDataEntity metaDataEntity = new AgencyMetaDataEntity();
        // 设置 meta data
        metaDataEntity.setMetaValue(RateUtil.FRAMEWORKS_OUSD_ID);

        // 当机构获取对应的 metadata 的时候，返回上面设置的 metaDataEntity
//        when(agencyDao.getMeta(anyString(), anyString())).thenReturn(metaDataEntity);
        // 当使用框架 id 查询对应框架信息的时候
        FrameworkEntity frameworkEntity = new FrameworkEntity();
        // 设置框架 id 为 OUSD 的框架 id
        frameworkEntity.setId(RateUtil.FRAMEWORKS_OUSD_ID);
        when(frameworkDao.getById(anyString())).thenReturn(frameworkEntity);

        // 模拟 frameworkViewDao.listByFrameworkId 方法调用和返回值
        List<FrameworkViewEntity> allViews = new ArrayList<>(); // 假设为空列表
        // 循环添加 view
        List<String> measureIds = ReportHelper.getSortedMeasureIds();
        // 循环添加 measure id
        for (String measureId : measureIds) {
            FrameworkViewEntity entity = new FrameworkViewEntity();
            // 设置框架 id
            entity.setFrameworkId(RateUtil.FRAMEWORKS_OUSD_ID);
            // 设置测评点 id
            entity.setMeasureId(measureId);
            // 添加进入 views 中
            allViews.add(entity);
        }
        when(frameworkViewDao.listByFrameworkId(anyString())).thenReturn(allViews);

        // 模拟 measureDao.getByIdIn 方法调用和返回值
        List<MeasureEntity> allMeasure = new ArrayList<>(); // 假设为空列表
        // 循环添加 measure
        // 循环添加 measure id
        for (String measureId : measureIds) {
            MeasureEntity entity = new MeasureEntity();
            // 设置 measure id
            entity.setId(measureId);
            // 设置缩写
            entity.setAbbreviation("measureAbbr" + measureId);
            // 设置 name
            entity.setName("measureName" + measureId);

            allMeasure.add(entity);
        }
        when(measureDao.getByIdIn(anyList())).thenReturn(allMeasure);

        // 模拟 domainsMapDao.listDomainMap 方法调用和返回值
        List<DomainsMapEntity> allMappedMeasures = new ArrayList<>(); // 假设为空列表
        when(domainsMapDao.listDomainMap(anyList(), anyList())).thenReturn(allMappedMeasures);

        // Act
        IdResponse actualResponse = exportSftpService.saveExportSftpSetting(userId, request);

        // Assert
        verify(userProvider, times(1)).getAgencyByUserId(userId);
        verify(exportSftpSettingDao, times(1)).getSettingByTenantId(agency.getId());
        verify(exportSftpSettingDao, times(1)).save(any());

        // Assert
        Assert.assertNotNull(actualResponse.getId());
    }

    @Test
    public void testSaveExportSftpSetting_existingSetting() {
        // Arrange

        String userId = "1";
        AgencyModel agency = new AgencyModel();
        agency.setId("2");
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);

        SaveExportSftpSettingRequest request = new SaveExportSftpSettingRequest();
        ExportSftpSettingMapModel mapModel = new ExportSftpSettingMapModel();
        request.setMapModel(mapModel);
        ExportSftpSettingCenterModel centerModel = new ExportSftpSettingCenterModel();
        request.setCenterModel(centerModel);
        Boolean autoExportEnabled = true;
        request.setAutoExportEnabled(autoExportEnabled);
        List<String> autoExportDate = Arrays.asList("2022-01-01", "2022-01-02");
        request.setAutoExportDate(autoExportDate);
        List<String> groupStageIds = Arrays.asList("1", "2", "3");
        request.setGroupStageIds(groupStageIds);
        List<String> notifyEmails = Arrays.asList("<EMAIL>", "<EMAIL>");
        request.setNotifyEmails(notifyEmails);
        request.setOnlyCompleteRating(true);
        request.setAutoExportTime("11:00");
        // 设置框架 id
        request.setExportFrameworkId(RateUtil.FRAMEWORKS_OUSD_ID);
        Date autoExportTime = TimeUtil.translateToUTC(new Date());

        IdResponse expectedResponse = new IdResponse();
        String expectedSettingId = UUID.randomUUID().toString();
        expectedResponse.setId(expectedSettingId);

        ExportSftpSetting savedSetting = new ExportSftpSetting();
        savedSetting.setId(expectedSettingId);
        savedSetting.setMapJson(JsonUtil.toJson(mapModel));
        savedSetting.setGroupJson(JsonUtil.toJson(centerModel));
        savedSetting.setAutoExportEnabled(true);
        savedSetting.setAutoExportDate(String.join(",", autoExportDate));
        savedSetting.setExportStageIds(String.join(",", groupStageIds));
        savedSetting.setNotifyEmails(String.join(",", notifyEmails));
        savedSetting.setOnlyCompleteRating(true);
        savedSetting.setAutoExportTime(TimeUtil.translateToUTC(autoExportTime));
        savedSetting.setUpdateAtUtc(TimeUtil.getUtcNow());
        savedSetting.setTenantId(agency.getId());
        when(exportSftpSettingDao.getSettingByTenantId(agency.getId())).thenReturn(savedSetting);
        when(exportSftpSettingDao.updateById(ArgumentMatchers.any())).thenReturn(true);
        when(cacheService.exist(RedisKeyPrefix.EXPORT_SFTP_SAMPLE_DATA_ALIGNMENT + userId)).thenReturn(true);

        // 设置 agency meta data
        AgencyMetaDataEntity metaDataEntity = new AgencyMetaDataEntity();
        // 设置 meta data
        metaDataEntity.setMetaValue(RateUtil.FRAMEWORKS_OUSD_ID);

        // 当机构获取对应的 metadata 的时候，返回上面设置的 metaDataEntity
//        when(agencyDao.getMeta(anyString(), anyString())).thenReturn(metaDataEntity);
        // 当使用框架 id 查询对应框架信息的时候
        FrameworkEntity frameworkEntity = new FrameworkEntity();
        // 设置框架 id 为 OUSD 的框架 id
        frameworkEntity.setId(RateUtil.FRAMEWORKS_OUSD_ID);
        when(frameworkDao.getById(anyString())).thenReturn(frameworkEntity);

        // 模拟 frameworkViewDao.listByFrameworkId 方法调用和返回值
        List<FrameworkViewEntity> allViews = new ArrayList<>(); // 假设为空列表
        // 循环添加 view
        List<String> measureIds = ReportHelper.getSortedMeasureIds();
        // 循环添加 measure id
        for (String measureId : measureIds) {
            FrameworkViewEntity entity = new FrameworkViewEntity();
            // 设置框架 id
            entity.setFrameworkId(RateUtil.FRAMEWORKS_OUSD_ID);
            // 设置测评点 id
            entity.setMeasureId(measureId);
            // 添加进入 views 中
            allViews.add(entity);
        }
        when(frameworkViewDao.listByFrameworkId(anyString())).thenReturn(allViews);

        // 模拟 measureDao.getByIdIn 方法调用和返回值
        List<MeasureEntity> allMeasure = new ArrayList<>(); // 假设为空列表
        // 循环添加 measure
        // 循环添加 measure id
        for (String measureId : measureIds) {
            MeasureEntity entity = new MeasureEntity();
            // 设置 measure id
            entity.setId(measureId);
            // 设置缩写
            entity.setAbbreviation("measureAbbr" + measureId);
            // 设置 name
            entity.setName("measureName" + measureId);
            // 设置 当前测评点是可以评测的测评点
            entity.setIsNode("0");

            allMeasure.add(entity);
        }
        when(measureDao.getByIdIn(anyList())).thenReturn(allMeasure);

        // 模拟 domainsMapDao.listDomainMap 方法调用和返回值
        List<DomainsMapEntity> allMappedMeasures = new ArrayList<>(); // 假设为空列表
        // 循环添加 domain map
        // 循环添加 measure id
        for (String measureId : measureIds) {
            DomainsMapEntity entity = new DomainsMapEntity();
            // 设置 domain map id
            entity.setId(UUID.randomUUID().toString());
            // 设置要转化的框架的 id
            entity.setFromId(measureId);
            // 设置从哪个框架转化
            entity.setToId(measureId);
            // 添加进入 mapped measures 中
            allMappedMeasures.add(entity);
        }
        when(domainsMapDao.listDomainMap(anyList(), anyList())).thenReturn(allMappedMeasures);

        IdResponse actualResponse = exportSftpService.saveExportSftpSetting(userId, request);

        Assert.assertNotNull(actualResponse.getId());
        verify(userProvider, times(1)).getAgencyByUserId(userId);
        verify(exportSftpSettingDao, times(1)).getSettingByTenantId(agency.getId());
        verify(exportSftpSettingDao, times(1)).updateById(ArgumentMatchers.any());
    }

    @Test
    public void testGetPageExportSftpRecords() {
        // Arrange
        String userId = "1";
        Integer pageNum = 1;
        Integer pageSize = 10;
        AgencyModel agency = new AgencyModel();
        agency.setId("2");
        List<com.learninggenie.common.data.entity.CenterEntity> centers = new ArrayList<>();
        com.learninggenie.common.data.entity.CenterEntity center = new com.learninggenie.common.data.entity.CenterEntity();
        center.setId("3");
        center.setCenterTimeZone("Asia/Shanghai");
        centers.add(center);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);

        List<ExportSftpRecord> exportSftpRecordList = new ArrayList<>();
        ExportSftpRecord record = new ExportSftpRecord();
        record.setId(UUID.randomUUID().toString());

        record.setCompleteAtUtc(TimeUtil.getUtcNow());
        record.setSchoolYear("2021-2022");
        record.setCenterCount(5);
        record.setGroupCount(10);
        record.setFileUrl("http://example.com/file1");
        record.setExportStatus("SUCCESS");
        record.setGroupJson("{\"centerIds\":[\"1\",\"2\",\"3\"]}");
        exportSftpRecordList.add(record);
        IPage<ExportSftpRecord> page = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageNum, pageSize);
        page.setRecords(exportSftpRecordList);
        when(exportSftpRecordDao.pageByExportTypeAndAgencyId(Mockito.any(), Mockito.anyList(), Mockito.eq(agency.getId()))).thenReturn(page);

        PageList<ExportSftpRecordResponse> pageList = new PageList<>();
        pageList.setPageNum((long) pageNum);
        pageList.setPageSize((long) pageSize);

        ExportSftpRecordResponse expectedResponse = new ExportSftpRecordResponse();
        expectedResponse.setId(record.getId());
        expectedResponse.setExportBy("SFTP - SIS");
        expectedResponse.setExportTo("SFTP");
        expectedResponse.setExportDate(record.getCompleteAtUtc());
        expectedResponse.setSchoolYear(record.getSchoolYear());
        expectedResponse.setExportCenterCount(record.getCenterCount());
        expectedResponse.setExportGroupCount(record.getGroupCount());
        expectedResponse.setFileUrl(record.getFileUrl());
        expectedResponse.setExportStatus(record.getExportStatus());

        List<ExportSftpRecordResponse> list = Collections.singletonList(expectedResponse);
        pageList.setRecords(list);


        PageList<ExportSftpRecordResponse> records = exportSftpService.getPageExportSftpRecords(userId, pageNum, pageSize);

        verify(userProvider, times(1)).getAgencyByUserId(userId);
        Assert.assertEquals(pageList.getRecords().size(), records.getRecords().size());
    }

    @Test
    public void testGetExportRecordDetails() {
        // Arrange
        String recordId = UUID.randomUUID().toString();

        ExportSftpRecord exportSftpRecord = new ExportSftpRecord();
        exportSftpRecord.setId(recordId);
        exportSftpRecord.setCompleteAtUtc(new Date());
        exportSftpRecord.setSchoolYear("2021-2022");
        exportSftpRecord.setCenterCount(5);
        exportSftpRecord.setGroupCount(10);
        exportSftpRecord.setFileUrl("http://example.com/file1");
        exportSftpRecord.setExportStatus("SUCCESS");
        exportSftpRecord.setGroupJson("{\"centerIds\":[\"1\",\"2\",\"3\"]}");
        exportSftpRecord.setExportStageIds("1,2,3");
        exportSftpRecord.setSampleJson("{\"tableData\":[{\"id\":\"1\",\"name\":\"Tom\"},{\"id\":\"2\",\"name\":\"Jerry\"}],\"chartData\":[]}");
        when(this.exportSftpRecordDao.getById(recordId)).thenReturn(exportSftpRecord);

        List<ContentsGroupStage> contentsGroupStages = new ArrayList<>();
        ContentsGroupStage contentsGroupStage1 = new ContentsGroupStage();
        contentsGroupStage1.setId("1");
        contentsGroupStage1.setGradeName("grandName1");
        contentsGroupStage1.setLangKey("key1");
        contentsGroupStage1.setName("name1");
        ContentsGroupStage contentsGroupStage2 = new ContentsGroupStage();
        contentsGroupStage1.setId("2");
        contentsGroupStage1.setGradeName("grandName2");
        contentsGroupStage1.setLangKey("key2");
        contentsGroupStage1.setName("name2");
        ContentsGroupStage contentsGroupStage3 = new ContentsGroupStage();
        contentsGroupStage1.setId("3");
        contentsGroupStage1.setGradeName("grandName3");
        contentsGroupStage1.setLangKey("key3");
        contentsGroupStage1.setName("name3");
        contentsGroupStages.add(contentsGroupStage1);
        contentsGroupStages.add(contentsGroupStage2);
        contentsGroupStages.add(contentsGroupStage3);

        ExportSftpRecordResponse expectedRecordResponse = new ExportSftpRecordResponse();
        expectedRecordResponse.setId(exportSftpRecord.getId());
        expectedRecordResponse.setExportBy("SFTP - SIS");
        expectedRecordResponse.setExportTo("SFTP");
        expectedRecordResponse.setExportDate(exportSftpRecord.getCompleteAtUtc());
        expectedRecordResponse.setSchoolYear(exportSftpRecord.getSchoolYear());
        expectedRecordResponse.setExportCenterCount(exportSftpRecord.getCenterCount());
        expectedRecordResponse.setExportGroupCount(exportSftpRecord.getGroupCount());
        expectedRecordResponse.setFileUrl(exportSftpRecord.getFileUrl());
        expectedRecordResponse.setExportStatus(exportSftpRecord.getExportStatus());

        ExportSftpSettingCenterModel centerModel = new ExportSftpSettingCenterModel();
        centerModel.setExportGradeStageIds(Arrays.asList("1", "2", "3"));
        expectedRecordResponse.setExportCenterCount(3);

        ExportSftpSettingResponse expectedSettingResponse = new ExportSftpSettingResponse();
        expectedSettingResponse.setCenterModel(centerModel);
        expectedSettingResponse.setExportStageIds("1,2,3");
        expectedSettingResponse.setExportGrades("Grade 1, Grade 2, Grade 3");

        ExportSftpDataModel expectedDataModel = new ExportSftpDataModel();
        expectedDataModel.setTableData(Arrays.asList(
                Collections.singletonMap("id", "1"),
                Collections.singletonMap("name", "Tom"),
                Collections.singletonMap("id", "2"),
                Collections.singletonMap("name", "Jerry")
        ));

        ExportSftpRecordDetailResponse expectedResponse = new ExportSftpRecordDetailResponse();
        expectedResponse.setRecordResponse(expectedRecordResponse);
        expectedResponse.setSettingResponse(expectedSettingResponse);
        expectedResponse.setSampleDataModel(expectedDataModel);

        // Act
        ExportSftpRecordDetailResponse actualResponse = exportSftpService.getExportRecordDetails(recordId);

        // Assert
        Assertions.assertNotNull(actualResponse);
        verify(exportSftpRecordDao, times(1)).getById(recordId);
        verifyNoMoreInteractions(exportSftpRecordDao, contentsGroupStageDao);
    }

    @Test
    public void testSampleExportType() {
        ExportSftpSettingMapModel mapModel = new ExportSftpSettingMapModel();
        ExportSftpGradeRatingMapModel gradeRatingMapModel = new ExportSftpGradeRatingMapModel();
        gradeRatingMapModel.setStageId("1");
        mapModel.setGradeRatingMapModels(Arrays.asList(gradeRatingMapModel));
        ExportSftpSetting setting = new ExportSftpSetting();
        setting.setId("1");
        setting.setUserId("1");
        setting.setTenantId("2");
        setting.setExportStageIds("1,2,3");
        setting.setMapJson(JsonUtil.toJson(mapModel));

        ExportSftpGetCenterGroupResponse centerGroups = new ExportSftpGetCenterGroupResponse();
        centerGroups.setModels(Arrays.asList(new CenterModel()));

        ExportSftpSettingResponse result = new ExportSftpSettingResponse();
        result.setSettingId("1");
        result.setAutoExportEnabled(false);
        result.setExportStageIds(setting.getExportStageIds());
        assertNotNull(result);
        assertNotNull(result.getSettingId());
        Assertions.assertTrue(CollectionUtils.isEmpty(result.getNotifyEmails()));
        Assertions.assertFalse(result.getAutoExportEnabled());
        Assertions.assertNull(result.getAutoExportTime());
        Assertions.assertEquals("1,2,3", result.getExportStageIds());
    }

//    @Test
//    public void testNonSampleExportType() {
//        ExportSftpSetting setting = new ExportSftpSetting();
//        ExportSftpSettingCenterModel centerModel = new ExportSftpSettingCenterModel();
//        centerModel.setExportGradeStageIds(Arrays.asList("1", "2"));
//        ExportSftpGroupModel groupModel = new ExportSftpGroupModel();
//        groupModel.setGroupId("1");
//        groupModel.setGroupName("group1");
//        ExportSftpCenterModel exportSftpCenterModel = new ExportSftpCenterModel();
//        exportSftpCenterModel.setGroupModels(Collections.singletonList(groupModel));
//        centerModel.setCenterModels(Collections.singletonList(exportSftpCenterModel));
//        setting.setGroupJson(JsonUtil.toJson(centerModel));
//        setting.setAutoExportEnabled(false);
//        ContentsGroupStage cgs1 = new ContentsGroupStage();
//        cgs1.setId("1");
//        cgs1.setGradeName("Grade1");
//        ContentsGroupStage cgs2 = new ContentsGroupStage();
//        cgs2.setId("2");
//        cgs2.setGradeName("Grade2");
//        when(contentsGroupStageDao.listByIds(Arrays.asList("1", "2"))).thenReturn(Arrays.asList(cgs1, cgs2));
//        List<String> groupIds = Arrays.asList("123","456");
//        List<com.learninggenie.common.data.model.GroupEntity> centerGroups = new ArrayList<>();
////        when(userDao.getCenterGroupByUserIdWithGroups(agencyModel.getId(), groupStageIds)).thenReturn(centerGroupList);
//        ExportSftpSettingResponse result = new ExportSftpSettingResponse();
//        result.setAutoExportEnabled(setting.getAutoExportEnabled());
//        result.setExportStageIds("1,2,3");
//        result.setCenterModel(centerModel);
//        assertNotNull(result);
//        Assertions.assertTrue(CollectionUtils.isEmpty(result.getNotifyEmails()));
//        Assertions.assertFalse(result.getAutoExportEnabled());
//        Assertions.assertNull(result.getAutoExportTime());
//        Assertions.assertEquals("1,2,3", result.getExportStageIds());
//        assertNotNull(result.getCenterModel());
//        Assertions.assertEquals(centerModel.getExportGradeStageIds(), result.getCenterModel().getExportGradeStageIds());
//        //Assertions.assertEquals(centerModel.getCenterModels().size(), result的CenterModel列表大小相同。
//        // 验证GroupDao调用了一次
////        verify(groupDao, times(1)).getCenterGroupByCenterIdWithGroupId(groupIds);
//    }

    @Test
    public void testAutoExportEnabled() {
        ExportSftpSetting setting = new ExportSftpSetting();
        setting.setId("1");
        setting.setUserId("1");
        setting.setTenantId("2");
        setting.setExportStageIds("1,2,3");
        setting.setAutoExportEnabled(true);
        setting.setAutoExportTime(new Date());
        ExportSftpSettingResponse result = new ExportSftpSettingResponse();
        result.setAutoExportEnabled(setting.getAutoExportEnabled());
        assertNotNull(result);
        Assertions.assertTrue(result.getAutoExportEnabled());
    }

    @Test
    public void testNotifyEmails() {
        ExportSftpSetting setting = new ExportSftpSetting();
        setting.setId("1");
        setting.setUserId("1");
        setting.setTenantId("2");
        setting.setExportStageIds("1,2,3");
        setting.setNotifyEmails("<EMAIL>,<EMAIL>");

        ExportSftpSettingResponse result = new ExportSftpSettingResponse();
        result.setNotifyEmails(Arrays.stream(setting.getNotifyEmails().split(",")).collect(Collectors.toList()));
        assertNotNull(result);
        Assertions.assertEquals(Arrays.asList("<EMAIL>", "<EMAIL>"), result.getNotifyEmails());
    }

    /**
     * 测试使用空用户 id 打开其他框架导出
     */
    @Test
    public void testGetOtherFrameworkExportOpen_WithEmptyUserId() {
        // 设置 userProvider.getCurrentUserId 方法的返回值
        String currentUserId = "mocked-user-id";
        when(userProvider.getCurrentUserId()).thenReturn(currentUserId);

        // 设置 userProvider.getAgencyByUserId 方法的返回值为空
        when(userProvider.getAgencyByUserId(eq(currentUserId))).thenReturn(null);

        // 验证 BusinessException 是否被抛出
        // 调用 getOtherFrameworkExportOpen 方法
        // 执行
        BusinessException noAgencyException = Assertions.assertThrows(BusinessException.class, () -> {
            exportSftpService.getOtherFrameworkExportOpen(null);
        });

        // 验证 userProvider.getCurrentUserId 方法被调用一次
        verify(userProvider, times(1)).getCurrentUserId();

        // 验证 userProvider.getAgencyByUserId 方法被调用一次
        verify(userProvider, times(1)).getAgencyByUserId(eq(currentUserId));

        // 验证返回结果的属性
        Assertions.assertEquals(ErrorCode.AGENCY_NOT_FOUND, noAgencyException.getErrorCode());
    }

    /**
     * 测试未经许可打开其他框架导出
     */
    @Test
    public void testGetOtherFrameworkExportOpen_WithNoPermission() {
        // 设置 userProvider.getAgencyByUserId 方法的返回值
        AgencyModel agency = new AgencyModel();
        // 设置 agency 的 id 属性
        agency.setId("agencyId");
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agency);

        // 设置 agencyDao.getMeta 方法的返回值为空
        when(agencyDao.getMeta(anyString(), anyString())).thenReturn(null);

        // 调用 getOtherFrameworkExportOpen 方法
        GetOtherFrameworkExportOpenResponse otherFrameworkExportOpen = exportSftpService.getOtherFrameworkExportOpen("mocked-user-id");

        // 验证 userProvider.getAgencyByUserId 方法被调用一次
        verify(userProvider, times(1)).getAgencyByUserId(anyString());

        // 验证 agencyDao.getMeta 方法被调用一次
        verify(agencyDao, times(1)).getMeta(anyString(), anyString());

        // 验证返回结果的属性
        Assertions.assertFalse(otherFrameworkExportOpen.isShowOUSD());
    }

    /**
     * 测试使用ousd permission 打开其他框架导出
     */
    @Test
    public void testGetOtherFrameworkExportOpen_WithOUSDPermission() {
        // 设置 userProvider.getAgencyByUserId 方法的返回值
        AgencyModel agency = new AgencyModel();
        // 设置 agency 的 id 属性
        agency.setId("agencyId");
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agency);

        // 设置 agencyDao.getMeta 方法的返回值
        AgencyMetaDataEntity agencyDaoMeta = new AgencyMetaDataEntity();
        agencyDaoMeta.setMetaValue("OUSD");
        when(agencyDao.getMeta(anyString(), anyString())).thenReturn(agencyDaoMeta);

        // 调用 getOtherFrameworkExportOpen 方法
        GetOtherFrameworkExportOpenResponse otherFrameworkExportOpen = exportSftpService.getOtherFrameworkExportOpen("mocked-user-id");

        // 验证 userProvider.getAgencyByUserId 方法被调用一次
        verify(userProvider, times(1)).getAgencyByUserId(anyString());

        // 验证 agencyDao.getMeta 方法被调用一次
        verify(agencyDao, times(1)).getMeta(anyString(), anyString());

        // 验证返回结果的属性
        Assertions.assertFalse(otherFrameworkExportOpen.isShowOUSD());
    }

    /**
     * 测试在没有ousd permission情况下打开其他框架导出
     */
    @Test
    public void testGetOtherFrameworkExportOpen_WithoutOUSDPermission() {
        // 设置 userProvider.getAgencyByUserId 方法的返回值
        AgencyModel agency = new AgencyModel();
        // 设置 agency 的 id 属性
        agency.setId("agencyId");
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agency);

        // 设置 agencyDao.getMeta 方法的返回值
        AgencyMetaDataEntity agencyDaoMeta = new AgencyMetaDataEntity();
        agencyDaoMeta.setMetaValue("SOME_OTHER_VALUE");
        when(agencyDao.getMeta(anyString(), anyString())).thenReturn(agencyDaoMeta);

        // 调用 getOtherFrameworkExportOpen 方法
        GetOtherFrameworkExportOpenResponse showOUSD = exportSftpService.getOtherFrameworkExportOpen("mocked-user-id");

        // 验证 userProvider.getAgencyByUserId 方法被调用一次
        verify(userProvider, times(1)).getAgencyByUserId(anyString());

        // 验证 agencyDao.getMeta 方法被调用一次
        verify(agencyDao, times(1)).getMeta(anyString(), anyString());

        // 验证返回结果的属性
        Assertions.assertFalse(showOUSD.isShowOUSD());
    }

    /**
     * 测试导出测评点缩写信息
     */
    @Test
    public void testExportMeasureAbbr() {
        // 定义原始测评点
        MeasureEntity measureEntity = new MeasureEntity();
        // 设置 measure id
        String measureId = "measureId";
        measureEntity.setId(measureId);
        // 设置缩写
        measureEntity.setAbbreviation("measureAbbr" + measureId);
        // 设置 name
        measureEntity.setName("measureName" + measureId);
        // 设置 当前测评点是可以评测的测评点
        measureEntity.setIsNode("0");
        // 定义测评点 id 和 测评点缩写的对应关系
        Map<String, String> measureMap = new HashMap<>();
        // 添加对应的关系
        measureMap.put(measureId, "testAbbr");
        // 定义列数据 map
        Map<String, String> columnData = new HashMap<>();
        // 调用要测试的方法
        exportSftpService.exportMeasureAbbr(measureEntity, measureMap, columnData);
        // 验证 columnData 的值
        Assertions.assertNotNull(columnData.get(ExportSftpLgColumnEnum.MEASURE.getName()));
        // 验证对应的值是否正确
        Assertions.assertEquals(columnData.get(ExportSftpLgColumnEnum.MEASURE.getName()), "testAbbr");
    }

    /**
     * 测试获取学生ID
     */
    @Test
    public void testGetStudentIds() {
        // 定义参数
        // 定义 center model
        List<ExportSftpCenterModel> centerModel = new ArrayList<>();
        // 定义单独的 center 对象
        ExportSftpCenterModel model = new ExportSftpCenterModel();
        // 设置 center 的属性
        model.setCenterId("CenterId");
        // 设置 center name
        model.setCenterName("CenterName");
        // 设置班级
        List<ExportSftpGroupModel> groupModels = Lists.newArrayList();
        // 定义单独的班级对象
        ExportSftpGroupModel groupModel = new ExportSftpGroupModel();
        // 设置班级 Id
        groupModel.setGroupId("GroupId");
        // 设置班级名称
        groupModel.setGroupName("GroupName");

        // 添加进入班级列表中
        groupModels.add(groupModel);
        // 添加进入 center model 中
        model.setGroupModels(groupModels);

        centerModel.add(model);
        HashMap<String, List<String>> cacheMap = new HashMap<>();
        // 定义缓存值
        cacheMap.put("1", Lists.newArrayList("1", "2", "3"));
        cacheMap.put("GroupId", Lists.newArrayList("1", "2", "3"));

        // 调用要测试的方法
        Map<String, List<String>> exportSftpServiceStudentIds = exportSftpService.getStudentIds(centerModel, exportSftpService.getCurrentSchoolYear(), cacheMap, false, "agencyId", "agencyId");
        // 断言结果
        Assertions.assertNotNull(exportSftpServiceStudentIds);
    }


    /**
     * 测试转换周期的框架 Id
     */
    @Test
    public void testConvertPeriodDomainId() {
        // 定义周期信息
        EnrollmentPeriodEntity enrollmentPeriodEntity = new EnrollmentPeriodEntity();
        // 设置周期属于哪个小孩
        String enrollmentId = "enrollment123";
        enrollmentPeriodEntity.setEnrollmentId(enrollmentId);

        // 定义小孩 Id 和小孩的对应关系
        Map<String, List<EnrollmentModel>> enrollmentIdModelListMap = new HashMap<>();
        // 定义小孩的信息
        List<EnrollmentModel> enrollmentModelList = new ArrayList<>();
        // 定义小孩的信息
        EnrollmentModel enrollmentModel = new EnrollmentModel();
        // 设置小孩的框架
        String frameworkId = "framework123";
        enrollmentModel.setFrameworkId(frameworkId);
        // 添加进入小孩的信息列表中
        enrollmentModelList.add(enrollmentModel);
        // 将小孩的 Id 和小孩的映射关系保存到 map 中
        enrollmentIdModelListMap.put(enrollmentId, enrollmentModelList);

        // 调用测试方法
        exportSftpService.convertPeriodDomainId(enrollmentPeriodEntity, enrollmentIdModelListMap);

        // 验证 enrollmentPeriodEntity 的 domainId 是否被正确设置
        assertEquals(frameworkId, enrollmentPeriodEntity.getDomainId());
    }

    /**
     * 测试将教师元数据转换为教师的 metaValue 属性
     */
    @Test
    public void testConvertTeacherMetadataToTeacher() {
        // 定义老师
        UserModel teacher = new UserModel();
        // 定义 teacherId
        String teacherId = "teacherId";
        // 设置 teacherId
        teacher.setId(teacherId);

        // 定义老师的 metadata
        List<UsersMetaDataEntity> teacherMetadata = new ArrayList<>();
        // 定义老师的 metadata 的值
        UsersMetaDataEntity usersMetaDataEntity = new UsersMetaDataEntity();
        // 定义 metaValue
        String metaValue = "metaValue";
        // 设置 metaValue
        usersMetaDataEntity.setMetaValue(metaValue);
        // 定义 user
        com.learninggenie.common.data.model.UserEntity user = new com.learninggenie.common.data.model.UserEntity();
        // 设置 user 的 id
        user.setId(teacherId);
        // 设置 user 的 metadata
        usersMetaDataEntity.setUser(user);
        // 设置 teacherMetadata 的值
        usersMetaDataEntity.setUserId(teacherId);

        // 添加进入 teacherMetadata 中
        teacherMetadata.add(usersMetaDataEntity);

        // 调用测试方法
        exportSftpService.convertTeacherMetadataToTeacher(teacher, teacherMetadata);
        // 验证 teacher 的 metadata 是否被正确设置
        assertEquals(metaValue, teacher.getMetaValue());
    }

    /**
     * 测试通过当前框架获取教师的数量
     */
    @Test
    public void testGetTeacherSizeByCurrentFramework() {
        // 模拟输入参数
        // 定义所有的 班级和对应的 enrollmentId
        Map<String, List<String>> allEnrollmentIdsMap = new HashMap<>();
        // 定义所有的 group 和对应的 stage
        Map<String, List<String>> groupStageMap = new HashMap<>();
        // 定义所有的 enrollmentId 和对应的 snapshot
        Map<String, List<SnapshotEntity>> enrollmentIdFrameworkIdSnapshotMap = new HashMap<>();
        // 定义所有的 enrollmentId 和对应的 score
        Map<String, List<StudentScoreModel>> studentIdFrameworkIdScoreListMap = new HashMap<>();
        // 定义当前的 stageId
        String currentStageId = "currentStageId";
        // 定义当前的 frameworkId
        String frameworkId = "frameworkId";
        // 定义当前的 frameworkGroupIds
        List<String> frameworkGroupIds = Arrays.asList("group1", "group2");
        // 定义最终的 group 和对应的老师列表
        Map<String, List<UserModel>> finalGroupIdTeacherListMap = new HashMap<>();
        // 定义最终的 teacherId 和对应的老师列表
        Map<String, List<UserModel>> finalTeacherIdTeacherListMap = new HashMap<>();
        // 定义最终的 teacherId 和对应的 metadata 列表
        Map<String, List<UsersMetaDataEntity>> finalUserIdMetaDataMap = new HashMap<>();

        // 模拟至少有一个教师满足所有的过滤条件
        // 添加 group 和对应的 enrollmentId
        groupStageMap.put("group1", Arrays.asList("currentStageId"));
        // 添加 group 和对应的 enrollmentId
        allEnrollmentIdsMap.put("group1", Arrays.asList("enrollment1"));
        // 添加 enrollmentId 和对应的 snapshot
        enrollmentIdFrameworkIdSnapshotMap.put("enrollment1_" + frameworkId, Arrays.asList(new SnapshotEntity()));
        // 添加 enrollmentId 和对应的 score
        studentIdFrameworkIdScoreListMap.put("enrollment1_" + frameworkId, Arrays.asList(new StudentScoreModel()));
        // 定义 teacher
        UserModel model = new UserModel();
        // 设置 teacher 的 id
        model.setId("teacher1");
        // 设置 teacher 的 displayName
        model.setDisplayName("teacher1");
        // 添加进入 finalGroupIdTeacherListMap 中
        finalGroupIdTeacherListMap.put("group1", Arrays.asList(model));
        // 添加进入 finalTeacherIdTeacherListMap 中
        finalTeacherIdTeacherListMap.put("TEACHER1", Arrays.asList(model));
        // 定义 teacher 的 metadata
        UsersMetaDataEntity usersMetaDataEntity = new UsersMetaDataEntity();
        // 设置 teacher 的 metadata
        usersMetaDataEntity.setMetaValue("metaValue");
        // 添加进入 finalUserIdMetaDataMap 中
        finalUserIdMetaDataMap.put("TEACHER1", Arrays.asList(usersMetaDataEntity));

        // 调用方法
        Integer result = exportSftpService.getTeacherSizeByCurrentFramework(allEnrollmentIdsMap, groupStageMap, enrollmentIdFrameworkIdSnapshotMap, studentIdFrameworkIdScoreListMap, currentStageId, frameworkId, frameworkGroupIds, finalGroupIdTeacherListMap, finalTeacherIdTeacherListMap, finalUserIdMetaDataMap);

        // 验证结果
        assertEquals(1, result);
    }

    @Test
    public void verifyDataShouldReturnSuccessResponseWhenDataExists() {
        // Mocking dependencies
        when(userProvider.getTimezoneOffsetNum()).thenReturn(0);
        when(userProvider.getCurrentUserId()).thenReturn("userId");
        when(userProvider.getCurrentAgencyId()).thenReturn("agencyId");
        AgencyEntity agency = new AgencyEntity();
        agency.setName("agencyName");
        when(agencyDao.getById(anyString())).thenReturn(agency);
        AgencyMetaDataEntity agencyMetaData = new AgencyMetaDataEntity();
        agencyMetaData.setMetaValue("true");
        when(agencyDao.getMeta(anyString(), anyString())).thenReturn(agencyMetaData);
        ExportSftpSetting exportSftpSetting = new ExportSftpSetting();
        exportSftpSetting.setExportStageIds("1,2,3");
        exportSftpSetting.setId("settingId");
        exportSftpSetting.setTenantId("agencyId");
        exportSftpSetting.setMapJson(mapJson);
        exportSftpSetting.setGroupJson(groupJson);
        when(exportSftpSettingDao.getSettingByTenantId(anyString())).thenReturn(exportSftpSetting);
        when(studentDao.getChildIdsByGroupIds(anyList())).thenReturn(Arrays.asList("child1", "child2", "child3"));

        StatisticsPaUpdateRecordDaoImpl daoSpy = spy(statisticsPaUpdateRecordDao);
        ReflectionTestUtils.setField(daoSpy, "baseMapper", statisticsPaUpdateRecordMapper);
        // 注入属性
        ReflectionTestUtils.setField(exportSftpService, "statisticsPaUpdateRecordDao", daoSpy);

        final LambdaUpdateChainWrapper<StatisticsPaUpdateRecordEntity> lambdaUpdate = new LambdaUpdateChainWrapper<>(statisticsPaUpdateRecordMapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaUpdateChain(statisticsPaUpdateRecordMapper)).thenReturn(lambdaUpdate);
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("agencyId");
        when(userProvider.getAgencyByUserId(any())).thenReturn(agencyModel);
        UserEntity userEntity = new UserEntity();
        userEntity.setId("userId");
        userEntity.setRole("Teacher");
        when(userProvider.getUser(any())).thenReturn(userEntity);
        AgenciesSftpuser mockSftpUser = new AgenciesSftpuser();
        mockSftpUser.setUserName("username");
        mockSftpUser.setPassword("password");

        when(agenciesSftpuserDao.getExportUserByUsername(anyString(), anyString())).thenReturn(mockSftpUser);

        // Call the method under test
        exportSftpService.verifyData();

        // 等待 5 秒，线程内代码覆盖
        try {
            Thread.sleep(5000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        // Verify the interactions
        verify(userDao, times(1)).createUserFile(any());
    }

}

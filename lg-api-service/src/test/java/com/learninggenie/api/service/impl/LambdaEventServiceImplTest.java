package com.learninggenie.api.service.impl;

import com.learninggenie.api.model.LambdaEventRequest;
import com.learninggenie.api.model.SuccessResponse;
import com.learninggenie.api.model.sqs.MessageAttributeValue;
import com.learninggenie.api.model.sqs.MessageModel;

import com.learninggenie.api.service.lesson2.LessonService;
import com.learninggenie.common.data.enums.sqs.MessageType;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class LambdaEventServiceImplTest {

    @InjectMocks
    private LambdaEventServiceImpl lambdaEventService;

    @Mock
    private LessonService lessonService;


    /**
     * 测试生成课程
     */
    @Test
    public void testGenerateLesson() {
        // 模拟测试数据
        LambdaEventRequest request = new LambdaEventRequest();
        MessageModel message = new MessageModel();
        message.setBody("testTaskId");
        MessageAttributeValue type = new MessageAttributeValue();
        // 设置消息类型为生成课程
        type.setStringValue(MessageType.GENERATE_LESSON.toString());
        MessageAttributeValue userInfo = new MessageAttributeValue();
        userInfo.setStringValue("userId");
        Map<String, MessageAttributeValue> attributes = new HashMap<>();
        attributes.put("Type", type);
        attributes.put("userId", userInfo);
        message.setMessageAttributes(attributes);
        request.setRecords(Collections.singletonList(message));

        // 调用测试方法
        SuccessResponse response = lambdaEventService.handleLambdaSqsEvent(request);

        // 验证测试结果
        assertTrue(response.isSuccess());
        verify(lessonService, times(1)).generateLesson("testTaskId");
    }

    /**
     * 测试改编课程
     */
    @Test
    public void testAdaptLesson() {
        // 模拟测试数据
        LambdaEventRequest request = new LambdaEventRequest();
        MessageModel message = new MessageModel();
        message.setBody("testTaskId");
        MessageAttributeValue type = new MessageAttributeValue();
        // 设置消息类型为生成课程
        type.setStringValue(MessageType.ADAPT_LESSON.toString());
        MessageAttributeValue userInfo = new MessageAttributeValue();
        userInfo.setStringValue("userId");
        Map<String, MessageAttributeValue> attributes = new HashMap<>();
        attributes.put("Type", type);
        attributes.put("userId", userInfo);
        message.setMessageAttributes(attributes);
        request.setRecords(Collections.singletonList(message));

        // 调用测试方法
        SuccessResponse response = lambdaEventService.handleLambdaSqsEvent(request);

        // 验证测试结果
        assertTrue(response.isSuccess());
        verify(lessonService, times(1)).adaptLesson("testTaskId");
    }

}
package com.learninggenie.api.service.impl;

import com.google.api.client.http.HttpHeaders;
import com.google.api.client.http.HttpResponseException;
import com.google.api.client.http.HttpTransport;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.jackson2.JacksonFactory;
import com.google.api.services.customsearch.v1.CustomSearchAPI;
import com.google.api.services.customsearch.v1.model.Result;
import com.google.api.services.customsearch.v1.model.Search;
import com.learninggenie.api.model.SearchImageResponse;
import com.learninggenie.api.model.SearchImageModel;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class GoogleCustomSearchServiceImplTest {

    @InjectMocks
    private GoogleCustomSearchServiceImpl googleCustomSearchService;

    @Mock
    private HttpTransport httpTransport;

    @Mock
    private JsonFactory jsonFactory;

    @Mock
    private CustomSearchAPI customsearchAPI;

    @Mock
    private CustomSearchAPI.Cse cse;

    @Mock
    private CustomSearchAPI.Cse.List list;

    private static final String API_KEY = "test-api-key";
    private static final String SEARCH_ENGINE_ID = "test-search-engine-id";

    /**
     * SetUp 设置配置
     *
     * @throws IOException
     */
    @Before
    public void setUp() throws IOException {
        ReflectionTestUtils.setField(googleCustomSearchService, "apiKey", API_KEY);
        ReflectionTestUtils.setField(googleCustomSearchService, "cxId", SEARCH_ENGINE_ID);
        ReflectionTestUtils.setField(googleCustomSearchService, "jsonFactory", new JacksonFactory());

        when(customsearchAPI.cse()).thenReturn(cse);
        when(cse.list()).thenReturn(list);
    }

    /**
     * 测试搜索图片
     * case: 正常搜索图片
     *
     * @throws IOException
     */
    @Test
    public void testSearchImages_Success() throws IOException {
        // 准备测试数据
        String keywords = "test image";
        Integer pageSize = 10;
        Integer pageNum = 1;

        Result result = new Result();
        result.setTitle("Test Image");
        result.setDisplayLink("test.com");
        result.setLink("http://test.com/image.jpg");
        result.setImage(new Result.Image().setThumbnailLink("http://test.com/thumb.jpg"));
        Search searchResult = new Search();
        searchResult.setItems(Collections.singletonList(result));

        when(list.setKey(API_KEY)).thenReturn(list);
        when(list.setCx(SEARCH_ENGINE_ID)).thenReturn(list);
        when(list.setSearchType("image")).thenReturn(list);
        when(list.setNum(pageSize)).thenReturn(list);
        when(list.setStart(Long.valueOf(pageNum))).thenReturn(list);
        when(list.execute()).thenReturn(searchResult);

        // 执行测试
        SearchImageResponse response = googleCustomSearchService.searchImages(keywords, pageSize, pageNum);

        // 验证结果
        assertNotNull(response);
        assertEquals(1, response.getImages().size());
        SearchImageModel image = response.getImages().get(0);
        assertEquals("http://test.com/image.jpg", image.getUrl());
        assertEquals("http://test.com/image.jpg", image.getLargeUrl());

        // 验证方法调用
        verify(list).setKey(API_KEY);
        verify(list).setCx(SEARCH_ENGINE_ID);
        verify(list).setSearchType("image");
        verify(list).setNum(pageSize);
        verify(list).setStart(Long.valueOf(pageNum));
        verify(list).execute();
    }

    /**
     * 测试搜索图片
     * case: 搜索图片时发生 IOException
     *
     * @throws IOException
     */
    @Test(expected = IOException.class)
    public void testSearchImages_NetworkTimeout() throws IOException {
        // 准备测试数据
        String keywords = "test";
        Integer pageSize = 10;
        Integer pageNum = 1;

        when(list.setKey(anyString())).thenReturn(list);
        when(list.setCx(anyString())).thenReturn(list);
        when(list.setSearchType(anyString())).thenReturn(list);
        when(list.setNum(anyInt())).thenReturn(list);
        when(list.setStart(anyLong())).thenReturn(list);
        when(list.execute()).thenThrow(new IOException("Connection timed out"));

        // 执行测试 - 应该抛出 IOException
        googleCustomSearchService.searchImages(keywords, pageSize, pageNum);
    }

    /**
     * 测试搜索图片
     * case: 搜索图片时发生 HttpResponseException
     *
     * @throws IOException
     */
    @Test(expected = HttpResponseException.class)
    public void testSearchImages_QuotaExceeded() throws IOException {
        // 准备测试数据
        String keywords = "test";
        Integer pageSize = 10;
        Integer pageNum = 1;

        when(list.setKey(anyString())).thenReturn(list);
        when(list.setCx(anyString())).thenReturn(list);
        when(list.setSearchType(anyString())).thenReturn(list);
        when(list.setNum(anyInt())).thenReturn(list);
        when(list.setStart(anyLong())).thenReturn(list);

        HttpResponseException httpException = new HttpResponseException.Builder(429, "Quota exceeded", new HttpHeaders())
            .setMessage("Daily Limit Exceeded")
            .build();
        when(list.execute()).thenThrow(httpException);

        // 执行测试 - 应该抛出 RuntimeException
        googleCustomSearchService.searchImages(keywords, pageSize, pageNum);
    }

    /**
     * 测试搜索图片
     * case: 搜索图片时发生 HttpResponseException
     *
     * @throws IOException
     */
    @Test(expected = HttpResponseException.class)
    public void testSearchImages_InvalidApiKey() throws IOException {
        // 准备测试数据
        String keywords = "test";
        Integer pageSize = 10;
        Integer pageNum = 1;

        when(list.setKey(anyString())).thenReturn(list);
        when(list.setCx(anyString())).thenReturn(list);
        when(list.setSearchType(anyString())).thenReturn(list);
        when(list.setNum(anyInt())).thenReturn(list);
        when(list.setStart(anyLong())).thenReturn(list);

        HttpResponseException httpException = new HttpResponseException.Builder(400, "Invalid Credentials", new HttpHeaders())
            .setMessage("Invalid API key")
            .build();
        when(list.execute()).thenThrow(httpException);

        // 执行测试 - 应该抛出 HttpResponseException
        googleCustomSearchService.searchImages(keywords, pageSize, pageNum);
    }

    /**
     * 测试搜索图片
     * case: 搜索图片返回空
     *
     * @throws IOException
     */
    @Test
    public void testSearchImages_MalformedResponse() throws IOException {
        // 准备测试数据
        String keywords = "test";
        Integer pageSize = 10;
        Integer pageNum = 1;

        Search searchResult = new Search();
        // 设置一个不完整或无效的响应
        searchResult.setItems(null);

        when(list.setKey(anyString())).thenReturn(list);
        when(list.setCx(anyString())).thenReturn(list);
        when(list.setSearchType(anyString())).thenReturn(list);
        when(list.setNum(anyInt())).thenReturn(list);
        when(list.setStart(anyLong())).thenReturn(list);
        when(list.execute()).thenReturn(searchResult);

        // 执行测试
        SearchImageResponse response = googleCustomSearchService.searchImages(keywords, pageSize, pageNum);

        // 验证结果
        assertNotNull(response);
        assertTrue(response.getImages().isEmpty());
    }

    /**
     * 测试搜索图片
     * case: 搜索图片返回无效的图片链接
     *
     * @throws IOException
     */
    @Test
    public void testSearchImages_InvalidImageUrls() throws IOException {
        // 准备测试数据
        String keywords = "test";
        Integer pageSize = 10;
        Integer pageNum = 1;

        Result result1 = new Result();
        result1.setLink("");

        Result result2 = new Result();
        result2.setLink("http://test.com/valid.jpg");
        result2.setDisplayLink("test.com");
        result2.setImage(new Result.Image().setThumbnailLink("http://test.com/thumb.jpg"));

        Search searchResult = new Search();
        searchResult.setItems(Arrays.asList(result1, result2));

        when(list.setKey(anyString())).thenReturn(list);
        when(list.setCx(anyString())).thenReturn(list);
        when(list.setSearchType(anyString())).thenReturn(list);
        when(list.setNum(anyInt())).thenReturn(list);
        when(list.setStart(anyLong())).thenReturn(list);
        when(list.execute()).thenReturn(searchResult);

        // 执行测试
        SearchImageResponse response = googleCustomSearchService.searchImages(keywords, pageSize, pageNum);

        // 验证结果
        assertNotNull(response);
        assertEquals(1, response.getImages().size());
    }

    /**
     * 测试搜索图片
     * case: 搜索图片失败时不重试
     *
     * @throws IOException
     */
    @Test
    public void testSearchImages_RetryOnFailure() throws IOException {
        // 准备测试数据
        String keywords = "test";
        Integer pageSize = 10;
        Integer pageNum = 1;

        when(list.setKey(anyString())).thenReturn(list);
        when(list.setCx(anyString())).thenReturn(list);
        when(list.setSearchType(anyString())).thenReturn(list);
        when(list.setNum(anyInt())).thenReturn(list);
        when(list.setStart(anyLong())).thenReturn(list);

        // 第一次调用抛出 IOException
        when(list.execute()).thenThrow(new IOException("First attempt failed"));

        // 执行测试 - 应该直接抛出 IOException
        try {
            googleCustomSearchService.searchImages(keywords, pageSize, pageNum);
            fail("Expected IOException to be thrown");
        } catch (IOException e) {
            assertEquals("First attempt failed", e.getMessage());
        }

        verify(list, times(1)).execute(); // 验证只调用一次，不重试
    }
}

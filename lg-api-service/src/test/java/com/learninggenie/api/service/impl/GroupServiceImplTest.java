package com.learninggenie.api.service.impl;

import com.amazonaws.services.dynamodbv2.xspec.S;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.learninggenie.api.model.ClassStatusResponse;
import com.learninggenie.api.model.SuccessResponse;
import com.learninggenie.api.model.UrlResponse;
import com.learninggenie.api.model.center.CenterGroupEnrollment;
import com.learninggenie.api.model.group.*;
import com.learninggenie.api.model.student.EnrollmentTeamModel;
import com.learninggenie.api.model.tag.TagResponse;
import com.learninggenie.api.provider.*;
import com.learninggenie.api.security.AuthUserDetails;
import com.learninggenie.api.service.EmailService;
import com.learninggenie.api.service.MediaService;
import com.learninggenie.api.service.TagService;
import com.learninggenie.api.service.UserService;
import com.learninggenie.api.util.HtmlToPdf;
import com.learninggenie.common.data.dao.*;
import com.learninggenie.common.data.dao.groups.GroupEnrollmentTeamEntityDao;
import com.learninggenie.common.data.dao.groups.GroupsMetaDataEntityDao;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.entity.*;
import com.learninggenie.common.data.entity.CenterEntity;
import com.learninggenie.common.data.entity.DomainEntity;
import com.learninggenie.common.data.entity.GroupEntity;
import com.learninggenie.common.data.entity.UserEntity;
import com.learninggenie.common.data.entity.groups.GroupEnrollmentTeamEntity;
import com.learninggenie.common.data.entity.groups.GroupsMetaDataEntity;
import com.learninggenie.common.data.enums.*;
import com.learninggenie.common.data.model.*;
import com.learninggenie.common.data.model.domain.EssentialGroupModel;
import com.learninggenie.common.data.model.framework.GroupFrameworkStatsModel;
import com.learninggenie.common.filesystem.FileSystem;
import com.learninggenie.common.framwork.FrameworkProvider;
import com.learninggenie.common.region.RegionService;
import com.learninggenie.common.score.RatingService;
import com.learninggenie.common.sharding.ShardingProvider;
import com.learninggenie.common.utils.JsonUtil;
import com.learninggenie.common.utils.ResourceUtil;
import com.learninggenie.common.utils.StringUtil;
import com.learninggenie.common.utils.TimeUtil;
import org.json.JSONObject;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.BeanUtils;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class GroupServiceImplTest {

    @Mock
    private GroupProvider groupProvider;

    @Mock
    private PortfolioProvider portfolioProvider;

    @Mock
    private FrameworkProvider frameworkProvider;

    @Mock
    private UserProvider userProvider;

    @Mock
    private UserService userService;

    @Mock
    private UserDaoImpl userDaoImpl;

    @Mock
    private GroupDao groupDao;

    @Mock
    private CenterDao centerDao;

    @Mock
    private EnrollmentProvider enrollmentProvider;

    @Mock
    private StudentDao studentDao;

    @Mock
    private MediaDao mediaDao;

    @Mock
    private AgencyDao agencyDao;

    @Mock
    private InvitationsEnrollmentInvitationDao invitationsEnrollmentInvitationDao;

    @Mock
    private MediaService mediaService;

    @Mock
    private InvitationDao invitationDao;

    @Mock
    private ShardingProvider shardingProvider;

    @Mock
    private DomainDao domainDao;

    @Mock
    private RatingService ratingService;

    @Mock
    private GroupEnrollmentTeamEntityDao groupEnrollmentTeamEntityDao;

    @Mock
    private GroupsMetaDataEntityDao groupsMetaDataEntityDao;

    @Mock
    private FileSystem fileSystem;

    @Mock
    private EmailService emailService;

    @Mock
    private ReportDao reportDao;

    @Mock
    private RemoteProvider remoteProvider;

    @Mock
    private UserDaoImpl userDao;

    @Mock
    private ScoreServiceImpl scoreService;

    @Mock
    private NoteDao noteDao;

    @Mock
    private RegionService regionService;

    @Mock
    private TagDao tagDao;

    @Mock
    private TagService tagService;

    @InjectMocks
    private GroupServiceImpl groupServiceImpl;

    private MockedStatic<ResourceUtil> resourceUtilStatic;

    private MockedStatic<JsonUtil> jsonUtilStatic;

    /**
     * 注册 MockedStatic
     */
    public void beforeMethod() {
        // 打印
        resourceUtilStatic = mockStatic(ResourceUtil.class);
        jsonUtilStatic = mockStatic(JsonUtil.class);
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 关闭 MockedStatic
     */
    public void afterMethod() {
        resourceUtilStatic.close();
        jsonUtilStatic.close();
    }
    /**
     * 测试方法：getGroupById 方法
     */
    @Test
    public void testGetGroupById() {

        // 数据准备
        String id = "U0001";
        String frameworkId = "F00001";
        List<EnrollmentEntity> enrollments = null;
        com.learninggenie.common.data.entity.GroupEntity group = new GroupEntity();
        group.setInactive(true);
        List<String> scoreTemplateIds = new ArrayList<>();
        String currentUserId = id;
        UserModel currentUser = new UserModel();
        currentUser.setRole("ADMIN_SITE");
        List<CenterEntity> centers = null;
        com.learninggenie.common.data.model.DomainEntity domain = null;
        AgencyModel userAgency = new AgencyModel();
        userAgency.setId(id);
        AgencyEntity groupAgency = new AgencyEntity();
        groupAgency.setId("U0002");

        EnrollmentModel child = new EnrollmentModel();
        child.setId(id);
        child.setFirstName("ni");
        child.setLastName("hao");
        child.setDisplayName("ni hao");
        child.setBirthDate("2000-01-01");
        child.setEnrollmentDate("2000-01-02");
        child.setPrivatePhoto(false);
        child.setInactive(true);
        List<UserEnrollmentModel> allLinkParentList = new ArrayList<>();
        List<UserEnrollmentModel> allCodeLinkParentList = new ArrayList<>();
        List<UserModel> allUserModels = new ArrayList<>();
        List<EnrollmentInvitationEntity> allEnrollmentInvitations = new ArrayList<>();
        List<EnrollmentEntity> enrollmentList = new ArrayList<>();
        EnrollmentEntity enrollment = new EnrollmentEntity();
        enrollment.setId(id);
        enrollment.setFrameworkId(frameworkId);
        enrollment.setInactive(true);

        enrollmentList.add(enrollment);

        List<GroupInvitationEntity> groupInvitationEntitys = new ArrayList<>();
        List<EssentialGroupModel> essentialGroupModels = new ArrayList<>();

        Map<String, Map<String, Boolean>> childIdToIepAndEldMap = new HashMap<>();
        Map<String, Boolean> eldMap = new HashMap<>();
        eldMap.put("ELD", true);
        eldMap.put("IEP/IFSP", true);
        childIdToIepAndEldMap.put(id, eldMap);

        ReflectionTestUtils.setField(groupServiceImpl, "idPSE2015", frameworkId);
        ReflectionTestUtils.setField(groupServiceImpl, "idITE2015", frameworkId);

        // 调用方法
        when(groupProvider.checkGroup(id)).thenReturn(group);
        when(portfolioProvider.getScoreTemplateIds()).thenReturn(scoreTemplateIds);
        when(userProvider.getCurrentUserId()).thenReturn(currentUserId);
        when(userDao.getUserById(currentUserId)).thenReturn(currentUser);
        when(centerDao.getCentersByGroupIds(anyList())).thenReturn(centers);
        when(groupDao.getDomain(any())).thenReturn(domain);
        when(studentDao.getInactiveChildrenByInactiveGroupId(group.getId())).thenReturn(enrollmentList);
        when(agencyDao.getByGroupId(any())).thenReturn(groupAgency);
        when(invitationsEnrollmentInvitationDao.getLinkParentIdByChildIds(any())).thenReturn(allLinkParentList);
        when(invitationsEnrollmentInvitationDao.getCodeLinkParentIdByChildIds(any())).thenReturn(allCodeLinkParentList);
//        when(userDaoImpl.getParentsByStudentIds(anyList())).thenReturn(allUserModels);
//        when(userDaoImpl.getParentCodesByStudentIds(anyList())).thenReturn(allEnrollmentInvitations);
        when(invitationDao.getGroupInvitationEntitys(id)).thenReturn(groupInvitationEntitys);
        when(enrollmentProvider.getIEPAndELDAttrsByEnrollmentIds(anyList())).thenReturn(childIdToIepAndEldMap);
        when(domainDao.getEssentialGroupByAgency(any())).thenReturn(essentialGroupModels);
        groupServiceImpl.getGroupById(id);

        // 验证结果
        verify(enrollmentProvider, times(1)).getIEPAndELDAttrsByEnrollmentIds(anyList());


    }


    /**
     * 测试获取注册
     */
    @Test
    public void testGetEnrollments() {
        // Mock group
        GroupEntity group = new GroupEntity();
        group.setPeriodGroupId("PeriodGroupId");
        String groupId = "GroupId";
        group.setId(groupId);
        String groupName = "GroupName";
        group.setName(groupName);
        group.setCreateAtUtc(new Date());
        group.setIsDeleted(false);
        // Mock center
        CenterEntity center = new CenterEntity();
        center.setId("CenterId");
        center.setCenterTimeZone("Asia/Shanghai");
        center.setCreateAtUtc(new Date());
        center.setDeleted(false);
        center.setName("CenterName");
        center.setSendReportTime(new Date());
        // Mock user
        UserEntity userEntity = new UserEntity();
        userEntity.setId("UserId");
        userEntity.setCenters(Sets.newHashSet());
        userEntity.setEmail("UserEmail");
        userEntity.setNeedActivate(false);
        userEntity.setEmailConfirmed(false);
        userEntity.setPasswordHash("UserPassword");
        userEntity.setRole(UserRole.COLLABORATOR.toString());
        userEntity.setMetadatas(Sets.newHashSet());
        userEntity.setCreditsRecords(Sets.newHashSet());
        userEntity.setReferRecords(Sets.newHashSet());
        userEntity.setCreateDateUtc(new Date());
        userEntity.setGroups(Sets.newHashSet());
        userEntity.setNotificationRecords(Sets.newHashSet());
        userEntity.setDistrict(new DistrictEntity());
        userEntity.setPhoneNumberConfirmed(false);
        userEntity.setTwoFactorEnabled(false);
        userEntity.setLockoutEndDateUtc(new Date());
        userEntity.setLockoutEnabled(false);
        userEntity.setAccessFailedCount(0);
        userEntity.setIsDeleted(false);
        userEntity.setUserName("UserName");
        userEntity.setLoginDateUtc(new Date());
        userEntity.setIsTest(false);
        userEntity.setFirstName("User");
        userEntity.setLastName("Name");
        userEntity.setEnrollments(Sets.newHashSet());
        userEntity.setCenterImportRecordEntities(Sets.newHashSet());
        userEntity.setAgencies(Sets.newHashSet());
        userEntity.setCreateCenters(Sets.newHashSet());
        userEntity.setTempPasswordExpiredAtUtc(new Date());

        // 设置 user
        center.setUser(userEntity);
        center.setPaymentPlan(new CenterPaymentPlanEntity());
        center.setIsDeleted(false);
        center.setGroups(Sets.newHashSet());
        center.setSubscribeRecords(Sets.newHashSet());
        center.setCenterMetaDataEntities(Sets.newHashSet());
        MediaEntity mediaEntity = new MediaEntity();
        mediaEntity.setWeb(false);
        mediaEntity.setCompressed(false);
        mediaEntity.setPrivateFile(false);
        mediaEntity.setId("mediaEntityId");
        mediaEntity.setCreateAtUtc(new Date());
        mediaEntity.setRelativePath("RelativePath");
        mediaEntity.setSnapshotPath("SnapshotPath");
        mediaEntity.setFileType("FileType");
        mediaEntity.setHeight(0);
        mediaEntity.setMimeType("");
        mediaEntity.setSize(0L);
        mediaEntity.setWidth(0);
        mediaEntity.setHaveSmall(false);
        mediaEntity.setHaveMedium(false);

        center.setLogoMedia(mediaEntity);
        center.setAgencies(Sets.newHashSet());
        center.setUsers(Sets.newHashSet());
        center.setMetaValue("");
        center.setTraining(false);
        center.setTrainingCenter(false);

        // 设置 group
        group.setCenter(center);
        group.setEnrollments(Sets.newHashSet());
        group.setTeachers(Sets.newHashSet());
        group.setGroupMetaDataEntities(Sets.newHashSet());
        group.setDomain(new DomainEntity());
        group.setDomainId("");
        group.setStage(new GroupStageEntity());
        group.setGroupInvitationEntities(Sets.newHashSet());
        group.setPeriods(Sets.newHashSet());
        group.setCurrentPeriod(new GroupPeriodEntity());
        group.setDeleted(false);
        group.setInactive(false);
        group.setMetaValue("");
        group.setTraining(false);
        group.setChildFrameworkIds(Lists.newArrayList());

        UserModel user = new UserModel();
        BeanUtils.copyProperties(userEntity, user);
        // mock enrollment
        CenterGroupResponse centerGroupResponse = new CenterGroupResponse();
        centerGroupResponse.setSpecialGroup(true);

        // mock language
        String language = "en-US";
        when(userProvider.getCurrentLang()).thenReturn(language);
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("AgencyId");
        agencyModel.setName("AgencyName");

        // mock getAgencyByUserId
        when(userProvider.getAgencyByUserId(user.getId())).thenReturn(agencyModel);
        // mock group
        AgencyEntity agencyEntity = new AgencyEntity();
        agencyEntity.setId("AgencyId2");
        agencyEntity.setName("AgencyName2");
        // mock agencyEntity
        when(agencyDao.getByGroupId(group.getId())).thenReturn(agencyEntity);

        // mock getChildrenByGroupIdentifiers
        ArrayList<EnrollmentModel> enrollmentModels = new ArrayList<>();
        EnrollmentModel enrollmentModel = new EnrollmentModel();
        enrollmentModel.setChildName("ChildName");
        enrollmentModel.setRatingPeriods(Lists.newArrayList());
        enrollmentModel.setId("ChildId");
        String firstName = "ChildFirstName";
        enrollmentModel.setFirstName(firstName);
        String lastName = "ChildLastName";
        enrollmentModel.setLastName(lastName);
        String middleName = "ChildMiddleName";
        enrollmentModel.setMiddleName(middleName);
        enrollmentModel.setDisplayName(StringUtil.combineWithSpace(firstName, middleName, lastName));
        enrollmentModel.setAvatarMediaId("AvatarMediaId");
        enrollmentModel.setAvatarUrl("AvatarUrl");
        enrollmentModel.setGroupId(groupId);
        enrollmentModel.setGroupName(groupName);
        enrollmentModel.setBirthDate("2020-12-12");
        enrollmentModel.setGender("Female");
        enrollmentModel.setEnrollmentDate("2022-12-10");
        enrollmentModel.setWithdrawnDate("2025-02-15");
        enrollmentModel.setPrivatePhoto(false);
        enrollmentModel.setInactive(false);

        enrollmentModels.add(enrollmentModel);
        when(studentDao.getChildrenByGroupIdentifiers(anyList(), anyList())).thenReturn(enrollmentModels);

        when(mediaDao.getMediaById(anyString())).thenReturn(mediaEntity);
        List<CenterGroupEnrollment> enrollments = groupServiceImpl.getEnrollments(group, user, centerGroupResponse);

        // assert
        String expectedId = enrollmentModel.getId().toUpperCase();
        Assert.assertEquals(enrollments.size(), 1);
        Assert.assertEquals(enrollments.get(0).getId(), expectedId);
        Assert.assertEquals(enrollments.get(0).getFirstName(), enrollmentModel.getFirstName());
        Assert.assertEquals(enrollments.get(0).getLastName(), enrollmentModel.getLastName());
        Assert.assertEquals(enrollments.get(0).getDisplayName(), enrollmentModel.getDisplayName());
    }

    /**
     * 测试对学校以及小孩进行排序 通过学校名称 以及小孩的 displayName 进行排序
     */
    @Test
    public void testSortGroupResponseByName() {
        // 数据准备
        // 学校数据列表
        List<CenterGroupResponse> groupResponses = new ArrayList<>();
        // 学校数据
        CenterGroupResponse centerGroupResponse = new CenterGroupResponse();
        // 学生列表
        List<CenterGroupEnrollment> enrollments = new ArrayList<>();
        // 学生数据
        CenterGroupEnrollment centerGroupEnrollment = new CenterGroupEnrollment();
        centerGroupEnrollment.setDisplayName("Allis Johnson");        // 学生姓名
        // 学生数据
        CenterGroupEnrollment centerGroupEnrollment1 = new CenterGroupEnrollment();
        centerGroupEnrollment1.setDisplayName("Pierce Johnson");       // 学生姓名
        // 将学生数据添加到学生列表中
        enrollments.add(centerGroupEnrollment);
        enrollments.add(centerGroupEnrollment1);
        // 将学生列表添加到学校数据中
        centerGroupResponse.setName("center");
        centerGroupResponse.setEnrollments(enrollments);
        // 将学校数据添加到学校数据列表中
        groupResponses.add(centerGroupResponse);

        // 调用方法
        groupServiceImpl.sortGroupResponseByDisplayName(groupResponses);

        // 验证结果 通过学校名称 以及小孩的 displayName 进行排序 期望结果为 Allis Johnson  Pierce Johnson
        Assert.assertEquals("Allis Johnson", groupResponses.get(0).getEnrollments().get(0).getDisplayName());
        Assert.assertEquals("Pierce Johnson", groupResponses.get(0).getEnrollments().get(1).getDisplayName());
    }

    /**
     * 测试对学校以及小孩进行排序 通过学校名称 以及小孩的 displayName 进行排序
     */
    @Test
    public void testSortGroupResponseByName1() {
        // 数据准备
        // 学校数据
        CenterGroupResponse centerGroupResponse = new CenterGroupResponse();
        // 学生列表
        List<CenterGroupEnrollment> enrollments = new ArrayList<>();
        // 学生数据
        CenterGroupEnrollment centerGroupEnrollment = new CenterGroupEnrollment();
        centerGroupEnrollment.setDisplayName("Leslie Renea");         // 学生姓名
        // 学生数据
        CenterGroupEnrollment centerGroupEnrollment1 = new CenterGroupEnrollment();
        centerGroupEnrollment1.setDisplayName("Renea Jordan");        // 学生姓名
        // 将学生数据添加到学生列表中
        enrollments.add(centerGroupEnrollment);
        enrollments.add(centerGroupEnrollment1);
        // 将学生列表添加到学校数据中
        centerGroupResponse.setName("center");
        centerGroupResponse.setEnrollments(enrollments);

        // 调用方法
        groupServiceImpl.sortGroupResponseByDisplayName(centerGroupResponse);

        // 验证结果 通过学校名称 以及小孩的 displayName 进行排序 期望结果为 Leslie Renea  Renea Jordan
        Assert.assertEquals("Leslie Renea", centerGroupResponse.getEnrollments().get(0).getDisplayName());
        Assert.assertEquals("Renea Jordan", centerGroupResponse.getEnrollments().get(1).getDisplayName());
    }

    /**
     * 测试方法：hasGroupTeams
     * 测试场景：groupId 为空
     * 预期结果：返回 false
     */
    @Test
    public void testHasGroupTeams_NoGroupId_ReturnsFalse() {
        // 调用方法，并且定义预期结果
        SuccessResponse response = groupServiceImpl.hasGroupTeams(null);

        // 验证结果
        Assert.assertFalse(response.isSuccess());
        // 验证方法调用
        verifyNoMoreInteractions(groupDao, userDaoImpl, studentDao, groupEnrollmentTeamEntityDao);
    }

    /**
     * 测试方法：hasGroupTeams
     * 测试场景：groupId 为空字符串
     * 预期结果：返回 false
     */
    @Test
    public void testHasGroupTeams_GroupNotInDatabase_ReturnsFalse() {
        // 调用方法，并且定义预期结果
        // 当调用 groupDao.getGroupWithCenter 方法时，返回 null
        when(groupDao.getGroupWithCenter(anyString())).thenReturn(null);

        // 调用方法，并且定义预期结果
        SuccessResponse response = groupServiceImpl.hasGroupTeams("groupId");

        // 验证结果
        Assert.assertFalse(response.isSuccess());
        // 验证方法调用
        verify(groupDao).getGroupWithCenter("groupId");
        // 验证方法调用
        verifyNoMoreInteractions(userDaoImpl, studentDao, groupEnrollmentTeamEntityDao);
    }

    /**
     * 测试方法：hasGroupTeams
     * 测试场景：groupId 不为空，但是没有老师
     * 预期结果：返回 false
     */
    @Test
    public void testHasGroupTeams_NoTeachers_ReturnsFalse() {
        // 调用方法，并且定义预期结果
        com.learninggenie.common.data.model.GroupEntity groupEntity = new com.learninggenie.common.data.model.GroupEntity();
        // 设置 group 的 Id
        groupEntity.setId("groupId");
        // 当调用 groupDao.getGroupWithCenter 方法时，返回空列表
        when(groupDao.getGroupWithCenter(anyString())).thenReturn(groupEntity);
        // 当调用 userDao.getTeachers 方法时，返回空列表
        when(userDao.getTeachers(anyString())).thenReturn(Collections.emptyList());

        // 调用方法，并且定义预期结果
        SuccessResponse response = groupServiceImpl.hasGroupTeams("groupId");

        // 验证结果
        Assert.assertFalse(response.isSuccess());
        // 验证方法调用
        verify(groupDao).getGroupWithCenter("groupId");
        // 验证方法调用
        verify(userDao).getTeachers("groupId");
        // 验证方法调用
        verifyNoMoreInteractions(studentDao, groupEnrollmentTeamEntityDao);
    }

    /**
     * 测试方法：hasGroupTeams
     * 测试场景：groupId 不为空，有老师，但是没有小孩
     * 预期结果：返回 false
     */
    @Test
    public void testHasGroupTeams_NoChildren_ReturnsFalse() {
        // 调用方法，并且定义预期结果
        com.learninggenie.common.data.model.GroupEntity groupEntity = new com.learninggenie.common.data.model.GroupEntity();
        // 设置 group 的 Id
        groupEntity.setId("groupId");
        // 当调用 groupDao.getGroupWithCenter 方法时，返回空列表
        when(groupDao.getGroupWithCenter(anyString())).thenReturn(groupEntity);
        // 当调用 userDao.getTeachers 方法时，返回空列表
        when(userDao.getTeachers(anyString())).thenReturn(Collections.singletonList(new UserModel()));
        // 当调用 studentDao.getAllByGroupId 方法时，返回空列表
        when(studentDao.getAllByGroupId(anyString())).thenReturn(Collections.emptyList());

        // 调用方法，并且定义预期结果
        SuccessResponse response = groupServiceImpl.hasGroupTeams("groupId");

        // 验证结果
        Assert.assertFalse(response.isSuccess());
        // 验证 getGroupWithCenter 方法调用
        verify(groupDao).getGroupWithCenter("groupId");
        // 验证 getTeachers 方法调用
        verify(userDao).getTeachers("groupId");
        // 验证 getAllByGroupId 方法调用
        verify(studentDao).getAllByGroupId("groupId");
        // 验证方法调用
        verifyNoMoreInteractions(groupEnrollmentTeamEntityDao);
    }

    /**
     * 测试方法：hasGroupTeams
     * 测试场景：groupId 不为空，有老师，有小孩，但是没有分组
     * 预期结果：返回 false
     */
    @Test
    public void testHasGroupTeams_NoGroupEnrollmentTeams_ReturnsFalse() {
        // 调用方法，并且定义预期结果
        com.learninggenie.common.data.model.GroupEntity groupEntity = new com.learninggenie.common.data.model.GroupEntity();
        // 设置 group 的 Id
        groupEntity.setId("groupId");
        // 当调用 groupDao.getGroupWithCenter 方法时，返回空列表
        when(groupDao.getGroupWithCenter(anyString())).thenReturn(groupEntity);
        // 当调用 userDao.getTeachers 方法时，返回空列表
        when(userDao.getTeachers(anyString())).thenReturn(Collections.singletonList(new UserModel()));
        // 当调用 studentDao.getAllByGroupId 方法时，返回空列表
        when(studentDao.getAllByGroupId(anyString())).thenReturn(Collections.singletonList(new EnrollmentEntity()));
        // 当调用 groupEnrollmentTeamEntityDao.listByGroupIds 方法时，返回空列表
        when(groupEnrollmentTeamEntityDao.listByGroupIds(anyList())).thenReturn(Collections.emptyList());

        // 调用方法，并且定义预期结果
        SuccessResponse response = groupServiceImpl.hasGroupTeams("groupId");

        // 验证结果
        Assert.assertFalse(response.isSuccess());
        // 验证 getGroupWithCenter 方法调用
        verify(groupDao).getGroupWithCenter("groupId");
        // 验证 getTeachers 方法调用
        verify(userDao).getTeachers("groupId");
        // 验证 getAllByGroupId 方法调用
        verify(studentDao).getAllByGroupId("groupId");
        // 验证方法调用
        verify(groupEnrollmentTeamEntityDao).listByGroupIds(Collections.singletonList("groupId"));
    }

    /**
     * 测试方法：hasGroupTeams
     * 测试场景：groupId 不为空，有老师，有小孩，有分组
     * 预期结果：返回 true
     */
    @Test
    public void testHasGroupTeams_WithGroupEnrollmentTeams_ReturnsTrue() {
        // 调用方法，并且定义预期结果
        com.learninggenie.common.data.model.GroupEntity groupEntity = new com.learninggenie.common.data.model.GroupEntity();
        // 设置 group 的 Id
        groupEntity.setId("groupId");
        // 当调用 groupDao.getGroupWithCenter 方法时，返回空列表
        when(groupDao.getGroupWithCenter(anyString())).thenReturn(groupEntity);
        // 当调用 userDao.getTeachers 方法时，返回空列表
        when(userDao.getTeachers(anyString())).thenReturn(Collections.singletonList(new UserModel()));
        // 当调用 studentDao.getAllByGroupId 方法时，返回空列表
        when(studentDao.getAllByGroupId(anyString())).thenReturn(Collections.singletonList(new EnrollmentEntity()));
        // 当调用 groupEnrollmentTeamEntityDao.listByGroupIds 方法时，返回空列表
        GroupEnrollmentTeamEntity enrollmentTeamEntity = new GroupEnrollmentTeamEntity();
        // 设置 enrollmentTeamEntity 的 teacherId
        enrollmentTeamEntity.setTeacherId("teacherId");
        // 设置 enrollmentTeamEntity 的 enrollmentId
        enrollmentTeamEntity.setEnrollmentId("enrollmentId");
        // 定义 enrollmentTeamEntities，用于存储 enrollmentTeamEntity
        GroupEnrollmentTeamEntity enrollmentTeamEntity2 = new GroupEnrollmentTeamEntity();
        // 设置 enrollmentTeamEntity2 的 teacherId
        enrollmentTeamEntity2.setTeacherId("teacherId2");
        // 设置 enrollmentTeamEntity2 的 enrollmentId
        enrollmentTeamEntity2.setEnrollmentId("enrollmentId2");

        when(groupEnrollmentTeamEntityDao.listByGroupIds(anyList())).thenReturn(Arrays.asList(enrollmentTeamEntity, enrollmentTeamEntity2));

        // 调用方法，并且定义预期结果
        SuccessResponse response = groupServiceImpl.hasGroupTeams("groupId");

        // 验证结果
        Assert.assertTrue(response.isSuccess());
        // 验证 getGroupWithCenter 方法调用
        verify(groupDao).getGroupWithCenter("groupId");
        // 验证 getTeachers 方法调用
        verify(userDao).getTeachers("groupId");
        // 验证 getAllByGroupId 方法调用
        verify(studentDao).getAllByGroupId("groupId");
        // 验证方法调用
        verify(groupEnrollmentTeamEntityDao).listByGroupIds(Collections.singletonList("groupId"));
    }

    /**
     * 测试方法：getGroupTeams_NoGroupId_ReturnsEmptyResponse
     * 测试场景：groupId 为空
     * 预期结果：返回空的 GetGroupTeamsResponse，且不执行任何数据库操作
     */
    @Test
    public void testGetGroupTeams_NoGroupId_ReturnsEmptyResponse() {
        // 调用方法，并定义预期结果
        GetGroupTeamsResponse response = groupServiceImpl.getGroupTeams(null, "true");

        // 验证结果
        Assert.assertNotNull(response);
        // 验证 response 中小孩的数量为 0
        assertEquals(0, response.getEnrollmentTeamModelList().size());

        // 验证方法调用
        verifyNoMoreInteractions(groupDao, userDaoImpl, studentDao, groupEnrollmentTeamEntityDao, groupsMetaDataEntityDao, userProvider);
    }

    /**
     * 测试方法：getGroupTeams_WithEmptyGroup_ReturnsEmptyResponse
     * 测试场景：根据groupId获取的GroupEntity为空
     * 预期结果：返回空的 GetGroupTeamsResponse，且不执行任何数据库操作
     */
    @Test
    public void testGetGroupTeams_WithEmptyGroup_ReturnsEmptyResponse() {
        // 模拟数据
        when(groupDao.getGroupWithCenter(any())).thenReturn(null);

        // 调用方法，并定义预期结果
        GetGroupTeamsResponse response = groupServiceImpl.getGroupTeams("groupId", "true");

        // 验证结果
        Assert.assertNotNull(response);
        // 验证 response 中小孩的数量为 0
        assertEquals(0, response.getEnrollmentTeamModelList().size());

        // 验证方法调用
        verify(groupDao).getGroupWithCenter("groupId");
        verifyNoMoreInteractions(groupDao, userDaoImpl, studentDao, groupEnrollmentTeamEntityDao, groupsMetaDataEntityDao, userProvider);
    }

    /**
     * 测试方法：getGroupTeams_WithNoTeachers_ReturnsEmptyResponse
     * 测试场景：根据groupId获取的老师列表为空
     * 预期结果：返回空的 GetGroupTeamsResponse，且不执行任何数据库操作
     */
    @Test
    public void testGetGroupTeams_WithNoTeachers_ReturnsEmptyResponse() {
        // 模拟数据
        com.learninggenie.common.data.model.GroupEntity groupEntity = new com.learninggenie.common.data.model.GroupEntity();
        // 设置 group 的 Id
        groupEntity.setId("groupId");
        // 当调用 groupDao.getGroupWithCenter 方法时，返回空列表
        when(groupDao.getGroupWithCenter(any())).thenReturn(groupEntity);
        // 当调用 userDao.getTeachers 方法时，返回空列表
        when(userDao.getTeachers(anyString())).thenReturn(Collections.emptyList());

        // 调用方法，并定义预期结果
        GetGroupTeamsResponse response = groupServiceImpl.getGroupTeams("groupId", "true");

        // 验证结果
        Assert.assertNotNull(response);
        // 验证 response 中小孩的数量为 0
        assertEquals(0, response.getEnrollmentTeamModelList().size());

        // 验证方法调用
        verify(groupDao).getGroupWithCenter("groupId");
        verify(userDao).getTeachers("groupId");
        verifyNoMoreInteractions(groupDao, userDaoImpl, studentDao, groupEnrollmentTeamEntityDao, groupsMetaDataEntityDao, userProvider);
    }

    /**
     * 测试方法：getGroupTeams_CreateTeamsForNoGroup_ReturnsTeamsAndSavesToDatabase
     * 测试场景：班级没有分组，将所有小孩默认放在第一个老师的分组中
     * 预期结果：返回包含小组信息的 GetGroupTeamsResponse，且保存分组信息到数据库
     */
    @Test
    public void testGetGroupTeams_CreateTeamsForNoGroup_ReturnsTeamsAndSavesToDatabase() {
        // 模拟数据
        com.learninggenie.common.data.model.GroupEntity groupEntity = new com.learninggenie.common.data.model.GroupEntity();
        // 设置 group 的 Id
        groupEntity.setId("groupId");
        // 当调用 groupDao.getGroupWithCenter 方法时，返回空列表
        when(groupDao.getGroupWithCenter(any())).thenReturn(groupEntity);
        // 定义 UserModel
        UserModel model = new UserModel();
        // 设置 UserModel 的 Id
        model.setId("teacherId1");
        // 设置老师的角色
        model.setRole(UserRole.COLLABORATOR.toString());
        // 设置 UserModel 的 DisplayName
        model.setDisplayName("teacher1");
        // 定义 UserModels，用于存储 UserModel
        List<UserModel> userModels = Collections.singletonList(model);
        // 当调用 userDao.getTeachers 方法时，返回 userModels
        when(userDao.getTeachers(anyString())).thenReturn(userModels);
        // 定义 EnrollmentEntity
        EnrollmentEntity enrollmentEntity = new EnrollmentEntity();
        // 设置 EnrollmentEntity 的 Id
        enrollmentEntity.setId("child1");
        // 设置 EnrollmentEntity 的 DisplayName
        enrollmentEntity.setDisplayName("child1");
        // 定义 GroupEntity
        GroupEntity group = new GroupEntity();
        // 设置 GroupEntity 的 Id
        group.setId("groupId");
        // 设置 enrollmentEntity 的 GroupEntity
        enrollmentEntity.setGroup(group);
        // 定义 EnrollmentEntities，用于存储 EnrollmentEntity
        List<EnrollmentEntity> enrollmentEntities = Collections.singletonList(enrollmentEntity);
        // 当调用 studentDao.getAllByGroupId 方法时，返回 enrollmentEntities
        when(studentDao.getAllByGroupId(any())).thenReturn(enrollmentEntities);
        // 定义 EnrollmentModel
        EnrollmentModel enrollmentModel = new EnrollmentModel();
        // 设置 EnrollmentModel 的 Id
        enrollmentModel.setId("child1");
        // 设置 EnrollmentModel 的 DisplayName
        enrollmentModel.setDisplayName("child1");
        // 定义 EnrollmentModels，用于存储 EnrollmentModel
        List<EnrollmentModel> enrollmentModels = Collections.singletonList(enrollmentModel);
        // 当 userProvider 获取当前机构 Id 的时候返回 "agencyId"
        when(userProvider.getCurrentAgencyId()).thenReturn("agencyId");

        // 调用方法，并定义预期结果
        GetGroupTeamsResponse response = groupServiceImpl.getGroupTeams("groupId", "true");

        // 验证结果
        Assert.assertNotNull(response);
        assertEquals(0, response.getEnrollmentTeamModelList().size());

        // 验证数据库操作
        verify(groupDao).getGroupWithCenter("groupId");
        verify(userDao).getTeachers("groupId");
        verify(studentDao).getAllByGroupId("groupId");
        verify(studentDao).getAllChildrenByIds(Collections.singletonList("CHILD1"));
        verify(groupEnrollmentTeamEntityDao).saveOrUpdateBatch(anyList());
        verify(groupsMetaDataEntityDao).saveOrUpdate(any(GroupsMetaDataEntity.class));
    }


    /**
     * 测试方法：getGroupTeams_GetTeamsForGroup_ReturnsTeamsAndSavesToDatabase
     * 测试场景：班级存在分组，按照分组中的数据获取
     * 预期结果：返回包含小组信息的 GetGroupTeamsResponse，且保存分组信息到数据库
     */
    @Test
    public void getGroupTeams_GetTeamsForGroup_ReturnsTeamsAndSavesToDatabase() {
        // 模拟数据
        com.learninggenie.common.data.model.GroupEntity groupEntity = new com.learninggenie.common.data.model.GroupEntity();
        // 设置 group 的 Id
        groupEntity.setId("groupId");
        // 当调用 groupDao.getGroupWithCenter 方法时，返回空列表
        when(groupDao.getGroupWithCenter(any())).thenReturn(groupEntity);
        // 定义 UserModel
        UserModel model = new UserModel();
        // 设置老师的角色
        model.setRole(UserRole.COLLABORATOR.toString());
        // 设置 UserModel 的 Id
        model.setId("teacherId1");
        // 设置 UserModel 的 DisplayName
        model.setDisplayName("teacher1");
        // 定义 UserModels，用于存储 UserModel
        List<UserModel> userModels = Collections.singletonList(model);
        // 当调用 userDao.getTeachers 方法时，返回 userModels
        when(userDao.getTeachers(anyString())).thenReturn(userModels);
        // 定义 EnrollmentEntity
        EnrollmentEntity enrollmentEntity = new EnrollmentEntity();
        // 设置 EnrollmentEntity 的 Id
        enrollmentEntity.setId("child1");
        // 设置 EnrollmentEntity 的 DisplayName
        enrollmentEntity.setDisplayName("child1");
        // 定义 GroupEntity
        GroupEntity group = new GroupEntity();
        // 设置 GroupEntity 的 Id
        group.setId("groupId");
        // 设置 enrollmentEntity 的 GroupEntity
        enrollmentEntity.setGroup(group);
        // 定义 EnrollmentEntities，用于存储 EnrollmentEntity
        List<EnrollmentEntity> enrollmentEntities = Collections.singletonList(enrollmentEntity);
        // 当调用 studentDao.getAllByGroupId 方法时，返回 enrollmentEntities
        when(studentDao.getAllByGroupId(any())).thenReturn(enrollmentEntities);
        // 定义 EnrollmentModel
        EnrollmentModel enrollmentModel = new EnrollmentModel();
        // 设置 EnrollmentModel 的 Id
        enrollmentModel.setId("child1");
        // 设置 EnrollmentModel 的 DisplayName
        enrollmentModel.setDisplayName("child1");
        // 定义 EnrollmentModels，用于存储 EnrollmentModel
        List<EnrollmentModel> enrollmentModels = Collections.singletonList(enrollmentModel);
        // 当 userProvider 获取当前机构 Id 的时候返回 "agencyId"
        when(userProvider.getCurrentAgencyId()).thenReturn("agencyId");

        // 定义 GroupEnrollmentTeamEntity
        GroupEnrollmentTeamEntity groupEnrollmentTeamEntity = new GroupEnrollmentTeamEntity();
        // 设置 GroupEnrollmentTeamEntity 的 EnrollmentId
        groupEnrollmentTeamEntity.setEnrollmentId("child1");
        // 设置 GroupEnrollmentTeamEntity 的 TeacherId
        groupEnrollmentTeamEntity.setTeacherId("teacherId1");
        // 设置删除状态
        groupEnrollmentTeamEntity.setDeleted(false);
        // 当 groupEnrollmentTeamEntityDao 通过班级 Id 获取对应的 GroupEnrollmentTeamEntity 的时候，返回已经 Mock 的数据
        when(groupEnrollmentTeamEntityDao.listByGroupIds(anyList())).thenReturn(Collections.singletonList(groupEnrollmentTeamEntity));
        // 调用方法，并定义预期结果
        GetGroupTeamsResponse response = groupServiceImpl.getGroupTeams("groupId", "true");

        // 验证结果
        Assert.assertNotNull(response);
        assertEquals(0, response.getEnrollmentTeamModelList().size());

        // 验证数据库操作
        verify(groupDao).getGroupWithCenter("groupId");
        verify(userDao).getTeachers("groupId");
        verify(studentDao).getAllByGroupId("groupId");
        verify(studentDao).getAllChildrenByIds(Collections.singletonList("CHILD1"));
    }


    /**
     * 当期望不分组的时候，将所有小孩默认放在第一个老师的分组中（管理员）
     * 预期结果：返回包含小组信息的 GetGroupTeamsResponse
     * 测试场景：enableGroupTeams 为 false，将所有小孩默认放在第一个老师的分组中
     */
    @Test
    public void getGroupTeams_No() {
        // 模拟数据
        com.learninggenie.common.data.model.GroupEntity groupEntity = new com.learninggenie.common.data.model.GroupEntity();
        // 设置 group 的 Id
        groupEntity.setId("groupId");
        // 当调用 groupDao.getGroupWithCenter 方法时，返回空列表
        when(groupDao.getGroupWithCenter(any())).thenReturn(groupEntity);
        // 定义 UserModel
        UserModel model = new UserModel();
        // 设置 UserModel 的 Id
        model.setId("teacherId1");
        // 设置 UserModel 的 DisplayName
        model.setDisplayName("teacher1");
        // 定义 UserModels，用于存储 UserModel
        List<UserModel> userModels = Collections.singletonList(model);
        // 当调用 userDao.getTeachers 方法时，返回 userModels
        when(userDao.getTeachers(anyString())).thenReturn(userModels);
        // 定义 EnrollmentEntity
        EnrollmentEntity enrollmentEntity = new EnrollmentEntity();
        // 设置 EnrollmentEntity 的 Id
        enrollmentEntity.setId("child1");
        // 设置 EnrollmentEntity 的 DisplayName
        enrollmentEntity.setDisplayName("child1");
        // 定义 GroupEntity
        GroupEntity group = new GroupEntity();
        // 设置 GroupEntity 的 Id
        group.setId("groupId");
        // 设置 enrollmentEntity 的 GroupEntity
        enrollmentEntity.setGroup(group);
        // 定义 EnrollmentEntities，用于存储 EnrollmentEntity
        List<EnrollmentEntity> enrollmentEntities = Collections.singletonList(enrollmentEntity);
        // 当调用 studentDao.getAllByGroupId 方法时，返回 enrollmentEntities
        when(studentDao.getAllByGroupId(any())).thenReturn(enrollmentEntities);
        // 定义 EnrollmentModel
        EnrollmentModel enrollmentModel = new EnrollmentModel();
        // 设置 EnrollmentModel 的 Id
        enrollmentModel.setId("child1");
        // 设置 EnrollmentModel 的 DisplayName
        enrollmentModel.setDisplayName("child1");
        // 定义 EnrollmentModels，用于存储 EnrollmentModel
        List<EnrollmentModel> enrollmentModels = Collections.singletonList(enrollmentModel);
        // 当 userProvider 获取当前机构 Id 的时候返回 "agencyId"
        when(userProvider.getCurrentAgencyId()).thenReturn("agencyId");
        when(userProvider.getCurrentUserId()).thenReturn("userId");

        // 当用户 checkUser 的时候，返回 管理员
        UserEntity userEntity = new UserEntity();
        // 设置用户角色
        userEntity.setRole(UserRole.AGENCY_ADMIN.toString());
        // 如果调用 userProvider.checkUser 方法，返回 userEntity
        when(userProvider.checkUser(anyString())).thenReturn(userEntity);

        // 调用方法，并定义预期结果
        GetGroupTeamsResponse response = groupServiceImpl.getGroupTeams("groupId", "false");

        // 验证结果
        Assert.assertNotNull(response);
        assertEquals(0, response.getEnrollmentTeamModelList().size());

        // 验证数据库操作
        verify(groupDao).getGroupWithCenter("groupId");
        verify(userDao).getTeachers("groupId");
        verify(studentDao).getAllByGroupId("groupId");
        verify(studentDao).getAllChildrenByIds(Collections.singletonList("CHILD1"));
    }

    /**
     * 当期望不分组的时候，将所有小孩默认放在第一个老师的分组中
     * 预期结果：返回包含小组信息的 GetGroupTeamsResponse
     * 测试场景：enableGroupTeams 为 false，将所有小孩默认放在第一个老师的分组中
     */
    @Test
    public void getGroupTeams_No_Teacher() {
        // 模拟数据
        com.learninggenie.common.data.model.GroupEntity groupEntity = new com.learninggenie.common.data.model.GroupEntity();
        // 设置 group 的 Id
        groupEntity.setId("groupId");
        // 当调用 groupDao.getGroupWithCenter 方法时，返回空列表
        when(groupDao.getGroupWithCenter(any())).thenReturn(groupEntity);
        // 定义 UserModel
        UserModel model = new UserModel();
        // 设置 UserModel 的 Id
        model.setId("teacherId1");
        // 设置 UserModel 的 DisplayName
        model.setDisplayName("teacher1");
        // 定义 UserModels，用于存储 UserModel
        List<UserModel> userModels = Collections.singletonList(model);
        // 当调用 userDao.getTeachers 方法时，返回 userModels
        when(userDao.getTeachers(anyString())).thenReturn(userModels);
        // 定义 EnrollmentEntity
        EnrollmentEntity enrollmentEntity = new EnrollmentEntity();
        // 设置 EnrollmentEntity 的 Id
        enrollmentEntity.setId("child1");
        // 设置 EnrollmentEntity 的 DisplayName
        enrollmentEntity.setDisplayName("child1");
        // 定义 GroupEntity
        GroupEntity group = new GroupEntity();
        // 设置 GroupEntity 的 Id
        group.setId("groupId");
        // 设置 enrollmentEntity 的 GroupEntity
        enrollmentEntity.setGroup(group);
        // 定义 EnrollmentEntities，用于存储 EnrollmentEntity
        List<EnrollmentEntity> enrollmentEntities = Collections.singletonList(enrollmentEntity);
        // 当调用 studentDao.getAllByGroupId 方法时，返回 enrollmentEntities
        when(studentDao.getAllByGroupId(any())).thenReturn(enrollmentEntities);
        // 定义 EnrollmentModel
        EnrollmentModel enrollmentModel = new EnrollmentModel();
        // 设置 EnrollmentModel 的 Id
        enrollmentModel.setId("child1");
        // 设置 EnrollmentModel 的 DisplayName
        enrollmentModel.setDisplayName("child1");
        // 定义 EnrollmentModels，用于存储 EnrollmentModel
        List<EnrollmentModel> enrollmentModels = Collections.singletonList(enrollmentModel);
        // 当 userProvider 获取当前机构 Id 的时候返回 "agencyId"
        when(userProvider.getCurrentAgencyId()).thenReturn("agencyId");
        when(userProvider.getCurrentUserId()).thenReturn("userId");

        // 当用户 checkUser 的时候，返回 管理员
        UserEntity userEntity = new UserEntity();
        // 设置用户角色
        userEntity.setRole(UserRole.COLLABORATOR.toString());
        // 如果调用 userProvider.checkUser 方法，返回 userEntity
        when(userProvider.checkUser(anyString())).thenReturn(userEntity);

        // 调用方法，并定义预期结果
        GetGroupTeamsResponse response = groupServiceImpl.getGroupTeams("groupId", "false");

        // 验证结果
        Assert.assertNotNull(response);
        assertEquals(0, response.getEnrollmentTeamModelList().size());

        // 验证数据库操作
        verify(groupDao).getGroupWithCenter("groupId");
        verify(userDao).getTeachers("groupId");
        verify(studentDao).getAllByGroupId("groupId");
        verify(studentDao).getAllChildrenByIds(Collections.singletonList("CHILD1"));
    }

    /**
     * 测试班级没有学生，只有老师情况
     */
    @Test
    public void testGetGroupTeamsNoStudents() {
        // 创建班级信息对象
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId("groupId");

        // 创建老师信息列表
        UserModel teacher1 = new UserModel();
        teacher1.setId("teacherId1");
        UserModel teacher2 = new UserModel();
        teacher2.setId("teacherId2");
        List<UserModel> teachers = Arrays.asList(teacher1, teacher2);

        // 设置数据库操作的预期行为
        // 定义 GroupEntity
        com.learninggenie.common.data.model.GroupEntity groupEntity2 = new com.learninggenie.common.data.model.GroupEntity();
        // 设置 group 的 Id
        groupEntity2.setId("groupId");
        when(groupDao.getGroupWithCenter("groupId")).thenReturn(groupEntity2);
        when(userDao.getTeachers("groupId")).thenReturn(teachers);
        when(studentDao.getAllByGroupId("groupId")).thenReturn(new ArrayList<>());

        // 调用方法
        GetGroupTeamsResponse response = groupServiceImpl.getGroupTeams("groupId", "true");

        // 验证结果
        assertNotNull(response);
        assertEquals(2, response.getTeacherList().size());
    }


    /**
     * 测试方法：convertGroupEnrollmentTeam2Response_ValidData_ReturnsGetGroupTeamsResponse
     * 测试场景：合法的输入数据，包含班级信息、小组信息、教师信息
     * 预期结果：返回正确的 GetGroupTeamsResponse
     */
    @Test
    public void testConvertGroupEnrollmentTeam2Response_ValidData_ReturnsGetGroupTeamsResponse() {
        // 模拟数据
        com.learninggenie.common.data.model.GroupEntity groupEntity = new com.learninggenie.common.data.model.GroupEntity();
        // 设置 group 的 Id
        groupEntity.setId("groupId");
        // 设置 group 的名称
        groupEntity.setName("groupName");

        // 设置 group 的元数据
        List<GroupEnrollmentTeamEntity> enrollmentTeamEntities = Arrays.asList(
                createEnrollmentTeamEntity("enrollmentId1", "teacherId1"),
                createEnrollmentTeamEntity("enrollmentId2", "teacherId2")
        );

        // 设置教师信息
        List<UserModel> teachers = Arrays.asList(
                createUserModel("teacherId1", "Teacher1", "Last1", "<EMAIL>", "avatar1"),
                createUserModel("teacherId2", "Teacher2", "Last2", "<EMAIL>", "avatar2")
        );

        // 设置小孩元数据
        List<EnrollmentMetaDataEntity> childrenAllMeta = Arrays.asList(
                createEnrollmentMetaDataEntity("enrollmentId1", "IEP/IFSP", "true"),
                createEnrollmentMetaDataEntity("enrollmentId1", DrdpAttr.ELD.toString(), "false"),
                createEnrollmentMetaDataEntity("enrollmentId2", "IEP/IFSP", "false"),
                createEnrollmentMetaDataEntity("enrollmentId2", DrdpAttr.ELD.toString(), "true")
        );

        // 设置小孩信息
        List<EnrollmentModel> allChildren = Arrays.asList(
                createEnrollmentModel("enrollmentId1", "Child1", "First1", "Last1", "avatar1"),
                createEnrollmentModel("enrollmentId2", "Child2", "First2", "Last2", "avatar2")
        );

        // 当 groupsMetaDataEntityDao 通过班级 Id 获取对应的 GroupsMetaDataEntity 的时候，返回已经 Mock 的数据
        when(groupsMetaDataEntityDao.getByGroupIdsAndKey(anyList(), anyString())).thenReturn(
                Collections.singletonList(createGroupsMetaDataEntity("groupId", GroupMetaKey.GROUP_TEAM_SIZE_LIMIT.toString(), "2,5"))
        );

        // 当 fileSystem 通过相对路径获取对应的绝对路径的时候，返回已经 Mock 的数据
        when(fileSystem.getPublicUrl(anyString())).thenAnswer(invocation -> "http://example.com/" + invocation.getArgument(0));

        // 调用方法
        GetGroupTeamsResponse response = groupServiceImpl.convertGroupEnrollmentTeam2Response(enrollmentTeamEntities, teachers, childrenAllMeta, allChildren, groupEntity);

        // 验证结果
        assertNotNull(response);
        assertEquals("groupName", response.getGroupName());
        assertEquals(2, response.getMinTeamSize());
        assertEquals(5, response.getMaxTeamSize());
        assertEquals(2, response.getEnrollmentTeamModelList().size());
        assertEquals(2, response.getTeacherList().size());

        // 验证教师信息
        assertEquals("<EMAIL>", response.getTeacherList().get(0).getPrimaryEmail());

        // 验证小组信息
        assertEquals("enrollmentId1", response.getEnrollmentTeamModelList().get(0).getEnrollmentId());
        assertTrue(response.getEnrollmentTeamModelList().get(0).getIep());
        assertFalse(response.getEnrollmentTeamModelList().get(0).getEld());

        // 验证方法调用
        verify(groupsMetaDataEntityDao).getByGroupIdsAndKey(Collections.singletonList("groupId"), GroupMetaKey.GROUP_TEAM_SIZE_LIMIT.toString());
        verify(fileSystem, times(2)).getPublicUrl(anyString());
    }

    /**
     * 测试方法：convertEnrollment2GroupTermEntity_ValidData_ReturnsGroupEnrollmentTeamEntity
     * 测试场景：合法的输入数据，包含小孩信息、班级信息、第一个老师信息
     * 预期结果：返回正确的 GroupEnrollmentTeamEntity
     */
    @Test
    public void testConvertEnrollment2GroupTermEntity_ValidData_ReturnsGroupEnrollmentTeamEntity() {
        // 模拟数据
        // 定义 EnrollmentModel
        EnrollmentModel child = createEnrollmentModel("childId", "Child", "First", "Last", "avatar");
        // 定义 GroupEntity
        com.learninggenie.common.data.model.GroupEntity groupEntity = new com.learninggenie.common.data.model.GroupEntity();
        // 设置 group 的 Id
        groupEntity.setId("groupId");
        // 定义 UserModel
        UserModel firstTeacher = createUserModel("teacherId", "First", "Teacher", "<EMAIL>", "teacherAvatar");
        // 设置 finalAgencyId
        String finalAgencyId = "agencyId";

        // 调用方法
        GroupEnrollmentTeamEntity teamEntity = groupServiceImpl.convertEnrollment2GroupTermEntity(child, groupEntity, firstTeacher, finalAgencyId);

        // 验证结果
        assertNotNull(teamEntity);
        assertNotNull(teamEntity.getId());
        assertEquals("childId", teamEntity.getEnrollmentId());
        assertEquals("groupId", teamEntity.getGroupId());
        assertEquals("teacherId", teamEntity.getTeacherId());
        assertNotNull(teamEntity.getCreateAtUtc());
        assertNotNull(teamEntity.getUpdateAtUtc());
        assertFalse(teamEntity.getDeleted());
        assertEquals("agencyId", teamEntity.getTenantId());

        // 验证方法调用
        verifyNoMoreInteractions(groupDao, userDaoImpl, studentDao, groupEnrollmentTeamEntityDao, groupsMetaDataEntityDao, userProvider, fileSystem);
    }

    /**
     * 测试方法：updateGroupTeams_ValidData_ReturnsSuccessResponse
     * 测试场景：合法的输入数据，包含更新、保存和删除分组信息
     * 预期结果：返回正确的 SuccessResponse，分组信息被正确更新、保存和删除
     */
    @Test
    public void testUpdateGroupTeams_ValidData_ReturnsSuccessResponse() {
        // 创建请求对象
        UpdateGroupTeamsRequest request = new UpdateGroupTeamsRequest();
        request.setGroupId("groupId");
        request.setMinTeamSize(2);
        request.setMaxTeamSize(5);
        EnrollmentTeamModel enrollmentTeamModel = new EnrollmentTeamModel();
        enrollmentTeamModel.setEnrollmentId("enrollmentId");
        enrollmentTeamModel.setTeacherId("teacherId");
        enrollmentTeamModel.setFirstName("firstName");
        enrollmentTeamModel.setLastName("lastName");
        enrollmentTeamModel.setDisplayName("displayName");
        enrollmentTeamModel.setAvatarUrl("avatarUrl");
        enrollmentTeamModel.setIep(true);
        enrollmentTeamModel.setEld(false);

        EnrollmentTeamModel enrollmentTeamModel2 = new EnrollmentTeamModel();
        enrollmentTeamModel2.setEnrollmentId("enrollmentId2");
        enrollmentTeamModel2.setTeacherId("teacherId2");
        enrollmentTeamModel2.setFirstName("firstName2");
        enrollmentTeamModel2.setLastName("lastName2");
        enrollmentTeamModel2.setDisplayName("displayName2");
        enrollmentTeamModel2.setAvatarUrl("avatarUrl2");
        enrollmentTeamModel2.setIep(false);
        enrollmentTeamModel2.setEld(true);

        request.setEnrollmentTeamModelList(Arrays.asList(
                enrollmentTeamModel2, enrollmentTeamModel
        ));

        // 创建班级信息对象
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId("groupId");

        // 创建老师信息列表
        UserModel teacher1 = new UserModel();
        teacher1.setId("teacherId1");
        UserModel teacher2 = new UserModel();
        teacher2.setId("teacherId2");
        List<UserModel> teachers = Arrays.asList(teacher1, teacher2);

        // 创建小孩信息列表
        EnrollmentEntity child1 = new EnrollmentEntity();
        child1.setId("childId1");
        EnrollmentEntity child2 = new EnrollmentEntity();
        child2.setId("childId2");
        List<EnrollmentEntity> allChildren = Arrays.asList(child1, child2);

        // 创建分组信息列表
        GroupEnrollmentTeamEntity groupEnrollmentTeamEntity = new GroupEnrollmentTeamEntity();
        groupEnrollmentTeamEntity.setEnrollmentId("childId1");
        groupEnrollmentTeamEntity.setGroupId("groupId");
        groupEnrollmentTeamEntity.setTeacherId("teacherId1");
        List<GroupEnrollmentTeamEntity> groupEnrollmentTeamEntities = new ArrayList<>();
        groupEnrollmentTeamEntities.add(groupEnrollmentTeamEntity);

        // 模拟用户信息
        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setAgencyId("agencyId");
        when(userProvider.getCurrentUser()).thenReturn(currentUser);

        // 设置数据库操作的预期行为
        // 定义 GroupEntity
        com.learninggenie.common.data.model.GroupEntity groupEntity2 = new com.learninggenie.common.data.model.GroupEntity();
        // 设置 group 的 Id
        groupEntity2.setId("groupId");
        when(groupDao.getGroupWithCenter("groupId")).thenReturn(groupEntity2);
        when(userDao.getTeachers("groupId")).thenReturn(teachers);
        when(studentDao.getAllByGroupId("groupId")).thenReturn(allChildren);
        when(studentDao.getAllChildrenByIds(anyList())).thenReturn(Arrays.asList(
                createEnrollmentModel("enrollmentId1", "Child1", "First1", "Last1", "avatar1"),
                createEnrollmentModel("enrollmentId2", "Child2", "First2", "Last2", "avatar2")
        ));
        when(groupEnrollmentTeamEntityDao.listByGroupIds(Collections.singletonList("groupId"))).thenReturn(groupEnrollmentTeamEntities);
        when(groupsMetaDataEntityDao.getByGroupIdAndMetaKey("groupId", GroupMetaKey.GROUP_TEAM_SIZE_LIMIT.toString())).thenReturn(null);

        // 调用方法
        SuccessResponse response = groupServiceImpl.updateGroupTeams(request);

        // 验证结果
        assertTrue(response.isSuccess());

        // 验证数据库操作方法调用
        verify(groupDao).getGroupWithCenter("groupId");
        verify(userDao).getTeachers("groupId");
        verify(studentDao).getAllByGroupId("groupId");
        verify(studentDao).getAllChildrenByIds(Arrays.asList("childId1", "childId2"));
        verify(groupEnrollmentTeamEntityDao).listByGroupIds(Collections.singletonList("groupId"));
        verify(groupsMetaDataEntityDao).getByGroupIdAndMetaKey("groupId", GroupMetaKey.GROUP_TEAM_SIZE_LIMIT.toString());
        verify(groupEnrollmentTeamEntityDao, times(2)).saveOrUpdateBatch(anyList());
        verify(groupsMetaDataEntityDao).saveOrUpdate(any(GroupsMetaDataEntity.class));
    }


    /**
     * 创建 GroupEnrollmentTeamEntity 实体
     *
     * @param enrollmentId 小孩 Id
     * @param teacherId    教师id
     * @return {@link GroupEnrollmentTeamEntity}
     */
    private GroupEnrollmentTeamEntity createEnrollmentTeamEntity(String enrollmentId, String teacherId) {
        // 创建 GroupEnrollmentTeamEntity 实体
        GroupEnrollmentTeamEntity entity = new GroupEnrollmentTeamEntity();
        // 设置小孩 Id
        entity.setEnrollmentId(enrollmentId);
        // 设置教师 Id
        entity.setTeacherId(teacherId);
        // 返回 GroupEnrollmentTeamEntity 实体
        return entity;
    }

    /**
     * 创建 UserModel
     *
     * @param id             身份证件
     * @param firstName      名字
     * @param lastName       姓
     * @param email          电子邮件
     * @param avatarMediaUrl 头像的链接地址
     * @return {@link UserModel}
     */
    private UserModel createUserModel(String id, String firstName, String lastName, String email, String avatarMediaUrl) {
        // 创建 UserModel
        UserModel model = new UserModel();
        // 设置用户的 Id
        model.setId(id);
        // 设置用户的名字
        model.setFirstName(firstName);
        // 设置用户的姓
        model.setLastName(lastName);
        // 设置用户的电子邮件
        model.setEmail(email);
        // 设置用户的头像的链接地址
        model.setAvatarMediaUrl(avatarMediaUrl);
        // 返回 UserModel
        return model;
    }

    /**
     * 创建注册元数据实体
     *
     * @param enrollmentId 小孩 Id
     * @param metaKey      指定 metaKey
     * @param metaValue    metadata 的 值
     * @return {@link EnrollmentMetaDataEntity}
     */
    private EnrollmentMetaDataEntity createEnrollmentMetaDataEntity(String enrollmentId, String metaKey, String metaValue) {
        // 创建注册元数据实体
        EnrollmentMetaDataEntity entity = new EnrollmentMetaDataEntity();
        // 设置 metadata 所属的小孩
        entity.setEnrollment(createEnrollmentEntity(enrollmentId, "Child", "First", "Last", "avatar"));
        // 设置指定元键
        entity.setMetaKey(metaKey);
        // 设置 metadata 的 值
        entity.setMetaValue(metaValue);
        // 返回 EnrollmentMetaDataEntity
        return entity;
    }

    /**
     * 创建注册模型
     *
     * @param id          身份证件
     * @param displayName 显示名称
     * @param firstName   名字
     * @param lastName    姓
     * @param avatarUrl   头像url
     * @return {@link EnrollmentModel}
     */
    private EnrollmentModel createEnrollmentModel(String id, String displayName, String firstName, String lastName, String avatarUrl) {
        // 创建小孩对应的 Model
        EnrollmentModel model = new EnrollmentModel();
        // 设置小孩的 Id
        model.setId(id);
        // 设置小孩的显示名称
        model.setDisplayName(displayName);
        // 设置小孩的名字
        model.setFirstName(firstName);
        // 设置小孩的姓
        model.setLastName(lastName);
        // 设置小孩的头像url
        model.setAvatarUrl(avatarUrl);
        // 返回 EnrollmentModel
        return model;
    }

    /**
     * 创建注册实体
     *
     * @param id          身份证件
     * @param displayName 显示名称
     * @param firstName   名字
     * @param lastName    姓
     * @param avatarUrl   头像url
     * @return {@link EnrollmentEntity}
     */
    private EnrollmentEntity createEnrollmentEntity(String id, String displayName, String firstName, String lastName, String avatarUrl) {
        // 创建小孩对应的 Entity
        EnrollmentEntity entity = new EnrollmentEntity();
        // 设置小孩的 Id
        entity.setId(id);
        // 设置小孩的显示名称
        entity.setDisplayName(displayName);
        // 设置小孩的名字
        entity.setFirstName(firstName);
        // 设置小孩的姓
        entity.setLastName(lastName);
        // 设置小孩的头像url
        MediaEntity avatarMedia = new MediaEntity();
        avatarMedia.setRelativePath(avatarUrl);
        entity.setAvatarMedia(avatarMedia);
        // 返回 EnrollmentEntity
        return entity;
    }

    /**
     * 创建组元数据实体
     *
     * @param groupId   组id
     * @param metaKey   指定元键
     * @param metaValue 元价值
     * @return {@link GroupsMetaDataEntity}
     */
    private GroupsMetaDataEntity createGroupsMetaDataEntity(String groupId, String metaKey, String metaValue) {
        // 创建班级 metadata 实体
        GroupsMetaDataEntity entity = new GroupsMetaDataEntity();
        // 设置班级 Id
        entity.setGroupId(groupId);
        // 设置指定元键
        entity.setMetaKey(metaKey);
        // 设置 metadata 的值
        entity.setMetaValue(metaValue);
        return entity;
    }

    @Test
    public void testSendInvitation() throws IOException {
        // 准备阶段
        // 创建一个 SendInvitationsRequest 对象，并设置其属性
        SendInvitationsRequest request = new SendInvitationsRequest();
        request.setUserId("testUserId");
        request.setInvitationIds(new String[]{"testInvitationId"});

        // 创建一个 UserEntity 对象，并设置其属性
        UserEntity userEntity = new UserEntity();
        userEntity.setId("testUserId");
        userEntity.setRole("SITE_ADMIN");

        // 当调用 userProvider 的 checkUser 方法时，返回上面创建的 UserEntity 对象
        when(userProvider.checkUser(anyString())).thenReturn(userEntity);
        // 当调用 invitationDao 的 getGroupById 方法时，返回一个新的 GroupEntity 对象
        when(invitationDao.getGroupById(anyString())).thenReturn(new GroupEntity());

        // 创建一个 CenterEntity 对象，并设置其属性
        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setId("testCenterId");

        // 创建一个 CenterEntity 对象的列表，并添加上面创建的 CenterEntity 对象
        List<CenterEntity> centers = new ArrayList<>();
        centers.add(centerEntity);

        // 当调用 centerDao 的 getBySiteAdminId 方法时，返回上面创建的 CenterEntity 对象的列表
        when(centerDao.getBySiteAdminId(anyString())).thenReturn(centers);
        // 当调用 invitationDao 的 getGroupInvitationEntity 方法时，返回一个新的 GroupInvitationEntity 对象
        when(invitationDao.getGroupInvitationEntity(anyString())).thenReturn(new GroupInvitationEntity());
        // 当调用 fileSystem 的 upload 方法时，返回 "testUrl"
        when(fileSystem.upload(anyString(), anyString(), any())).thenReturn("testUrl");
        // 当调用 fileSystem 的 getPublicUrl 方法时，返回 "testPublicUrl"
        when(fileSystem.getPublicUrl(anyString(), anyString())).thenReturn("testPublicUrl");

        // 使用反射设置 groupServiceImpl 的字段值
        ReflectionTestUtils.setField(groupServiceImpl, "emailTemplateVersion", "v1");
        ReflectionTestUtils.setField(groupServiceImpl, "s3Root", "s3Root");
        ReflectionTestUtils.setField(groupServiceImpl, "pdfEndpoint", "pdfEndpoint");
        ReflectionTestUtils.setField(groupServiceImpl, "pdfBucket", "pdfBucket");

        // 创建一个 HtmlToPdf 的静态 mock
        MockedStatic<HtmlToPdf> htmlToPdf = mockStatic(HtmlToPdf.class);
        // 当调用 HtmlToPdf 的 convertHtmlToPdf 方法时，返回一个新的 ByteArrayInputStream 对象
        htmlToPdf.when(() -> HtmlToPdf.convertHtmlToPdf(anyList(), any())).thenReturn(new ByteArrayInputStream(new byte[10]));

        // 动作阶段
        // 调用被测试的方法
        groupServiceImpl.sendInvitation(request);

        // 断言阶段
        // 验证 emailService 的 sendEmailAndCCWithAttachment 方法被调用了一次
        verify(emailService, times(1)).sendEmailAndCCWithAttachment(any(), anyString(), any());
        // 关闭 HtmlToPdf 的静态 mock
        htmlToPdf.close();
    }

    /**
     * 测试获取记录的老师
     */
    @Test
    public void testGetRecordTeacher() {
        // 准备参数
        String groupId = "response1";
        String childId = "testChildId";
        String childId2 = "testChildId2";
        String userId = "testCurrentUserId";
        String agencyId = "AgencyId";
        boolean featureOpen = true;
        // 机构模型
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        agencyModel.setName("AgencyName");

        // 小孩 id 数组
        List<String> childIds = new ArrayList<>();
        childIds.add(childId);
        childIds.add(childId2);
        List<ChildEntity> childEntities = new ArrayList<>();
        ChildEntity childEntity = new ChildEntity();
        childEntity.setId(childId);
        childEntities.add(childEntity);
        ChildEntity childEntity2 = new ChildEntity();
        childEntity2.setId(childId2);
        childEntities.add(childEntity2);

        // 小孩属性
        List<StudentAttrEntity> studentAttrEntities = new ArrayList<>();
        StudentAttrEntity studentAttrEntity = new StudentAttrEntity();
        studentAttrEntity.setAttrName(ChildMetaKey.Teacher.toString());
        studentAttrEntity.setAttrValue("<EMAIL>");
        studentAttrEntity.setEnrollmentId(childId);
        studentAttrEntities.add(studentAttrEntity);

        // 确认属性
        UserMetaDataEntity userMetaDataEntity = new UserMetaDataEntity();
        userMetaDataEntity.setMetaKey(UserMetaKey.EDIT_CLASS_CONFIRM_RECORD_TEACHER.toString());
        userMetaDataEntity.setMetaValue("true");

        // DRDP 上传周期别名
        AgencyMetaDataEntity aliasMeta = new AgencyMetaDataEntity();
        aliasMeta.setMetaKey(DrdpSettingKey.UPLOAD_DRDP_FILTER_ALIAS.toString());
        aliasMeta.setMetaValue("spring,summer,fall,winter");

        // mock 数据
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        // mock getAgencyByUserId
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        when(userProvider.isGranteeTeacher(userId)).thenReturn(true);
        when(userProvider.getAgencyOpenDefaultOpen(agencyId, AgencyMetaKey.DRDP_UPLOAD.toString())).thenReturn(featureOpen);
//        when(userProvider.getAgencyOpenDefaultClose(agencyId, AgencyMetaKey.LOCK_SKIP_CHECK_OPEN.toString())).thenReturn(true);
        Set<String> uploadGroupIds = new HashSet<>();
        doNothing().when(scoreService).setUploadCenterGroupIds(agencyId, new HashSet<>(), uploadGroupIds);
        when(groupDao.getChilds(groupId)).thenReturn(childEntities);
        when(studentDao.getAttrsByChildIdsAndMetaKey(childIds, ChildMetaKey.Teacher.toString())).thenReturn(studentAttrEntities);
        when(userDao.getMetaData(userId, UserMetaKey.EDIT_CLASS_CONFIRM_RECORD_TEACHER.toString())).thenReturn(userMetaDataEntity);
        // 获取当前周期
//        when(agencyDao.getMeta(agencyId, DrdpSettingKey.UPLOAD_DRDP_FILTER_ALIAS.toString())).thenReturn(aliasMeta);
        UserModel currentUser = new UserModel();
        currentUser.setRole(UserRole.COLLABORATOR.toString());
        when(userDao.getUserById(userId)).thenReturn(currentUser);
        List<CenterGroupResponse> specialTeacherGroups = new ArrayList<>();
        CenterGroupResponse centerGroupResponse = new CenterGroupResponse();
        centerGroupResponse.setId(groupId);
        when(groupServiceImpl.getSpecialTeacherGroups(userId)).thenReturn(specialTeacherGroups);

        RecordTeacherResponse response = groupServiceImpl.getRecordTeacher(groupId, childId, "spring");
        // 断言验证
        assertFalse(response.isShowPopup());
        assertFalse(response.isLockShowPopup());
        assertEquals(1, response.getChildTeacherEmails().size());
        assertEquals("<EMAIL>", response.getChildTeacherEmail());

        UserModel currentUser1 = new UserModel();
        currentUser1.setRole(UserRole.SITE_ADMIN.toString());
        when(userDao.getUserById(userId)).thenReturn(currentUser1);
        List<com.learninggenie.common.data.model.GroupEntity> groupEntities = new ArrayList<>();
        com.learninggenie.common.data.model.GroupEntity groupEntity = new com.learninggenie.common.data.model.GroupEntity();
        groupEntity.setId("testGroupId01");
        groupEntities.add(groupEntity);
        when(groupDao.getGroupByAgencyId(agencyId)).thenReturn(groupEntities);
        RecordTeacherResponse response1 = groupServiceImpl.getRecordTeacher(groupId, childId, "spring");
        // 断言验证
        assertFalse(response1.isShowPopup());
        assertFalse(response1.isLockShowPopup());
        assertEquals(1, response1.getChildTeacherEmails().size());
        assertEquals("<EMAIL>", response1.getChildTeacherEmail());
    }

    /**
     * 获取班级观察状态的 HTML
     */
    @Test
    public void testGetClassStatusPdf() throws Exception {
        this.beforeMethod();
        // 数据准备
        String currentUserId = "currentUserId";
        String agencyId = "AgencyId";
        String groupId = "testGroupId";
        String fromDate = "2022-01-01";
        String language = "en";
        String alias = "2023-2024 Fall";
        String currentTime = null;
        boolean downLoadCore = true;
        String frameworkId = "testFrameworkId";
        // 机构属性
        AgencyEntity agency = new AgencyEntity();
        agency.setId(agencyId);
        // 自定义标签列表
        List<TagModel> cunsomTagList = new ArrayList<>();


        JSONObject languageJson =new JSONObject();
        languageJson.put("key","value");

        // 创建自定义标签
        TagModel cunstomTagModel = new TagModel();
        cunstomTagModel.setId("TagId01");
        cunstomTagModel.setValue("TagValue01");
        cunstomTagModel.setCategoryId("TagCategoryId");
        cunstomTagModel.setType("Custom");
        TagModel cunstomTagModel02 = new TagModel();
        cunstomTagModel02.setId("TagId02");
        cunstomTagModel02.setValue("TagValue02");
        cunstomTagModel02.setCategoryId("TagCategoryId");
        cunstomTagModel02.setType("Custom");
        TagModel cunstomTagModel03 = new TagModel();
        cunstomTagModel03.setId("TagId03");
        cunstomTagModel03.setValue("TagValue03");
        cunstomTagModel03.setCategoryId("TagCategoryId");
        cunstomTagModel03.setType("Custom");

        cunsomTagList.add(cunstomTagModel);
        cunsomTagList.add(cunstomTagModel02);
        cunsomTagList.add(cunstomTagModel03);

        // 系统标签列表
        List<TagModel> workSampleTagList = new ArrayList<>();
        TagModel workSampleTagModel = new TagModel();
        workSampleTagModel.setId("TagId11");
        workSampleTagModel.setValue("TagValue04");
        workSampleTagModel.setCategoryId("TagCategoryId");
        workSampleTagModel.setType("workSample");
        TagModel workSampleTagModel01 = new TagModel();
        workSampleTagModel01.setId("TagId12");
        workSampleTagModel01.setValue("TagValue05");
        workSampleTagModel01.setCategoryId("TagCategoryId");
        workSampleTagModel01.setType("workSample");
        TagModel workSampleTagModel02 = new TagModel();
        workSampleTagModel02.setId("TagId13");
        workSampleTagModel02.setValue("TagValue06");
        workSampleTagModel02.setCategoryId("TagCategoryId");
        workSampleTagModel02.setType("workSample");

        workSampleTagList.add(workSampleTagModel);
        workSampleTagList.add(workSampleTagModel01);
        workSampleTagList.add(workSampleTagModel02);

        // 创建系统测评点
        com.learninggenie.common.data.model.DomainEntity groupDomainEntity = new com.learninggenie.common.data.model.DomainEntity();
        groupDomainEntity.setId("domainId01");
        groupDomainEntity.setAbbreviation("System");
        groupDomainEntity.setName("domainName01");
        groupDomainEntity.setParentId("domainParentId");
        groupDomainEntity.setMappingAbbr("domainMappingAbbr");
        groupDomainEntity.setDescription("domainDescription");
        groupDomainEntity.setUseCondition("domainUseCondition");

        // 所有标签与测评点
        TagResponse tagResponse = new TagResponse();
        tagResponse.setCustom(cunsomTagList);
        tagResponse.setWorkSample(workSampleTagList);
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId("groupId");
        groupEntity.setName("testGroup");
        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setName("testCenter");
        groupEntity.setCenter(centerEntity);
        // 系统标签
        TagModel workSampleTagModelDeleted = new TagModel();
        workSampleTagModelDeleted.setId("TagDeleteId");
        workSampleTagModelDeleted.setValue("TagDelete");
        workSampleTagModelDeleted.setCategoryId("TagCategoryId");
        workSampleTagModelDeleted.setType("workSample");
        // 所有标签
        List<TagModel> allTagNoteList = new ArrayList<>();
        allTagNoteList.addAll(workSampleTagList);
        allTagNoteList.addAll(cunsomTagList);
        allTagNoteList.add(workSampleTagModelDeleted);

        when(domainDao.getDomain(anyString())).thenReturn(groupDomainEntity);
        when(userProvider.getCurrentUserId()).thenReturn(currentUserId);
        when(groupProvider.checkGroup(groupId)).thenReturn(groupEntity);
        List<GroupFrameworkStatsModel> groupFrameworkStatsModels = new ArrayList<>();
        when(studentDao.getUsedFrameworksByGroup(groupId)).thenReturn(groupFrameworkStatsModels);
        Map<String, Boolean> iepMap = new HashMap<>();
        when(enrollmentProvider.getIEPMapByGroup(groupId)).thenReturn(iepMap);
        when(userService.hideIEPOpen(anyString())).thenReturn(false);
        List<NoteDomainModel> noteDomainWithDateByGroup = new ArrayList<>();
        when(noteDao.getNoteDomainWithDateByGroup(groupId)).thenReturn(noteDomainWithDateByGroup);
        AgencyEntity agencyEntity = new AgencyEntity();
        agencyEntity.setId(agencyId);
        agencyEntity.setName("AgencyName");
        when(agencyDao.getByGroupId(groupId)).thenReturn(agencyEntity);
        UserMetaDataEntity userMetaDataEntity = new UserMetaDataEntity();
        userMetaDataEntity.setMetaKey(UserMetaKey.EDIT_CLASS_CONFIRM_RECORD_TEACHER.toString());
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        agencyModel.setName("AgencyName");
        when(userProvider.getAgencyByUserId(currentUserId)).thenReturn(agencyModel);

        jsonUtilStatic.when(() -> JsonUtil.toJson(anyList())).thenReturn("testJson");
        resourceUtilStatic.when(() -> ResourceUtil.getResourceAsString(anyString())).thenReturn("testHtml");
        List<com.learninggenie.common.data.model.UserEntity> teacherByGroupId = new ArrayList<>();
        when(tagService.getTagByUserId(currentUserId, agency.getId())).thenReturn(tagResponse);
        when(tagDao.getAll()).thenReturn(allTagNoteList);
        ReflectionTestUtils.setField(groupServiceImpl, "s3Root", "s3Root");
        ReflectionTestUtils.setField(groupServiceImpl, "pdfBucket", "pdfBucket");
        ReflectionTestUtils.setField(groupServiceImpl, "pdfEndpoint", "pdfEndpoint");
        when(fileSystem.getPublicUrl(anyString(),anyString())).thenReturn("testPublicUrl");
        // 当用户 checkUser 的时候，返回 管理员
        UserEntity userEntity = new UserEntity();
        // 设置用户角色
        userEntity.setRole(UserRole.AGENCY_ADMIN.toString());
        // 如果调用 userProvider.checkUser 方法，返回 userEntity
        when(userProvider.checkUser(anyString())).thenReturn(userEntity);
//        when(ratingService.getLanguageJson(frameworkId,language)).thenReturn(languageJson);
        // 调用测试方法
        UrlResponse classStatusPdf = groupServiceImpl.getClassStatusPdf(groupId, fromDate, language, currentTime, alias, downLoadCore, frameworkId);
        // 结果校验
        Assert.assertNotNull(classStatusPdf);
        this.afterMethod();
    }

    /**
     * 测试观察进度页面测评点和标签的显示
     */
    @Test
    public void testGetGroupStatus() throws ParseException {
        // 准备参数
        String groupId = "E9C9A103-350A-4989-B6A1-5CA322852A0E";
        String frameworkId = "E9C9A103-350A-4989-B6A1-5CA322852A0E";
        String alias = "Spring_Summer1";
        String userId = "E9C9A103-350A-4989-B6A1";
        String language = "EN_US";
        boolean hideChildAndIEPOpen = false;
        // 定义日期格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        // 小孩列表
        List<ChildEntity> childEntityList = new ArrayList<>();
        // 创建小孩
        ChildEntity childEntity01 = new ChildEntity();
        childEntity01.setId("childId01");
        childEntity01.setFirstName("childFirstName");
        childEntity01.setName("childName01");
        childEntity01.setFrameworkId("FrameworkId01");
        ChildEntity childEntity02 = new ChildEntity();
        childEntity02.setId("childId02");
        childEntity02.setFirstName("childFirstName");
        childEntity02.setName("childName02");
        childEntity02.setFrameworkId("FrameworkId01");
        ChildEntity childEntity03 = new ChildEntity();
        childEntity03.setId("childId03");
        childEntity03.setFirstName("childFirstName");
        childEntity03.setName("childName03");
        childEntity03.setFrameworkId("FrameworkId01");

        childEntityList.add(childEntity01);
        childEntityList.add(childEntity02);
        childEntityList.add(childEntity03);

        // 班级中小孩使用的框架列表
        List<GroupFrameworkStatsModel> usedFrameworks = new ArrayList<>();
        // 创建框架
        GroupFrameworkStatsModel frameworkStatsModel = new GroupFrameworkStatsModel();
        frameworkStatsModel.setFrameworkId("FrameworkId01");
        frameworkStatsModel.setFrameworkName("FrameworkName");
        frameworkStatsModel.setGroupId(groupId);
        frameworkStatsModel.setGroupName("GroupName");
        frameworkStatsModel.setUsedCount(3);
        frameworkStatsModel.setReferenceUrl("ReferenceUrl");
        usedFrameworks.add(frameworkStatsModel);

        // 分数示例对象列表
        List<ScoreExample> scoreExampleList = new ArrayList<>();
        ScoreExample scoreExample = new ScoreExample();
        scoreExample.setExampleName("ExampleName");
        scoreExample.setColumnSize(4);
        List<String> list = new ArrayList<>();
        list.add("Content");
        scoreExample.setContent(list);
        scoreExampleList.add(scoreExample);

        // 评分等级列表
        List<LevelEntity> levelEntities = new ArrayList<>();
        LevelEntity levelEntity = new LevelEntity();
        levelEntity.setId("levelId");
        levelEntity.setValue("levelValue");
        levelEntity.setName("levelName");
        levelEntity.setType("levelType");
        levelEntity.setTip("levelTip");
        levelEntity.setSortIndex("levelSortIndex");
        levelEntity.setFinalName("levelFinalName");
        levelEntity.setFinalValue("levelFinalValue");
        levelEntity.setRated(false);
        levelEntity.setHidden(false);
        levelEntity.setRateWithoutEvidence(false);
        levelEntity.setScoreExamples(scoreExampleList);
        levelEntities.add(levelEntity);

        // 学校属性
        CenterEntity center = new CenterEntity();
        center.setName("testCenter01");

        // 班级属性
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setName("GroupName");
        groupEntity.setDomainId("testDomainId");
        groupEntity.setCenter(center);

        // 测评点列表
        List<com.learninggenie.common.data.model.DomainEntity> domainEntityList = new ArrayList<>();
        // 创建系统测评点
        com.learninggenie.common.data.model.DomainEntity groupDomainEntity = new com.learninggenie.common.data.model.DomainEntity();
        groupDomainEntity.setId("domainId01");
        groupDomainEntity.setAbbreviation("System");
        groupDomainEntity.setName("domainName01");
        groupDomainEntity.setParentId("domainParentId");
        groupDomainEntity.setMappingAbbr("domainMappingAbbr");
        groupDomainEntity.setDescription("domainDescription");
        groupDomainEntity.setUseCondition("domainUseCondition");
        groupDomainEntity.setLevels(levelEntities);
        com.learninggenie.common.data.model.DomainEntity groupDomainEntity02 = new com.learninggenie.common.data.model.DomainEntity();
        groupDomainEntity02.setId("domainId02");
        groupDomainEntity02.setAbbreviation("System");
        groupDomainEntity02.setName("domainName02");
        groupDomainEntity02.setParentId("domainParentId");
        groupDomainEntity02.setMappingAbbr("domainMappingAbbr");
        groupDomainEntity02.setDescription("domainDescription");
        groupDomainEntity02.setUseCondition("domainUseCondition");
        groupDomainEntity02.setLevels(levelEntities);
        com.learninggenie.common.data.model.DomainEntity groupDomainEntity03 = new com.learninggenie.common.data.model.DomainEntity();
        groupDomainEntity03.setId("domainId03");
        groupDomainEntity03.setAbbreviation("System");
        groupDomainEntity03.setName("domainName03");
        groupDomainEntity03.setParentId("domainParentId");
        groupDomainEntity03.setMappingAbbr("domainMappingAbbr");
        groupDomainEntity03.setDescription("domainDescription");
        groupDomainEntity03.setUseCondition("domainUseCondition");
        groupDomainEntity03.setLevels(levelEntities);

        domainEntityList.add(groupDomainEntity);
        domainEntityList.add(groupDomainEntity02);
        domainEntityList.add(groupDomainEntity03);

        // 要统计的框架
        GroupFrameworkStatsModel statsFramework = new GroupFrameworkStatsModel();
        statsFramework.setFrameworkId("FrameworkId01");
        statsFramework.setFrameworkName("FrameworkName");

        // 框架 ID
        String portfolioId = "FrameworkId01";

        // 是否存在 IEP 的小孩
        Map<String, Boolean> iepMap = new HashMap<>();
        iepMap.put("childId01",true);
        iepMap.put("childId02",true);
        iepMap.put("childId03",true);

        // 测评点对应 Note 列表
        List<NoteDomainModel> noteDomainModels = new ArrayList<>();

        NoteDomainModel noteDomainModel = new NoteDomainModel();
        noteDomainModel.setDomainId("domainId01");
        noteDomainModel.setNoteId("NoteId01");
        noteDomainModel.setOldDomainId("OldDomainId");
        noteDomainModel.setChildId("childId01");
        noteDomainModel.setCreateAtLocal(sdf.parse("2024-06-01"));
        NoteDomainModel noteDomainModel02 = new NoteDomainModel();
        noteDomainModel02.setDomainId("domainId02");
        noteDomainModel02.setNoteId("NoteId02");
        noteDomainModel02.setOldDomainId("OldDomainId");
        noteDomainModel02.setChildId("childId01");
        noteDomainModel02.setCreateAtLocal(sdf.parse("2024-06-01"));
        NoteDomainModel noteDomainModel03 = new NoteDomainModel();
        noteDomainModel03.setDomainId("domainId03");
        noteDomainModel03.setNoteId("NoteId03");
        noteDomainModel03.setOldDomainId("OldDomainId");
        noteDomainModel03.setChildId("childId01");
        noteDomainModel03.setCreateAtLocal(sdf.parse("2024-06-01"));
        noteDomainModels.add(noteDomainModel);
        noteDomainModels.add(noteDomainModel02);
        noteDomainModels.add(noteDomainModel03);

        // 机构属性
        AgencyEntity agency = new AgencyEntity();
        agency.setId("agencyId");

        // 系统标签
        TagModel workSampleTagModelDeleted = new TagModel();
        workSampleTagModelDeleted.setId("TagDeleteId");
        workSampleTagModelDeleted.setValue("TagDelete");
        workSampleTagModelDeleted.setCategoryId("TagCategoryId");
        workSampleTagModelDeleted.setType("workSample");

        // 标签对应 Note 列表
        List<NoteTagWithDateModel> childNoteList = new ArrayList<>();
        NoteTagWithDateModel noteTagWithDateModel = new NoteTagWithDateModel();
        noteTagWithDateModel.setTagId("TagId01");
        noteTagWithDateModel.setNoteId("NoteId01");
        noteTagWithDateModel.setChildId("childId01");
        noteTagWithDateModel.setCreateAtLocal(sdf.parse("2024-06-01"));
        NoteTagWithDateModel noteTagWithDateModel02 = new NoteTagWithDateModel();
        noteTagWithDateModel02.setTagId("TagId02");
        noteTagWithDateModel02.setNoteId("NoteId02");
        noteTagWithDateModel02.setChildId("childId01");
        noteTagWithDateModel02.setCreateAtLocal(sdf.parse("2024-06-01"));
        NoteTagWithDateModel noteTagWithDateModel03 = new NoteTagWithDateModel();
        noteTagWithDateModel03.setTagId("TagId03");
        noteTagWithDateModel03.setNoteId("NoteId03");
        noteTagWithDateModel03.setChildId("childId01");
        noteTagWithDateModel03.setCreateAtLocal(sdf.parse("2024-06-01"));
        NoteTagWithDateModel noteTagWithDateModel04 = new NoteTagWithDateModel();
        noteTagWithDateModel04.setTagId("TagId11");
        noteTagWithDateModel04.setNoteId("NoteId01");
        noteTagWithDateModel04.setChildId("childId01");
        noteTagWithDateModel04.setCreateAtLocal(sdf.parse("2024-06-01"));
        NoteTagWithDateModel noteTagWithDateModel05 = new NoteTagWithDateModel();
        noteTagWithDateModel05.setTagId("TagId12");
        noteTagWithDateModel05.setNoteId("NoteId02");
        noteTagWithDateModel05.setChildId("childId01");
        noteTagWithDateModel05.setCreateAtLocal(sdf.parse("2024-06-01"));
        NoteTagWithDateModel noteTagWithDateModel06 = new NoteTagWithDateModel();
        noteTagWithDateModel06.setTagId("TagId13");
        noteTagWithDateModel06.setNoteId("NoteId03");
        noteTagWithDateModel06.setChildId("childId01");
        noteTagWithDateModel06.setCreateAtLocal(sdf.parse("2024-06-01"));
        NoteTagWithDateModel noteTagWithDateModel07 = new NoteTagWithDateModel();
        noteTagWithDateModel07.setTagId(workSampleTagModelDeleted.getId());
        noteTagWithDateModel07.setNoteId("NoteId01");
        noteTagWithDateModel07.setChildId("childId01");
        noteTagWithDateModel07.setCreateAtLocal(sdf.parse("2024-06-01"));

        childNoteList.add(noteTagWithDateModel);
        childNoteList.add(noteTagWithDateModel02);
        childNoteList.add(noteTagWithDateModel03);
        childNoteList.add(noteTagWithDateModel04);
        childNoteList.add(noteTagWithDateModel05);
        childNoteList.add(noteTagWithDateModel06);
        childNoteList.add(noteTagWithDateModel07);

        // 自定义标签列表
        List<TagModel> cunsomTagList = new ArrayList<>();

        // 创建自定义标签
        TagModel cunstomTagModel = new TagModel();
        cunstomTagModel.setId("TagId01");
        cunstomTagModel.setValue("TagValue01");
        cunstomTagModel.setCategoryId("TagCategoryId");
        cunstomTagModel.setType("Custom");
        TagModel cunstomTagModel02 = new TagModel();
        cunstomTagModel02.setId("TagId02");
        cunstomTagModel02.setValue("TagValue02");
        cunstomTagModel02.setCategoryId("TagCategoryId");
        cunstomTagModel02.setType("Custom");
        TagModel cunstomTagModel03 = new TagModel();
        cunstomTagModel03.setId("TagId03");
        cunstomTagModel03.setValue("TagValue03");
        cunstomTagModel03.setCategoryId("TagCategoryId");
        cunstomTagModel03.setType("Custom");

        cunsomTagList.add(cunstomTagModel);
        cunsomTagList.add(cunstomTagModel02);
        cunsomTagList.add(cunstomTagModel03);

        // 系统标签列表
        List<TagModel> workSampleTagList = new ArrayList<>();
        TagModel workSampleTagModel = new TagModel();
        workSampleTagModel.setId("TagId11");
        workSampleTagModel.setValue("TagValue04");
        workSampleTagModel.setCategoryId("TagCategoryId");
        workSampleTagModel.setType("workSample");
        TagModel workSampleTagModel01 = new TagModel();
        workSampleTagModel01.setId("TagId12");
        workSampleTagModel01.setValue("TagValue05");
        workSampleTagModel01.setCategoryId("TagCategoryId");
        workSampleTagModel01.setType("workSample");
        TagModel workSampleTagModel02 = new TagModel();
        workSampleTagModel02.setId("TagId13");
        workSampleTagModel02.setValue("TagValue06");
        workSampleTagModel02.setCategoryId("TagCategoryId");
        workSampleTagModel02.setType("workSample");

        workSampleTagList.add(workSampleTagModel);
        workSampleTagList.add(workSampleTagModel01);
        workSampleTagList.add(workSampleTagModel02);

        // 所有标签与测评点
        TagResponse tagResponse = new TagResponse();
        tagResponse.setCustom(cunsomTagList);
        tagResponse.setWorkSample(workSampleTagList);

        // 所有标签
        List<TagModel> allTagNoteList = new ArrayList<>();
        allTagNoteList.addAll(workSampleTagList);
        allTagNoteList.addAll(cunsomTagList);
        allTagNoteList.add(workSampleTagModelDeleted);

        JSONObject languageJson =new JSONObject();
        languageJson.put("key","value");

        // 调用方法
        when(groupProvider.checkGroup(groupId)).thenReturn(groupEntity);
        when(studentDao.getUsedFrameworksByGroup(groupId)).thenReturn(usedFrameworks);
        when(domainDao.getDomain(anyString())).thenReturn(domainEntityList.get(0));
        when(enrollmentProvider.getIEPMapByGroup(groupId)).thenReturn(iepMap);
        when(userService.hideIEPOpen("")).thenReturn(hideChildAndIEPOpen);
        when(noteDao.getNoteDomainWithDateByGroup(groupId)).thenReturn(noteDomainModels);
        when(agencyDao.getByGroupId(groupId)).thenReturn(agency);
        when(groupDao.getChilds(groupId)).thenReturn(childEntityList);
        when(tagDao.getNoteTagWithDateByGroup(groupId)).thenReturn(childNoteList);
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(tagService.getTagByUserId(userId, agency.getId())).thenReturn(tagResponse);
        when(tagDao.getAll()).thenReturn(allTagNoteList);
        doNothing().when(shardingProvider).resetShardingHint(anyString());
        // 当用户 checkUser 的时候，返回 管理员
        UserEntity userEntity = new UserEntity();
        // 设置用户角色
        userEntity.setRole(UserRole.AGENCY_ADMIN.toString());
        // 如果调用 userProvider.checkUser 方法，返回 userEntity
        when(userProvider.checkUser(anyString())).thenReturn(userEntity);
//        when(ratingService.getLanguageJson(frameworkId,language)).thenReturn(languageJson);
        // 调用测试方法
        ClassStatusResponse classStatusResponse = groupServiceImpl.getGroupStatus(groupId, language, alias, frameworkId);
        List<GroupStatus> groupStatuses = classStatusResponse.getGroupStatuses();
        // 断言验证
        assertEquals(3,groupStatuses.size());
        assertEquals(0,groupStatuses.stream().filter(element->element.getAbbreviation().equalsIgnoreCase("workSample")).count());
        assertEquals(3,groupStatuses.stream().filter(element->element.getAbbreviation().equalsIgnoreCase("Custom")).count());
        assertEquals(0,groupStatuses.stream().filter(element->element.getAbbreviation().equalsIgnoreCase("System")).count());
        assertEquals(0,groupStatuses.stream().filter(element->element.getMeasureName().equalsIgnoreCase("domainName01")).filter(element->element.getObservedChild().size()==1).count());
        assertEquals(0,groupStatuses.stream().filter(element->element.getMeasureName().equalsIgnoreCase("domainName01")).filter(element->element.getUnobservedChildNames().size()==2).count());
        assertEquals(0,groupStatuses.stream().filter(element->element.getMeasureName().equalsIgnoreCase("TagValue06")).filter(element->element.getObservedChild().size()==1).count());
        assertEquals(0,groupStatuses.stream().filter(element->element.getMeasureName().equalsIgnoreCase("TagDelete")).filter(element->element.getObservedChild().size()==1).count());
        assertEquals(0,groupStatuses.stream().filter(element->element.getMeasureName().equalsIgnoreCase("TagDelete")).filter(element->element.getUnobservedChildNames().size()==2).count());
    }
}

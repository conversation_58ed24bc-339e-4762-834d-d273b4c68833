package com.learninggenie.api.service.impl;

import com.learninggenie.common.data.dao.period.PeriodsGroupOperationRecordDao;
import com.learninggenie.common.data.model.PeriodsGroupEntity;
import com.learninggenie.common.data.model.PeriodsGroupPeriodGroupEntity;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class PeriodsGroupOperationRecordServiceImplTest {
    @InjectMocks
    private PeriodsGroupOperationRecordServiceImpl periodsGroupOperationRecordService;
    @Mock
    private PeriodsGroupOperationRecordDao periodsGroupOperationRecordDao;

    /**
     * 原映射关系与新映射关系都为null
     */
    @Test
    public void testCompareAndSaveRecordNull() {
        periodsGroupOperationRecordService.compareAndSaveRecord(null, null);
        verify(periodsGroupOperationRecordDao, times(0)).saveBatch(anyList());
    }

    /**
     * 原映射关系为null，新映射关系有值
     */
    @Test
    public void testCompareAndSaveRecordOriginNull() {
        List<PeriodsGroupPeriodGroupEntity> entityList = new ArrayList<>();
        PeriodsGroupPeriodGroupEntity entity = new PeriodsGroupPeriodGroupEntity();
        entity.setGroupId("groupId");
        entity.setPeriodGroupId("periodGroupId");
        entityList.add(entity);
        periodsGroupOperationRecordService.compareAndSaveRecord(null, entityList);
        verify(periodsGroupOperationRecordDao, times(1)).saveBatch(anyList());
    }

    /**
     * 原映射关系有值，新映射关系有null
     */
    @Test
    public void testCompareAndSaveRecordNewNull() {
        List<PeriodsGroupEntity> entityList = new ArrayList<>();
        PeriodsGroupEntity entity = new PeriodsGroupEntity();
        entity.setGroupId("groupId");
        entity.setId("Id");
        entityList.add(entity);
        periodsGroupOperationRecordService.compareAndSaveRecord(entityList, null);
        verify(periodsGroupOperationRecordDao, times(0)).saveBatch(anyList());
    }

    /**
     * 新关系与原关系有相同（无变化）的
     */
    @Test
    public void testCompareAndSaveRecord() {
        List<PeriodsGroupEntity> originList = new ArrayList<>();
        PeriodsGroupEntity periodsGroup = new PeriodsGroupEntity();
        periodsGroup.setGroupId("groupId");
        periodsGroup.setId("Id");
        originList.add(periodsGroup);
        List<PeriodsGroupPeriodGroupEntity> newList = new ArrayList<>();
        PeriodsGroupPeriodGroupEntity periodsGroupPeriodGroupEntity = new PeriodsGroupPeriodGroupEntity();
        periodsGroupPeriodGroupEntity.setGroupId("groupId");
        periodsGroupPeriodGroupEntity.setPeriodGroupId("periodGroupId");
        newList.add(periodsGroupPeriodGroupEntity);
        periodsGroupOperationRecordService.compareAndSaveRecord(originList, newList);
        verify(periodsGroupOperationRecordDao, times(1)).saveBatch(anyList());
    }
}

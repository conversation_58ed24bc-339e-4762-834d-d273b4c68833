package com.learninggenie.api.service.impl;

import com.learninggenie.api.model.ai.MessageHistoriesRequest;
import com.learninggenie.api.model.ai.MessageHistoriesResponse;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.security.AuthUserDetails;
import com.learninggenie.common.data.dao.prompts.PromptDao;
import com.learninggenie.common.data.entity.prompt.PromptEntity;
import com.learninggenie.common.data.dao.chat.ChatRecordDao;
import com.learninggenie.api.provider.OpenAIProvider;
import com.learninggenie.common.data.entity.prompt.PromptUsageRecordEntity;
import com.learninggenie.common.data.enums.prompt.CreateSource;
import com.learninggenie.common.data.enums.prompt.PromptScene;
import com.learninggenie.common.data.enums.prompt.UseType;
import com.learninggenie.common.data.model.chat.ChatbotRecord;
import com.learninggenie.common.utils.openai.ChatPromptUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;


@RunWith(MockitoJUnitRunner.class)
public class ChatbotServiceImplTest {
    @Mock
    private ChatRecordDao chatRecordDao;

    @Mock
    private PromptDao promptDao;

    @Mock
    private OpenAIProvider openAIProvider;

    @Mock
    private UserProvider userProvider;
    @InjectMocks
    private ChatbotServiceImpl chatbotService;

    /**
     * 测试根据历史聊天记录做上下文重新生成新问题
     */
    @Test
    public void testRedesignQuestion() {
        // 模拟用户权限对象
        AuthUserDetails authUser = new AuthUserDetails();
        // 期望的结果
        String expect = "what is 1";
        // 用户问题
        String userQuetion = "1";
        // 模拟DB查询的promptEntity
        PromptEntity promptEntity = new PromptEntity();
        // promptEntity设值
        promptEntity.setModule("gpt-4");
        promptEntity.setPromptTemplate("Given the following conversation and a follow up question, rephrase the follow up question to be a standalone question.\n" +
                "\n" +
                "Chat History:\n" +
                "{{ chat_history }}\n" +
                "Follow Up Input: {{ question }}\n" +
                "Standalone question:");
        promptEntity.setTemperature(0.0);
        // 模拟getActivePromptByScene()接口，指定返回值
        when(promptDao.getActivePromptByScene(PromptScene.REDESIGN_QUESTION.toString())).thenReturn(promptEntity);
        // 拼装prompt字符串内容
        String prompt = ChatPromptUtil.generateChatbotRedesignQuestionPrompt(promptEntity.getPromptTemplate(), userQuetion, "");
        // 模拟OpenAI返回的结果对象
        PromptUsageRecordEntity promptUsageRecordEntity = new PromptUsageRecordEntity();
        promptUsageRecordEntity.setCompletion("what is 1");
        // 模拟请求OpenAI
        when(openAIProvider.createChatCompletion(prompt,promptEntity, CreateSource.MANUAL, UseType.NORMAL, null))
                .thenReturn(promptUsageRecordEntity);
        // 获取方法实际返回的新问题
        String question = chatbotService.redesignQuestion(userQuetion,authUser);
        // 断言结果
        assertEquals(expect, question);
    }

    /**
     * 测试获取聊天历史信息
     */
    @Test
    public void testGetMessageHistories() {
        // 模拟请求对象
        MessageHistoriesRequest messageHistoriesRequest = new MessageHistoriesRequest();
        // 给请求对象设值
        messageHistoriesRequest.setBeginDate(new Date());
        messageHistoriesRequest.setRecordCount(10);
        // 模拟用户权限对象
        AuthUserDetails authUser = new AuthUserDetails();
        // 模拟检查用户权限接口
        when(userProvider.checkCurrentUser()).thenReturn(authUser);
        // 模拟请求历史的结果
        List<ChatbotRecord> records = new ArrayList<>();
        ChatbotRecord record = new ChatbotRecord();
        records.add(record);
        // 模拟请求历史接口
        when(chatRecordDao.getChatRecords(authUser.getId(), messageHistoriesRequest.getRecordCount(),messageHistoriesRequest.getBeginDate()))
                .thenReturn(records);
        // 模拟请求响应对象
        MessageHistoriesResponse expectResponse = new MessageHistoriesResponse();
        // 响应对象设值
        expectResponse.setHistoriesRecord(records);
        // 实际接口响应结果
        MessageHistoriesResponse response = chatbotService.getMessageHistories(messageHistoriesRequest);
        // 断言结果
        assertEquals(expectResponse,response);
    }
}

package com.learninggenie.api.service.lesson2.impl;

import com.learninggenie.api.model.lesson2.LessonDetailResponse;
import com.learninggenie.api.model.lesson2.LessonVersionRequest;
import com.learninggenie.api.model.lesson2.MediaModel;
import com.learninggenie.api.model.lesson2.LessonThemeModel;
import com.learninggenie.api.model.lesson2.LessonStepModel;
import com.learninggenie.api.model.lesson2.LessonStepGuideModel;
import com.learninggenie.api.model.lesson2.LessonVersionDescription;
import com.learninggenie.api.model.lesson2.bo.LessonVersionBO;
import com.learninggenie.api.model.dll.DLLSubjectModel;
import com.learninggenie.api.model.curriculum.CreateLessonRequest;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.service.lesson2.LessonService;
import com.learninggenie.common.data.dao.lesson2.LessonDao;
import com.learninggenie.common.data.dao.lesson2.LessonVersionDao;
import com.learninggenie.common.data.entity.UserEntity;
import com.learninggenie.common.data.entity.lesson2.LessonEntity;
import com.learninggenie.common.data.entity.lesson2.LessonVersionEntity;
import com.learninggenie.common.data.entity.frameworks.MeasureEntity;
import com.learninggenie.common.data.enums.ProjectType;
import com.learninggenie.common.utils.JsonUtil;
import com.learninggenie.common.utils.TimeUtil;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.AfterClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class LessonVersionServiceImplTest {

    @InjectMocks
    private LessonVersionServiceImpl lessonVersionService;

    @Mock
    private LessonService lessonService;

    @Mock
    private LessonVersionDao lessonVersionDao;

    @Mock
    private UserProvider userProvider;

    @Mock
    private LessonDao lessonDao;

    private static MockedStatic<JsonUtil> jsonUtilMockedStatic;
    private static MockedStatic<TimeUtil> timeUtilMockedStatic;

    @BeforeClass
    public static void beforeClass() {
        jsonUtilMockedStatic = Mockito.mockStatic(JsonUtil.class);
        timeUtilMockedStatic = Mockito.mockStatic(TimeUtil.class);
    }

    @AfterClass
    public static void afterClass() {
        if (jsonUtilMockedStatic != null) {
            jsonUtilMockedStatic.close();
        }
        if (timeUtilMockedStatic != null) {
            timeUtilMockedStatic.close();
        }
    }

    @Before
    public void setUp() {
        // 重置静态 Mock 的行为
        if (jsonUtilMockedStatic != null) {
            jsonUtilMockedStatic.reset();
        }
        if (timeUtilMockedStatic != null) {
            timeUtilMockedStatic.reset();
        }
    }

    /**
     * 测试创建课程快照 - 正常情况
     */
    @Test
    public void testCreateLessonVersion_Normal() {
        // 准备数据
        String lessonId = "lesson-001";
        String userId = "user-001";
        String sourceVersionId = "source-version-001";
        String description = "测试更新";
        Date utcNow = new Date();
        
        LessonEntity lesson = new LessonEntity();
        lesson.setId(lessonId);
        
        LessonVersionBO lessonVersionBO = new LessonVersionBO();
        lessonVersionBO.setLesson(lesson);
        lessonVersionBO.setSourceVersionId(sourceVersionId);
        lessonVersionBO.setDescription(description);
        lessonVersionBO.setFirstPublish(false);
        lessonVersionBO.setAdapt(false);
        lessonVersionBO.setEnhanceLesson(false);
        
        LessonDetailResponse lessonDetail = createMockLessonDetail(lessonId);
        
        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setFirstName("测试");
        user.setLastName("用户");
        user.setRole("TEACHER");
        
        LessonVersionEntity sourceVersion = new LessonVersionEntity();
        sourceVersion.setId(sourceVersionId);
        sourceVersion.setVersionTime(new Date());
        
        // Mock 设置
        when(lessonService.getLessonDetail(lessonId)).thenReturn(lessonDetail);
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.getUser(userId)).thenReturn(user);
        when(userProvider.getCurrentProject()).thenReturn(ProjectType.CURRICULUM_PLUGIN.toString());
        when(lessonVersionDao.existsByLessonId(lessonId)).thenReturn(true);
        when(lessonVersionDao.getById(sourceVersionId)).thenReturn(sourceVersion);
        doNothing().when(lessonVersionDao).create(any(LessonVersionEntity.class));
        
        // 静态方法 Mock
        timeUtilMockedStatic.when(TimeUtil::getUtcNow).thenReturn(utcNow);
        jsonUtilMockedStatic.when(() -> JsonUtil.toJsonWithAmPmDateFormat(any())).thenReturn("{\"snapshot\":\"data\"}");
        jsonUtilMockedStatic.when(() -> JsonUtil.toJson(any())).thenReturn("{\"description\":\"data\"}");
        
        // 调用测试方法
        LessonVersionEntity result = lessonVersionService.createLessonVersion(lessonVersionBO);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(lessonId, result.getLessonId());
        assertEquals(userId, result.getUpdateUserId());
        assertEquals(sourceVersionId, result.getSourceVersionId());
        assertEquals(utcNow, result.getCreateAtUtc());
        assertEquals(utcNow, result.getUpdateAtUtc());
        
        // 验证方法调用
        verify(lessonService).getLessonDetail(lessonId);
        verify(userProvider, times(2)).getCurrentUserId(); // 调用了2次：获取用户信息和设置更新用户ID
        verify(userProvider).getUser(userId);
        verify(lessonVersionDao).existsByLessonId(lessonId);
        verify(lessonVersionDao).getById(sourceVersionId);
        verify(lessonVersionDao).create(any(LessonVersionEntity.class));
    }

    /**
     * 测试创建课程快照 - 参数为空
     */
    @Test
    public void testCreateLessonVersion_NullParams() {
        // 测试 lessonVersionBO 为空
        LessonVersionEntity result = lessonVersionService.createLessonVersion(null);
        assertNotNull(result);
        assertNull(result.getId());
        
        // 测试 lesson 为空
        LessonVersionBO lessonVersionBO = new LessonVersionBO();
        lessonVersionBO.setLesson(null);
        result = lessonVersionService.createLessonVersion(lessonVersionBO);
        assertNotNull(result);
        assertNull(result.getId());
    }

    /**
     * 测试创建课程快照 - 首次发布
     */
    @Test
    public void testCreateLessonVersion_FirstPublish() {
        // 准备数据
        String lessonId = "lesson-001";
        String userId = "user-001";
        Date utcNow = new Date();
        
        LessonEntity lesson = new LessonEntity();
        lesson.setId(lessonId);
        
        LessonVersionBO lessonVersionBO = new LessonVersionBO();
        lessonVersionBO.setLesson(lesson);
        lessonVersionBO.setFirstPublish(true);
        
        LessonDetailResponse lessonDetail = createMockLessonDetail(lessonId);
        
        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setFirstName("测试");
        user.setLastName("用户");
        user.setRole("TEACHER");
        
        // Mock 设置
        when(lessonService.getLessonDetail(lessonId)).thenReturn(lessonDetail);
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.getUser(userId)).thenReturn(user);
        when(userProvider.getCurrentProject()).thenReturn(ProjectType.CURRICULUM_PLUGIN.toString());
        when(lessonVersionDao.existsByLessonId(lessonId)).thenReturn(false);
        doNothing().when(lessonVersionDao).create(any(LessonVersionEntity.class));
        
        // 静态方法 Mock
        timeUtilMockedStatic.when(TimeUtil::getUtcNow).thenReturn(utcNow);
        jsonUtilMockedStatic.when(() -> JsonUtil.toJsonWithAmPmDateFormat(any())).thenReturn("{\"snapshot\":\"data\"}");
        jsonUtilMockedStatic.when(() -> JsonUtil.toJson(any())).thenReturn("{\"description\":\"data\"}");
        
        // 调用测试方法
        LessonVersionEntity result = lessonVersionService.createLessonVersion(lessonVersionBO);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(lessonId, result.getLessonId());
        assertEquals(userId, result.getUpdateUserId());
        
        // 验证方法调用
        verify(lessonService).getLessonDetail(lessonId);
        verify(lessonVersionDao).existsByLessonId(lessonId);
        verify(lessonVersionDao).create(any(LessonVersionEntity.class));
    }

    /**
     * 测试创建课程快照 - 改编课程
     */
    @Test
    public void testCreateLessonVersion_Adapt() {
        // 准备数据
        String lessonId = "lesson-001";
        String userId = "user-001";
        Date utcNow = new Date();
        
        LessonEntity lesson = new LessonEntity();
        lesson.setId(lessonId);
        
        LessonVersionBO lessonVersionBO = new LessonVersionBO();
        lessonVersionBO.setLesson(lesson);
        lessonVersionBO.setAdapt(true);
        
        LessonDetailResponse lessonDetail = createMockLessonDetail(lessonId);
        
        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setFirstName("测试");
        user.setLastName("用户");
        user.setRole("TEACHER");
        
        // Mock 设置
        when(lessonService.getLessonDetail(lessonId)).thenReturn(lessonDetail);
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.getUser(userId)).thenReturn(user);
        when(userProvider.getCurrentProject()).thenReturn(ProjectType.CURRICULUM_PLUGIN.toString());
        when(lessonVersionDao.existsByLessonId(lessonId)).thenReturn(true);
        doNothing().when(lessonVersionDao).create(any(LessonVersionEntity.class));
        
        // 静态方法 Mock
        timeUtilMockedStatic.when(TimeUtil::getUtcNow).thenReturn(utcNow);
        jsonUtilMockedStatic.when(() -> JsonUtil.toJsonWithAmPmDateFormat(any())).thenReturn("{\"snapshot\":\"data\"}");
        jsonUtilMockedStatic.when(() -> JsonUtil.toJson(any())).thenReturn("{\"description\":\"data\"}");
        
        // 调用测试方法
        LessonVersionEntity result = lessonVersionService.createLessonVersion(lessonVersionBO);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(lessonId, result.getLessonId());
        assertEquals(userId, result.getUpdateUserId());
        
        // 验证方法调用
        verify(lessonService).getLessonDetail(lessonId);
        verify(lessonVersionDao).existsByLessonId(lessonId);
        verify(lessonVersionDao).create(any(LessonVersionEntity.class));
    }

    /**
     * 测试创建课程快照 - AI 增强
     */
    @Test
    public void testCreateLessonVersion_EnhanceLesson() {
        // 准备数据
        String lessonId = "lesson-001";
        String userId = "user-001";
        Date utcNow = new Date();
        
        LessonEntity lesson = new LessonEntity();
        lesson.setId(lessonId);
        
        LessonVersionBO lessonVersionBO = new LessonVersionBO();
        lessonVersionBO.setLesson(lesson);
        lessonVersionBO.setEnhanceLesson(true);
        
        LessonDetailResponse lessonDetail = createMockLessonDetail(lessonId);
        
        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setFirstName("测试");
        user.setLastName("用户");
        user.setRole("TEACHER");
        
        // Mock 设置
        when(lessonService.getLessonDetail(lessonId)).thenReturn(lessonDetail);
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.getUser(userId)).thenReturn(user);
        when(userProvider.getCurrentProject()).thenReturn(ProjectType.CURRICULUM_PLUGIN.toString());
        when(lessonVersionDao.existsByLessonId(lessonId)).thenReturn(true);
        doNothing().when(lessonVersionDao).create(any(LessonVersionEntity.class));
        
        // 静态方法 Mock
        timeUtilMockedStatic.when(TimeUtil::getUtcNow).thenReturn(utcNow);
        jsonUtilMockedStatic.when(() -> JsonUtil.toJsonWithAmPmDateFormat(any())).thenReturn("{\"snapshot\":\"data\"}");
        jsonUtilMockedStatic.when(() -> JsonUtil.toJson(any())).thenReturn("{\"description\":\"data\"}");
        
        // 调用测试方法
        LessonVersionEntity result = lessonVersionService.createLessonVersion(lessonVersionBO);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(lessonId, result.getLessonId());
        assertEquals(userId, result.getUpdateUserId());
        
        // 验证方法调用
        verify(lessonService).getLessonDetail(lessonId);
        verify(lessonVersionDao).existsByLessonId(lessonId);
        verify(lessonVersionDao).create(any(LessonVersionEntity.class));
    }

    /**
     * 测试改编课程增加历史版本
     */
    @Test
    public void testAdaptLessonAddVersion() {
        // 准备数据
        String lessonId = "lesson-001";
        String userId = "user-001";
        Date utcNow = new Date();
        
        LessonDetailResponse lessonDetail = createMockLessonDetail(lessonId);
        
        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setFirstName("测试");
        user.setLastName("用户");
        user.setRole("TEACHER");
        
        // Mock 设置
        when(lessonService.getLessonDetail(lessonId)).thenReturn(lessonDetail);
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.getUser(userId)).thenReturn(user);
        when(userProvider.getCurrentProject()).thenReturn(ProjectType.CURRICULUM_PLUGIN.toString());
        when(lessonVersionDao.existsByLessonId(lessonId)).thenReturn(true);
        doNothing().when(lessonVersionDao).create(any(LessonVersionEntity.class));
        
        // 静态方法 Mock
        timeUtilMockedStatic.when(TimeUtil::getUtcNow).thenReturn(utcNow);
        jsonUtilMockedStatic.when(() -> JsonUtil.toJsonWithAmPmDateFormat(any())).thenReturn("{\"snapshot\":\"data\"}");
        jsonUtilMockedStatic.when(() -> JsonUtil.toJson(any())).thenReturn("{\"description\":\"data\"}");
        
        // 调用测试方法
        LessonVersionEntity result = lessonVersionService.adaptLessonAddVersion(lessonId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(lessonId, result.getLessonId());
        assertEquals(userId, result.getUpdateUserId());
        
        // 验证方法调用
        verify(lessonService).getLessonDetail(lessonId);
        verify(lessonVersionDao).existsByLessonId(lessonId);
        verify(lessonVersionDao).create(any(LessonVersionEntity.class));
    }

    /**
     * 测试回滚改编课程历史版本
     */
    @Test
    public void testRestoreAdaptLessonVersion() {
        // 准备数据
        String lessonId = "lesson-001";
        String versionId = "version-001";
        
        LessonVersionEntity versionEntity = new LessonVersionEntity();
        versionEntity.setId(versionId);
        versionEntity.setLessonId(lessonId);
        versionEntity.setSnapshot("{\"id\":\"lesson-001\",\"name\":\"测试课程\"}");
        
        LessonDetailResponse lessonDetail = createMockLessonDetail(lessonId);
        
        // Mock 设置
        when(lessonVersionDao.getLastByLessonId(lessonId)).thenReturn(versionEntity);
        when(lessonService.updateLesson(any(CreateLessonRequest.class))).thenReturn(null);
        
        // 静态方法 Mock
        jsonUtilMockedStatic.when(() -> JsonUtil.fromJsonWithAmPmDateFormat(anyString(), eq(LessonDetailResponse.class)))
                .thenReturn(lessonDetail);
        
        // 调用测试方法
        lessonVersionService.restoreAdaptLessonVersion(lessonId, versionId);
        
        // 验证方法调用
        verify(lessonVersionDao).removeById(versionId);
        verify(lessonVersionDao).getLastByLessonId(lessonId);
        verify(lessonService).updateLesson(any(CreateLessonRequest.class));
    }

    /**
     * 测试回滚课程历史版本
     */
    @Test
    public void testRestoreLessonVersion() {
        // 准备数据
        String lessonId = "lesson-001";
        String versionId = "version-001";
        
        LessonVersionEntity versionEntity = new LessonVersionEntity();
        versionEntity.setId(versionId);
        versionEntity.setLessonId(lessonId);
        versionEntity.setSnapshot("{\"id\":\"lesson-001\",\"name\":\"测试课程\"}");
        
        LessonDetailResponse lessonDetail = createMockLessonDetail(lessonId);
        
        // Mock 设置
        when(lessonVersionDao.getById(versionId)).thenReturn(versionEntity);
        when(lessonService.updateLesson(any(CreateLessonRequest.class))).thenReturn(null);
        
        // 静态方法 Mock
        jsonUtilMockedStatic.when(() -> JsonUtil.fromJsonWithAmPmDateFormat(anyString(), eq(LessonDetailResponse.class)))
                .thenReturn(lessonDetail);
        
        // 调用测试方法
        lessonVersionService.restoreLessonVersion(lessonId, versionId);
        
        // 验证方法调用
        verify(lessonVersionDao).getById(versionId);
        verify(lessonService).updateLesson(any(CreateLessonRequest.class));
    }

    /**
     * 测试回滚课程历史版本 - 参数为空
     */
    @Test
    public void testRestoreLessonVersion_EmptyParams() {
        // 调用测试方法
        lessonVersionService.restoreLessonVersion("", "");
        
        // 验证没有调用任何方法
        verify(lessonVersionDao, never()).getById(anyString());
        verify(lessonVersionDao, never()).getLastByLessonId(anyString());
        verify(lessonService, never()).updateLesson(any(CreateLessonRequest.class));
    }

    /**
     * 测试回滚课程历史版本 - 没有历史版本
     */
    @Test
    public void testRestoreLessonVersion_NoVersion() {
        // 准备数据
        String lessonId = "lesson-001";
        String versionId = "version-001";
        
        // Mock 设置
        when(lessonVersionDao.getById(versionId)).thenReturn(null);
        
        // 调用测试方法
        lessonVersionService.restoreLessonVersion(lessonId, versionId);
        
        // 验证方法调用
        verify(lessonVersionDao).getById(versionId);
        verify(lessonService, never()).updateLesson(any(CreateLessonRequest.class));
    }

    /**
     * 测试批量回滚课程版本
     */
    @Test
    public void testBatchRestoreLessonVersion() {
        // 准备数据
        List<String> lessonIds = Arrays.asList("lesson-001", "lesson-002");
        
        LessonVersionEntity versionEntity1 = new LessonVersionEntity();
        versionEntity1.setId("version-001");
        versionEntity1.setLessonId("lesson-001");
        versionEntity1.setSnapshot("{\"id\":\"lesson-001\",\"name\":\"测试课程1\"}");
        
        LessonVersionEntity versionEntity2 = new LessonVersionEntity();
        versionEntity2.setId("version-002");
        versionEntity2.setLessonId("lesson-002");
        versionEntity2.setSnapshot("{\"id\":\"lesson-002\",\"name\":\"测试课程2\"}");
        
        LessonDetailResponse lessonDetail1 = createMockLessonDetail("lesson-001");
        LessonDetailResponse lessonDetail2 = createMockLessonDetail("lesson-002");
        
        // Mock 设置
        when(lessonVersionDao.getLastByLessonId("lesson-001")).thenReturn(versionEntity1);
        when(lessonVersionDao.getLastByLessonId("lesson-002")).thenReturn(versionEntity2);
        when(lessonService.updateLesson(any(CreateLessonRequest.class))).thenReturn(null);
        
        // 静态方法 Mock
        jsonUtilMockedStatic.when(() -> JsonUtil.fromJsonWithAmPmDateFormat(eq(versionEntity1.getSnapshot()), eq(LessonDetailResponse.class)))
                .thenReturn(lessonDetail1);
        jsonUtilMockedStatic.when(() -> JsonUtil.fromJsonWithAmPmDateFormat(eq(versionEntity2.getSnapshot()), eq(LessonDetailResponse.class)))
                .thenReturn(lessonDetail2);
        
        // 调用测试方法
        lessonVersionService.batchRestoreLessonVersion(lessonIds);
        
        // 验证方法调用
        verify(lessonVersionDao).getLastByLessonId("lesson-001");
        verify(lessonVersionDao).getLastByLessonId("lesson-002");
        verify(lessonService, times(2)).updateLesson(any(CreateLessonRequest.class));
    }

    /**
     * 测试批量回滚课程版本 - 空列表
     */
    @Test
    public void testBatchRestoreLessonVersion_EmptyList() {
        // 调用测试方法
        lessonVersionService.batchRestoreLessonVersion(new ArrayList<>());
        
        // 验证没有调用任何方法
        verify(lessonVersionDao, never()).getLastByLessonId(anyString());
        verify(lessonService, never()).updateLesson(any(CreateLessonRequest.class));
    }

    /**
     * 测试批量回滚改编课程版本
     */
    @Test
    public void testBatchRestoreAdaptLessonVersion() {
        // 准备数据
        List<String> lessonIds = Arrays.asList("lesson-001", "lesson-002");
        Map<String, String> overwriteLessonVersion = new HashMap<>();
        overwriteLessonVersion.put("lesson-001", "version-001");
        overwriteLessonVersion.put("lesson-002", "version-002");
        
        Date createTime1 = new Date(System.currentTimeMillis() - 1000);
        Date createTime2 = new Date(System.currentTimeMillis() - 2000);
        Date createTime3 = new Date();
        
        LessonVersionEntity originalVersion1 = new LessonVersionEntity();
        originalVersion1.setId("version-001");
        originalVersion1.setLessonId("lesson-001");
        originalVersion1.setCreateAtUtc(createTime1);
        originalVersion1.setSnapshot("{\"id\":\"lesson-001\",\"name\":\"测试课程1\"}");
        
        LessonVersionEntity laterVersion1 = new LessonVersionEntity();
        laterVersion1.setId("version-later-001");
        laterVersion1.setLessonId("lesson-001");
        laterVersion1.setCreateAtUtc(createTime3);
        
        List<LessonVersionEntity> allVersions1 = Arrays.asList(originalVersion1, laterVersion1);
        
        LessonVersionEntity originalVersion2 = new LessonVersionEntity();
        originalVersion2.setId("version-002");
        originalVersion2.setLessonId("lesson-002");
        originalVersion2.setCreateAtUtc(createTime2);
        originalVersion2.setSnapshot("{\"id\":\"lesson-002\",\"name\":\"测试课程2\"}");
        
        List<LessonVersionEntity> allVersions2 = Arrays.asList(originalVersion2);
        
        LessonDetailResponse lessonDetail1 = createMockLessonDetail("lesson-001");
        LessonDetailResponse lessonDetail2 = createMockLessonDetail("lesson-002");
        
        // Mock 设置
        when(lessonVersionDao.getByLessonId("lesson-001")).thenReturn(allVersions1);
        when(lessonVersionDao.getByLessonId("lesson-002")).thenReturn(allVersions2);
        when(lessonVersionDao.getById("version-001")).thenReturn(originalVersion1);
        when(lessonVersionDao.getById("version-002")).thenReturn(originalVersion2);
        when(lessonService.updateLesson(any(CreateLessonRequest.class))).thenReturn(null);
        
        // 静态方法 Mock
        jsonUtilMockedStatic.when(() -> JsonUtil.fromJsonWithAmPmDateFormat(eq(originalVersion1.getSnapshot()), eq(LessonDetailResponse.class)))
                .thenReturn(lessonDetail1);
        jsonUtilMockedStatic.when(() -> JsonUtil.fromJsonWithAmPmDateFormat(eq(originalVersion2.getSnapshot()), eq(LessonDetailResponse.class)))
                .thenReturn(lessonDetail2);
        
        // 调用测试方法
        lessonVersionService.batchRestoreAdaptLessonVersion(lessonIds, overwriteLessonVersion);
        
        // 验证方法调用
        verify(lessonVersionDao).getByLessonId("lesson-001");
        verify(lessonVersionDao).getByLessonId("lesson-002");
        verify(lessonVersionDao).removeByIds(Arrays.asList("version-later-001"));
        verify(lessonVersionDao).getById("version-001");
        verify(lessonVersionDao).getById("version-002");
        verify(lessonService, times(2)).updateLesson(any(CreateLessonRequest.class));
    }

    /**
     * 测试批量回滚改编课程版本 - 空参数
     */
    @Test
    public void testBatchRestoreAdaptLessonVersion_EmptyParams() {
        // 调用测试方法
        lessonVersionService.batchRestoreAdaptLessonVersion(new ArrayList<>(), new HashMap<>());
        
        // 验证没有调用任何方法
        verify(lessonVersionDao, never()).getByLessonId(anyString());
        verify(lessonService, never()).updateLesson(any(CreateLessonRequest.class));
    }

    /**
     * 测试获取课程版本信息
     */
    @Test
    public void testGetLessonsVersionInfo() {
        // 准备数据
        LessonVersionRequest request = new LessonVersionRequest();
        request.setLessonIds(Arrays.asList("lesson-001", "lesson-002"));
        
        LessonVersionEntity version1 = new LessonVersionEntity();
        version1.setId("version-001");
        version1.setLessonId("lesson-001");
        
        LessonVersionEntity version2 = new LessonVersionEntity();
        version2.setId("version-002");
        version2.setLessonId("lesson-002");
        
        List<LessonVersionEntity> expectedVersions = Arrays.asList(version1, version2);
        
        // Mock 设置
        when(lessonVersionDao.getLastByLessonIds(request.getLessonIds())).thenReturn(expectedVersions);
        
        // 调用测试方法
        List<LessonVersionEntity> result = lessonVersionService.getLessonsVersionInfo(request);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("version-001", result.get(0).getId());
        assertEquals("version-002", result.get(1).getId());
        
        // 验证方法调用
        verify(lessonVersionDao).getLastByLessonIds(request.getLessonIds());
    }

    /**
     * 测试获取课程版本信息 - 空请求
     */
    @Test
    public void testGetLessonsVersionInfo_EmptyRequest() {
        // 调用测试方法
        List<LessonVersionEntity> result = lessonVersionService.getLessonsVersionInfo(null);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证没有调用任何方法
        verify(lessonVersionDao, never()).getLastByLessonIds(anyList());
    }

    /**
     * 测试获取课程版本信息 - 空课程ID列表
     */
    @Test
    public void testGetLessonsVersionInfo_EmptyLessonIds() {
        // 准备数据
        LessonVersionRequest request = new LessonVersionRequest();
        request.setLessonIds(new ArrayList<>());
        
        // 调用测试方法
        List<LessonVersionEntity> result = lessonVersionService.getLessonsVersionInfo(request);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证没有调用任何方法
        verify(lessonVersionDao, never()).getLastByLessonIds(anyList());
    }

    /**
     * 创建模拟的课程详情数据
     */
    private LessonDetailResponse createMockLessonDetail(String lessonId) {
        LessonDetailResponse lessonDetail = new LessonDetailResponse();
        lessonDetail.setId(lessonId);
        lessonDetail.setName("测试课程");
        lessonDetail.setPrepareTime(15L);
        lessonDetail.setActivityTime(30L);
        lessonDetail.setUpdateTime(new Date());
        lessonDetail.setGenerateStatus("CONFIRMED");
        
        // 设置封面媒体
        MediaModel coverMedia = new MediaModel();
        coverMedia.setId("media-001");
        lessonDetail.setCoverMedias(Arrays.asList(coverMedia));
        
        // 设置测评点
        MeasureEntity measure = new MeasureEntity();
        measure.setAbbreviation("ATL-REG5");
        lessonDetail.setMeasures(Arrays.asList(measure));
        
        // 设置课程目标
        lessonDetail.setObjectives(Arrays.asList("目标1", "目标2"));
        
        // 设置DLL信息
        DLLSubjectModel dll = new DLLSubjectModel();
        dll.setContent("Movement");
        dll.setDescription("运动相关");
        lessonDetail.setDlls(Arrays.asList(dll));
        
        // 设置主题
        LessonThemeModel theme = new LessonThemeModel();
        theme.setId("theme-001");
        lessonDetail.setThemes(Arrays.asList(theme));
        
        // 设置步骤
        LessonStepModel step = new LessonStepModel();
        step.setAgeGroupName("PS/PK (3-4)");
        step.setContent("步骤内容");
        step.setUniversalDesignForLearning("差异化教学内容");
        step.setCulturallyResponsiveInstruction("文化响应式教学内容");
        step.setHomeActivity("家庭活动");
        
        LessonStepGuideModel guide = new LessonStepGuideModel();
        guide.setMeasureAbbreviation("ATL-REG5");
        step.setLessonStepGuides(Arrays.asList(guide));
        
        lessonDetail.setSteps(Arrays.asList(step));
        
        // 设置年龄段
        lessonDetail.setAges(Arrays.asList("3", "4"));
        
        return lessonDetail;
    }
}
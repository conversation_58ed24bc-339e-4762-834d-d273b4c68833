package com.learninggenie.api.service.impl;

import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.model.message.SendMessageRequest;
import com.learninggenie.api.model.message.SingleMessageStatisticsResponse;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.security.AuthUserDetails;
import com.learninggenie.common.data.dao.*;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.dao.messages.MessagesSendTaskDao;
import com.learninggenie.common.data.entity.*;
import com.learninggenie.common.data.enums.AgencyMetaKey;
import com.learninggenie.common.data.model.CenterModel;
import com.learninggenie.common.data.model.UserModel;
import com.learninggenie.common.data.model.group.TeacherCenterGroupModel;
import com.learninggenie.common.data.model.message.UserMessageModel;
import com.learninggenie.common.utils.StringUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Created by hxl on 2023/06/07.
 * MessageServiceImpl 的测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class MessageServiceImplTest {

    @InjectMocks
    private MessageServiceImpl messageService;

    @Mock
    private MessageDao messageDao;

    @Mock
    private UserDaoImpl userDao;

    @Mock
    private CenterDao centerDao;

    @Mock
    private GroupDao groupDao;

    @Mock
    private StudentDao studentDao;

    @Mock
    private UserProvider userProvider;

    @Mock
    private MessagesSendTaskDao messagesSendTaskDao;

    @Mock
    private AgencyDao agencyDao;

    /**
     * Create by hxl 2023/06/09.
     * ExtractRecipients 方法。
     */
    @Test
    public void testExtractRecipients() {
        // Create a mock object to pass to the method
        final Object mockObj = mock(Object.class);
        when(mockObj.toString()).thenReturn("[recipient1, RECIPIENT2, , recipient3]");

        // Call the method and check the result
        final List<String> expected = Arrays.asList("RECIPIENT1", "RECIPIENT2", "RECIPIENT3");
        final List<String> actual = messageService.extractRecipients(mockObj);
        assertEquals(expected, actual);
    }

    /**
     * 测试获取消息打开统计
     */
    @Test
    public void testGetMessageReadStatistics() {
        // 准备数据
        final String messageId = "message01";
        final String siteAdminId = "siteAdmin01";
        final String parentId = "siteAdmin02";
        final String teacherId = "teacher01";
        final String siteAdminEmail = "email01";
        final String parentEmail = "email02";
        final String teacherEmail = "email03";
        final String enrollmentId = "enrollment01";
        final String centerId = "center01";
        final String centerName = "centerName01";
        final String groupId = "groupId01";
        final String groupName = "groupName01";
        // 用户邮箱
        List<String> userEmails = new ArrayList<>();
        userEmails.add(siteAdminEmail);
        userEmails.add(parentEmail);
        userEmails.add(teacherEmail);
        // 用户 ID
        List<String> userIds = new ArrayList<>();
        userIds.add(siteAdminId);
        userIds.add(parentId);
        userIds.add(teacherId);
        // 学生 ID
        List<String> enrollmentIds = new ArrayList<>();
        enrollmentIds.add(enrollmentId);
        // 消息列表
        List<UserMessageModel> userMessageModels = new ArrayList<>();
        // 消息 Model1
        UserMessageModel userMessageModel = new UserMessageModel();
        userMessageModel.setReceiver(siteAdminEmail);
        userMessageModel.setRead(false);
        userMessageModel.setMessageId(messageId);
        userMessageModel.setEnrollmentId(enrollmentId);
        userMessageModel.setGroupId(groupId);
        userMessageModel.setCenterId(centerId);
        userMessageModel.setUserId(siteAdminId);
        userMessageModel.setReceiveType("APP");
        userMessageModels.add(userMessageModel);
        // 消息 Model2
        UserMessageModel userMessageModel2 = new UserMessageModel();
        userMessageModel2.setReceiver(parentEmail);
        userMessageModel2.setRead(true);
        userMessageModel2.setMessageId(messageId);
        userMessageModel2.setEnrollmentId(enrollmentId);
        userMessageModel2.setGroupId(groupId);
        userMessageModel2.setCenterId(centerId);
        userMessageModel2.setUserId(parentId);
        userMessageModel2.setReceiveType("APP");
        userMessageModels.add(userMessageModel2);
        // 消息 Model3
        UserMessageModel userMessageModel3 = new UserMessageModel();
        userMessageModel3.setReceiver(teacherEmail);
        userMessageModel3.setRead(false);
        userMessageModel3.setMessageId(messageId);
        userMessageModel3.setEnrollmentId(enrollmentId);
        userMessageModel3.setGroupId(groupId);
        userMessageModel3.setCenterId(centerId);
        userMessageModel3.setUserId(teacherId);
        userMessageModel3.setReceiveType("APP");
        userMessageModels.add(userMessageModel3);
        // 用户 Model1
        List<UserModel> userModels = new ArrayList<>();
        UserModel userModel = new UserModel();
        userModel.setId(siteAdminId);
        userModel.setEmail(siteAdminEmail);
        userModel.setRole("SITE_ADMIN");
        userModels.add(userModel);
        // 用户 Model2
        UserModel userModel2 = new UserModel();
        userModel2.setId(parentId);
        userModel2.setEmail(parentEmail);
        userModel2.setRole("PARENT");
        userModels.add(userModel2);
        // 用户 Model3
        UserModel userModel3 = new UserModel();
        userModel3.setId(teacherId);
        userModel3.setEmail(teacherEmail);
        userModel3.setDisplayName("teacherName");
        userModel3.setRole("COLLABORATOR");
        userModels.add(userModel3);
        // 学校 Model
        List<CenterModel> centerModels = new ArrayList<>();
        CenterModel centerModel = new CenterModel();
        centerModel.setId(centerId);
        centerModel.setUserId(siteAdminId);
        centerModel.setName(centerName);
        centerModels.add(centerModel);
        // 学校实体
        List<CenterEntity> centerEntities = new ArrayList<>();
        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setId(centerId);
        centerEntity.setName(centerName);
        centerEntities.add(centerEntity);
        // 班级实体
        List<GroupEntity> groupEntities = new ArrayList<>();
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId(groupId);
        groupEntity.setName(groupName);
        groupEntities.add(groupEntity);
        // 老师班级关系实体
        List<TeacherCenterGroupModel> teacherCenterGroupModels = new ArrayList<>();
        TeacherCenterGroupModel teacherCenterGroupModel = new TeacherCenterGroupModel();
        teacherCenterGroupModel.setCenterId(centerId);
        teacherCenterGroupModel.setCenterName(centerName);
        teacherCenterGroupModel.setGroupId(groupId);
        teacherCenterGroupModel.setGroupName(groupName);
        teacherCenterGroupModel.setTeacherId(teacherId);
        teacherCenterGroupModels.add(teacherCenterGroupModel);
        // 学生实体
        List<EnrollmentEntity> enrollmentEntities = new ArrayList<>();
        EnrollmentEntity enrollmentEntity = new EnrollmentEntity();
        enrollmentEntity.setId(enrollmentId);
        enrollmentEntity.setDisplayName("displayName01");
        enrollmentEntities.add(enrollmentEntity);

        // 模拟数据
        when(messageDao.getUserMessageByMessageIdAndFilter(anyList(), anyList())).thenReturn(userMessageModels);
        when(userDao.getUsersByEmails(StringUtil.convertIdsToString(userEmails))).thenReturn(userModels);
        when(centerDao.getCentersBySiteAdminIds(Collections.singletonList(siteAdminId))).thenReturn(centerModels);
        lenient().when(messageDao.getMessageStatsInfoByReceiverUserIdsAndFilter(messageId, userIds, null)).thenReturn(userMessageModels);
        lenient().when(centerDao.getAllCentersByCenterIds(Collections.singletonList(centerId))).thenReturn(centerEntities);
        lenient().when(groupDao.getGroupsByIds(Collections.singletonList(groupId))).thenReturn(groupEntities);
        lenient().when(studentDao.getAllChildrenById(StringUtil.convertIdsToString(enrollmentIds))).thenReturn(enrollmentEntities);
        when(groupDao.getGroupsByTeachers(Collections.singletonList(teacherId))).thenReturn(teacherCenterGroupModels);
        when(userProvider.getCurrentLang()).thenReturn("en-us");
        AuthUserDetails currentUser = new AuthUserDetails();
        when(userProvider.getCurrentUser()).thenReturn(currentUser);

        // 调用
        SingleMessageStatisticsResponse response = messageService.getMessageReadStatistics(messageId);

        // 验证
        assertEquals(1, response.getReadMsgList().size());
        assertEquals(2, response.getUnReadMdgList().size());
    }

    /**
     * 测试发送 school message 方法
     * case: 给两个家长发送 school message，每个家长有一个孩子，其中一个家长是已邀请的，另一个家长是未邀请的，已邀请的家长可以收到消息，未邀请的家长收不到消息
     */
    @Test
    public void testPreSendMessage() {
        // 模拟请求数据
        SendMessageRequest request = new SendMessageRequest();
        request.setReceiveRole("PARENT");
        request.setSendType("Recommend");
        request.setSendAtLocal("2023-10-26 19:28:16.050");
        request.setChildCenterIds(Collections.singletonList("2C105D0A-2EDF-4315-B0E9-1D70F44A0C8F"));
        request.setSubject("Test subject");
        request.setContent("<div style=\"white-space: pre-wrap;line-height: 1.42;color:#323338;font-family: \"Source Sans Pro\",\"Helvetica Neue\",Helvetica,Arial,sans-serif;\"><p>Test</p></div>");

        AgencyMetaDataEntity metaData = new AgencyMetaDataEntity();
        metaData.setMetaKey(AgencyMetaKey.SCHOOL_MESSAGE_OPEN.toString());
        metaData.setMetaValue("true");
        when(agencyDao.getMeta(any(), any())).thenReturn(metaData);
        
        List<EnrollmentEntity> enrollmentEntities = new ArrayList<>(); // 小孩数据
        EnrollmentEntity enrollmentEntity = new EnrollmentEntity(); // 创建小孩数据
        enrollmentEntity.setId("childId001"); // 设置小孩 Id
        enrollmentEntity.setDisplayName("childName001"); // 设置小孩名字
        enrollmentEntities.add(enrollmentEntity); // 添加小孩数据
        EnrollmentEntity enrollmentEntity2 = new EnrollmentEntity(); // 创建小孩数据
        enrollmentEntity2.setId("childId002"); // 设置小孩 Id
        enrollmentEntity2.setDisplayName("childName002"); // 设置小孩名字
        enrollmentEntities.add(enrollmentEntity2); // 添加小孩数据
        when(studentDao.getChildrenByCenterIds(request.getChildCenterIds())).thenReturn(enrollmentEntities); // 模拟学生数据

        List<EnrollmentInvitationEntity> invitationEntities = new ArrayList<>(); // 家长邀请数据
        EnrollmentInvitationEntity invitationEntity = new EnrollmentInvitationEntity(); // 创建家长邀请数据
        invitationEntity.setId("invitationId001"); // 设置家长邀请 Id
        invitationEntity.setToken("token001"); // 设置家长邀请 token
        invitationEntity.setUserEmail("<EMAIL>"); // 设置家长邮箱
        invitationEntity.setDisplayName("parentName001"); // 设置家长姓名
        invitationEntity.setEnrollmentId("childId001"); // 设置小孩
        com.learninggenie.common.data.entity.UserEntity user = new com.learninggenie.common.data.entity.UserEntity(); // 创建家长
        user.setId("parentId001"); // 设置家长 Id
        invitationEntity.setApplyUser(user); // 设置家长
        invitationEntity.setState(2); // 设置家长邀请状态
        invitationEntities.add(invitationEntity); // 添加家长邀请数据
        EnrollmentInvitationEntity invitationEntity2 = new EnrollmentInvitationEntity(); // 创建家长邀请数据
        invitationEntity2.setId("invitationId002"); // 设置家长邀请 Id
        invitationEntity2.setToken("token002"); // 设置家长邀请 token
        invitationEntity2.setUserEmail("<EMAIL>"); // 设置家长邮箱
        invitationEntity2.setDisplayName("parentName002");  // 设置家长姓名
        invitationEntity2.setEnrollmentId("childId002"); // 设置小孩
        com.learninggenie.common.data.entity.UserEntity user2 = new com.learninggenie.common.data.entity.UserEntity(); // 创建家长
        user2.setId("parentId002"); // 设置家长 Id
        invitationEntity2.setApplyUser(user2); // 设置家长
        invitationEntity2.setState(0); // 设置家长邀请状态
        invitationEntities.add(invitationEntity2); // 添加家长邀请数据
        when(userDao.getParentCodesByStudentIds(anyString())).thenReturn(invitationEntities); // 模拟家长邀请数据

        // 家长信息
        List<com.learninggenie.common.data.entity.UserEntity> parents = new ArrayList<>(); // 家长数据
        com.learninggenie.common.data.entity.UserEntity parent = new com.learninggenie.common.data.entity.UserEntity(); // 家长数据
        parent.setId("parentId001"); // 设置家长 Id
        parent.setEmail("<EMAIL>"); // 设置家长邮箱
        parent.getEnrollments().add(enrollmentEntity); // 设置家长的小孩
        parents.add(parent); // 添加家长数据
        com.learninggenie.common.data.entity.UserEntity parent2 = new com.learninggenie.common.data.entity.UserEntity(); // 家长数据
        parent2.setId("parentId002"); // 设置家长 Id
        parent.setEmail("<EMAIL>"); // 设置家长邮箱
        parent2.getEnrollments().add(enrollmentEntity2); // 设置家长的小孩
        parents.add(parent2); // 添加家长数据
        when(userDao.getParentsByStudentIds(anyString())).thenReturn(parents); // 模拟获取家长数据
        AuthUserDetails authUserDetails = new AuthUserDetails();
        authUserDetails.setAgencyId("agencyId001");
        authUserDetails.setAgencyName("agencyName001");
        authUserDetails.setUsername("username001");
        when(userProvider.getCurrentUser()).thenReturn(authUserDetails);
        try {
            // 调用测试方法：预发送消息
            Map<String, Object> map = messageService.preSendMessage(request);

            // 验证调用方法
            verify(messagesSendTaskDao).save(any()); // 应该调用了保存消息任务的方法
            // 验证返回数据
            assertEquals(1, map.size()); // 应该只有一个数据
            assertTrue(map.containsKey("messageId")); // 应该包含了 messageId
            assertFalse(StringUtil.isEmptyOrBlank((String) map.get("messageId"))); // messageId 应该不为空
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 测试发送 school message 方法
     * case: 给一个家长发送 school message，家长有两个小孩，其中一个小孩已经被邀请，另一个小孩没有被邀请，家长可以收到消息
     */
    @Test
    public void testPreSendMessage2() {
        // 模拟请求数据
        SendMessageRequest request = new SendMessageRequest();
        request.setReceiveRole("PARENT");
        request.setSendType("Recommend");
        request.setSendAtLocal("2023-10-26 19:28:16.050");
        request.setChildCenterIds(Collections.singletonList("2C105D0A-2EDF-4315-B0E9-1D70F44A0C8F"));
        request.setSubject("Test subject");
        request.setContent("<div style=\"white-space: pre-wrap;line-height: 1.42;color:#323338;font-family: \"Source Sans Pro\",\"Helvetica Neue\",Helvetica,Arial,sans-serif;\"><p>Test</p></div>");

        AgencyMetaDataEntity metaData = new AgencyMetaDataEntity();
        metaData.setMetaKey(AgencyMetaKey.SCHOOL_MESSAGE_OPEN.toString());
        metaData.setMetaValue("true");
        when(agencyDao.getMeta(any(), any())).thenReturn(metaData);

        List<EnrollmentEntity> enrollmentEntities = new ArrayList<>(); // 小孩数据
        EnrollmentEntity enrollmentEntity = new EnrollmentEntity(); // 创建小孩数据
        enrollmentEntity.setId("childId001"); // 设置小孩 Id
        enrollmentEntity.setDisplayName("childName001"); // 设置小孩名字
        enrollmentEntities.add(enrollmentEntity); // 添加小孩数据
        EnrollmentEntity enrollmentEntity2 = new EnrollmentEntity(); // 创建小孩数据
        enrollmentEntity2.setId("childId002"); // 设置小孩 Id
        enrollmentEntity2.setDisplayName("childName002"); // 设置小孩名字
        enrollmentEntities.add(enrollmentEntity2); // 添加小孩数据
        when(studentDao.getChildrenByCenterIds(request.getChildCenterIds())).thenReturn(enrollmentEntities); // 模拟学生数据

        List<EnrollmentInvitationEntity> invitationEntities = new ArrayList<>(); // 家长邀请数据
        EnrollmentInvitationEntity invitationEntity = new EnrollmentInvitationEntity(); // 创建家长邀请数据
        invitationEntity.setId("invitationId001"); // 设置家长邀请 Id
        invitationEntity.setToken("token001"); // 设置家长邀请 token
        invitationEntity.setUserEmail("<EMAIL>"); // 设置家长邮箱
        invitationEntity.setDisplayName("parentName001"); // 设置家长姓名
        invitationEntity.setEnrollmentId("childId001"); // 设置小孩
        com.learninggenie.common.data.entity.UserEntity user = new com.learninggenie.common.data.entity.UserEntity(); // 创建家长
        user.setId("parentId001"); // 设置家长 Id
        invitationEntity.setApplyUser(user); // 设置家长
        invitationEntity.setState(2); // 设置家长邀请状态
        invitationEntities.add(invitationEntity); // 添加家长邀请数据
        EnrollmentInvitationEntity invitationEntity2 = new EnrollmentInvitationEntity(); // 创建家长邀请数据
        invitationEntity2.setId("invitationId002"); // 设置家长邀请 Id
        invitationEntity2.setToken("token002"); // 设置家长邀请 token
        invitationEntity2.setUserEmail("<EMAIL>"); // 设置家长邮箱
        invitationEntity2.setDisplayName("parentName001");  // 设置家长姓名
        invitationEntity2.setEnrollmentId("childId002"); // 设置小孩
        com.learninggenie.common.data.entity.UserEntity user2 = new com.learninggenie.common.data.entity.UserEntity(); // 创建家长
        user2.setId("parentId001"); // 设置家长 Id
        invitationEntity2.setApplyUser(user2); // 设置家长
        invitationEntity2.setState(0); // 设置家长邀请状态
        invitationEntities.add(invitationEntity2); // 添加家长邀请数据
        when(userDao.getParentCodesByStudentIds(anyString())).thenReturn(invitationEntities); // 模拟家长邀请数据

        // 家长信息
        List<com.learninggenie.common.data.entity.UserEntity> parents = new ArrayList<>(); // 家长数据
        com.learninggenie.common.data.entity.UserEntity parent = new com.learninggenie.common.data.entity.UserEntity(); // 家长数据
        parent.setId("parentId001"); // 设置家长 Id
        parent.setEmail("<EMAIL>"); // 设置家长邮箱
        parent.getEnrollments().add(enrollmentEntity); // 设置家长的小孩
        parents.add(parent); // 添加家长数据
        when(userDao.getParentsByStudentIds(anyString())).thenReturn(parents); // 模拟获取家长数据
        AuthUserDetails authUserDetails = new AuthUserDetails();
        authUserDetails.setAgencyId("agencyId001");
        authUserDetails.setAgencyName("agencyName001");
        authUserDetails.setUsername("username001");
        when(userProvider.getCurrentUser()).thenReturn(authUserDetails);
        try {
            // 调用测试方法：预发送消息
            Map<String, Object> map = messageService.preSendMessage(request);

            // 验证调用方法
            verify(messagesSendTaskDao).save(any()); // 应该调用了保存消息任务的方法
            // 验证返回数据
            assertEquals(1, map.size()); // 应该只有一个数据
            assertTrue(map.containsKey("messageId")); // 应该包含了 messageId
            assertFalse(StringUtil.isEmptyOrBlank((String) map.get("messageId"))); // messageId 应该不为空
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 测试发送 school message 方法
     * case: 给一个家长发送 school message，家长有一个小孩，小孩没有被邀请，家长不能收到消息
     */
    @Test
    public void testPreSendMessage3() {
        // 模拟请求数据
        SendMessageRequest request = new SendMessageRequest();
        request.setReceiveRole("PARENT");
        request.setSendType("Recommend");
        request.setSendAtLocal("2023-10-26 19:28:16.050");
        request.setChildCenterIds(Collections.singletonList("2C105D0A-2EDF-4315-B0E9-1D70F44A0C8F"));
        request.setSubject("Test subject");
        request.setContent("<div style=\"white-space: pre-wrap;line-height: 1.42;color:#323338;font-family: \"Source Sans Pro\",\"Helvetica Neue\",Helvetica,Arial,sans-serif;\"><p>Test</p></div>");

        AgencyMetaDataEntity metaData = new AgencyMetaDataEntity();
        metaData.setMetaKey(AgencyMetaKey.SCHOOL_MESSAGE_OPEN.toString());
        metaData.setMetaValue("true");
        when(agencyDao.getMeta(any(), any())).thenReturn(metaData);
        
        List<EnrollmentEntity> enrollmentEntities = new ArrayList<>(); // 小孩数据
        EnrollmentEntity enrollmentEntity = new EnrollmentEntity(); // 创建小孩数据
        enrollmentEntity.setId("childId001"); // 设置小孩 Id
        enrollmentEntity.setDisplayName("childName001"); // 设置小孩名字
        enrollmentEntities.add(enrollmentEntity); // 添加小孩数据
        when(studentDao.getChildrenByCenterIds(request.getChildCenterIds())).thenReturn(enrollmentEntities); // 模拟学生数据

        List<EnrollmentInvitationEntity> invitationEntities = new ArrayList<>(); // 家长邀请数据
        EnrollmentInvitationEntity invitationEntity = new EnrollmentInvitationEntity(); // 创建家长邀请数据
        invitationEntity.setId("invitationId001"); // 设置家长邀请 Id
        invitationEntity.setToken("token001"); // 设置家长邀请 token
        invitationEntity.setUserEmail("<EMAIL>"); // 设置家长邮箱
        invitationEntity.setDisplayName("parentName001"); // 设置家长姓名
        invitationEntity.setEnrollmentId("childId001"); // 设置小孩
        com.learninggenie.common.data.entity.UserEntity user = new com.learninggenie.common.data.entity.UserEntity(); // 创建家长
        user.setId("parentId001"); // 设置家长 Id
        invitationEntity.setApplyUser(user); // 设置家长
        invitationEntity.setState(0); // 设置家长邀请状态
        invitationEntities.add(invitationEntity); // 添加家长邀请数据
        when(userDao.getParentCodesByStudentIds(anyString())).thenReturn(invitationEntities); // 模拟家长邀请数据

        // 家长信息
        List<com.learninggenie.common.data.entity.UserEntity> parents = new ArrayList<>(); // 家长数据
        com.learninggenie.common.data.entity.UserEntity parent = new com.learninggenie.common.data.entity.UserEntity(); // 家长数据
        parent.setId("parentId001"); // 设置家长 Id
        parent.setEmail("<EMAIL>"); // 设置家长邮箱
        parent.getEnrollments().add(enrollmentEntity); // 设置家长的小孩
        parents.add(parent); // 添加家长数据
        when(userDao.getParentsByStudentIds(anyString())).thenReturn(parents); // 模拟获取家长数据
        AuthUserDetails authUserDetails = new AuthUserDetails();
        authUserDetails.setAgencyId("agencyId001");
        authUserDetails.setAgencyName("agencyName001");
        authUserDetails.setUsername("username001");
        when(userProvider.getCurrentUser()).thenReturn(authUserDetails);
        assertThrows(BusinessException.class, () -> messageService.preSendMessage(request)); // 不能发送消息，应该抛出异常
    }

    @Test
    public void testUpdateUserMessageReadStatusByReceiver() {
        // 模拟消息数据
        String messageType = "HEALTH_CHECK_MSG";
        String email = "email";
        String userId = "userId";
        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setEmail(email);
        List<String> ids = Arrays.asList("id");
        when(userProvider.getCurrentUserId()).thenReturn(user.getId());
        when(userProvider.checkUser(userId)).thenReturn(user);
        when(messageDao.getUserUnReadMessagesIds(email, messageType)).thenReturn(ids);
        when(messageDao.getUserUnReadMessagesIds(email, messageType)).thenReturn(ids);
        messageService.updateUserMessageReadStatusByReceiver("", messageType);
        // 验证模拟对象的方法是否被调用
        Mockito.verify(messageDao, Mockito.times(1)).updateUserMessageReadStatusByIds(ids);
    }
}

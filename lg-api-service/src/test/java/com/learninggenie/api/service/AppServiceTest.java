package com.learninggenie.api.service;

import com.learninggenie.api.model.AppVersion;
import com.learninggenie.api.service.impl.AppServiceImpl;
import com.learninggenie.common.data.entity.AppMetadataEntity;
import com.learninggenie.common.data.enums.AppVersionType;
import com.learninggenie.common.data.repository.AppMetadataRepository;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class AppServiceTest {
    @InjectMocks
    private AppServiceImpl appService;
    @Mock
    private AppMetadataRepository appMetadataRepository;

    @Test
    public void testGetAppVersion(){
        AppMetadataEntity appMetadataEntity = new AppMetadataEntity();
        appMetadataEntity.setMetaValue("2.3");
        Mockito.when(appMetadataRepository.findTop1ByMetaKey(Mockito.anyString())).thenReturn(appMetadataEntity);
        AppVersion appVersion = appService.getAppVersion(AppVersionType.WEB_VERSION);
        Assert.assertTrue(appVersion.getVersion().equals("2.3"));
    }
}

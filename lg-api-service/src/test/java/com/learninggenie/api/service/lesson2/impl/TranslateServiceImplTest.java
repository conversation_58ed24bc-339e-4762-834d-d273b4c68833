package com.learninggenie.api.service.lesson2.impl;

import com.learninggenie.api.model.lesson2.LessonDetailResponse;
import com.learninggenie.api.model.lesson2.enums.TranslateConfigKey;
import com.learninggenie.api.provider.TranslateProvider;
import com.learninggenie.common.cache.RedisCacheServiceImpl;
import com.learninggenie.common.data.dao.TranslationDao;
import com.learninggenie.common.data.dao.lesson2.TranslationsConfigDao;
import org.apache.tika.language.detect.LanguageResult;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.Silent.class)
public class TranslateServiceImplTest {
    @InjectMocks
    private TranslateServiceImpl translateServiceImpl;

    @Mock
    private RedisCacheServiceImpl redisCacheServiceImpl;

    @Mock
    private TranslateProvider translateProviderImpl;

    @Mock
    private TranslationDao translationDao;

    @Mock
    private TranslationsConfigDao translationsConfigDao;

    /**
     * 测试翻译方法
     */
    @Test
    public void testTranslateObjectData() {
        // 构建请求参数
        LessonDetailResponse lessonDetailResponse = new LessonDetailResponse();
        String targetLanguageLangCode = "zh-CN";
        String configVal = "001";

        // 设置方法调用返回结果
        when(translationsConfigDao.getConfigValueByKey(anyString())).thenReturn(configVal);

        // 调用测试方法
        LessonDetailResponse translateObjectData = translateServiceImpl.translateObjectData(lessonDetailResponse, TranslateConfigKey.LESSON_DETAIL, LessonDetailResponse.class, targetLanguageLangCode);

        // 断言
        assertNotNull(translateObjectData);
    }

    /**
     * 测试识别内容的语言类型方法
     */
    @Test
    public void testDetectLanguage() {
        // 构建请求参数
        LessonDetailResponse lessonDetailResponse = new LessonDetailResponse();
        String targetLanguageLangCode = "zh-CN";
        String configVal = "001";

        // 设置方法调用返回结果
        when(translationsConfigDao.getConfigValueByKey(anyString())).thenReturn(configVal);
        when(translateProviderImpl.detectLanguage(any())).thenReturn(new LanguageResult(null, null, 0.0F));

        // 调用测试方法
        LanguageResult languageResult = translateServiceImpl.detectLanguage(lessonDetailResponse, TranslateConfigKey.LESSON_DETAIL);

        // 断言
        assertNotNull(languageResult);
    }
}

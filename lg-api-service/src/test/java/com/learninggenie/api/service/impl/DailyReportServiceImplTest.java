package com.learninggenie.api.service.impl;


import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.model.dailyreport.ApproveReportRequest;
import com.learninggenie.api.model.dailyreport.DailyReportResponse;
import com.learninggenie.api.model.note.NoteModel;
import com.learninggenie.api.model.report.*;
import com.learninggenie.api.provider.EnrollmentProvider;
import com.learninggenie.api.provider.NoteProvider;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.security.AuthUserDetails;
import com.learninggenie.api.service.DropOffNoteService;
import com.learninggenie.api.service.NoteService;
import com.learninggenie.common.data.dao.*;
import com.learninggenie.common.data.entity.*;
import com.learninggenie.common.data.enums.ApproveStatus;
import com.learninggenie.common.data.enums.NoteType;
import com.learninggenie.common.data.enums.SleepCheckPosition;
import com.learninggenie.common.data.enums.SleepCheckStatus;
import com.learninggenie.common.data.model.AgencyModel;
import com.learninggenie.common.data.model.note.NoteEnrollmentModel;
import com.learninggenie.common.utils.TimeUtil;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.mockito.Mockito.*;

/**
 * Created by junjie on 2017/6/12.
 */
@RunWith(MockitoJUnitRunner.class)
public class DailyReportServiceImplTest {

    @InjectMocks
    private DailyReportServiceImpl dailyReportService;
    @Mock
    private EnrollmentProvider enrollmentProvider;
    @Mock
    private NoteDao noteDao;
    @Mock
    private UserProvider userProvider;
    @Mock
    private StudentDao studentDao;
    @Mock
    private EventDao eventDao;
    @Mock
    private CenterDao centerDao;
    @Mock
    private DomainDao domainDao;
    @Mock
    private LessonDao lessonDao;
    @Mock
    private NoteService noteService;
    @Mock
    private NoteProvider noteProvider;

    @Mock
    private FormDao formDao;

    @Mock
    private DropOffNoteService dropOffNoteService;

    @Mock
    private AgencyDao agencyDao;

    @Test(expected = BusinessException.class)
    public void testGetDailyReport_StudentIdIsNull() {
        dailyReportService.getDailyReport(null, new Date(), true);
    }

    @Test(expected = BusinessException.class)
    public void testGetDailyReport_DateIsNull() {
        dailyReportService.getDailyReport("s001", null, true);
    }

    private List<NoteEntity> prepareNoteData() {
        EnrollmentEntity student = this.prepareStudentData();

        EnrollmentEntity student2 = new EnrollmentEntity();
        student2.setId("s002");
        student2.setFirstName("s");
        student2.setLastName("2");
        student2.setDisplayName("s 2");

        List<NoteEntity> notes = new ArrayList<>();
        NoteEntity note1 = new NoteEntity();
        note1.getEnrollments().add(student);
        note1.setId("n001");
        note1.setCopiedToActivity(false);
        note1.setFavorite(false);
        note1.setCreateAtLocal(new Date("2017/6/10"));
        note1.setType(NoteType.NORMAL.toString());
        note1.setOneNote("on001");
        notes.add(note1);

        NoteEntity note2 = new NoteEntity();
        note2.getEnrollments().add(student);
        note2.setId("n002");
        note2.setCopiedToActivity(false);
        note2.setFavorite(false);
        note2.setCreateAtLocal(new Date("2017/6/11"));
        NoteMetaDataEntity metaData2 = new NoteMetaDataEntity();
        metaData2.setMetaKey("key");
        metaData2.setMetaValue("value");
        note2.getMetas().add(metaData2);
        note2.setType(NoteType.VIDEO.toString());
        note2.setApproveStatus(ApproveStatus.PENDING.toString());
        note2.setOneNote("on002");
        notes.add(note2);

        NoteEntity note3 = new NoteEntity();
        note3.getEnrollments().add(student);
        note3.getEnrollments().add(student2);
        note3.setId("n003");
        note3.setCopiedToActivity(false);
        note3.setFavorite(false);
        note3.setCreateAtLocal(new Date("2017/6/12"));
        note3.setType(NoteType.ACTIVITY.toString());
        note3.setApproveStatus(ApproveStatus.PENDING.toString());
        note3.setOneNote("on003");
        notes.add(note3);

        NoteEntity note4 = new NoteEntity();
        note4.getEnrollments().add(student);
        note4.getEnrollments().add(student2);
        note4.setId("n004");
        note4.setCopiedToActivity(false);
        note4.setFavorite(false);
        note4.setCreateAtLocal(new Date("2017/6/09"));
        note4.setType(NoteType.ACTIVITY.toString());
        note4.setApproveStatus(ApproveStatus.REJECTED.toString());
        note4.setOneNote("on004");
        notes.add(note4);
        return notes;
    }

    private EnrollmentEntity prepareStudentData(){
        EnrollmentEntity student = new EnrollmentEntity();
        student.setId("s001");
        student.setFirstName("s");
        student.setLastName("1");
        student.setDisplayName("s 1");
        return student;
    }

    private List<NoteEntity> prepareNoteData2(){
        List<NoteEntity> notes = this.prepareNoteData();
        NoteEntity note = new NoteEntity();
        note.getEnrollments().add(this.prepareStudentData());
        note.setId("n005");
        note.setCopiedToActivity(false);
        note.setFavorite(false);
        note.setCreateAtLocal(new Date("2017/6/09"));
        note.setType(NoteType.ACTIVITY.toString());
        note.setApproveStatus(ApproveStatus.APPROVED.toString());
        notes.add(note);
        return notes;
    }

    @Ignore
    @Test
    public void testGetDailyReport_MultiType() {
        EnrollmentEntity student = this.prepareStudentData();

        when(enrollmentProvider.checkEnrollment("s001")).thenReturn(student);
        List<NoteEntity> notes = this.prepareNoteData();
        List<NoteEnrollmentModel> childModel = new ArrayList<>();
        when(noteDao.getNotes(anyString(), anyString(), anyString())).thenReturn(notes);

        Date date = new Date("2017/6/12 17:17:17");
        when(userProvider.getCurrentUserId()).thenReturn("s001");
        when(studentDao.getEnrollmentByOneNotes(anyList())).thenReturn(childModel);
        DailyReportResponse response = dailyReportService.getDailyReport("s001", date, true);

        Assert.assertTrue(response.getNotes().size() == 3);
        Assert.assertTrue(response.getNotes().get(0).getId() == "n003");
        Assert.assertTrue(response.getNotes().get(1).getType().equalsIgnoreCase(NoteType.VIDEO.toString()));
        Assert.assertTrue(response.getReport().getCreateDate().equalsIgnoreCase(TimeUtil.format(date, "MM/dd/yyyy HH:mm:ss")));
    }

    @Test(expected = BusinessException.class)
    public void getPendingDailyReport_StudentIdIsNull() {
        dailyReportService.getPendingDailyReport(null);
    }

    @Test
    public void getPendingDailyReport() {
//        String studentId = "s001";
//        List<NoteEntity> notes = this.prepareNoteData();
//        when(noteDao.getAllByApproveStatus(studentId, ApproveStatus.PENDING.toString())).thenReturn(notes);
        //TODO: fix
//        List<NoteModel> response = dailyReportService.getPendingDailyReport(studentId);
//        Assert.assertTrue(response.size() == 1);
//        Assert.assertTrue(response.get(0).getType().equalsIgnoreCase(NoteType.VIDEO.toString()));
    }

    @Test(expected = BusinessException.class)
    public void testGetPendingDailyReport_GroupIdIsNull() {
        dailyReportService.getPendingDailyReportByGroupId(null);
    }

    @Test
    public void testGetPendingDailyReportByGroupId() {
//        String groupId = "g001";
//        List<NoteEntity> notes = this.prepareNoteData();
//        when(noteDao.getNoteByGroupId(groupId, ApproveStatus.PENDING.toString())).thenReturn(notes);
        //TODO: fix
//        List<NoteModel> response = dailyReportService.getPendingDailyReportByGroupId(groupId);
//        Assert.assertTrue(response.size() == 1);
//        Assert.assertTrue(response.get(0).getType().equalsIgnoreCase(NoteType.ACTIVITY.toString()));
//        Assert.assertTrue(response.get(0).getId().equalsIgnoreCase("n003"));
    }

    @Test(expected = BusinessException.class)
    public void testApproveReport_NoReportId(){
        ApproveReportRequest request = new ApproveReportRequest();
        dailyReportService.approveReport(request);
        verify(noteDao,times(0)).batchUpdateReportApproveStatus(anyList(),anyString(),anyString());
    }

    @Ignore
    @Test
    public void testApproveReport(){
        ApproveReportRequest request = new ApproveReportRequest();
        request.getNoteIds().add("n001");
        dailyReportService.approveReport(request);
        verify(noteDao,times(1)).batchUpdateReportApproveStatus(anyList(),anyString(),anyString());
    }

    @Test(expected = BusinessException.class)
    public void testRejectReport_NoReportId(){
        ApproveReportRequest request = new ApproveReportRequest();
        dailyReportService.rejectReport(request);
        verify(noteDao,times(0)).batchUpdateReportApproveStatus(anyList(),anyString(),anyString());
    }

    @Test(expected = BusinessException.class)
    public void testRejectReport_MultiReportId(){
        ApproveReportRequest request = new ApproveReportRequest();
        request.getNoteIds().add("n001");
        request.getNoteIds().add("n002");
        dailyReportService.rejectReport(request);
        verify(noteDao,times(0)).batchUpdateReportApproveStatus(anyList(),anyString(),anyString());
    }

    @Test(expected = BusinessException.class)
    public void testRejectReport_NoRejectReason(){
        ApproveReportRequest request = new ApproveReportRequest();
        request.getNoteIds().add("n001");
        dailyReportService.rejectReport(request);
        verify(noteDao,times(0)).batchUpdateReportApproveStatus(anyList(),anyString(),anyString());
    }

    @Ignore
    @Test
    public void testRejectReport(){
        ApproveReportRequest request = new ApproveReportRequest();
        request.getNoteIds().add("n001");
        request.setApproveDescription("reason");
        dailyReportService.rejectReport(request);
        verify(noteDao,times(1)).batchUpdateReportApproveStatus(anyList(),anyString(),anyString());
    }

    @Test(expected = BusinessException.class)
    public void testGetApproveReport_StudentIdIsNull() {
        dailyReportService.getApprovedReport(null,new Date());
    }

    @Ignore
    @Test
    public void testGetApproveReport_Date(){
        String studentId = "s001";
        when(noteDao.getNotes(anyString(), anyString(), anyString())).thenReturn(this.prepareNoteData());
        List<NoteModel> response = dailyReportService.getApprovedReport(studentId,new Date());
        Assert.assertTrue(response.size() == 0);
    }

    @Ignore
    @Test
    public void testGetApproveReport_DateIsNull(){
        String studentId = "s001";
        when(noteDao.getAllByApproveStatus(studentId, ApproveStatus.APPROVED.toString())).thenReturn(this.prepareNoteData2());
        List<NoteModel> response = dailyReportService.getApprovedReport(studentId,null);
        Assert.assertTrue(response.size() == 1);
    }

    /**
     * case:查询的开始日期在结束日期后
     * 结果:抛异常
     */
    @Ignore
    @Test(expected = BusinessException.class)
    public void testGetDailyReportByTime_childIsNotExist() {
        String studentId = "e001";
        Calendar instance1 = Calendar.getInstance();
        instance1.set(2018, 05, 12);
        Date fromDate = instance1.getTime();
        Calendar instance2 = Calendar.getInstance();
        instance2.set(2018, 05, 05);
        Date toDate = instance2.getTime();
        dailyReportService.getDailyReportByTime(studentId, fromDate.toString(), toDate.toString(), true);
    }

    @Ignore
    @Test(expected = BusinessException.class)
    public void testGetSleepChildList() {
        // 班级 ID GroupId
        String groupId = UUID.randomUUID().toString();
        List<ChildSleepCheckModel> sleepCheckModels = new ArrayList<>();
        String date = TimeUtil.format(TimeUtil.getNow(), TimeUtil.format10);
        when(dailyReportService.getSleepChildList(groupId, date)).thenReturn(sleepCheckModels);
    }

    /**
     * 测试
     */
    @Ignore
    @Test(expected = BusinessException.class)
    public void testGetChildCheckRecord() {
        String childId = UUID.randomUUID().toString();
        String date = TimeUtil.format(TimeUtil.getNow(), TimeUtil.format10);
        List<SleepRecordResponse> recordResponses = new ArrayList<>();
        when(dailyReportService.getChildCheckRecord(childId, date)).thenReturn(recordResponses);
    }

    @Ignore
    @Test(expected = BusinessException.class)
    public void testGetChildLatestCheckRecord() {
        // 班级 ID GroupId
        String groupId = UUID.randomUUID().toString();
        List<SleepLatestCheckResponse> checkResponses = new ArrayList<>();
        when(dailyReportService.getChildLatestCheckRecord(groupId)).thenReturn(checkResponses);
    }

    @Ignore
    @Test
    public void testStartSleep() {
        StartSleepRequest request = new StartSleepRequest();
        String childId = UUID.randomUUID().toString();
        request.getChildIds().add(childId);
        // 班级 ID GroupId
        String groupId = UUID.randomUUID().toString();
        request.setGroupId(groupId);
        dailyReportService.startSleep(request);
    }

    @Ignore
    @Test
    public void testStopSleep() {
        StartSleepRequest request = new StartSleepRequest();
        String childId = UUID.randomUUID().toString();
        request.getChildIds().add(childId);
        // 班级 ID GroupId
        String groupId = UUID.randomUUID().toString();
        request.setGroupId(groupId);
        dailyReportService.stopSleep(request);
    }

    @Ignore
    @Test
    public void testDeleteSleep() {
        // sleepId
        String sleepId = UUID.randomUUID().toString();
        dailyReportService.deleteSleep(sleepId);
    }

    @Ignore
    @Test
    public void testDeleteSleepCheck() {
        // checkId
        String checkId = UUID.randomUUID().toString();
        dailyReportService.deleteSleepCheck(checkId);
    }

    @Ignore
    @Test
    public void testCheckSleep() {
        CheckSleepRequest request = new CheckSleepRequest();
        // 用户 Id
        String userId = UUID.randomUUID().toString();
        request.setCheckUserId(userId);
        // 班级 Id
        String groupId = UUID.randomUUID().toString();
        // 睡眠 Id
        String sleepId = UUID.randomUUID().toString();
        String date = TimeUtil.format(TimeUtil.getNow(), TimeUtil.format2);
        request.setGroupId(groupId);
        CheckSleepModel checkSleepModel = new CheckSleepModel();
        checkSleepModel.setCheckUserName("AA");
        checkSleepModel.setSleepId(sleepId);
        checkSleepModel.setCheckDate(date);
        checkSleepModel.setId(UUID.randomUUID().toString());
        checkSleepModel.setPosition(SleepCheckPosition.SIDE.toString());
        checkSleepModel.setStatue(SleepCheckStatus.SLEEPING_SOUNDLY.toString());
        request.getCheckSleepModels().add(checkSleepModel);
        dailyReportService.checkSleep(request);
    }

    @Ignore
    @Test
    public void testSetSleepCheckAlert() {
        CheckSleepAlertRequest request = new CheckSleepAlertRequest();
        request.setAlert(false);
        request.setAdvanceMinutes(2);
        dailyReportService.setSleepCheckAlert(request);
    }

    @Ignore
    @Test
    public void testGetSleepCheckAlertSetting() {
        String userId = UUID.randomUUID().toString();
        dailyReportService.getSleepCheckAlertSetting(userId);
    }

    @Ignore
    @Test
    public void testGetSleepCheckAlert() {
        String userId = UUID.randomUUID().toString();
        dailyReportService.getSleepCheckAlert(userId);
    }

    @Test
    public void testGetDailyReport() {
        AuthUserDetails userEntity = new AuthUserDetails();
        List<EventEntity> entities = new ArrayList<>();

        when(enrollmentProvider.checkEnrollment(any())).thenReturn(new EnrollmentEntity());
        when(eventDao.getEventByEnrollment(any(), any(), any())).thenReturn(entities);
        when(userProvider.checkCurrentUser()).thenReturn(userEntity);
        when(userProvider.getAgencyByUserId(any())).thenReturn(new AgencyModel());
        when(centerDao.getCentersByAgencyId(any())).thenReturn(new ArrayList<>());
        List<NoteEntity> notes = new ArrayList<>();
        NoteEntity noteEntity = new NoteEntity();
        noteEntity.setId("N001");
        noteEntity.setType("ACTIVITY");
        noteEntity.setCopiedToActivity(false);
        noteEntity.setFavorite(false);
        noteEntity.setApproveStatus("PENDING");
        UserEntity userEntity1 = new UserEntity();
        userEntity1.setId("U001");
        noteEntity.setUpdateByUser(userEntity1);
        NoteEntity noteEntity2 = new NoteEntity();
        noteEntity2.setId("N002");
        noteEntity2.setType("ACTIVITY");
        noteEntity2.setCopiedToActivity(false);
        noteEntity2.setFavorite(false);
        noteEntity2.setApproveStatus("APPROVED");
        noteEntity2.setUpdateByUser(userEntity1);
        notes.add(noteEntity);
        notes.add(noteEntity2);

        when(noteDao.getNotes(anyString(), anyString(), anyString())).thenReturn(notes);
        when(domainDao.getDomainByNoteIds(any())).thenReturn(new ArrayList<>());
        when(noteProvider.isOpenApproval(any(), any())).thenReturn(true);
//        when(dropOffNoteService.getSubmittedFormDropOffNotes("COO1", "2022-01-01 00:00:00", "2022-01-01 00:00:00")).thenReturn(new ArrayList<>());
        dailyReportService.getDailyReport("S001", new Date(), true);
        verify(noteService, times(2)).translateNotes(any(), any());
    }


    @Test
    public void testGetDailyReportByTime() {
        AuthUserDetails userEntity = new AuthUserDetails();
        List<EventEntity> entities = new ArrayList<>();

        when(eventDao.getEventByEnrollment(any(), any(), any())).thenReturn(entities);
        when(userProvider.checkCurrentUser()).thenReturn(userEntity);
        when(userProvider.getAgencyByUserId(any())).thenReturn(new AgencyModel());
        when(centerDao.getCentersByAgencyId(any())).thenReturn(new ArrayList<>());
        List<NoteEntity> notes = new ArrayList<>();
        NoteEntity noteEntity = new NoteEntity();
        noteEntity.setId("N001");
        noteEntity.setType("ACTIVITY");
        noteEntity.setCopiedToActivity(false);
        noteEntity.setFavorite(false);
        noteEntity.setApproveStatus("PENDING");
        noteEntity.setOneNote("oneNote");
        noteEntity.setCreateAtLocal(new Date());
        UserEntity userEntity1 = new UserEntity();
        userEntity1.setId("U001");
        userEntity1.setRole("AGENCY_OWNER");
        noteEntity.setUpdateByUser(userEntity1);
        NoteEntity noteEntity2 = new NoteEntity();
        noteEntity2.setId("N002");
        noteEntity2.setType("ACTIVITY");
        noteEntity2.setCopiedToActivity(false);
        noteEntity2.setFavorite(false);
        noteEntity2.setApproveStatus("APPROVED");
        noteEntity2.setUpdateByUser(userEntity1);
        noteEntity2.setOneNote("oneNote2");
        noteEntity2.setCreateAtLocal(new Date());
        notes.add(noteEntity2);
        notes.add(noteEntity);

        when(noteDao.getNotes(anyString(), anyString(), anyString())).thenReturn(notes);
        when(domainDao.getDomainByNoteIds(any())).thenReturn(new ArrayList<>());
        when(noteProvider.isOpenApproval(any(), any())).thenReturn(true);
        when(enrollmentProvider.checkEnrollment(any(), any())).thenReturn(new EnrollmentEntity());
        when(userProvider.getCurrentPlatform()).thenReturn("WEB");
        when(userProvider.getCurrentAppType()).thenReturn("WEB");
//        when(dropOffNoteService.getSubmittedFormDropOffNotes("COO1", "2022-01-01 00:00:00", "2022-01-01 00:00:00")).thenReturn(new ArrayList<>());
        when(userProvider.getVersion()).thenReturn(1);
        when(userProvider.checkUser(any())).thenReturn(userEntity1);
        when(userProvider.getVersion()).thenReturn(1);
        when(userProvider.checkUser(any())).thenReturn(userEntity1);
        dailyReportService.getDailyReportByTime("S001", "", "", true);
        verify(noteService, times(1)).translateNotes(any(), any());
    }
}
package com.learninggenie.api.service.impl;

import com.amazonaws.services.lambda.model.InvokeResult;
import com.learninggenie.api.model.DownFileResponse;
import com.learninggenie.api.model.event.NotifyResponse;
import com.learninggenie.api.model.event.ResultsResponse;
import com.learninggenie.api.provider.RemoteProvider;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.common.cache.CacheServiceImpl;
import com.learninggenie.common.data.dao.*;
import com.learninggenie.common.data.dao.impl.EventDaoImpl;
import com.learninggenie.common.data.dao.impl.MetaDaoImpl;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.entity.UserEntity;
import com.learninggenie.common.data.entity.*;
import com.learninggenie.common.data.enums.PdfConvertStatus;
import com.learninggenie.common.data.enums.UserMetaKey;
import com.learninggenie.common.data.model.*;
import com.learninggenie.common.data.model.event.EventStatisticsModel;
import com.learninggenie.common.data.repository.AppMetadataRepository;
import com.learninggenie.common.data.repository.EventRepository;
import com.learninggenie.common.filesystem.FileSystem;
import com.learninggenie.common.utils.TimeUtil;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

/**
 * Created by gaochenjie on 2023/9/7.
 */
@RunWith(MockitoJUnitRunner.class)
public class EventServiceImplTest {

    @InjectMocks
    private EventServiceImpl eventService;

    @Mock
    UsersMetaDataDao userMetaDao;

    @Mock
    UserProvider userProvider;

    @Mock
    private GroupDao groupDao;

    @Mock
    private AgencyDao agencyDao;

    @Mock
    private HealthCheckFormDao healthCheckFormDao;

    @Mock
    private FormsFormDao formsFormDao;

    @Mock
    private EventDaoImpl eventDao;

    @Mock
    private UserDaoImpl userDao;

    @Mock
    private CacheServiceImpl cacheService;

    @Mock
    private MetaDaoImpl metaDao;

    @Mock
    private EventRepository eventRepository;

    @Mock
    private FileSystem fileSystem;

    @Mock
    private ReportDao reportDao;

    @Mock
    private MediaDao mediaDao;

    @Mock
    private UsersFileDao usersFileDao;

    @Mock
    private StudentDao studentDao;

    @Mock
    private AppMetadataRepository appMetadataRepository;

    @Mock
    private com.learninggenie.common.messaging.EmailService emailService;

    @Mock
    private RemoteProvider remoteProvider;

    /**
     * 测试获取未读消息邮件通知开关是否打开
     * case: 未读消息邮件通知功能打开
     */
    @Test
    public void testGetEventNotify1() {
        // 准备数据
        String userId = "userId001";
        String agencyId = "agencyId001";
        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole("AGENCY_OWNER");
        Date to = TimeUtil.addMinutes(TimeUtil.getUtcNow(), 10);
        List<EventUserEntity> eventUserEntities = new ArrayList<>();
        EventUserEntity eventUserEntity = new EventUserEntity();
        eventUserEntity.setIsRead(false);
        eventUserEntity.setUserId(userId);
        eventUserEntities.add(eventUserEntity);
        List<UsersMetaDataEntity> metas = new ArrayList<>();
        AgencyModel userAgency = new AgencyModel();
        userAgency.setId(agencyId);
        List<AgencyMetaDataEntity> allMetaData = new ArrayList<>();
        AgencyMetaDataEntity agencyMetaDataEntity = new AgencyMetaDataEntity();
        agencyMetaDataEntity.setAgencyId(agencyId);
        agencyMetaDataEntity.setMetaKey("EVENT_OPEN");
        agencyMetaDataEntity.setMetaValue("1");
        allMetaData.add(agencyMetaDataEntity);
        List<String> groupIds = new ArrayList<>();
        groupIds.add("groupId001");
        AgencyModel agency = new AgencyModel();
        agency.setId(agencyId);
        // 用户引导使用的 metaKey
        String[] featureOpenKeys = new String[] {
                "UNREAD_MESSAGE_EMAIL_OPEN",
                "PERMISSION_LESSONS_WITHOUT_APPROVAL",
                "CREATE_UNIT_PLANNER_OPEN"
        };
        List<UsersMetaDataEntity> allMetaKeys = new ArrayList<>();
        UsersMetaDataEntity userMetaDataEntity = new UsersMetaDataEntity();
        userMetaDataEntity.setUserId(userId);
        userMetaDataEntity.setMetaKey("UNREAD_MESSAGE_EMAIL_OPEN");
        userMetaDataEntity.setMetaValue("true");
        allMetaKeys.add(userMetaDataEntity);
        FormsFormEntity formEntity = new FormsFormEntity();
        formEntity.setAgencyId(agencyId);
        formEntity.setFitBatch(false);
        List<CenterGroupModel> centerGroupModels = new ArrayList<>();
        CenterGroupModel centerGroupModel = new CenterGroupModel();
        centerGroupModel.setCenterId("centerId001");
        centerGroupModel.setGroupId("group001");
        centerGroupModels.add(centerGroupModel);
        String key = "USER_ACTIVE_TIME" + userId.toUpperCase();

        // 数据模拟
        lenient().when(userProvider.checkUser(userId)).thenReturn(userEntity);
        lenient().when(eventDao.queryEventUserByUserIdAndToTime(userId, to)).thenReturn(eventUserEntities);
        lenient().when(userDao.getCenterGroupByAgencyUser(userId)).thenReturn(centerGroupModels);
        lenient().when(userMetaDao.getMetas("ACADEMY_OPEN_FLAG", userId)).thenReturn(metas);
        lenient().when(userProvider.getAgencyByUserId(userId)).thenReturn(userAgency);
        lenient().when(agencyDao.getAllMetaData(agencyId)).thenReturn(allMetaData);
        lenient().when(agencyDao.isBatchAddOpen(agencyId, "BOOKLEARNINGMEDIA_BATCHCREATE")).thenReturn(false);
        lenient().when(cacheService.exist(key)).thenReturn(false);
        lenient().when(healthCheckFormDao.getFormsByAgencyAndType(agencyId, "CHILD_HEALTH")).thenReturn(new ArrayList<>());
        lenient().when(healthCheckFormDao.getFormsByAgencyAndType(agencyId, "HEALTH_CHECK_QUESTION")).thenReturn(new ArrayList<>());
        lenient().when(groupDao.getGroupIdsByAgencyAdminId(userId)).thenReturn(groupIds);
        lenient().when(agencyDao.getMeta(agencyId, "HEALTH_CARD_OPEN_GROUP")).thenReturn(null);
        lenient().when(userProvider.getOpenValueDefaultOpen(userId, "HEALTH_CHECK_NOTIFICATION")).thenReturn(false);
        lenient().when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        lenient().when(userProvider.getOpenValueDefaultClose(userId, "REDUCE_PERIOD")).thenReturn(false);
        lenient().when(metaDao.getAppMeta("CHAT_MANUAL_NOTIFY")).thenReturn("true");
        lenient().when(metaDao.getAppMeta("CHAT_REMOTE_DEBUG")).thenReturn("true");
        lenient().when(userMetaDao.getMetasByUserId(Arrays.asList(featureOpenKeys), userId)).thenReturn(allMetaKeys);
        lenient().when(formsFormDao.selectByAgencyIdAndType(agency.getId(), "HEALTH_CHECK_QUESTION")).thenReturn(formEntity);
        lenient().when(formsFormDao.selectByAgencyIdAndType(agency.getId(), "CHILD_HEALTH")).thenReturn(null);
        lenient().when(userMetaDao.getMeta(userId, "UNREAD_MESSAGE_EMAIL_OPEN")).thenReturn(null);

        // 接口模拟
        NotifyResponse eventNotify = eventService.getEventNotify(userId, "");

        // 断言
        Assert.assertEquals(false, eventNotify.isUnreadMessageEmailOpen());
    }

    /**
     * 测试获取未读消息邮件通知开关是否打开
     * case: 未读消息邮件通知功能关闭
     */
    @Test
    public void testGetEventNotify2() {
        // 准备数据
        String userId = "userId001";
        String agencyId = "agencyId001";
        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole("AGENCY_OWNER");
        Date to = TimeUtil.addMinutes(TimeUtil.getUtcNow(), 10);
        List<EventUserEntity> eventUserEntities = new ArrayList<>();
        EventUserEntity eventUserEntity = new EventUserEntity();
        eventUserEntity.setIsRead(false);
        eventUserEntity.setUserId(userId);
        eventUserEntities.add(eventUserEntity);
        List<UsersMetaDataEntity> metas = new ArrayList<>();
        AgencyModel userAgency = new AgencyModel();
        userAgency.setId(agencyId);
        List<AgencyMetaDataEntity> allMetaData = new ArrayList<>();
        AgencyMetaDataEntity agencyMetaDataEntity = new AgencyMetaDataEntity();
        agencyMetaDataEntity.setAgencyId(agencyId);
        agencyMetaDataEntity.setMetaKey("EVENT_OPEN");
        agencyMetaDataEntity.setMetaValue("1");
        allMetaData.add(agencyMetaDataEntity);
        List<String> groupIds = new ArrayList<>();
        groupIds.add("groupId001");
        AgencyModel agency = new AgencyModel();
        agency.setId(agencyId);
        // 用户引导使用的 metaKey
        String[] featureOpenKeys = new String[] {
                "UNREAD_MESSAGE_EMAIL_OPEN"
        };
        FormsFormEntity formEntity = new FormsFormEntity();
        formEntity.setAgencyId(agencyId);
        formEntity.setFitBatch(false);
        List<CenterGroupModel> centerGroupModels = new ArrayList<>();
        CenterGroupModel centerGroupModel = new CenterGroupModel();
        centerGroupModel.setCenterId("centerId001");
        centerGroupModel.setGroupId("group001");
        centerGroupModels.add(centerGroupModel);
        String key = "USER_ACTIVE_TIME" + userId.toUpperCase();

        // 数据模拟
        lenient().when(userProvider.checkUser(userId)).thenReturn(userEntity);
        lenient().when(eventDao.queryEventUserByUserIdAndToTime(userId, to)).thenReturn(eventUserEntities);
        lenient().when(userDao.getCenterGroupByAgencyUser(userId)).thenReturn(centerGroupModels);
        lenient().when(userMetaDao.getMetas("ACADEMY_OPEN_FLAG", userId)).thenReturn(metas);
        lenient().when(userProvider.getAgencyByUserId(userId)).thenReturn(userAgency);
        lenient().when(agencyDao.getAllMetaData(agencyId)).thenReturn(allMetaData);
        lenient().when(agencyDao.isBatchAddOpen(agencyId, "BOOKLEARNINGMEDIA_BATCHCREATE")).thenReturn(false);
        lenient().when(cacheService.exist(key)).thenReturn(false);
        lenient().when(healthCheckFormDao.getFormsByAgencyAndType(agencyId, "CHILD_HEALTH")).thenReturn(new ArrayList<>());
        lenient().when(healthCheckFormDao.getFormsByAgencyAndType(agencyId, "HEALTH_CHECK_QUESTION")).thenReturn(new ArrayList<>());
        lenient().when(groupDao.getGroupIdsByAgencyAdminId(userId)).thenReturn(groupIds);
        lenient().when(agencyDao.getMeta(agencyId, "HEALTH_CARD_OPEN_GROUP")).thenReturn(null);
        lenient().when(userProvider.getOpenValueDefaultOpen(userId, "HEALTH_CHECK_NOTIFICATION")).thenReturn(false);
        lenient().when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        lenient().when(userProvider.getOpenValueDefaultClose(userId, "REDUCE_PERIOD")).thenReturn(false);
        lenient().when(metaDao.getAppMeta("CHAT_MANUAL_NOTIFY")).thenReturn("true");
        lenient().when(metaDao.getAppMeta("CHAT_REMOTE_DEBUG")).thenReturn("true");
        lenient().when(userMetaDao.getMetasByUserId(Arrays.asList(featureOpenKeys), userId)).thenReturn(new ArrayList<>());
        lenient().when(formsFormDao.selectByAgencyIdAndType(agency.getId(), "HEALTH_CHECK_QUESTION")).thenReturn(formEntity);
        lenient().when(formsFormDao.selectByAgencyIdAndType(agency.getId(), "CHILD_HEALTH")).thenReturn(null);
        lenient().when(userMetaDao.getMeta(userId, "UNREAD_MESSAGE_EMAIL_OPEN")).thenReturn(null);

        // 接口模拟
        NotifyResponse eventNotify = eventService.getEventNotify(userId, "");

        // 断言
        Assert.assertEquals(false, eventNotify.isUnreadMessageEmailOpen());
    }

    /**
     * 测试获取 Drop-off Note 功能开关是否打开
     * Case: Drop-off Note 功能默认打开
     */
    @Test
    public void testGetEventNotify3() {
        // 准备数据
        String userId = "userId001"; // 模拟用户 ID
        String agencyId = "agencyId001"; // 模拟机构 ID
        // 模拟用户实体
        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole("AGENCY_OWNER");
        Date to = TimeUtil.addMinutes(TimeUtil.getUtcNow(), 10);
        List<EventUserEntity> eventUserEntities = new ArrayList<>();
        // 模拟 event 实体
        EventUserEntity eventUserEntity = new EventUserEntity();
        eventUserEntity.setIsRead(false);
        eventUserEntity.setUserId(userId);
        eventUserEntities.add(eventUserEntity);
        List<UsersMetaDataEntity> metas = new ArrayList<>();
        // 模拟机构实体
        AgencyModel userAgency = new AgencyModel();
        userAgency.setId(agencyId);
        List<AgencyMetaDataEntity> allMetaData = new ArrayList<>();
        // 模拟机构 event 功能打开 Metadata 实体
        AgencyMetaDataEntity agencyMetaDataEntity = new AgencyMetaDataEntity();
        agencyMetaDataEntity.setAgencyId(agencyId);
        agencyMetaDataEntity.setMetaKey("EVENT_OPEN");
        agencyMetaDataEntity.setMetaValue("1");
        allMetaData.add(agencyMetaDataEntity);
        List<String> groupIds = new ArrayList<>();
        groupIds.add("groupId001");
        AgencyModel agency = new AgencyModel(); // 模拟机构 Model
        agency.setId(agencyId);
        // 用户引导使用的 metaKey
        String[] featureOpenKeys = new String[] {};
        // 模拟健康卡问卷实体
        FormsFormEntity formEntity = new FormsFormEntity();
        formEntity.setAgencyId(agencyId);
        formEntity.setFitBatch(false);
        List<CenterGroupModel> centerGroupModels = new ArrayList<>();
        // 模拟 center group 实体
        CenterGroupModel centerGroupModel = new CenterGroupModel();
        centerGroupModel.setCenterId("centerId001");
        centerGroupModel.setGroupId("group001");
        centerGroupModels.add(centerGroupModel);
        // 模拟用户活跃缓存 Key
        String key = "USER_ACTIVE_TIME" + userId.toUpperCase();
        lenient().when(userProvider.checkUser(userId)).thenReturn(userEntity); // 模拟获取用户实体方法
        lenient().when(eventDao.queryEventUserByUserIdAndToTime(userId, to)).thenReturn(eventUserEntities); // 模拟获取 event 实体方法
        lenient().when(userDao.getCenterGroupByAgencyUser(userId)).thenReturn(centerGroupModels); // 模拟获取 center group 实体方法
        lenient().when(userMetaDao.getMetas("ACADEMY_OPEN_FLAG", userId)).thenReturn(metas); // 模拟获取用户 meta 方法
        lenient().when(userProvider.getAgencyByUserId(userId)).thenReturn(userAgency); // 模拟获取机构实体方法
        lenient().when(agencyDao.getAllMetaData(agencyId)).thenReturn(allMetaData); // 模拟获取机构 Metadata 方法
        lenient().when(agencyDao.isBatchAddOpen(agencyId, "BOOKLEARNINGMEDIA_BATCHCREATE")).thenReturn(false); // 模拟获取机构批量添加开关方法
        lenient().when(cacheService.exist(key)).thenReturn(false); // 模拟获取用户活跃缓存 Key 方法
        lenient().when(healthCheckFormDao.getFormsByAgencyAndType(agencyId, "CHILD_HEALTH")).thenReturn(new ArrayList<>()); // 模拟获取健康卡问卷实体方法
        lenient().when(healthCheckFormDao.getFormsByAgencyAndType(agencyId, "HEALTH_CHECK_QUESTION")).thenReturn(new ArrayList<>()); // 模拟获取健康卡问卷实体方法
        lenient().when(groupDao.getGroupIdsByAgencyAdminId(userId)).thenReturn(groupIds); // 模拟获取 center group 实体方法
        lenient().when(agencyDao.getMeta(agencyId, "HEALTH_CARD_OPEN_GROUP")).thenReturn(null); // 模拟获取机构 Metadata 方法
        lenient().when(userProvider.getOpenValueDefaultOpen(userId, "HEALTH_CHECK_NOTIFICATION")).thenReturn(false); // 模拟获取机构 Metadata 方法
        lenient().when(userProvider.getAgencyByUserId(userId)).thenReturn(agency); // 模拟获取机构实体方法
        lenient().when(userProvider.getOpenValueDefaultClose(userId, "REDUCE_PERIOD")).thenReturn(false); // 模拟获取机构 Metadata 方法
        lenient().when(metaDao.getAppMeta("CHAT_MANUAL_NOTIFY")).thenReturn("true");
        lenient().when(metaDao.getAppMeta("CHAT_REMOTE_DEBUG")).thenReturn("true");
        lenient().when(userMetaDao.getMetasByUserId(Arrays.asList(featureOpenKeys), userId)).thenReturn(new ArrayList<>()); // 模拟获取用户 Metadata 方法
        lenient().when(formsFormDao.selectByAgencyIdAndType(agency.getId(), "HEALTH_CHECK_QUESTION")).thenReturn(formEntity); // 模拟获取健康卡问卷实体方法
        lenient().when(formsFormDao.selectByAgencyIdAndType(agency.getId(), "CHILD_HEALTH")).thenReturn(null); // 模拟获取健康卡问卷实体方法
        lenient().when(userMetaDao.getMeta(userId, "UNREAD_MESSAGE_EMAIL_OPEN")).thenReturn(null); // 模拟获取用户 Metadata 方法

        // 接口模拟
        NotifyResponse eventNotify = eventService.getEventNotify(userId, "");

        // 断言 Drop-off Note 功能应为打开状态
        Assert.assertTrue(eventNotify.isDropOffNoteOpen());
    }

    /**
     * 测试获取 Drop-off Note 功能开关是否打开
     * Case: Drop-off Note 功能关闭
     */
    @Test
    public void testGetEventNotify4() {
        // 准备数据
        String userId = "userId001"; // 模拟用户 ID
        String agencyId = "agencyId001"; // 模拟机构 ID
        // 模拟用户实体
        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole("AGENCY_OWNER");
        Date to = TimeUtil.addMinutes(TimeUtil.getUtcNow(), 10);
        List<EventUserEntity> eventUserEntities = new ArrayList<>();
        // 模拟 event 实体
        EventUserEntity eventUserEntity = new EventUserEntity();
        eventUserEntity.setIsRead(false);
        eventUserEntity.setUserId(userId);
        eventUserEntities.add(eventUserEntity);
        List<UsersMetaDataEntity> metas = new ArrayList<>();
        // 模拟机构实体
        AgencyModel userAgency = new AgencyModel();
        userAgency.setId(agencyId);
        List<AgencyMetaDataEntity> allMetaData = new ArrayList<>();
        // 模拟机构 event 功能打开 Metadata 实体
        AgencyMetaDataEntity agencyMetaDataEntity = new AgencyMetaDataEntity();
        agencyMetaDataEntity.setAgencyId(agencyId);
        agencyMetaDataEntity.setMetaKey("EVENT_OPEN");
        agencyMetaDataEntity.setMetaValue("1");
        allMetaData.add(agencyMetaDataEntity);
        // Drop-off Note 功能关闭
        AgencyMetaDataEntity agencyMetaDataEntity1 = new AgencyMetaDataEntity();
        agencyMetaDataEntity1.setAgencyId(agencyId);
        agencyMetaDataEntity1.setMetaKey("DROP_OFF_NOTE_OPEN");
        agencyMetaDataEntity1.setMetaValue("0");
        allMetaData.add(agencyMetaDataEntity1);
        List<String> groupIds = new ArrayList<>();
        groupIds.add("groupId001");
        AgencyModel agency = new AgencyModel(); // 模拟机构 Model
        agency.setId(agencyId);
        // 用户引导使用的 metaKey
        String[] featureOpenKeys = new String[] {};
        // 模拟健康卡问卷实体
        FormsFormEntity formEntity = new FormsFormEntity();
        formEntity.setAgencyId(agencyId);
        formEntity.setFitBatch(false);
        List<CenterGroupModel> centerGroupModels = new ArrayList<>();
        // 模拟学校班级 Model
        CenterGroupModel centerGroupModel = new CenterGroupModel();
        centerGroupModel.setCenterId("centerId001");
        centerGroupModel.setGroupId("group001");
        centerGroupModels.add(centerGroupModel);
        // 模拟用户活跃缓存 Key
        String key = "USER_ACTIVE_TIME" + userId.toUpperCase();

        // 数据模拟
        lenient().when(userProvider.checkUser(userId)).thenReturn(userEntity); // 模拟获取用户实体方法
        lenient().when(eventDao.queryEventUserByUserIdAndToTime(userId, to)).thenReturn(eventUserEntities); // 模拟获取 event 实体方法
        lenient().when(userDao.getCenterGroupByAgencyUser(userId)).thenReturn(centerGroupModels); // 模拟获取 center group 实体方法
        lenient().when(userMetaDao.getMetas("ACADEMY_OPEN_FLAG", userId)).thenReturn(metas); // 模拟获取用户 meta 方法
        lenient().when(userProvider.getAgencyByUserId(userId)).thenReturn(userAgency); // 模拟获取机构实体方法
        lenient().when(agencyDao.getAllMetaData(agencyId)).thenReturn(allMetaData); // 模拟获取机构 Metadata 方法
        lenient().when(agencyDao.isBatchAddOpen(agencyId, "BOOKLEARNINGMEDIA_BATCHCREATE")).thenReturn(false); // 模拟获取机构批量添加开关方法
        lenient().when(cacheService.exist(key)).thenReturn(false); // 模拟获取用户活跃缓存 Key 方法
        lenient().when(healthCheckFormDao.getFormsByAgencyAndType(agencyId, "CHILD_HEALTH")).thenReturn(new ArrayList<>()); // 模拟获取健康卡问卷实体方法
        lenient().when(healthCheckFormDao.getFormsByAgencyAndType(agencyId, "HEALTH_CHECK_QUESTION")).thenReturn(new ArrayList<>()); // 模拟获取健康卡问卷实体方法
        lenient().when(groupDao.getGroupIdsByAgencyAdminId(userId)).thenReturn(groupIds); // 模拟获取 center group 实体方法
        lenient().when(agencyDao.getMeta(agencyId, "HEALTH_CARD_OPEN_GROUP")).thenReturn(null); // 模拟获取机构 Metadata 方法
        lenient().when(userProvider.getOpenValueDefaultOpen(userId, "HEALTH_CHECK_NOTIFICATION")).thenReturn(false); // 模拟获取机构 Metadata 方法
        lenient().when(userProvider.getAgencyByUserId(userId)).thenReturn(agency); // 模拟获取机构实体方法
        lenient().when(userProvider.getOpenValueDefaultClose(userId, "REDUCE_PERIOD")).thenReturn(false); // 模拟获取机构 Metadata 方法
        lenient().when(metaDao.getAppMeta("CHAT_MANUAL_NOTIFY")).thenReturn("true");
        lenient().when(metaDao.getAppMeta("CHAT_REMOTE_DEBUG")).thenReturn("true");
        lenient().when(userMetaDao.getMetasByUserId(Arrays.asList(featureOpenKeys), userId)).thenReturn(new ArrayList<>()); // 模拟获取用户 Metadata 方法
        lenient().when(formsFormDao.selectByAgencyIdAndType(agency.getId(), "HEALTH_CHECK_QUESTION")).thenReturn(formEntity); // 模拟获取健康卡问卷实体方法
        lenient().when(formsFormDao.selectByAgencyIdAndType(agency.getId(), "CHILD_HEALTH")).thenReturn(null); // 模拟获取健康卡问卷实体方法
        lenient().when(userMetaDao.getMeta(userId, "UNREAD_MESSAGE_EMAIL_OPEN")).thenReturn(null); // 模拟获取用户 Metadata 方法

        // 接口模拟
        NotifyResponse eventNotify = eventService.getEventNotify(userId, "");

        // 断言 Drop-off Note 功能应为关闭状态
        Assert.assertFalse(eventNotify.isDropOffNoteOpen());
    }

    /**
     * 测试获取培训课程功能开关是否打开
     * Case: 培训课程功能功能打开
     */
    @Test
    public void testGetEventNotify5() {
        // 准备数据
        String userId = "userId001"; // 模拟用户 ID
        String agencyId = "agencyId001"; // 模拟机构 ID
        // 模拟用户实体
        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole("AGENCY_OWNER");
        Date to = TimeUtil.addMinutes(TimeUtil.getUtcNow(), 10);
        List<EventUserEntity> eventUserEntities = new ArrayList<>();
        // 模拟 event 实体
        EventUserEntity eventUserEntity = new EventUserEntity();
        eventUserEntity.setIsRead(false);
        eventUserEntity.setUserId(userId);
        eventUserEntities.add(eventUserEntity);
        List<UsersMetaDataEntity> metas = new ArrayList<>();
        // 模拟机构实体
        AgencyModel userAgency = new AgencyModel();
        userAgency.setId(agencyId);
        List<AgencyMetaDataEntity> allMetaData = new ArrayList<>();
        // 模拟机构 event 功能打开 Metadata 实体
        AgencyMetaDataEntity agencyMetaDataEntity = new AgencyMetaDataEntity();
        agencyMetaDataEntity.setAgencyId(agencyId);
        agencyMetaDataEntity.setMetaKey("EVENT_OPEN");
        agencyMetaDataEntity.setMetaValue("1");
        allMetaData.add(agencyMetaDataEntity);
        // FE 模块培训视频打开
        AgencyMetaDataEntity agencyMetaDataEntity1 = new AgencyMetaDataEntity();
        agencyMetaDataEntity1.setAgencyId(agencyId);
        agencyMetaDataEntity1.setMetaKey("TRAINING_MODULE_FE_BASIC");
        agencyMetaDataEntity1.setMetaValue("1");
        allMetaData.add(agencyMetaDataEntity1);
        List<String> groupIds = new ArrayList<>();
        groupIds.add("groupId001");
        AgencyModel agency = new AgencyModel(); // 模拟机构 Model
        agency.setId(agencyId);
        // 用户引导使用的 metaKey
        String[] featureOpenKeys = new String[] {};
        // 模拟健康卡问卷实体
        FormsFormEntity formEntity = new FormsFormEntity();
        formEntity.setAgencyId(agencyId);
        formEntity.setFitBatch(false);
        List<CenterGroupModel> centerGroupModels = new ArrayList<>();
        // 模拟学校班级 Model
        CenterGroupModel centerGroupModel = new CenterGroupModel();
        centerGroupModel.setCenterId("centerId001");
        centerGroupModel.setGroupId("group001");
        centerGroupModels.add(centerGroupModel);
        // 模拟用户活跃缓存 Key
        String key = "USER_ACTIVE_TIME" + userId.toUpperCase();
        // 模拟用户开通的

        // 数据模拟
        lenient().when(userProvider.checkUser(userId)).thenReturn(userEntity); // 模拟获取用户实体方法
        lenient().when(eventDao.queryEventUserByUserIdAndToTime(userId, to)).thenReturn(eventUserEntities); // 模拟获取 event 实体方法
        lenient().when(userDao.getCenterGroupByAgencyUser(userId)).thenReturn(centerGroupModels); // 模拟获取 center group 实体方法
        lenient().when(userMetaDao.getMetas("ACADEMY_OPEN_FLAG", userId)).thenReturn(metas); // 模拟获取用户 meta 方法
        lenient().when(userProvider.getAgencyByUserId(userId)).thenReturn(userAgency); // 模拟获取机构实体方法
        lenient().when(agencyDao.getAllMetaData(agencyId)).thenReturn(allMetaData); // 模拟获取机构 Metadata 方法
        lenient().when(agencyDao.isBatchAddOpen(agencyId, "BOOKLEARNINGMEDIA_BATCHCREATE")).thenReturn(false); // 模拟获取机构批量添加开关方法
        lenient().when(cacheService.exist(key)).thenReturn(false); // 模拟获取用户活跃缓存 Key 方法
        lenient().when(healthCheckFormDao.getFormsByAgencyAndType(agencyId, "CHILD_HEALTH")).thenReturn(new ArrayList<>()); // 模拟获取健康卡问卷实体方法
        lenient().when(healthCheckFormDao.getFormsByAgencyAndType(agencyId, "HEALTH_CHECK_QUESTION")).thenReturn(new ArrayList<>()); // 模拟获取健康卡问卷实体方法
        lenient().when(groupDao.getGroupIdsByAgencyAdminId(userId)).thenReturn(groupIds); // 模拟获取 center group 实体方法
        lenient().when(agencyDao.getMeta(agencyId, "HEALTH_CARD_OPEN_GROUP")).thenReturn(null); // 模拟获取机构 Metadata 方法
        lenient().when(userProvider.getOpenValueDefaultOpen(userId, "HEALTH_CHECK_NOTIFICATION")).thenReturn(false); // 模拟获取机构 Metadata 方法
        lenient().when(userProvider.getAgencyByUserId(userId)).thenReturn(agency); // 模拟获取机构实体方法
        lenient().when(userProvider.getOpenValueDefaultClose(userId, "REDUCE_PERIOD")).thenReturn(false); // 模拟获取机构 Metadata 方法
        lenient().when(metaDao.getAppMeta("CHAT_MANUAL_NOTIFY")).thenReturn("true");
        lenient().when(metaDao.getAppMeta("CHAT_REMOTE_DEBUG")).thenReturn("true");
        lenient().when(userMetaDao.getMetasByUserId(Arrays.asList(featureOpenKeys), userId)).thenReturn(new ArrayList<>()); // 模拟获取用户 Metadata 方法
        lenient().when(formsFormDao.selectByAgencyIdAndType(agency.getId(), "HEALTH_CHECK_QUESTION")).thenReturn(formEntity); // 模拟获取健康卡问卷实体方法
        lenient().when(formsFormDao.selectByAgencyIdAndType(agency.getId(), "CHILD_HEALTH")).thenReturn(null); // 模拟获取健康卡问卷实体方法
        lenient().when(userMetaDao.getMeta(userId, "UNREAD_MESSAGE_EMAIL_OPEN")).thenReturn(null); // 模拟获取用户 Metadata 方法

        // 接口模拟
        NotifyResponse eventNotify = eventService.getEventNotify(userId, "");

        // 断言培训课程功能开关应为开启状态
        Assert.assertTrue(eventNotify.isTrainingCertificateOpen());
    }

    /**
     * 测试获取老师创建单元计划功能开关打开情况
     */
    @Test
    public void testGetEventNotify6() {
        // 准备数据
        String userId = "userId001";
        String agencyId = "agencyId001";
        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole("AGENCY_OWNER");
        Date to = TimeUtil.addMinutes(TimeUtil.getUtcNow(), 10);
        List<EventUserEntity> eventUserEntities = new ArrayList<>();
        EventUserEntity eventUserEntity = new EventUserEntity();
        eventUserEntity.setIsRead(false);
        eventUserEntity.setUserId(userId);
        eventUserEntities.add(eventUserEntity);
        List<UsersMetaDataEntity> metas = new ArrayList<>();
        AgencyModel userAgency = new AgencyModel();
        userAgency.setId(agencyId);
        List<AgencyMetaDataEntity> allMetaData = new ArrayList<>();
        AgencyMetaDataEntity agencyMetaDataEntity = new AgencyMetaDataEntity();
        agencyMetaDataEntity.setAgencyId(agencyId);
        agencyMetaDataEntity.setMetaKey("EVENT_OPEN");
        agencyMetaDataEntity.setMetaValue("1");
        allMetaData.add(agencyMetaDataEntity);
        List<String> groupIds = new ArrayList<>();
        groupIds.add("groupId001");
        AgencyModel agency = new AgencyModel();
        agency.setId(agencyId);
        // 用户引导使用的 metaKey
        String[] featureOpenKeys = new String[] {
                "UNREAD_MESSAGE_EMAIL_OPEN",
                "PERMISSION_LESSONS_WITHOUT_APPROVAL",
                "CREATE_UNIT_PLANNER_OPEN"
        };
        List<UsersMetaDataEntity> allMetaKeys = new ArrayList<>();
        UsersMetaDataEntity userMetaDataEntity = new UsersMetaDataEntity();
        userMetaDataEntity.setUserId(userId);
        userMetaDataEntity.setMetaKey("CREATE_UNIT_PLANNER_OPEN");
        userMetaDataEntity.setMetaValue("true");
        allMetaKeys.add(userMetaDataEntity);
        FormsFormEntity formEntity = new FormsFormEntity();
        formEntity.setAgencyId(agencyId);
        formEntity.setFitBatch(false);
        List<CenterGroupModel> centerGroupModels = new ArrayList<>();
        CenterGroupModel centerGroupModel = new CenterGroupModel();
        centerGroupModel.setCenterId("centerId001");
        centerGroupModel.setGroupId("group001");
        centerGroupModels.add(centerGroupModel);
        String key = "USER_ACTIVE_TIME" + userId.toUpperCase();

        // 数据模拟
        lenient().when(userProvider.checkUser(userId)).thenReturn(userEntity);
        lenient().when(eventDao.queryEventUserByUserIdAndToTime(userId, to)).thenReturn(eventUserEntities);
        lenient().when(userDao.getCenterGroupByAgencyUser(userId)).thenReturn(centerGroupModels);
        lenient().when(userMetaDao.getMetas("ACADEMY_OPEN_FLAG", userId)).thenReturn(metas);
        lenient().when(userProvider.getAgencyByUserId(userId)).thenReturn(userAgency);
        lenient().when(agencyDao.getAllMetaData(agencyId)).thenReturn(allMetaData);
        lenient().when(agencyDao.isBatchAddOpen(agencyId, "BOOKLEARNINGMEDIA_BATCHCREATE")).thenReturn(false);
        lenient().when(cacheService.exist(key)).thenReturn(false);
        lenient().when(healthCheckFormDao.getFormsByAgencyAndType(agencyId, "CHILD_HEALTH")).thenReturn(new ArrayList<>());
        lenient().when(healthCheckFormDao.getFormsByAgencyAndType(agencyId, "HEALTH_CHECK_QUESTION")).thenReturn(new ArrayList<>());
        lenient().when(groupDao.getGroupIdsByAgencyAdminId(userId)).thenReturn(groupIds);
        lenient().when(agencyDao.getMeta(agencyId, "HEALTH_CARD_OPEN_GROUP")).thenReturn(null);
        lenient().when(userProvider.getOpenValueDefaultOpen(userId, "HEALTH_CHECK_NOTIFICATION")).thenReturn(false);
        lenient().when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        lenient().when(userProvider.getOpenValueDefaultClose(userId, "REDUCE_PERIOD")).thenReturn(false);
        lenient().when(metaDao.getAppMeta("CHAT_MANUAL_NOTIFY")).thenReturn("true");
        lenient().when(metaDao.getAppMeta("CHAT_REMOTE_DEBUG")).thenReturn("true");
        lenient().when(userMetaDao.getMetasByUserId(Arrays.asList(featureOpenKeys), userId)).thenReturn(allMetaKeys);
        lenient().when(formsFormDao.selectByAgencyIdAndType(agency.getId(), "HEALTH_CHECK_QUESTION")).thenReturn(formEntity);
        lenient().when(formsFormDao.selectByAgencyIdAndType(agency.getId(), "CHILD_HEALTH")).thenReturn(null);
        lenient().when(userMetaDao.getMeta(userId, "UNREAD_MESSAGE_EMAIL_OPEN")).thenReturn(null);

        // 接口模拟
        NotifyResponse eventNotify = eventService.getEventNotify(userId, "");

        // 断言
        Assert.assertTrue(eventNotify.isCreateUnitPlannerOpen());
    }

    /**
     * 测试获取老师创建单元计划功能关闭打开情况
     */
    @Test
    public void testGetEventNotify7() {
        // 准备数据
        String userId = "userId001";
        String agencyId = "agencyId001";
        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole("AGENCY_OWNER");
        Date to = TimeUtil.addMinutes(TimeUtil.getUtcNow(), 10);
        List<EventUserEntity> eventUserEntities = new ArrayList<>();
        EventUserEntity eventUserEntity = new EventUserEntity();
        eventUserEntity.setIsRead(false);
        eventUserEntity.setUserId(userId);
        eventUserEntities.add(eventUserEntity);
        List<UsersMetaDataEntity> metas = new ArrayList<>();
        AgencyModel userAgency = new AgencyModel();
        userAgency.setId(agencyId);
        List<AgencyMetaDataEntity> allMetaData = new ArrayList<>();
        AgencyMetaDataEntity agencyMetaDataEntity = new AgencyMetaDataEntity();
        agencyMetaDataEntity.setAgencyId(agencyId);
        agencyMetaDataEntity.setMetaKey("EVENT_OPEN");
        agencyMetaDataEntity.setMetaValue("1");
        allMetaData.add(agencyMetaDataEntity);
        List<String> groupIds = new ArrayList<>();
        groupIds.add("groupId001");
        AgencyModel agency = new AgencyModel();
        agency.setId(agencyId);
        // 用户引导使用的 metaKey
        String[] featureOpenKeys = new String[] {
                "UNREAD_MESSAGE_EMAIL_OPEN",
                "PERMISSION_LESSONS_WITHOUT_APPROVAL",
                "CREATE_UNIT_PLANNER_OPEN"
        };
        List<UsersMetaDataEntity> allMetaKeys = new ArrayList<>();
        UsersMetaDataEntity userMetaDataEntity = new UsersMetaDataEntity();
        userMetaDataEntity.setUserId(userId);
        userMetaDataEntity.setMetaKey("CREATE_UNIT_PLANNER_OPEN");
        userMetaDataEntity.setMetaValue("false");
        allMetaKeys.add(userMetaDataEntity);
        FormsFormEntity formEntity = new FormsFormEntity();
        formEntity.setAgencyId(agencyId);
        formEntity.setFitBatch(false);
        List<CenterGroupModel> centerGroupModels = new ArrayList<>();
        CenterGroupModel centerGroupModel = new CenterGroupModel();
        centerGroupModel.setCenterId("centerId001");
        centerGroupModel.setGroupId("group001");
        centerGroupModels.add(centerGroupModel);
        String key = "USER_ACTIVE_TIME" + userId.toUpperCase();

        // 数据模拟
        lenient().when(userProvider.checkUser(userId)).thenReturn(userEntity);
        lenient().when(eventDao.queryEventUserByUserIdAndToTime(userId, to)).thenReturn(eventUserEntities);
        lenient().when(userDao.getCenterGroupByAgencyUser(userId)).thenReturn(centerGroupModels);
        lenient().when(userMetaDao.getMetas("ACADEMY_OPEN_FLAG", userId)).thenReturn(metas);
        lenient().when(userProvider.getAgencyByUserId(userId)).thenReturn(userAgency);
        lenient().when(agencyDao.getAllMetaData(agencyId)).thenReturn(allMetaData);
        lenient().when(agencyDao.isBatchAddOpen(agencyId, "BOOKLEARNINGMEDIA_BATCHCREATE")).thenReturn(false);
        lenient().when(cacheService.exist(key)).thenReturn(false);
        lenient().when(healthCheckFormDao.getFormsByAgencyAndType(agencyId, "CHILD_HEALTH")).thenReturn(new ArrayList<>());
        lenient().when(healthCheckFormDao.getFormsByAgencyAndType(agencyId, "HEALTH_CHECK_QUESTION")).thenReturn(new ArrayList<>());
        lenient().when(groupDao.getGroupIdsByAgencyAdminId(userId)).thenReturn(groupIds);
        lenient().when(agencyDao.getMeta(agencyId, "HEALTH_CARD_OPEN_GROUP")).thenReturn(null);
        lenient().when(userProvider.getOpenValueDefaultOpen(userId, "HEALTH_CHECK_NOTIFICATION")).thenReturn(false);
        lenient().when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        lenient().when(userProvider.getOpenValueDefaultClose(userId, "REDUCE_PERIOD")).thenReturn(false);
        lenient().when(metaDao.getAppMeta("CHAT_MANUAL_NOTIFY")).thenReturn("true");
        lenient().when(metaDao.getAppMeta("CHAT_REMOTE_DEBUG")).thenReturn("true");
        lenient().when(userMetaDao.getMetasByUserId(Arrays.asList(featureOpenKeys), userId)).thenReturn(allMetaKeys);
        lenient().when(formsFormDao.selectByAgencyIdAndType(agency.getId(), "HEALTH_CHECK_QUESTION")).thenReturn(formEntity);
        lenient().when(formsFormDao.selectByAgencyIdAndType(agency.getId(), "CHILD_HEALTH")).thenReturn(null);
        lenient().when(userMetaDao.getMeta(userId, "UNREAD_MESSAGE_EMAIL_OPEN")).thenReturn(null);

        // 接口模拟
        NotifyResponse eventNotify = eventService.getEventNotify(userId, "");

        // 断言
        Assert.assertTrue(eventNotify.isCreateUnitPlannerOpen());
    }

    @Test
    public void testGenerateQRCodePdf() {
        // 测试数据准备
        String eventId = "testEventId"; // 事件 ID
        String userId = "testUserId"; // 用户 ID
        String currentTime = "testCurrentTime"; // 当前时间
        boolean isSendEmail = false; // 是否发送邮件

        // 创建一个事件实体并设置其属性
        EventEntity eventEntity = new EventEntity();
        eventEntity.setId(eventId);
        eventEntity.setQRCode("testQRCode");
        eventEntity.setName("testEventName");
        eventEntity.setFromAtLocal(new Date());
        eventEntity.setToAtLocal(new Date());
        eventEntity.setLocation("testLocation");

        // 当调用 eventRepository 的 findOne 方法时，返回上面创建的事件实体
        when(eventRepository.findById(eventId)).thenReturn(Optional.of(eventEntity));
        // 当调用 fileSystem 的 upload 方法时，返回空字符串
        when(fileSystem.upload(anyString(), anyString(), any(File.class))).thenReturn("");
        // 当调用 fileSystem 的 getPublicUrl 方法时，返回 "testPublicUrl"
        when(fileSystem.getPublicUrl(anyString(), anyString())).thenReturn("testPublicUrl");
        // 当调用 userProvider 的 getCurrentUserId 方法时，返回测试用户 ID
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        // 当调用 remoteProvider 的 callPdfService 方法时，返回一个状态码为 200 的 InvokeResult
        when(remoteProvider.callPdfService(anyString(), anyList())).thenReturn(CompletableFuture.completedFuture(new InvokeResult().withStatusCode(200)));

        // 创建一个 PdfConvertJobEntity 实体并设置其属性
        PdfConvertJobEntity pdfConvertJobEntity = new PdfConvertJobEntity();
        pdfConvertJobEntity.setId("jobId");
        pdfConvertJobEntity.setStatus(PdfConvertStatus.SUCCEED.toString());
        pdfConvertJobEntity.setPdfUrl("testPdfUrl");

        // 当调用 reportDao 的 getPdfJob 方法时，返回上面创建的 PdfConvertJobEntity 实体
        when(reportDao.getPdfJob(any())).thenReturn(pdfConvertJobEntity);

        // 使用反射设置 eventService 的字段值
        ReflectionTestUtils.setField(eventService, "pdfBucket", "testBucket");
        ReflectionTestUtils.setField(eventService, "s3Root", "testS3Root");
        ReflectionTestUtils.setField(eventService, "pdfEndpoint", "testPdfEndpoint");
        ReflectionTestUtils.setField(eventService, "apiUrl", "testApiUrl");

        // 调用被测试的方法
        DownFileResponse response = eventService.getQRCodePdf(eventId, userId, currentTime, isSendEmail);

        // 断言结果
        // 验证返回的URL是否包含 "testApiUrl/api/v1/medias/files/"
        Assert.assertTrue(response.getUrl().contains("testApiUrl/api/v1/medias/files/"));
    }

    @Test
    public void testReturnResults() {
        String eventId = "testEventId"; // 事件 ID
        String centerId = "testCenterId"; // 中心 ID
        String groupId = "testGroupId"; // 组 ID
        String userId = "testUserId"; // 用户 ID
        String currentTime = "2024-01-04 00:00:00.000"; // 当前时间

        // 创建一个事件实体并设置其属性
        EventEntity eventEntity = new EventEntity();
        eventEntity.setId(eventId); // 设置事件 ID
        eventEntity.setName("testEventName"); // 设置事件名称
        eventEntity.setFromAtLocal(new Date()); // 设置本地开始时间
        eventEntity.setFromAtUtc(new Date()); // 设置 UTC 开始时间
        eventEntity.setToAtLocal(new Date()); // 设置本地结束时间
        eventEntity.setToAtUtc(new Date()); // 设置 UTC 结束时间
        eventEntity.setLocation("testLocation"); // 设置位置
        eventEntity.setNotPublished(false); // 设置是否未发布
        eventEntity.setOpenRecurringEvent(false); // 设置是否开启重复事件
        eventEntity.setVotingOption("Yes"); // 设置投票选项
        eventEntity.setTeacherName("testTeacherName"); // 设置教师名称

        // 创建一个用户实体并设置其属性
        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId); // 设置用户 ID
        userEntity.setRole("AGENCY_OWNER"); // 设置用户角色

        // 创建一个中心组模型并设置其属性
        CenterGroupModel centerGroupModel = new CenterGroupModel();
        centerGroupModel.setCenterId(centerId); // 设置中心 ID
        centerGroupModel.setCenterName("testCenterName"); // 设置中心名称
        centerGroupModel.setGroupId(groupId); // 设置组 ID
        centerGroupModel.setGroupName("testGroupName"); // 设置组名称

        // 创建一个中心组模型列表并添加上面创建的中心组模型
        List<CenterGroupModel> centerGroupList = new ArrayList<>();
        centerGroupList.add(centerGroupModel);

        // 创建一个组 ID 列表并添加一个组 ID
        List<String> groupIds = new ArrayList<>();
        groupIds.add(groupId);

        // 创建一个注册 ID 列表并添加一个注册 ID
        List<String> enrollmentIds = new ArrayList<>();
        enrollmentIds.add("testEnrollmentId");

        // 创建一个事件统计模型并设置其属性
        EventStatisticsModel eventStatisticsModel = new EventStatisticsModel();
        eventStatisticsModel.setEventId(eventId); // 设置事件 ID
        eventStatisticsModel.setGroupId(groupId); // 设置组 ID
        eventStatisticsModel.setCenterId(centerId); // 设置中心 ID
        eventStatisticsModel.setEnrollmentId("testEnrollmentId"); // 设置注册 ID

        // 创建一个事件统计模型列表并添加上面创建的事件统计模型
        List<EventStatisticsModel> eventStatisticsModels = new ArrayList<>();
        eventStatisticsModels.add(eventStatisticsModel);

        // 创建一个 Pdf 转换任务实体并设置其属性
        PdfConvertJobEntity pdfConvertJobEntity = new PdfConvertJobEntity();
        pdfConvertJobEntity.setId("jobId"); // 设置任务 ID
        pdfConvertJobEntity.setStatus(PdfConvertStatus.SUCCEED.toString()); // 设置任务状态
        pdfConvertJobEntity.setPdfUrl("testPdfUrl"); // 设置 PDF URL

        // 创建一个媒体实体并设置其属性
        MediaEntity mediaEntity = new MediaEntity();
        mediaEntity.setId("mediaId"); // 设置媒体 ID
        mediaEntity.setRelativePath("testRelativePath"); // 设置相对路径
        mediaEntity.setCreateAtUtc(new Date()); // 设置 UTC 创建时间
        mediaEntity.setFileName("testFileName"); // 设置文件名

        String fileId = "testFileId"; // 文件 ID
        // 当调用 userDao 的 getCenterGroupByAgencyUser 方法时，返回上面创建的中心组模型列表
        when(userDao.getCenterGroupByAgencyUser(any())).thenReturn(centerGroupList);
        // 当调用 eventRepository 的 findOne 方法时，返回上面创建的事件实体
        when(eventRepository.findById(eventId)).thenReturn(Optional.of(eventEntity));
        // 当调用 userProvider 的 checkUser 方法时，返回上面创建的用户实体
        when(userProvider.checkUser(userId)).thenReturn(userEntity);
        // 当调用 studentDao 的 getChildrenByGroupIds 方法时，返回一个空列表
        when(studentDao.getChildrenByGroupIds(groupIds)).thenReturn(new ArrayList<>());
        // 当调用 eventDao 的 queryEventStatisticsByEventId 方法时，返回上面创建的事件统计模型列表
        when(eventDao.queryEventStatisticsByEventId(any(), any())).thenReturn(eventStatisticsModels);
        // 当调用 remoteProvider 的 callPdfService 方法时，返回一个状态码为 200 的 InvokeResult
        when(remoteProvider.callPdfService(anyString(), anyList())).thenReturn(CompletableFuture.completedFuture(new InvokeResult().withStatusCode(200)));
        // 当调用 reportDao 的 getPdfJob 方法时，返回上面创建的 Pdf 转换任务实体
        when(reportDao.getPdfJob(anyString())).thenReturn(pdfConvertJobEntity);
        // 当调用 fileSystem 的 getPublicUrl 方法时，返回 "testPublicUrl"
        when(fileSystem.getPublicUrl(anyString(), anyString())).thenReturn("testPublicUrl");
        // 当调用 fileSystem 的 getPublicUrl 方法时，返回 "testPublicUrl"
        when(fileSystem.getPublicUrl(anyString())).thenReturn("testPublicUrl");
        // 使用反射设置 eventService 的字段值
        ReflectionTestUtils.setField(eventService, "pdfBucket", "pdfBucket");
        ReflectionTestUtils.setField(eventService, "s3Root", "s3Root");
        ReflectionTestUtils.setField(eventService, "pdfEndpoint", "pdfEndpoint");
        ReflectionTestUtils.setField(eventService, "apiUrl", "testApiUrl");
        // 创建一个 AppMetadataEntity 实体并设置其属性
        AppMetadataEntity appMetadataEntity = new AppMetadataEntity();
        appMetadataEntity.setMetaKey("EMAIL_TEMPLATE_EVENT_RSVP_DOWNLOAD");
        appMetadataEntity.setMetaValue("testMetaValue");
        // 当调用 appMetadataRepository 的 findTop1ByMetaKey 方法时，返回上面创建的 AppMetadataEntity 实体
        when(appMetadataRepository.findTop1ByMetaKey("EMAIL_TEMPLATE_EVENT_RSVP_DOWNLOAD")).thenReturn(appMetadataEntity);

        // 调用被测试的方法
        ResultsResponse result = eventService.getResults(eventId, centerId, groupId, userId, currentTime);

        // 断言结果
        // 断言结果不为空
        assertNotNull(result);
        // 断言返回的 PDF URL 为 "testPublicUrl"
        assertEquals("testPublicUrl", result.getPdfUrl());
        // 断言返回的下载 URL 包含 "testApiUrl/api/v1/medias/files/"
        Assert.assertTrue(result.getDownloadUrl().contains("testApiUrl/api/v1/medias/files/"));
    }

    @Test
    public void testGetEventNotify_SiteAdmin() {
        // 准备数据
        String userId = "userId001";
        String agencyId = "agencyId001";
        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole("SITE_ADMIN");
        Date to = TimeUtil.addMinutes(TimeUtil.getUtcNow(), 10);
        List<EventUserEntity> eventUserEntities = new ArrayList<>();
        EventUserEntity eventUserEntity = new EventUserEntity();
        eventUserEntity.setIsRead(false);
        eventUserEntity.setUserId(userId);
        eventUserEntities.add(eventUserEntity);
        List<UsersMetaDataEntity> metas = new ArrayList<>();
        AgencyModel userAgency = new AgencyModel();
        userAgency.setId(agencyId);
        List<AgencyMetaDataEntity> allMetaData = new ArrayList<>();
        AgencyMetaDataEntity agencyMetaDataEntity = new AgencyMetaDataEntity();
        agencyMetaDataEntity.setAgencyId(agencyId);
        agencyMetaDataEntity.setMetaKey("ENABLE_SITE_ADMIN_MANAGEMENT_ACCESS");
        agencyMetaDataEntity.setMetaValue("1");
        allMetaData.add(agencyMetaDataEntity);
        List<String> groupIds = new ArrayList<>();
        groupIds.add("groupId001");
        AgencyModel agency = new AgencyModel();
        agency.setId(agencyId);
        // 用户引导使用的 metaKey
        String[] featureOpenKeys = new String[] {
                "UNREAD_MESSAGE_EMAIL_OPEN",
                "PERMISSION_LESSONS_WITHOUT_APPROVAL",
                "CREATE_UNIT_PLANNER_OPEN"
        };
        List<UsersMetaDataEntity> allMetaKeys = new ArrayList<>();
        UsersMetaDataEntity userMetaDataEntity = new UsersMetaDataEntity();
        userMetaDataEntity.setUserId(userId);
        userMetaDataEntity.setMetaKey("UNREAD_MESSAGE_EMAIL_OPEN");
        userMetaDataEntity.setMetaValue("true");
        allMetaKeys.add(userMetaDataEntity);
        FormsFormEntity formEntity = new FormsFormEntity();
        formEntity.setAgencyId(agencyId);
        formEntity.setFitBatch(false);
        List<CenterGroupModel> centerGroupModels = new ArrayList<>();
        CenterGroupModel centerGroupModel = new CenterGroupModel();
        centerGroupModel.setCenterId("centerId001");
        centerGroupModel.setGroupId("group001");
        centerGroupModels.add(centerGroupModel);
        String key = "USER_ACTIVE_TIME" + userId.toUpperCase();

        // 数据模拟
        lenient().when(userProvider.checkUser(userId)).thenReturn(userEntity);
        lenient().when(eventDao.queryEventUserByUserIdAndToTime(userId, to)).thenReturn(eventUserEntities);
        lenient().when(userDao.getCenterGroupByAgencyUser(userId)).thenReturn(centerGroupModels);
        lenient().when(userMetaDao.getMetas("ACADEMY_OPEN_FLAG", userId)).thenReturn(metas);
        UserMetaDataEntity userMetaDataEntity1 = new UserMetaDataEntity();
        userMetaDataEntity1.setMetaKey(UserMetaKey.SITE_ADMIN_HIDDEN_IMPORT_SYNC_OPEN.toString());
        userMetaDataEntity1.setMetaValue("true");
        lenient().when(userDao.getMetaData(userId, UserMetaKey.SITE_ADMIN_HIDDEN_IMPORT_SYNC_OPEN.toString())).thenReturn(userMetaDataEntity1);
        UserMetaDataEntity userMetaDataEntity2 = new UserMetaDataEntity();
        userMetaDataEntity2.setMetaKey(UserMetaKey.SITE_ADMIN_HIDDEN_MANAGE_SITES_OPEN.toString());
        userMetaDataEntity2.setMetaValue("true");
        lenient().when(userDao.getMetaData(userId, UserMetaKey.SITE_ADMIN_HIDDEN_MANAGE_SITES_OPEN.toString())).thenReturn(userMetaDataEntity2);
        UserMetaDataEntity userMetaDataEntity3 = new UserMetaDataEntity();
        userMetaDataEntity3.setMetaKey(UserMetaKey.SITE_ADMIN_HIDDEN_MANAGE_STAFF_OPEN.toString());
        userMetaDataEntity3.setMetaValue("true");
        lenient().when(userDao.getMetaData(userId, UserMetaKey.SITE_ADMIN_HIDDEN_MANAGE_STAFF_OPEN.toString())).thenReturn(userMetaDataEntity3);
        lenient().when(userProvider.getAgencyByUserId(userId)).thenReturn(userAgency);
        UserMetaDataEntity userMetaDataEntity4 = new UserMetaDataEntity();
        userMetaDataEntity4.setMetaKey(UserMetaKey.SITE_ADMIN_HIDDEN_RATING_PERIOD_SETUP_OPEN.toString());
        userMetaDataEntity4.setMetaValue("true");
        lenient().when(userDao.getMetaData(userId, UserMetaKey.SITE_ADMIN_HIDDEN_RATING_PERIOD_SETUP_OPEN.toString())).thenReturn(userMetaDataEntity4);

        lenient().when(userDao.getMetaData(userId, UserMetaKey.SITE_ADMIN_HIDDEN_IMPORT_SYNC_OPEN.toString())).thenReturn(userMetaDataEntity1);
        lenient().when(agencyDao.getAllMetaData(agencyId)).thenReturn(allMetaData);
        lenient().when(agencyDao.isBatchAddOpen(agencyId, "BOOKLEARNINGMEDIA_BATCHCREATE")).thenReturn(false);
        lenient().when(cacheService.exist(key)).thenReturn(false);
        lenient().when(healthCheckFormDao.getFormsByAgencyAndType(agencyId, "CHILD_HEALTH")).thenReturn(new ArrayList<>());
        lenient().when(healthCheckFormDao.getFormsByAgencyAndType(agencyId, "HEALTH_CHECK_QUESTION")).thenReturn(new ArrayList<>());
        lenient().when(groupDao.getGroupIdsByAgencyAdminId(userId)).thenReturn(groupIds);
        lenient().when(agencyDao.getMeta(agencyId, "HEALTH_CARD_OPEN_GROUP")).thenReturn(null);
        lenient().when(userProvider.getOpenValueDefaultOpen(userId, "HEALTH_CHECK_NOTIFICATION")).thenReturn(false);
        lenient().when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        lenient().when(userProvider.getOpenValueDefaultClose(userId, "REDUCE_PERIOD")).thenReturn(false);
        lenient().when(metaDao.getAppMeta("CHAT_MANUAL_NOTIFY")).thenReturn("true");
        lenient().when(metaDao.getAppMeta("CHAT_REMOTE_DEBUG")).thenReturn("true");
        lenient().when(userMetaDao.getMetasByUserId(Arrays.asList(featureOpenKeys), userId)).thenReturn(allMetaKeys);
        lenient().when(formsFormDao.selectByAgencyIdAndType(agency.getId(), "HEALTH_CHECK_QUESTION")).thenReturn(formEntity);
        lenient().when(formsFormDao.selectByAgencyIdAndType(agency.getId(), "CHILD_HEALTH")).thenReturn(null);
        lenient().when(userMetaDao.getMeta(userId, "UNREAD_MESSAGE_EMAIL_OPEN")).thenReturn(null);

        // 接口模拟
        NotifyResponse eventNotify = eventService.getEventNotify(userId, "");

        // 断言
        Assert.assertEquals(false, eventNotify.isImportSyncOpen());
        Assert.assertEquals(false, eventNotify.isManageSitesOpen());
        Assert.assertEquals(false, eventNotify.isManageStaffOpen());
        Assert.assertEquals(false, eventNotify.isRatingPeriodOpen());
    }
}

package com.learninggenie.api.service.impl;

import com.learninggenie.api.model.DownFileResponse;
import com.learninggenie.api.model.ExportQuestionnaireRequest;
import com.learninggenie.api.model.VideoFormResponse;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.service.HealthCheckService;
import com.learninggenie.common.data.dao.FormsOptionDao;
import com.learninggenie.common.data.dao.FormsQuestionEntityDao;
import com.learninggenie.common.data.dao.HealthCheckFormDao;
import com.learninggenie.common.data.dao.QuestionNaireDao;
import com.learninggenie.common.data.dao.impl.FormsFormDaoImpl;
import com.learninggenie.common.data.model.*;
import com.learninggenie.common.data.model.form.MediaFormEntity;
import com.learninggenie.common.encryption.EncryptionService;
import com.learninggenie.common.filesystem.FileSystem;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

/**
 * QuestionNaireServiceImpl 单元测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class QuestionNaireServiceImplTest {

    @InjectMocks
    private QuestionNaireServiceImpl questionNaireService;

    @Mock
    private QuestionNaireDao questionNaireDao;

    @Mock
    private FormsQuestionEntityDao formsQuestionEntityDao;

    @Mock
    private EncryptionService encryptionService;

    @Mock
    private FormsOptionDao formsOptionDao;

    @Mock
    private FileSystem fileSystem;

    @Mock
    private UserProvider userProvider;

    @Mock
    private FormsFormDaoImpl formsFormDao;

    @Mock
    private HealthCheckFormDao healthCheckFormDao;

    @Mock
    private HealthCheckService healthCheckService;

    /**
     * 测试导出问卷方法
     */
    @Test
    public void testExportQuestionnaire() {
        // 准备方法入参
        ExportQuestionnaireRequest request = new ExportQuestionnaireRequest();
        request.setChannelName("ChannelName");
        List<String> playListIds = new ArrayList<>();
        playListIds.add("playListId");
        request.setPlayListIds(playListIds);
        List<String> videoIds = new ArrayList<>();
        videoIds.add("videoId");
        request.setVideoIds(videoIds);
        Map<String, String> videoNameMap = new HashMap<>();
        videoNameMap.put("videoId", "videoName");
        request.setVideoNameMap(videoNameMap);
        request.setPlayListName("playListName");
        // 数据模拟
        List<MediaFormRecordModel> mediaForms = new ArrayList<>();
        MediaFormRecordModel model = new MediaFormRecordModel();
        model.setId("FormRecordID");
        model.setFormId("F8C1E48B-BC49-429E-B3E1-890A0E9E5D4F");
        model.setVideoId("videoId");
        mediaForms.add(model);
        List<QuestionNaireModel> questionNaireModels = new ArrayList<>();
        QuestionNaireModel questionNaireModel = new QuestionNaireModel();
        questionNaireModel.setId("Id");
        questionNaireModel.setCenterId("CenterId");
        questionNaireModel.setGroupId("GroupId");
        questionNaireModel.setChildId("ChildId");
        questionNaireModel.setCreateUserId("CreateUserId");
        questionNaireModel.setFormId("F8C1E48B-BC49-429E-B3E1-890A0E9E5D4F");
        questionNaireModel.setCenterName("CenterName");
        questionNaireModel.setClassName("GroupName");
        questionNaireModel.setChildName("ChildName");
        questionNaireModel.setParentName("ParentName");
        questionNaireModel.setRelationShip("Relationship");
        questionNaireModel.setResponseData(new byte[1]);
        questionNaireModels.add(questionNaireModel);
        List<FormsQuestionEntity> formQuestionList = new ArrayList<>();
        FormsQuestionEntity entity1 = new FormsQuestionEntity();
        entity1.setId("B0FE8AA4-CB45-4D95-81EA-12C3B642796D");
        entity1.setFormId("F8C1E48B-BC49-429E-B3E1-890A0E9E5D4F");
        entity1.setName("Name");
        entity1.setDescription("Description");
        entity1.setType("SINGLE_SELECT");
        entity1.setTextRegular("TextRegular");
        entity1.setRequired(true);
        entity1.setSortNum(1);
        entity1.setPrefab(false);
        entity1.setValue("Value");
        FormsQuestionEntity entity2 = new FormsQuestionEntity();
        entity2.setId("1AED994E-63E8-45FD-B3E2-6914ABD1DABF");
        entity2.setFormId("F8C1E48B-BC49-429E-B3E1-890A0E9E5D4F");
        entity2.setName("Name");
        entity2.setDescription("Description");
        entity2.setType("MULTI_SELECT");
        entity2.setTextRegular("TextRegular");
        entity2.setRequired(true);
        entity2.setSortNum(1);
        entity2.setPrefab(false);
        entity2.setValue("Value");
        formQuestionList.add(entity1);
        formQuestionList.add(entity2);
        List<FormsOptionEntity> options = new ArrayList<>();
        FormsOptionEntity option1 = new FormsOptionEntity();
        option1.setId("4AA0EF7B-9E2E-48D4-96E8-DC1ED21D992D");
        option1.setQuestionId("B0FE8AA4-CB45-4D95-81EA-12C3B642796D");
        option1.setName("Name");
        option1.setSortNum(1);
        option1.setFormId("F8C1E48B-BC49-429E-B3E1-890A0E9E5D4F");
        FormsOptionEntity option2 = new FormsOptionEntity();
        option2.setId("847DF13E-78E7-4E72-B1EA-D4B32F445EBC");
        option2.setQuestionId("1AED994E-63E8-45FD-B3E2-6914ABD1DABF");
        option2.setName("Name");
        option2.setSortNum(2);
        option2.setFormId("F8C1E48B-BC49-429E-B3E1-890A0E9E5D4F");
        options.add(option1);
        options.add(option2);
        userProvider.getTimezoneOffsetNum();
        // 模拟方法调用
        when(questionNaireDao.getMediaFormRecordList(playListIds)).thenReturn(mediaForms);
        when(questionNaireDao.getQuestionNaire(anyList())).thenReturn(questionNaireModels);
        when(formsQuestionEntityDao.getListByFormsIds(anyList())).thenReturn(formQuestionList);
        String string = "{\"responses\":[{\"id\":\"69a28cb3-52bc-4dd8-bbd3-0f6583d9f57e\",\"formId\":\"F8C1E48B-BC49-429E-B3E1-890A0E9E5D4F\",\"questionId\":\"B0FE8AA4-CB45-4D95-81EA-12C3B642796D\",\"optionId\":\"4AA0EF7B-9E2E-48D4-96E8-DC1ED21D992D\",\"mediaKeys\":[]},{\"id\":\"b171850b-2eb2-42e8-9198-c39e2e67825d\",\"formId\":\"F8C1E48B-BC49-429E-B3E1-890A0E9E5D4F\",\"questionId\":\"1AED994E-63E8-45FD-B3E2-6914ABD1DABF\",\"optionId\":\"847DF13E-78E7-4E72-B1EA-D4B32F445EBC\",\"mediaKeys\":[]},{\"id\":\"9aaad012-c3f7-445f-b9f1-042410bbd393\",\"formId\":\"F8C1E48B-BC49-429E-B3E1-890A0E9E5D4F\",\"questionId\":\"8C9DC9D9-2C9F-4AFA-B8D3-6EC5E136E780\",\"optionId\":\"4AC25238-448A-4CF8-93E9-85E3DD045133\",\"mediaKeys\":[]},{\"id\":\"fa4511c1-d112-494b-ba6c-56c0b6b99c10\",\"formId\":\"F8C1E48B-BC49-429E-B3E1-890A0E9E5D4F\",\"questionId\":\"34E39981-8B86-4E2C-8FFE-775103192C2D\",\"optionId\":\"7477D56B-07B8-4B9B-9295-10537CF8C32F\",\"mediaKeys\":[]},{\"id\":\"0814e283-e28f-4bae-bdc3-93f3aadd9172\",\"formId\":\"F8C1E48B-BC49-429E-B3E1-890A0E9E5D4F\",\"questionId\":\"92D10940-127B-4A9C-86A5-A9A0D3A992EF\",\"optionId\":\"984C90E1-7E9E-4663-98C1-F1A9F40CD824\",\"mediaKeys\":[]},{\"id\":\"c12514f9-8abe-42ae-96bf-9fe1f9b2cc99\",\"formId\":\"F8C1E48B-BC49-429E-B3E1-890A0E9E5D4F\",\"questionId\":\"B4402000-E656-4D37-951B-E592467FDD87\",\"optionId\":\"6DA74A6E-6EE7-4C9F-8499-2094AF429B45\",\"mediaKeys\":[]},{\"id\":\"9cbee9c8-fd7d-454c-a365-3909e18114d9\",\"formId\":\"F8C1E48B-BC49-429E-B3E1-890A0E9E5D4F\",\"questionId\":\"4760A971-E157-4F4A-B0C1-EA5922C087C8\",\"optionId\":\"9A8BB582-9C6D-41DC-B578-FEB05A2CB6FB\",\"mediaKeys\":[]}],\"fillRecords\":[]}";
        byte[] bytes = string.getBytes();
        when(encryptionService.decryptData(any())).thenReturn(bytes);
        when(formsOptionDao.getAllListByFormIds(any())).thenReturn(options);
        when(userProvider.getTimezoneOffsetNum()).thenReturn(0);
        doNothing().when(fileSystem).upload(any(), any());
        when(fileSystem.getPublicUrl(any())).thenReturn("http://test.com/upoload/a001.jpg");
        // 调用执行方法
        DownFileResponse downFileResponse = questionNaireService.exportQuestionnaire(request);
        // 验证结果
        assertEquals("http://test.com/upoload/a001.jpg", downFileResponse.getUrl()); // 验证测试方法返回响应是否正确
    }

    /**
     * 测试视频问卷获取方法
     */
    @Test
    public void testGetVideoForm() {
        // 准备测试数据
        String videoId = "IPimwOKUDKI";
        String channelId = "channel123";
        String fromId = "form123";
        String agencyId = "agency123";
        int count = 110;
        when(userProvider.getCurrentAgencyId()).thenReturn(agencyId);

        MediaFormEntity mediaFormEntity = new MediaFormEntity();
        mediaFormEntity.setId(fromId);
        mediaFormEntity.setFormId(fromId);

        // 配置 Mock 返回对象
        when(formsFormDao.getMediaFormByVideo(channelId, videoId, agencyId)).thenReturn(mediaFormEntity);
        when(formsFormDao.getMediaFormByFormId(fromId)).thenReturn(mediaFormEntity);
        when(healthCheckFormDao.getFormsByIds(any())).thenReturn(Arrays.asList(new FormsFormEntity()));
        when(questionNaireDao.getCountQuestionnaireByFormIds(any())).thenReturn(count);

        // 执行被测试的方法
        VideoFormResponse response = questionNaireService.getVideoForm(videoId, channelId, fromId);

        // 验证结果
        assertNotNull(response);
        assertEquals(fromId, response.getId());
        assertEquals(count, response.getCount());
    }
}

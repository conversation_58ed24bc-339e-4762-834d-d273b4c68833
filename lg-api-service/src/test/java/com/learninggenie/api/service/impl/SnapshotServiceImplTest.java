package com.learninggenie.api.service.impl;

import com.google.api.client.util.Lists;
import com.learninggenie.api.model.LockStudentRequest;
import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.model.GetFrameworkSettingResponse;
import com.learninggenie.api.model.GetKeyMeasuresSettingResponse;
import com.learninggenie.api.model.report.SnapshotEditRecord;
import com.learninggenie.common.data.model.score.BatchLockResponse;
import com.learninggenie.api.model.student.CheckLockResponse;
import com.learninggenie.api.model.student.StudentsAttrRequest;
import com.learninggenie.api.model.student.StudentsAttrValuesRequest;
import com.learninggenie.api.model.student.UpdateSnapshotRequest;
import com.learninggenie.api.provider.EnrollmentProvider;
import com.learninggenie.api.provider.SnapshotProvider;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.service.PortfolioService;
import com.learninggenie.api.service.ScoreService;
import com.learninggenie.common.data.dao.*;
import com.learninggenie.common.data.dao.impl.CenterMapper;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.entity.AgencyEntity;
import com.learninggenie.common.data.entity.AgencyMetaDataEntity;
import com.learninggenie.common.data.entity.CenterEntity;
import com.learninggenie.common.data.entity.EnrollmentEntity;
import com.learninggenie.common.data.entity.EnrollmentPeriodEntity;
import com.learninggenie.common.data.entity.RatingPeriodEntity;
import com.learninggenie.common.data.entity.UserEntity;
import com.learninggenie.common.data.enums.AgencyMetaKey;
import com.learninggenie.common.data.enums.DrdpSettingKey;
import com.learninggenie.common.data.enums.DrdpSettingValue;
import com.learninggenie.common.data.enums.UserRole;
import com.learninggenie.common.data.model.*;
import com.learninggenie.common.data.model.drdp.setting.CenterSetting;
import com.learninggenie.common.data.model.drdp.setting.CenterSettingData;
import com.learninggenie.common.data.model.drdp.setting.DRDPSetting;
import com.learninggenie.common.data.model.drdp.setting.FrameworkType;
import com.learninggenie.common.exception.LearningGenieRuntimeException;
import com.learninggenie.common.framwork.FrameworkService;
import com.learninggenie.common.report.AnalysisService;
import com.learninggenie.common.report.CheckLockResult;
import com.learninggenie.common.report.LGSnapshot;
import com.learninggenie.common.score.DomainLevelResult;
import com.learninggenie.common.score.RatingService;
import com.learninggenie.common.utils.*;
import org.json.JSONObject;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThrows;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyBoolean;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Created by zjj on 2016/9/6.
 */
@RunWith(MockitoJUnitRunner.class)
public class SnapshotServiceImplTest {
    @InjectMocks
    private SnapshotServiceImpl snapshotService;
    @Mock
    private AgencyDao agencyDao;
    @Mock
    private CenterMapper centerMapper;
    @Mock
    private EnrollmentProvider enrollmentProvider;
    @Mock
    private StudentDao studentDao;
    @Mock
    private AnalysisService analysisService;
    @Mock
    private ScoreDao scoreDao;
    @Mock
    private ReportDao reportDao;
    @Mock
    private UserProvider userProvider;
    @Mock
    private UserDaoImpl userDao;
    @Mock
    RatingService ratingService;

    @Mock
    private DomainDao domainDao;

    @Mock
    private NoteDao noteDao;

    @Mock
    private PortfolioService portfolioService;

    @Mock
    private JobDao jobDao;

    @Mock
    private ScoreService scoreService;

    @Mock
    private GroupDao groupDao;

    @Mock
    private FrameworkService frameworkService;

    @Mock
    private SnapshotProvider snapshotProvider;

    @Mock
    private PortfolioDao portfolioDao;

    /**
     * 孩子锁定检查
     *正常锁定
     */
    @Ignore
    @Test
    public void testCheckChildLock(){
        String childId = "c001";
        String userId = "u001";
        AgencyModel agency = new AgencyModel();
        agency.setId("a001");
        agency.setName("a001");
        Mockito.when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        CheckLockResult result = new CheckLockResult();
        Mockito.when(analysisService.checkChildLock(anyString(), anyString(), any(Date.class), any(Date.class), anyString())).thenReturn(result);
        UserEntity userEntity = new UserEntity();
        assertTrue(!StringUtil.isEmptyOrBlank(userId));
        when(userProvider.checkUser(userId)).thenReturn(userEntity);
        JSONObject jsonObject = new JSONObject();
        when(ratingService.getLanguageJson(anyString(), anyString())).thenReturn(jsonObject);
        snapshotService.checkChildLock(childId,userId, new Date(), new Date(), null);
        Mockito.verify(analysisService, times(1)).checkChildLock(anyString(), anyString(), any(Date.class), any(Date.class), anyString());
    }

    /**
     *孩子锁定检查
     * 孩子周期没找到，抛异常
     */
    @Ignore
    @Test (expected = BusinessException.class)
    public void testCheckChildLockWithException1 (){
        String childId = "c001";
        String userId = "u001";
        AgencyModel agency = new AgencyModel();
        agency.setId("a001");
        agency.setName("a001");
        Mockito.when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        Mockito.when(analysisService.checkChildLock(anyString(), anyString(), any(Date.class), any(Date.class), anyString())).thenThrow(new LearningGenieRuntimeException("1"));
        snapshotService.checkChildLock(childId,userId, new Date(), new Date(), null);
        Mockito.verify(analysisService, times(1)).checkChildLock(childId,agency.getId(), new Date(), new Date(), null);
    }

    /**
     *孩子锁定检查
     * 孩子的周期别名和班级的周期别名不一致，抛异常
     */
    @Ignore
    @Test (expected = BusinessException.class)
    public void testCheckChildLockWithException2 (){
        String childId = "c001";
        String userId = "u001";
        AgencyModel agency = new AgencyModel();
        agency.setId("a001");
        agency.setName("a001");
        Mockito.when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        Mockito.when(analysisService.checkChildLock(anyString(), anyString(), any(Date.class), any(Date.class), anyString())).thenThrow(new LearningGenieRuntimeException("2"));
        snapshotService.checkChildLock(childId,userId, new Date(), new Date(), null);
        Mockito.verify(analysisService, times(1)).checkChildLock(childId,agency.getId(), new Date(), new Date(), null);
    }

    /**
     * 锁定学生
     * 孩子的周期别名和班级的周期别名一致，学生所在的agency没有快照
     */
    @Test
    public void testLockStudent (){
        String childId = "c001";
        String lockAtLocal = "2016-03-26 13:31:40.000";
        String userId = "u001";
        String alias = "2016-2017 Fall";
        AgencyModel agency = new AgencyModel();
        agency.setId("a001");
        agency.setName("a001");
        RatingPeriodEntity childPeriod = new RatingPeriodEntity();
        childPeriod.setAlias(alias);
        childPeriod.setFromAtLocal(new Date());
        childPeriod.setToAtLocal(new Date());
        String from = "2017-04-18";
        String to = "2017-04-18";
        Date fromDate = TimeUtil.parse(from, "yyyy-MM-dd"); // 开始时间
        Date toDate = TimeUtil.parse(to, "yyyy-MM-dd"); // 结束时间
        String frameworkId = UUID.randomUUID().toString(); // 框架 Id
        String centerId = "c001";
        String groupId = "g001";


//        Mockito.when(studentDao.getCurrentPeriod(childId)).thenReturn(childPeriod);
//        Mockito.when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        CheckLockResult result = new CheckLockResult();
        result.setSuccess(true);
        result.setLockAttr(true);
        result.setAliasIsSame(true);
        when(analysisService.checkChildLock(childId, agency.getId(), fromDate, toDate, frameworkId)).thenReturn(result);

        DomainEntity currentFramework = new DomainEntity();
        currentFramework.setId(frameworkId); // 设置框架 Id
        when(studentDao.getChildFramework(childId)).thenReturn(currentFramework);

        // 框架设置
        GetFrameworkSettingResponse frameworkSetting = new GetFrameworkSettingResponse();
        // mock 获取框架设置
        when(portfolioService.getFrameworkSetting(null)).thenReturn(frameworkSetting);

        // 关键测评点设置
        GetKeyMeasuresSettingResponse keyMeasuresSetting = new GetKeyMeasuresSettingResponse();
        // mock 获取关键测评点设置
        when(portfolioService.getKeyMeasuresSetting(anyString(), anyString())).thenReturn(keyMeasuresSetting);

        List<DRDPSetting> drdpSettings = new ArrayList<>();
        DRDPSetting drdpSetting = new DRDPSetting();
        drdpSetting.setId("1");
        drdpSetting.setComplete(true);
        List<CenterSetting> centerSettings = new ArrayList<>();
        CenterSetting centerSetting = new CenterSetting();
        centerSetting.setId(centerId);
        List<String> groupIds = Lists.newArrayList();
        groupIds.add(groupId);
        centerSetting.setGroupIds(groupIds);

        centerSettings.add(centerSetting);
        drdpSetting.setCenterSettings(centerSettings);

        CenterSettingData centerSettingData = new CenterSettingData();
        centerSettingData.setCenterSettings(centerSettings);
        drdpSetting.setCenterSetting(JsonUtil.toJson(centerSettingData));

        drdpSettings.add(drdpSetting);
        when(agencyDao.getDRDPSettingByAgency(agency.getId())).thenReturn(drdpSettings);

        // 小孩信息
        EnrollmentModel enrollmentModel = new EnrollmentModel();
        enrollmentModel.setId(childId); // 设置小孩 Id
        enrollmentModel.setGroupId(groupId);
        enrollmentModel.setFrameworkId(frameworkId);
        // mock 获取小孩信息
        when(studentDao.getEnrollment(childId)).thenReturn(enrollmentModel);

        RatingPeriodEntity period = new RatingPeriodEntity();
        period.setAlias(alias);
        when(studentDao.getPeriodByTime(childId, from, to)).thenReturn(period);

        // 机构实体
        AgencyEntity agencyEntity = new AgencyEntity();
        agencyEntity.setId(agency.getId()); // 设置机构 Id
        // mock 获取机构
        when(agencyDao.getAgencyByChildId(anyString())).thenReturn(agencyEntity);

        EnrollmentPeriodEntity enrollmentPeriodEntity = new EnrollmentPeriodEntity();
        enrollmentPeriodEntity.setId(childId);
        enrollmentPeriodEntity.setPeriodEntity(childPeriod);

        StudentSnapshotEntity studentSnapshot = new StudentSnapshotEntity();
        studentSnapshot.setId("s001");
        when(analysisService.lockStudent(any(EnrollmentPeriodEntity.class), anyString(), anyString(), any(Date.class), anyString())).thenReturn(studentSnapshot);

        boolean featureOpen = true;
        when(userProvider.getAgencyOpenDefaultOpen(agency.getId(), AgencyMetaKey.DRDP_UPLOAD.toString())).thenReturn(featureOpen);

        AgencyMetaDataEntity uploadMeta = new AgencyMetaDataEntity();
        uploadMeta.setMetaValue(DrdpSettingValue.DRDP_UPLOAD_OPEN.toString());
        when(agencyDao.getMeta(agency.getId(), DrdpSettingKey.UPLOAD_DRDP_SETTING.toString())).thenReturn(uploadMeta);

        when(ratingService.isPSFramework(frameworkId)).thenReturn(true);

        StudentScoreEntity scoreEntity = new StudentScoreEntity();
        scoreEntity.setCreateAtUtc(lockAtLocal);
//        Mockito.when(scoreDao.getLastScore(childId)).thenReturn(scoreEntity);
        List<RequestCenterModel> requestCenterModels = new ArrayList<>();
        RequestCenterModel centerModel = new RequestCenterModel();
        requestCenterModels.add(centerModel);
        centerModel.setTimezone("TimeZone");
        EnrollmentEntity child = new EnrollmentEntity();
        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setId("c001");
        com.learninggenie.common.data.entity.GroupEntity groupEntity = new com.learninggenie.common.data.entity.GroupEntity();
        groupEntity.setCenter(centerEntity);
        child.setId(childId);
        child.setGroup(groupEntity);
//        Mockito.when(studentDao.getChildWithGroupCenter(anyString())).thenReturn(child);
//        Mockito.when(centerMapper.getCenterByCenterId(anyString())).thenReturn(requestCenterModels);
        Mockito.when(userProvider.checkUser(anyString())).thenReturn(new UserEntity());

        BatchLockResponse batchLockResponse = new BatchLockResponse();
//        when(scoreService.batchLockAndUpload(anyString())).thenReturn(batchLockResponse);

        LockStudentRequest request = new LockStudentRequest();
        request.setChildId(childId).setLockAtLocal(lockAtLocal).setAlias(alias).setFrom(from).setTo(to)
                .setFrameworkId(frameworkId).setUserId(userId).setIgnoreSsidAttr(false);
        snapshotService.lockStudent(request);
        Mockito.verify(analysisService,times(1)).lockStudent(any(EnrollmentPeriodEntity.class), anyString(), anyString(), any(Date.class), anyString());
    }

    /**
     * 锁定班级/孩子快照
     * 锁定指定孩子的快照
     * zjj 2016.8.24
     */
    @Test
    public void testLockStudentSnapshotNoGroupId() {
        String groupId = null;
        String userId = "u001";
        List<String> snapshotIds = new ArrayList<>();
        snapshotIds.add("s001");
        snapshotIds.add("s002");
        String alias = "2016-2017 Fall";
        AgencyModel agency = new AgencyModel();
        agency.setId("a001");
        agency.setName("a001");
        Mockito.when(userProvider.getCurrentUserId()).thenReturn(userId);
        Mockito.when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        snapshotService.lockStudentSnapshot(alias, groupId, snapshotIds);
        Mockito.verify(studentDao, times(1)).lockSnapshot(snapshotIds);
        Mockito.verify(agencyDao, times(1)).updateAgencySnapshotTime(agency.getId(),alias);
        Mockito.verify(studentDao, times(0)).getSnapshotByGroup(anyString(), anyString());
    }

    /**
     * 锁定班级/孩子快照
     * 锁定指定班级的快照
     * zjj 2016.8.24
     */
    @Test
    public void testLockStudentSnapshotByGroupId() {
        String userId = "u001";
        String groupId = "g001";
        List<StudentSnapshotEntity> snapshotEntities = new ArrayList<>();
        StudentSnapshotEntity snapshotEntity1 = new StudentSnapshotEntity();
        snapshotEntity1.setId("s001");
        StudentSnapshotEntity snapshotEntity2 = new StudentSnapshotEntity();
        snapshotEntity2.setId("s002");
        snapshotEntities.add(snapshotEntity1);
        snapshotEntities.add(snapshotEntity2);
        List<String> snapshotIds = new ArrayList<>();
        snapshotIds.add("s001");
        snapshotIds.add("s002");
        String alias = "2016-2017 Fall";
        AgencyModel agency = new AgencyModel();
        agency.setId("a001");
        agency.setName("a001");
        Mockito.when(userProvider.getCurrentUserId()).thenReturn(userId);
        Mockito.when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        Mockito.when(studentDao.getSnapshotByGroup(groupId, alias)).thenReturn(snapshotEntities);
        snapshotService.lockStudentSnapshot(alias, groupId, null);
        Mockito.verify(studentDao, times(1)).lockSnapshot(snapshotIds);
        Mockito.verify(studentDao, times(1)).getSnapshotByGroup(anyString(), anyString());
        Mockito.verify(agencyDao, times(1)).updateAgencySnapshotTime(agency.getId(),alias);
    }

    @Test(expected = BusinessException.class)
    public void testLockStudentSnapshotWithException() {
        snapshotService.lockStudentSnapshot(null, null, null);
    }

    public SnapshotSearch getSnapshotSearch(String alias) {
        SnapshotSearch snapshotSearch = new SnapshotSearch();
        List<AttrBase> attrFilters = new ArrayList<>();
        snapshotSearch.setAttrFilters(attrFilters);
        snapshotSearch.setPeriodAlias(alias);
        snapshotSearch.setIncludeScores(false);
        return snapshotSearch;
    }

    public StudentSnapshotEntity getSnapshotEntities(String firstName, long birthday, LGSnapshot.StudentSnapshot.GenderType genderType, String alias) {
        StudentSnapshotEntity entity = new StudentSnapshotEntity();
        entity.setPeriodAlias(alias);
        entity.setLocked(true);
        entity.setData(this.getSnapshotData(firstName, birthday, genderType));
        entity.setCreateAtUtc(new Date(1476892800000L));
        entity.setLockAtUtc(new Date(1476892800000L));
        return entity;
    }

    //获取快照数据
    public byte[] getSnapshotData(String firstName, long birthday, LGSnapshot.StudentSnapshot.GenderType genderType) {
        LGSnapshot.ClassSnapshot classSnapshot = LGSnapshot.ClassSnapshot.newBuilder()
                .setId("class001")
                .setName("Class 001")
                .setCreatedAt(100)
                .setUpdatedAt(200)
                .build();
        LGSnapshot.StudentSnapshot studentSnapshot = LGSnapshot.StudentSnapshot.newBuilder()
                .setId("s001")
                .setFirstName(firstName)
                .setLastName("M")
                .setGender(genderType)
                .setBirthday(birthday)
                .setGroup(classSnapshot)
                .build();

        return studentSnapshot.toByteArray();
    }
    /**
     * 测试获取快照列表
     * 没有filter，没有分页
     */
    @Test
    public void testGetSnapshots() {
        SnapshotSearch request = this.getSnapshotSearch("2016-2017 Summer");
        AgencyModel agency = new AgencyModel();
        agency.setId("001");
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agency);
        AgencySnapshotEntity snapshotEntity = new AgencySnapshotEntity();
        snapshotEntity.setAgencyId(agency.getId());
//        when(agencyDao.getSnapshot(anyString(),anyString())).thenReturn(snapshotEntity);

        List<SnapshotResponse> snapshotResponses = new ArrayList<>();
        SnapshotSearchResponse model = new SnapshotSearchResponse();
        model.setTotal(1);
        model.setResults(snapshotResponses);
//        when(analysisService.getSnapshots(any(AgencySnapshotEntity.class),any(SnapshotSearch.class),anyBoolean(), anyBoolean(), anyBoolean(), any(UserEntity.class), anyMap())).thenReturn(model);
//        UserEntity user = new UserEntity();
//        when(userProvider.checkUser(anyString())).thenReturn(user);
        snapshotService.getSnapshots("u001", request, true, false);
//        verify(analysisService,times(1)).getSnapshots(any(AgencySnapshotEntity.class),any(SnapshotSearch.class),anyBoolean(), anyBoolean(), anyBoolean(), any(UserEntity.class), anyMap());
    }

    /**
     * 测试获取快照列表
     * 没有filter，没有分页
     */
    @Test(expected = BusinessException.class)
    public void testGetSnapshotsWithException() {
        SnapshotSearch request = this.getSnapshotSearch("2016-2017 Summer");
        snapshotService.getSnapshots("u001", request, true, false);
    }

    @Test
    public void testGetSnapshotNotFound (){
        String snapshotId = "s001";
        when(studentDao.getSnapshot(snapshotId)).thenReturn(null);
        SnapshotResponse response  = snapshotService.getSnapshot(snapshotId);
        assertEquals(null,response);
    }

    /**
     * 更新快照
     * 没有agency快照抛异常
     */
    @Test
    public void testUpdateSnapshot (){
        String userId = "u001";
        UpdateSnapshotRequest snapshot = this.prepareSnapshotData();

        StudentSnapshotEntity snapshotEntity = this.prepareSnapshotEntityData(true);
        snapshotEntity.setLocked(false);
        snapshotEntity.setPeriodAlias("Fall 2023-2024");
        snapshotEntity.setEnrollmentId("test1");
        when(studentDao.getSnapshot(anyString())).thenReturn(snapshotEntity);
        AgencyModel agency = new AgencyModel();
        agency.setId("001");
        agency.setName("agencyName");
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agency);
        AgencySnapshotEntity agencySnapshotEntity = new AgencySnapshotEntity();
        LGSnapshot.StudentRecordSnapshot studentSnapshot = LGSnapshot.StudentRecordSnapshot.newBuilder()
                .setRecordId("r001")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .build();
        LGSnapshot.ClassSnapshot classSnapshot =LGSnapshot.ClassSnapshot.newBuilder()
                .setId("sg001")
                .setName("sg001")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .addRecords(studentSnapshot)
                .build();
        LGSnapshot.CenterSnapshot centerSnapshot = LGSnapshot.CenterSnapshot.newBuilder()
                .setId("sc001")
                .setName("sc001")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .addClasses(classSnapshot)
                .build();
        LGSnapshot.Properties property  = LGSnapshot.Properties.newBuilder()
                .setName("a001")
                .setValue("avalue001")
                .setType("atype001")
                .build();
        LGSnapshot.AgencySnapshot agencySnapshot = LGSnapshot.AgencySnapshot.newBuilder()
                .setId("a000001")
                .setName("s000001")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .addCenters(centerSnapshot)
                .addProperties(property)
                .build();
        agencySnapshotEntity.setId("snapshotId");
        agencySnapshotEntity.setData(agencySnapshot.toByteArray());
        when(agencyDao.getSnapshot(agency.getId(), snapshotEntity.getPeriodAlias())).thenReturn(agencySnapshotEntity);

        List<DomainEntity> domains = new  ArrayList<>();
        DomainEntity domain = new DomainEntity();
        domain.setId("d001");
        domain.setName("d001");
        domain.setAbbreviation("d001");
        domain.setUseCondition("IEP");
        domains.add(domain);
        DomainEntity domain1 = new DomainEntity();
        domain1.setId("d002");
        domain1.setName("d002");
        domain1.setAbbreviation("d002");
        domains.add(domain1);
        when(domainDao.getAllChildDomains(any())).thenReturn(domains);

        // 查询评分模板
        ScoreTemplateEntity scoreTemplate = new ScoreTemplateEntity();
        scoreTemplate.setPortfolioId("p001");
        scoreTemplate.setDomainLevelsJson("domainLevelsJson");
        scoreTemplate.setLevelsJson("[\n" +
                "  {\n" +
                "    \"id\": \"levelId1\",\n" +
                "    \"name\": \"level1\",\n" +
                "    \"value\": \"u\",\n" +
                "    \"levelIndex\": 0\n" +
                "  }\n" +
                "]");
        when(portfolioDao.loadScoreTemplate(any())).thenReturn(scoreTemplate);

        Map<String, LevelEntity> levelMap = new HashMap<>();
        for (int i = 0; i < 30; i++ ) {
            LevelEntity levelEntity = new LevelEntity();
            levelEntity.setId(("levelId" + i).toUpperCase());
            levelEntity.setName("level" + i);
            levelMap.put(("levelId" + i).toUpperCase(), levelEntity);
        }

        when(frameworkService.getLevels(snapshot.getRatingRecord().getFrameworkId())).thenReturn(levelMap);


//        when(reportDao.getScoreComment(anyString())).thenReturn(null);
        snapshotService.updateSnapshot(snapshot,userId);
        verify(studentDao, times(1)).setSnapshotActiveByChildId(anyBoolean(), anyString(), anyString(), anyString());
        verify(studentDao,times(1)).createSnapshot(any(StudentSnapshotEntity.class));
        verify(analysisService,times(1)).generateAgencySnapshot(anyString(),any(AgencyModel.class),anyList());

    }

    @Test
    public void testUpdateSnapshot_1() {
        String userId = "u001";
        UpdateSnapshotRequest snapshot = this.prepareSnapshotData();

        StudentSnapshotEntity snapshotEntity = this.prepareSnapshotEntityData(false);
        snapshotEntity.setLocked(false);
        snapshotEntity.setPeriodAlias("Fall 2023-2024");
        snapshotEntity.setLockScore("[\n" +
                "  {\n" +
                "    \"notesCount\": 0,\n" +
                "    \"measureNotesCount\": 0,\n" +
                "    \"distinctCenterMeasureCount\": 0,\n" +
                "    \"distinctScoreMeasureCount\": 0,\n" +
                "    \"distinctCenterKeyMeasureCount\": 0,\n" +
                "    \"distinctScoreKeyMeasureCount\": 0,\n" +
                "    \"iepDomainCount\": 0,\n" +
                "    \"distinctIEPDomainCount\": 0,\n" +
                "    \"distinctIEPRatingCount\": 0,\n" +
                "    \"openUpload\": false,\n" +
                "    \"iep\": false,\n" +
                "    \"eld\": false,\n" +
                "    \"keyMeasureNum\": 0,\n" +
                "  }\n" +
                "]");
        when(studentDao.getSnapshot(anyString())).thenReturn(snapshotEntity);
        AgencyModel agency = new AgencyModel();
        agency.setId("001");
        agency.setName("agencyName");
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agency);
        AgencySnapshotEntity agencySnapshotEntity = new AgencySnapshotEntity();
        LGSnapshot.StudentRecordSnapshot studentSnapshot = LGSnapshot.StudentRecordSnapshot.newBuilder()
                .setRecordId("r001")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .build();
        LGSnapshot.ClassSnapshot classSnapshot =LGSnapshot.ClassSnapshot.newBuilder()
                .setId("sg001")
                .setName("sg001")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .addRecords(studentSnapshot)
                .build();
        LGSnapshot.CenterSnapshot centerSnapshot = LGSnapshot.CenterSnapshot.newBuilder()
                .setId("sc001")
                .setName("sc001")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .addClasses(classSnapshot)
                .build();
        LGSnapshot.Properties property  = LGSnapshot.Properties.newBuilder()
                .setName("a001")
                .setValue("avalue001")
                .setType("atype001")
                .build();
        LGSnapshot.AgencySnapshot agencySnapshot = LGSnapshot.AgencySnapshot.newBuilder()
                .setId("a000001")
                .setName("s000001")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .addCenters(centerSnapshot)
                .addProperties(property)
                .build();
        agencySnapshotEntity.setId("snapshotId");
        agencySnapshotEntity.setData(agencySnapshot.toByteArray());
        when(agencyDao.getSnapshot(agency.getId(), snapshotEntity.getPeriodAlias())).thenReturn(agencySnapshotEntity);

        Map<String, LevelEntity> levelMap = new HashMap<>();
        for (int i = 0; i < 30; i++ ) {
            LevelEntity levelEntity = new LevelEntity();
            levelEntity.setId(("levelId" + i).toUpperCase());
            levelEntity.setName("level" + i);
            levelMap.put(("levelId" + i).toUpperCase(), levelEntity);
        }

        when(frameworkService.getLevels(snapshot.getRatingRecord().getFrameworkId())).thenReturn(levelMap);


//        when(reportDao.getScoreComment(anyString())).thenReturn(null);
        snapshotService.updateSnapshot(snapshot,userId);
        verify(studentDao,times(1)).setSnapshotActiveByChildId(anyBoolean(),anyString(),anyString(),anyString());
        verify(studentDao,times(1)).createSnapshot(any(StudentSnapshotEntity.class));
        verify(analysisService,times(1)).generateAgencySnapshot(anyString(),any(AgencyModel.class),anyList());
    }

    private UpdateSnapshotRequest prepareSnapshotData(){
        UpdateSnapshotRequest snapshot = new UpdateSnapshotRequest();
        snapshot.setId("snapshotId");
        snapshot.setFirstName("0");
        snapshot.setMiddleName("0");
        snapshot.setLastName("1");
        snapshot.setBirthDate(TimeUtil.getUtcNowStr());
        snapshot.setEnrollmentDate(TimeUtil.getUTCTimeStr());
        snapshot.setWithdrawnDate(TimeUtil.getUTCTimeStr());
        snapshot.setCompletedDate(TimeUtil.getUTCTimeStr());
        snapshot.setGender(LGSnapshot.StudentSnapshot.GenderType.MALE.toString());
        snapshot.setGroupId("g001");
        snapshot.setGroupName("gName");
        snapshot.setCenterId("c001");
        snapshot.setCenterName("cName");
        List<StudentAttr> attrs = new ArrayList<>();
        for (int i = 0; i < 4; i++){
            StudentAttr studentAttr = new StudentAttr();
            studentAttr.setId("attr" + i);
            studentAttr.setName("attrName" + i);
            studentAttr.getValues().add("value" + i);
            studentAttr.setTypeValue("type" + i);
            attrs.add(studentAttr);
        }
        snapshot.setAttrs(attrs);
        RatingRecordModel currentPeriod = new RatingRecordModel();
        currentPeriod.setFramework("00");
        currentPeriod.setFrameworkId("Id");
        currentPeriod.setPeriodAlias("2016 - 2017  Spring");
        currentPeriod.setFrom(TimeUtil.getNow().getTime());
        currentPeriod.setTo(TimeUtil.getNow().getTime());
        List<DomainLevelResult> scores = new ArrayList<>();
        for (int i = 0; i < 30; i++ ) {
            DomainLevelResult model = new DomainLevelResult();
            model.setDomainId("id" + i);
            model.setMeasureName("measureName" + i);
            model.setMeasure("measure" + i);
            model.setLevelId("levelId" + i);
            model.setLevelName("level" + i);
            scores.add(model);
        }
        currentPeriod.setScores(scores);
        snapshot.setRatingRecord(currentPeriod);

        List<StudentsAttrRequest> updateAttrs = new ArrayList<>();
        StudentsAttrRequest studentAttrRequest = new StudentsAttrRequest();
        studentAttrRequest.setName("Statewide Student Identifier");
        List<StudentsAttrValuesRequest> values = new ArrayList<>();
        StudentsAttrValuesRequest request = new StudentsAttrValuesRequest();
        request.setName("Statewide Student Identifier");
        request.setTextboxValue("1234567891");
        values.add(request);
        studentAttrRequest.setValues(values);
        updateAttrs.add(studentAttrRequest);

        snapshot.setUpdateAttrs(updateAttrs);
        snapshot.setEnrollmentId("test1");
        snapshot.setFrameworkId("ttt");
        return snapshot;
    }

    private StudentSnapshotEntity prepareSnapshotEntityData(boolean prepareSsidAttr){
        StudentSnapshotEntity snapshotEntity = new StudentSnapshotEntity();
        LGSnapshot.StudentSnapshot.Builder studentSnapshotBuilder = LGSnapshot.StudentSnapshot.newBuilder();
        Date birth = TimeUtil.parseDate("2016-11-3");
        studentSnapshotBuilder.setId("test001")
                .setFirstName("testFirstName")
                .setMiddleName("testMiddleName")
                .setLastName("testLastName")
                .setBirthday(birth.getTime())
                .setGender(LGSnapshot.StudentSnapshot.GenderType.FEMALE)
                .setAvatarUrl("avatarUrl")
                .setAge(TimeUtil.getAgeByBirthday(birth))
                .setEnrollmentDate(new Date().getTime())
                .setWithdrawnDate(new Date().getTime())
                .setCompletedDate(new Date().getTime());

        LGSnapshot.StudentRecordSnapshot lastRecord = LGSnapshot.StudentRecordSnapshot.newBuilder()
                .setRecordId("test000")
                .setCreatedAt(new Date().getTime())
                .setUpdatedAt(new Date().getTime())
                .build();
        LGSnapshot.StudentRecordSnapshot nextRecord = LGSnapshot.StudentRecordSnapshot.newBuilder()
                .setRecordId("test002")
                .setCreatedAt(new Date().getTime())
                .setUpdatedAt(new Date().getTime())
                .build();
        //学生的属性
        List<LGSnapshot.Properties> properties = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            LGSnapshot.Properties property = LGSnapshot.Properties.newBuilder()
                    .setName("attrName" + i)
                    .setValue("values" + i)
                    .setType("boolean")
                    .build();
            properties.add(property);
        }
        if (prepareSsidAttr) {
            LGSnapshot.Properties ssidProperty = LGSnapshot.Properties.newBuilder().setName("Statewide Student Identifier").setValue("1234567890").setType("text").build();
            properties.add(ssidProperty);
        }
        //学生的当前周期
        List<LGSnapshot.Score> scores = new ArrayList<>();
        for (int i = 0; i < 30; i++) {
            LGSnapshot.Score score = LGSnapshot.Score.newBuilder()
                    .setDomainId("domainId" + i)
                    .setDomainAbbr("abbr" + i)
                    .setDomainName("domainName" + i)
                    .setScoreId("scoreId" + i)
                    .setScore(String.valueOf(GenerateCodeUtil.SECURE_RANDOM.nextDouble() * 10))
                    .setCore(true)
                    .build();
            scores.add(score);
        }
        LGSnapshot.RatingRecords ratingRecords = LGSnapshot.RatingRecords.newBuilder()
                .setPeriodAlias("2016 - 2017 Fall")
                .setFrom(TimeUtil.parseDate("2016-01-03").getTime())
                .setTo(TimeUtil.parseDate("2016-11-03").getTime())
                .setFramework("FrameWorkName")
                .setFrameworkId("FrameWorkId")
                .addAllScores(scores)
                .build();

        //agency
        LGSnapshot.AgencySnapshot agency = LGSnapshot.AgencySnapshot.newBuilder()
                .setId("agencyId0")
                .setName("agencyName")
                .setCreatedAt(TimeUtil.getNow().getTime())
                .setUpdatedAt(TimeUtil.getNow().getTime())
                .build();
        //学校
        LGSnapshot.CenterSnapshot center = LGSnapshot.CenterSnapshot.newBuilder()
                .setId("centerId")
                .setName("centerName")
                .setCreatedAt(TimeUtil.getNow().getTime())
                .setUpdatedAt(TimeUtil.getNow().getTime())
                .setAgency(agency)
                .build();
        //班级
        LGSnapshot.ClassSnapshot group = LGSnapshot.ClassSnapshot.newBuilder()
                .setId("groupId")
                .setName("groupName")
                .setCreatedAt(TimeUtil.getNow().getTime())
                .setUpdatedAt(TimeUtil.getNow().getTime())
                .setCenter(center)
                .build();

        studentSnapshotBuilder.setRatingRecords(ratingRecords)
                .addAllProperties(properties)
                .setGroup(group);
        studentSnapshotBuilder.setLastSnapshot(lastRecord);
        studentSnapshotBuilder.setNextSnapshot(nextRecord);

        LGSnapshot.StudentSnapshot studentSnapshot = studentSnapshotBuilder.build();
        snapshotEntity.setData(studentSnapshot.toByteArray());
        snapshotEntity.setMasterId("masterId");
        snapshotEntity.setId("snapshotId");
        snapshotEntity.setAgencyId("agencyId");
        snapshotEntity.setActive(true);
        snapshotEntity.setCreateAtUtc(TimeUtil.getNow());
        snapshotEntity.setDeleted(false);
        snapshotEntity.setEnrollmentId("test001");
        snapshotEntity.setFromAtLocal(TimeUtil.parseDate("2016-01-03"));
        snapshotEntity.setToAtLocal(TimeUtil.parseDate("2016-11-03"));
        snapshotEntity.setLockAtLocal(TimeUtil.getNow());
        snapshotEntity.setUpdateByUserId("userId0");
        snapshotEntity.setFrameworkId("ttt");
        return snapshotEntity;
    }

    /**
     * 获取历史快照
     */
    @Test
    public void testGetHistoricalSnapshots (){
        String snapshotId = "s001";
        StudentSnapshotEntity snapshotEntity = this.prepareSnapshotEntityData(false);
        snapshotEntity.setLocked(false);
        snapshotEntity.setId(snapshotId);
        when(studentDao.getSnapshot(anyString())).thenReturn(snapshotEntity);
        List<StudentSnapshotEntity> snapshotEntities = new ArrayList<>();
        StudentSnapshotEntity snapshotEntity1 = this.prepareSnapshotEntityData(false);
        StudentSnapshotEntity snapshotEntity2 = this.prepareSnapshotEntityData(false);
        snapshotEntity1.setId("s001");
        snapshotEntity2.setId("s002");
        snapshotEntities.add(snapshotEntity1);
        snapshotEntities.add(snapshotEntity2);
        when(studentDao.getSnapshotByMasterId(anyString())).thenReturn(snapshotEntities);
        UserModel updatedUser = new UserModel();
        updatedUser.setId("userId");
        updatedUser.setDisplayName("name");
        updatedUser.setEmail("email");
        when(userDao.getUserById(anyString())).thenReturn(updatedUser);
        List<SnapshotEditRecord> snapshotEditRecords = snapshotService.getHistoricalSnapshots(snapshotId);
        assertEquals(1,snapshotEditRecords.size());
    }

    /**
     * 解锁快照
     * 正常解锁
     */
    @Test
    public void testUnlockStudentSnapshot (){
        String periodAlias = "2016-2017 Fall";
        String groupId = "g001";
        List<String> snapshotIds = new ArrayList<>();
        snapshotIds.add("s001");
        AgencyModel agency = new AgencyModel();
        agency.setId("001");
        agency.setName("agencyName");
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agency);
        snapshotService.unlockStudentSnapshot(periodAlias,groupId,snapshotIds);
        verify(studentDao,times(1)).deleteSnapshot(anyList());
    }

    /**
     * 解锁快照
     * groupId，快照Id 为空， 抛异常
     */
    @Test(expected = BusinessException.class)
    public void testUnlockStudentSnapshotWithException (){
        String periodAlias = "2016-2017 Fall";
        snapshotService.unlockStudentSnapshot(periodAlias,null,null);
    }
    /**
     * 测试获取快照列表
     * 添加filter，查询性别为女的快照
     */
    @Ignore
    @Test
    public void testGetSnapshots_filter() {
        SnapshotSearch request = this.getSnapshotSearch("2016-2017 Fall");
        AttrBase attr = new AttrBase();
        attr.setName("Gender");
        List<String> values = new ArrayList<>();
        values.add("Female");
        attr.setValues(values);
        request.getAttrFilters().add(attr);

        UserEntity user = new UserEntity();
        user.setRole(UserRole.AGENCY_OWNER.toString());
        when(userProvider.checkUser(anyString())).thenReturn(user);
        AgencyModel agency = new AgencyModel();
        agency.setId("001");
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agency);

        List<StudentSnapshotEntity> studentSnapshotEntities = new ArrayList<>();
        studentSnapshotEntities.add(this.getSnapshotEntities("S", 1413734400000L, LGSnapshot.StudentSnapshot.GenderType.FEMALE, "2016-2017 Fall"));
        studentSnapshotEntities.add(this.getSnapshotEntities("D", 1413934400000L, LGSnapshot.StudentSnapshot.GenderType.MALE, "2016-2017 Fall"));
        when(studentDao.getSnapshotByAgency(anyString(), anyString())).thenReturn(studentSnapshotEntities);

        SnapshotSearchResponse model = snapshotService.getSnapshots("u001", request, true, false);
        assertEquals(1, model.getTotal());
        List<SnapshotResponse> snapshotResponses = model.getResults();
        SnapshotResponse response = snapshotResponses.get(0);
        assertEquals("2016-2017 Fall", response.getPeriodAlias());
        assertEquals("S", response.getFirstName());
    }
    //生成快照
    @Test
    public void testGenerateSnapshotsWithIds() {
        snapshotService.generateSnapshotsWithIds(anyList());
        verify(analysisService,times(1)).generateSnapshotsWithIds(anyList());
    }

    /**
     *删除快照
     * 正常删除
     */
    @Test
    public void testDeleteSnapshot (){
        String userId = "u001";
        String alias = "2016-2017 Fall";
        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setRole(UserRole.AGENCY_OWNER.toString());
        when(userProvider.checkUser(anyString())).thenReturn(user);
        AgencyModel agency = new AgencyModel();
        agency.setId("001");
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agency);
        snapshotService.deleteSnapshot(userId,alias);
        verify(studentDao,times(1)).deleteAgencySnapshotByAgencyId(anyString(),anyString());
        verify(studentDao,times(1)).deleteSnapshotByAgencyId(anyString(),anyString());
    }

    /**
     * 删除快照
     * 用户不是agency owner 直接抛异常
     */
    @Test (expected = BusinessException.class)
    public void testDeleteSnapshotWithException (){
        String userId = "u001";
        String alias = "2016-2017 Fall";
        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setRole(UserRole.SITE_ADMIN.toString());
        when(userProvider.checkUser(anyString())).thenReturn(user);
        snapshotService.deleteSnapshot(userId,alias);
        verify(studentDao,times(0)).deleteAgencySnapshotByAgencyId(anyString(),anyString());
        verify(studentDao,times(0)).deleteSnapshotByAgencyId(anyString(),anyString());
    }

    /**
     * 快照还原
     */
    @Test
    public void testRevertSnapshot (){
        String userId = "u001";
        String currentSnapshotId = "cs001";
        String revertSnapshotId = "rs001";
        AgencyModel agency = new AgencyModel();
        agency.setId("001");
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agency);

        StudentSnapshotEntity snapshotEntity = this.prepareSnapshotEntityData(false);
        snapshotEntity.setLocked(false);
        //获取学生当前的快照
        when(studentDao.getSnapshot(currentSnapshotId)).thenReturn(snapshotEntity);
        when(studentDao.getSnapshot("test000")).thenReturn(snapshotEntity);
        when(studentDao.getSnapshot("test002")).thenReturn(snapshotEntity);
        //获取学生要还原的快照
        snapshotEntity.setId("test003");
        when(studentDao.getSnapshot(revertSnapshotId)).thenReturn(snapshotEntity);
        snapshotService.revertSnapshot(currentSnapshotId, revertSnapshotId, userId);
        verify(studentDao, times(4)).updateSnapshotData(any(StudentSnapshotEntity.class));
        verify(studentDao, times(2)).setSnapshotActive(anyBoolean(),anyString());
        verify(analysisService,times(1)).generateAgencySnapshot(any(),any(AgencyModel.class),anyList());

    }

    /**
     * 测试获取快照列表
     * 分页查询
     */
    @Ignore
    @Test
    public void testGetSnapshots_page() {
        SnapshotSearch request = this.getSnapshotSearch("2016-2017 Fall");
        request.setPageSize(2);

        UserEntity user = new UserEntity();
        user.setRole(UserRole.AGENCY_OWNER.toString());
        when(userProvider.checkUser(anyString())).thenReturn(user);
        AgencyModel agency = new AgencyModel();
        agency.setId("001");
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agency);

        List<StudentSnapshotEntity> studentSnapshotEntities = new ArrayList<>();
        studentSnapshotEntities.add(this.getSnapshotEntities("S", 1413734400000L, LGSnapshot.StudentSnapshot.GenderType.FEMALE, "2016-2017 Fall"));
        studentSnapshotEntities.add(this.getSnapshotEntities("D", 1413934400000L, LGSnapshot.StudentSnapshot.GenderType.MALE, "2016-2017 Fall"));
        studentSnapshotEntities.add(this.getSnapshotEntities("G", 1413234400000L, LGSnapshot.StudentSnapshot.GenderType.MALE, "2016-2017 Fall"));
        studentSnapshotEntities.add(this.getSnapshotEntities("H", 1413134400000L, LGSnapshot.StudentSnapshot.GenderType.MALE, "2016-2017 Fall"));
        when(studentDao.getSnapshotByAgency(anyString(), anyString())).thenReturn(studentSnapshotEntities);

        SnapshotSearchResponse model = snapshotService.getSnapshots("u001", request, false, false);
        assertEquals(2, model.getResults().size());
    }

    /**
     * 测试获取快照列表
     * 添加排序条件
     */
    @Ignore
    @Test
    public void testGetSnapshots_sort() {
        SnapshotSearch request = this.getSnapshotSearch("2016-2017 Fall");
        request.setSort("firstName");

        UserEntity user = new UserEntity();
        user.setRole(UserRole.AGENCY_OWNER.toString());
        when(userProvider.checkUser(anyString())).thenReturn(user);
        AgencyModel agency = new AgencyModel();
        agency.setId("001");
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agency);

        List<StudentSnapshotEntity> studentSnapshotEntities = new ArrayList<>();
        studentSnapshotEntities.add(this.getSnapshotEntities("S", 1413734400000L, LGSnapshot.StudentSnapshot.GenderType.FEMALE, "2016-2017 Fall"));
        studentSnapshotEntities.add(this.getSnapshotEntities("D", 1413934400000L, LGSnapshot.StudentSnapshot.GenderType.MALE, "2016-2017 Fall"));
        studentSnapshotEntities.add(this.getSnapshotEntities("A", 1413234400000L, LGSnapshot.StudentSnapshot.GenderType.MALE, "2016-2017 Fall"));
        studentSnapshotEntities.add(this.getSnapshotEntities("H", 1413134400000L, LGSnapshot.StudentSnapshot.GenderType.MALE, "2016-2017 Fall"));
        when(studentDao.getSnapshotByAgency(anyString(), anyString())).thenReturn(studentSnapshotEntities);

        SnapshotSearchResponse model = snapshotService.getSnapshots("u001", request, false, false);
        SnapshotResponse response = model.getResults().get(0);
        assertEquals("A", response.getFirstName());
    }

    //agency 是否能导入快照
    @Test
    public void testCanImportSnapshot (){
        String periodAlias = "2016-2017 Fall";
        String userId = "u001";
        AgencyModel agency = new AgencyModel();
        agency.setId("001");
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agency);
        AgencySnapshotEntity agencySnapshotEntity = new AgencySnapshotEntity();
        agencySnapshotEntity.setAgencyId("001");
        agencySnapshotEntity.setPeriodAlias(periodAlias);
        when(agencyDao.getSnapshot(anyString(), anyString())).thenReturn(agencySnapshotEntity);
        boolean result = snapshotService.canImportSnapshot(periodAlias,userId);
        assertEquals(false,result);
    }

    /**
     * Create by hxl 2023/04/24.
     * 测试获取快照列表
     */
    @Test(expected = BusinessException.class)
    public void testUnlockStudentSnapshotWithInvalidPeriodAlias() {
        final String userId = "80287cf3-c32b-47af-9df8-56ff15723ce5";
        final String periodAlias = "";
        final List<String> snapshotIds = Arrays.asList("1", "2", "3");
        snapshotService.unlockStudentSnapshot(userId, periodAlias, snapshotIds);
    }


    /**
     * Create by hxl 2023/04/24.
     * 测试获取快照列表
     */
    @Test
    public void testUnlockStudentSnapshotWithNoAgency() {
        final String userId = "0183df16-7428-44d9-b992-dacd0ca41b42";
        final String periodAlias = "Q1";
        final List<String> snapshotIds = new ArrayList<>();
        snapshotIds.add("e4409a17-466b-4b78-a7fb-9059accc5309");
        when(userProvider.getAgencyByUserId(userId)).thenReturn(null);
        assertThrows(BusinessException.class, () -> {
            snapshotService.unlockStudentSnapshot(userId, periodAlias, snapshotIds);
        });
    }

    /**
     * Create by hxl 2023/04/24.
     * 测试获取快照列表
     */
    @Test
    public void testUnlockStudentSnapshotWithValidParameters() {
        final String userId = "efe17d4f-70f1-444c-8c89-8cc8e1ed8096";
        final String periodAlias = "Q1";
        final List<String> snapshotIds = Arrays.asList("1", "2", "3");
        when(userProvider.getAgencyByUserId(userId)).thenReturn(new AgencyModel());
        snapshotService.unlockStudentSnapshot(userId, periodAlias, snapshotIds);
        verify(studentDao, times(1)).deleteSnapshot(eq(snapshotIds));
        verify(reportDao, times(1)).deleteActionPlanBySnapshotIds(eq(snapshotIds));
        verify(agencyDao, times(1)).updateAgencySnapshotTime(any(), eq(periodAlias));
    }

    /**
     * Case: 机构开启了完成所有测评点才能锁定开关但未评完所有测评点
     * 结果: 提示新的错误提示
     */
    @Test
    public void testLockStudent1() {
        // 准备数据
        String childId = UUID.randomUUID().toString(); // 学生 Id
        String lockAtLocal = "2019-01-01 00:00:00"; // 本地锁定时间
        String alias = "2019-2020 Fall"; // 周期
        String from = "2019-01-01"; // 开始时间
        String to = "2019-06-01"; // 结束时间
        String userId = UUID.randomUUID().toString(); // 用户 Id
        String frameworkId = UUID.randomUUID().toString(); // 框架 Id
        Date fromDate = TimeUtil.parse(from, "yyyy-MM-dd"); // 开始时间
        Date toDate = TimeUtil.parse(to, "yyyy-MM-dd"); // 结束时间
        String agencyId = UUID.randomUUID().toString(); // 机构 Id
        // 用户
        UserEntity user = new UserEntity();
        user.setId(userId); // 设置用户 Id
        // mock 获取用户
        when(userProvider.checkUser(userId)).thenReturn(user);
        // 机构
        AgencyModel agency = new AgencyModel();
        agency.setId(agencyId); // 设置机构 Id
        // mock 获取机构
//        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        // 框架信息
        DomainEntity currentFramework = new DomainEntity();
        currentFramework.setId(frameworkId); // 设置框架 Id
        // mock 获取框架信息
        when(studentDao.getChildFramework(childId)).thenReturn(currentFramework);
        // 框架设置
        GetFrameworkSettingResponse frameworkSetting = new GetFrameworkSettingResponse();
        // mock 获取框架设置
        when(portfolioService.getFrameworkSetting(null)).thenReturn(frameworkSetting);
        // 关键测评点设置
        GetKeyMeasuresSettingResponse keyMeasuresSetting = new GetKeyMeasuresSettingResponse();
        // mock 获取关键测评点设置
        when(portfolioService.getKeyMeasuresSetting(anyString(), anyString())).thenReturn(keyMeasuresSetting);
        // 小孩信息
        EnrollmentModel child = new EnrollmentModel();
        child.setId(childId); // 设置小孩 Id
        // mock 获取小孩信息
        when(studentDao.getEnrollment(childId)).thenReturn(child);
        // 是否上传到 DRDP
        RatingPeriodEntity period = new RatingPeriodEntity();
        period.setAlias(alias);
        // mock 是否上传到 DRDP
        when(studentDao.getPeriodByTime(childId, from, to)).thenReturn(period);
        // 校验锁定对象
        CheckLockResult result = new CheckLockResult();
        // mock 校验锁定对象
        when(analysisService.checkChildLock(childId, agencyId, fromDate, toDate, frameworkId)).thenReturn(result);
        // 校验锁定对象
        CheckLockResponse lockResponse = new CheckLockResponse();
        lockResponse.setSuccess(false);
        // 机构实体
        AgencyEntity agencyEntity = new AgencyEntity();
        agencyEntity.setId(agencyId); // 设置机构 Id
        // mock 获取机构
        when(agencyDao.getAgencyByChildId(anyString())).thenReturn(agencyEntity);
        // 机构 meta 信息
        AgencyMetaDataEntity meta = new AgencyMetaDataEntity();
        meta.setMetaValue("true");
        // mock 获取机构 meta 信息
        when(agencyDao.getMeta(anyString(), anyString())).thenReturn(meta);

        // 使用 assertThrows 捕获并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            // 调用方法
            LockStudentRequest request = new LockStudentRequest();
            request.setChildId(childId).setLockAtLocal(lockAtLocal).setAlias(alias).setFrom(from).setTo(to).setUserId(userId).setFrameworkId(frameworkId).setIgnoreSsidAttr(false);
            snapshotService.lockStudent(request);
        });

        // 校验异常信息 提示新的提示语
        assertEquals("Please ensure all measures are rated before locking this child. Verify completion by toggling to \"Show all measures\" for any unrated.", exception.getDetail());
    }

    /**
     * Case: 机构未开启了完成所有测评点才能锁定开关但未评完所有测评点
     * 结果: 提示之前的错误提示
     */
    @Test
    public void testLockStudent2() {
        // 准备数据
        String childId = UUID.randomUUID().toString(); // 学生 Id
        String lockAtLocal = "2019-01-01 00:00:00"; // 本地锁定时间
        String alias = "2019-2020 Fall"; // 周期
        String from = "2019-01-01"; // 开始时间
        String to = "2019-06-01"; // 结束时间
        String userId = UUID.randomUUID().toString(); // 用户 Id
        String frameworkId = UUID.randomUUID().toString(); // 框架 Id
        Date fromDate = TimeUtil.parse(from, "yyyy-MM-dd"); // 开始时间
        Date toDate = TimeUtil.parse(to, "yyyy-MM-dd"); // 结束时间
        String agencyId = UUID.randomUUID().toString(); // 机构 Id
        // 用户
        UserEntity user = new UserEntity();
        user.setId(userId); // 设置用户 Id
        // mock 获取用户
        when(userProvider.checkUser(userId)).thenReturn(user);
        // 机构
        AgencyModel agency = new AgencyModel();
        agency.setId(agencyId); // 设置机构 Id
        // mock 获取机构
//        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        // 框架信息
        DomainEntity currentFramework = new DomainEntity();
        currentFramework.setId(frameworkId); // 设置框架 Id
        // mock 获取框架信息
        when(studentDao.getChildFramework(childId)).thenReturn(currentFramework);
        // 框架设置
        GetFrameworkSettingResponse frameworkSetting = new GetFrameworkSettingResponse();
        // mock 获取框架设置
        when(portfolioService.getFrameworkSetting(null)).thenReturn(frameworkSetting);
        // 关键测评点设置
        GetKeyMeasuresSettingResponse keyMeasuresSetting = new GetKeyMeasuresSettingResponse();
        // mock 获取关键测评点设置
        when(portfolioService.getKeyMeasuresSetting(anyString(), anyString())).thenReturn(keyMeasuresSetting);
        // 小孩信息
        EnrollmentModel child = new EnrollmentModel();
        child.setId(childId); // 设置小孩 Id
        // mock 获取小孩信息
        when(studentDao.getEnrollment(childId)).thenReturn(child);
        // 是否上传到 DRDP
        RatingPeriodEntity period = new RatingPeriodEntity();
        // mock 是否上传到 DRDP
        when(studentDao.getPeriodByTime(childId, from, to)).thenReturn(period);
        // 校验锁定对象
        CheckLockResult result = new CheckLockResult();
        // mock 校验锁定对象
        when(analysisService.checkChildLock(childId, agencyId, fromDate, toDate, frameworkId)).thenReturn(result);
        // 校验锁定对象
        CheckLockResponse lockResponse = new CheckLockResponse();
        lockResponse.setSuccess(false);
        // 机构实体
        AgencyEntity agencyEntity = new AgencyEntity();
        agencyEntity.setId(agencyId); // 设置机构 Id
        // mock 获取机构
        when(agencyDao.getAgencyByChildId(anyString())).thenReturn(agencyEntity);
        // 机构 meta 信息
        AgencyMetaDataEntity meta = new AgencyMetaDataEntity();
        meta.setMetaValue("false");
        // mock 获取机构 meta 信息
        when(agencyDao.getMeta(agencyId, AgencyMetaKey.COMPLETE_RATING_REQUIRED_FOR_CHILD_LOCK.toString())).thenReturn(meta);

        // 使用 assertThrows 捕获并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            // 调用方法

            LockStudentRequest request = new LockStudentRequest();
            request.setChildId(childId).setLockAtLocal(lockAtLocal).setAlias(alias).setFrom(from).setTo(to)
                    .setFrameworkId(frameworkId).setUserId(userId).setIgnoreSsidAttr(false);
            snapshotService.lockStudent(request);
        });

        // 校验异常信息 提示之前的提示语
        assertEquals("Required Child's Attributes Missing or Rating Incomplete.", exception.getDetail());
    }

    @Test
    public void testChildFrameworkIsNa() {
        DRDPSetting drdpSetting = new DRDPSetting();
        CenterSettingData centerSettingData = new CenterSettingData();
        List<FrameworkType> frameworkTypes = Lists.newArrayList();
        frameworkTypes.add(new FrameworkType("IT", "ITC"));

        centerSettingData.setFrameworkTypes(frameworkTypes);

        drdpSetting.setCenterSetting(JsonUtil.toJson(centerSettingData));

        when(ratingService.isPSFramework(anyString())).thenReturn(true);

        boolean result = snapshotService.childFrameworkIsNa(RateUtil.FRAMEWORK_IT_ID, drdpSetting);

        Assert.assertFalse(result);
    }

    @Test
    public void testGetSnapshotInfo() {
        String snapshotId = "1";
        // 获取快照
        StudentSnapshotEntity studentSnapshot = new StudentSnapshotEntity();
        studentSnapshot.setId(snapshotId);
        studentSnapshot.setStatus("PENDING");
        when(studentDao.getSnapshot(snapshotId)).thenReturn(studentSnapshot);

        SnapshotResponse snapshotInfo = snapshotService.getSnapshotInfo(snapshotId);

        // 校验快照的状态
        Assert.assertEquals("PENDING", snapshotInfo.getStatus());
    }

    @Test
    public void testGetSnapshotInfo_returnNull() {
        String snapshotId = "1";
        // 获取快照
        when(studentDao.getSnapshot(snapshotId)).thenReturn(null);

        SnapshotResponse snapshotInfo = snapshotService.getSnapshotInfo(snapshotId);

        // 校验快照的状态
        Assert.assertNull(snapshotInfo.getStatus());
    }
}
package com.learninggenie.api.service.impl;

import com.google.api.client.googleapis.json.GoogleJsonError;
import com.google.api.client.googleapis.json.GoogleJsonResponseException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class YouTubeServiceImplTest {

    @InjectMocks
    private YouTubeServiceImpl youTubeService;

    /**
     * 测试 extractGoogleJsonErrorInfos 方法
     * case: GoogleJsonResponseException 存在错误列表
     */
    @Test
    public void testExtractGoogleJsonErrorInfosWhenGoogleJsonResponseExceptionWithDetails() {
        // 数据准备
        GoogleJsonError googleJsonError = new GoogleJsonError();
        List<GoogleJsonError.ErrorInfo> errorInfos = new ArrayList<>();
        GoogleJsonError.ErrorInfo errorInfo = new GoogleJsonError.ErrorInfo();
        errorInfos.add(errorInfo);
        googleJsonError.setErrors(errorInfos);
        GoogleJsonResponseException exception = Mockito.mock(GoogleJsonResponseException.class);
        when(exception.getDetails()).thenReturn(googleJsonError);
        // 调用测试方法
        List<GoogleJsonError.ErrorInfo> result = youTubeService.extractGoogleJsonErrorInfos(exception);
        // 验证
        assertEquals(1, result.size()); // 验证返回的列表长度为 1
        assertEquals(errorInfo, result.get(0)); // 验证返回的列表中的元素为 errorInfo
    }

    /**
     * 测试 extractGoogleJsonErrorInfos 方法
     * case: 入参为非 GoogleJsonResponseException 异常
     */
    @Test
    public void testExtractGoogleJsonErrorInfosWhenNonGoogleJsonResponseException() {
        // 数据准备
        Exception exception = new Exception();
        // 调用测试方法
        List<GoogleJsonError.ErrorInfo> result = youTubeService.extractGoogleJsonErrorInfos(exception);
        // 验证
        assertNull(result); // 验证返回值为 null
    }
}

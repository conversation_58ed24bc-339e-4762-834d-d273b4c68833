package com.learninggenie.api.service;

import com.google.api.services.slides.v1.model.BatchUpdatePresentationResponse;
import com.learninggenie.api.model.googleslides.ReplaceRelation;
import com.learninggenie.api.service.impl.GoogleSlidesServiceImpl;
import com.learninggenie.common.data.enums.googleslides.ReplaceType;
import org.junit.Ignore;
import org.junit.Test;

import java.io.IOException;
import java.security.GeneralSecurityException;
import java.util.ArrayList;

import static org.junit.Assert.assertEquals;

@Ignore
public class GoogleSlidesServiceTest {

    /**
     * 测试替换模板文本
     *
     * @throws GeneralSecurityException
     * @throws IOException
     */
    @Test
    public void testReplaceTemplateText() throws GeneralSecurityException, IOException {
        // 创建一个 GoogleSlidesService 对象
        GoogleSlidesService authService = new GoogleSlidesServiceImpl();

        // 模板 ppt 对应的 ID
        String presentationId = "1zOl9Mdse1-vPLt0JQxyY8U8BGq62gyxXDoU4in36qhk";

        // 复制模板 ppt
        String copyId = authService.copyPresentation(presentationId, "My Duplicate Presentation");

        // 构建要替换的关系
        ArrayList<ReplaceRelation> mappingRelations = new ArrayList<>();
        // 构建要替换的关系
        ReplaceRelation relation = new ReplaceRelation();
        relation.setReplaceType(ReplaceType.TEXT);
        relation.setOldReplaceText("{Agency Name}");
        relation.setReplaceWithText("Google");

        mappingRelations.add(relation);
        // 执行替换
        BatchUpdatePresentationResponse response = authService.replaceTemplateContent(copyId, mappingRelations);

        // 断言
        assertEquals(1, response.getReplies().size());
    }


    /**
     * 测试替换模板链接
     *
     * @throws GeneralSecurityException
     * @throws IOException
     */
    @Test
    public void testReplaceTemplateLink() throws GeneralSecurityException, IOException {
        // 创建一个 GoogleSlidesService 对象
        GoogleSlidesService authService = new GoogleSlidesServiceImpl();

        // 模板 ppt 对应的 ID
        String presentationId = "1zOl9Mdse1-vPLt0JQxyY8U8BGq62gyxXDoU4in36qhk";

        // 复制模板 ppt
        String copyId = authService.copyPresentation(presentationId, "My Duplicate Presentation");


        // 构建要替换的关系
        ArrayList<ReplaceRelation> mappingRelations = new ArrayList<>();
        // 构建要替换的关系
        ReplaceRelation relation = new ReplaceRelation();
        relation.setReplaceType(ReplaceType.SHEET_CHART_LINK);
        relation.setOldReplaceText("{Centers Contributing Image}");
        relation.setSheetId("1asypfp9fk51aWWBJsLUziSEpWNnRXJbbID3FLvHs_sM");
        relation.setChartId(1140958338);

        mappingRelations.add(relation);
        // 执行替换
        BatchUpdatePresentationResponse response = authService.replaceTemplateContent(copyId, mappingRelations);

        // 断言
        assertEquals(1, response.getReplies().size());
    }


    /**
     * 测试替换模板图片
     *
     * @throws GeneralSecurityException
     * @throws IOException
     */
    @Test
    public void testReplaceTemplateImage() throws GeneralSecurityException, IOException {
        // 创建一个 GoogleSlidesService 对象
        GoogleSlidesService authService = new GoogleSlidesServiceImpl();

        // 模板 ppt 对应的 ID
        String presentationId = "1zOl9Mdse1-vPLt0JQxyY8U8BGq62gyxXDoU4in36qhk";

        // 复制模板 ppt
        String copyId = authService.copyPresentation(presentationId, "My Duplicate Presentation");


        // 构建要替换的关系
        ArrayList<ReplaceRelation> mappingRelations = new ArrayList<>();
        // 构建要替换的关系
        ReplaceRelation relation = new ReplaceRelation();
        relation.setReplaceType(ReplaceType.IMAGE);
        relation.setOldReplaceText("{Assessment progress}");
        relation.setImageUrl("https://www.gstatic.com/webp/gallery/1.jpg");
        mappingRelations.add(relation);
        // 执行替换
        BatchUpdatePresentationResponse response = authService.replaceTemplateContent(copyId, mappingRelations);

        // 断言
        assertEquals(1, response.getReplies().size());
    }


    /**
     * 测试替换模板图片以及插入链接
     *
     * @throws GeneralSecurityException
     * @throws IOException
     */
    @Test
    public void testReplaceTemplateImageLink() throws GeneralSecurityException, IOException {
        // 创建一个 GoogleSlidesService 对象
        GoogleSlidesService authService = new GoogleSlidesServiceImpl();

        // 模板 ppt 对应的 ID
        String presentationId = "1zOl9Mdse1-vPLt0JQxyY8U8BGq62gyxXDoU4in36qhk";

        // 复制模板 ppt
        String copyId = authService.copyPresentation(presentationId, "My Duplicate Presentation");


        // 构建要替换的关系
        ArrayList<ReplaceRelation> mappingRelations = new ArrayList<>();
        // 构建要替换的关系
        ReplaceRelation relation = new ReplaceRelation();
        relation.setReplaceType(ReplaceType.IMAGE_LINK);
        relation.setOldReplaceText("{Top Centers Contributing}");
        relation.setImageUrl("https://www.gstatic.com/webp/gallery/1.jpg");
        relation.setExternalUrl("https://docs.google.com/spreadsheets/d/1asypfp9fk51aWWBJsLUziSEpWNnRXJbbID3FLvHs_sM/edit#gid=0");
        mappingRelations.add(relation);
        // 执行替换
        BatchUpdatePresentationResponse response = authService.replaceTemplateContent(copyId, mappingRelations);

        // 断言
        assertEquals(1, response.getReplies().size());
    }
}

package com.learninggenie.api.service.impl.inkind;

import com.learninggenie.api.model.inkind.*;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.provider.impl.InKindProviderImpl;
import com.learninggenie.api.security.AuthUserDetails;
import com.learninggenie.common.data.dao.*;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.dao.inkind.*;
import com.learninggenie.common.data.entity.*;
import com.learninggenie.common.data.enums.AgencyMetaKey;
import com.learninggenie.common.data.enums.InKindStageEnum;
import com.learninggenie.common.data.enums.InkindApproveStatus;
import com.learninggenie.common.data.enums.UserMetaKey;
import com.learninggenie.common.data.mapper.dynamo.AgencyMetadataMapper;
import com.learninggenie.common.data.model.AgencyModel;
import com.learninggenie.common.data.model.UserModel;
import com.learninggenie.common.data.model.dynamo.AgencyMetadata;
import com.learninggenie.common.data.model.inkind.*;
import com.learninggenie.common.filesystem.FileSystem;
import com.learninggenie.common.utils.JsonUtil;
import com.learninggenie.common.utils.TimeUtil;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.times;

@ExtendWith(MockitoExtension.class)
class InKindDonationServiceImplTest {
    @InjectMocks
    InKindDonationServiceImpl inKindDonationService;

    @Mock
    private InKindProviderImpl inKindCommonService;

    @Mock
    private UserProvider userProvider;

    @Mock
    private AgencyDao agencyDao;

    @Mock
    private CenterDao centerDao;

    @Mock
    private GroupDao groupDao;

    @Mock
    private UserDaoImpl userDao;

    @Mock
    private StudentDao studentDao;

    @Mock
    private InkindDao inkindDao;

    @Mock
    private InKindReportApproveDao inKindReportApproveDao;

    @Mock
    private InKindReportGrantDao inKindReportGrantDao;

    @Mock
    private AgencyMetadataMapper agencyMetadataMapper;

    @Mock
    private UsersMetaDataDao usersMetaDataDao;

    @Mock
    private FileSystem fileSystem;

    /**
     * mock 获取机构 id 的方法
     *
     * @param user 用户
     * @param agencyId 机构 id
     */
    private void mockGetAgencyId(UserEntity user, String agencyId) {
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        String userId = user.getId();
        when(inKindCommonService.getAgencyId()).thenReturn(agencyId);
        lenient().when(userProvider.getCurrentUserId()).thenReturn(userId);
        lenient().when(userProvider.checkUser(userId)).thenReturn(user);
        if (user.isAgencyAdmin() || user.isAgencyOwner()) {
            lenient().when(userDao.getAgencyByAgencyAdminId(user.getId())).thenReturn(Arrays.asList(agencyModel));
        } else if (user.isSiteAdmin()) {
            lenient().when(userDao.getAgencyBySiteAdminId(user.getId())).thenReturn(Arrays.asList(agencyModel));
        } else if (user.isTeacher()) {
            lenient().when(userDao.getAgencyByTeacherId(user.getId())).thenReturn(Arrays.asList(agencyModel));
        }
    }

    @Test
    void saveActivityDescriptionHistory() {
        // 数据准备 -- 登录人信息
        String currentUserId = "53deb562-b57a-4eb9-be15-f4f41dcaff92";
        // 数据准备 -- 入参
        ActivityDescriptionsHistoryRequest request = new ActivityDescriptionsHistoryRequest();
        request.setActivityType("DONATION");
        ArrayList<String> descriptions = new ArrayList<>();
        descriptions.add("第三世界");
        descriptions.add("爱玩足球");
        request.setActivityDescriptionsHistory(descriptions);
        // 数据准备 -- 接口模拟
        when(userProvider.getCurrentUserId()).thenReturn(currentUserId);
        // 是否查询到数据
//        Mockito.when(usersMetaDataDao.setMeta(anyString(), anyString(), anyString()));
        // Mockito.when(usersMetaDataDao.getMeta(anyString(),anyString())).thenReturn(anyString());
        // 调用
        inKindDonationService.saveActivityDescriptionHistory(request);
        // 验证 -- 方法调用次数
        verify(userProvider, times(1)).getCurrentUserId();
        verify(usersMetaDataDao, times(1)).setMeta(currentUserId, UserMetaKey.INKIND_DONATION_HST_ACTIVITY_DESCRIPTIONS.toString(), JsonUtil.toJson(request.getActivityDescriptionsHistory()));
//        verify(usersMetaDataDao, Mockito.times(1)).insertMeta(UserMetaKey.INKIND_DONATION_HST_ACTIVITY_DESCRIPTIONS.toString(), JsonUtil.toJson(request.getActivityDescriptionsHistory()), currentUserId);
//        verify(usersMetaDataDao, Mockito.times(0)).updateMeta(UserMetaKey.INKIND_DONATION_HST_ACTIVITY_DESCRIPTIONS.toString(), JsonUtil.toJson(request.getActivityDescriptionsHistory()), currentUserId);
    }

    /**
     * 测试用例：获取 inkind 分享链接
     */
    @Test
    void getInKindShareLink() {
        // 数据准备 -- 入参
        String userId = "U0001";
        String agencyId = "A0001";
        String lindUrl = "www.link.com";

        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        agencyModel.setName("test Agency");

        // 数据准备 -- 接口模拟
//        Mockito.when(userProvider.getCurrentUserId()).thenReturn(anyString());
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);

        InKindDonateLinkModel donateLinkModel = new InKindDonateLinkModel();
        donateLinkModel.setId("L0001");
        donateLinkModel.setCreateUserId(userId);
        donateLinkModel.setIsDeleted(false);
        donateLinkModel.setIsInactive(false);
        donateLinkModel.setTenantId("T0001");
//        donateLinkModel.setCreateAtUtc();
        donateLinkModel.setShareLink(lindUrl);
        // 调用失效
        inKindDonationService.inactiveInKindShareLink(userId, agencyId);
        // 调用生成
        inKindDonationService.generateDonateLink(userId);
        when(inkindDao.getShareLinkByUserId(userId)).thenReturn(new InKindDonateLinkModel());
        // 调用h获取
        inKindDonationService.getDonateLink(userId);
        verify(userProvider, times(1)).getAgencyByUserId(userId);
        verify(inkindDao, times(1)).getShareLinkByUserId(userId);

        when(inkindDao.getShareLinkByUserId(userId)).thenReturn(null);
        inKindDonationService.getDonateLink(userId);

        // 验证 -- 方法调用次数
        verify(userProvider, times(2)).getAgencyByUserId(userId);
        verify(inkindDao, times(2)).getShareLinkByUserId(userId);
    }


    /**
     * 测试用例：失效 inkind 分享链接
     */
    @Test
    void inactiveInKindShareLink() {
        // 数据准备 -- 入参
        String userId = "U0001";
        String agencyId = "A0001";

        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        agencyModel.setName("test Agency");
        // 调用
        inKindDonationService.inactiveInKindShareLink(userId, agencyId);
        // 验证 -- 方法调用次数
        verify(inkindDao, times(1)).inactiveShareLink(userId, agencyId);
    }

    /**
     * 测试用例：检查 inkind 链接是否失效
     */
    @Test
    void checkInKindShareLink() {
        // 数据准备 -- 入参
        String userId = "U0001";
        String agencyId = "A0001";
        String linkId = "L0001";

        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        agencyModel.setName("test Agency");

        InKindDonateLinkModel donateLinkModel = new InKindDonateLinkModel();
        donateLinkModel.setId("L0001");
        donateLinkModel.setIsInactive(false);

        UserModel userModel = new UserModel();
        userModel.setId(userId);

        // 数据准备 -- 接口模拟
        when(inkindDao.getShareLinkByLinkId(linkId)).thenReturn(donateLinkModel);
        when(userDao.getUserById(anyString())).thenReturn(userModel);
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        inKindDonationService.checkShareLinkActive(linkId);
        // 验证 -- 方法调用次数
        verify(inkindDao, times(1)).getShareLinkByLinkId(linkId);
    }

    /**
     * 测试用例：获取捐赠PENDING列表
     */
    @Test
    void getInKindDonationPendingList() {
        // 数据准备 -- 入参
        InkindDonateListRequest request = new InkindDonateListRequest();
        request.setCurrentPage(1);
        request.setPageSize(10);

        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setAgencyId("A0001");
        currentUser.setAgencyName("test Agency");
        currentUser.setUsername("U0001");
        currentUser.setRole("SITE_ADMIN");

        List<InKindDonatePendingModel> pendingList = new ArrayList<>();
        InKindDonatePendingModel pendingModel = new InKindDonatePendingModel();
        pendingModel.setRelativePath("test");
        pendingModel.setRelationship("test");
        pendingModel.setCenterName("test");
        pendingModel.setRoleSignatureId("roleSignature");
        pendingList.add(pendingModel);

        InKindDonatePendingModel pendingModel1 = new InKindDonatePendingModel();
        pendingModel1.setRelativePath("test");
        pendingModel1.setRelationship("OTHER");
        pendingModel1.setCenterName("");
        pendingModel.setRoleSignatureId("roleSignature");
        pendingList.add(pendingModel1);

        InKindDonatePendingModel pendingModel2 = new InKindDonatePendingModel();
        pendingModel2.setRelativePath("test");
        pendingModel2.setRelationship("ENTITY");
        pendingModel2.setCenterName("test");
        pendingModel.setRoleSignatureId("roleSignature");
        pendingList.add(pendingModel2);

        List<MediaEntity> mediaEntityList = new ArrayList<>();
        MediaEntity mediaEntity1 = new MediaEntity();
        mediaEntity1.setPrivateFile(true);
        mediaEntity1.setId("roleSignature");
        mediaEntityList.add(mediaEntity1);

        MediaEntity mediaEntity2 = new MediaEntity();
        mediaEntity1.setPrivateFile(false);
        mediaEntity1.setId("roleSignature");
        mediaEntityList.add(mediaEntity2);

        when(userProvider.getCurrentUser()).thenReturn(currentUser);
        when(fileSystem.getPublicUrl(pendingModel.getRelativePath())).thenReturn("test");


        when(inkindDao.getDonateAppendReportForSiteAdmin(currentUser.getAgencyId(), new ArrayList<>(), new ArrayList<>(), "PENDING", InKindStageEnum.RATIFY, request.getCurrentPage(), request.getPageSize())).thenReturn(pendingList);
        // 数据准备 -- 接口模拟
        inKindDonationService.getDonatePendingApprovalList(request);

        // 验证 -- 方法调用次数
        verify(inkindDao, times(1)).getDonateAppendReportForSiteAdmin(currentUser.getAgencyId(), new ArrayList<>(), new ArrayList<>(), "PENDING", InKindStageEnum.RATIFY, request.getCurrentPage(), request.getPageSize());
    }


    /**
     * 测试用例：获取捐赠DISCARD列表
     */
    @Test
    void getInKindDonationDiscardList() {
        // 数据准备 -- 入参
        InkindDonateListRequest request = new InkindDonateListRequest();
        request.setCurrentPage(1);
        request.setPageSize(10);


        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setAgencyId("A0001");
        currentUser.setAgencyName("test Agency");
        currentUser.setUsername("U0001");
        currentUser.setRole("SITE_ADMIN");


        List<InKindDonatePendingModel> pendingList = new ArrayList<>();
        InKindDonatePendingModel pendingModel = new InKindDonatePendingModel();
        pendingModel.setRelativePath("test");
        pendingModel.setRelationship("test");
        pendingModel.setCenterName("test");
        pendingList.add(pendingModel);

        InKindDonatePendingModel pendingModel1 = new InKindDonatePendingModel();
        pendingModel1.setRelativePath("test");
        pendingModel1.setRelationship("OTHER");
        pendingModel1.setCenterName("");
        pendingList.add(pendingModel1);

        InKindDonatePendingModel pendingModel2 = new InKindDonatePendingModel();
        pendingModel2.setRelativePath("test");
        pendingModel2.setRelationship("ENTITY");
        pendingModel2.setCenterName("test");
        pendingList.add(pendingModel2);

        when(userProvider.getCurrentUser()).thenReturn(currentUser);
        when(fileSystem.getPublicUrl(pendingModel.getRelativePath())).thenReturn("test");
        when(inkindDao.getDonateAppendReport(currentUser.getAgencyId(), new ArrayList<>(), "DISCARD", InKindStageEnum.RATIFY, request.getCurrentPage(), request.getPageSize())).thenReturn(pendingList);

        // 数据准备 -- 接口模拟
        inKindDonationService.getDiscardDonateList(request);
        // 验证 -- 方法调用次数
        verify(inkindDao, times(1)).getDonateAppendReport(currentUser.getAgencyId(), new ArrayList<>(), "DISCARD", InKindStageEnum.RATIFY, request.getCurrentPage(), request.getPageSize());
    }


    /**
     * 测试用例：获取捐赠DISCARD列表
     */
    @Test
    void getApprovedDonateList() {
        // 数据准备 -- 入参
        InkindDonateListRequest request = new InkindDonateListRequest();
        request.setCurrentPage(1);
        request.setPageSize(10);
        request.setStartTime("2023-01-01");
        request.setEndTime("2024-01-01");

        String enrollmentId = "E0001";

        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setAgencyId("A0001");
        currentUser.setAgencyName("test Agency");
        currentUser.setUsername("U0001");
        currentUser.setRole("SITE_ADMIN");

        List<String> enrollmentIds = new ArrayList<>();
        enrollmentIds.add(enrollmentId);

        List<InKindDonatePendingModel> pendingList = new ArrayList<>();
        InKindDonatePendingModel pendingModel = new InKindDonatePendingModel();
        pendingModel.setRelativePath("test");
        pendingModel.setRelationship("test");
        pendingModel.setCenterName("test");
        pendingModel.setRole("IN_SYSTEM");
        pendingModel.setEnrollmentId("E0001");
        pendingList.add(pendingModel);

        InKindDonatePendingModel pendingModel1 = new InKindDonatePendingModel();
        pendingModel1.setRelativePath("test");
        pendingModel1.setRelationship("OTHER");
        pendingModel1.setCenterName("");
        pendingModel1.setRole("OUT_SYSTEM");
        pendingList.add(pendingModel1);

        InKindDonatePendingModel pendingModel2 = new InKindDonatePendingModel();
        pendingModel2.setRelativePath("test");
        pendingModel2.setRelationship("ENTITY");
        pendingModel2.setCenterName("test");
        pendingModel2.setRole("IN_SYSTEM");
        pendingList.add(pendingModel2);

        InKindDonatePendingModel pendingModel3 = new InKindDonatePendingModel();
        pendingModel3.setRelativePath("test");
        pendingModel3.setRelationship("");
        pendingModel3.setEnrollmentId("E0001");
        pendingModel3.setCenterName("test");
        pendingModel3.setRole("IN_SYSTEM");
        pendingList.add(pendingModel3);

        List<UserModel> parents = new ArrayList<>();
        UserModel userModel = new UserModel();
        userModel.setFirstName("f");
        userModel.setLastName("l");
        userModel.setEmail("email");
        userModel.setId("U0001");
        parents.add(userModel);

        when(userProvider.getCurrentUser()).thenReturn(currentUser);
        when(fileSystem.getPublicUrl(pendingModel.getRelativePath())).thenReturn("test");
        when(inkindDao.getDonateAppendReportByDate(currentUser.getAgencyId(), new ArrayList<>(), InkindApproveStatus.APPROVED.toString(), InKindStageEnum.EFFECTIVE, request.getCurrentPage(), request.getPageSize(), request.getStartTime(), request.getEndTime())).thenReturn(pendingList);
        // 数据准备 -- 接口模拟
        inKindDonationService.getApprovedDonateList(request);
        // 验证 -- 方法调用次数
        verify(inkindDao, times(1)).getDonateAppendReportByDate(currentUser.getAgencyId(), new ArrayList<>(), InkindApproveStatus.APPROVED.toString(), InKindStageEnum.EFFECTIVE, request.getCurrentPage(), request.getPageSize(), request.getStartTime(), request.getEndTime());
    }


    /**
     * 测试用例：删除弃置
     */
    @Test
    void deleteDiscardDonate() {
        // 数据准备 -- 入参
        String userId = "U0001";
        String agencyId = "A0001";

        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        agencyModel.setName("test Agency");

        InKindDonateApproveRequest request = new InKindDonateApproveRequest();
        request.setStatus("PENDING");
        request.setApproveSignatureId("S0001");
        request.setApproveUserId(userId);
        request.setApproveUserName("test user");
        request.setDonateIds(Arrays.asList("D0001"));
        request.setRememberSignature(true);

        List<InkindReportAppendEntity> inkindReportAppendEntityList = new ArrayList<>();
        InkindReportAppendEntity inkindReportAppendEntity = new InkindReportAppendEntity();
        inkindReportAppendEntity.setAgencyId(agencyId);
        inkindReportAppendEntity.setId("D0001");
        inkindReportAppendEntity.setApproveStatus("DISCARD");
        inkindReportAppendEntityList.add(inkindReportAppendEntity);
        when(inkindDao.getAppendReportByIds(request.getDonateIds())).thenReturn(inkindReportAppendEntityList);
        inKindDonationService.deleteDiscardDonate(request);
        verify(inkindDao, times(1)).batchDeletedDonateAppendReport(inkindReportAppendEntityList);
    }

    /**
     * 测试用例：生成 inkind 分享链接
     */
    @Test
    void generateInKindShareLink() {
        // 数据准备 -- 入参
        String userId = "U0001";
        String agencyId = "A0001";

        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        agencyModel.setName("test Agency");

        // 数据准备 -- 接口模拟
//        Mockito.when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        // 调用
        inKindDonationService.generateDonateLink(userId);
        // 验证 -- 方法调用次数
        verify(inkindDao, times(1)).createDonateLink(any(InKindDonateLinkModel.class));
    }


    /**
     * 删除弃置列表数据
     */
    @Test
    void testDeleteDiscardDonate() {
        InKindDonateApproveRequest request = new InKindDonateApproveRequest();
        request.setDonateIds(Arrays.asList("D0001"));
        request.setApproveUserId("U0001");

        List<InkindReportAppendEntity> inKindReportAppendEntities = new ArrayList<>();
        InkindReportAppendEntity inkindReportAppendEntity = new InkindReportAppendEntity();
        inkindReportAppendEntity.setId("D0001");
        inkindReportAppendEntity.setDeleted(false);
        inKindReportAppendEntities.add(inkindReportAppendEntity);
        when(inkindDao.getAppendReportByIds(request.getDonateIds())).thenReturn(inKindReportAppendEntities);
        inKindDonationService.deleteDiscardDonate(request);
        verify(inkindDao, times(1)).batchDeletedDonateAppendReport(inKindReportAppendEntities);
    }


    /**
     * 待审核数
     */
    @Test
    void getDonatePendingApprovalRedDot() {
        String userId = "U0001";
        String agencyId = "A0001";

        List<AgencyModel> agencyModelList = new ArrayList<>();
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        agencyModel.setName("test Agency");
        agencyModelList.add(agencyModel);

        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole("AGENCY_ADMIN");

        InkindDonateListRequest request = new InkindDonateListRequest();
        request.setSelectGroups(new ArrayList<>());
        // 数据准备 -- 接口模拟
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(inkindDao.getDonateAppendReportCountByAgencyId(agencyId, "PENDING", InKindStageEnum.RATIFY)).thenReturn(0);
        // when(userProvider.checkUser(userId)).thenReturn(userEntity);
        // when(groupDao.getGroupByAgencyAdmin(userId)).thenReturn(new ArrayList<>());
        // when(userDao.getAgencyByAgencyAdminId(userEntity.getId())).thenReturn(agencyModelList);
        when(inKindCommonService.getPendingNum(request.getSelectGroups())).thenReturn(0);
//        Mockito.when(userDao.getAgencyByAgencyAdminId(userEntity.getId()).get(0).getId()).thenReturn(agencyId);
        inKindDonationService.getDonatePendingApprovalRedDot(request);
        verify(inkindDao, times(1)).getDonateAppendReportCountByAgencyId(agencyId, "PENDING", InKindStageEnum.RATIFY);
    }


    /**
     * 测试获取捐赠列表
     */
    @Disabled
    void testGetDonateInkindList() {
        // 数据准备
        String userId = "U0001";
        String type = "AT_HOME";
        String agencyId = "A0001";
        String startDate = "2020-01-01";
        String endDate = "2020-01-31";
        String role = "AGENCY_ADMIN";
        String donateId = "D0001";

        List<AgencyModel> agencyModelList = new ArrayList<>();
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        agencyModel.setName("test Agency");
        agencyModelList.add(agencyModel);

        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole(role);

        List<InKindDonate> donates = new ArrayList<>();
        InKindDonate donate = new InKindDonate();
        donate.setId(donateId);
        donate.setAgencyId(agencyId);
        donate.setAgencyName("test Agency");
        donate.setCenterId("C0001");
        donate.setCenterName("test Center");
        donate.setGroupId("G0001");
        donate.setGroupName("test Group");
        donate.setDonorRelationship(role);
        donate.setApproveUserId(userId);
        donate.setApproveUserRelationship(role);
        donate.setReportType("NEW");
        donate.setUnit("MINUTE");
        donate.setRateUnit("MINUTE");
        donate.setRoleSignatureId("roleSignature");
        donates.add(donate);

        List<InKindDonatePendingModel> donateModels = new ArrayList<>();
        InKindDonatePendingModel donateModel = new InKindDonatePendingModel();
        donateModel.setId(donateId);
        donateModel.setLinkId("L0001");
        donateModels.add(donateModel);


        List<MediaEntity> mediaEntityList = new ArrayList<>();
        MediaEntity mediaEntity1 = new MediaEntity();
        mediaEntity1.setPrivateFile(true);
        mediaEntity1.setId("roleSignature");
        mediaEntityList.add(mediaEntity1);

        MediaEntity mediaEntity2 = new MediaEntity();
        mediaEntity1.setPrivateFile(false);
        mediaEntity1.setId("roleSignature");
        mediaEntityList.add(mediaEntity2);

        UserEntity user = new UserEntity();
        user.setId("U0001");
        user.setRole("Teacher");

        // 数据准备 -- 接口模拟
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.checkUser(userId)).thenReturn(userEntity);
        when(userDao.getAgencyByAgencyAdminId(userId)).thenReturn(agencyModelList);
        when(inkindDao.getDonateInkindByPageAndSelectCenters(type, agencyId, TimeUtil.parseDate(startDate), TimeUtil.parseDate(endDate), "", "", "0", "10", false, Arrays.asList("C0001"), Arrays.asList("G0001"), false, false, false, Arrays.asList("C0001"), user, false)).thenReturn(donates);
        when(inkindDao.getDonateAppendReportByIds(Arrays.asList(donateId))).thenReturn(donateModels);
        InkindDonateListResponse response = inKindDonationService.getDonateInkindList(userId, "", type, startDate, endDate, "", "", 1, 10, false, Arrays.asList("C0001"), Arrays.asList("G0001"), false, false, Arrays.asList("C0001"));

        // 断言
        assertNotNull(response);
    }


    /**
     * 测试提交 In-kind
     * <p>
     * case1: 机构管理员在（教师批准和管理员批准）TEACHER_APPROVE_AND_ADMIN_RATIFY 审核模式下
     * 不通过 web link 提交并记住签名。
     */
    @Test
    void testAddDonateInkindCase1() {
        // 准备数据
        InKindDonate inKindDonate = JsonUtil.fromJson("{\n" + "  \"type\": \"AT_HOME\",\n" + "  \"donateCenterId\": \"7396EC65-751D-4CE8-A191-33EBE3B97B86\",\n" + "  \"activityGroupId\": \"99F9EEDF-C800-4DED-AB45-6CC04299D068\",\n" + "  \"centerId\": \"C50DB6DB-46F4-4FDC-8AFF-273B17CD3172\",\n" + "  \"groupId\": \"8F6C66CD-4796-4E64-8EF4-1722DD9AAB82\",\n" + "  \"enrollmentId\": \"7E46953B-7AEF-4D4A-A00E-834F580DC6FB\",\n" + "  \"rateValue\": 0,\n" + "  \"rateUnit\": \"HOUR\",\n" + "  \"isCustom\": false,\n" + "  \"activityNumber\": \"1\",\n" + "  \"activityDescription\": \"https://www.baidu.com/ Apply changes to all activities with the same source, theme and group name.\",\n" + "  \"themeName\": \"Hyperlinks 1\",\n" + "  \"sourceName\": \"Hyperlinks\",\n" + "  \"domainName\": \"\",\n" + "  \"value\": \"11\",\n" + "  \"unit\": \"minute\",\n" + "  \"activityDateStr\": \"2023-07-25\",\n" + "  \"approveSignatureId\": \"dc68fd97-20e1-4680-816a-31c8d1a85e54\",\n" + "  \"approveUserId\": \"4E8EAB68-A2A8-426C-A2AC-FD2EA6CF05A9\",\n" + "  \"approveUserName\": \"Ms. How zhang\",\n" + "  \"money\": \"0.00\",\n" + "  \"roleId\": \"765AB168-46D7-45E7-B1B4-C00B66C0378A\",\n" + "  \"roleSignatureId\": null,\n" + "  \"mdeias\": [],\n" + "  \"donorType\": \"PARENT\",\n" + "  \"rememberSignature\": true\n" + "}", InKindDonate.class);

        String userId = inKindDonate.getRoleId();
        String role = "AGENCY_ADMIN";
        String agencyId = "A0001";
        InkindActivityTypeEntity activityType = new InkindActivityTypeEntity();
        activityType.setId("T0001");

        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole(role);

        UserModel userModel = new UserModel();
        userModel.setId(userId);
        userModel.setRole(role);

        AuthUserDetails authUserDetails = new AuthUserDetails();
        authUserDetails.setAgencyId(agencyId);
        authUserDetails.setRole(role);

        AgencyMetadata metadata = new AgencyMetadata();
        metadata.setMetaValue("TEACHER_APPROVE_AND_ADMIN_RATIFY");

        InkindActivityGroupEntity activityGroup = new InkindActivityGroupEntity();
        activityGroup.setLimitValue(true);
        activityGroup.setUnit("MILE");
        activityGroup.setValue(BigDecimal.valueOf(20));

        List<InkindSchoolYearEntity> schoolYears = new ArrayList<>();
        InkindSchoolYearEntity schoolYearEntity = new InkindSchoolYearEntity();
        schoolYearEntity.setId("S0001");
        schoolYearEntity.setStartDate(TimeUtil.parseDate("2020-01-02"));
        schoolYearEntity.setShowRate(true);
        schoolYears.add(schoolYearEntity);

        // 调用 getAgencyId() 的 mock 方法
        mockGetAgencyId(userEntity, agencyId);

        // 模拟接口
        // when(inkindDao.getSchoolYearByAgencyId(agencyId)).thenReturn(schoolYears);
        when(inKindCommonService.getCurrentSchoolYear(agencyId)).thenReturn(schoolYearEntity);
        when(inkindDao.getActivityTypeBySchoolYearIdAndType(any(), any())).thenReturn(activityType);
        when(agencyMetadataMapper.get(any(), any())).thenReturn(metadata);
        when(userProvider.getCurrentUser()).thenReturn(authUserDetails);
        when(inkindDao.getActivityGroup(anyString(), anyString())).thenReturn(activityGroup);
        when(inkindDao.getInkindReport(any(), any(), any(), any(), any())).thenReturn(Collections.emptyList());
        doNothing().when(inkindDao).addDonateInkind(any(), any());
        doNothing().when(userDao).setMetaData(userId, UserMetaKey.INKIND_STAFF_SIGNATURE_ID.toString(), inKindDonate.getApproveSignatureId());
        // 调用方法
        when(userDao.getUserById(anyString())).thenReturn(userModel);
        InkindLimit inkindLimit = inKindDonationService.addDonateInkind(inKindDonate);

        // 验证结果
        Assertions.assertNotNull(inkindLimit);
        Assertions.assertEquals(BigDecimal.valueOf(20), inkindLimit.getWeekLimitDuration());
        Assertions.assertEquals(false, inkindLimit.isExceed());

        ArgumentCaptor<InKindDonate> arg = ArgumentCaptor.forClass(InKindDonate.class);
        verify(inkindDao, times(1)).addDonateInkind(arg.capture(), any());
        InKindDonate inKindDonateResp = arg.getValue();
        Assertions.assertEquals(inKindDonate.getApproveSignatureId(), inKindDonateResp.getApproveSignatureId());
        Assertions.assertEquals(inKindDonate.getType(), inKindDonateResp.getType());
        Assertions.assertEquals("A0001", inKindDonateResp.getAgencyId());
        Assertions.assertEquals("T0001", inKindDonateResp.getActivityTypeId());
        Assertions.assertEquals(inKindDonate.getGroupId(), inKindDonateResp.getGroupId());
        Assertions.assertEquals(inKindDonate.getCenterId(), inKindDonateResp.getCenterId());
        Assertions.assertEquals(inKindDonate.getActivityGroupId(), inKindDonateResp.getActivityGroupId());
        Assertions.assertEquals(inKindDonate.getEnrollmentId(), inKindDonateResp.getEnrollmentId());
        Assertions.assertEquals("0", inKindDonateResp.getRateValue().toString());
        Assertions.assertEquals("HOUR", inKindDonateResp.getRateUnit());
        Assertions.assertEquals("1", inKindDonateResp.getActivityNumber());
        Assertions.assertEquals(inKindDonate.getActivityDescription(), inKindDonateResp.getActivityDescription());
        Assertions.assertEquals(inKindDonate.getThemeName(), inKindDonateResp.getThemeName());
        Assertions.assertEquals(inKindDonate.getSourceName(), inKindDonateResp.getSourceName());
        Assertions.assertEquals("11", inKindDonateResp.getValue().toString());
        Assertions.assertEquals(inKindDonate.getUnit(), inKindDonateResp.getUnit());
        Assertions.assertEquals("EFFECTIVE", inKindDonateResp.getStatus());
        Assertions.assertEquals("APPROVED", inKindDonateResp.getApproveStatus());
    }

    /**
     * 测试提交 In-kind
     * <p>
     * case2: 管理员在通过 web link 提交，不记住签名。
     */
    @Test
    void testAddDonateInkindCase2() {
        // 准备数据
        InKindDonate inKindDonate = JsonUtil.fromJson("{\n" + "  \"type\": \"AT_HOME\",\n" + "  \"donateCenterId\": \"7396EC65-751D-4CE8-A191-33EBE3B97B86\",\n" + "  \"activityGroupId\": \"99F9EEDF-C800-4DED-AB45-6CC04299D068\",\n" + "  \"centerId\": \"C50DB6DB-46F4-4FDC-8AFF-273B17CD3172\",\n" + "  \"groupId\": \"8F6C66CD-4796-4E64-8EF4-1722DD9AAB82\",\n" + "  \"enrollmentId\": \"7E46953B-7AEF-4D4A-A00E-834F580DC6FB\",\n" + "  \"rateValue\": 0,\n" + "  \"rateUnit\": \"HOUR\",\n" + "  \"isCustom\": false,\n" + "  \"activityNumber\": \"1\",\n" + "  \"activityDescription\": \"https://www.baidu.com/ Apply changes to all activities with the same source, theme and group name.\",\n" + "  \"themeName\": \"Hyperlinks 1\",\n" + "  \"sourceName\": \"Hyperlinks\",\n" + "  \"domainName\": \"\",\n" + "  \"value\": \"11\",\n" + "  \"unit\": \"minute\",\n" + "  \"activityDateStr\": \"2023-07-25\",\n" + "  \"approveSignatureId\": \"dc68fd97-20e1-4680-816a-31c8d1a85e54\",\n" + "  \"approveUserId\": \"4E8EAB68-A2A8-426C-A2AC-FD2EA6CF05A9\",\n" + "  \"approveUserName\": \"Ms. How zhang\",\n" + "  \"money\": \"0.00\",\n" + "  \"roleId\": \"765AB168-46D7-45E7-B1B4-C00B66C0378A\",\n" + "  \"roleSignatureId\": null,\n" + "  \"mdeias\": [],\n" + "  \"donorType\": \"PARENT\",\n" + "  \"rememberSignature\": false\n" + "}", InKindDonate.class);
        inKindDonate.setLinkId("L0001");
        InKindDonateLinkModel linkModel = new InKindDonateLinkModel();
        linkModel.setIsInactive(false);

        String userId = inKindDonate.getRoleId();
        String role = "AGENCY_ADMIN";
        String agencyId = "A0001";
        InkindActivityTypeEntity activityType = new InkindActivityTypeEntity();
        activityType.setId("T0001");

        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole(role);

        UserModel userModel = new UserModel();
        userModel.setId(userId);
        userModel.setRole(role);

        AuthUserDetails authUserDetails = new AuthUserDetails();
        authUserDetails.setAgencyId(agencyId);
        authUserDetails.setRole(role);

        AgencyMetaDataEntity metaDataEntity = new AgencyMetaDataEntity();
        metaDataEntity.setMetaValue("1");

        InkindActivityGroupEntity activityGroup = new InkindActivityGroupEntity();
        activityGroup.setLimitValue(true);
        activityGroup.setUnit("MILE");
        activityGroup.setValue(BigDecimal.valueOf(20));

        List<InkindSchoolYearEntity> schoolYears = new ArrayList<>();
        InkindSchoolYearEntity schoolYearEntity = new InkindSchoolYearEntity();
        schoolYearEntity.setId("S0001");
        schoolYearEntity.setStartDate(TimeUtil.parseDate("2020-01-01"));
        schoolYearEntity.setShowRate(true);
        schoolYears.add(schoolYearEntity);

        // 调用 getAgencyId() 的 mock 方法
        mockGetAgencyId(userEntity, agencyId);

        // 模拟接口

        when(inkindDao.getShareLinkByLinkId(anyString())).thenReturn(linkModel);
        when(agencyDao.getMeta(agencyId, AgencyMetaKey.INKIND_OPEN.toString())).thenReturn(metaDataEntity);
        when(agencyDao.getMeta(agencyId, AgencyMetaKey.IN_KIND_GOALS_ADD_DONATION_DATA_OPEN.toString())).thenReturn(null);
        // when(inkindDao.getSchoolYearByAgencyId(agencyId)).thenReturn(schoolYears);
        when(inKindCommonService.getCurrentSchoolYear(agencyId)).thenReturn(schoolYearEntity);
        when(inkindDao.getActivityTypeBySchoolYearIdAndType(any(), any())).thenReturn(activityType);
        when(inkindDao.getActivityGroup(anyString(), anyString())).thenReturn(activityGroup);
        when(inkindDao.getInkindReport(any(), any(), any(), any(), any())).thenReturn(Collections.emptyList());
        doNothing().when(inkindDao).addDonateInkind(any(), any());
        doNothing().when(userDao).setMetaData(userId, UserMetaKey.INKIND_STAFF_SIGNATURE_ID.toString(), "");
        // 调用方法
        when(userDao.getUserById(anyString())).thenReturn(userModel);
        InkindLimit inkindLimit = inKindDonationService.addDonateInkind(inKindDonate);

        // 验证结果
        Assertions.assertNotNull(inkindLimit);
        Assertions.assertEquals(BigDecimal.valueOf(20), inkindLimit.getWeekLimitDuration());
        Assertions.assertEquals(false, inkindLimit.isExceed());

        ArgumentCaptor<InKindDonate> arg = ArgumentCaptor.forClass(InKindDonate.class);
        verify(inkindDao, times(1)).addDonateInkind(arg.capture(), any());
        InKindDonate inKindDonateResp = arg.getValue();
        Assertions.assertEquals(inKindDonate.getApproveSignatureId(), inKindDonateResp.getApproveSignatureId());
        Assertions.assertEquals(inKindDonate.getType(), inKindDonateResp.getType());
        Assertions.assertEquals("A0001", inKindDonateResp.getAgencyId());
        Assertions.assertEquals("T0001", inKindDonateResp.getActivityTypeId());
        Assertions.assertEquals(inKindDonate.getGroupId(), inKindDonateResp.getGroupId());
        Assertions.assertEquals(inKindDonate.getCenterId(), inKindDonateResp.getCenterId());
        Assertions.assertEquals(inKindDonate.getActivityGroupId(), inKindDonateResp.getActivityGroupId());
        Assertions.assertEquals(inKindDonate.getEnrollmentId(), inKindDonateResp.getEnrollmentId());
        Assertions.assertEquals("0", inKindDonateResp.getRateValue().toString());
        Assertions.assertEquals("HOUR", inKindDonateResp.getRateUnit());
        Assertions.assertEquals("1", inKindDonateResp.getActivityNumber());
        Assertions.assertEquals(inKindDonate.getActivityDescription(), inKindDonateResp.getActivityDescription());
        Assertions.assertEquals(inKindDonate.getThemeName(), inKindDonateResp.getThemeName());
        Assertions.assertEquals(inKindDonate.getSourceName(), inKindDonateResp.getSourceName());
        Assertions.assertEquals("11", inKindDonateResp.getValue().toString());
        Assertions.assertEquals(inKindDonate.getUnit(), inKindDonateResp.getUnit());
        Assertions.assertEquals("RATIFY", inKindDonateResp.getStatus());
        Assertions.assertEquals("PENDING", inKindDonateResp.getApproveStatus());
    }

    /**
     * 测试提交 In-kind
     * <p>
     * case3: 园长在（教师批准和管理员批准）TEACHER_APPROVE_AND_ADMIN_RATIFY 审核模式下
     * 不通过 web link 提交并记住签名.
     */
    @Test
    void testAddDonateInkindCase3() {
        // 准备数据
        InKindDonate inKindDonate = JsonUtil.fromJson("{\n" + "  \"type\": \"AT_HOME\",\n" + "  \"donateCenterId\": \"7396EC65-751D-4CE8-A191-33EBE3B97B86\",\n" + "  \"activityGroupId\": \"99F9EEDF-C800-4DED-AB45-6CC04299D068\",\n" + "  \"centerId\": \"C50DB6DB-46F4-4FDC-8AFF-273B17CD3172\",\n" + "  \"groupId\": \"8F6C66CD-4796-4E64-8EF4-1722DD9AAB82\",\n" + "  \"enrollmentId\": \"7E46953B-7AEF-4D4A-A00E-834F580DC6FB\",\n" + "  \"rateValue\": 0,\n" + "  \"rateUnit\": \"HOUR\",\n" + "  \"isCustom\": false,\n" + "  \"activityNumber\": \"1\",\n" + "  \"activityDescription\": \"https://www.baidu.com/ Apply changes to all activities with the same source, theme and group name.\",\n" + "  \"themeName\": \"Hyperlinks 1\",\n" + "  \"sourceName\": \"Hyperlinks\",\n" + "  \"domainName\": \"\",\n" + "  \"value\": \"11\",\n" + "  \"unit\": \"minute\",\n" + "  \"activityDateStr\": \"2023-07-25\",\n" + "  \"approveSignatureId\": \"dc68fd97-20e1-4680-816a-31c8d1a85e54\",\n" + "  \"approveUserId\": \"4E8EAB68-A2A8-426C-A2AC-FD2EA6CF05A9\",\n" + "  \"approveUserName\": \"Ms. How zhang\",\n" + "  \"money\": \"0.00\",\n" + "  \"roleId\": \"765AB168-46D7-45E7-B1B4-C00B66C0378A\",\n" + "  \"roleSignatureId\": null,\n" + "  \"mdeias\": [],\n" + "  \"donorType\": \"PARENT\",\n" + "  \"rememberSignature\": true\n" + "}", InKindDonate.class);

        String userId = inKindDonate.getRoleId();
        String role = "SITE_ADMIN";
        String agencyId = "A0001";
        InkindActivityTypeEntity activityType = new InkindActivityTypeEntity();
        activityType.setId("T0001");

        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole(role);

        UserModel userModel = new UserModel();
        userModel.setId(userId);
        userModel.setRole(role);

        AuthUserDetails authUserDetails = new AuthUserDetails();
        authUserDetails.setAgencyId(agencyId);
        authUserDetails.setRole(role);

        AgencyMetadata metadata = new AgencyMetadata();
        metadata.setMetaValue("TEACHER_APPROVE_AND_ADMIN_RATIFY");

        InkindActivityGroupEntity activityGroup = new InkindActivityGroupEntity();
        activityGroup.setLimitValue(true);
        activityGroup.setUnit("MILE");
        activityGroup.setValue(BigDecimal.valueOf(20));

        List<InkindSchoolYearEntity> schoolYears = new ArrayList<>();
        InkindSchoolYearEntity schoolYearEntity = new InkindSchoolYearEntity();
        schoolYearEntity.setId("S0001");
        schoolYearEntity.setStartDate(TimeUtil.parseDate("2020-01-07"));
        schoolYearEntity.setShowRate(true);
        schoolYears.add(schoolYearEntity);

        // 调用 getAgencyId() 的 mock 方法
        mockGetAgencyId(userEntity, agencyId);

        // 模拟接口
        // when(inkindDao.getSchoolYearByAgencyId(agencyId)).thenReturn(schoolYears);
        when(inKindCommonService.getCurrentSchoolYear(agencyId)).thenReturn(schoolYearEntity);
        when(inkindDao.getActivityTypeBySchoolYearIdAndType(any(), any())).thenReturn(activityType);
        when(agencyMetadataMapper.get(any(), any())).thenReturn(metadata);
        when(userProvider.getCurrentUser()).thenReturn(authUserDetails);
        when(inkindDao.getActivityGroup(anyString(), anyString())).thenReturn(activityGroup);
        when(inkindDao.getInkindReport(any(), any(), any(), any(), any())).thenReturn(Collections.emptyList());
        doNothing().when(inkindDao).addDonateInkind(any(), any());
        doNothing().when(userDao).setMetaData(userId, UserMetaKey.INKIND_STAFF_SIGNATURE_ID.toString(), inKindDonate.getApproveSignatureId());
        // 调用方法
        when(userDao.getUserById(anyString())).thenReturn(userModel);
        InkindLimit inkindLimit = inKindDonationService.addDonateInkind(inKindDonate);

        // 验证结果
        Assertions.assertNotNull(inkindLimit);
        Assertions.assertEquals(BigDecimal.valueOf(20), inkindLimit.getWeekLimitDuration());
        Assertions.assertEquals(false, inkindLimit.isExceed());

        ArgumentCaptor<InKindDonate> arg = ArgumentCaptor.forClass(InKindDonate.class);
        verify(inkindDao, times(1)).addDonateInkind(arg.capture(), any());
        InKindDonate inKindDonateResp = arg.getValue();
        Assertions.assertEquals(inKindDonate.getApproveSignatureId(), inKindDonateResp.getApproveSignatureId());
        Assertions.assertEquals(inKindDonate.getType(), inKindDonateResp.getType());
        Assertions.assertEquals("A0001", inKindDonateResp.getAgencyId());
        Assertions.assertEquals("T0001", inKindDonateResp.getActivityTypeId());
        Assertions.assertEquals(inKindDonate.getGroupId(), inKindDonateResp.getGroupId());
        Assertions.assertEquals(inKindDonate.getCenterId(), inKindDonateResp.getCenterId());
        Assertions.assertEquals(inKindDonate.getActivityGroupId(), inKindDonateResp.getActivityGroupId());
        Assertions.assertEquals(inKindDonate.getEnrollmentId(), inKindDonateResp.getEnrollmentId());
        Assertions.assertEquals("0", inKindDonateResp.getRateValue().toString());
        Assertions.assertEquals("HOUR", inKindDonateResp.getRateUnit());
        Assertions.assertEquals("1", inKindDonateResp.getActivityNumber());
        Assertions.assertEquals(inKindDonate.getActivityDescription(), inKindDonateResp.getActivityDescription());
        Assertions.assertEquals(inKindDonate.getThemeName(), inKindDonateResp.getThemeName());
        Assertions.assertEquals(inKindDonate.getSourceName(), inKindDonateResp.getSourceName());
        Assertions.assertEquals("11", inKindDonateResp.getValue().toString());
        Assertions.assertEquals(inKindDonate.getUnit(), inKindDonateResp.getUnit());
        Assertions.assertEquals("EFFECTIVE", inKindDonateResp.getStatus());
        Assertions.assertEquals("APPROVED", inKindDonateResp.getApproveStatus());
    }

    /**
     * 测试提交 In-kind
     * <p>
     * case4: 老师在（教师批准和管理员批准）TEACHER_APPROVE_AND_ADMIN_RATIFY 审核模式下
     * 不通过 web link 提交并记住签名。
     */
    @Test
    void testAddDonateInkindCase4() {
        // 准备数据
        InKindDonate inKindDonate = JsonUtil.fromJson("{\n" + "  \"type\": \"AT_HOME\",\n" + "  \"donateCenterId\": \"7396EC65-751D-4CE8-A191-33EBE3B97B86\",\n" + "  \"activityGroupId\": \"99F9EEDF-C800-4DED-AB45-6CC04299D068\",\n" + "  \"centerId\": \"C50DB6DB-46F4-4FDC-8AFF-273B17CD3172\",\n" + "  \"groupId\": \"8F6C66CD-4796-4E64-8EF4-1722DD9AAB82\",\n" + "  \"enrollmentId\": \"7E46953B-7AEF-4D4A-A00E-834F580DC6FB\",\n" + "  \"rateValue\": 0,\n" + "  \"rateUnit\": \"HOUR\",\n" + "  \"isCustom\": false,\n" + "  \"activityNumber\": \"1\",\n" + "  \"activityDescription\": \"https://www.baidu.com/ Apply changes to all activities with the same source, theme and group name.\",\n" + "  \"themeName\": \"Hyperlinks 1\",\n" + "  \"sourceName\": \"Hyperlinks\",\n" + "  \"domainName\": \"\",\n" + "  \"value\": \"11\",\n" + "  \"unit\": \"minute\",\n" + "  \"activityDateStr\": \"2023-07-25\",\n" + "  \"approveSignatureId\": \"dc68fd97-20e1-4680-816a-31c8d1a85e54\",\n" + "  \"approveUserId\": \"4E8EAB68-A2A8-426C-A2AC-FD2EA6CF05A9\",\n" + "  \"approveUserName\": \"Ms. How zhang\",\n" + "  \"money\": \"0.00\",\n" + "  \"roleId\": \"765AB168-46D7-45E7-B1B4-C00B66C0378A\",\n" + "  \"roleSignatureId\": null,\n" + "  \"mdeias\": [],\n" + "  \"donorType\": \"PARENT\",\n" + "  \"rememberSignature\": true\n" + "}", InKindDonate.class);

        String userId = inKindDonate.getRoleId();
        String role = "TEACHING_ASSISTANT";
        String agencyId = "A0001";
        InkindActivityTypeEntity activityType = new InkindActivityTypeEntity();
        activityType.setId("T0001");

        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole(role);

        UserModel userModel = new UserModel();
        userModel.setId(userId);
        userModel.setRole(role);

        AuthUserDetails authUserDetails = new AuthUserDetails();
        authUserDetails.setAgencyId(agencyId);
        authUserDetails.setRole(role);

        AgencyMetadata metadata = new AgencyMetadata();
        metadata.setMetaValue("TEACHER_APPROVE_AND_ADMIN_RATIFY");

        InkindActivityGroupEntity activityGroup = new InkindActivityGroupEntity();
        activityGroup.setLimitValue(true);
        activityGroup.setUnit("MILE");
        activityGroup.setValue(BigDecimal.valueOf(20));

        List<InkindSchoolYearEntity> schoolYears = new ArrayList<>();
        InkindSchoolYearEntity schoolYearEntity = new InkindSchoolYearEntity();
        schoolYearEntity.setId("S0001");
        schoolYearEntity.setStartDate(TimeUtil.parseDate("2020-01-06"));
        schoolYearEntity.setShowRate(true);
        schoolYears.add(schoolYearEntity);

        // 调用 getAgencyId() 的 mock 方法
        mockGetAgencyId(userEntity, agencyId);

        // 模拟接口
        // when(inkindDao.getSchoolYearByAgencyId(agencyId)).thenReturn(schoolYears);
        when(inKindCommonService.getCurrentSchoolYear(agencyId)).thenReturn(schoolYearEntity);
        when(inkindDao.getActivityTypeBySchoolYearIdAndType(any(), any())).thenReturn(activityType);
        when(agencyMetadataMapper.get(any(), any())).thenReturn(metadata);
        when(userProvider.getCurrentUser()).thenReturn(authUserDetails);
        when(inkindDao.getActivityGroup(anyString(), anyString())).thenReturn(activityGroup);
        when(inkindDao.getInkindReport(any(), any(), any(), any(), any())).thenReturn(Collections.emptyList());
        doNothing().when(inkindDao).addDonateInkind(any(), any());
        doNothing().when(userDao).setMetaData(userId, UserMetaKey.INKIND_STAFF_SIGNATURE_ID.toString(), inKindDonate.getApproveSignatureId());
        // 调用方法
        when(userDao.getUserById(anyString())).thenReturn(userModel);
        InkindLimit inkindLimit = inKindDonationService.addDonateInkind(inKindDonate);

        // 验证结果
        Assertions.assertNotNull(inkindLimit);
        Assertions.assertEquals(BigDecimal.valueOf(20), inkindLimit.getWeekLimitDuration());
        Assertions.assertEquals(false, inkindLimit.isExceed());

        ArgumentCaptor<InKindDonate> arg = ArgumentCaptor.forClass(InKindDonate.class);
        verify(inkindDao, times(1)).addDonateInkind(arg.capture(), any());
        InKindDonate inKindDonateResp = arg.getValue();
        Assertions.assertEquals(inKindDonate.getApproveSignatureId(), inKindDonateResp.getApproveSignatureId());
        Assertions.assertEquals(inKindDonate.getType(), inKindDonateResp.getType());
        Assertions.assertEquals("A0001", inKindDonateResp.getAgencyId());
        Assertions.assertEquals("T0001", inKindDonateResp.getActivityTypeId());
        Assertions.assertEquals(inKindDonate.getGroupId(), inKindDonateResp.getGroupId());
        Assertions.assertEquals(inKindDonate.getCenterId(), inKindDonateResp.getCenterId());
        Assertions.assertEquals(inKindDonate.getActivityGroupId(), inKindDonateResp.getActivityGroupId());
        Assertions.assertEquals(inKindDonate.getEnrollmentId(), inKindDonateResp.getEnrollmentId());
        Assertions.assertEquals("0", inKindDonateResp.getRateValue().toString());
        Assertions.assertEquals("HOUR", inKindDonateResp.getRateUnit());
        Assertions.assertEquals("1", inKindDonateResp.getActivityNumber());
        Assertions.assertEquals(inKindDonate.getActivityDescription(), inKindDonateResp.getActivityDescription());
        Assertions.assertEquals(inKindDonate.getThemeName(), inKindDonateResp.getThemeName());
        Assertions.assertEquals(inKindDonate.getSourceName(), inKindDonateResp.getSourceName());
        Assertions.assertEquals("11", inKindDonateResp.getValue().toString());
        Assertions.assertEquals(inKindDonate.getUnit(), inKindDonateResp.getUnit());
        Assertions.assertEquals("RATIFY", inKindDonateResp.getStatus());
        Assertions.assertEquals("PENDING", inKindDonateResp.getApproveStatus());
    }

    /**
     * 测试提交 In-kind
     * <p>
     * case5: 老师在（教师批准签名）TEACHER_APPROVE_SINGLE 审核模式下
     * 不通过 web link 提交并记住签名。
     */
    @Test
    void testAddDonateInkindCase5() {
        // 准备数据
        InKindDonate inKindDonate = JsonUtil.fromJson("{\n" + "  \"type\": \"AT_HOME\",\n" + "  \"donateCenterId\": \"7396EC65-751D-4CE8-A191-33EBE3B97B86\",\n" + "  \"activityGroupId\": \"99F9EEDF-C800-4DED-AB45-6CC04299D068\",\n" + "  \"centerId\": \"C50DB6DB-46F4-4FDC-8AFF-273B17CD3172\",\n" + "  \"groupId\": \"8F6C66CD-4796-4E64-8EF4-1722DD9AAB82\",\n" + "  \"enrollmentId\": \"7E46953B-7AEF-4D4A-A00E-834F580DC6FB\",\n" + "  \"rateValue\": 0,\n" + "  \"rateUnit\": \"HOUR\",\n" + "  \"isCustom\": false,\n" + "  \"activityNumber\": \"1\",\n" + "  \"activityDescription\": \"https://www.baidu.com/ Apply changes to all activities with the same source, theme and group name.\",\n" + "  \"themeName\": \"Hyperlinks 1\",\n" + "  \"sourceName\": \"Hyperlinks\",\n" + "  \"domainName\": \"\",\n" + "  \"value\": \"11\",\n" + "  \"unit\": \"minute\",\n" + "  \"activityDateStr\": \"2023-07-25\",\n" + "  \"approveSignatureId\": \"dc68fd97-20e1-4680-816a-31c8d1a85e54\",\n" + "  \"approveUserId\": \"4E8EAB68-A2A8-426C-A2AC-FD2EA6CF05A9\",\n" + "  \"approveUserName\": \"Ms. How zhang\",\n" + "  \"money\": \"0.00\",\n" + "  \"roleId\": \"765AB168-46D7-45E7-B1B4-C00B66C0378A\",\n" + "  \"roleSignatureId\": null,\n" + "  \"mdeias\": [],\n" + "  \"donorType\": \"PARENT\",\n" + "  \"rememberSignature\": true\n" + "}", InKindDonate.class);

        String userId = inKindDonate.getRoleId();
        String role = "TEACHING_ASSISTANT";
        String agencyId = "A0001";
        InkindActivityTypeEntity activityType = new InkindActivityTypeEntity();
        activityType.setId("T0001");

        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole(role);

        UserModel userModel = new UserModel();
        userModel.setId(userId);
        userModel.setRole(role);

        AuthUserDetails authUserDetails = new AuthUserDetails();
        authUserDetails.setAgencyId(agencyId);
        authUserDetails.setRole(role);

        AgencyMetadata metadata = new AgencyMetadata();
        metadata.setMetaValue("TEACHER_APPROVE_SINGLE");

        InkindActivityGroupEntity activityGroup = new InkindActivityGroupEntity();
        activityGroup.setLimitValue(true);
        activityGroup.setUnit("MILE");
        activityGroup.setValue(BigDecimal.valueOf(10));

        List<InkindSchoolYearEntity> schoolYears = new ArrayList<>();
        InkindSchoolYearEntity schoolYearEntity = new InkindSchoolYearEntity();
        schoolYearEntity.setId("S0001");
        schoolYearEntity.setStartDate(TimeUtil.parseDate("2020-01-01"));
        schoolYearEntity.setShowRate(true);
        schoolYears.add(schoolYearEntity);

        List<InKindDonate> donateList = new ArrayList<>();
        InKindDonate donate1 = new InKindDonate();
        donate1.setValue(BigDecimal.valueOf(10));
        InKindDonate donate2 = new InKindDonate();
        donate2.setValue(BigDecimal.valueOf(10));
        donateList.add(donate1);
        donateList.add(donate2);

        // 调用 getAgencyId() 的 mock 方法
        mockGetAgencyId(userEntity, agencyId);

        // 模拟接口
        // when(inkindDao.getSchoolYearByAgencyId(agencyId)).thenReturn(schoolYears);
        when(inKindCommonService.getCurrentSchoolYear(agencyId)).thenReturn(schoolYearEntity);
        when(inkindDao.getActivityTypeBySchoolYearIdAndType(any(), any())).thenReturn(activityType);
        when(agencyMetadataMapper.get(any(), any())).thenReturn(metadata);
        when(userProvider.getCurrentUser()).thenReturn(authUserDetails);
        when(inkindDao.getActivityGroup(anyString(), anyString())).thenReturn(activityGroup);
        when(inkindDao.getInkindReport(any(), any(), any(), any(), any())).thenReturn(donateList);
        doNothing().when(inkindDao).addDonateInkind(any(), any());
        doNothing().when(userDao).setMetaData(userId, UserMetaKey.INKIND_STAFF_SIGNATURE_ID.toString(), inKindDonate.getApproveSignatureId());
        // 调用方法
        when(userDao.getUserById(anyString())).thenReturn(userModel);
        InkindLimit inkindLimit = inKindDonationService.addDonateInkind(inKindDonate);

        // 验证结果
        Assertions.assertNotNull(inkindLimit);
        Assertions.assertEquals(BigDecimal.valueOf(10), inkindLimit.getWeekLimitDuration());
        Assertions.assertEquals(true, inkindLimit.isExceed());

        ArgumentCaptor<InKindDonate> arg = ArgumentCaptor.forClass(InKindDonate.class);
        verify(inkindDao, times(1)).addDonateInkind(arg.capture(), any());
        InKindDonate inKindDonateResp = arg.getValue();
        Assertions.assertEquals(inKindDonate.getApproveSignatureId(), inKindDonateResp.getApproveSignatureId());
        Assertions.assertEquals(inKindDonate.getType(), inKindDonateResp.getType());
        Assertions.assertEquals("A0001", inKindDonateResp.getAgencyId());
        Assertions.assertEquals("T0001", inKindDonateResp.getActivityTypeId());
        Assertions.assertEquals(inKindDonate.getGroupId(), inKindDonateResp.getGroupId());
        Assertions.assertEquals(inKindDonate.getCenterId(), inKindDonateResp.getCenterId());
        Assertions.assertEquals(inKindDonate.getActivityGroupId(), inKindDonateResp.getActivityGroupId());
        Assertions.assertEquals(inKindDonate.getEnrollmentId(), inKindDonateResp.getEnrollmentId());
        Assertions.assertEquals("0", inKindDonateResp.getRateValue().toString());
        Assertions.assertEquals("HOUR", inKindDonateResp.getRateUnit());
        Assertions.assertEquals("1", inKindDonateResp.getActivityNumber());
        Assertions.assertEquals(inKindDonate.getActivityDescription(), inKindDonateResp.getActivityDescription());
        Assertions.assertEquals(inKindDonate.getThemeName(), inKindDonateResp.getThemeName());
        Assertions.assertEquals(inKindDonate.getSourceName(), inKindDonateResp.getSourceName());
        Assertions.assertEquals("11", inKindDonateResp.getValue().toString());
        Assertions.assertEquals(inKindDonate.getUnit(), inKindDonateResp.getUnit());
        Assertions.assertEquals("EFFECTIVE", inKindDonateResp.getStatus());
        Assertions.assertEquals("IGNORE", inKindDonateResp.getApproveStatus());
    }

}

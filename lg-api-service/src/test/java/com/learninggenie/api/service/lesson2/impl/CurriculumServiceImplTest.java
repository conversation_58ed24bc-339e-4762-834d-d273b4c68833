package com.learninggenie.api.service.lesson2.impl;

import com.learninggenie.api.constant.RedisKeyPrefix;
import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.model.IdResponse;
import com.learninggenie.api.model.PageResponse;
import com.learninggenie.api.model.SuccessResponse;
import com.learninggenie.api.model.curriculum.*;
import com.learninggenie.api.model.curriculum.test.CreateUnitOverviewTestRequest;
import com.learninggenie.api.model.dll.DLLResourceRequest;
import com.learninggenie.api.model.dll.DLLSubjectModel;
import com.learninggenie.api.model.lesson2.*;
import com.learninggenie.api.model.lesson2.curriculum.*;
import com.learninggenie.api.model.lesson2.plan.ItemCategoryModel;
import com.learninggenie.api.model.lesson2.plan.ItemModel;
import com.learninggenie.api.model.lesson2.plan.UpdatePlanRequest;
import com.learninggenie.api.model.prompt.CreatePromptTestResponse;
import com.learninggenie.api.provider.*;
import com.learninggenie.api.security.AuthUserDetails;
import com.learninggenie.api.service.PortfolioService;
import com.learninggenie.api.service.UserService;
import com.learninggenie.api.service.lesson2.LessonService;
import com.learninggenie.api.service.lesson2.PlanService;
import com.learninggenie.api.service.lesson2.TranslateService;
import com.learninggenie.common.ai.service.OpenAIService;
import com.learninggenie.common.cache.CacheModel;
import com.learninggenie.common.cache.CacheService;
import com.learninggenie.common.data.dao.*;
import com.learninggenie.common.data.dao.dll.SubjectDao;
import com.learninggenie.common.data.dao.dll.SubjectMediaDao;
import com.learninggenie.common.data.dao.frameworks.FrameworkDao;
import com.learninggenie.common.data.dao.frameworks.MeasureDao;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.dao.lesson2.*;
import com.learninggenie.common.data.dao.lesson2.LessonDao;
import com.learninggenie.common.data.dao.medias.MediaEntityDao;
import com.learninggenie.common.data.dao.prompts.PromptDao;
import com.learninggenie.common.data.dao.prompts.PromptTestRecordDao;
import com.learninggenie.common.data.dao.prompts.PromptUsageObjectDao;
import com.learninggenie.common.data.dao.prompts.PromptUsageRecordDao;
import com.learninggenie.common.data.dao.users.MetaDataDao;
import com.learninggenie.common.data.entity.AgencyMetaDataEntity;
import com.learninggenie.common.data.entity.CenterEntity;
import com.learninggenie.common.data.entity.PageList;
import com.learninggenie.common.data.entity.UserProfileEntity;
import com.learninggenie.common.data.entity.frameworks.FrameworkEntity;
import com.learninggenie.common.data.entity.frameworks.MeasureEntity;
import com.learninggenie.common.data.entity.lesson2.LessonEntity;
import com.learninggenie.common.data.entity.lesson2.LessonMeasureEntity;
import com.learninggenie.common.data.entity.lesson2.LessonThemeEntity;
import com.learninggenie.common.data.entity.lesson2.curriculum.*;
import com.learninggenie.common.data.entity.lesson2.plan.*;
import com.learninggenie.common.data.entity.medias.MediaEntity;
import com.learninggenie.common.data.entity.prompt.PromptEntity;
import com.learninggenie.common.data.entity.prompt.PromptTestRecordEntity;
import com.learninggenie.common.data.entity.prompt.PromptUsageObjectEntity;
import com.learninggenie.common.data.entity.prompt.PromptUsageRecordEntity;
import com.learninggenie.common.data.enums.AgencyMetaKey;
import com.learninggenie.common.data.enums.UserRole;
import com.learninggenie.common.data.enums.lesson2.ClassroomType;
import com.learninggenie.common.data.enums.lesson2.CurriculumResourceType;
import com.learninggenie.common.data.enums.lesson2.CurriculumStatusEnum;
import com.learninggenie.common.data.enums.lesson2.CurriculumTypeEnum;
import com.learninggenie.common.data.enums.prompt.*;
import com.learninggenie.common.data.mapper.mybatisplus.lesson2.LessonsMediaExternalMapper;
import com.learninggenie.common.data.model.*;
import com.learninggenie.common.data.model.lesson2.CurriculumUnitEditRecordModel;
import com.learninggenie.common.data.model.lesson2.curriculum.CurriculumGenieModel;
import com.learninggenie.common.data.model.openai.completion.chat.ChatCompletionResult;
import com.learninggenie.common.filesystem.FileSystem;
import com.learninggenie.common.framwork.FrameworkProvider;
import com.learninggenie.common.utils.FileUtil;
import com.learninggenie.common.utils.JsonUtil;
import com.learninggenie.common.utils.TimeUtil;
import com.theokanning.openai.completion.chat.ChatCompletionChoice;
import com.theokanning.openai.completion.chat.ChatMessage;
import com.theokanning.openai.image.Image;
import org.json.JSONArray;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.Silent.class)
public class CurriculumServiceImplTest {

    @InjectMocks
    private CurriculumServiceImpl curriculumService;

    @Mock
    private CurriculumPlanApplyDao curriculumPlanApplyDao;

    @Mock
    private CurriculumAgeDao curriculumAgeDao;

    @Mock
    private FileSystem fileSystem;

    @Mock
    private DomainDao domainDao;

    @Mock
    private PlanService planService;

    @Mock
    private LessonsMediaExternalMapper mediaExternalMapper;

    @Mock
    private PlanUserDao planUserDao;

    @Mock
    private UserDaoImpl userDao;

    @Mock
    private CurriculumDao curriculumDao;

    @Mock
    private CurriculumDomainDao curriculumDomainDao;

    @Mock
    private CurriculumUnitDao curriculumUnitDao;

    @Mock
    private CurriculumCustomModuleRecordDao customModuleRecordDao;

    @Mock
    private CurriculumUnitPlanDao curriculumUnitPlanDao;

    @Mock
    private GroupDao groupDao;

    @Mock
    private PortfolioService portfolioService;

    @Mock
    private CenterDao centerDao;

    @Mock
    private FrameworkDao frameworkDao;

    @Mock
    private MeasureDao measureDao;

    @Mock
    private AgencyDao agencyDao;

    @Mock
    private com.learninggenie.common.messaging.EmailService emailService;

    @Mock
    private UserProvider userProvider;

    @Mock
    private UserService userService;

    @Mock
    private MetaDataDao usersMetaDataDao;

    @Mock
    private MediaEntityDao mediaDao;

    @Mock
    private PlanDao planDao;

    @Mock
    private PlanCornerItemDao planCornerItemDao;

    @Mock
    private PlanCategoryDao planCategoryDao;

    @Mock
    private PlanItemDao planItemDao;

    @Mock
    private PlanItemMeasureDao planItemMeasureDao;

    @Mock
    private LessonDao lessonDao;

    @Mock
    private SubjectDao subjectDao;

    @Mock
    private CurriculumMaterialDao curriculumMaterialDao;

    @Mock
    private CurriculumAttachmentDao curriculumAttachmentDao;

    @Mock
    private CurriculumBookDao curriculumBookDao;

    @Mock
    private CurriculumVocabularyDao curriculumVocabularyDao;

    @Mock
    private CurriculumUnitPlanMeasureDao curriculumUnitPlanMeasureDao;

    @Mock
    private LessonMeasureDao lessonMeasureDao;

    @Mock
    private SubjectMediaDao subjectMediaDao;

    @Mock
    private PlanCenterDao planCenterDao;

    @Mock
    private LessonService lessonService;

    @Mock
    private MediaEntityDao mediaEntityDao;

    @Mock
    private LessonsMediaExternalMapper lessonsMediaExternalMapper;

    @Mock
    private PlanInterpretDao planInterpretDao;

    @Mock
    private UnitPlannerService unitPlannerService;

    @Mock
    private LessonsCurriculumUnitEditRecordDao lessonsCurriculumUnitEditRecordDao;

    @Mock
    @Qualifier("redisCacheServiceImpl")
    private CacheService cacheService;

    @Mock
    private PromptProvider promptProvider;

    @Mock
    private FrameworkProvider frameworkProvider;

    @Mock
    private OpenAIProvider openAIProvider;

    @Mock
    private PromptUsageRecordDao promptUsageRecordDao;

    @Mock
    private PromptUsageObjectDao promptUsageObjectDao;

    @Mock
    private PromptDao promptDao;

    @Mock
    private LessonThemeDao lessonThemeDao;

    @Mock
    private OpenAIService openAIService;

    @Mock
    private PromptTestRecordDao promptTestRecordDao;

    @Mock
    private CurriculumProvider curriculumProvider;

    @Mock
    private MediaDao odlMediaDao;

    @Mock
    private TranslateService translateService;

    @Mock
    private TranslateProvider translateProviderImpl;

    @Mock
    private CurriculumPromptHistoryDao curriculumPromptHistoryDao;

    @Mock
    private CurriculumLearnerProfileDao curriculumLearnerProfileDao;

    @Mock
    private CurriculumLearnerProfileEditRecordDao curriculumLearnerProfileEditRecordDao;

    private static MockedStatic<FileUtil> fileUtilMockedStatic;


    @BeforeClass
    public static void beforeClass() {
        fileUtilMockedStatic = Mockito.mockStatic(FileUtil.class);
    }

    @AfterClass
    public static void afterClass() {
        fileUtilMockedStatic.close();
    }


    /**
     * 测试创建系列课程
     *
     * @return 操作成功响应
     */
    @Test
    public void create() {
        // 数据准备 -- 入参
        // 模拟用户的机构 Id
        AuthUserDetails user = new AuthUserDetails();
        // 获取当前用户 Id
        user.setAgencyId("4BD0392C-55D3-EB11-9C19-4CCC6ACF6129");

        user.setRole("admin");

        String userId = user.getId();
        user.setAgencyId("a001");
        Date utcNow = TimeUtil.getUtcNow();
        // 模拟系列课程实体
        String id = "1BFEE19D-875D-908E-3C38-61D6B3D78E59";
        CurriculumEntity curriculum = new CurriculumEntity();
        curriculum.setId(id);
        curriculum.setName("Curriculum ");
        curriculum.setStatus(CurriculumStatusEnum.DRAFT.toString());
        curriculum.setTenantId(user.getAgencyId());
        curriculum.setType(CurriculumTypeEnum.AGENCY.toString());
        curriculum.setCreateUserId(userId);
        curriculum.setCreateAtUtc(utcNow);
        curriculum.setUpdateAtUtc(utcNow);
        curriculum.setUnitsNum(1);
        curriculum.setWeeksNum(1);
        curriculum.setLessonActivitiesNum(0);
        ;
        // 模拟默认的单元
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setId(id);
        unit.setCurriculumId(id);
        unit.setNumber(1);
        unit.setCreateAtUtc(utcNow);
        unit.setUpdateAtUtc(utcNow);
        unit.setTenantId(user.getAgencyId());
        user.setRole(UserRole.AGENCY_OWNER.toString());

        // 创建默认单元下的周
        CurriculumUnitPlanEntity unitPlan = new CurriculumUnitPlanEntity();
        unitPlan.setId(id);
        unitPlan.setCurriculumId(id);
        unitPlan.setNumber(1);
        unitPlan.setUnitId(unit.getId());
        unitPlan.setTenantId(user.getAgencyId());
        unitPlan.setCreateAtUtc(utcNow);
        unitPlan.setUpdateAtUtc(utcNow);
        long draftCurriculumCount = 0L;
        // 数据模拟
        Mockito.when(userProvider.getCurrentUser()).thenReturn(user);
        Mockito.when(curriculumDao.getDraftCurriculumCount(userId)).thenReturn(draftCurriculumCount);
        Mockito.when(userProvider.getCurrentUser()).thenReturn(user);
//        Mockito.when(user.isAgencyAdmin()).thenReturn(false);
//        Mockito.when(user.isAgencyOwner()).thenReturn(true);

        // 调用
        curriculumService.create();
        // 验证,3个dao都执行一次
        Mockito.verify(curriculumDao, times(1)).save(any());// save应该被调用了一次
        Mockito.verify(curriculumUnitDao, times(1)).save(any());// save应该被调用了一次
        Mockito.verify(curriculumUnitPlanDao, times(1)).save(any());// save应该被调用了一次
    }

    /**
     * 测试更新系列课程
     *
     * @param
     * @return 操作成功响应
     */
    @Test
    public void testUpdate() {
        // 数据准备
        UpdateCurriculumRequest request = new UpdateCurriculumRequest();
        String curriculumId = "001";
        request.setId(curriculumId);
        request.setName("new name");
        request.setAgeGroupNames("new age group names");
        request.setDescription("new description");

        CurriculumEntity sourceCurriculum = new CurriculumEntity();
        sourceCurriculum.setAgeGroupNames("6");
        AuthUserDetails currentUser = new AuthUserDetails();
        // 数据模拟
        Mockito.when(curriculumDao.getCurriculumById(curriculumId)).thenReturn(sourceCurriculum);
        Mockito.when(userProvider.getCurrentUser()).thenReturn(currentUser);

        // 调用
        SuccessResponse response = curriculumService.update(request);

        // 验证更新一次
        verify(curriculumDao, times(1)).updateById(any());

        // 验证返回值
        assertEquals(response.getId(), curriculumId);
        assertTrue(response.isSuccess());
    }

    @Test
    public void testUpdateCurriculumFramework() {
        // 数据准备
        String curriculumId = "001";
        String frameworkId = "frameworkId";
        String oldFrameworkId = "oldFrameworkId";
        String planId = "planId";
        List<String> planIds = new ArrayList<>();
        planIds.add(planId);
        List<CurriculumUnitPlanEntity> curriculumUnitPlanEntities = new ArrayList<>();
        CurriculumUnitPlanEntity curriculumUnitPlanEntity = new CurriculumUnitPlanEntity();
        curriculumUnitPlanEntity.setPlanId(planId);
        curriculumUnitPlanEntities.add(curriculumUnitPlanEntity);
        // mock
        when(curriculumUnitPlanDao.getPlansByCurriculumId(curriculumId)).thenReturn(curriculumUnitPlanEntities);
        // 调用
        boolean changed = curriculumService.updateCurriculumFramework(curriculumId, frameworkId, oldFrameworkId);
        // 验证
        verify(planDao, times(1)).updatePlanFrameworkId(frameworkId, planIds);
        // 断言
        assertTrue(changed);
    }

    /**
     * 测试发布系列课程
     *
     * @param 、、 发布的系列课程请求体
     * @return 发布系列课程响应体
     */
    @Test
    public void testPublish() {
        // 数据准备
        PublishCurriculumRequest request = new PublishCurriculumRequest();
        request.setId("001");
        CurriculumEntity curriculum = new CurriculumEntity();
        curriculum.setId("002");
        curriculum.setAgeGroupNames("全部");
        curriculum.setStatus(CurriculumStatusEnum.DRAFT.toString());
        List<CurriculumUnitEntity> units = new ArrayList<>();
        List<CurriculumUnitPlanEntity> unitWeeks = new ArrayList<>();
        // 数据模拟
        Mockito.when(curriculumDao.getCurriculumById(request.getId())).thenReturn(curriculum);
        Mockito.when(curriculumUnitDao.getUnitsByCurriculumId(curriculum.getId())).thenReturn(units);
        Mockito.when(curriculumUnitPlanDao.getPlansByCurriculumId(curriculum.getId())).thenReturn(unitWeeks);

        // 调用
        curriculumService.publish(request);

        // update 应该被调用了一次
        Mockito.verify(curriculumDao, times(1)).getCurriculumById(request.getId());

    }

    /**
     * 测试编辑已发布的课程
     *
     * @param //系列课程 ID
     * @return 生成的新的系列课程 Id
     */
    @Test
    public void testEditPublishedCurriculum() {
        // 准备数据
        // 系列课程的id
        String id = "001";
        CurriculumEntity modifyingCurriculum = new CurriculumEntity();
        modifyingCurriculum.setId("4BD0392C-55D3-EB11-9C19-4CCC6ACF6129");
        // 数据模拟
        Mockito.when(cacheService.get(anyString())).thenReturn(null);
        Mockito.when(curriculumDao.getModifyingCurriculumBySourceId(id)).thenReturn(modifyingCurriculum);

        // 调用
        curriculumService.editPublishedCurriculum(id);
        // 搜索服务应该被调用了一次
        Mockito.verify(curriculumDao, times(1)).getModifyingCurriculumBySourceId(any());
        //  缓存应该被删除了一次
        Mockito.verify(cacheService, times(1)).delete(any());

    }

    /**
     * 移除系列课程
     *
     * @param 、、id 列课程的 Id
     * @return 操作成功响应
     */

    @Test
    public void testRemove() {
        // 准备数据
        // 系列课程的id
        String id = "001";
        CurriculumEntity curriculum = new CurriculumEntity();
        curriculum.setId("4BD0392C-55D3-EB11-9C19-4CCC6ACF6129");
        // 数据模拟
        Mockito.when(curriculumDao.getCurriculumById((anyString()))).thenReturn(curriculum);


        // 调用
        SuccessResponse successResponse = curriculumService.remove(id);
        // 搜索服务应该被调用了一次
        Mockito.verify(curriculumDao, times(1)).getCurriculumById(any());
        //  更新被删除了一次
        Mockito.verify(curriculumDao, times(1)).updateById(any());
        assertEquals(successResponse.isSuccess(), true); // 删除成功为true


    }

    /**
     * 测试恢复系列课程
     *
     * @param 、、id 列课程的 Id
     * @return 操作成功响应
     */
    @Test
    public void testRecovery() {
        // 准备数据
        // 系列课程的id
        String id = "001";
        CurriculumEntity curriculum = new CurriculumEntity();
        curriculum.setId("4BD0392C-55D3-EB11-9C19-4CCC6ACF6129");
        // 数据模拟
        Mockito.when(curriculumDao.getCurriculumById((anyString()))).thenReturn(curriculum);


        // 调用
        SuccessResponse successResponse = curriculumService.recovery(id);
        // 搜索服务应该被调用了一次
        Mockito.verify(curriculumDao, times(1)).getCurriculumById(any());
        //  更新被删除了一次
        Mockito.verify(curriculumDao, times(1)).updateById(any());
        assertEquals(successResponse.isSuccess(), true); // 恢复成功为true
    }

    /**
     * delete系列课程
     *
     * @param 、、id 列课程的 Id
     * @return 操作成功响应
     */

    @Test
    public void testDelete() {
        // 准备数据
        // 系列课程的id
        String id = "001";
        CurriculumEntity curriculum = new CurriculumEntity();
        curriculum.setId("4BD0392C-55D3-EB11-9C19-4CCC6ACF6129");
        // 数据模拟
        Mockito.when(curriculumDao.getCurriculumById((anyString()))).thenReturn(curriculum);


        // 调用
        SuccessResponse successResponse = curriculumService.delete(id);
        // 搜索服务应该被调用了一次
        Mockito.verify(curriculumDao, times(1)).getCurriculumById(any());
        //  更新被删除了一次
        Mockito.verify(curriculumDao, times(1)).updateById(any());
        assertEquals(successResponse.isSuccess(), true); // 删除成功为true


    }

    /**
     * 测试获取系列课程草稿
     *
     * @param ''pageSize  分页大小
     * @param ''pageNum   页码
     * @param ''ages      年龄
     * @param ''domainIds 领域
     * @param ''keyword   关键字
     * @return 系列课程草稿
     */
    @Test
    public void getDraftCurriculums() {
        // 数据准备
        Integer pageSize = 8;
        Integer pageNum = 1;
        String keyword = "number line";
        String domainIds = "4BD0392C-55D3-EB11-9C19-4CCC6ACF6129";
        String ages = "LIKES";
        String userId = "4BD0392C-55D3-EB11-9C19-4CCC6ACF6129";
        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setUsername("4BD0392C-55D3-EB11-9C19-4CCC6ACF6129");
        CurriculumEntity curriculumEntity = new CurriculumEntity();
        curriculumEntity.setId("4BD0392C-55D3-EB11-9C19-4CCC6ACF6129");
        PageList<CurriculumEntity> draftCurriculums = new PageList<>();
        draftCurriculums.setPageNum(1L);
        draftCurriculums.setPageSize(2L);
        draftCurriculums.setTotal(5L);
        // 数据模拟
        Mockito.when(userProvider.getCurrentUser()).thenReturn(currentUser);

        Mockito.when(curriculumDao.getDraftCurriculums(pageSize, pageNum, userId, ages, domainIds, keyword)).thenReturn(draftCurriculums);
        // 调用
        PageResponse<CurriculumResponse> response = curriculumService.getDraftCurriculums(pageSize, pageNum, ages, domainIds, keyword);

        // 验证
        assertEquals(response.getItems().size(), 0); // 应该存在0个系列课程草稿
        assertEquals(response.getTotal().longValue(), 5); // 系列课程草稿为2
    }

    /**
     * 测试获取机构系列课程
     *
     * @param ''pageSize  分页大小
     * @param ''pageNum   页码
     * @param ''ages      年龄
     * @param ''domainIds 领域
     * @param ''keyword   关键字
     * @return 机构系列课程
     */
    @Test
    public void testGetAgencyCurriculums() {
        // 数据准备
        Integer pageSize = 8;
        Integer pageNum = 1;
        String keyword = "number line";
        String domainIds = "4BD0392C-55D3-EB11-9C19-4CCC6ACF6129";
        String ages = "LIKES";
        String agencyId = "agencyId-mock";
        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setUsername("4BD0392C-55D3-EB11-9C19-4CCC6ACF6129");
        List<CurriculumEntity> curriculumEntities = new ArrayList<>();
        CurriculumEntity curriculumEntity = new CurriculumEntity();
        curriculumEntity.setId("4BD0392C-55D3-EB11-9C19-4CCC6ACF6129");
        curriculumEntities.add(curriculumEntity);
        PageList<CurriculumEntity> agencyCurriculums = PageList.from(curriculumEntities,Long.parseLong(String.valueOf(pageNum)),Long.parseLong(String.valueOf(pageSize)));


        // 数据模拟
        Mockito.when(userProvider.getCurrentUser()).thenReturn(currentUser);
        Mockito.when(agencyDao.getMeta(agencyId, AgencyMetaKey.CREATE_AGENCY_CURRICULUM_OPEN.toString())).thenReturn(null);
        Mockito.when(curriculumDao.getAgencyCurriculums(any(), any(), any(), any(), any(), any())).thenReturn(agencyCurriculums);
        // 调用
        PageResponse<CurriculumResponse> response = curriculumService.getAgencyCurriculums(pageSize, pageNum, ages, domainIds, keyword);

        // 验证
        assertEquals(response.getItems().size(), 1); // 应该存在0个系列课程
        assertEquals(response.getTotal().longValue(), 1); // 系列课程为2
    }


    /**
     * 测试获取系列课程详情
     *
     * @param ''id 系列课程 id
     * @return 系列课程详情
     */

    @Test
    public void testDetail() {
        // 数据准备 -- 入参
        String id = "005";
        CurriculumEntity curriculumEntity = new CurriculumEntity();
        curriculumEntity.setId("5CD0392C-55D3-EB11-9C19-4CCC6ACF6129");
        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setUsername("4BD0392C-55D3-EB11-9C19-4CCC6ACF6129");
        FrameworkEntity frameworkEntity = new FrameworkEntity();
        List<MeasureEntity> domains = new ArrayList<>();
        MeasureEntity measureEntity = new MeasureEntity();
        domains.add(measureEntity);
        Integer curriculumBookCount = new Integer("1");
        Integer curriculumVocabularyCount = new Integer("1");
        Integer curriculumAttachmentCount = new Integer("1");

        // 数据模拟
        Mockito.when(userProvider.getCurrentUser()).thenReturn(currentUser);
        Mockito.when(curriculumDao.getCurriculumById(anyString())).thenReturn(curriculumEntity);
        Mockito.when(frameworkDao.getById(anyString())).thenReturn(frameworkEntity);
        Mockito.when(curriculumDomainDao.getByCurriculumId(anyString())).thenReturn(new ArrayList<>());
        Mockito.when(curriculumBookDao.getCurriculumBookCount(id)).thenReturn(curriculumBookCount);
        Mockito.when(measureDao.getByIdIn(any())).thenReturn(domains);
        Mockito.when(curriculumVocabularyDao.getCurriculumVocabularyCount(id)).thenReturn(curriculumVocabularyCount);
        Mockito.when(curriculumAttachmentDao.getCurriculumAttachmentCount(id)).thenReturn(curriculumAttachmentCount);

        // 调用
        CurriculumResponse response = curriculumService.detail(id);
        // 验证
        // Printable 数量为1
        assertEquals(response.getPrintableNum().longValue(), 1);
        // curriculumBookCount数量为1
        assertEquals(response.getVocabularyNum().longValue(), 1);

    }

    /**
     * 测试获取单元周计划概览
     *
     * @param ''unitId 单元 ID
     * @return 单元周计划概览
     */
    @Test
    public void testUnitPlanOverView() {
        String unitId = "4BD0392C-55D3-EB11-9C19-4CCC6ACF6129";

        CurriculumUnitEntity unitEntity = new CurriculumUnitEntity();
        unitEntity.setId("001");

        // 数据模拟

        List<CategoryEntity> categoryEntities = new ArrayList<>();
        CategoryEntity categoryEntity = new CategoryEntity();
        categoryEntity.setId("4BD0392C-55D3-EB11-9C19-4CCC6ACF6129");
        categoryEntities.add(categoryEntity);
        List<ItemEntity> itemEntities = new ArrayList<>();
        ItemEntity itemEntity = new ItemEntity();
        itemEntity.setId("001");
        itemEntities.add(itemEntity);
        List<ItemMeasureDetailEntity> itemMeasureDetailEntities = new ArrayList<>();
        ItemMeasureDetailEntity detailEntity = new ItemMeasureDetailEntity();
        detailEntity.setItemId("001");
        itemMeasureDetailEntities.add(detailEntity);

        Mockito.when(curriculumUnitDao.getById(unitId)).thenReturn(unitEntity);

        Mockito.when(planCategoryDao.listByPlanIds(any())).thenReturn(categoryEntities);

        Mockito.when(planItemDao.listByPlanIds(any())).thenReturn(itemEntities);

        Mockito.when(planItemMeasureDao.listItemMeasureDetailsByPlanIds(any())).thenReturn(itemMeasureDetailEntities);
        //  调用
        curriculumService.getUnitPlanMaterials(unitId);
        // 验证
        Mockito.verify(curriculumUnitDao, times(1)).getById(any());


    }

    @Test
    public void testBookList() {

        // 数据准备 -- 入参
        String id = "005";

        // 数据准备 -- 接口模拟
        CurriculumEntity curriculumEntity = new CurriculumEntity();

        List<MeasureEntity> measureEntities = new ArrayList<>();

        List<CurriculumUnitEntity> curriculumUnitEntities = new ArrayList<>();

        List<PlanEntity> planEntities = new ArrayList<>();

        List<CurriculumUnitPlanEntity> curriculumUnitPlanEntities = new ArrayList<>();

        List<ItemEntity> itemEntities = new ArrayList<>();

        Mockito.when(curriculumDao.getById(any())).thenReturn(curriculumEntity);

        Mockito.when(measureDao.getByIdIn(any())).thenReturn(measureEntities);

        Mockito.when(curriculumUnitDao.getUnitsByCurriculumId(any())).thenReturn(curriculumUnitEntities);


        Mockito.when(planDao.listPlansByIds(any())).thenReturn(planEntities);

        Mockito.when(curriculumUnitPlanDao.getPlansByCurriculumId(any())).thenReturn(curriculumUnitPlanEntities);

        Mockito.when(planItemDao.listByPlanIds(any())).thenReturn(itemEntities);

        // 调用
        CurriculumBookListResponse response = curriculumService.getBookList(id);

        // 验证
        Assert.assertNotEquals(response.getSum().SIZE, -1); // 不应该存在负值
        Assert.assertNotEquals(response.getUnits().size(), -1);


    }

    /**
     * 测试获取系列课程词汇资源
     *
     * @param //系列课程 id
     * @return 系列课程词汇资源
     */
    @Test
    public void testGetKeyVocabularyList() {
        // 数据准备 -- 入参
        String id = "001";


        CurriculumEntity curriculumEntity = new CurriculumEntity();
        curriculumEntity.setId("002");
        List<CurriculumUnitEntity> unitEntities = new ArrayList<>();
        List<CurriculumVocabularyEntity> vocabularyEntities = new ArrayList<>();

        // 数据准备 -- 接口模拟
        Mockito.when(curriculumDao.getCurriculumById(id)).thenReturn(curriculumEntity);
        Mockito.when(curriculumUnitDao.getUnitsByCurriculumId(id)).thenReturn(unitEntities);
        Mockito.when(curriculumVocabularyDao.listByCurriculumId(id)).thenReturn(vocabularyEntities);

        // 调用
        curriculumService.getKeyVocabularyList(id);
        // 验证
        Mockito.verify(curriculumDao, times(1)).getCurriculumById(any());

    }

    /**
     * 测试获取系列课程活动资源
     *
     * @param ''id 系列课程 id
     * @return 系列课程活动资源
     */
    @Test
    public void testGetActivitiesList() {
        // 数据准备 -- 入参
        String id = "001";

        CurriculumEntity curriculumEntity = new CurriculumEntity();
        curriculumEntity.setId("002");
        List<CurriculumUnitEntity> unitEntities = new ArrayList<>();
        List<CurriculumUnitPlanEntity> unitPlanEntities = new ArrayList<>();

        List<PlanEntity> planEntities = new ArrayList<>();

        List<ItemEntity> itemEntities = new ArrayList<>();

        List<CategoryEntity> categoryEntities = new ArrayList<>();


        // 数据准备 -- 接口模拟
        Mockito.when(curriculumDao.getCurriculumById(id)).thenReturn(curriculumEntity);
        Mockito.when(curriculumUnitDao.getUnitsByCurriculumId(id)).thenReturn(unitEntities);
        Mockito.when(curriculumUnitPlanDao.getPlansByCurriculumId(id)).thenReturn(unitPlanEntities);
        Mockito.when(planDao.listAllPlansByIds(any())).thenReturn(planEntities);
        Mockito.when(planItemDao.listByPlanIds(any())).thenReturn(itemEntities);
        Mockito.when(planCategoryDao.listByPlanIds(any())).thenReturn(categoryEntities);


        // 调用
        curriculumService.getActivitiesList(id);
        // 验证
        // 只调用一次
        Mockito.verify(curriculumDao, times(1)).getCurriculumById(any());
        Mockito.verify(planDao, times(1)).listAllPlansByIds(any());
        Mockito.verify(planItemDao, times(1)).listByPlanIds(any());
        Mockito.verify(planCategoryDao, times(1)).listByPlanIds(any());

    }

    /**
     * 测试获取系列课程测评点资源
     *
     * @param ''id 系列课程 id
     * @return 系列课程测评点资源
     */
    @Test
    public void testGetAssessmentList() {
        // 数据准备 -- 入参
        String id = "001";
        String userId = "user-001";
        String agencyId = "agency-001";
        String frameworkId = "framework-001";
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);


        CurriculumEntity curriculumEntity = new CurriculumEntity();
        curriculumEntity.setId("002");
        List<CurriculumUnitEntity> unitEntities = new ArrayList<>();
        List<CurriculumUnitPlanEntity> unitPlanEntities = new ArrayList<>();

        FrameworkEntity frameworkEntity = new FrameworkEntity();
        frameworkEntity.setId("006");


        List<CurriculumUnitPlanMeasureEntity> unitPlanMeasureEntities = new ArrayList<>();


        // 数据准备 -- 接口模拟
        Mockito.when(curriculumDao.getCurriculumById(id)).thenReturn(curriculumEntity);
        Mockito.when(curriculumUnitDao.getUnitsByCurriculumId(id)).thenReturn(unitEntities);
        Mockito.when(curriculumUnitPlanDao.getPlansByCurriculumId(id)).thenReturn(unitPlanEntities);


        Mockito.when(curriculumUnitPlanMeasureDao.listByCurriculumId(id)).thenReturn(unitPlanMeasureEntities);

        Mockito.when(frameworkDao.getById(curriculumEntity.getFrameworkId())).thenReturn(frameworkEntity);
        //  FrameworkEntity frameworkEntity = frameworkDao.getById(curriculumEntity.getFrameworkId());

        Mockito.when(userProvider.getCurrentUserId()).thenReturn(userId);

        Mockito.when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);

        Mockito.when(userProvider.getAgencyOpenDefaultClose(agencyId, AgencyMetaKey.DRDP_KEY_MEASURE_SETTING.toString())).thenReturn(true);

        Mockito.when(domainDao.getKeyMeasuresSetting(agencyId, frameworkId)).thenReturn(new ArrayList<>());

        // 调用
        curriculumService.getAssessmentList(id);
        // 验证
        // 只调用一次
        Mockito.verify(curriculumDao, times(1)).getCurriculumById(any());
        Mockito.verify(frameworkDao, times(1)).getById(any());
        Mockito.verify(curriculumUnitPlanMeasureDao, times(1)).listByCurriculumId(any());


    }


    /**
     * 测试获取周计划活动
     *
     * @param ''planId
     * @return
     */
    @Test
    public void testGetPlanItem() {

        // 数据准备 -- 入参
        String planId = "001";

        List<ItemEntity> itemEntities = new ArrayList<>();
        ItemEntity itemEntity = new ItemEntity();
        itemEntity.setId("002");
        itemEntities.add(itemEntity);

        CurriculumEntity curriculumEntity = new CurriculumEntity();
        curriculumEntity.setId("002");
        List<CategoryEntity> categoryEntities = new ArrayList<>();

        List<PlanCenterEntity> planCenterEntities = new ArrayList<>();

        FrameworkEntity frameworkEntity = new FrameworkEntity();
        frameworkEntity.setId("006");

        // 数据准备 -- 接口模拟

        Mockito.when(planItemDao.listByPlanId(planId)).thenReturn(itemEntities);

        Mockito.when(planCategoryDao.listByCategoryIdIn(any())).thenReturn(categoryEntities);

        Mockito.when(planCenterDao.listByCenterIdIn(any())).thenReturn(planCenterEntities);

        // 调用
        curriculumService.getPlanItem(planId);
        // 验证
        // 只调用一次
        Mockito.verify(planItemDao, times(1)).listByPlanId(any());
        Mockito.verify(planCategoryDao, times(1)).listByCategoryIdIn(any());

    }

    /**
     * test系列课程应用到班级
     *
     * @param ''request 系列课程应用到班级请求体
     * @return 操作成功响应
     */
    @Test
    public void testApply() {

        // 构造请求参数
        CurriculumApplyRequest request = new CurriculumApplyRequest();
        request.setCenterId("001");
        request.setGroupId("002");
        request.setPlanIds(Arrays.asList(new String[]{"001"}));
        request.setCurriculumId("004");
        request.setStartDate(new Date());
        request.setStartWeek(1);
        String centerId = request.getCenterId();
        String groupId = request.getGroupId();
//        List<String> planIds = request.getPlanIds();
        String curriculumId = request.getCurriculumId();
//        Date startDate = request.getStartDate();
//        Integer startWeek = request.getStartWeek();
        CurriculumEntity curriculumEntity = new CurriculumEntity();
        curriculumEntity.setId("002");
        GroupEntry group = new GroupEntry();
        CenterEntity center = new CenterEntity();
        List<CurriculumUnitPlanEntity> allPlans = new ArrayList<>();
        CurriculumUnitPlanEntity curriculumUnitPlanEntity = new CurriculumUnitPlanEntity();
        curriculumUnitPlanEntity.setPlanId("001");
        allPlans.add(curriculumUnitPlanEntity);
        AuthUserDetails userDetail =new AuthUserDetails();
        AgencyModel agencyModel = new AgencyModel();
        PlanEntity planEntity = new PlanEntity();
        // 数据准备 -- 接口模拟
        Mockito.when(curriculumDao.getCurriculumById(curriculumId)).thenReturn(curriculumEntity);
        Mockito.when(groupDao.getGroup(groupId)).thenReturn(group);
        Mockito.when(userProvider.getCurrentUser()).thenReturn(userDetail);
        Mockito.when(centerDao.getCenter(centerId)).thenReturn(center);
        Mockito.when(curriculumUnitPlanDao.getPlansByCurriculumId(curriculumId)).thenReturn(allPlans);
        Mockito.when(userProvider.getAgencyByUserId(any())).thenReturn(agencyModel);
        Mockito.when(userProvider.getAgencyOpenDefaultOpen(any(), any())).thenReturn(true);
        Mockito.when(planDao.getAgencyCurrentWeekPlanTemplate(any())).thenReturn(planEntity);
        Mockito.when(planDao.get(any())).thenReturn(planEntity);
        // 调用
        curriculumService.apply(request);
        // 验证只调用一次
        Mockito.verify(curriculumDao, times(1)).getCurriculumById(any());
        Mockito.verify(groupDao, times(1)).getGroup(any());

    }

    /**
     * 测试生成系列课程单元周计划的材料和资源详情
     *
     * @param ''planId 周计划 ID
     * @return 系列课程单元周计划的材料和资源详情
     */
    @Test
    public void testGeneratePlanMaterialsAndResource() {
        // 模拟数据
        String planId = "001";
        String unitId = "002";
        String curriculumId = "003";
        String frameworkId = "006";
        List<ItemEntity> itemEntities = new ArrayList<>();
        ItemEntity itemEntity = new ItemEntity();
        itemEntity.setCategoryId("009");
        itemEntities.add(itemEntity);
        List<CategoryEntity> categoryEntities = new ArrayList<>();
        List<LessonEntity> lessonEntities = new ArrayList<>();
//        List<SubjectEntity> subjectEntityList = new ArrayList<>();
        List<ItemMeasureEntity> itemMeasureEntities = new ArrayList<>();
        AgencyModel agencyModel = new AgencyModel();
        CurriculumEntity curriculumEntity = new CurriculumEntity();
        curriculumEntity.setFrameworkId(frameworkId);
        PlanEntity planEntity = new PlanEntity();
        planEntity.setId("ggg");
        planEntity.setAgencyId("111");

        // 数据准备 -- 接口模拟
        when(planDao.get(anyString())).thenReturn(planEntity);
        Mockito.when(planItemDao.listByPlanId(planId)).thenReturn(itemEntities);
        Mockito.when(planCategoryDao.listByCategoryIdIn(any())).thenReturn(categoryEntities);
        Mockito.when(lessonDao.getByIds(any())).thenReturn(lessonEntities);
        Mockito.when(planItemMeasureDao.listByItemIdIn(any())).thenReturn(itemMeasureEntities);
        Mockito.when(userProvider.getAgencyByUserId(any())).thenReturn(agencyModel);
        Mockito.when(curriculumDao.getById(curriculumId)).thenReturn(curriculumEntity);

        // 调用
        curriculumService.generatePlanMaterialsAndResource(planId, unitId, curriculumId);
        // 验证
        // 只保存一次、
        Mockito.verify(curriculumMaterialDao, times(1)).saveBatch(any());
        Mockito.verify(curriculumBookDao, times(1)).saveBatch(any());
        Mockito.verify(curriculumVocabularyDao, times(1)).saveBatch(any());


    }

    /**
     * 测试获取单元周计划材料
     *
     * @param ''planId       周计划ID
     * @param ''unitId       单元ID
     * @param ''curriculumId 系列ID
     * @return 返回生成的周计划资源
     */
    @Test
    public void testGetUnitPlanSource() {
        String planId = "001";
        String unitId = "002";
        String curriculumId = "003";
        String userId = "004";
        String agencyId = "005";
        String frameworkId = "006";
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        List<ItemEntity> itemEntities = new ArrayList<>();
        ItemEntity itemEntity = new ItemEntity();
        itemEntity.setCategoryId("009");
        itemEntities.add(itemEntity);
        List<CurriculumMaterialEntity> materialEntities = new ArrayList<>();
        List<CurriculumBookEntity> bookEntities = new ArrayList<>();
        List<CurriculumVocabularyEntity> vocabularyEntities = new ArrayList<>();
        List<MediaEntity> mediaEntities = new ArrayList<>();
        List<CurriculumAttachmentEntity> attachmentEntities = new ArrayList<>();
        CurriculumEntity curriculumEntity = new CurriculumEntity();
        curriculumEntity.setFrameworkId(frameworkId);
        PlanEntity planEntity = new PlanEntity();
        planEntity.setId("ggg");
        planEntity.setAgencyId("111");
        List<PlanCornerItemEntity> planCornerItemEntities = new ArrayList<>();
        PlanCornerItemEntity planCornerItemEntity = new PlanCornerItemEntity();
        planCornerItemEntity.setPlanId("001");
        planCornerItemEntities.add(planCornerItemEntity);

        // 数据准备 -- 接口模拟
        when(planDao.get(anyString())).thenReturn(planEntity);
        when(planCornerItemDao.listByPlanIdsAndUserId(anyList(), anyString())).thenReturn(planCornerItemEntities);
        Mockito.when(planItemDao.listByPlanId(planId)).thenReturn(itemEntities);
        Mockito.when(curriculumMaterialDao.listByItemIdIn(any(), any(), any())).thenReturn(materialEntities);
        Mockito.when(curriculumBookDao.listByItemIdIn(any(), any(), any())).thenReturn(bookEntities);
        Mockito.when(curriculumVocabularyDao.listByItemIdIn(any(), any(), any())).thenReturn(vocabularyEntities);

        Mockito.when(mediaDao.listByIdIn(any())).thenReturn(mediaEntities);

        Mockito.when(curriculumAttachmentDao.listByItemIdIn(any(), anyString(), anyString())).thenReturn(attachmentEntities);

        Mockito.when(userProvider.getCurrentUserId()).thenReturn(userId);

        Mockito.when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        Mockito.when(curriculumDao.getById(curriculumId)).thenReturn(curriculumEntity);
        Mockito.when(userProvider.getAgencyOpenDefaultClose(agencyId, AgencyMetaKey.DRDP_KEY_MEASURE_SETTING.toString())).thenReturn(true);
        Mockito.when(domainDao.getKeyMeasuresSetting(agencyId, frameworkId)).thenReturn(new ArrayList<>());

        //Mockito.when(measureDao.getByIdIn(measureIds)).thenReturn(true);
        // 调用
        curriculumService.getUnitPlanSource(planId, unitId, curriculumId);
        // 验证
        // 只保存一次、
        Mockito.verify(curriculumMaterialDao, times(1)).listByItemIdIn(any(), any(), any());
        Mockito.verify(curriculumBookDao, times(1)).listByItemIdIn(any(), any(), anyString());
        Mockito.verify(curriculumVocabularyDao, times(1)).listByItemIdIn(any(), anyString(), anyString());


    }

    /**
     * 测试获取单元周计划材料
     *
     * @param ''planId 周计划ID
     * @return 返回没材料的组和组里面的活动
     */
    @Test
    public void testGetAddPlanMaterialInfo() {
        // 模拟数据
        String id = "9000";
        List<CurriculumMaterialEntity> materialEntities = new ArrayList<>();
        List<ItemEntity> itemEntities = new ArrayList<>();
        List<CategoryEntity> categoryEntities = new ArrayList<>();
        List<PlanCenterEntity> planCenterEntities = new ArrayList<>();

        // 数据准备 -- 接口模拟
        Mockito.when(curriculumMaterialDao.listByPlanId(any())).thenReturn(materialEntities);
        Mockito.when(planItemDao.listByPlanId(any())).thenReturn(itemEntities);
        Mockito.when(planCategoryDao.listByCategoryIdIn(any())).thenReturn(categoryEntities);
        Mockito.when(planCenterDao.listByCenterIdIn(any())).thenReturn(planCenterEntities);
        // 调用
        curriculumService.getAddPlanMaterialInfo(id);

        // 只保存一次、
        Mockito.verify(curriculumMaterialDao, times(1)).listByPlanId(any());
        Mockito.verify(planItemDao, times(1)).listByPlanId(any());

    }

    /**
     * 测试获取单元周计划材料
     *
     * @param ''planId 周计划ID
     * @return 返回没材料的组和组里面的活动
     */
    @Test
    public void testGetUpdatePlanMaterialInfo() {
        // 模拟数据
        String planId = "9000";
        String centerId = "001";
        List<CurriculumMaterialEntity> materialEntities = new ArrayList<>();
        List<ItemEntity> itemEntities = new ArrayList<>();
        List<CategoryEntity> categoryEntities = new ArrayList<>();
        List<PlanCenterEntity> planCenterEntities = new ArrayList<>();

        // 数据准备 -- 接口模拟
        Mockito.when(curriculumMaterialDao.listByPlanId(any())).thenReturn(materialEntities);
        Mockito.when(planItemDao.listByPlanId(any())).thenReturn(itemEntities);
        Mockito.when(planCategoryDao.listByCategoryIdIn(any())).thenReturn(categoryEntities);
        Mockito.when(planCenterDao.listByCenterIdIn(any())).thenReturn(planCenterEntities);
        // 调用
        curriculumService.getUpdatePlanMaterialInfo(planId, centerId);

        // 只保存一次、
        Mockito.verify(curriculumMaterialDao, times(1)).listByPlanId(any());
        Mockito.verify(planItemDao, times(1)).listByPlanId(any());

    }

    /**
     * 测试更新单元周计划活动材料
     *
     * @param ''request 更新单元周计划活动材料请求体
     * @return 操作成功响应
     */

    @Test
    public void testUpdatePlanMaterial() {
        UpdateUnitPlanCenterMaterialRequest request = new UpdateUnitPlanCenterMaterialRequest();
        request.setCurriculumId("001");
        request.setUnitId("002");
        request.setPlanId("003");
        request.setPlanItemId("004");
        request.setId("005");
        // 模拟参数
//        String curriculumId = request.getCurriculumId();
//        String unitId = request.getUnitId();
//        String planId = request.getPlanId();
//        String planItemId = request.getPlanItemId();
        String id = request.getId();
        AgencyModel agencyModel = new AgencyModel();
        CurriculumMaterialEntity materialEntity = new CurriculumMaterialEntity();
        materialEntity.setType(CurriculumResourceType.CUSTOM.toString());
        // 数据准备 -- 接口模拟

        Mockito.when(userProvider.getAgencyByUserId(any())).thenReturn(agencyModel);

        Mockito.when(curriculumMaterialDao.getById(id)).thenReturn(materialEntity);

        Mockito.when(curriculumMaterialDao.updateById(any())).thenReturn(true);
        // 调用

        curriculumService.updatePlanMaterial(request);

        // 验证 更新了一次
        Mockito.verify(curriculumMaterialDao, times(1)).updateById(any());


    }

    /**
     * 测试添加单元周计划活动材料
     *
     * @param \\request 添加单元周计划活动材料请求体
     * @return 操作成功响应
     */
    @Test
    public void testaddPlanMaterial() {

        UpdateUnitPlanCenterMaterialRequest request = new UpdateUnitPlanCenterMaterialRequest();
        request.setCurriculumId("001");
        request.setUnitId("002");
        request.setPlanId("003");
        request.setPlanItemId("004");
        request.setId("005");
        request.setPlanItemIds(Arrays.asList(new String[]{"001"}));
        // 模拟参数
//        String curriculumId = request.getCurriculumId();
//        String unitId = request.getUnitId();
//        String planId = request.getPlanId();
//        String planItemId = request.getPlanItemId();
//        String id = request.getId();
        AgencyModel agencyModel = new AgencyModel();
        CurriculumMaterialEntity materialEntity = new CurriculumMaterialEntity();
        materialEntity.setType(CurriculumResourceType.CUSTOM.toString());
        // 数据准备 -- 接口模拟

        Mockito.when(curriculumMaterialDao.save(materialEntity)).thenReturn(true);
        Mockito.when(userProvider.getAgencyByUserId(any())).thenReturn(agencyModel);

        // 调用
        curriculumService.addPlanMaterial(request);

        // 验证 更新了一次
        Mockito.verify(curriculumMaterialDao, times(1)).save(any());

    }

    /**
     * test添加系列课程单元中周计划课程书信息
     *
     * @param ''request 添加系列课程单元中周计划课程书信息请求体
     * @return 操作成功响应
     */
    @Test
    public void testAddBook() {
        // 模拟参数
        AddUnitPlanLessonBookRequest request = new AddUnitPlanLessonBookRequest();
        request.setPlanItemIds(Arrays.asList(new String[]{"001"}));
        List<Object[]> books = new ArrayList<>();
        books.add(new Object[]{"001"});
        request.setBooks(books);
        request.setPlanId("001");
        request.setCurriculumId("002");
        request.setUnitId("003|");
        request.setPlanItemId("004");
//        String curriculumId = request.getCurriculumId();
//        String unitId = request.getUnitId();
//        String planId = request.getPlanId();
//        String planItemId = request.getPlanItemId();
        AgencyModel agencyModel = new AgencyModel();


        // 数据准备 -- 接口模拟

        Mockito.when(curriculumBookDao.saveBatch(any())).thenReturn(true);
        Mockito.when(userProvider.getAgencyByUserId(any())).thenReturn(agencyModel);

        // 调用
        curriculumService.addBook(request);

        // 验证 更新了一次
        Mockito.verify(curriculumBookDao, times(1)).saveBatch(any());
    }

    /**
     * 测试添加系列课程单元中周计划课程词汇信息
     *
     * @param ''request 添加系列课程单元中周计划课程词汇信息请求体
     * @return 操作成功响应
     */
    @Test
    public void testAddVocabulary() {

        // 模拟参数
        AddUnitPlanLessonVocabularyRequest request = new AddUnitPlanLessonVocabularyRequest();
        request.setPlanItemIds(Arrays.asList(new String[]{"001"}));
        List<DLLResourceRequest> dllResource = new ArrayList<>();
        dllResource.add(new DLLResourceRequest());
        request.setDllResource(dllResource);
        request.setPlanId("001");
        request.setCurriculumId("002");
        request.setUnitId("003|");
        request.setPlanItemId("004");
//        String curriculumId = request.getCurriculumId();
//        String unitId = request.getUnitId();
//        String planId = request.getPlanId();
//        String planItemId = request.getPlanItemId();
        AgencyModel agencyModel = new AgencyModel();


        // 数据准备 -- 接口模拟   curriculumVocabularyDao.saveBatch(curriculumVocabularyEntities);

        Mockito.when(curriculumVocabularyDao.saveBatch(any())).thenReturn(true);
        Mockito.when(userProvider.getAgencyByUserId(any())).thenReturn(agencyModel);

        // 调用
        curriculumService.addVocabulary(request);

        // 验证 更新了一次
        Mockito.verify(curriculumVocabularyDao, times(1)).saveBatch(any());

    }


    /**
     * 测试添加系列课程单元中周计划课程附件信息
     *
     * @param ''request 添加系列课程单元中周计划课程附件信息请求体
     * @return 操作成功响应
     */
    @Test
    public void testAddAttachment() {

        // 模拟参数
        AddUnitPlanLessonAttachmentRequest request = new AddUnitPlanLessonAttachmentRequest();
        request.setPlanItemIds(Arrays.asList(new String[]{"001"}));
        List<DLLResourceRequest> dllResource = new ArrayList<>();
        dllResource.add(new DLLResourceRequest());

        request.setMediaIds(Arrays.asList(new String[]{"001"}));
        request.setPlanId("001");
        request.setCurriculumId("002");
        request.setUnitId("003|");
        request.setPlanItemId("004");
//        String curriculumId = request.getCurriculumId();
//        String unitId = request.getUnitId();
//        String planId = request.getPlanId();
//        String planItemId = request.getPlanItemId();
        AgencyModel agencyModel = new AgencyModel();


        // 数据准备 -- 接口模拟

        Mockito.when(curriculumAttachmentDao.saveBatch(any())).thenReturn(true);
        Mockito.when(userProvider.getAgencyByUserId(any())).thenReturn(agencyModel);

        // 调用
        curriculumService.addAttachment(request);

        // 验证 更新了一次
        Mockito.verify(curriculumAttachmentDao, times(1)).saveBatch(any());


    }

    /**
     * 测试更新系列课程单元周计划的测评点信息
     *
     * @param ''request 更新系列课程单元周计划的测评点信息请求体
     * @return 操作成功响应
     */
    @Test
    public void testUpdateMeasures() {
        UpdateUnitPlanMeasuresRequest request = new UpdateUnitPlanMeasuresRequest();
        // 模拟参数
        request.setCurriculumId("001");
        request.setUnitId("002");
        request.setPlanId("003");
//        String curriculumId = request.getCurriculumId();
//        String unitId = request.getUnitId();
        String planId = request.getPlanId();
        AgencyModel agencyModel = new AgencyModel();
        List<CurriculumUnitPlanMeasureEntity> planMeasureEntities = new ArrayList<>();
        // 数据准备 -- 接口模拟
        Mockito.when(curriculumUnitPlanMeasureDao.listByPlanId(planId)).thenReturn(planMeasureEntities);
        Mockito.when(userProvider.getAgencyByUserId(any())).thenReturn(agencyModel);
        Mockito.when(curriculumUnitPlanMeasureDao.saveBatch(any())).thenReturn(true);


        // 调用
        curriculumService.updateMeasures(request);

        // 验证 更新了一次
        Mockito.verify(curriculumUnitPlanMeasureDao, times(1)).saveBatch(any());
        // 删除了一次
        Mockito.verify(curriculumUnitPlanMeasureDao, times(1)).deleteByIds(any());

    }

    /**
     * 测试获取系列课程单元中的材料
     *
     * @param ''unitId 单元 Id
     * @return 系列课程单元中的材料
     */
    @Test
    public void testGetUnitPlanMaterials() {
        // 数据模拟
        String unitId = "001";
        CurriculumUnitEntity curriculumUnitEntity = new CurriculumUnitEntity();
        curriculumUnitEntity.setDeleted(false);
        List<CurriculumUnitPlanEntity> curriculumUnitPlans = new ArrayList<>();
        CurriculumUnitPlanEntity curriculumUnitPlanEntity = new CurriculumUnitPlanEntity();
        curriculumUnitPlanEntity.setId("004");
        curriculumUnitPlans.add(curriculumUnitPlanEntity);
        List<CurriculumMaterialEntity> materialEntities = new ArrayList<>();
        CurriculumMaterialEntity curriculumMaterialEntity = new CurriculumMaterialEntity();
        curriculumMaterialEntity.setId("005");
        materialEntities.add(curriculumMaterialEntity);


        // 数据准备 -- 接口模拟
        Mockito.when(curriculumUnitDao.getById(unitId)).thenReturn(curriculumUnitEntity);
        Mockito.when(curriculumUnitPlanDao.getPlansByUnitId(unitId)).thenReturn(curriculumUnitPlans);
        Mockito.when(curriculumMaterialDao.listByUnitId(unitId)).thenReturn(materialEntities);
        // 调用
        curriculumService.getUnitPlanMaterials(unitId);


        // 验证 更新了一次
        Mockito.verify(curriculumUnitDao, times(1)).getById(any());
        // 删除了一次
        Mockito.verify(curriculumUnitPlanDao, times(1)).getPlansByUnitId(any());


    }

    /**
     * 测试添加系列课程单元
     *
     * @param ;;request 添加系列课程单元请求体
     * @return 系列课程单元 Id
     */
    @Test
    public void testAddUnit() {
        // 模拟参数
        AddCurriculumUnitRequest request = new AddCurriculumUnitRequest();

        request.setNumber(6666);
        request.setCurriculumId("002");

        String curriculumId = request.getCurriculumId();
//        Integer unitNumber = request.getNumber();
        AuthUserDetails user = new AuthUserDetails();

        CurriculumEntity curriculum = new CurriculumEntity();
        curriculum.setId("009");
        // 数据准备 -- 接口模拟


        Mockito.when(userProvider.getCurrentUser()).thenReturn(user);
        Mockito.when(curriculumDao.getById(curriculumId)).thenReturn(curriculum);
        Mockito.when(curriculumUnitDao.save(any())).thenReturn(true);
        Mockito.when(curriculumUnitPlanDao.save(any())).thenReturn(true);

        curriculumService.addUnit(request);

        // 验证 更新了一次
        Mockito.verify(curriculumUnitDao, times(1)).save(any());
        Mockito.verify(curriculumUnitPlanDao, times(1)).save(any());


    }

    /**
     * 测试更新系列课程单元基本信息
     *
     * @param ''request 更新系列课程单元请求体
     * @return 操作成功响应
     */
    @Test
    public void testUpdateUnit() {
        // 模拟参数
        UpdateCurriculumUnitRequest request = new UpdateCurriculumUnitRequest();

        request.setUnitId("001");

        AuthUserDetails user = new AuthUserDetails();

        CurriculumUnitEntity oldUnit = new CurriculumUnitEntity();

        // 数据准备 -- 接口模拟


        Mockito.when(userProvider.getCurrentUser()).thenReturn(user);
        Mockito.when(curriculumUnitDao.getById(any())).thenReturn(oldUnit);

        Mockito.when(curriculumUnitPlanDao.save(any())).thenReturn(true);
        // 调用
        curriculumService.updateUnit(request);

        // 验证 此逻辑不走
        Mockito.verify(curriculumUnitDao, times(1)).updateById(any());


    }

    /**
     * 测试添加单元周计划活动材料
     *
     * @param ''request 添加单元周计划活动材料请求体
     * @return 操作成功响应
     */
    @Test
    public void testAddPlanMaterial() {

        // 模拟参数
        UpdateUnitPlanCenterMaterialRequest request = new UpdateUnitPlanCenterMaterialRequest();

        request.setPlanId("001");
        request.setCurriculumId("002");
        request.setUnitId("003|");
        request.setPlanItemId("004");
        request.setPlanItemIds(Arrays.asList(new String[]{"001"}));
//        String curriculumId = request.getCurriculumId();
//        String unitId = request.getUnitId();
//        String planId = request.getPlanId();
//        String planItemId = request.getPlanItemId();
        AuthUserDetails user = new AuthUserDetails();
        AgencyModel agencyModel = new AgencyModel();
        CurriculumEntity curriculum = new CurriculumEntity();
        curriculum.setId("009");
        // 数据准备 -- 接口模拟


        Mockito.when(userProvider.getCurrentUser()).thenReturn(user);
        Mockito.when(curriculumMaterialDao.save(any())).thenReturn(true);
        Mockito.when(userProvider.getAgencyByUserId(any())).thenReturn(agencyModel);

        curriculumService.addPlanMaterial(request);

        // 验证 更新了一次
        Mockito.verify(curriculumMaterialDao, times(1)).save(any());

    }

    /**
     * 测试更新系列课程单元周计划的测评点信息
     *
     * @param ''request 更新系列课程单元周计划的测评点信息请求体
     * @return 操作成功响应
     */

    @Test

    public void updateMeasures() {

        // 模拟参数
        UpdateUnitPlanMeasuresRequest request = new UpdateUnitPlanMeasuresRequest();

        request.setPlanId("001");
        request.setCurriculumId("002");
        request.setUnitId("003|");

//        String curriculumId = request.getCurriculumId();
//        String unitId = request.getUnitId();
        String planId = request.getPlanId();

        AuthUserDetails user = new AuthUserDetails();
        AgencyModel agencyModel = new AgencyModel();
        CurriculumEntity curriculum = new CurriculumEntity();
        curriculum.setId("009");
        // 数据准备 -- 接口模拟
        List<CurriculumUnitPlanMeasureEntity> planMeasureEntities = new ArrayList<>();

        Mockito.when(userProvider.getCurrentUser()).thenReturn(user);
        Mockito.when(userProvider.getAgencyByUserId(any())).thenReturn(agencyModel);
        Mockito.when(curriculumUnitPlanMeasureDao.listByPlanId(planId)).thenReturn(planMeasureEntities);

        Mockito.when(curriculumUnitPlanMeasureDao.saveBatch(any())).thenReturn(true);
        //  调用
        curriculumService.updateMeasures(request);

        // 验证 更新了一次
        Mockito.verify(curriculumUnitPlanMeasureDao, times(1)).saveBatch(any());
    }


    /**
     * 测试创建单元
     */
    @Test
    public void mockCreateUnit() {
        // 模拟参数
        CreateUnitRequest request = new CreateUnitRequest();
        request.setCity("Los Angeles");
        request.setGrade("Transitional Kindergarten children (4-5 yrs old)");
        request.setState("California");
        request.setTitle("Unit 1: Welcome to the World of Animals");
        request.setWeekCount(2);
        // 用户信息
        AuthUserDetails user = new AuthUserDetails();
        user.setUsername("userId");
        com.learninggenie.common.data.entity.UserEntity userEntity = new com.learninggenie.common.data.entity.UserEntity();
        userEntity.setId("userId");
        // 模拟接口调用
        Mockito.when(userProvider.getCurrentUser()).thenReturn(user);
        Mockito.when(userProvider.checkUser("userId")).thenReturn(userEntity);

        // 调用测试方法
        IdResponse unit = curriculumService.createUnit(request);

        // 验证保存了一次单元实体
        Mockito.verify(curriculumUnitDao, times(1)).save(any());
        // 验证设置了一次缓存
        Mockito.verify(cacheService, times(1)).set(anyString(), anyString(), anyInt());
        // 验证返回的单元 ID 不为空
        Assertions.assertNotNull(unit.getId());
    }

    /**
     * 测试更新单元
     */
    @Test
    public void mockUpdateUnit() {
        // 模拟参数
        CreateUnitRequest request = new CreateUnitRequest();
        String unitId = UUID.randomUUID().toString();
        request.setCity("Los Angeles");
        request.setTitle("Unit 1: Welcome to the World of Animals");
        request.setUnitId(unitId);
        request.setClearWeekPlans(true);
        // 模拟数据库中原有的数据
        CurriculumUnitEntity unitEntity = new CurriculumUnitEntity();
        unitEntity.setCity("Los Angeles");
        unitEntity.setTitle("Unit 1: Welcome to the World of Animals");
        unitEntity.setId(unitId);
        Mockito.when(curriculumUnitDao.getById(any())).thenReturn(unitEntity);
        // 单元下的周计划列表信息
        List<CurriculumUnitPlanEntity> existingUnitPlans = new ArrayList<>();
        CurriculumUnitPlanEntity unitPlanEntity = new CurriculumUnitPlanEntity();
        unitPlanEntity.setUnitId(unitId);
        unitPlanEntity.setPlanId(UUID.randomUUID().toString());
        existingUnitPlans.add(unitPlanEntity);
        when(curriculumUnitPlanDao.getPlansByUnitId(unitId)).thenReturn(existingUnitPlans);
        IdResponse unit = curriculumService.updateUnit(request);
        // 验证 更改成功
        Assertions.assertNotNull(unit.getId());
        // 验证 id 一致
        Assertions.assertTrue(unitId.equalsIgnoreCase(unit.getId()));
    }

    /**
     * 测试更新单元
     */
    @Test
    public void mockUpdateUnitTitle() {
        // 模拟参数
        CreateUnitRequest request = new CreateUnitRequest();
        String unitId = UUID.randomUUID().toString();
        request.setCity("Los Angeles");
        request.setTitle("Unit 1: Welcome to the World of Animals");
        request.setUnitId(unitId);
        // 模拟数据库中原有的数据
        CurriculumUnitEntity unitEntity = new CurriculumUnitEntity();
        unitEntity.setTitle("Unit 2: Welcome to the World of Animals");
        Mockito.when(curriculumUnitDao.getById(any())).thenReturn(unitEntity);
        IdResponse unit = curriculumService.updateUnit(request);
        // 验证
        Assertions.assertNotNull(unit.getId());
        // 验证 id 一致
        Assertions.assertTrue(unitId.equalsIgnoreCase(unit.getId()));
    }

    /**
     * 测试更新单元
     */
    @Test
    public void mockUpdateUnitOverview() {
        // 模拟参数
        String unitId = UUID.randomUUID().toString();
        CreateUnitRequest request = new CreateUnitRequest();
        request.setCity("Los Angeles");
        request.setUnitId(unitId);
        request.setOverview("test");
        // 模拟数据库中原有的数据
        CurriculumUnitEntity unitEntity = new CurriculumUnitEntity();
        unitEntity.setOverview("test1");
        Mockito.when(curriculumUnitDao.getById(any())).thenReturn(unitEntity);
        IdResponse unit = curriculumService.updateUnit(request);
        // 验证
        Assertions.assertNotNull(unit.getId());
        // 验证 id 一致
        Assertions.assertTrue(unitId.equalsIgnoreCase(unit.getId()));
    }

    /**
     * 测试更新单元
     */
    @Test
    public void mockUpdateUnitConcepts() {
        // 模拟参数
        String unitId = UUID.randomUUID().toString();
        CreateUnitRequest request = new CreateUnitRequest();
        request.setCity("Los Angeles");
        request.setUnitId(unitId);
        request.setConcepts("test");
        // 模拟数据库中原有的数据
        CurriculumUnitEntity unitEntity = new CurriculumUnitEntity();
        unitEntity.setConcepts("test1");
        Mockito.when(curriculumUnitDao.getById(any())).thenReturn(unitEntity);
        IdResponse unit = curriculumService.updateUnit(request);
        // 验证
        Assertions.assertNotNull(unit.getId());
        // 验证 id 一致
        Assertions.assertTrue(unitId.equalsIgnoreCase(unit.getId()));
    }

    /**
     * 测试更新单元
     */
    @Test
    public void mockUpdateUnitTrajectory() {
        // 模拟参数
        String unitId = UUID.randomUUID().toString();
        CreateUnitRequest request = new CreateUnitRequest();
        request.setCity("Los Angeles");
        request.setUnitId(unitId);
        request.setTrajectory("test");
        // 模拟数据库中原有的数据
        CurriculumUnitEntity unitEntity = new CurriculumUnitEntity();
        unitEntity.setTrajectory("test1");
        Mockito.when(curriculumUnitDao.getById(any())).thenReturn(unitEntity);
        IdResponse unit = curriculumService.updateUnit(request);
        // 验证
        Assertions.assertNotNull(unit.getId());
        // 验证 id 一致
        Assertions.assertTrue(unitId.equalsIgnoreCase(unit.getId()));
    }

    /**
     * 测试更新单元
     */
    @Test
    public void mockUpdateUnitGuidingQuestions() {
        // 模拟参数
        String unitId = UUID.randomUUID().toString();
        CreateUnitRequest request = new CreateUnitRequest();
        request.setCity("Los Angeles");
        request.setUnitId(unitId);
        request.setGuidingQuestions("test");
        // 模拟数据库中原有的数据
        CurriculumUnitEntity unitEntity = new CurriculumUnitEntity();
        unitEntity.setGuidingQuestions("test1");
        Mockito.when(curriculumUnitDao.getById(any())).thenReturn(unitEntity);
        IdResponse unit = curriculumService.updateUnit(request);
        // 验证
        Assertions.assertNotNull(unit.getId());
        // 验证 id 一致
        Assertions.assertTrue(unitId.equalsIgnoreCase(unit.getId()));
    }

    /**
     * 测试获取单元列表
     */
    @Test
    public void mockListUnits() {
        // 模拟参数
        AuthUserDetails user = new AuthUserDetails();
        user.setUsername(UUID.randomUUID().toString());
        Mockito.when(userProvider.getCurrentUser()).thenReturn(user);
        // 单元计划信息
        List<CurriculumUnitEntity> unitEntities = new ArrayList<>();
        CurriculumUnitEntity unitEntity = new CurriculumUnitEntity();
        unitEntity.setId(UUID.randomUUID().toString());
        unitEntity.setWeekCount(2);
        unitEntities.add(unitEntity);
        Mockito.when(curriculumUnitDao.getUnitsByUserIdAndCreateSource(anyString(), anyString())).thenReturn(unitEntities);

        // 调用方法
        ListUnitsResponse response = curriculumService.listUnits(new ListUnitsRequest());
        // 验证
        Assertions.assertNotNull(response.getItems());
        // 验证获取的单元数量
        Assertions.assertEquals(1, response.getItems().size());
    }

    /**
     * 测试获取单元详细信息
     */
    @Test
    public void mockGetUnit() {
        // 模拟参数
        String unitId = UUID.randomUUID().toString();
        CurriculumUnitEntity unitEntity = new CurriculumUnitEntity();
        unitEntity.setId(unitId);
        Mockito.when(curriculumUnitDao.getUnitById(anyString())).thenReturn(unitEntity);
        List<CurriculumUnitPlanEntity> curriculumUnitPlans = new ArrayList<>();
        CurriculumUnitPlanEntity curriculumUnitPlanEntity = new CurriculumUnitPlanEntity();
        curriculumUnitPlanEntity.setId(UUID.randomUUID().toString());
        String planId = UUID.randomUUID().toString();
        curriculumUnitPlanEntity.setPlanId(planId);
        curriculumUnitPlans.add(curriculumUnitPlanEntity);
        Mockito.when(curriculumUnitPlanDao.getPlansByUnitId(anyString())).thenReturn(curriculumUnitPlans);
        List<PlanEntity> planEntities = new ArrayList<>();
        PlanEntity planEntity = new PlanEntity();
        planEntity.setId(planId);
        planEntities.add(planEntity);
        Mockito.when(planDao.getByIds(anyList())).thenReturn(planEntities);
        List<ItemEntity> itemEntities = new ArrayList<>();
        ItemEntity itemEntity = new ItemEntity();
        itemEntity.setId(UUID.randomUUID().toString());
        itemEntity.setLessonId(UUID.randomUUID().toString());
        itemEntity.setDayOfWeek(1);
        itemEntity.setCategoryId(UUID.randomUUID().toString());
        itemEntities.add(itemEntity);
        ItemEntity itemEntity1 = new ItemEntity();
        itemEntity1.setId(UUID.randomUUID().toString());
        itemEntity1.setDayOfWeek(1);
        itemEntity1.setCategoryId(UUID.randomUUID().toString());
        itemEntities.add(itemEntity1);
        Mockito.when(planItemDao.listByPlanId(anyString())).thenReturn(itemEntities);
        // 项目测评点信息
        List<ItemMeasureDetailEntity> itemMeasureEntities = new ArrayList<>();
        ItemMeasureDetailEntity itemMeasureEntity = new ItemMeasureDetailEntity();
        itemMeasureEntity.setMeasureAbbr("test");
        itemMeasureEntity.setMeasureName("test");
        itemMeasureEntity.setMeasureId(UUID.randomUUID().toString());
        itemMeasureEntities.add(itemMeasureEntity);
        Mockito.when(planItemMeasureDao.listItemMeasureDetailsByItemId(anyString())).thenReturn(itemMeasureEntities);
        // 项目类别信息
        CategoryEntity categoryEntity = new CategoryEntity();
        categoryEntity.setId(UUID.randomUUID().toString());
        categoryEntity.setName("goals test");
        Mockito.when(planCategoryDao.get(anyString())).thenReturn(categoryEntity);
        // 课程信息
        LessonDetailResponse lesson = new LessonDetailResponse();
        lesson.setId(itemEntity.getLessonId());
        lesson.setName("test");
        Mockito.when(lessonService.getLessonDetail(anyString())).thenReturn(lesson);
        // Center 活动
        Mockito.when(curriculumProvider.getGenerateCenterThemes(anyBoolean(), anyInt(), anyInt())).thenReturn(new ArrayList<>());
        GetUnitResponse unit = curriculumService.getUnit(unitId, false);
        // 验证
        Assertions.assertNotNull(unit.getUnit());
        // 验证 id 一致
        Assertions.assertTrue(unitId.equalsIgnoreCase(unit.getUnit().getId()));
        // 验证课程名字相同
        Assertions.assertEquals(lesson.getName(), unit.getUnit().getWeeklyPlans().get(0).getItems().get(0).getLesson().getName());
    }

    /**
     * 测试获取单元详细信息
     * case: 预览时获取单元信息
     */
    @Test
    public void testGetUnitInfo() {
        // 数据模拟
        String unitId = "validUnitId"; // 单元 ID
        boolean edit = false; // 不进行编辑
        CurriculumUnitEntity unitEntity = new CurriculumUnitEntity(); // 单元信息
        GetMeasureResponse getMeasureResponse = new GetMeasureResponse(); // 测评点响应
        getMeasureResponse.setMeasures(new ArrayList<>());
        unitEntity.setId(unitId);
        unitEntity.setFrameworkId("frameworkId");
        // 自定义模块信息
        List<CurriculumCustomModuleRecordEntity> customModuleRecordEntities = new ArrayList<>();
        // 模拟接口返回的数据
        when(curriculumUnitDao.getNotDeletedUnitsById(unitId)).thenReturn(unitEntity); // 模拟获取单元信息
        when(userProvider.getCurrentUserId()).thenReturn("userId"); // 模拟获取当前用户 ID
        when(lessonService.getMeasures(anyString(), eq(false), anyList())).thenReturn(getMeasureResponse); // 模拟获取测评点信息
        when(customModuleRecordDao.getListByMasterDataId(anyString())).thenReturn(customModuleRecordEntities);
        // 调用接口
        GetUnitResponse response = curriculumService.getUnitInfo(unitId, edit, false, false);

        // 断言
        Assertions.assertEquals(unitId, response.getUnit().getId()); // 单元 ID 应一致
    }

    /**
     * 测试获取单元详细信息
     * case: 编辑时获取单元信息失败，单元编辑状态已锁定
     */
    @Test
    public void testGetUnitInfoWhenEdit() {
        // 数据模拟
        String unitId = "unitId"; // 单元 ID
        boolean edit = true; // 进行编辑
        // 单元锁定信息缓存模拟
        CacheModel cacheModel = new CacheModel();
        LockedUnitModel lockedUnitModel = new LockedUnitModel();
        lockedUnitModel.setUserId("lockedUserId");
        cacheModel.setValue(JsonUtil.toJson((lockedUnitModel)));

        // 模拟接口返回的数据
        when(cacheService.get("UNIT_EDITING_" + unitId.toUpperCase())).thenReturn(cacheModel);
        when(userProvider.getCurrentUserId()).thenReturn("userId");

        // 调用接口
        GetUnitResponse response = curriculumService.getUnitInfo(unitId, edit, false, false);

        // 断言
        Assertions.assertNull(response.getUnit()); // 单元信息应为空
        Assertions.assertEquals("lockedUserId", response.getLockedData().getUserId()); // 单元锁定信息中用户 ID 应为 lockedUserId
    }

    /**
     * 测试解锁单元编辑状态
     */
    @Test
    public void testUnlockUnit() {
        // 数据模拟
        String unitId = "unitId"; // 单元 ID
        // 模拟接口返回的数据
        when(cacheService.get("UNIT_EDITING_" + unitId.toUpperCase())).thenReturn(null); // 模拟获取单元锁定信息
        // 调用接口
        curriculumService.unlockUnit(unitId);
        // 断言
        Mockito.verify(cacheService, times(1)).delete("UNIT_EDITING_" + unitId.toUpperCase()); // 验证删除缓存
    }


    /**
     * 测试获取单元周计划的主题和概览
     */
    @Test
    public void testGetPlanThemeAndOverview() {
        // 数据模拟
        String unitId = "validUnitId"; // 单元 ID
        PlanEntity planEntity1 = new PlanEntity(); // 第一个周计划
        planEntity1.setWeek(1);
        PlanEntity planEntity2 = new PlanEntity(); // 第二个周计划
        planEntity2.setWeek(2);
        List<PlanEntity> planEntities = Arrays.asList(planEntity1, planEntity2);

        // 模拟接口返回的数据
        when(planDao.getPlansByUnitId(unitId)).thenReturn(planEntities);

        // 调用接口
        GetPlanThemeAndOverviewResponse response = curriculumService.getPlanThemeAndOverview(unitId);

        // 断言
        Assertions.assertEquals(2, response.getWeeklyPlans().size()); // 周计划数量应为 2
        Assertions.assertEquals(1, response.getWeeklyPlans().get(0).getWeek()); // 第一个周计划的周数应为 1
        Assertions.assertEquals(2, response.getWeeklyPlans().get(1).getWeek()); // 第二个周计划的周数应为 2
    }

    /**
     * 测试获取单元周计划的活动单元测试
     */
    @Test
    public void testGetPlanActivities() {
        // 数据模拟
        String planId = "validPlanId";
        List<ItemEntity> itemEntities = new ArrayList<>(); // 模拟周计划的活动
        ItemEntity itemEntity1 = new ItemEntity(); // 模拟 center 活动
        itemEntity1.setId("itemId1");
        itemEntity1.setCategoryId("categoryId1");
        itemEntity1.setDayOfWeek(1);
        itemEntity1.setCenterId("centerId");
        itemEntities.add(itemEntity1);
        ItemEntity itemEntity2 = new ItemEntity(); // 模拟大小组活动
        itemEntity2.setId("itemId2");
        itemEntity2.setCategoryId("categoryId1");
        itemEntity2.setDayOfWeek(1);
        itemEntities.add(itemEntity2);
        // 周计划分组信息
        List<CategoryEntity> categoryEntities = new ArrayList<>();
        CategoryEntity categoryEntity = new CategoryEntity();
        categoryEntity.setId("categoryId1");
        categoryEntity.setName("category1");
        categoryEntity.setType("TOP_WEEK_COL");
        categoryEntities.add(categoryEntity);
        // Center 组信息
        List<PlanCenterEntity> planCenterEntities = new ArrayList<>();
        PlanCenterEntity planCenterEntity = new PlanCenterEntity();
        planCenterEntity.setId("centerId");
        planCenterEntity.setName("center1");
        planCenterEntities.add(planCenterEntity);
        // plan
        PlanEntity planEntity = new PlanEntity();
        planEntity.setId(planId);
        planEntity.setDomainIds("['domainId1','domainId2']");
        planEntity.setMeasureIds("['measureId1','measureId2']");
        // 模拟接口返回的数据
        when(planDao.get(planId)).thenReturn(planEntity); // 模拟获取周计划项
        when(planItemDao.listByPlanId(planId)).thenReturn(itemEntities); // 模拟获取周计划项
        when(planItemMeasureDao.listItemMeasureDetailsByItemIds(Arrays.asList("itemId1", "itemId2"))).thenReturn(new ArrayList<>()); // 模拟获取周计划项的测评点
        when(planCategoryDao.listByCategoryIdIn(Collections.singletonList("categoryId1"))).thenReturn(categoryEntities); // 模拟获取周计划项的分组
        when(planCenterDao.listByCenterIdIn(Collections.singletonList("centerId"))).thenReturn(planCenterEntities); // 模拟获取周计划项的 Center 组

        // 调用接口
        GetPlanActivitiesResponse response = curriculumService.getPlanActivities(planId);

        // 断言
        Assertions.assertEquals(0, response.getItems().size()); // 不存在大小组活动
        Assertions.assertEquals(0, response.getCenterItems().size()); // 不存在 Center 活动
    }


    /**
     * 测试生成单元概览
     */
    @Test
    public void mockGenerateUnitOverview() {
        String unitId = UUID.randomUUID().toString();
        // 单元信息
        CurriculumUnitEntity unitEntity = new CurriculumUnitEntity();
        unitEntity.setId(unitId);
        unitEntity.setWeekCount(2);
        unitEntity.setTitle("title");
        unitEntity.setCity("city");
        unitEntity.setState("state");
        unitEntity.setDescription("description");
        unitEntity.setGrade("grade");
        Mockito.when(curriculumUnitDao.getUnitById(anyString())).thenReturn(unitEntity);
        // 获取单元概览当前版本 Prompt
        PromptEntity promptEntity = new PromptEntity();
        promptEntity.setPromptTemplate("{{ total_number_of_week }} {{ unit_name }} {{ city }} {{ state }} {{ unit_description }} {{ grade }}");
        promptEntity.setVersion("V1.0");
        promptEntity.setScene(PromptScene.UNIT_OVERVIEW.toString());
        Mockito.when(promptProvider.getActivePromptByScene(anyString())).thenReturn(promptEntity);
        // 单元概览信息
        ChatCompletionResult completionResult = new ChatCompletionResult();
        List<ChatCompletionChoice> choices = new ArrayList<>();
        ChatCompletionChoice choice = new ChatCompletionChoice();
        ChatMessage chatMessage = new ChatMessage();
        chatMessage.setContent("content");
        choice.setMessage(chatMessage);
        choices.add(choice);
        completionResult.setChoices(choices);
        when(openAIService.createChatCompletion(any())).thenReturn(completionResult);
        // 生成记录信息
        PromptUsageRecordEntity promptUsageRecord = new PromptUsageRecordEntity();
        promptUsageRecord.setId(UUID.randomUUID().toString());
        when(promptProvider.createPromptUsageRecord(anyString(), any(), any(), any(), any(), anyString())).thenReturn(promptUsageRecord);
        // 请求信息
        GenerateUnitOverviewRequest request = new GenerateUnitOverviewRequest();
        request.setUnitId(unitId);
        request.setCreateSource("AUTO");
        // 调用方法
        GenerateUnitOverviewResponse response = curriculumService.generateUnitOverview(request);
        // 验证
        Assertions.assertNotNull(response);
        Assertions.assertEquals(promptUsageRecord.getId(), response.getPromptUsageId());

    }

    /**
     * 测试生成单元概览
     */
    @Test
    public void mockGenerateUnitOverview2() {
        String unitId = UUID.randomUUID().toString();
        // 单元信息
        CurriculumUnitEntity unitEntity = new CurriculumUnitEntity();
        unitEntity.setId(unitId);
        unitEntity.setWeekCount(2);
        unitEntity.setTitle("title");
        unitEntity.setCity("city");
        unitEntity.setState("state");
        unitEntity.setDescription("description");
        unitEntity.setGrade("grade");
        Mockito.when(curriculumUnitDao.getUnitById(anyString())).thenReturn(unitEntity);
        // 获取单元概览当前版本 Prompt
        PromptEntity promptEntity = new PromptEntity();
        promptEntity.setPromptTemplate("{{ total_number_of_week }} {{ unit_name }} {{ city }} {{ state }} {{ unit_description }} {{ grade }}");
        promptEntity.setVersion("V1.0");
        promptEntity.setScene(PromptScene.UNIT_OVERVIEW.toString());
        Mockito.when(promptProvider.getActivePromptByScene(anyString())).thenReturn(promptEntity);
        // 单元概览信息
        ChatCompletionResult completionResult = new ChatCompletionResult();
        List<ChatCompletionChoice> choices = new ArrayList<>();
        ChatCompletionChoice choice = new ChatCompletionChoice();
        ChatMessage chatMessage = new ChatMessage();
        chatMessage.setContent("overview trajectory concepts  guidingQuestions");
        choice.setMessage(chatMessage);
        choices.add(choice);
        completionResult.setChoices(choices);
        when(openAIService.createChatCompletion(any())).thenReturn(completionResult);
        // 生成记录信息
        PromptUsageRecordEntity promptUsageRecord = new PromptUsageRecordEntity();
        promptUsageRecord.setId(UUID.randomUUID().toString());
        when(promptProvider.updatePromptUsageRecord(any(), anyString(), any())).thenReturn(promptUsageRecord);
        // 请求信息
        GenerateUnitOverviewRequest request = new GenerateUnitOverviewRequest();
        request.setUnitId(unitId);
        request.setCreateSource("AUTO");
        request.setUsageRecordEntity(promptUsageRecord);
        // 调用方法
        GenerateUnitOverviewResponse response = curriculumService.generateUnitOverview(request);
        // 验证
        Assertions.assertNotNull(response);
        Assertions.assertEquals(promptUsageRecord.getId(), response.getPromptUsageId());

    }


    /**
     * 测试异步生成单元概览
     */
    @Test
    public void mockGenerateUnitOverviewStream() {
        // 单元信息
        CurriculumUnitEntity unitEntity = new CurriculumUnitEntity();
        unitEntity.setId(UUID.randomUUID().toString());
        unitEntity.setWeekCount(2);
        unitEntity.setTitle("title");
        unitEntity.setCity("city");
        unitEntity.setState("state");
        unitEntity.setDescription("description");
        unitEntity.setGrade("grade");
        unitEntity.setFrameworkId(UUID.randomUUID().toString());
        Mockito.when(curriculumUnitDao.getUnitById(anyString())).thenReturn(unitEntity);
        // 获取单元概览当前版本 Prompt
        PromptEntity promptEntity = new PromptEntity();
        promptEntity.setPromptTemplate("{{ week }} {{ name }} {{ city }} {{ state }} {{ unit_description }} {{ grade }}");
        promptEntity.setVersion("V1.0");
        promptEntity.setScene(PromptScene.UNIT_OVERVIEW.toString());
        Mockito.when(promptProvider.getActivePromptByScene(anyString())).thenReturn(promptEntity);
        when(promptProvider.getPromptSceneByProject(any())).thenReturn(PromptScene.UNIT_OVERVIEW);
        when(promptProvider.getPromptSceneByAgeGroup(any(), anyString())).thenReturn(PromptScene.UNIT_OVERVIEW);
        GenerateUnitOverviewRequest request = new GenerateUnitOverviewRequest();
        request.setUnitId(unitEntity.getId());

        List<com.learninggenie.common.data.model.DomainEntity> domains = new ArrayList<>();
        DomainEntity domainEntity = new DomainEntity();
        domainEntity.setId(UUID.randomUUID().toString());
        domains.add(domainEntity);
        when(frameworkProvider.getFrameworkMeasuresTreeWithLevels(anyString())).thenReturn(domains);

        // 调用方法
        SseEmitter sseEmitter = curriculumService.generateUnitOverviewStream(request);
        // 验证
        Assertions.assertNotNull(sseEmitter);
    }

    /**
     * 测试创建单元概览测试
     */
    @Test
    public void mockCreateUnitOverviewTest() {
        // 请求信息
        CreateUnitOverviewTestRequest request = new CreateUnitOverviewTestRequest();
        String promptId = UUID.randomUUID().toString();
        String unitId = UUID.randomUUID().toString();
        int testCount = 1;
        request.setPromptId(promptId);
        request.setUnitId(unitId);
        request.setTestCount(testCount);
        // 当前用户 ID
        String userId = UUID.randomUUID().toString();
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        // 调用方法
        CreatePromptTestResponse response = curriculumService.createUnitOverviewTest(request);
        // 验证
        Assertions.assertNotNull(response.getTestRecordId());
        verify(promptTestRecordDao, times(1)).save(any());
        verify(promptProvider, times(1)).createPromptTestUsageRecords(any(), any(), anyString());
        ArgumentCaptor<PromptTestRecordEntity> captor = ArgumentCaptor.forClass(PromptTestRecordEntity.class);
        verify(promptTestRecordDao).save(captor.capture());
        PromptTestRecordEntity recordEntity = captor.getValue();
        Assertions.assertEquals(promptId, recordEntity.getPromptId());
        Assertions.assertEquals(userId, recordEntity.getCreateUserId());
        Assertions.assertEquals(testCount, recordEntity.getTestCount());
    }

    /**
     * 测试异步评价单元概览生成内容
     */
    @Test
    public void mockEvaluateUnitOverview() {
        // 获取记录
        PromptUsageRecordEntity usageRecordEntity = new PromptUsageRecordEntity();
        String promptId = UUID.randomUUID().toString();
        usageRecordEntity.setId(UUID.randomUUID().toString());
        usageRecordEntity.setPromptId(promptId);
        usageRecordEntity.setType("SYSTEM");
        usageRecordEntity.setCompletion("test");
        usageRecordEntity.setAdditionalData("{}");
        when(promptUsageRecordDao.getById(anyString())).thenReturn(usageRecordEntity);
        // 获取评价记录
        PromptUsageObjectEntity usageObjectEntity = new PromptUsageObjectEntity();
        usageObjectEntity.setId(UUID.randomUUID().toString());
        usageObjectEntity.setUseObject(UUID.randomUUID().toString());
        usageObjectEntity.setUsageRecordId(usageRecordEntity.getId());
        when(promptUsageObjectDao.getByUseObjectAndObjectId(anyString(), anyString())).thenReturn(usageObjectEntity);
        // 获取 Prompt 信息
        PromptEntity promptEntity = new PromptEntity();
        promptEntity.setId(promptId);
        promptEntity.setVersion("V1.0");
        promptEntity.setScene(PromptScene.UNIT_OVERVIEW.toString());
        when(promptDao.getById(anyString())).thenReturn(promptEntity);
        // 生成结果信息
        PromptUsageRecordEntity evaluateRecordEntity = new PromptUsageRecordEntity();
        evaluateRecordEntity.setCompletion("test");
        when(openAIProvider.createChatCompletion(anyString(), any(), any(), any(), anyString())).thenReturn(evaluateRecordEntity);
        // 调用方法
        EvaluatePromptResponse evaluatePromptResponse = curriculumService.evaluateUnitOverview(usageRecordEntity.getId());
        // 验证
        Assertions.assertNotNull(evaluatePromptResponse);
        // 验证内容一致
        Assertions.assertEquals(evaluateRecordEntity.getCompletion(), evaluatePromptResponse.getResult());
    }

    /**
     * 测试异步评价单元概览生成内容
     */
    @Test
    public void mockEvaluateUnitOverviewFlag() {
        // 获取记录
        PromptUsageRecordEntity usageRecordEntity = new PromptUsageRecordEntity();
        String promptId = UUID.randomUUID().toString();
        usageRecordEntity.setId(UUID.randomUUID().toString());
        usageRecordEntity.setPromptId(promptId);
        usageRecordEntity.setType("SYSTEM");
        usageRecordEntity.setCompletion("test");
        usageRecordEntity.setAdditionalData("{}");
        when(promptUsageRecordDao.getById(anyString())).thenReturn(usageRecordEntity);
        // 获取 Prompt 信息
        PromptEntity promptEntity = new PromptEntity();
        promptEntity.setId(promptId);
        promptEntity.setVersion("V1.0");
        promptEntity.setScene(PromptScene.UNIT_OVERVIEW.toString());
        promptEntity.setEvaluatePromptTemplate("test");
        when(promptDao.getById(anyString())).thenReturn(promptEntity);
        // 单元信息
        CurriculumUnitEntity unitEntity = new CurriculumUnitEntity();
        unitEntity.setWeekCount(2);
        unitEntity.setTitle("title");
        unitEntity.setCity("city");
        unitEntity.setState("state");
        unitEntity.setDescription("description");
        unitEntity.setGrade("grade");
        when(curriculumUnitDao.getUnitById(anyString())).thenReturn(unitEntity);
        // 生成结果信息
        PromptUsageRecordEntity evaluateRecordEntity = new PromptUsageRecordEntity();
        evaluateRecordEntity.setCompletion("test");
        when(openAIProvider.createChatCompletion("test", promptEntity, CreateSource.MANUAL, UseType.EVALUATE, null)).thenReturn(evaluateRecordEntity);
        // 调用方法
        EvaluatePromptResponse evaluatePromptResponse = curriculumService.evaluateUnitOverview(usageRecordEntity.getId());
        //验证
        Assertions.assertNotNull(evaluatePromptResponse);
        // 验证内容一致
        Assertions.assertEquals(evaluateRecordEntity.getCompletion(), evaluatePromptResponse.getResult());
    }

    /**
     * 测试重新生成单元下课程
     */
    @Test
    public void mockRedesignUnitLessons() {
        // 准备数据
        String unitId = UUID.randomUUID().toString();
        String planId = UUID.randomUUID().toString();
        List<CurriculumUnitPlanEntity> unitPlanEntities = new ArrayList<>();
        CurriculumUnitPlanEntity unitPlanEntity = new CurriculumUnitPlanEntity();
        unitPlanEntity.setUnitId(unitId);
        unitPlanEntity.setPlanId(planId);
        unitPlanEntities.add(unitPlanEntity);
        when(curriculumUnitPlanDao.getPlansByUnitId(unitId)).thenReturn(unitPlanEntities);
        // 主题信息 ThemeId
        String themeId = UUID.randomUUID().toString();
        List<LessonThemeModel> allThemes = new ArrayList<>();
        LessonThemeModel themeModel = new LessonThemeModel();
        themeModel.setId(themeId);
        allThemes.add(themeModel);
        LessonGetThemesResponse lessonGetThemesResponse = new LessonGetThemesResponse();
        lessonGetThemesResponse.setThemes(allThemes);
        when(lessonService.getThemes(true, true)).thenReturn(lessonGetThemesResponse);
        // 单元下活动项信息
        List<ItemEntity> itemEntities = new ArrayList<>();
        String itemId = UUID.randomUUID().toString();
        String lessonId = UUID.randomUUID().toString();
        ItemEntity itemEntity = new ItemEntity();
        itemEntity.setId(itemId);
        itemEntity.setLessonId(lessonId);
        itemEntity.setPlanId(planId);
        itemEntities.add(itemEntity);
        when(planItemDao.listByPlanIds(anyList())).thenReturn(itemEntities);
        // 课程详情信息
        String frameworkId = UUID.randomUUID().toString();
        LessonEntity lessonEntity = new LessonEntity();
        lessonEntity.setId(lessonId);
        lessonEntity.setName("test");
        lessonEntity.setFrameworkId(frameworkId);
        lessonEntity.setSteps("[{\"ageGroupName\":\"TK (4-5)\",\"ageGroupValue\":\"4\",\"content\":\"\\u003cp\\u003e1. Begin by gathering the children in a comfortable open space, such as a rug or an empty area of the classroom. Explain the importance of exercise for their bodies and how it can be fun. Play the song \\\"Head, Shoulders, Knees, and Toes\\\" and encourage the children to follow along with the movements. Repeat the song a few times to ensure that children are comfortable with the movements.\\u003cbr\\u003e2. Set up an obstacle course with different types of physical activities such as jumping, crawling, and balancing. Explain the different activities to the children and demonstrate how to do them. Encourage the children to complete the obstacle course as many times as they want, taking breaks as needed, to build their endurance and intensity.\\u003cbr\\u003e3. After the obstacle course, lead the children in a cool-down stretching session. Ask them to lay down on the yoga mats and guide them through simple yoga poses, such as the child\\u0027s pose and the mountain pose, to help them relax and stretch their muscles.\\u003cbr\\u003e4. Introduce the concept of dance by showing the children images of different dance movements, such as spinning, jumping, and sliding. Explain that dance is a form of movement expression that can be done to music. Play a fun and upbeat song and encourage the children to try out different movements through space, using their creativity and body control. Provide guidance and support as needed.\\u003cbr\\u003e5. After the dance session, ask the children to reflect on how exercise and dance make them feel. Encourage them to share their thoughts and feelings with the group. Use open-ended questions such as \\\"What was your favorite part of the activity?\\\" or \\\"How did your body feel during the exercise?\\\"\\u003cbr\\u003e6. Conclude the activity by reminding the children that exercise is fun and healthy for their bodies. Encourage them to continue engaging in physical activities and trying out new movements and dance styles.\\u003cp\\u003e\",\"lessonStepGuides\":[{\"measureId\":\"94D0392C-55D3-EB11-9C19-4CCC6ACF6129\",\"core\":false,\"measureAbbreviation\":\"PD-HLTH2\",\"measureName\":\"Gross Locomotor Movement Skills\",\"typicalBehaviors\":\"- Child crawls through the obstacle course with increasing proficiency and coordination.\\n- Child demonstrates improved body control while jumping and running through the obstacle course.\",\"sortIndex\":48},{\"measureId\":\"9CD0392C-55D3-EB11-9C19-4CCC6ACF6129\",\"core\":false,\"measureAbbreviation\":\"PD-HLTH9\",\"measureName\":\"Active Physical Play\",\"typicalBehaviors\":\"- Child engages in active physical play with sustained endurance, taking breaks as needed.\\n- Child shows increasing intensity during active physical play by attempting more challenging activities.\",\"sortIndex\":57},{\"measureId\":\"B1D0392C-55D3-EB11-9C19-4CCC6ACF6129\",\"core\":false,\"measureAbbreviation\":\"VPA4\",\"measureName\":\"Dance\",\"typicalBehaviors\":\"- Child tries out different dance movements with some body control and a their own dance moves in response to the visual aids and the music.\",\"sortIndex\":70}]}]");
        lessonEntity.setCoverMediaIds("123,456");
        lessonEntity.setMaterials("{\"descriptions\":[\"- Bingo cards with pictures of different food groups (e.g. fruits, vegetables, dairy, grains, protein)\\n- Markers or bingo chips\\n- Music player and speakers\\n- Optional: dancing scarves or props\"]}");
        lessonEntity.setAttachmentMediaIds("789,101112");
        lessonEntity.setBooks("[{'password':'123123','username':'zhangsan'},{'password':'321321','username':'lisi'}]");
        lessonEntity.setVideos("[{'password':'123123','username':'zhangsan'},{'password':'321321','username':'lisi'}]");
        when(lessonDao.getById(anyString())).thenReturn(lessonEntity);
        // 框架测评点
        String measureId = UUID.randomUUID().toString();
        List<DomainEntity> allMeasures = new ArrayList<>();
        DomainEntity domainEntity = new DomainEntity();
        domainEntity.setId(measureId);
        domainEntity.setAbbreviation("abbreviation");
        allMeasures.add(domainEntity);
        when(domainDao.getAllChildDomains(anyString())).thenReturn(allMeasures);
        // 课程测评点信息
        List<LessonMeasureEntity> lessonMeasureEntities = new ArrayList<>();
        LessonMeasureEntity lessonMeasureEntity = new LessonMeasureEntity();
        lessonMeasureEntity.setLessonId(lessonId);
        lessonMeasureEntity.setMeasureId(measureId);
        lessonMeasureEntities.add(lessonMeasureEntity);
        when(lessonMeasureDao.getByLessonId(anyString())).thenReturn(lessonMeasureEntities);
        // 重新生成课程信息
        GenerateLessonResponse generateLessonResponse = new GenerateLessonResponse();
        // Prompt 使用记录 ID
        String promptUsageId = UUID.randomUUID().toString();
        generateLessonResponse.setPromptUsageId(promptUsageId);
        LessonModel newLesson = new LessonModel();
        newLesson.setName("newLesson");
        newLesson.setAgeGroup("ageGroup");
        newLesson.setPrepareTime("10");
        newLesson.setActivityTime("20");
        newLesson.setFrameworkId(frameworkId);
        newLesson.setObjectives("objectives");
        newLesson.setKeyVocabularyWords("keyVocabularyWords");
        newLesson.setMeasuresString("measuresString,measuresString2");
        List<LessonStepModel> steps = new ArrayList<>();
        LessonStepModel lessonStepModel = new LessonStepModel();
        lessonStepModel.setAgeGroupName("ageGroupName");
        lessonStepModel.setAgeGroupValue("5,6");
        lessonStepModel.setContent("content");
        List<LessonStepGuideModel> guides = new ArrayList<>();
        LessonStepGuideModel lessonStepGuideModel = new LessonStepGuideModel();
        lessonStepGuideModel.setMeasureId(measureId);
        lessonStepGuideModel.setMeasureName("measureName");
        lessonStepGuideModel.setMeasureAbbreviation("measureAbbreviation");
        lessonStepGuideModel.setTypicalBehaviors("typicalBehaviors");
        guides.add(lessonStepGuideModel);
        lessonStepModel.setLessonStepGuides(guides);
        steps.add(lessonStepModel);
        newLesson.setSteps(steps);
        generateLessonResponse.setLesson(newLesson);
        when(lessonService.redesignLesson(any())).thenReturn(generateLessonResponse);
        // 保存课程信息
        IdResponse lessonIdResponse = new IdResponse();
        // 新的课程 ID
        String newLessonId = UUID.randomUUID().toString();
        lessonIdResponse.setId(newLessonId);
        when(lessonService.createLesson(any())).thenReturn(lessonIdResponse);
        //  请求方法参数
        List<String> newMeasures = new ArrayList<>();
        newMeasures.add("newMeasure");
        newMeasures.add("newMeasure2");
        String newPrefix = "newPrefix";
        String ignorePrefix = "ignorePrefix";

        List<LessonThemeEntity> lessonThemeEntities = new ArrayList<>();
        LessonThemeEntity lessonThemeEntity = new LessonThemeEntity();
        lessonThemeEntity.setThemeId(themeId);
        lessonThemeEntities.add(lessonThemeEntity);
        when(lessonThemeDao.getByLessonId(anyString())).thenReturn(lessonThemeEntities);
        // 原有课程 DLL
        String dllId = UUID.randomUUID().toString();
        List<DLLSubjectModel> dllModels = new ArrayList<>();
        DLLSubjectModel dllModel = new DLLSubjectModel();
        dllModel.setId(dllId);
        dllModel.setTitle("title");
        dllModel.setContent("content");
        dllModels.add(dllModel);
        when(lessonService.getDLLModel(anyString())).thenReturn(dllModels);
        // 生成典型行为
        GenerateTypicalBehaviorResponse generateTypicalBehaviorResponse = new GenerateTypicalBehaviorResponse();
        generateTypicalBehaviorResponse.setPromptUsageId(promptUsageId);
        List<LessonStepGuideModel> typicalBehaviors = new ArrayList<>();
        LessonStepGuideModel typicalBehavior = new LessonStepGuideModel();
        typicalBehavior.setMeasureAbbreviation("newMeasure");
        typicalBehavior.setMeasureId(measureId);
        typicalBehaviors.add(typicalBehavior);
        generateTypicalBehaviorResponse.setTypicalBehaviors(typicalBehaviors);
        when(lessonService.generateTypicalBehavior(anyString(), anyString(), any())).thenReturn(generateTypicalBehaviorResponse);
        // 生成差异教学内容
        GenerateUniversalDesignForLearningResponse generateUniversalDesignForLearningResponse = new GenerateUniversalDesignForLearningResponse();
        generateUniversalDesignForLearningResponse.setUniversalDesignForLearning("universalDesignForLearning");
        generateUniversalDesignForLearningResponse.setPromptUsageId(promptUsageId);
        when(lessonService.generateUniversalDesignForLearning(unitId, null, newLessonId)).thenReturn(generateUniversalDesignForLearningResponse);
        // 生成文化教学内容
        GenerateCulturallyResponsiveInstructionResponse generateCulturallyResponsiveInstructionResponse = new GenerateCulturallyResponsiveInstructionResponse();
        generateCulturallyResponsiveInstructionResponse.setCulturallyResponsiveInstruction("culturallyResponsiveInstruction");
        generateCulturallyResponsiveInstructionResponse.setPromptUsageId(promptUsageId);
        when(lessonService.generateCulturallyResponsiveInstruction(unitId, null, newLessonId)).thenReturn(generateCulturallyResponsiveInstructionResponse);
        // 家庭活动内容
        GenerateHomeActivityResponse generateHomeActivityResponse = new GenerateHomeActivityResponse();
        generateHomeActivityResponse.setHomeActivity("homeActivities");
        generateHomeActivityResponse.setPromptUsageId(promptUsageId);
        when(lessonService.generateHomeActivity(unitId, null, newLessonId)).thenReturn(generateHomeActivityResponse);
        // 调用方法
        curriculumService.redesignUnitLessons(unitId, newMeasures, newPrefix, ignorePrefix);
        // 验证 -- 重新生成课程信息
        ArgumentCaptor<RedesignLessonRequest> argumentCaptor = ArgumentCaptor.forClass(RedesignLessonRequest.class);
        verify(lessonService).redesignLesson(argumentCaptor.capture());
        RedesignLessonRequest request = argumentCaptor.getValue();
        // 验证
        assertEquals(unitId, request.getUnitId());
        assertEquals(planId, request.getPlanId());
        assertEquals(itemId, request.getItemId());
        // 验证 更新了一次
        verify(lessonService, times(1)).generateUniversalDesignForLearning(unitId, null, newLessonId);
        // 验证 更新了一次
        verify(lessonService, times(1)).generateCulturallyResponsiveInstruction(unitId, null, newLessonId);
        // 验证 更新了一次
        verify(lessonService, times(1)).generateHomeActivity(unitId, null, newLessonId);
    }

    /**
     * 测试比较单元下课程
     */
    @Test
    public void mockCompareUnitRedesignedLessons() {
        // 单元信息
        String unitId = UUID.randomUUID().toString();
        CurriculumUnitEntity unitEntity = new CurriculumUnitEntity();
        unitEntity.setId(unitId);
        unitEntity.setTitle("title");
        when(curriculumUnitDao.getById(anyString())).thenReturn(unitEntity);
        // 单元下周计划列表
        String planId = UUID.randomUUID().toString();
        List<CurriculumUnitPlanEntity> unitPlanEntities = new ArrayList<>();
        CurriculumUnitPlanEntity unitPlanEntity = new CurriculumUnitPlanEntity();
        unitPlanEntity.setPlanId(planId);
        unitPlanEntity.setUnitId(unitId);
        unitPlanEntity.setNumber(1);
        unitPlanEntities.add(unitPlanEntity);
        when(curriculumUnitPlanDao.getPlansByUnitId(anyString())).thenReturn(unitPlanEntities);
        // 获取周计划列表
        List<PlanEntity> planEntities = new ArrayList<>();
        PlanEntity planEntity = new PlanEntity();
        planEntity.setId(planId);
        planEntity.setWeek(1);
        planEntity.setTheme("week theme");
        planEntities.add(planEntity);
        when(planDao.getByIds(anyList())).thenReturn(planEntities);
        // 获取单元下活动项
        List<ItemEntity> itemEntities = new ArrayList<>();
        // 包含前缀的活动项
        ItemEntity itemEntity = new ItemEntity();
        String itemId = UUID.randomUUID().toString();
        itemEntity.setId(itemId);
        itemEntity.setName("comparePrefixItem name");
        itemEntity.setPlanId(planId);
        String lessonId = UUID.randomUUID().toString();
        itemEntity.setLessonId(lessonId);
        String categoryId = UUID.randomUUID().toString();
        itemEntity.setCategoryId(categoryId);
        itemEntity.setDayOfWeek(1);
        itemEntities.add(itemEntity);
        // 无活动名称的活动项
        ItemEntity itemEntity2 = new ItemEntity();
        String itemId2 = UUID.randomUUID().toString();
        itemEntity2.setId(itemId2);
        itemEntity2.setName("Item name");
        itemEntity2.setPlanId(planId);
        String lessonId2 = UUID.randomUUID().toString();
        itemEntity2.setLessonId(lessonId2);
        itemEntity2.setCategoryId(categoryId);
        itemEntity2.setDayOfWeek(2);
        itemEntities.add(itemEntity2);
        // 不包含前缀的活动项
        ItemEntity itemEntity3 = new ItemEntity();
        String itemId3 = UUID.randomUUID().toString();
        itemEntity3.setId(itemId3);
        itemEntity3.setName("item name");
        itemEntity3.setPlanId(planId);
        String lessonId3 = UUID.randomUUID().toString();
        itemEntity3.setLessonId(lessonId3);
        itemEntity3.setCategoryId(categoryId);
        itemEntity3.setDayOfWeek(3);
        itemEntities.add(itemEntity3);
        when(planItemDao.listByPlanId(anyString())).thenReturn(itemEntities);
        // 原课程
        LessonDetailResponse lessonDetail = new LessonDetailResponse();
        lessonDetail.setId(lessonId);
        lessonDetail.setName("lesson name");
        lessonDetail.setPrepareTime(10L);
        lessonDetail.setActivityTime(20L);
        lessonDetail.setUniversalDesignForLearning("universalDesignForLearning");
        lessonDetail.setCulturallyResponsiveInstruction("culturallyResponsiveInstruction");
        lessonDetail.setHomeActivities("homeActivity");
        FrameworkEntity framework = new FrameworkEntity();
        String frameworkId = UUID.randomUUID().toString();
        framework.setId(frameworkId);
        framework.setName("framework name");
        lessonDetail.setFramework(framework);
        List<MeasureEntity> measures = new ArrayList<>();
        MeasureEntity measure = new MeasureEntity();
        String measureId = UUID.randomUUID().toString();
        measure.setId(measureId);
        measure.setName("measure name");
        measure.setAbbreviation("measure abbreviation");
        measures.add(measure);
        lessonDetail.setMeasures(measures);
        List<String> objectives = new ArrayList<>();
        objectives.add("objective");
        objectives.add("objective2");
        lessonDetail.setObjectives(objectives);
        LessonMaterialModel materialModel = new LessonMaterialModel();
        materialModel.setDescriptions(new String[]{"material description", "material description2"});
        lessonDetail.setMaterials(materialModel);
        List<DLLSubjectModel> dlls = new ArrayList<>();
        DLLSubjectModel dll = new DLLSubjectModel();
        dll.setContent("dll content");
        dlls.add(dll);
        lessonDetail.setDlls(dlls);
        List<LessonStepModel> steps = new ArrayList<>();
        LessonStepModel step = new LessonStepModel();
        step.setContent("step content");
        List<LessonStepGuideModel> guides = new ArrayList<>();
        LessonStepGuideModel lessonStepGuideModel = new LessonStepGuideModel();
        lessonStepGuideModel.setMeasureId(measureId);
        lessonStepGuideModel.setMeasureName("measureName");
        lessonStepGuideModel.setMeasureAbbreviation("measureAbbreviation");
        lessonStepGuideModel.setTypicalBehaviors("typicalBehaviors");
        guides.add(lessonStepGuideModel);
        step.setLessonStepGuides(guides);
        steps.add(step);
        lessonDetail.setSteps(steps);
        List<String> ages = new ArrayList<>();
        ages.add("5");
        ages.add("6");
        lessonDetail.setAges(ages);
        when(lessonService.getLessonDetail(anyString())).thenReturn(lessonDetail);
        // 原课程分类
        CategoryEntity categoryEntity = new CategoryEntity();
        categoryEntity.setId(categoryId);
        categoryEntity.setName("category name");
        when(planCategoryDao.get(anyString())).thenReturn(categoryEntity);
        // 原课程测评点
        List<ItemMeasureDetailEntity> itemMeasureDetailEntities = new ArrayList<>();
        ItemMeasureDetailEntity itemMeasureDetailEntity = new ItemMeasureDetailEntity();
        itemMeasureDetailEntity.setMeasureId(measureId);
        itemMeasureDetailEntity.setMeasureAbbr("measure abbreviation");
        itemMeasureDetailEntity.setMeasureName("measure name");
        itemMeasureDetailEntities.add(itemMeasureDetailEntity);
        when(planItemMeasureDao.listItemMeasureDetailsByItemId(anyString())).thenReturn(itemMeasureDetailEntities);
        // 新课程 Prompt
        List<PromptUsageObjectEntity> promptUsageObjectEntities = new ArrayList<>();
        PromptUsageObjectEntity promptUsageObjectEntity = new PromptUsageObjectEntity();
        String usageObjectId = UUID.randomUUID().toString();
        promptUsageObjectEntity.setId(usageObjectId);
        promptUsageObjectEntity.setUsageRecordId(UUID.randomUUID().toString());
        promptUsageObjectEntity.setUseObject("LESSON");
        promptUsageObjectEntities.add(promptUsageObjectEntity);
        when(promptUsageObjectDao.listByUseObjectId(anyString())).thenReturn(promptUsageObjectEntities);
        // Prompt 使用记录
        PromptUsageRecordEntity usageRecordEntity = new PromptUsageRecordEntity();
        usageRecordEntity.setScore(100.00);
        when(promptUsageRecordDao.getById(anyString())).thenReturn(usageRecordEntity);
        // 请求参数
        String comparePrefix = "comparePrefix";
        String ignorePrefix = "ignorePrefix";
        JSONArray jsonArray = new JSONArray();
        // 调用方法
        curriculumService.compareUnitRedesignedLessons(planId, comparePrefix, ignorePrefix, jsonArray);
        // 验证结果
        verify(planDao, times(1)).getByIds(anyList());
        // 验证结果
        verify(curriculumUnitPlanDao, times(1)).getPlansByUnitId(anyString());
        // 验证结果
        verify(planItemDao, times(1)).listByPlanId(anyString());
    }

    /**
     * 测试重新生成单元下课程的典型行为
     */
    @Test
    public void mockRegenerateUnitLessonTypicalBehaviors() {
        String unitId = UUID.randomUUID().toString();
        String planId = UUID.randomUUID().toString();
        // 获取单元下周计划列表
        List<CurriculumUnitPlanEntity> unitPlanEntities = new ArrayList<>();
        CurriculumUnitPlanEntity unitPlanEntity = new CurriculumUnitPlanEntity();
        unitPlanEntity.setId(UUID.randomUUID().toString());
        unitPlanEntity.setUnitId(unitId);
        unitPlanEntity.setPlanId(planId);
        unitPlanEntities.add(unitPlanEntity);
        when(curriculumUnitPlanDao.getPlansByUnitId(anyString())).thenReturn(unitPlanEntities);
        // 获取单元下活动项
        List<ItemEntity> itemEntities = new ArrayList<>();
        // 课程 id 为空的活动项
        ItemEntity itemEntity = new ItemEntity();
        itemEntity.setLessonId(null);
        itemEntities.add(itemEntity);
        // 课程 id 不为空的活动项
        ItemEntity itemEntity2 = new ItemEntity();
        String lessonId = UUID.randomUUID().toString();
        itemEntity2.setLessonId(lessonId);
        itemEntity2.setName("item name");
        itemEntity2.setPlanId(planId);
        itemEntities.add(itemEntity2);
        when(planItemDao.listByPlanIds(anyList())).thenReturn(itemEntities);
        // 获取新课程
        String frameworkId = UUID.randomUUID().toString();
        LessonEntity lessonEntity = new LessonEntity();
        lessonEntity.setId(lessonId);
        lessonEntity.setName("lesson name");
        lessonEntity.setFrameworkId(frameworkId);
        lessonEntity.setSteps("[{\"ageGroupName\":\"TK (4-5)\",\"ageGroupValue\":\"4\",\"content\":\"\\u003cp\\u003e1. Begin by reading The Tiny Seed by Eric Carle to the class. Ask open-ended questions such as:\\u003cbr\\u003e   • What do you think will happen to the tiny seed?\\u003cbr\\u003e   • What do you notice about the pictures in the book?\\u003cbr\\u003e   • Have you seen any plants like the ones in the book before?\\u003cbr\\u003e\\u003cbr\\u003e2. After reading the book, show the class a large picture of a plant with labels for the different parts. Explain the different parts of the plant and how they all work together to help the plant grow.\\u003cbr\\u003e\\u003cbr\\u003e3. Facilitate a discussion with the children about what they know and what they want to know about seeds and how they grow into plants or trees. Write down their questions on a piece of chart paper or whiteboard.\\u003cbr\\u003e\\u003cbr\\u003e4. Explain to the children that they will now create their own drawings of a plant and label its parts. Provide them with drawing paper and crayons or colored pencils. Encourage them to be creative and use their imaginations.\\u003cbr\\u003e\\u003cbr\\u003e5. As the children are drawing, circulate around the room and ask open-ended questions such as:\\u003cbr\\u003e   • What part of the plant are you drawing now?\\u003cbr\\u003e   • How does your plant grow?\\u003cbr\\u003e   • What colors are you using for your plant?\\u003cbr\\u003e\\u003cbr\\u003e6. Once the children have finished their drawings, have them share their artwork with the class. Encourage them to explain the different parts of their plant and how it grows, while also discussing any stories or songs they associate with plants.\\u003cbr\\u003e\\u003cbr\\u003e7. Conclude the lesson by revisiting the questions on the chart paper or whiteboard and seeing if any of them were answered during the activity. Encourage the children to continue learning about plants and their growth, and to express their interests in books, songs, or other literacy activities related to the topic.\\u003cp\\u003e\",\"lessonStepGuides\":[{\"measureId\":\"\",\"core\":false,\"measureAbbreviation\":\"LLD5\",\"measureName\":\"\",\"typicalBehaviors\":\"- Child initiates a conversation about their plant drawing, using vocabulary words like \\\"seed,\\\" \\\"roots,\\\" \\\"stem,\\\" \\\"leaves,\\\" and \\\"flowers\\\" to describe the different parts of their plant.\\r\\n- Child listens attentively to the story The Tiny Seed by Eric Carle and engages in a discussion by asking questions or making comments about the story, showing interest in books and stories about plants.\",\"sortIndex\":20},{\"measureId\":\"85D0392C-55D3-EB11-9C19-4CCC6ACF6129\",\"core\":false,\"measureAbbreviation\":\"COG11\",\"measureName\":\"Knowledge of the Natural World\",\"typicalBehaviors\":\"- Child demonstrates knowledge of the natural world by identifying the different parts of a plant in their drawing and explaining how each part contributes to the plant\\u0027s growth, such as roots absorbing water and nutrients, and leaves helping with photosynthesis.\\r\\n- Child shares their understanding of the basic needs of living things, such as water and food, while discussing the growth and changes in the plants from the story The Tiny Seed or during their drawing activity.\",\"sortIndex\":44},{\"measureId\":\"AED0392C-55D3-EB11-9C19-4CCC6ACF6129\",\"core\":false,\"measureAbbreviation\":\"VPA1\",\"measureName\":\"Visual Art\",\"typicalBehaviors\":\"- Child creatively expresses their understanding of plants through their artwork, using different colors and details to represent the various parts of a plant, such as roots, stem, leaves, and flowers.\\r\\n- Child experiments with various techniques, such as shading, blending, or adding texture, to create a visually appealing and detailed representation of a plant in their drawing.\",\"sortIndex\":67}]}]");
        when(lessonDao.getById(anyString())).thenReturn(lessonEntity);
        // 获取框架测评点
        List<DomainEntity> allMeasures = new ArrayList<>();
        DomainEntity domainEntity = new DomainEntity();
        domainEntity.setAbbreviation("LLD5");
        allMeasures.add(domainEntity);
        when(domainDao.getAllChildDomains(anyString())).thenReturn(allMeasures);
        // 生成典型行为
        String promptUsageId = UUID.randomUUID().toString();
        GenerateTypicalBehaviorResponse generateTypicalBehaviorResponse = new GenerateTypicalBehaviorResponse();
        generateTypicalBehaviorResponse.setPromptUsageId(promptUsageId);
        List<LessonStepGuideModel> typicalBehaviors = new ArrayList<>();
        LessonStepGuideModel lessonStepGuideModel = new LessonStepGuideModel();
        lessonStepGuideModel.setMeasureAbbreviation("COG3");
        typicalBehaviors.add(lessonStepGuideModel);
        LessonStepGuideModel lessonStepGuideModel2 = new LessonStepGuideModel();
        lessonStepGuideModel2.setMeasureAbbreviation("COG4");
        typicalBehaviors.add(lessonStepGuideModel2);
        generateTypicalBehaviorResponse.setTypicalBehaviors(typicalBehaviors);
        when(lessonService.generateTypicalBehavior(anyString(), anyString(), any())).thenReturn(generateTypicalBehaviorResponse);
        // 请求参数
        List<String> regenerateMeasures = new ArrayList<>();
        regenerateMeasures.add("LLD4");
        regenerateMeasures.add("COG10");
        regenerateMeasures.add("VPA2");
        regenerateMeasures.add("PD-HLTH5");
        String matchPrefix = "lesson name";
        // 调用方法
        curriculumService.regenerateUnitLessonTypicalBehaviors(unitId, regenerateMeasures, matchPrefix);
        // 验证结果 保存 Prompt 使用记录和典型行为关系
        verify(promptProvider, times(1)).createPromptUsageObject(anyString(), any(), anyString());
        // 验证生成典型行为
        verify(lessonService, times(1)).generateTypicalBehavior(anyString(), anyString(), any());
    }

    /**
     * 测试重新生成单元下课程内容
     */
    @Test
    public void mockRegenerateUnitLessonContent() {
        // 获取单元下周计划列表
        String unitId = UUID.randomUUID().toString();
        String planId = UUID.randomUUID().toString();
        String lessonId = UUID.randomUUID().toString();
        List<CurriculumUnitPlanEntity> unitPlanEntities = new ArrayList<>();
        CurriculumUnitPlanEntity curriculumUnitPlanEntity = new CurriculumUnitPlanEntity();
        curriculumUnitPlanEntity.setPlanId(planId);
        curriculumUnitPlanEntity.setUnitId(unitId);
        unitPlanEntities.add(curriculumUnitPlanEntity);
        when(curriculumUnitPlanDao.getPlansByUnitId(anyString())).thenReturn(unitPlanEntities);
        // 获取单元下活动项
        List<ItemEntity> itemEntities = new ArrayList<>();
        // lessonId 为空的活动项
        ItemEntity itemEntity = new ItemEntity();
        itemEntity.setLessonId("");
        itemEntities.add(itemEntity);
        // lessonId 不为空的活动项
        ItemEntity itemEntity2 = new ItemEntity();
        itemEntity2.setLessonId(lessonId);
        itemEntities.add(itemEntity2);
        when(planItemDao.listByPlanIds(anyList())).thenReturn(itemEntities);
        // 课程详情
        LessonEntity lessonEntity = new LessonEntity();
        lessonEntity.setId(lessonId);
        lessonEntity.setName("lesson name");
        when(lessonDao.getById(anyString())).thenReturn(lessonEntity);
        // 生成差异教学内容
        String promptUsageId = UUID.randomUUID().toString();
        GenerateUniversalDesignForLearningResponse generateUniversalDesignForLearningResponse = new GenerateUniversalDesignForLearningResponse();
        generateUniversalDesignForLearningResponse.setUniversalDesignForLearning("universalDesignForLearning");
        generateUniversalDesignForLearningResponse.setPromptUsageId(promptUsageId);
        when(lessonService.generateUniversalDesignForLearning(unitId, null, lessonId)).thenReturn(generateUniversalDesignForLearningResponse);
        // 生成文化教学内容
        GenerateCulturallyResponsiveInstructionResponse generateCulturallyResponsiveInstructionResponse = new GenerateCulturallyResponsiveInstructionResponse();
        generateCulturallyResponsiveInstructionResponse.setCulturallyResponsiveInstruction("culturallyResponsiveInstruction");
        generateCulturallyResponsiveInstructionResponse.setPromptUsageId(promptUsageId);
        when(lessonService.generateCulturallyResponsiveInstruction(unitId, null, lessonId)).thenReturn(generateCulturallyResponsiveInstructionResponse);
        // 家庭活动内容
        GenerateHomeActivityResponse generateHomeActivityResponse = new GenerateHomeActivityResponse();
        generateHomeActivityResponse.setHomeActivity("homeActivities");
        generateHomeActivityResponse.setPromptUsageId(promptUsageId);
        when(lessonService.generateHomeActivity(unitId, null, lessonId)).thenReturn(generateHomeActivityResponse);
        // 请求参数
        String matchPrefix = "lesson name";
        // 调用方法
        curriculumService.regenerateUnitLessonContent(unitId, matchPrefix);
        // 保存 Prompt 使用记录和生成内容的关系
        verify(promptProvider, times(3)).createPromptUsageObject(anyString(), any(), anyString());
    }

    /**
     *
     */
    @Test
    public void mockGenerateUnitDocument() {
        String unitId = UUID.randomUUID().toString();
        String planId = UUID.randomUUID().toString();
        String itemId = UUID.randomUUID().toString();
        String lessonId = UUID.randomUUID().toString();
        // 查询单元信息
        CurriculumUnitEntity curriculumUnit = new CurriculumUnitEntity();
        curriculumUnit.setId(unitId);
        curriculumUnit.setTitle("unit title");
        curriculumUnit.setOverview("unit overview");
        curriculumUnit.setTrajectory("unit trajectory");
        curriculumUnit.setConcepts("unit concepts");
        curriculumUnit.setGuidingQuestions("unit guiding questions");
        when(curriculumUnitDao.getUnitById(anyString())).thenReturn(curriculumUnit);
        // 根据单元 ID 获取单元下面的周计划
        List<CurriculumUnitPlanEntity> plansByUnitId = new ArrayList<>();
        CurriculumUnitPlanEntity curriculumUnitPlanEntity = new CurriculumUnitPlanEntity();
        curriculumUnitPlanEntity.setPlanId(planId);
        plansByUnitId.add(curriculumUnitPlanEntity);
        when(curriculumUnitPlanDao.getPlansByUnitId(anyString())).thenReturn(plansByUnitId);
        // 根据周计划Id获取该单元下面的周计划
        List<PlanEntity> planEntities = new ArrayList<>();
        PlanEntity planEntity = new PlanEntity();
        planEntity.setId(planId);
        planEntity.setWeek(1);
        planEntity.setTheme("plan theme");
        planEntity.setOverview("plan overview");
        planEntity.setGenerateStatus("CONFIRMED");
        planEntities.add(planEntity);
        when(planDao.getByIds(anyList())).thenReturn(planEntities);
        // 根据周计划Id获取该周计划每天的活动
        List<ItemEntity> itemEntities = new ArrayList<>();
        String categoryId = UUID.randomUUID().toString();
        ItemEntity itemEntity = new ItemEntity();
        itemEntity.setId(itemId);
        itemEntity.setDayOfWeek(1);
        itemEntity.setName("item name");
        itemEntity.setCategoryId(categoryId);
        itemEntity.setDescription("item description");
        itemEntity.setLessonId(lessonId);
        itemEntity.setGenerateStatus("CONFIRMED");
        itemEntities.add(itemEntity);
        when(planItemDao.listByPlanId(anyString())).thenReturn(itemEntities);
        // 获取项目类别
        CategoryEntity categoryEntity = new CategoryEntity();
        categoryEntity.setName("category name");
        when(planCategoryDao.get(anyString())).thenReturn(categoryEntity);
        // 获取项目测评点
        List<ItemMeasureDetailEntity> itemMeasureEntities = new ArrayList<>();
        ItemMeasureDetailEntity itemMeasureEntity = new ItemMeasureDetailEntity();
        itemMeasureEntity.setMeasureAbbr("measure abbr");
        itemMeasureEntity.setMeasureName("measure name");
        itemMeasureEntities.add(itemMeasureEntity);
        when(planItemMeasureDao.listItemMeasureDetailsByItemId(anyString())).thenReturn(itemMeasureEntities);
        LessonDetailResponse lessonDetail = new LessonDetailResponse();
        lessonDetail.setId(lessonId);
        lessonDetail.setName("lesson name");
        lessonDetail.setPrepareTime(10L);
        lessonDetail.setActivityTime(20L);
        lessonDetail.setUniversalDesignForLearning("universalDesignForLearning");
        lessonDetail.setCulturallyResponsiveInstruction("culturallyResponsiveInstruction");
        lessonDetail.setHomeActivities("homeActivity");
        FrameworkEntity framework = new FrameworkEntity();
        String frameworkId = UUID.randomUUID().toString();
        framework.setId(frameworkId);
        framework.setName("framework name");
        lessonDetail.setFramework(framework);
        List<MeasureEntity> measures = new ArrayList<>();
        MeasureEntity measure = new MeasureEntity();
        String measureId = UUID.randomUUID().toString();
        measure.setId(measureId);
        measure.setName("measure name");
        measure.setAbbreviation("measure abbreviation");
        measures.add(measure);
        lessonDetail.setMeasures(measures);
        List<String> objectives = new ArrayList<>();
        objectives.add("objective");
        objectives.add("objective2");
        lessonDetail.setObjectives(objectives);
        LessonMaterialModel materialModel = new LessonMaterialModel();
        materialModel.setDescriptions(new String[]{"material description", "material description2"});
        lessonDetail.setMaterials(materialModel);
        List<DLLSubjectModel> dlls = new ArrayList<>();
        DLLSubjectModel dll = new DLLSubjectModel();
        dll.setContent("dll content");
        dlls.add(dll);
        lessonDetail.setDlls(dlls);
        List<LessonStepModel> steps = new ArrayList<>();
        LessonStepModel step = new LessonStepModel();
        step.setContent("step content");
        List<LessonStepGuideModel> guides = new ArrayList<>();
        LessonStepGuideModel lessonStepGuideModel = new LessonStepGuideModel();
        lessonStepGuideModel.setMeasureId(measureId);
        lessonStepGuideModel.setMeasureName("measureName");
        lessonStepGuideModel.setMeasureAbbreviation("measureAbbreviation");
        lessonStepGuideModel.setTypicalBehaviors("typicalBehaviors");
        guides.add(lessonStepGuideModel);
        step.setLessonStepGuides(guides);
        steps.add(step);
        lessonDetail.setSteps(steps);
        List<String> ages = new ArrayList<>();
        ages.add("5");
        ages.add("6");
        lessonDetail.setAges(ages);
        lessonDetail.setUniversalDesignForLearning("");
        lessonDetail.setCulturallyResponsiveInstruction("culturallyResponsiveInstruction");
        lessonDetail.setHomeActivities("homeActivities");
        when(lessonService.getLessonDetail(anyString())).thenReturn(lessonDetail);
        // 调用方法
        curriculumService.generateUnitDocument(unitId);
        // 验证结果
        verify(curriculumUnitDao, times(1)).getUnitById(anyString());
        verify(curriculumUnitPlanDao, times(1)).getPlansByUnitId(anyString());
        verify(planDao, times(1)).getByIds(anyList());
    }

    /**
     * 测试获取机构单元列表
     * case: 老师角色
     */
    @Test
    public void testListAgencyUnitsWithTeacherRole() {
        // 定义分页数据
        Integer pageSize = 8;
        Integer pageNum = 1;
        // 定义当前操作用户
        AuthUserDetails user = new AuthUserDetails();
        // 设置用户 ID
        user.setUsername("userId001");
        // 设置当前用户为老师角色
        user.setRole("COLLABORATOR");
        // 定义当前用户所属机构
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("agencyId001");
        // 定义 Unit 列表数据
        List<CurriculumUnitEntity> unitEntities = new ArrayList<>();
        // 定义 Unit 数据
        CurriculumUnitEntity unitEntity = new CurriculumUnitEntity();
        unitEntity.setId("unitId004");
        unitEntity.setGenerateStatus("COMPLETED");
        unitEntity.setAvatarMediaId("avatarMediaId004");
        unitEntity.setCreateSource("SYSTEM");
        unitEntities.add(unitEntity);
        // 获取 Unit 下的媒体资源 ID 集合
        List<String> mediaIds = unitEntities.stream().map(CurriculumUnitEntity::getAvatarMediaId).collect(Collectors.toList());
        // 定义分页数据
        PageList<CurriculumUnitEntity> agencyUnits = PageList.from(unitEntities, Long.parseLong(String.valueOf(pageNum)), Long.parseLong(String.valueOf(pageSize)));
        // 定义单元下的活动数
        List<UnitActivitiesModel> activitiesModels = new ArrayList<>();
        UnitActivitiesModel model = new UnitActivitiesModel();
        model.setUnitId(unitEntity.getId());
        model.setActivitiesCount(8);
        activitiesModels.add(model);
        List<String> unitIds = new ArrayList<>();
        unitIds.add(unitEntity.getId());
        // 自定义模块信息
        List<CurriculumCustomModuleRecordEntity> customModuleRecordEntities = new ArrayList<>();

        when(customModuleRecordDao.getListByMasterDataId(anyString())).thenReturn(customModuleRecordEntities);
        when(userProvider.getCurrentProject()).thenReturn("MAGIC-CURRICULUM");
        when(userProvider.getCurrentUser()).thenReturn(user);
        when(userProvider.getAgencyByUserId(user.getId())).thenReturn(agencyModel);
        when(curriculumUnitDao.listAgencyUnits(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(),
                any(), any(), any(), any(), any())).thenReturn(agencyUnits);
        when(lessonService.getMediaURLs(mediaIds)).thenReturn(new HashMap<>());
        when(curriculumUnitDao.getUnitActivitiesCount(unitIds)).thenReturn(activitiesModels);

        // 接口调用
        // 准备请求体
        ListUnitsRequest request = new ListUnitsRequest();
        request.setPageNum(1);
        request.setPageSize(10);
        ListUnitsResponse response = curriculumService.listAgencyUnits(request, new MockHttpServletRequest());

        // 验证结果
        // 列表中只有一条数据
        assertEquals(1, response.getItems().size());
        // 验证 Unit 列表的第一条 Unit 数据的 ID 与 UnitEntity 的 ID 相同
        assertEquals(unitEntity.getId(), response.getItems().get(0).getId());
        // 获取单元生成进度
        int completed = UnitProgressStatus.getProgressByKey("COMPLETED");
        // 验证 Unit 的生成进度是 100%
        assertEquals(completed, response.getItems().get(0).getProgress().intValue());
    }

    /**
     * 测试获取机构单元列表
     * case: 园长角色
     */
    @Test
    public void testListAgencyUnitsWithSiteAdminRole() {
        // 定义分页数据
        Integer pageSize = 8;
        Integer pageNum = 1;
        // 定义当前操作用户
        AuthUserDetails user = new AuthUserDetails();
        // 设置用户 ID
        user.setUsername("userId001");
        // 设置当前用户为老师角色
        user.setRole("SITE_ADMIN");
        // 定义当前用户所属机构
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("agencyId001");
        // 定义 Unit 列表数据
        List<CurriculumUnitEntity> unitEntities = new ArrayList<>();
        // 定义 Unit 数据
        CurriculumUnitEntity unitEntity = new CurriculumUnitEntity();
        unitEntity.setId("unitId004");
        unitEntity.setGenerateStatus("COMPLETED");
        unitEntity.setAvatarMediaId("avatarMediaId004");
        unitEntity.setCreateSource("SYSTEM");
        unitEntities.add(unitEntity);
        // 获取 Unit 下的媒体资源 ID 集合
        List<String> mediaIds = unitEntities.stream().map(CurriculumUnitEntity::getAvatarMediaId).collect(Collectors.toList());
        // 定义分页数据
        PageList<CurriculumUnitEntity> agencyUnits = PageList.from(unitEntities, Long.parseLong(String.valueOf(pageNum)), Long.parseLong(String.valueOf(pageSize)));
        // 定义单元下的活动数
        List<UnitActivitiesModel> activitiesModels = new ArrayList<>();
        UnitActivitiesModel model = new UnitActivitiesModel();
        model.setUnitId(unitEntity.getId());
        model.setActivitiesCount(8);
        activitiesModels.add(model);
        List<String> unitIds = new ArrayList<>();
        unitIds.add(unitEntity.getId());
        // 自定义模块信息
        List<CurriculumCustomModuleRecordEntity> customModuleRecordEntities = new ArrayList<>();

        // 数据模拟
        when(customModuleRecordDao.getListByMasterDataId(anyString())).thenReturn(customModuleRecordEntities);
        when(userProvider.getCurrentUser()).thenReturn(user);
        when(userProvider.getCurrentProject()).thenReturn("MAGIC-CURRICULUM");
        when(userProvider.getAgencyByUserId(user.getId())).thenReturn(agencyModel);
        when(curriculumUnitDao.listAgencyUnits(any(), any(), any(), any(), any(), any(), any(),
                any(), any(), any(), any(), any(), any(), any(), any(), any())).thenReturn(agencyUnits);
        when(lessonService.getMediaURLs(mediaIds)).thenReturn(new HashMap<>());
        when(curriculumUnitDao.getUnitActivitiesCount(unitIds)).thenReturn(activitiesModels);

        // 接口调用
        // 准备请求体
        ListUnitsRequest request = new ListUnitsRequest();
        request.setPageNum(1);
        request.setPageSize(10);
        ListUnitsResponse response = curriculumService.listAgencyUnits(request, new MockHttpServletRequest());

        // 验证结果
        // 列表中只有一条数据
        assertEquals(1, response.getItems().size());
        // 验证 Unit 列表的第一条 Unit 数据的 ID 与 UnitEntity 的 ID 相同
        assertEquals(unitEntity.getId(), response.getItems().get(0).getId());
        // 获取单元生成进度
        int completed = UnitProgressStatus.getProgressByKey("COMPLETED");
        // 验证 Unit 的生成进度是 100%
        assertEquals(completed, response.getItems().get(0).getProgress().intValue());
    }

    /**
     * 测试获取机构单元列表
     * case: 管理员角色
     */
    @Test
    public void testListAgencyUnitsWithAgencyRole() {
        // 定义分页数据
        Integer pageSize = 8;
        Integer pageNum = 1;
        // 定义当前操作用户
        AuthUserDetails user = new AuthUserDetails();
        // 设置用户 ID
        user.setUsername("userId001");
        // 设置当前用户为老师角色
        user.setRole("COLLABORATOR");
        // 定义当前用户所属机构
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("agencyId001");
        // 定义 Unit 列表数据
        List<CurriculumUnitEntity> unitEntities = new ArrayList<>();
        // 定义 Unit 数据
        CurriculumUnitEntity unitEntity1 = new CurriculumUnitEntity();
        unitEntity1.setId("unitId001");
        unitEntity1.setGenerateStatus("INITIALIZED");
        unitEntity1.setAvatarMediaId("avatarMediaId001");
        unitEntity1.setCreateSource("CREATE_GENIE");
        unitEntities.add(unitEntity1);
        CurriculumUnitEntity unitEntity2 = new CurriculumUnitEntity();
        unitEntity2.setId("unitId002");
        unitEntity2.setGenerateStatus("WEEK_OVERVIEW_GENERATED");
        unitEntity2.setAvatarMediaId("avatarMediaId002");
        unitEntity2.setCreateSource("CREATE_GENIE");
        unitEntities.add(unitEntity2);
        CurriculumUnitEntity unitEntity3 = new CurriculumUnitEntity();
        unitEntity3.setId("unitId003");
        unitEntity3.setGenerateStatus("WEEK_OVERVIEW_CONFIRMED");
        unitEntity3.setAvatarMediaId("avatarMediaId003");
        unitEntity3.setCreateSource("CREATE_GENIE");
        unitEntities.add(unitEntity3);
        CurriculumUnitEntity unitEntity = new CurriculumUnitEntity();
        unitEntity.setId("unitId004");
        unitEntity.setGenerateStatus("COMPLETED");
        unitEntity.setAvatarMediaId("avatarMediaId004");
        unitEntity.setCreateSource("SYSTEM");
        unitEntities.add(unitEntity);
        List<String> mediaIds = unitEntities.stream().map(CurriculumUnitEntity::getAvatarMediaId).collect(Collectors.toList());
        // 定义分页数据
        PageList<CurriculumUnitEntity> agencyUnits = PageList.from(unitEntities, Long.parseLong(String.valueOf(pageNum)), Long.parseLong(String.valueOf(pageSize)));
        // 定义单元下的活动数
        List<UnitActivitiesModel> activitiesModels = new ArrayList<>();
        UnitActivitiesModel model = new UnitActivitiesModel();
        model.setUnitId(unitEntity.getId());
        model.setActivitiesCount(8);
        activitiesModels.add(model);
        List<String> unitIds = new ArrayList<>();
        unitIds.add(unitEntity.getId());

        // 数据模拟
        when(userProvider.getCurrentUser()).thenReturn(user);
        when(userProvider.getCurrentProject()).thenReturn("MAGIC-CURRICULUM");
        when(userProvider.getAgencyByUserId(user.getId())).thenReturn(agencyModel);
        when(curriculumUnitDao.listAgencyUnits(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())).thenReturn(agencyUnits);
        when(lessonService.getMediaURLs(mediaIds)).thenReturn(new HashMap<>());
        when(curriculumUnitDao.getUnitActivitiesCount(unitIds)).thenReturn(activitiesModels);

        // 接口调用
        // 准备请求体
        ListUnitsRequest request = new ListUnitsRequest();
        request.setPageNum(1);
        request.setPageSize(10);
        ListUnitsResponse response = curriculumService.listAgencyUnits(request, new MockHttpServletRequest());

        // 验证结果
        // 列表中只有四条数据
        assertEquals(4, response.getItems().size());
        // 获取单元生成进度
        int completedProgress = UnitProgressStatus.getProgressByKey("COMPLETED");
        // 验证 Unit 列表中包含 Unit 的生成进度不是 100% 的单元
        int initializedProgress = UnitProgressStatus.getProgressByKey("INITIALIZED");
        assertEquals(initializedProgress, response.getItems().get(0).getProgress().intValue());
        // 进度为 40%
        int weekOverviewGeneratedProgress = UnitProgressStatus.getProgressByKey("WEEK_OVERVIEW_GENERATED");
        assertEquals(weekOverviewGeneratedProgress, response.getItems().get(1).getProgress().intValue());
        // 进度为 60%
        int weekOverviewConfirmedProgress = UnitProgressStatus.getProgressByKey("WEEK_OVERVIEW_CONFIRMED");
        assertEquals(weekOverviewConfirmedProgress, response.getItems().get(2).getProgress().intValue());
        // 验证 Unit 列表中包含 Unit 的生成进度是 100% 的单元
        assertEquals(completedProgress, response.getItems().get(3).getProgress().intValue());
    }

    /**
     * 测试应用单元到周计划
     */
    @Test
    public void testApplyUnit() {
        // 定于 Unit Id 集合
        List<String> unitIds = new ArrayList<>();
        unitIds.add("005");
        // 构造请求参数
        CurriculumApplyRequest request = new CurriculumApplyRequest();
        request.setCenterId("001");
        request.setGroupId("002");
        request.setPlanIds(Arrays.asList(new String[]{"001"}));
        request.setUnitIds(unitIds);
        request.setAiGenerated(true);
        request.setStartDate(new Date());
        request.setStartWeek(1);
        String centerId = request.getCenterId();
        String groupId = request.getGroupId();
        GroupEntry group = new GroupEntry();
        CenterEntity center = new CenterEntity();
        AuthUserDetails userDetail =new AuthUserDetails();
        AgencyModel agencyModel = new AgencyModel();
        PlanEntity planEntity = new PlanEntity();
        List<QueryPlanModel> planModels = new ArrayList<>();
        QueryPlanModel planModel = new QueryPlanModel();
        planModel.setPlanId("001");
        planModel.setUnitId("005");
        planModels.add(planModel);
        // 定义 CurriculumGenieTemplate 模板周计划
        PlanEntity planTemplate = new PlanEntity();
        planTemplate.setId("template001");
        // 定义 CurriculumGenieTemplate 模板下的分类
        List<CategoryEntity> categories = new ArrayList<>();
        CategoryEntity categoryEntity = new CategoryEntity();
        categoryEntity.setId("category001");
        categories.add(categoryEntity);
        String unitId = "786604c5-faa3-4e3b-9556-4ff208fe9353";
        CurriculumUnitPlanEntity curriculumUnitPlanEntity = new CurriculumUnitPlanEntity();
        curriculumUnitPlanEntity.setUnitId(unitId);
        // 获取年龄
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setId(unitId);
        unit.setGrade("K (5-6)");
        PlanEntity latestPlan = new PlanEntity();
        latestPlan.setId("template001");
        List<QueryPlanModel> queryPlanModels = new ArrayList<>();
        QueryPlanModel queryPlanModel = new QueryPlanModel();
        queryPlanModel.setPlanId("001");
        queryPlanModel.setUnitId("005");
        queryPlanModel.setWeek(1);
        queryPlanModel.setFrameworkId("fam001");
        queryPlanModels.add(queryPlanModel);
        // 数据准备 -- 接口模拟
        Mockito.when(groupDao.getGroup(groupId)).thenReturn(group);
        Mockito.when(userProvider.getCurrentUser()).thenReturn(userDetail);
        Mockito.when(centerDao.getCenter(centerId)).thenReturn(center);
        Mockito.when(planDao.listByUnitIds(unitIds)).thenReturn(planModels);
        Mockito.when(userProvider.getAgencyByUserId(any())).thenReturn(agencyModel);
        Mockito.when(userProvider.getAgencyOpenDefaultOpen(any(), any())).thenReturn(false);
        Mockito.when(planDao.getCurriculumGenieTemplate()).thenReturn(planTemplate);
        Mockito.when(planCategoryDao.listByPlanId("template001")).thenReturn(categories);
        Mockito.when(planDao.getAgencyCurrentWeekPlanTemplate(any())).thenReturn(planEntity);
        Mockito.when(planDao.get(any())).thenReturn(planEntity);
        Mockito.when(curriculumUnitPlanDao.getUnitPlanByPlanId(anyString())).thenReturn(curriculumUnitPlanEntity);
        Mockito.when(curriculumUnitDao.getById(anyString())).thenReturn(unit);
        Mockito.when(planService.getPlanTemplateByUnitId(anyString())).thenReturn(latestPlan);
        Mockito.when(planCategoryDao.listByPlanId(anyString())).thenReturn(categories);
        Mockito.when(planDao.listByUnitIds(anyList())).thenReturn(queryPlanModels);
        // 调用
        curriculumService.apply(request);
        // 验证只调用一次
        Mockito.verify(planDao, times(1)).listByUnitIds(any());
        Mockito.verify(groupDao, times(1)).getGroup(any());
    }

    /**
     * 测试周计划应用
     * case: Unit Planner 可以自动适配周计划模板
     */
    @Test
    public void ableAutoAdaptAgencyWeekPlanTemplate() {
        // 准备数据
        List<QueryPlanModel> planModels = new ArrayList<>();
        QueryPlanModel queryPlanModel = new QueryPlanModel();
        queryPlanModel.setPlanId("planId001");
        queryPlanModel.setWeek(1);
        queryPlanModel.setAdapted(false);
        queryPlanModel.setFrameworkId("fam001");
        queryPlanModel.setUnitId("unitId001");
        planModels.add(queryPlanModel);
        CurriculumApplyRequest request = new CurriculumApplyRequest();
        request.setCenterId("centerId001");
        request.setGroupId("groupId001");
        request.setPlanIds(Arrays.asList(new String[]{"planId001"}));
        request.setUnitIds(Arrays.asList(new String[]{"unitId001"}));
        request.setAiGenerated(true);
        request.setStartDate(new Date());
        request.setStartWeek(1);
        request.setAutoAdapt(true);
        // Unit Planner 应用周计划公共 Mock 方法
        unitPlannerApplyCommonMock(false);

        String unitId = "786604c5-faa3-4e3b-9556-4ff208fe9353";
        CurriculumUnitPlanEntity curriculumUnitPlanEntity = new CurriculumUnitPlanEntity();
        curriculumUnitPlanEntity.setUnitId(unitId);
        // 获取年龄
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setId(unitId);
        unit.setGrade("K (5-6)");
        PlanEntity latestPlan = new PlanEntity();
        latestPlan.setId("template001");
        Mockito.when(curriculumUnitPlanDao.getUnitPlanByPlanId(anyString())).thenReturn(curriculumUnitPlanEntity);
        Mockito.when(curriculumUnitDao.getById(anyString())).thenReturn(unit);
        Mockito.when(planService.getPlanTemplateByUnitId(anyString())).thenReturn(latestPlan);
        Mockito.when(planDao.listByUnitIds(anyList())).thenReturn(planModels);
        // 调用

        // 调用接口
        curriculumService.apply(request);

        // 校验结果
        Mockito.verify(curriculumPlanApplyDao, times(1)).saveBatch(any());
        Mockito.verify(planInterpretDao, times(1)).batchCreate(any());
        Mockito.verify(planDao, times(1)).createBatch(any());
        Mockito.verify(planCenterDao, times(1)).saveBatch(any());
        Mockito.verify(planCategoryDao, times(1)).batchCreate(any());
        Mockito.verify(planItemDao, times(1)).batchCreate(any());
    }

    /**
     * 测试周计划应用
     * case: Unit Planner 可以自动适配周计划模板
     */
    @Test
    public void ableAutoAdaptAgencyWeekPlanTemplateNoAgencyTemplate() {
        // 准备数据
        CurriculumApplyRequest request = new CurriculumApplyRequest();
        List<QueryPlanModel> planModels = new ArrayList<>();
        QueryPlanModel queryPlanModel = new QueryPlanModel();
        queryPlanModel.setPlanId("planId001");
        queryPlanModel.setWeek(1);
        queryPlanModel.setAdapted(false);
        queryPlanModel.setFrameworkId("fam001");
        queryPlanModel.setUnitId("unitId001");
        planModels.add(queryPlanModel);
        request.setCenterId("centerId001");
        request.setGroupId("groupId001");
        request.setPlanIds(Arrays.asList(new String[]{"planId001"}));
        request.setUnitIds(Arrays.asList(new String[]{"unitId001"}));
        request.setAiGenerated(true);
        request.setStartDate(new Date());
        request.setStartWeek(1);
        request.setAutoAdapt(true);
        // Unit Planner 应用周计划公共 Mock 方法
        unitPlannerApplyCommonMock(true);

        String unitId = "786604c5-faa3-4e3b-9556-4ff208fe9353";
        CurriculumUnitPlanEntity curriculumUnitPlanEntity = new CurriculumUnitPlanEntity();
        curriculumUnitPlanEntity.setUnitId(unitId);
        // 获取年龄
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setId(unitId);
        unit.setGrade("K (5-6)");
        PlanEntity latestPlan = new PlanEntity();
        latestPlan.setId("template001");
        Mockito.when(curriculumUnitPlanDao.getUnitPlanByPlanId(anyString())).thenReturn(curriculumUnitPlanEntity);
        Mockito.when(curriculumUnitDao.getById(anyString())).thenReturn(unit);
        Mockito.when(planService.getPlanTemplateByUnitId(anyString())).thenReturn(latestPlan);
        Mockito.when(planDao.listByUnitIds(anyList())).thenReturn(planModels);
        // 调用接口
        curriculumService.apply(request);

        // 校验结果
        Mockito.verify(curriculumPlanApplyDao, times(1)).saveBatch(any());
        Mockito.verify(planInterpretDao, times(1)).batchCreate(any());
        Mockito.verify(planDao, times(1)).createBatch(any());
        Mockito.verify(planCenterDao, times(1)).saveBatch(any());
        Mockito.verify(planCategoryDao, times(1)).batchCreate(any());
        Mockito.verify(planItemDao, times(1)).batchCreate(any());
    }

    /**
     * 测试周计划应用
     * case: Unit Planner 不可自动适配周计划模板
     */
    @Test
    public void unAbleAutoAdaptAgencyWeekPlanTemplate() {
        // 准备数据
        List<QueryPlanModel> planModels = new ArrayList<>();
        QueryPlanModel queryPlanModel = new QueryPlanModel();
        queryPlanModel.setPlanId("planId001");
        queryPlanModel.setWeek(1);
        queryPlanModel.setAdapted(false);
        queryPlanModel.setFrameworkId("fam001");
        queryPlanModel.setUnitId("unitId001");
        planModels.add(queryPlanModel);
        CurriculumApplyRequest request = new CurriculumApplyRequest();
        request.setCenterId("centerId001");
        request.setGroupId("groupId001");
        request.setPlanIds(Arrays.asList(new String[]{"planId001"}));
        request.setUnitIds(Arrays.asList(new String[]{"unitId001"}));
        request.setAiGenerated(true);
        request.setStartDate(new Date());
        request.setStartWeek(1);
        request.setAutoAdapt(false);
        // Unit Planner 应用周计划公共 Mock 方法
        unitPlannerApplyCommonMock(false);

        String unitId = "786604c5-faa3-4e3b-9556-4ff208fe9353";
        CurriculumUnitPlanEntity curriculumUnitPlanEntity = new CurriculumUnitPlanEntity();
        curriculumUnitPlanEntity.setUnitId(unitId);
        // 获取年龄
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setId(unitId);
        unit.setGrade("K (5-6)");
        PlanEntity latestPlan = new PlanEntity();
        latestPlan.setId("template001");
        Mockito.when(curriculumUnitPlanDao.getUnitPlanByPlanId(anyString())).thenReturn(curriculumUnitPlanEntity);
        Mockito.when(curriculumUnitDao.getById(anyString())).thenReturn(unit);
        Mockito.when(planService.getPlanTemplateByUnitId(anyString())).thenReturn(latestPlan);
        Mockito.when(planDao.listByUnitIds(anyList())).thenReturn(planModels);
        // 调用接口
        curriculumService.apply(request);

        // 校验结果
        Mockito.verify(curriculumPlanApplyDao, times(1)).saveBatch(any());
        Mockito.verify(planInterpretDao, times(1)).batchCreate(any());
        Mockito.verify(planDao, times(1)).createBatch(any());
        Mockito.verify(planCenterDao, times(1)).saveBatch(any());
        Mockito.verify(planCategoryDao, times(1)).batchCreate(any());
        Mockito.verify(planItemDao, times(1)).batchCreate(any());
    }

    /**
     * Unit Planner 应用周计划公共 Mock 方法
     */
    private void unitPlannerApplyCommonMock(boolean noAgencyTemplate) {
        // 学校
        CenterEntity center = new CenterEntity();
        center.setId("centerId001");
        // 班级
        GroupEntry group = new GroupEntry();
        group.setId("groupId001");

        // Unit 数据
        List<QueryPlanModel> planModels = new ArrayList<>();
        QueryPlanModel planModel = new QueryPlanModel();
        planModel.setPlanId("planId001");
        planModel.setUnitId("unitId001");
        planModel.setWeek(1);
        planModels.add(planModel);

        // 用户信息
        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setUsername("userId001");
        currentUser.setAgencyId("agencyId001");

        // 机构周计划模板
        PlanEntity agencyCurrentWeekPlanTemplate = new PlanEntity();
        agencyCurrentWeekPlanTemplate.setId("agencyCurrentWeekPlanTemplateId001");
        agencyCurrentWeekPlanTemplate.setAgencyId("agencyId001");
        agencyCurrentWeekPlanTemplate.setCustomThemeRowName("customThemeRowName");

        // 机构信息
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("agencyId001");
        agencyModel.setName("agencyName001");

        // 要复制的周计划信息
        PlanEntity planEntity = new PlanEntity();
        planEntity.setId("planId001");
        // 要继承的周计划
        PlanEntity latestPlan = new PlanEntity();
        latestPlan.setId("latestPlanId001");
        // 要继承的周计划下的分类
        List<CategoryEntity> categoryEntities = new ArrayList<>();
        CategoryEntity smallGroupCategory = new CategoryEntity();
        smallGroupCategory.setId("category001");
        smallGroupCategory.setType("BOTTOM_DAY_COL");
        smallGroupCategory.setName("Small Group");
        CategoryEntity largeGroupCategory = new CategoryEntity();
        largeGroupCategory.setId("category002");
        largeGroupCategory.setType("BOTTOM_DAY_COL");
        largeGroupCategory.setName("Large Group");
        CategoryEntity centerCategory = new CategoryEntity();
        centerCategory.setId("category002");
        centerCategory.setType("BOTTOM_CENTER_ROW");
        centerCategory.setName("Centers");
        categoryEntities.add(centerCategory);
        categoryEntities.add(smallGroupCategory);
        categoryEntities.add(largeGroupCategory);

        // 要复制周计划的项目列表
        List<ItemEntity> itemEntities = new ArrayList<>();
        // 活动项数据
        for (CategoryEntity categoryEntity : categoryEntities) {
            if (categoryEntity.getName().equalsIgnoreCase("Small Group")) {
                for (int i = 1; i <= 5; i++) {
                    if (i == 1) {
                        for (int j = 0; j < 4; j++) {
                            ItemEntity itemEntity = new ItemEntity();
                            itemEntity.setId("itemId00" + j);
                            itemEntity.setPlanId("planId001");
                            itemEntity.setDayOfWeek(i);
                            itemEntity.setCategoryId(categoryEntity.getId());
                            itemEntities.add(itemEntity);
                        }
                    } else {
                        for (int j = 0; j < 5; j++) {
                            ItemEntity itemEntity = new ItemEntity();
                            itemEntity.setId("itemId00" + j);
                            itemEntity.setPlanId("planId001");
                            itemEntity.setDayOfWeek(i);
                            itemEntity.setCategoryId(categoryEntity.getId());
                            itemEntities.add(itemEntity);
                        }
                    }
                }
            }
            if (categoryEntity.getName().equalsIgnoreCase("Large Group")) {
                for (int i = 1; i <= 5; i++) {
                    for (int j = 0; j < 5; j++) {
                        ItemEntity itemEntity = new ItemEntity();
                        itemEntity.setId("itemId00" + j);
                        itemEntity.setPlanId("planId001");
                        itemEntity.setDayOfWeek(i);
                        itemEntity.setCategoryId(categoryEntity.getId());
                        itemEntities.add(itemEntity);
                    }
                }
            }
        }

        // 要复制周计划中项目关联的测评点列表
        List<ItemMeasureDetailEntity> itemMeasureDetailEntities = new ArrayList<>();
        ItemMeasureDetailEntity measure1 = new ItemMeasureDetailEntity();
        measure1.setMeasureId("measureId001");
        measure1.setItemId("itemId001");
        measure1.setMeasureName("measureName001");
        ItemMeasureDetailEntity measure2 = new ItemMeasureDetailEntity();
        measure1.setMeasureId("measureId002");
        measure1.setItemId("itemId002");
        measure1.setMeasureName("measureName002");
        itemMeasureDetailEntities.add(measure1);
        itemMeasureDetailEntities.add(measure2);

        // 班级框架
        DomainEntity domainEntity = new DomainEntity();
        domainEntity.setId("domainId001");

        // 班级框架中的测评点列表
        List<DomainEntity> domainEntities = new ArrayList<>();
        DomainEntity domain1 = new DomainEntity();
        domain1.setId("domainId001");
        DomainEntity domain2 = new DomainEntity();
        domain1.setId("domainId002");
        domainEntities.add(domain1);
        domainEntities.add(domain2);

        // Centers 数据
        List<PlanCenterEntity> planCenterEntities = new ArrayList<>();
        PlanCenterEntity planCenter = new PlanCenterEntity();
        planCenter.setId("centerId001");
        planCenter.setPlanId("planId001");
        planCenterEntities.add(planCenter);

        // 机构周计划模板分类
        List<CategoryEntity> agencyWeeklyPlanTemplateCategories = new ArrayList<>();
        CategoryEntity agencySmallGroupCategory = new CategoryEntity();
        agencySmallGroupCategory.setId("category001");
        agencySmallGroupCategory.setType("BOTTOM_DAY_COL");
        agencySmallGroupCategory.setName("Small Group");
        CategoryEntity agencyLargeGroupCategory = new CategoryEntity();
        agencyLargeGroupCategory.setId("category002");
        agencyLargeGroupCategory.setType("BOTTOM_DAY_COL");
        agencyLargeGroupCategory.setName("Large Group");
        CategoryEntity agencyCenterCategory = new CategoryEntity();
        agencyCenterCategory.setId("category002");
        agencyCenterCategory.setType("BOTTOM_CENTER_ROW");
        agencyCenterCategory.setName("Centers");
        agencyWeeklyPlanTemplateCategories.add(agencyCenterCategory);
        agencyWeeklyPlanTemplateCategories.add(agencyLargeGroupCategory);
        agencyWeeklyPlanTemplateCategories.add(agencySmallGroupCategory);


        // 接口模拟
        when(centerDao.getCenter("centerId001")).thenReturn(center);
        when(groupDao.getGroup("groupId001")).thenReturn(group);
        when(planDao.listByUnitIds(Collections.singletonList("unitId001"))).thenReturn(planModels);
        when(userProvider.getCurrentUser()).thenReturn(currentUser);
        when(userProvider.getAgencyOpenDefaultOpen("agencyId001", "WEEKLY_PLAN_TEMPLATE_EDIT_ONLY_ADMIN")).thenReturn(true);
        // 判断是否存在机构周计划模板
        if (noAgencyTemplate) {
            when(planDao.getAgencyCurrentWeekPlanTemplate("agencyId001")).thenReturn(null);
            when(planDao.getSystemTemplate()).thenReturn(agencyCurrentWeekPlanTemplate);
        } else {
            when(planDao.getAgencyCurrentWeekPlanTemplate("agencyId001")).thenReturn(agencyCurrentWeekPlanTemplate);
        }
        when(userProvider.getAgencyByUserId("userId001")).thenReturn(agencyModel);
        when(planDao.get("planId001")).thenReturn(planEntity);
        when(planDao.getCurriculumGenieTemplate()).thenReturn(latestPlan);
        when(planCategoryDao.listByPlanId(latestPlan.getId())).thenReturn(categoryEntities);
        when(planCategoryDao.listByPlanId(agencyCurrentWeekPlanTemplate.getId())).thenReturn(agencyWeeklyPlanTemplateCategories);
        when(planCenterDao.queryPlanCentersByPlanId("planId001")).thenReturn(planCenterEntities);
        when(planItemDao.listByPlanId("planId001")).thenReturn(itemEntities);
        when(planItemMeasureDao.listItemMeasureDetails("planId001")).thenReturn(itemMeasureDetailEntities);
        when(groupDao.getDomain("groupId001")).thenReturn(domainEntity);
        when(domainDao.getAllChildDomains("domainId001")).thenReturn(domainEntities);
        when(planInterpretDao.getByPlanId("planId001")).thenReturn(null);
    }

    /**
     * 测试周计划应用
     * case: Unit Planner 可以自动适配周计划模板
     */
    @Test
    public void ableAutoAdaptAgencyWeekPlanTemplateForCurriculum() {
        // 准备数据
        CurriculumApplyRequest request = new CurriculumApplyRequest();
        request.setCenterId("centerId001");
        request.setGroupId("groupId001");
        request.setPlanIds(Arrays.asList(new String[]{"planId001"}));
        request.setCurriculumId("curriculumId001");
        request.setStartDate(new Date());
        request.setStartWeek(1);
        request.setAutoAdapt(true);
        // Curriculum 应用周计划公共 Mock 方法
        curriculumApplyCommonMock(false);

        String unitId = "786604c5-faa3-4e3b-9556-4ff208fe9353";
        CurriculumUnitPlanEntity curriculumUnitPlanEntity = new CurriculumUnitPlanEntity();
        curriculumUnitPlanEntity.setUnitId(unitId);
        // 获取年龄
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setId(unitId);
        unit.setGrade("K (5-6)");
        PlanEntity latestPlan = new PlanEntity();
        latestPlan.setId("template001");
        Mockito.when(curriculumUnitPlanDao.getUnitPlanByPlanId(anyString())).thenReturn(curriculumUnitPlanEntity);
        Mockito.when(curriculumUnitDao.getById(anyString())).thenReturn(unit);
        Mockito.when(planService.getPlanTemplateByUnitId(anyString())).thenReturn(latestPlan);

        // 调用接口
        curriculumService.apply(request);

        // 校验结果
        Mockito.verify(curriculumPlanApplyDao, times(1)).saveBatch(any());
        Mockito.verify(planInterpretDao, times(1)).batchCreate(any());
        Mockito.verify(planDao, times(1)).createBatch(any());
        Mockito.verify(planCenterDao, times(1)).saveBatch(any());
        Mockito.verify(planCategoryDao, times(1)).batchCreate(any());
        Mockito.verify(planItemDao, times(1)).batchCreate(any());
    }

    /**
     * 测试周计划应用
     * case: Unit Planner 可以自动适配周计划模板
     */
    @Test
    public void ableAutoAdaptAgencyWeekPlanTemplateForCurriculumNoAgencyTemplate() {
        // 准备数据
        CurriculumApplyRequest request = new CurriculumApplyRequest();
        request.setCenterId("centerId001");
        request.setGroupId("groupId001");
        request.setPlanIds(Arrays.asList(new String[]{"planId001"}));
        request.setCurriculumId("curriculumId001");
        request.setStartDate(new Date());
        request.setStartWeek(1);
        request.setAutoAdapt(true);
        // Curriculum 应用周计划公共 Mock 方法
        curriculumApplyCommonMock(true);

        String unitId = "786604c5-faa3-4e3b-9556-4ff208fe9353";
        CurriculumUnitPlanEntity curriculumUnitPlanEntity = new CurriculumUnitPlanEntity();
        curriculumUnitPlanEntity.setUnitId(unitId);
        // 获取年龄
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setId(unitId);
        unit.setGrade("K (5-6)");
        PlanEntity latestPlan = new PlanEntity();
        latestPlan.setId("template001");
        Mockito.when(curriculumUnitPlanDao.getUnitPlanByPlanId(anyString())).thenReturn(curriculumUnitPlanEntity);
        Mockito.when(curriculumUnitDao.getById(anyString())).thenReturn(unit);
        Mockito.when(planService.getPlanTemplateByUnitId(anyString())).thenReturn(latestPlan);

        // 调用接口
        curriculumService.apply(request);

        // 校验结果
        Mockito.verify(curriculumPlanApplyDao, times(1)).saveBatch(any());
        Mockito.verify(planInterpretDao, times(1)).batchCreate(any());
        Mockito.verify(planDao, times(1)).createBatch(any());
        Mockito.verify(planCenterDao, times(1)).saveBatch(any());
        Mockito.verify(planCategoryDao, times(1)).batchCreate(any());
        Mockito.verify(planItemDao, times(1)).batchCreate(any());
    }

    /**
     * 测试周计划应用
     * case: Curriculum 取消自动适配周计划模板
     */
    @Test
    public void unAbleAutoAdaptAgencyWeekPlanTemplateForCurriculum() {
        // 准备数据
        CurriculumApplyRequest request = new CurriculumApplyRequest();
        request.setCenterId("centerId001");
        request.setGroupId("groupId001");
        request.setPlanIds(Arrays.asList(new String[]{"planId001"}));
        request.setCurriculumId("curriculumId001");
        request.setStartDate(new Date());
        request.setStartWeek(1);
        request.setAutoAdapt(false);
        // Curriculum 应用周计划公共 Mock 方法
        curriculumApplyCommonMock(false);

        // 调用接口
        curriculumService.apply(request);

        // 校验结果
        Mockito.verify(curriculumPlanApplyDao, times(1)).saveBatch(any());
        Mockito.verify(planInterpretDao, times(1)).batchCreate(any());
        Mockito.verify(planDao, times(1)).createBatch(any());
        Mockito.verify(planCenterDao, times(1)).saveBatch(any());
        Mockito.verify(planCategoryDao, times(1)).batchCreate(any());
        Mockito.verify(planItemDao, times(1)).batchCreate(any());
    }

    /**
     * Curriculum 应用周计划公共 Mock 方法
     */
    private void curriculumApplyCommonMock(boolean noAgencyTemplate) {
        // 学校
        CenterEntity center = new CenterEntity();
        center.setId("centerId001");
        // 班级
        GroupEntry group = new GroupEntry();
        group.setId("groupId001");


        // 用户信息
        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setUsername("userId001");
        currentUser.setAgencyId("agencyId001");

        // 机构周计划模板
        PlanEntity agencyCurrentWeekPlanTemplate = new PlanEntity();
        agencyCurrentWeekPlanTemplate.setId("agencyCurrentWeekPlanTemplateId001");
        agencyCurrentWeekPlanTemplate.setAgencyId("agencyId001");
        agencyCurrentWeekPlanTemplate.setCustomThemeRowName("customThemeRowName");

        // 机构信息
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("agencyId001");
        agencyModel.setName("agencyName001");

        // 要复制的周计划信息
        PlanEntity planEntity = new PlanEntity();
        planEntity.setId("planId001");
        // 要继承的周计划
        PlanEntity latestPlan = new PlanEntity();
        latestPlan.setId("latestPlanId001");
        // 要继承的周计划下的分类
        List<CategoryEntity> categoryEntities = new ArrayList<>();
        CategoryEntity smallGroupCategory = new CategoryEntity();
        smallGroupCategory.setId("category001");
        smallGroupCategory.setType("BOTTOM_DAY_COL");
        smallGroupCategory.setName("Small Group");
        CategoryEntity largeGroupCategory = new CategoryEntity();
        largeGroupCategory.setId("category002");
        largeGroupCategory.setType("BOTTOM_DAY_COL");
        largeGroupCategory.setName("Large Group");
        CategoryEntity centerCategory = new CategoryEntity();
        centerCategory.setId("category002");
        centerCategory.setType("BOTTOM_CENTER_ROW");
        centerCategory.setName("Centers");
        categoryEntities.add(smallGroupCategory);
        categoryEntities.add(largeGroupCategory);
        categoryEntities.add(centerCategory);

        // 要复制周计划的项目列表
        List<ItemEntity> itemEntities = new ArrayList<>();

        for (CategoryEntity categoryEntity : categoryEntities) {
            if (categoryEntity.getName().equalsIgnoreCase("Small Group")) {
                for (int i = 1; i <= 5; i++) {
                    if (i == 1) {
                        for (int j = 0; j < 4; j++) {
                            ItemEntity itemEntity = new ItemEntity();
                            itemEntity.setId("itemId00" + j);
                            itemEntity.setPlanId("planId001");
                            itemEntity.setDayOfWeek(i);
                            itemEntity.setCategoryId(categoryEntity.getId());
                            itemEntities.add(itemEntity);
                        }
                    } else {
                        for (int j = 0; j < 5; j++) {
                            ItemEntity itemEntity = new ItemEntity();
                            itemEntity.setId("itemId00" + j);
                            itemEntity.setPlanId("planId001");
                            itemEntity.setDayOfWeek(i);
                            itemEntity.setCategoryId(categoryEntity.getId());
                            itemEntities.add(itemEntity);
                        }
                    }
                }
            }
            if (categoryEntity.getName().equalsIgnoreCase("Large Group")) {
                for (int i = 1; i <= 5; i++) {
                    for (int j = 0; j < 5; j++) {
                        ItemEntity itemEntity = new ItemEntity();
                        itemEntity.setId("itemId00" + j);
                        itemEntity.setPlanId("planId001");
                        itemEntity.setDayOfWeek(i);
                        itemEntity.setCategoryId(categoryEntity.getId());
                        itemEntities.add(itemEntity);
                    }
                }
            }
        }

        // 获取机构周计划模板下的分类列表
        List<CategoryEntity> agencyWeeklyPlanTemplateCategories = new ArrayList<>();
        CategoryEntity categoryEntity = new CategoryEntity();
        categoryEntity.setId("category001");
        categoryEntity.setType("BOTTOM_DAY_COL");
        categoryEntity.setName("Small Group");
        agencyWeeklyPlanTemplateCategories.add(categoryEntity);

        // 要复制周计划中项目关联的测评点列表
        List<ItemMeasureDetailEntity> itemMeasureDetailEntities = new ArrayList<>();
        ItemMeasureDetailEntity measure1 = new ItemMeasureDetailEntity();
        measure1.setMeasureId("measureId001");
        measure1.setItemId("itemId001");
        measure1.setMeasureName("measureName001");
        ItemMeasureDetailEntity measure2 = new ItemMeasureDetailEntity();
        measure1.setMeasureId("measureId002");
        measure1.setItemId("itemId002");
        measure1.setMeasureName("measureName002");
        itemMeasureDetailEntities.add(measure1);
        itemMeasureDetailEntities.add(measure2);

        // 班级框架
        DomainEntity domainEntity = new DomainEntity();
        domainEntity.setId("domainId001");

        // 班级框架中的测评点列表
        List<DomainEntity> domainEntities = new ArrayList<>();
        DomainEntity domain1 = new DomainEntity();
        domain1.setId("domainId001");
        DomainEntity domain2 = new DomainEntity();
        domain1.setId("domainId002");
        domainEntities.add(domain1);
        domainEntities.add(domain2);

        // Centers 数据
        List<PlanCenterEntity> planCenterEntities = new ArrayList<>();
        PlanCenterEntity planCenter = new PlanCenterEntity();
        planCenter.setId("centerId001");
        planCenter.setPlanId("planId001");
        planCenterEntities.add(planCenter);

        // Curriculum 信息
        CurriculumEntity curriculum = new CurriculumEntity();
        curriculum.setId("curriculumId001");

        // 系列课程所有的周计划
        List<CurriculumUnitPlanEntity> allPlans = new ArrayList<>();
        CurriculumUnitPlanEntity curriculumUnitPlanEntity = new CurriculumUnitPlanEntity();
        curriculumUnitPlanEntity.setId("curriculumUnitPlanId001");
        curriculumUnitPlanEntity.setUnitId("unitId001");
        curriculumUnitPlanEntity.setPlanId("planId001");
        allPlans.add(curriculumUnitPlanEntity);

        // 系列课程的资源
        List<CurriculumBookEntity> curriculumBookEntities = new ArrayList<>();
        CurriculumBookEntity curriculumBookEntity1 = new CurriculumBookEntity();
        curriculumBookEntity1.setId("curriculumBookId001");
        curriculumBookEntity1.setBook("book");
        curriculumBookEntity1.setCurriculumId("curriculumId001");
        curriculumBookEntity1.setPlanId("planId001");
        curriculumBookEntity1.setPlanItemId("itemId001");
        CurriculumBookEntity curriculumBookEntity2 = new CurriculumBookEntity();
        curriculumBookEntity2.setId("curriculumBookId001");
        curriculumBookEntity2.setBook("book");
        curriculumBookEntity2.setCurriculumId("curriculumId001");
        curriculumBookEntity2.setPlanId("planId001");
        curriculumBookEntity2.setPlanItemId("itemId002");
        curriculumBookEntities.add(curriculumBookEntity1);
        curriculumBookEntities.add(curriculumBookEntity2);

        // 课程词汇数据
        List<CurriculumVocabularyEntity> curriculumVocabularyEntities = new ArrayList<>();
        CurriculumVocabularyEntity curriculumVocabularyEntity1 = new CurriculumVocabularyEntity();
        curriculumVocabularyEntity1.setId("curriculumVocabularyId001");
        curriculumVocabularyEntity1.setCurriculumId("curriculumId001");
        curriculumVocabularyEntity1.setPlanId("planId001");
        curriculumVocabularyEntity1.setPlanItemId("itemId001");
        CurriculumVocabularyEntity curriculumVocabularyEntity2 = new CurriculumVocabularyEntity();
        curriculumVocabularyEntity2.setId("curriculumVocabularyId002");
        curriculumVocabularyEntity2.setCurriculumId("curriculumId001");
        curriculumVocabularyEntity2.setPlanId("planId001");
        curriculumVocabularyEntity2.setPlanItemId("itemId002");
        curriculumVocabularyEntities.add(curriculumVocabularyEntity1);
        curriculumVocabularyEntities.add(curriculumVocabularyEntity2);

        // 课程附件数据
        List<CurriculumAttachmentEntity> curriculumAttachmentEntities = new ArrayList<>();
        CurriculumAttachmentEntity curriculumAttachmentEntity1 = new CurriculumAttachmentEntity();
        curriculumAttachmentEntity1.setId("curriculumAttachmentId001");
        curriculumAttachmentEntity1.setCurriculumId("curriculumId001");
        curriculumAttachmentEntity1.setPlanId("planId001");
        curriculumAttachmentEntity1.setPlanItemId("itemId001");
        CurriculumAttachmentEntity curriculumAttachmentEntity2 = new CurriculumAttachmentEntity();
        curriculumAttachmentEntity2.setId("curriculumAttachmentId002");
        curriculumAttachmentEntity2.setCurriculumId("curriculumId001");
        curriculumAttachmentEntity2.setPlanId("planId001");
        curriculumAttachmentEntity2.setPlanItemId("itemId002");
        curriculumAttachmentEntities.add(curriculumAttachmentEntity1);
        curriculumAttachmentEntities.add(curriculumAttachmentEntity2);



        // 接口模拟
        when(centerDao.getCenter("centerId001")).thenReturn(center);
        when(groupDao.getGroup("groupId001")).thenReturn(group);
        when(curriculumDao.getCurriculumById("curriculumId001")).thenReturn(curriculum);
        when(curriculumUnitPlanDao.getPlansByCurriculumId("curriculumId001")).thenReturn(allPlans);
        when(curriculumBookDao.listCustomByCurriculumId("curriculumId001")).thenReturn(curriculumBookEntities);
        when(curriculumAttachmentDao.listCustomByCurriculumId("curriculumId001")).thenReturn(curriculumAttachmentEntities);
        when(curriculumVocabularyDao.listCustomByCurriculumId("curriculumId001")).thenReturn(curriculumVocabularyEntities);
        when(userProvider.getCurrentUser()).thenReturn(currentUser);
        when(userProvider.getAgencyOpenDefaultOpen("agencyId001", "WEEKLY_PLAN_TEMPLATE_EDIT_ONLY_ADMIN")).thenReturn(true);
        // 判断是否存在机构周计划模板
        if (noAgencyTemplate) {
            when(planDao.getAgencyCurrentWeekPlanTemplate("agencyId001")).thenReturn(null);
            when(planDao.getSystemTemplate()).thenReturn(agencyCurrentWeekPlanTemplate);
        } else {
            when(planDao.getAgencyCurrentWeekPlanTemplate("agencyId001")).thenReturn(agencyCurrentWeekPlanTemplate);
        }
        when(userProvider.getAgencyByUserId("userId001")).thenReturn(agencyModel);
        when(planDao.get("planId001")).thenReturn(planEntity);
        when(planDao.getCurriculumGenieTemplate()).thenReturn(latestPlan);
        when(planCategoryDao.listByPlanId("planId001")).thenReturn(categoryEntities);
        when(planCategoryDao.listByPlanId("agencyCurrentWeekPlanTemplateId001")).thenReturn(agencyWeeklyPlanTemplateCategories);
        when(planCenterDao.queryPlanCentersByPlanId("planId001")).thenReturn(planCenterEntities);
        when(planItemDao.listByPlanId("planId001")).thenReturn(itemEntities);
        when(planItemMeasureDao.listItemMeasureDetails("planId001")).thenReturn(itemMeasureDetailEntities);
        when(groupDao.getDomain("groupId001")).thenReturn(domainEntity);
        when(domainDao.getAllChildDomains("domainId001")).thenReturn(domainEntities);
        when(planInterpretDao.getByPlanId("planId001")).thenReturn(null);
    }

    /**
     * 测试获取单元列表
     * case: 没有生成进度为 100% 的单元
     */
    @Test
    public void testGetUnitsNoData() {
        // 定义请求体
        Integer pageNum = 1;
        Integer pageSize = 10;
        GetUnitsRequest request = new GetUnitsRequest();
        request.setPageNum(pageNum);
        request.setPageSize(pageSize);
        // 定义当前用户
        AuthUserDetails user = new AuthUserDetails();
        user.setUsername("userId001");
        // 定义当前用户所在机构
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("agencyId001");
        // 定义单元列表数据
        PageList<CurriculumUnitEntity> pageList = PageList.from(new ArrayList<>(), Long.parseLong(String.valueOf(pageNum)), Long.parseLong(String.valueOf(pageSize)));

        // 数据模拟
        when(userProvider.getCurrentUser()).thenReturn(user);
        when(userProvider.getAgencyByUserId(user.getId())).thenReturn(agencyModel);
        when(curriculumUnitDao.listAgencyUnits(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())).thenReturn(pageList);

        // 接口调用
        UnitPlansResponse units = curriculumService.getUnits(request);

        // 验证结果
        verify(curriculumUnitDao, times(1)).listAgencyUnits(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any()); // 验证调用了一次

    }

    /**
     * 测试获取单元列表
     * case: 存在生成进度为 100% 的单元
     */
    @Test
    public void testGetUnits() {
        // 定义请求体
        Integer pageNum = 1;
        Integer pageSize = 10;
        GetUnitsRequest request = new GetUnitsRequest();
        request.setPageNum(pageNum);
        request.setPageSize(pageSize);
        // 定义当前用户
        AuthUserDetails user = new AuthUserDetails();
        user.setUsername("userId001");
        // 定义当前用户所在机构
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("agencyId001");
        // 定义 CurriculumUnitEntity 列表数据
        List<CurriculumUnitEntity> unitEntities = new ArrayList<>();
        CurriculumUnitEntity curriculumUnitEntity = new CurriculumUnitEntity();
        curriculumUnitEntity.setId("unitId001");
        curriculumUnitEntity.setTitle("unit title");
        curriculumUnitEntity.setCreateSource("CREATE_GENIE");
        unitEntities.add(curriculumUnitEntity);
        // 定义单元下周计划列表数据
        List<QueryPlanModel> queryPlanModels = new ArrayList<>();
        QueryPlanModel queryPlanModel = new QueryPlanModel();
        queryPlanModel.setUnitId("unitId001");
        queryPlanModel.setPlanId("planId001");
        queryPlanModel.setWeek(1);
        queryPlanModels.add(queryPlanModel);
        // 定义单元列表数据
        PageList<CurriculumUnitEntity> pageList = PageList.from(unitEntities, Long.parseLong(String.valueOf(pageNum)), Long.parseLong(String.valueOf(pageSize)));

        // 数据模拟
        when(userProvider.getCurrentUser()).thenReturn(user);
        when(userProvider.getAgencyByUserId(user.getId())).thenReturn(agencyModel);
        when(curriculumUnitDao.listAgencyUnits(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())).thenReturn(pageList);
        when(planDao.listByUnitIds(any())).thenReturn(queryPlanModels);

        // 接口调用
        UnitPlansResponse units = curriculumService.getUnits(request);

        // 验证结果
        verify(curriculumUnitDao, times(1)).listAgencyUnits(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any()); // 验证调用了一次
        verify(planDao, times(1)).listByUnitIds(any()); // 验证调用了一次
        assertEquals(units.getTotal().intValue(), 1); // 验证返回的数据总数为 1
    }

    /**
     * 测试获取单元列表
     * case: 有指定单元
     */
    @Test
    public void testGetUnits2() {
        // 定义请求体
        Integer pageNum = 1;
        Integer pageSize = 10;
        GetUnitsRequest request = new GetUnitsRequest();
        request.setPageNum(pageNum);
        request.setPageSize(pageSize);
        request.setUnitId("unitId001");
        // 定义当前用户
        AuthUserDetails user = new AuthUserDetails();
        user.setUsername("userId001");
        // 定义当前用户所在机构
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("agencyId001");
        // 定义 CurriculumUnitEntity 列表数据
        CurriculumUnitEntity curriculumUnitEntity = new CurriculumUnitEntity();
        curriculumUnitEntity.setId("unitId001");
        curriculumUnitEntity.setTitle("unit title");
        curriculumUnitEntity.setCreateSource("CREATE_GENIE");
        // 定义单元下周计划列表数据
        List<QueryPlanModel> queryPlanModels = new ArrayList<>();
        QueryPlanModel queryPlanModel = new QueryPlanModel();
        queryPlanModel.setUnitId("unitId001");
        queryPlanModel.setPlanId("planId001");
        queryPlanModel.setWeek(1);
        queryPlanModels.add(queryPlanModel);
        // 定义单元列表数据

        // 数据模拟
        when(userProvider.getCurrentUser()).thenReturn(user);
        when(userProvider.getAgencyByUserId(user.getId())).thenReturn(agencyModel);
        when(curriculumUnitDao.getUnitInfoById("unitId001", false)).thenReturn(curriculumUnitEntity);
        when(planDao.listByUnitIds(any())).thenReturn(queryPlanModels);

        // 接口调用
        UnitPlansResponse units = curriculumService.getUnits(request);

        // 验证结果
        verify(curriculumUnitDao, times(1)).getUnitInfoById("unitId001", false); // 验证调用了一次
        verify(planDao, times(1)).listByUnitIds(any()); // 验证调用了一次
        assertEquals(1, units.getItems().size()); // 验证返回的数据总数为 1
    }

    /**
     * 测试获取单元详情方法
     */
    @Test
    public void testGetUnitDetail() {
        // 用户 ID
        String userId = "userId";
        // 单元 ID
        String unitId = "unitId";
        // 单元实体
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setId(unitId);
        unit.setCreateUserId(userId);
        unit.setCreateSource("CREATE_GENIE");
        unit.setAdapted(true);
        unit.setAdaptCenterId("centerId001");
        unit.setAdaptGroupId("groupId001");
        unit.setAdaptGroupName("groupName0001");
        // 用户实体
        com.learninggenie.common.data.entity.UserEntity userEntity = new com.learninggenie.common.data.entity.UserEntity();
        // 用户信息实体
        UserProfileEntity userProfileEntity = new UserProfileEntity();
        userEntity.setProfile(userProfileEntity);
        // 单元周计划列表
        List<CurriculumUnitPlanEntity> unitPlanEntities = new ArrayList<>();
        // 单元周计划
        CurriculumUnitPlanEntity unitPlanEntity = new CurriculumUnitPlanEntity();
        unitPlanEntity.setPlanId("planId");
        unitPlanEntities.add(unitPlanEntity);
        PlanEntity lastPlan = new PlanEntity();
        lastPlan.setId("planId001");
        PlanEntity planEntity = new PlanEntity();
        planEntity.setId("planId");
        planEntity.setTheme("theme");
        planEntity.setAdapted(true);
        List<CategoryEntity> categoryEntities = new ArrayList<>();
        CategoryEntity categoryEntity = new CategoryEntity();
        categoryEntity.setId("category001");
        categoryEntity.setName("categoryName");
        categoryEntity.setType("BOTTOM_DAY_COL");
        categoryEntities.add(categoryEntity);
        PlanEntity latestPlan = new PlanEntity();
        latestPlan.setId("template001");
        List<CurriculumUnitEditRecordModel> curriculumUnitEditRecordModels = new ArrayList<>();
        CurriculumUnitEditRecordModel curriculumUnitEditRecordModel= new CurriculumUnitEditRecordModel();
        curriculumUnitEditRecordModel.setUserId("001");
        curriculumUnitEditRecordModel.setUserName("menderName");
        curriculumUnitEditRecordModel.setAvatarUrl("avatarUrl");
        // 自定义模块信息
        List<CurriculumCustomModuleRecordEntity> customModuleRecordEntities = new ArrayList<>();


        // 模拟方法调用
        when(userProvider.getUser(userId)).thenReturn(userEntity);
        // when(curriculumUnitDao.getById(unitId)).thenReturn(unit);
        when(customModuleRecordDao.getListByMasterDataId(anyString())).thenReturn(customModuleRecordEntities);
        when(curriculumUnitDao.getUnitInfoById(unitId, true)).thenReturn(unit);
        when(planDao.getCurriculumGenieTemplate()).thenReturn(lastPlan);
        when(planCategoryDao.listByPlanId(lastPlan.getId())).thenReturn(categoryEntities);
        when(curriculumUnitPlanDao.getPlansWithCornerByUnitId(unitId)).thenReturn(unitPlanEntities);
        when(planItemDao.listByPlanIds(Collections.singletonList("planId"))).thenReturn(new ArrayList<>());
        when(planCategoryDao.listByPlanIds(Collections.singletonList("planId"))).thenReturn(new ArrayList<>());
        when(planService.getPlanTemplateByUnitId(anyString())).thenReturn(latestPlan);
        when(lessonsCurriculumUnitEditRecordDao.getUnitEditRecordModelList(unitId)).thenReturn(curriculumUnitEditRecordModels);
        when(fileSystem.getUserAvatarUrl(anyString())).thenReturn("filepath");
        when(planDao.listPlansByIds(Collections.singletonList("planId"))).thenReturn(Collections.singletonList(planEntity));

        // 调用测试方法
        CurriculumUnitResponse unitModel = curriculumService.getUnitDetail(unitId);

        // 验证单元响应不为空,响应的单元 ID 与请求的一致
        Assert.assertNotNull(unitModel);
        assertEquals(unitId, unitModel.getId());
        assertEquals(1, unitModel.getPlans().size());
        assertEquals("theme", unitModel.getPlans().get(0).getTheme());
    }

    /**
     * 测试获取单元编辑状态
     * case: 没有其他人正在编辑情况
     */
    @Test
    public void testCheckUnitEditingStatusWithNobody() {
        // 模拟数据
        String validUnitId = "validUnitId";
        String userId = "userId";
        String key = RedisKeyPrefix.UNIT_EDITING + validUnitId.toUpperCase();

        // 模拟接口调用
        Mockito.when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(cacheService.get(key)).thenReturn(null);

        // 调用测试方法
        LockedUnitModel actualLockedUnitModel = curriculumService.getUnitEditStatus(validUnitId);

        // 验证结果为空
        assertNull(actualLockedUnitModel);
    }

    /**
     * 测试获取单元编辑状态
     * case: 有其他人正在编辑情况
     */
    @Test
    public void testCheckUnitEditingStatusWithSomeBody() {
        // 模拟数据
        String validUnitId = "validUnitId";
        String userId = "userId";
        LockedUnitModel expectedLockedUnitModel = new LockedUnitModel();
        expectedLockedUnitModel.setUserId("otherUserId");
        CacheModel cacheModel = new CacheModel();
        cacheModel.setValue(JsonUtil.toJson(expectedLockedUnitModel));
        String key = RedisKeyPrefix.UNIT_EDITING + validUnitId.toUpperCase();

        // 模拟接口调用
        Mockito.when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(cacheService.get(key)).thenReturn(cacheModel);

        // 调用测试方法
        LockedUnitModel actualLockedUnitModel = curriculumService.getUnitEditStatus(validUnitId);

        // 验证结果不空，且正在编辑的用户 ID 与当前用户的 ID 不一致
        Assert.assertNotNull(actualLockedUnitModel);
        Assert.assertNotEquals(expectedLockedUnitModel.getUserId(), userId);
    }

    /**
     * 测试获取单元编辑状态
     * case: 单元 ID 为空情况
     */
    @Test(expected = BusinessException.class)
    public void testCheckUnitEditingStatusWithoutUnitId() {
        // 模拟单元 ID 为空
        String invalidUnitId = "";

        // 调用后抛出异常
        curriculumService.getUnitEditStatus(invalidUnitId);
    }

    /**
     * 测试生成并保存封面接口
     */
    @Test
    public void testGenerateAndSaveUnitCover() {
        // 模拟生成单元封面请求体
        GenerateUnitCoverRequest request = new GenerateUnitCoverRequest();
        UnitModel unitModel = new UnitModel();
        unitModel.setId("unitId");
        unitModel.setTitle("unitName");
        unitModel.setConcepts("concepts");
        unitModel.setOverview("overview");
        request.setUnit(unitModel);

        PromptEntity generateUnitCoverPrompt = new PromptEntity();
        generateUnitCoverPrompt.setPromptTemplate("generateUnitCoverPrompt");

        // 模拟生成封面结果
        List<Image> images = new ArrayList<>();
        Image image = new Image();
        image.setUrl("url");
        images.add(image);
        // 模拟生成 prompt 使用记录
        PromptUsageRecordEntity promptUsageRecord = new PromptUsageRecordEntity();
        promptUsageRecord.setId("promptUsageRecordId");
        // 自定义模块信息
        List<CurriculumCustomModuleRecordEntity> customModuleRecordEntities = new ArrayList<>();

        // 模拟接口调用
        when(customModuleRecordDao.getListByMasterDataId(anyString())).thenReturn(customModuleRecordEntities);
        when(promptProvider.getActivePromptByScene(PromptScene.GENERATE_UNIT_COVER.toString())).thenReturn(generateUnitCoverPrompt);
        when(openAIService.createImages(any())).thenReturn(images);
        when(promptProvider.createPromptUsageRecord(anyString(), anyList(), eq(null), any(), eq(CreateSource.MANUAL), eq(UseType.NORMAL), eq(null))).thenReturn(promptUsageRecord);
        doNothing().when(fileSystem).upload(anyString(), any());
        doNothing().when(odlMediaDao).insertMediaAll(any());
        when(fileSystem.getPublicUrl(anyString())).thenReturn("filepath");
        try {
            fileUtilMockedStatic.when(() -> FileUtil.compressImage(any(), anyFloat(), anyString())).thenReturn(File.createTempFile("filepath", ".jpg"));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        // 调用生成封面并保存方法
        curriculumService.generateAndSaveUnitCover(request);

        // 验证封面更新方法调用次数为 1
        verify(curriculumUnitDao, times(1)).updateUnitCover(eq("unitId"), anyString());
    }


    /**
     * 测试激活 Unit Planner 功能
     */
    @Test
    public void testActivationUnitPlanner() {
        // 当前用户
        AuthUserDetails currentUser = new AuthUserDetails();
        String agencyId = UUID.randomUUID().toString();
        currentUser.setAgencyId(agencyId); // 设置机构 Id
        currentUser.setRole("AGENCY_OWNER"); // 设置角色
        AgencyMetaDataEntity agencyMetaData = new AgencyMetaDataEntity();
        agencyMetaData.setMetaValue("false");
        AgencyMetaDataEntity agencyDaoMeta = new AgencyMetaDataEntity();
        agencyDaoMeta.setMetaValue("false");
        AgencyMetaDataEntity meta = new AgencyMetaDataEntity();
        meta.setMetaValue("false");

        ReflectionTestUtils.setField(curriculumService, "emailTemplateVersion", "v1");
        // 模拟当前用户
        when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // 模拟该机构是否已经发送过使用 unitplanner 邮件
        when(agencyDao.getMeta(agencyId, AgencyMetaKey.SEND_USE_UNIT_PLANNER_EMAIL.toString())).thenReturn(agencyMetaData);
        // 模拟机构激活 Unit Planner 的方式
        when(agencyDao.getMeta(agencyId, AgencyMetaKey.ACTIVATED_UNIT_PLANNER_TYPE.toString())).thenReturn(agencyDaoMeta);
        // 模拟机构是否已经激活 Unit Planner 功能
        when(agencyDao.getMeta(agencyId, AgencyMetaKey.IS_IT_ACTIVATED_UNIT_PLANNER.toString())).thenReturn(meta);

        // 调用方法
        ActivationUnitPlannerResponse response = curriculumService.activationUnitPlanner();

        // 验证结果
        Assert.assertNotNull(response);
        assertTrue(response.getIsRedirect());
    }

    /**
     * 测试拒绝激活 Unit Planner
     */
    @Test
    public void testRejectActivationUnitPlanner() {
        // 当前用户
        AuthUserDetails currentUser = new AuthUserDetails();
        String agencyId = UUID.randomUUID().toString();
        currentUser.setAgencyId(agencyId); // 设置机构 Id
        currentUser.setRole("AGENCY_OWNER"); // 设置角色
        AgencyMetaDataEntity meta = new AgencyMetaDataEntity();
        meta.setMetaValue("false");

        ReflectionTestUtils.setField(curriculumService, "emailTemplateVersion", "v1");
        // 模拟当前用户
        when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // 模拟机构是否已经激活 Unit Planner 功能
        when(agencyDao.getMeta(agencyId, AgencyMetaKey.IS_IT_ACTIVATED_UNIT_PLANNER.toString())).thenReturn(meta);

        // 调用测试方法
        ActivationUnitPlannerResponse response = curriculumService.rejectActivationUnitPlanner();

        // 验证结果
        Assert.assertNotNull(response);

    }

    /**
     * 测试当 frameworkId 为空时，方法直接返回
     */
    @Test
    public void testUnitDetailStandardNames_FrameworkIdBlank() {
        // 定义参数
        CurriculumUnitEntity curriculumUnitEntity = new CurriculumUnitEntity();
        CurriculumUnitResponse curriculumUnitResponse = new CurriculumUnitResponse();
        // 设置 frameworkId 为空
        curriculumUnitEntity.setFrameworkId("");
        // 调用方法
        curriculumService.unitDetailStandardNames(curriculumUnitEntity, curriculumUnitResponse);
        // 验证结果
        assertNull(curriculumUnitResponse.getStandardNames());
    }

    /**
     * 测试当 listDomainId 为空且存在 measureEntities 时
     */
    @Test
    public void testUnitDetailStandardNames_ListDomainIdEmpty() {
        // 定义参数
        CurriculumUnitEntity curriculumUnitEntity = new CurriculumUnitEntity();
        CurriculumUnitResponse curriculumUnitResponse = new CurriculumUnitResponse();
        // 设置 frameworkId 和 listDomainId
        curriculumUnitEntity.setFrameworkId("framework1");
        curriculumUnitEntity.setDomainIds("[\"BE9F5037-A3EF-418A-8000-284F1C575183\",\"15F3DAD0-82F1-4398-9C62-18CDC01A8199\",\"29A731AF-1F65-43C9-9A75-69CAF412A0F5\",\"3F6180DD-79D3-40A1-A97F-B0A84DCA3265\",\"4948FAAD-B1C1-4EA7-B177-5F10F8C819C7\",\"516F9950-28BB-4EFB-A52B-38D7D54AF42E\",\"BA1FA58F-CE9B-4056-B96F-39ABD28AD484\",\"32EC8E1C-CB10-4FD7-8173-37D55432AF71\",\"A2C8F78A-B33A-49D8-8E33-265D391144E0\"]");

        // 创建 MeasureEntity 列表
        MeasureEntity measure1 = new MeasureEntity();
        measure1.setId("BE9F5037-A3EF-418A-8000-284F1C575183");
        measure1.setName("Measure A");
        measure1.setSortIndex(1);

        MeasureEntity measure2 = new MeasureEntity();
        measure2.setId("15F3DAD0-82F1-4398-9C62-18CDC01A8199");
        measure2.setName("Measure B");
        measure2.setSortIndex(2);

        // Mock portfolioService 返回值
        when(portfolioService.getDomainInfosByFrameworkId("framework1"))
                .thenReturn(Arrays.asList(measure1, measure2));

        // 调用方法
        curriculumService.unitDetailStandardNames(curriculumUnitEntity, curriculumUnitResponse);

        // 预期结果
        List<String> expected = Arrays.asList("Measure A", "Measure B");
        // 验证结果
        assertEquals(expected, curriculumUnitResponse.getStandardNames());
    }

    /**
     * 测试当 listDomainId 不为空且存在 measureEntities 时
     */
    @Test
    public void testUnitDetailStandardNames_ListDomainIdNotEmpty() {
        // 定义参数
        CurriculumUnitEntity curriculumUnitEntity = new CurriculumUnitEntity();
        CurriculumUnitResponse curriculumUnitResponse = new CurriculumUnitResponse();
        // 设置 frameworkId 和 listDomainId
        curriculumUnitEntity.setFrameworkId("framework1");
        curriculumUnitEntity.setDomainIds("[\"BE9F5037-A3EF-418A-8000-284F1C575183\",\"15F3DAD0-82F1-4398-9C62-18CDC01A8199\",\"29A731AF-1F65-43C9-9A75-69CAF412A0F5\",\"3F6180DD-79D3-40A1-A97F-B0A84DCA3265\",\"4948FAAD-B1C1-4EA7-B177-5F10F8C819C7\",\"516F9950-28BB-4EFB-A52B-38D7D54AF42E\",\"BA1FA58F-CE9B-4056-B96F-39ABD28AD484\",\"32EC8E1C-CB10-4FD7-8173-37D55432AF71\",\"A2C8F78A-B33A-49D8-8E33-265D391144E0\"]");

        // 创建 MeasureEntity 列表
        // 创建 MeasureEntity 列表
        MeasureEntity measure1 = new MeasureEntity();
        measure1.setId("BE9F5037-A3EF-418A-8000-284F1C575183");
        measure1.setName("Measure A");
        measure1.setSortIndex(1);

        MeasureEntity measure2 = new MeasureEntity();
        measure2.setId("15F3DAD0-82F1-4398-9C62-18CDC01A8199");
        measure2.setName("Measure B");
        measure2.setSortIndex(2);

        MeasureEntity measure3 = new MeasureEntity();
        measure2.setId("3");
        measure2.setName("Measure C");
        measure2.setSortIndex(3);

        // Mock portfolioService 返回值
        when(portfolioService.getDomainInfosByFrameworkId("framework1"))
                .thenReturn(Arrays.asList(measure1, measure2, measure3));

        // 调用方法
        curriculumService.unitDetailStandardNames(curriculumUnitEntity, curriculumUnitResponse);

        // 预期结果
        List<String> expected = Arrays.asList("Measure A");
        // 验证结果
        assertEquals(expected, curriculumUnitResponse.getStandardNames());
    }

    /**
     * 测试当 listDomainId 和 measureEntities 都为空时
     */
    @Test
    public void testUnitDetailStandardNames_EmptyListDomainIdAndMeasureEntities() {
        // 定义参数
        CurriculumUnitEntity curriculumUnitEntity = new CurriculumUnitEntity();
        CurriculumUnitResponse curriculumUnitResponse = new CurriculumUnitResponse();
        // 设置 frameworkId 和 listDomainId
        curriculumUnitEntity.setFrameworkId("framework1");
        curriculumUnitEntity.setDomainIds("[]");

        // Mock portfolioService 返回空列表
        when(portfolioService.getDomainInfosByFrameworkId("framework1"))
                .thenReturn(Collections.emptyList());

        // 调用方法
        curriculumService.unitDetailStandardNames(curriculumUnitEntity, curriculumUnitResponse);

        // 验证结果为空列表
        assertTrue(curriculumUnitResponse.getStandardNames().isEmpty());
    }

    /**
     * 测试当 listDomainId 不为空且 measureEntities 为空时
     */
    @Test
    public void testUnitDetailStandardNames_NonEmptyListDomainIdAndEmptyMeasureEntities() {
        // 定义参数
        CurriculumUnitEntity curriculumUnitEntity = new CurriculumUnitEntity();
        CurriculumUnitResponse curriculumUnitResponse = new CurriculumUnitResponse();
        // 设置 frameworkId 和 listDomainId
        curriculumUnitEntity.setFrameworkId("framework1");
        curriculumUnitEntity.setDomainIds("[\"BE9F5037-A3EF-418A-8000-284F1C575183\",\"15F3DAD0-82F1-4398-9C62-18CDC01A8199\",\"29A731AF-1F65-43C9-9A75-69CAF412A0F5\",\"3F6180DD-79D3-40A1-A97F-B0A84DCA3265\",\"4948FAAD-B1C1-4EA7-B177-5F10F8C819C7\",\"516F9950-28BB-4EFB-A52B-38D7D54AF42E\",\"BA1FA58F-CE9B-4056-B96F-39ABD28AD484\",\"32EC8E1C-CB10-4FD7-8173-37D55432AF71\",\"A2C8F78A-B33A-49D8-8E33-265D391144E0\"]");

        // Mock portfolioService 返回空列表
        when(portfolioService.getDomainInfosByFrameworkId("framework1"))
                .thenReturn(Collections.emptyList());

        // 调用方法
        curriculumService.unitDetailStandardNames(curriculumUnitEntity, curriculumUnitResponse);

        // 验证结果为空列表
        assertTrue(curriculumUnitResponse.getStandardNames().isEmpty());
    }

    /**
     * 测试复制改编单元
     */
    @Test
    public void testCopyAdaptUnit() {
        // 准备数据
        String userId = "userId";
        String agencyId = "agencyId";
        String unitId = "unitId";
        String adaptGroupId = "groupId";
        String adaptCenterId = "centerId";
        String planId = "planId";
        CopyAdaptUnitRequest request = new CopyAdaptUnitRequest();
        request.setUnitId(unitId);
        request.setAdaptGroupId(adaptGroupId);
        request.setAdaptCenterId(adaptCenterId);
        // 单元
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setUsername(userId);
        currentUser.setAgencyId(agencyId);
        // 单元周计划
        List<CurriculumUnitPlanEntity> unitPlans = new ArrayList<>();
        CurriculumUnitPlanEntity unitPlan = new CurriculumUnitPlanEntity();
        unitPlan.setPlanId(planId);
        unitPlan.setUnitId(unitId);
        unitPlan.setNumber(1);
        unitPlans.add(unitPlan);
        // 周计划
        PlanEntity planEntity = new PlanEntity();
        planEntity.setId(planId);
        // 周计划活动项
        ItemEntity itemEntity = new ItemEntity();
        itemEntity.setId("itemId");
        itemEntity.setPlanId(planId);
        // 周计划活动项测评点
        ItemMeasureEntity itemMeasureEntity = new ItemMeasureEntity();
        itemMeasureEntity.setItemId("itemId");
        itemMeasureEntity.setMeasureId("measureId");
        // 周计划 Center 分组
        PlanCenterEntity planCenterEntity = new PlanCenterEntity();
        planCenterEntity.setId("centerId");
        planCenterEntity.setPlanId(planId);

        // 模拟接口调用
        when(curriculumUnitDao.getById(unitId)).thenReturn(unit);
        when(userProvider.getCurrentUser()).thenReturn(currentUser);
        when(planDao.listPlansByIds(anyList())).thenReturn(Collections.singletonList(planEntity));
        when(planItemDao.listByPlanIds(anyList())).thenReturn(Collections.singletonList(itemEntity));
        when(planItemMeasureDao.listByItemIdIn(anyList())).thenReturn(Collections.singletonList(itemMeasureEntity));
        when(curriculumUnitPlanDao.getPlansWithCornerByUnitId(unitId)).thenReturn(unitPlans);
        when(planCenterDao.listPlanCentersByPlanIds(anyList())).thenReturn(Collections.singletonList(planCenterEntity));

        // 调用测试方法
        CopyAdaptUnitResponse response = curriculumService.copyAdaptUnit(request);

        // 验证接口调用次数
        verify(curriculumUnitDao, times(1)).save(any());
        verify(curriculumUnitPlanDao, times(1)).saveBatch(any());
        verify(planDao, times(1)).createBatch(any());
        verify(planItemDao, times(1)).batchCreate(any());
        verify(planCenterDao, times(1)).saveBatch(any());
        Assert.assertNotNull(response); // 验证返回结果不为空
        Assert.assertEquals(1, response.getPlans().size()); // 验证返回结果中的 unitId 与请求的一致
    }

    /**
     * 测试获取单元周计划和活动项
     */
    @Test
    public void testGetUnitPlanAndActivities() {
        // 准备数据
        String unitId = "unitId";
        String planId = "planId";
        // 单元
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setId(unitId);
        unit.setTitle("unitName");
        // 单元周计划
        List<CurriculumUnitPlanEntity> unitPlans = new ArrayList<>();
        CurriculumUnitPlanEntity unitPlan = new CurriculumUnitPlanEntity();
        unitPlan.setNumber(1);
        unitPlan.setPlanId(planId);
        unitPlan.setUnitId(unitId);
        unitPlans.add(unitPlan);
        // 周计划
        PlanEntity planEntity = new PlanEntity();
        planEntity.setId(planId);
        planEntity.setTheme("theme");
        // 周计划活动项
        ItemEntity itemEntity = new ItemEntity();
        itemEntity.setId("itemId");
        itemEntity.setName("lesson name");
        itemEntity.setPlanId(planId);
        itemEntity.setLessonId("lessonId");
        itemEntity.setDayOfWeek(1);

        // 模拟接口调用
        when(curriculumUnitDao.getById(unitId)).thenReturn(unit);
        when(planDao.listPlansByIds(Collections.singletonList(planId))).thenReturn(Collections.singletonList(planEntity));
        when(planItemDao.listByPlanIds(Collections.singletonList(planId))).thenReturn(Collections.singletonList(itemEntity));
        when(curriculumUnitPlanDao.getPlansByUnitId(unitId)).thenReturn(unitPlans);

        // 调用测试方法
        UnitPlanAndActivitiesResponse response = curriculumService.getUnitPlanAndActivities(unitId);

        // 验证响应结果
        Assert.assertNotNull(response);
        Assert.assertEquals("unitId", response.getUnitId());
        Assert.assertEquals("unitName", response.getUnitName());
        Assert.assertEquals(1, response.getPlans().size());
        Assert.assertEquals("theme", response.getPlans().get(0).getTheme());
    }

    /**
     * 测试添加单元编辑操作记录
     */
    @Test
    public void testAddUnitEditActionRecord() {
        String unitId = "unitId";
        String userId = "userId";
        String agencyId = "agencyId";

        // 当前用户信息
        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setUsername(userId);
        currentUser.setAgencyId(agencyId);


        // 模拟接口调用
        Mockito.when(userProvider.getCurrentUser()).thenReturn(currentUser);
        Mockito.when(lessonsCurriculumUnitEditRecordDao.save(any())).thenReturn(true);
        Mockito.doNothing().when(curriculumUnitDao).updateUnitUpdateTime(any(), any());

        // 调用测试方法
        SuccessResponse response = curriculumService.addUnitEditActionRecord(unitId);

        // 验证响应结果
        Assert.assertNotNull(response);
        Assert.assertTrue(response.isSuccess());
        Assert.assertEquals("unitId", response.getId());
    }

    /**
     * 测试更新 Unit 的周计划项信息
     * case: 其他人正在编辑
     */
    @Test
    public void testUpdateUnitPlanItem() {
        // 数据准备
        String unitId = "unitId001";
        String userId = "userId001";
        String planId = "planId001";
        UpdatePlanRequest request = new UpdatePlanRequest();
        request.setUnitId(unitId);
        request.setId(planId);

        LockedUnitModel lockedUnitModel = new LockedUnitModel();
        lockedUnitModel.setEmail("email");
        lockedUnitModel.setUserId("userId002");
        lockedUnitModel.setLockedAtUtc(new Date());
        lockedUnitModel.setUserName("username");
        CacheModel cacheModel = new CacheModel();
        cacheModel.setValue(JsonUtil.toJson(lockedUnitModel));
        cacheModel.setKey("cache_key");
        cacheModel.setExpired(false);
        cacheModel.setExpiredTTL(1000L);


        // 模拟接口调用
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(cacheService.get(anyString())).thenReturn(cacheModel);

        // 调用测试方法
        LockedUnitModel response = curriculumService.updateUnitPlanItem(request);

        // 校验结果
        Assert.assertNotNull(response);
        Assert.assertEquals("email", response.getEmail());
        Assert.assertEquals("userId002", response.getUserId());
    }

    /**
     * 测试更新 Unit 的周计划项信息
     * case: 其他人正在编辑
     */
    @Test
    public void testUpdateUnitPlanItem2() {
        // 数据准备
        String unitId = "unitId001";
        String userId = "userId001";
        String planId = "planId001";
        UpdatePlanRequest request = new UpdatePlanRequest();
        request.setUnitId(unitId);
        request.setId(planId);
        List<ItemModel> items = new ArrayList<>();
        ItemModel itemModel = new ItemModel();
        itemModel.setId("itemId001");
        itemModel.setName("itemName0000");
        itemModel.setPlanId(planId);
        itemModel.setCategoryId("categoryId001");
        itemModel.setLessonId("lessonId001");
        items.add(itemModel);
        ItemModel itemModel2 = new ItemModel();
        itemModel2.setId("itemId002");
        itemModel2.setName("itemName00000");
        itemModel2.setPlanId(planId);
        itemModel2.setCategoryId("categoryId002");
        itemModel2.setLessonId("lessonId002");
        items.add(itemModel2);
        List<ItemCategoryModel> categories = new ArrayList<>();
        ItemCategoryModel categoryModel = new ItemCategoryModel();
        categoryModel.setId("categoryId001");
        categoryModel.setItems(items);
        categoryModel.setName("categoryName001");
        categories.add(categoryModel);
        request.setCategories(categories);
        // 周计划数据
        PlanEntity planEntity = new PlanEntity();
        planEntity.setId("templatePlanId001");
        // 周计划分类集合
        List<CategoryEntity> categoryEntities = new ArrayList<>();
        CategoryEntity categoryEntity = new CategoryEntity();
        categoryEntity.setId("categoryId001");
        categoryEntity.setName("categoryName001");
        categoryEntities.add(categoryEntity);
        CategoryEntity categoryEntity2 = new CategoryEntity();
        categoryEntity2.setId("categoryId002");
        categoryEntity2.setName("categoryName002");
        categoryEntities.add(categoryEntity2);
        // 周计划项集合
        List<ItemEntity> itemEntities = new ArrayList<>();
        ItemEntity itemEntity = new ItemEntity();
        itemEntity.setId("itemId001");
        itemEntity.setName("itemName001");
        itemEntity.setCategoryId("categoryId001");
        itemEntity.setLessonId("lessonId001");
        itemEntity.setPlanId(planId);
        itemEntities.add(itemEntity);
        ItemEntity itemEntity2 = new ItemEntity();
        itemEntity2.setId("itemId002");
        itemEntity2.setName("itemName002");
        itemEntity2.setLessonId("lessonId002");
        itemEntity2.setCategoryId("categoryId002");
        itemEntity2.setPlanId(planId);
        itemEntities.add(itemEntity);
        // 测评点信息
        List<ItemMeasureDetailEntity> itemMeasureDetailEntities = new ArrayList<>();
        ItemMeasureDetailEntity measureDetailEntity = new ItemMeasureDetailEntity();
        measureDetailEntity.setItemId("itemId001");
        measureDetailEntity.setMeasureName("measureName001");
        measureDetailEntity.setMeasureAbbr("measureAbbr001");
        measureDetailEntity.setMeasureId("measureId001");
        measureDetailEntity.setCore(true);
        measureDetailEntity.setViewAbbreviation("viewAbbreviation001");
        itemMeasureDetailEntities.add(measureDetailEntity);
        ItemMeasureDetailEntity measureDetailEntity2 = new ItemMeasureDetailEntity();
        measureDetailEntity2.setItemId("itemId002");
        measureDetailEntity2.setMeasureName("measureName002");
        measureDetailEntity2.setMeasureAbbr("measureAbbr002");
        measureDetailEntity2.setMeasureId("measureId002");
        measureDetailEntity2.setCore(false);
        measureDetailEntity2.setViewAbbreviation("viewAbbreviation001");
        itemMeasureDetailEntities.add(measureDetailEntity2);
        // 课程数据
        LessonEntity lessonEntity = new LessonEntity();
        lessonEntity.setId("lessonId001");
        lessonEntity.setName("lessonName001");
        lessonEntity.setCoverMediaIds("coverId0001");
        // 模拟接口调用
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(cacheService.get(anyString())).thenReturn(null);
        when(planDao.getCurriculumGenieTemplate()).thenReturn(planEntity);
        when(planCategoryDao.listByPlanId(planEntity.getId())).thenReturn(categoryEntities);
        when(planItemDao.listByPlanId(planId)).thenReturn(itemEntities);
        when(planItemMeasureDao.listItemMeasureDetails(planId)).thenReturn(itemMeasureDetailEntities);
        when(lessonDao.getById(any())).thenReturn(lessonEntity);
        doNothing().when(planService).updateItemInfo(any(),any());
        when(planService.updateItemMeasures(any(),any(), anyList())).thenAnswer(invocation -> new Random().nextBoolean());
        // 调用测试方法
        LockedUnitModel response = curriculumService.updateUnitPlanItem(request);
        // 校验参数
        assertNull(response);
    }

    /**
     * 测试创建 Curriculum
     * Case: 正常创建
     */
    @Test
    public void testCreateCurriculum() {
        // 创建模拟数据
        CreateCurriculumRequest request = new CreateCurriculumRequest();
        request.setName("name");
        request.setAge("0");
        request.setGrade("Infant (0-1)");
        request.setAgeGroupName("[{\\\"name\\\":\\\"Infant (0-1)\\\",\\\"value\\\":\\\"0\\\"}]");
        request.setFrameworkId("frameworkId");
        request.setUseDomain(true);
        request.setLanguage("English");
        request.setUseLocation(true);
        request.setCountry("US");
        request.setState("CA");
        CurriculumGenieUnitModel unit = new CurriculumGenieUnitModel();
        unit.setTitle("title");
        unit.setDescription("desc");
        unit.setWeeks(4);
        request.setUnits(Collections.singletonList(unit));
        // 模拟用户信息
        AuthUserDetails user = new AuthUserDetails();
        user.setAgencyId("agencyId");
        user.setUsername("userId");

        // 模拟接口调用
        when(userProvider.getCurrentUser()).thenReturn(user);

        // 调用测试方法
        CreateCurriculumResponse response = curriculumService.createCurriculum(request);

        // 验证结果不空
        Assert.assertNotNull(request);
        Mockito.verify(curriculumDao, times(1)).save(any()); // save 方法应该被调用一次
        Mockito.verify(curriculumAgeDao, times(1)).save(any()); // save 方法应该被调用一次
        Mockito.verify(curriculumUnitDao, times(1)).saveBatch(any()); // saveBatch 方法应该被调用一次
    }

    /**
     * 测试更新 Curriculum
     * Case: 正常更新
     */
    @Test
    public void testUpdateCurriculum() {
        // 创建模拟数据
        CreateCurriculumRequest request = new CreateCurriculumRequest();
        request.setId("curriculumId");
        request.setName("name");
        request.setAge("0");
        request.setGrade("Infant (0-1)");
        request.setAgeGroupName("[{\\\"name\\\":\\\"Infant (0-1)\\\",\\\"value\\\":\\\"0\\\"}]");
        request.setFrameworkId("frameworkId");
        request.setUseDomain(true);
        request.setLanguage("English");
        request.setUseLocation(true);
        request.setCountry("US");
        request.setState("CA");
        List<CurriculumGenieUnitModel> unitModels = new ArrayList<>();
        CurriculumGenieUnitModel unit1 = new CurriculumGenieUnitModel();
        unit1.setId("unitId1");
        unit1.setTitle("title");
        unit1.setDescription("desc");
        unit1.setWeeks(4);
        unitModels.add(unit1);
        CurriculumGenieUnitModel unit2 = new CurriculumGenieUnitModel();
        unit2.setTitle("title");
        unit2.setDescription("desc");
        unit2.setWeeks(4);
        unitModels.add(unit2);
        request.setUnits(unitModels);
        // 模拟系列课程实体
        CurriculumEntity curriculumEntity = new CurriculumEntity();
        curriculumEntity.setId("curriculumId");
        curriculumEntity.setName("name");
        // 模拟单元实体
        List<CurriculumUnitEntity> unitEntities = new ArrayList<>();
        CurriculumUnitEntity unitEntity1 = new CurriculumUnitEntity();
        unitEntity1.setId("unitId1");
        unitEntity1.setTitle("title");
        unitEntity1.setDescription("desc");
        unitEntity1.setWeekCount(4);
        unitEntities.add(unitEntity1);
        CurriculumUnitEntity unitEntity2 = new CurriculumUnitEntity();
        unitEntity2.setId("unitId2");
        unitEntity2.setTitle("title");
        unitEntity2.setDescription("desc");
        unitEntity2.setWeekCount(4);
        unitEntities.add(unitEntity2);

        // 模拟用户信息
        AuthUserDetails user = new AuthUserDetails();
        user.setAgencyId("agencyId");
        user.setUsername("userId");
        // 自定义模块信息
        List<CurriculumCustomModuleRecordEntity> customModuleRecordEntities = new ArrayList<>();

        // 模拟接口调用
        when(userProvider.getCurrentUser()).thenReturn(user);
        when(curriculumDao.getById("curriculumId")).thenReturn(curriculumEntity);
        when(curriculumUnitDao.getUnitsByCurriculumId("curriculumId")).thenReturn(unitEntities);
        when(customModuleRecordDao.getListByMasterDataId(anyString())).thenReturn(customModuleRecordEntities);

        // 调用测试方法
        CreateCurriculumResponse response = curriculumService.updateCurriculum(request);

        // 验证结果不空
        Assert.assertNotNull(response);
        Mockito.verify(curriculumUnitDao, times(1)).saveBatch(any()); // update 方法应该被调用一次
        Mockito.verify(curriculumUnitDao, times(2)).updateBatchById(any()); // update 方法应该被调用一次、 delete 方法一次
        Mockito.verify(curriculumDao, times(1)).updateById(any()); // 更新 curriculum 方法应该被调用一次
    }

    /**
     * 测试获取 Curriculum 详情
     * Case: 正常获取非空 Curriculum 详情
     */
    @Test
    public void testGetCurriculumDetail() {
        // 构造参数
        String curriculumId = "curriculumId001";
        AuthUserDetails userDetails = new AuthUserDetails();
        userDetails.setUsername("001");
        CurriculumGenieModel curriculumGenieModel = new CurriculumGenieModel();

        // 设置返回结果
        when(userProvider.getCurrentUser()).thenReturn(userDetails);
        when(curriculumDao.getCurriculumGenieById(curriculumId)).thenReturn(curriculumGenieModel);

        // 调用方法
        GetCurriculumResponse curriculumResponse = curriculumService.getCurriculumDetail(curriculumId);

        // 校验参数
        assertNotNull(curriculumResponse);
    }

    /**
     * 测试获取 unit 详情（可选是否翻译）
     */
    @Test
    public void testGetCurriculumUnitDetailWithTranslation() {
        // 构造请求参数
        String unitId = "001";
        String langCode = "zh-CN";
        // 构建单元详情实体
        CurriculumUnitEntity unitEntity = new CurriculumUnitEntity();
        // 构建周计划详情实体
        PlanEntity planEntity = new PlanEntity();

        // 设置方法调用返回结果
        when(userProvider.getCurrentProject()).thenReturn("MAGIC-CURRICULUM");
        when(curriculumUnitDao.getUnitInfoById(unitId, true)).thenReturn(unitEntity);
        when(planService.getPlanTemplateByUnitId(unitId)).thenReturn(planEntity);
        when(translateProviderImpl.formatLangCode(anyString())).thenReturn(langCode);
        when(translateService.translateObjectData(any(), any(), any(), anyString())).thenReturn(new CurriculumUnitResponse());

        // 测试方法调用
        CurriculumUnitResponse response = curriculumService.getCurriculumUnitDetailWithTranslation(unitId, langCode, null, "", new MockHttpServletRequest());

        // 断言
        assertNotNull(response);
    }

    /**
     * 测试获取 curriculum 详情（可选是否翻译）
     */
    @Test
    public void testGetCurriculumDetailWithTranslation() {
        // 构造请求参数
        String curriculumId = "001";
        String langCode = "zh-CN";
        // 构建系列课程详情实体
        CurriculumEntity curriculumEntity = new CurriculumEntity();
        // 定义用户数据对象
        AuthUserDetails user = new AuthUserDetails();
        user.setUsername("002");

        // 设置方法调用返回结果
        when(curriculumDao.getCurriculumById(curriculumId)).thenReturn(curriculumEntity);
        when(userProvider.getCurrentUser()).thenReturn(user);
        when(translateProviderImpl.formatLangCode(anyString())).thenReturn(langCode);
        when(translateService.translateObjectData(any(), any(), any(), anyString())).thenReturn(new CurriculumResponse());

        // 测试方法调用
        CurriculumResponse response = curriculumService.getCurriculumDetailWithTranslation(curriculumId, false, langCode, null);

        // 断言
        assertNotNull(response);
    }

    /**
     * 测试获取 unit 列表（可选是否翻译）
     */
    @Test
    public void testGetUnitList() {
        // 构造请求参数
        String curriculumId = "001";
        String langCode = "zh-CN";
        // 构建系列课程详情实体
        CurriculumEntity curriculumEntity = new CurriculumEntity();

        // 设置方法调用返回结果
        when(curriculumDao.getById(curriculumId)).thenReturn(curriculumEntity);
        when(translateProviderImpl.formatLangCode(anyString())).thenReturn(langCode);
        when(translateService.translateObjectData(any(), any(), any(), anyString())).thenReturn(new UnitListResponse());

        // 测试方法调用
        UnitListResponse unitList = curriculumService.getUnitList(curriculumId, langCode);

        // 断言
        assertNotNull(unitList);
    }

    /**
     * 测试 获取 unit 课程计划源数据（可选是否翻译）
     */
    @Test
    public void testGetUnitPlanSourceWithTranslation() {
        // 构建请求参数
        String planId = "aaa";
        String unitId = "bbb";
        String curriculumId = "ccc";
        String langCode = "ddd";
        Boolean isDetect = false;
        CurriculumEntity curriculumEntity = new CurriculumEntity();
        curriculumEntity.setFrameworkId("eee");
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("fff");

        PlanEntity planEntity = new PlanEntity();
        planEntity.setId("ggg");
        planEntity.setAgencyId("111");

        // 设置方法调用返回结果
        when(planDao.get(anyString())).thenReturn(planEntity);
        when(curriculumDao.getById(anyString())).thenReturn(curriculumEntity);
        when(userProvider.getCurrentUserId()).thenReturn("");
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agencyModel);
        when(translateService.translateObjectData(any(), any(), any(), anyString())).thenReturn(new CurriculumResourcesResponse());

        // 调用服务方法
        CurriculumResourcesResponse response = curriculumService.getUnitPlanSourceWithTranslation(planId, unitId, curriculumId, langCode, isDetect);

        // 断言
        assertNotNull(response);
    }

    /**
     * 测试获取周计划概数据（可选是否翻译）
     */
    @Test
    public void testGetUnitPlanOverViewWithTranslation() {
        // 构建请求参数
        String unitId = "aaa";
        String langCode = "bbb";
        Boolean isDetect = false;
        CurriculumUnitEntity curriculumUnitEntity = new CurriculumUnitEntity();
        curriculumUnitEntity.setCurriculumId("ddd");
        CurriculumEntity curriculumEntity = new CurriculumEntity();
        curriculumEntity.setFrameworkId("ccc");
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("eee");

        // 设置方法调用返回结果
        when(curriculumUnitDao.getById(anyString())).thenReturn(curriculumUnitEntity);
        when(curriculumDao.getById(anyString())).thenReturn(curriculumEntity);
        when(userProvider.getCurrentUserId()).thenReturn("fff");
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agencyModel);
        when(translateService.translateObjectData(any(), any(), any(), anyString())).thenReturn(new UnitPlanOverviewResponse());

        // 调用服务方法
        UnitPlanOverviewResponse response = curriculumService.getUnitPlanOverViewWithTranslation(unitId, langCode, isDetect);

        // 断言
        assertNotNull(response);
    }

    /**
     * 测试获取单元 prompt 历史记录
     */
    @Test
    public void testGetUnitPromptHistory() {
        // 准备数据
        String userId = "userId";
        int pageNum = 1;
        int pageSize = 10;
        PageList<CurriculumPromptHistoryEntity> pageList = new PageList<>();
        CurriculumPromptHistoryEntity entity = new CurriculumPromptHistoryEntity();
        entity.setId("id");
        entity.setPromptHistory("{\"id\":\"id\",\"unitId\":\"unitId\",\"title\":\"title\",\"description\":\"description\",\"frameworkId\":\"frameworkId\",\"frameworkName\":\"frameworkName\",\"useDomain\":false,\"measures\":[{\"id\":\"measureId\",\"abbreviation\":\"Measure\"}],\"domains\":[{\"id\":\"domainId\",\"abbreviation\":\"Domain\"}]}");
        pageList.setRecords(Collections.singletonList(entity));
        pageList.setTotal(1l);
        // 模拟接口调用
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(curriculumPromptHistoryDao.listByTypeAndUserId(pageNum, pageSize, PromptHistoryType.UNIT_OVERVIEW.toString(), userId)).thenReturn(pageList);

        // 调用测试方法
        PageResponse<UnitPromptHistoryModel> unitPromptHistory = curriculumService.getUnitPromptHistory(pageNum, pageSize);

        // 验证结果
        Assert.assertNotNull(unitPromptHistory);
        Assert.assertEquals(1, unitPromptHistory.getTotal().intValue());
        Assert.assertEquals("unitId", unitPromptHistory.getItems().get(0).getUnitId());
    }

    /**
     * 测试创建单元 prompt 历史记录
     */
    @Test
    public void testCreateUnitPromptHistory() {
        // 准备数据
        String userId = "userId";
        String agencyId = "agencyId";
        String unitId = "unitId";
        String title = "title";
        String grade = "grade";
        String description = "description";
        String frameworkId = "frameworkId";
        String frameworkName = "frameworkName";
        Boolean useDomain = false;
        List<CurriculumPromptHistoryMeasureModel> measures = new ArrayList<>();
        CurriculumPromptHistoryMeasureModel measure = new CurriculumPromptHistoryMeasureModel();
        measure.setId("measureId");
        measure.setAbbreviation("Measure");
        measures.add(measure);
        List<CurriculumPromptHistoryMeasureModel> domains = new ArrayList<>();
        CurriculumPromptHistoryMeasureModel domain = new CurriculumPromptHistoryMeasureModel();
        domain.setId("domainId");
        domain.setAbbreviation("Domain");
        domains.add(domain);
        CreateUnitPromptHistoryRequest request = new CreateUnitPromptHistoryRequest();
        request.setUnitId(unitId);
        request.setTitle(title);
        request.setWeekCount(1);
        request.setDescription(description);
        request.setGrade(grade);
        request.setFrameworkId(frameworkId);
        request.setFrameworkName(frameworkName);
        request.setUseDomain(useDomain);
        request.setMeasures(measures);
        request.setDomains(domains);
        request.setClassroomType(ClassroomType.IN_PERSON);

        // 模拟接口调用
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.getCurrentAgencyId()).thenReturn(agencyId);
        when(curriculumPromptHistoryDao.save(any())).thenReturn(true);

        // 调用测试方法
        SuccessResponse response = curriculumService.createUnitPromptHistory(request);
        Mockito.verify(curriculumPromptHistoryDao, times(1)).save(any());// save应该被调用了一次
        // 验证结果
        Assert.assertNotNull(response);
    }

    /**
     * 测试删除单元 prompt 历史记录
     */
    @Test
    public void testDeleteUnitPromptHistory() {
        // 准备数据
        String userId = "userId";
        String id = "id";

        // 模拟接口调用
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(curriculumPromptHistoryDao.deleteUnitPromptHistory(id)).thenReturn(true);

        // 调用测试方法
        SuccessResponse response = curriculumService.deleteUnitPromptHistory(id);
        Assert.assertNotNull(response);
    }


    /**
     * 测试创建校训接口
     * Case: 测试请求体为空时，抛出异常情况
     */
    @Test(expected = BusinessException.class)
    public void testCreateLeanerProfileWithException() {
        CreateLearnerProfileRequest request = new CreateLearnerProfileRequest();
        curriculumService.createLearnerProfile(request);
    }

    /**
     * 测试创建校训接口
     * Case: 测试正常创建校训
     */
    @Test
    public void testCreateLeanerProfile() {
        // 模拟请求数据
        CreateLearnerProfileRequest request = new CreateLearnerProfileRequest();
        request.setLearnerProfile("learnerProfile");
        AgeGroupRubricsModel model = new AgeGroupRubricsModel();
        request.setAgeGroupRubrics(Collections.singletonList(model));
        AuthUserDetails currentUser = new AuthUserDetails(); // 模拟当前用户
        currentUser.setUsername("userId");
        currentUser.setAgencyId("agencyId");

        // 模拟接口调用
        when(userProvider.getCurrentUser()).thenReturn(currentUser);

        // 调用测试方法
        SuccessResponse response = curriculumService.createLearnerProfile(request);

        // 验证方法调用次数
        Mockito.verify(curriculumLearnerProfileDao, times(1)).save(any()); //  校训 Dao save 方法应该被调用一次
        Mockito.verify(curriculumLearnerProfileEditRecordDao, times(1)).save(any()); // 校训编辑记录 Dao save 方法应该被调用一次
        Assert.assertNotNull(response.getId()); // 验证返回结果 ID 不为空
        Assert.assertTrue(response.isSuccess()); // 验证返回结果为成功
    }

    /**
     * 验证更新校训接口
     * Case: 请求体为空时，抛出异常情况
     */
    @Test(expected = BusinessException.class)
    public void testUpdateLeanerProfileWithException() {
        UpdateLearnerProfileRequest request = new UpdateLearnerProfileRequest();
        curriculumService.updateLearnerProfile(request);
    }

    /**
     * 测试更新校训接口
     * Case: 正常更新校训
     */
    @Test
    public void testUpdateLeanerProfile() {
        // 模拟请求数据
        UpdateLearnerProfileRequest request = new UpdateLearnerProfileRequest();
        request.setId("id");
        request.setLearnerProfile("learnerProfile");
        AgeGroupRubricsModel model = new AgeGroupRubricsModel();
        request.setAgeGroupRubrics(Collections.singletonList(model));
        AuthUserDetails currentUser = new AuthUserDetails(); // 模拟当前用户
        currentUser.setUsername("userId");
        currentUser.setAgencyId("agencyId");

        when(curriculumLearnerProfileDao.getById(any())).thenReturn(new CurriculumLearnerProfileEntity());
        when(userProvider.getCurrentUser()).thenReturn(currentUser);

        // 调用测试方法
        SuccessResponse response = curriculumService.updateLearnerProfile(request);

        // 验证方法调用次数
        Mockito.verify(curriculumLearnerProfileDao, times(1)).updateById(any()); //  校训 Dao updateById 方法应该被调用一次
        Mockito.verify(curriculumLearnerProfileEditRecordDao, times(1)).save(any()); // 校训编辑记录 Dao save 方法应该被调用一次
        Assert.assertEquals("id", response.getId()); // 验证返回结果 ID 与请求一致
        Assert.assertTrue(response.isSuccess()); // 验证返回结果为成功
    }

    /**
     * 测试删除校训接口
     * Case: 校训不存在，抛出异常
     */
    @Test(expected = BusinessException.class)
    public void testDeleteLearnerProfileWithException() {
        curriculumService.deleteLearnerProfile("id");
    }

    /**
     * 测试删除校训接口
     * Case: 校训存在，删除成功
     */
    @Test
    public void testDeleteLearnerProfile() {
        AuthUserDetails currentUser = new AuthUserDetails(); // 模拟当前用户
        currentUser.setUsername("userId");
        currentUser.setAgencyId("agencyId");

        // 模拟接口调用
        when(curriculumLearnerProfileDao.getById(any())).thenReturn(new CurriculumLearnerProfileEntity());
        when(userProvider.getCurrentUser()).thenReturn(currentUser);

        // 调用测试方法
        SuccessResponse response = curriculumService.deleteLearnerProfile("id");

        // 验证方法调用次数
        Mockito.verify(curriculumLearnerProfileDao, times(1)).updateById(any()); //  校训 Dao updateById 方法应该被调用一次
        Mockito.verify(curriculumLearnerProfileEditRecordDao, times(1)).save(any()); // 校训编辑记录 Dao save 方法应该被调用一次
        Assert.assertEquals("id", response.getId()); // 验证返回结果 ID 与请求一致
        Assert.assertTrue(response.isSuccess()); // 验证返回结果为成功
    }

    /**
     * 测试获取默认校训
     * Case: 存在默认校训
     */
    @Test
    public void testGetAgencyLearnerProfileExist() {
        // 校训 Entity
        CurriculumLearnerProfileEntity curriculumLearnerProfileEntity = new CurriculumLearnerProfileEntity();
        curriculumLearnerProfileEntity.setId("learnerProfileId");
        curriculumLearnerProfileEntity.setGenerateExpectations(true);
        // 模拟当前用户
        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setUsername("userId");
        currentUser.setAgencyId("agencyId");

        // 模拟接口调用
        when(curriculumLearnerProfileDao.getAgencyLearnerProfile(anyString())).thenReturn(curriculumLearnerProfileEntity);
        when(userProvider.getCurrentUser()).thenReturn(currentUser);
        when(curriculumLearnerProfileEditRecordDao.getCurriculumLearnerProfileEditRecordModelList(anyList())).thenReturn(new ArrayList<>());

        // 调用测试方法
        GetAgencyLearnerProfileResponse response = curriculumService.getAgencyLearnerProfile();

        // 验证响应结果
        Assert.assertNotNull(response);
        Assert.assertEquals("learnerProfileId", response.getId());
    }

    /**
     * 测试获取默认校训
     * Case: 存在默认校训
     */
    @Test
    public void testGetAgencyLearnerProfileNotExist() {
        // 模拟当前用户
        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setUsername("userId");
        currentUser.setAgencyId("agencyId");

        // 模拟接口调用
        when(curriculumLearnerProfileDao.getAgencyLearnerProfile(anyString())).thenReturn(null);
        when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // 模拟接口调用
        when(userProvider.getCurrentUserId()).thenReturn("userId");

        // 调用测试方法
        GetAgencyLearnerProfileResponse response = curriculumService.getAgencyLearnerProfile();

        // 验证响应结果校训 ID 为空
        Assert.assertNull(response.getId());
    }
}

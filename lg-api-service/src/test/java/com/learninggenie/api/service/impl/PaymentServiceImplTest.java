package com.learninggenie.api.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.learninggenie.api.config.TestBase;
import com.learninggenie.api.constant.PaymentConstants;
import com.learninggenie.api.model.QuantityDiscountViewModel;
import com.learninggenie.api.model.TypePriceViewModel;
import com.learninggenie.common.data.entity.AppMetadataEntity;
import com.learninggenie.common.data.repository.AppMetadataRepository;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Ignore
public class PaymentServiceImplTest extends TestBase {
    @Autowired
    private AppMetadataRepository appMetadataRepository;
    ObjectMapper objectMapper = new ObjectMapper();

    @Test
    public void injectAllMetatdata() throws JsonProcessingException {
        createMetadata(PaymentConstants.TAX, "970");
        createMetadata(PaymentConstants.SHIPPING_FEE, "500");
        createMetadata(PaymentConstants.QUANTITY_DISCOUNT, createQuantityDiscount(1, 0));
        createMetadata(PaymentConstants.QUANTITY_DISCOUNT, createQuantityDiscount(2, 0));
        createMetadata(PaymentConstants.QUANTITY_DISCOUNT, createQuantityDiscount(3, 100));
        createMetadata(PaymentConstants.QUANTITY_DISCOUNT, createQuantityDiscount(4, 100));
        createMetadata(PaymentConstants.QUANTITY_DISCOUNT, createQuantityDiscount(5, 200));

        createMetadata(PaymentConstants.BOOK_TYPE, createPhotoBookType("t001", 1500, "10*10 photo book 20 pages"));
        createMetadata(PaymentConstants.BOOK_TYPE, createPhotoBookType("t002", 2500, "10*10 photo book 40 pages"));
        createMetadata(PaymentConstants.BOOK_TYPE, createPhotoBookType("t003", 3500, "10*10 photo book 60 pages"));

    }

    private String createPhotoBookType(String id, int price, String name) throws JsonProcessingException {
        TypePriceViewModel typePriceViewModel = new TypePriceViewModel();
        typePriceViewModel.setTypeId(id);
        typePriceViewModel.setPrice(price);
        typePriceViewModel.setTypeName(name);
        return objectMapper.writeValueAsString(typePriceViewModel);
    }

    private String createQuantityDiscount(int quantity, int discount) throws JsonProcessingException {
        QuantityDiscountViewModel quantityDiscountViewModel = new QuantityDiscountViewModel();
        quantityDiscountViewModel.setQuantity(quantity);
        quantityDiscountViewModel.setDiscount(discount);
        return objectMapper.writeValueAsString(quantityDiscountViewModel);
    }

    private void createMetadata(String metaKey, String metaValue) {
        AppMetadataEntity appMetadataEntity = new AppMetadataEntity();
        appMetadataEntity.setMetaKey(metaKey);
        appMetadataEntity.setMetaValue(metaValue);
        appMetadataRepository.saveAndFlush(appMetadataEntity);
    }
}
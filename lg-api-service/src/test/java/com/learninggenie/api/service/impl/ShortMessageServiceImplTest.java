package com.learninggenie.api.service.impl;

import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.model.ShortMessage;
import com.learninggenie.api.model.captcha.Captcha;
import com.learninggenie.api.model.shortmessage.BatchSendParentsInvitationRequest;
import com.learninggenie.api.provider.CaptchaProvider;
import com.learninggenie.api.provider.ShieldProvider;
import com.learninggenie.api.provider.ShortMessageProvider;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.common.data.entity.UserEntity;
import com.learninggenie.common.data.repository.UserRepository;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.*;

/**
 * @author: ZL
 * @Date: Created in 11:24 2018/3/8
 */
@Ignore
@RunWith(MockitoJUnitRunner.class)
public class ShortMessageServiceImplTest {
    @Mock
    private ShortMessageProvider shortMessageProvider;
    @Mock
    private ShieldProvider shieldProvider;
    @Mock
    private CaptchaProvider captchaProvider;
    @Mock
    private UserRepository userRepository;
    @Mock
    private UserProvider userProvider;
    @InjectMocks
    private ShortMessageServiceImpl shortMessageService;

    /**
     * case:发送短信，手机号为空，IP不为空
     */
    @Test(expected = BusinessException.class)
    public void testSendUserRegisterCaptcha_PhoneNumberIsNull(){
        shortMessageService.sendUserRegisterCaptcha(null,"");
    }

    /**
     * case:发送短信，手机号不为空，IP为空
     */
    @Test(expected = BusinessException.class)
    public void testSendUserRegisterCaptcha_IpIsNull(){
        shortMessageService.sendUserRegisterCaptcha("testPhone",null);
    }

    /**
     * case:安全检查 手机号发送超过最大次数
     */
    @Test(expected = BusinessException.class)
    public void testSendUserRegisterCaptcha_OperationExcessive(){
        when(shieldProvider.isOperationExcessive(anyString(),any(),anyInt())).thenReturn(true);
        shortMessageService.sendUserRegisterCaptcha("testPhone","testTest");
    }

    /**
     * case:安全检查 手机号发送未过最大次数,需要等待验证码发送间隔
     */
    @Test(expected = BusinessException.class)
    public void  testSendUserRegisterCaptcha_NotOperationAvailable(){
        when(shieldProvider.isOperationExcessive(anyString(),any(),anyInt())).thenReturn(false);
        when(shieldProvider.isOperationAvailable(anyString(),any())).thenReturn(false);
        shortMessageService.sendUserRegisterCaptcha("testPhone","testTest");
    }
    /**
     * case:安全检查 手机号发送未过最大次数,不需要等待验证码发送间隔
     */
    @Ignore
    @Test()
    public void  testSendUserRegisterCaptcha_OperationAvailable(){
        when(shieldProvider.isOperationExcessive(anyString(),any(),anyInt())).thenReturn(false);
        when(shieldProvider.isOperationAvailable(anyString(),any())).thenReturn(true);
        shortMessageService.sendUserRegisterCaptcha("testPhone","testTest");
    }

    public Captcha prepareCaptcha(){
        Captcha captcha=new Captcha();
        captcha.setCode("654321");
        captcha.setKey("USER_REGISTER_SEND_MSG12345678912");
        captcha.setContact("12345678912");
        return captcha;
    }
    /**
     * case:测试用户注册短信验证码发送
     */
    @Test
    public void testSendUserRegisterCaptcha_Send() throws Exception {
        Captcha captcha= this.prepareCaptcha();
        when(userRepository.getUserEntityByPhoneNumberAndIsDeletedFalse(anyString())).thenReturn(null);
        when(shieldProvider.isOperationExcessive(anyString(), any(),anyInt())).thenReturn(false);
        when(shieldProvider.isOperationAvailable(anyString(),any())).thenReturn(true);
        when(captchaProvider.generateCaptcha(anyString(),anyString(),any(),anyInt())).thenReturn(captcha);
        //when(shortMessageProvider.sendMsg(message)).thenReturn(true);
        shortMessageService.sendUserRegisterCaptcha("p001","");
        verify(shortMessageProvider,times(1)).sendMsg(any(ShortMessage.class));
    }

    /**
     * case:测试批量发送方法BatchSendParentsInvitationRequest为null，IP不为null
     */
    @Test(expected = NullPointerException.class)
    public void testBatchSendParentsInvitation_RequestIsNull(){
        shortMessageService.batchSendParentsInvitation(null,"testIp");
    }

    /**
     * case:测试批量发送方法IP为null，BatchSendParentsInvitationRequest不为null
     */
    @Test(expected = BusinessException.class)
    public void testBatchSendParentsInvitation_IpIsNull(){
        BatchSendParentsInvitationRequest testRequest = new BatchSendParentsInvitationRequest();
        shortMessageService.batchSendParentsInvitation(testRequest,null);
    }

//    public List<ParentInvitationModel> prepareList(){
////        List<ParentInvitationModel> list = new ArrayList<>();
////        ParentInvitationModel parentInvitationModel1 = new ParentInvitationModel();
////        parentInvitationModel1.setInvitation("t001");
////        parentInvitationModel1.setPhoneNumber("p001");
////        ParentInvitationModel parentInvitationModel2 = new ParentInvitationModel();
////        parentInvitationModel2.setInvitation("t002");
////        parentInvitationModel2.setPhoneNumber("p002");
////        list.add(parentInvitationModel1);
////        list.add(parentInvitationModel2);
////        return list;
////    }
//    public BatchSendParentsInvitationRequest prepareBatchSendParentsInvitationRequest(){
//        BatchSendParentsInvitationRequest request = new BatchSendParentsInvitationRequest();
//        request.setUid("r001");
//        request.setParentInvitations(this.prepareList());
//        return request;
//    }

    public UserEntity prepareUserEntity(){
        UserEntity userEntity = new UserEntity();
        userEntity.setId("u001");
        userEntity.setRole("parent");
        return userEntity;
    }
//    /**
//     * case:测试批量发送短信
//     */
//    @Ignore
//    @Test
//    public void testBatchSendParentsInvitation_Send() throws Exception {
//        BatchSendParentsInvitationRequest request = this.prepareBatchSendParentsInvitationRequest();
//        UserEntity userEntity = this.prepareUserEntity();
//        when(userProvider.checkUser(anyString())).thenReturn(userEntity);
//        Assert.assertEquals(false,userEntity==null);
//        Assert.assertEquals(true,userEntity.getRole().equalsIgnoreCase(UserRole.PARENT.toString()));
//        List<ShortMessage> messages = request.getParentInvitations().stream().map(SmsUtil::parentInvitationToSortMessage).collect(Collectors.toList());
//        when(shortMessageProvider.batchMsg(messages)).thenReturn(true);
//        //Boolean bool = shortMessageProvider.batchMsg(messages);
//        shortMessageService.batchSendParentsInvitation(request,"");
//        verify(shortMessageProvider,times(1)).batchMsg(any());
//    }
}

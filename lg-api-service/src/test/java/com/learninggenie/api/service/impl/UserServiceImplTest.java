package com.learninggenie.api.service.impl;
import com.google.api.client.util.Lists;
import com.google.protobuf.InvalidProtocolBufferException;
import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.model.*;
import com.learninggenie.api.model.agency.AcademyOpen;
import com.learninggenie.api.model.agency.AgencySnapshotModel;
import com.learninggenie.api.model.drdp.UploadFailedResponse;
import com.learninggenie.api.model.importdata.LastImportInviteParentsOptionResponse;
import com.learninggenie.api.model.lesson2.*;
import com.learninggenie.api.model.note.LearningStoryModel;
import com.learninggenie.api.model.user.*;
import com.learninggenie.api.model.wechat.EducatorUserEnrollmentChatGroupResp;
import com.learninggenie.api.model.inkind.InKindBatchUpdateRateGuideResponse;
import com.learninggenie.common.data.entity.InkindSchoolYearEntity;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.security.AuthUserDetails;
import com.learninggenie.api.security.DotNetPasswordEncoder;
import com.learninggenie.common.cache.CacheModel;
import com.learninggenie.common.cache.CacheServiceImpl;
import com.learninggenie.common.cache.RedisCacheServiceImpl;
import com.learninggenie.common.comm.CenterChatModel;
import com.learninggenie.common.comm.ChildChatModel;
import com.learninggenie.common.data.dao.*;
import com.learninggenie.common.data.dao.agencies.AgencyCenterDao;
import com.learninggenie.common.data.dao.authentication.UserDao;
import com.learninggenie.common.data.dao.authentication.UserProfileDao;
import com.learninggenie.common.data.dao.impl.CenterMapper;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.dao.medias.MediaEntityDao;
import com.learninggenie.common.data.dao.users.MetaDataDao;
import com.learninggenie.common.data.entity.*;
import com.learninggenie.common.data.entity.agencies.AgencyCenterEntity;
import com.learninggenie.common.data.entity.contents.UserGroupEntity;
import com.learninggenie.common.data.entity.medias.MediaEntity;
import com.learninggenie.common.data.entity.users.MetaDataEntity;
import com.learninggenie.common.data.enums.AgencyMetaKey;
import com.learninggenie.common.data.enums.DrdpSettingKey;
import com.learninggenie.common.data.enums.DrdpSettingValue;
import com.learninggenie.common.data.enums.TopNavbarStatus;
import com.learninggenie.common.data.enums.UserMetaKey;
import com.learninggenie.common.data.enums.UserRole;
import com.learninggenie.common.data.enums.user.Role;
import com.learninggenie.common.data.model.AgencyModel;
import com.learninggenie.common.data.model.AgencySnapshotEntity;
import com.learninggenie.common.data.model.CenterGroupModel;
import com.learninggenie.common.data.model.CenterModel;
import com.learninggenie.common.data.model.CenterWithIdName;
import com.learninggenie.common.data.model.GroupEntry;
import com.learninggenie.common.data.model.UserModel;
import com.learninggenie.common.data.model.UserStatistics;
import com.learninggenie.common.data.model.UsersMetaDataEntity;
import com.learninggenie.common.data.model.dll.DLLLanguageModel;
import com.learninggenie.common.data.model.framework.GroupFrameworkStatsModel;
import com.learninggenie.common.data.model.user.AgencyCenterInfoModel;
import com.learninggenie.common.data.repository.UserRepository;
import com.learninggenie.common.filesystem.FileSystem;
import com.learninggenie.common.framwork.FrameworkProvider;
import com.learninggenie.common.messaging.EmailModel;
import com.learninggenie.common.messaging.EmailTemplate;
import com.learninggenie.common.messaging.MandrillServiceImpl;
import com.learninggenie.common.region.RegionService;
import com.learninggenie.common.report.LGSnapshot;
import com.learninggenie.common.utils.StringUtil;
import com.learninggenie.common.utils.TimeUtil;
import org.apache.commons.collections4.list.GrowthList;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.jdbc.core.JdbcTemplate;

import java.io.IOException;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Created by zjj on 2016/3/1.
 */
@RunWith(MockitoJUnitRunner.class)
public class UserServiceImplTest {
    @InjectMocks
    UserServiceImpl userService;

    @Mock
    UsersMetaDataDao userMetaDao;

    @Mock
    UsersMetaDataDao usersMetaDataDao;;

    @Mock
    JdbcTemplate jdbcTemplate;

    @Mock
    UserProvider userProvider;

    @Mock
    private CenterDao centerDao;

    @Mock
    private GroupDao groupDao;

    @Mock
    private UserDaoImpl userDao;

    @Mock
    private StudentDao studentDao;

    @Mock
    private AgencyDao agencyDao;

    @Mock
    private CenterMapper centerMapper;

    @Mock
    private CacheServiceImpl cacheService;

    @Mock
    private MandrillServiceImpl mandrillService;

    @Mock
    private FileSystem fileSystem;

    @Mock
    private UserRepository userRepository;

    @Mock
    private CacheDao cacheDao;

    @Mock
    private DomainDao domainDao;
    
    @Mock
    private InkindDao inkindDao;

    @Mock
    private FrameworkProvider frameworkProvider;

    @Mock
    private RegionService regionService;

    @Mock
    private ScoreServiceImpl scoreService;

    @Mock
    private RedisCacheServiceImpl redisCacheService;

    @Mock
    private MetaDataDao metaDataDao;

    @Mock
    private AgencyCenterDao agencyCenterDao;

    @Mock
    private com.learninggenie.common.data.dao.contents.CenterDao Center2Dao;

    @Mock
    private com.learninggenie.common.data.dao.agencies.CenterUserDao agencyCenterUserDao;

    @Mock
    private com.learninggenie.common.data.dao.CenterUserDao centerUserDao;

    @Mock
    private com.learninggenie.common.data.dao.contents.GroupDao contentsGroupDao;

    @Mock
    private UserDao user2Dao;

    @Mock
    private UserProfileDao userProfileDao;

    @Mock
    private MediaEntityDao mediaEntityDao;

    @Mock
    private DLLDao dllDao;

    @Mock
    private MetaDao metaDao;

    @Mock
    private DotNetPasswordEncoder dotNetPasswordEncoder;

    /**
     * 添加老师的MAC地址
     * mac 已存在 抛异常
     * zjj 2016.3.1
     */
    @Test(expected = BusinessException.class)
    public void testAddTeacherMacAddress_macAddressExist(){
        List<UsersMetaDataEntity> metas=new ArrayList<>();
        MacAddress macaddress=new MacAddress();
        macaddress.setId("001");
        macaddress.setMacAddress("1:2:3");
        macaddress.setEquipment("Windows");
        UsersMetaDataEntity meta1=new UsersMetaDataEntity();
        meta1.setId(macaddress.getId());
        meta1.setMetaValue("1:2:3");
        metas.add(meta1);
        when(userMetaDao.getMetas(Mockito.anyString(), Mockito.anyString())).thenReturn(metas);
        userService.addTeacherMacAddress(macaddress,"001");
    }

    /**
     * 添加老师的MAC地址
     * mac 为空 抛异常
     * zjj 2016.3.1
     */
    @Test(expected = BusinessException.class)
    public void testAddTeacherMacAddress_noMacAddress(){
        List<UsersMetaDataEntity> metas=new ArrayList<>();
        MacAddress macaddress=new MacAddress();
        macaddress.setId("001");
        macaddress.setEquipment("Windows");
        UsersMetaDataEntity meta1=new UsersMetaDataEntity();
        meta1.setId(macaddress.getId());
        meta1.setMetaValue("1:2:3");
        metas.add(meta1);
//        Mockito.when(userMetaDao.getMetas(Mockito.anyString(), Mockito.anyString())).thenReturn(metas);
        userService.addTeacherMacAddress(macaddress,"001");
    }
    /**
     * 添加老师的MAC地址
     * 正常添加
     * zjj 2016.3.1
     */
    @Test
    public void testAddTeacherMacAddress(){
        List<UsersMetaDataEntity> metas=new ArrayList<>();
        MacAddress macaddress=new MacAddress();
        macaddress.setId("001");
        macaddress.setMacAddress("1:2:4");
        macaddress.setEquipment("Windows");
        UsersMetaDataEntity meta1=new UsersMetaDataEntity();
        meta1.setId(macaddress.getId());
        meta1.setMetaValue("1:2:3");
        metas.add(meta1);
        when(userMetaDao.getMetas(Mockito.anyString(), Mockito.anyString())).thenReturn(metas);
        userService.addTeacherMacAddress(macaddress,"001");
        Mockito.verify(userMetaDao,Mockito.times(1)).insertMeta(Mockito.anyString(),Mockito.anyString(),Mockito.anyString());
    }

    /**
     * 更新老师的MAC地址
     * 传入的mac 为空 抛异常
     * zjj 2016.3.1
     */
    @Test(expected = BusinessException.class)
    public void testUpdateTeacherMacAddress_noMacAddress(){
        MacAddress macaddress=new MacAddress();
        macaddress.setId("001");
        macaddress.setEquipment("Windows");
        userService.updateTeacherMacAddress(macaddress);
    }
    /**
     * 更新老师的MAC地址
     *要更新的mac不存在
     * zjj 2016.3.1
     */
    @Test(expected = BusinessException.class)
    public void testUpdateTeacherMacAddressMAC_addressNotFound(){
        MacAddress macaddress=new MacAddress();
        macaddress.setId("001");
        macaddress.setMacAddress("1:2:4");
        macaddress.setEquipment("Windows");
        when(userMetaDao.exist(Mockito.anyString())).thenReturn(false);
        userService.updateTeacherMacAddress(macaddress);
    }
    /**
     * 更新老师的MAC地址
     * 正常更新
     * zjj 2016.3.1
     */
    @Test
    public void testUpdateTeacherMacAddressMAC(){
        MacAddress macaddress=new MacAddress();
        macaddress.setId("001");
        macaddress.setMacAddress("1:2:4");
        macaddress.setEquipment("Windows");
        when(userMetaDao.exist(Mockito.anyString())).thenReturn(true);
        userService.updateTeacherMacAddress(macaddress);
        Mockito.verify(userMetaDao,Mockito.times(1)).updateMetaById(Mockito.anyString(),Mockito.anyString());
    }

    /**
     * 删除老师mac地址
     * zjj 2016.3.1
     */
    @Test
    public void testDeleteTeacherMacAddress(){
        userService.deleteTeacherMacAddress(Mockito.anyString());
        Mockito.verify(userMetaDao,Mockito.times(1)).delete(Mockito.anyString());
    }

    /**
     * 根据用户的获取学校的id，name
     * case : 用户为agency_owner
     */
    @Test
    public void testGetCenterIdNameByAgencyOwner(){
        String userId = "U001";
        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setRole(UserRole.AGENCY_OWNER.toString());
        when(userProvider.checkUser(userId)).thenReturn(user);
        userService.getCenterIdNameByUserId(userId, null);
        Mockito.verify(userDao,Mockito.times(1)).getCenterIdNameByAgencyAdmin(userId);
    }

    /**
     * 根据用户的获取学校的id，name
     * case : 用户为agency_admin
     */
    @Test
    public void testGetCenterIdNameByAgencyAdmin(){
        String userId = "U001";
        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setRole(UserRole.AGENCY_ADMIN.toString());
        when(userProvider.checkUser(userId)).thenReturn(user);
        userService.getCenterIdNameByUserId(userId, null);
        Mockito.verify(userDao,Mockito.times(1)).getCenterIdNameByAgencyAdmin(userId);
    }

    /**
     * 根据用户的获取学校的id，name
     * case : 用户为site_admin
     */
    @Test
    public void testGetCenterIdNameBySiteAdmin(){
        String userId = "U001";
        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setRole(UserRole.SITE_ADMIN.toString());
        when(userProvider.checkUser(userId)).thenReturn(user);
        userService.getCenterIdNameByUserId(userId,null);
        Mockito.verify(userDao,Mockito.times(1)).getCenterIdNameBySiteAdminId(userId);
    }

    /**
     * 根据用户的获取学校的id，name
     * case : 用户为teacher
     */
    @Test
    public void testGetCenterIdNameByTeacher(){
        String userId = "U001";
        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setRole(UserRole.COLLABORATOR.toString());
        when(userProvider.checkUser(userId)).thenReturn(user);
        userService.getCenterIdNameByUserId(userId,null);
        Mockito.verify(userDao,Mockito.times(1)).getCenterIdNameByTeacherId(userId);
    }

    /**
     * 根据用户的id获取学校和班级
     * case : 用户为agency_owner
     */
    @Test
    public void getCenterGroupByAgencyOwner(){
        String userId = "U001";
        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setRole(UserRole.AGENCY_OWNER.toString());
//        Mockito.when(userProvider.checkUser(userId)).thenReturn(user);
        List<CenterGroupModel> centerGroupModels = new ArrayList<>();
        CenterGroupModel center001 = new CenterGroupModel();
        center001.setCenterId("c001");
        center001.setCenterName("c001");
        CenterGroupModel center002 = new CenterGroupModel();
        center002.setCenterId("c002");
        center002.setCenterName("c002");
        centerGroupModels.add(center001);
        centerGroupModels.add(center002);

        List<AgencyModel> agencyModels = new ArrayList<>();
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("a001");
        agencyModel.setName("a001");
        agencyModels.add(agencyModel);
//        Mockito.when(userDao.getCenterGroupByAgencyUser(userId)).thenReturn(centerGroupModels);
//        Mockito.when(centerMapper.getAgencyByCenterId(centerGroupModels.get(0).getCenterId())).thenReturn(agencyModels);
//        List<CenterModel> centerModelList = userService.getCenterGroupByUserId(userId, false);
//        Assert.assertEquals(2,centerModelList.size());
    }

    /**
     * 根据用户的id获取学校和班级
     * case : 用户为agency_admin
     */
    @Test
    public void getCenterGroupByAgencyAdmin(){
        String userId = "U001";
        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setRole(UserRole.AGENCY_ADMIN.toString());
//        Mockito.when(userProvider.checkUser(userId)).thenReturn(user);
        List<CenterGroupModel> centerGroupModels = new ArrayList<>();
        CenterGroupModel center001 = new CenterGroupModel();
        center001.setCenterId("c001");
        center001.setCenterName("c001");
        CenterGroupModel center002 = new CenterGroupModel();
        center002.setCenterId("c002");
        center002.setCenterName("c002");
        centerGroupModels.add(center001);
        centerGroupModels.add(center002);

        List<AgencyModel> agencyModels = new ArrayList<>();
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("a001");
        agencyModel.setName("a001");
        agencyModels.add(agencyModel);
//        Mockito.when(userDao.getCenterGroupByAgencyUser(userId)).thenReturn(centerGroupModels);
//        Mockito.when(centerMapper.getAgencyByCenterId(centerGroupModels.get(0).getCenterId())).thenReturn(agencyModels);
//        List<CenterModel> centerModelList = userService.getCenterGroupByUserId(userId, false);
//        Assert.assertEquals(2,centerModelList.size());
    }
    /**
     * 根据用户的id获取学校和班级
     * case : 用户为agency_admin
     */
    @Test
    public void getCenterGroupBySiteAdmin(){
        String userId = "U001";
        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setRole(UserRole.SITE_ADMIN.toString());
//        Mockito.when(userProvider.checkUser(userId)).thenReturn(user);
        List<CenterGroupModel> centerGroupModels = new ArrayList<>();
        CenterGroupModel center001 = new CenterGroupModel();
        center001.setCenterId("c001");
        center001.setCenterName("c001");
        CenterGroupModel center002 = new CenterGroupModel();
        center002.setCenterId("c002");
        center002.setCenterName("c002");
        centerGroupModels.add(center001);
        centerGroupModels.add(center002);

        List<AgencyModel> agencyModels = new ArrayList<>();
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("a001");
        agencyModel.setName("a001");
        agencyModels.add(agencyModel);
//        Mockito.when(userDao.getCenterGroupBySiteAdmin(userId)).thenReturn(centerGroupModels);
//        Mockito.when(centerMapper.getAgencyByCenterId(centerGroupModels.get(0).getCenterId())).thenReturn(agencyModels);
//        List<CenterModel> centerModelList = userService.getCenterGroupByUserId(userId, false);
//        Assert.assertEquals(2,centerModelList.size());
    }


    /**
     * 根据用户的id获取学校和班级
     * case : 用户为teacher
     */
    @Test
    public void getCenterGroupByTeacher(){
        String userId = "U001";
        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setRole(UserRole.COLLABORATOR.toString());
//        Mockito.when(userProvider.checkUser(userId)).thenReturn(user);
        List<CenterGroupModel> centerGroupModels = new ArrayList<>();
        CenterGroupModel center001 = new CenterGroupModel();
        center001.setCenterId("c001");
        center001.setCenterName("c001");
        CenterGroupModel center002 = new CenterGroupModel();
        center002.setCenterId("c002");
        center002.setCenterName("c002");
        centerGroupModels.add(center001);
        centerGroupModels.add(center002);

        List<AgencyModel> agencyModels = new ArrayList<>();
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("a001");
        agencyModel.setName("a001");
        agencyModels.add(agencyModel);
//        Mockito.when(userDao.getCenterGroupByTeacherId(userId)).thenReturn(centerGroupModels);
//        Mockito.when(centerMapper.getAgencyByCenterId(centerGroupModels.get(0).getCenterId())).thenReturn(agencyModels);
//        List<CenterModel> centerModelList = userService.getCenterGroupByUserId(userId, false);
//        Assert.assertEquals(2,centerModelList.size());
    }

    /**
     * 获取整个agency中的agecny_admin
     */
    @Test
    public void testGetAgencyAdmins(){
        String userId = "001";
        List<AgencyModel> agencyModels = new ArrayList<>();
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("a001");
        agencyModel.setName("a001");
        agencyModels.add(agencyModel);
        when(userDao.getAgencyByAgencyAdminId(userId)).thenReturn(agencyModels);
        List<UserModel> userModels = new ArrayList<>();
        UserModel user001 = new UserModel();
        user001.setId("001");
        user001.setRole(UserRole.AGENCY_OWNER.toString());
        user001.setDisplayName("001");
        UserModel user002 = new UserModel();
        user002.setId("002");
        user002.setRole(UserRole.AGENCY_ADMIN.toString());
        user002.setDisplayName("002");
        userModels.add(user001);
        userModels.add(user002);
        when(userDao.getAgencyAdminsByAgencyId(agencyModels.get(0).getId())).thenReturn(userModels);
        List<UserModel> userModelList = userService.getAgencyAdmins(userId);
        assertEquals(1,userModelList.size());
    }

    /**
     * transfer操作
     * case： 两个用户不在同一个agency 抛异常
     */
    @Test(expected = BusinessException.class)
    public void testTransferEmailNotInSameAgency (){
        String  applicantId = "u001";
        String  approverId = "u002";
        //模拟agency
        List<AgencyModel> agencyModels = new ArrayList<>();
        AgencyModel agency = new AgencyModel();
        agency.setId("a001");
        agency.setName("a001");
        agencyModels.add(agency);
        when(userDao.getAgencyByAgencyAdminId(applicantId)).thenReturn(agencyModels);
        //模拟申请transfer的用户
        UserEntity applicant = new UserEntity();
        applicant.setId(applicantId);
        UserProfileEntity profile1 = new UserProfileEntity();
        profile1.setDisplayName("u001");
        applicant.setProfile(profile1);
        applicant.setRole(UserRole.AGENCY_OWNER.toString());
        when(userProvider.checkUser(applicantId)).thenReturn(applicant);
        //模拟接受transfer申请的用户
        UserModel approver = new UserModel();
        approver.setId(approverId);
        profile1.setDisplayName("u002");
        approver.setRole(UserRole.AGENCY_ADMIN.toString());
        when(userDao.getUserByEmail(approverId)).thenReturn(approver);
        userService.transferEmail(applicantId,approverId);

    }

    /**
     * transfer功能
     * case： 要transfer的人不是agency admin 抛异常
     */
    @Test(expected = BusinessException.class)
    public void testTransferEmailApproverNotAgencyAdmin (){
        String  applicantId = "u001";
        String  approverId = "u002";
        //模拟agency
        List<AgencyModel> agencyModels = new ArrayList<>();
        AgencyModel agency = new AgencyModel();
        agency.setId("a001");
        agency.setName("a001");
        agencyModels.add(agency);
        when(userDao.getAgencyByAgencyAdminId(applicantId)).thenReturn(agencyModels);
        //模拟申请transfer的用户
        UserEntity applicant = new UserEntity();
        applicant.setId(applicantId);
        UserProfileEntity profile1 = new UserProfileEntity();
        profile1.setDisplayName("u001");
        applicant.setProfile(profile1);
        applicant.setRole(UserRole.AGENCY_OWNER.toString());
        when(userProvider.checkUser(applicantId)).thenReturn(applicant);
        //模拟接受transfer申请的用户
        UserEntity approver = new UserEntity();
        approver.setId(approverId);
        profile1.setDisplayName("u002");
        approver.setProfile(profile1);
        approver.setRole(UserRole.SITE_ADMIN.toString());
//        Mockito.when(userProvider.checkUserByEmail(approverId)).thenReturn(approver);
        userService.transferEmail(applicantId,approverId);
    }
    /**
     * transfer功能
     * 正常transfer
     */
    @Test
    public void testTransferEmail (){
        String  applicantId = "u001";
        String  approverId = "u002";
        //模拟agency
        List<AgencyModel> agencyModels = new ArrayList<>();
        AgencyModel agency = new AgencyModel();
        agency.setId("a001");
        agency.setName("a001");
        agencyModels.add(agency);
        when(userDao.getAgencyByAgencyAdminId(applicantId)).thenReturn(agencyModels);
        //模拟申请transfer的用户
        UserEntity applicant = new UserEntity();
        applicant.setId(applicantId);
        UserProfileEntity profile1 = new UserProfileEntity();
        profile1.setDisplayName("u001");
        applicant.setProfile(profile1);
        applicant.setRole(UserRole.AGENCY_OWNER.toString());
        when(userProvider.checkUser(applicantId)).thenReturn(applicant);
        when(userProvider.isInSameAgency(applicantId,approverId)).thenReturn(true);
        //模拟接受transfer申请的用户
        UserModel approver = new UserModel();
        approver.setId(approverId);
        profile1.setDisplayName("u002");
        approver.setRole(UserRole.AGENCY_ADMIN.toString());
        CacheModel cache = new CacheModel();
        cache.setKey("key");
        cache.setValue("value");
        when(cacheService.create(null,24*60)).thenReturn(cache);
        when(userDao.getUserByEmail(approverId)).thenReturn(approver);
        when(mandrillService.getEmailTemplate(EmailTemplate.TRANSFER_ACCOUNT)).thenReturn("template");
        userService.transferEmail(applicantId,approverId);
        Mockito.verify(mandrillService,Mockito.times(1)).sendAsync(any(EmailModel.class));
    }

    /**
     * 搜索要合并的用户
     * case： 两个用户在同一个agency  返回null
     */
    @Test
    public void testMergeSearchUserInSameAgency(){
        //要被合并的用户邮箱
        String email = "testEmail";
        //申请合并的用户id
        String userId = "001";
        UserModel user = new UserModel();
        user.setId("002");
        user.setRole(UserRole.AGENCY_OWNER.toString());
        when(userProvider.isInSameAgency(user.getId(),userId)).thenReturn(true);
        when(userDao.getUserByEmail(email)).thenReturn(user);
        MergeSearchUser mergeSearchUser = userService.getMergeSearchUser(email,userId);
        assertEquals(null,mergeSearchUser);
    }

    /**
     * 合并agency
     * case: 申请合并的两个账户在同一个agency,抛异常
     */
    @Test (expected = BusinessException.class)
    public void testMergeEmail_InSameAgency() {
        String targetId = "u001";
        String fromId = "u002";
        UserEntity applicant = new UserEntity();
        applicant.setId(targetId);
        applicant.setRole(UserRole.AGENCY_ADMIN.toString());
        when(userProvider.checkUser(targetId)).thenReturn(applicant);
        List<AgencyModel> agencyModels = new ArrayList<>();
        AgencyModel agency = new AgencyModel();
        agency.setId("a001");
        agency.setName("a001");
        agencyModels.add(agency);
        when(userDao.getAgencyByAgencyAdminId(targetId)).thenReturn(agencyModels);
        UserEntity approver = new UserEntity();
        approver.setId(fromId);
        approver.setRole(UserRole.AGENCY_ADMIN.toString());
        when(userProvider.checkUser(fromId)).thenReturn(approver);
        when(userProvider.isInSameAgency(targetId, fromId)).thenReturn(true);
        userService.mergeEmail(targetId, fromId);
    }

    /**
     * 合并agency
     * case: 申请合并的账户不是agency admin,抛异常
     */
    @Test(expected = BusinessException.class)
    public void testMergeEmail_NotAgencyAdmin() {
        String targetId = "u001";
        String fromId = "u002";
        UserEntity applicant = new UserEntity();
        applicant.setId(targetId);
        applicant.setRole(UserRole.SITE_ADMIN.toString());
        when(userProvider.checkUser(targetId)).thenReturn(applicant);
        List<AgencyModel> agencyModels = new ArrayList<>();
        AgencyModel agency = new AgencyModel();
        agency.setId("a001");
        agency.setName("a001");
        agencyModels.add(agency);
//        Mockito.when(userDao.getAgencyByAgencyAdminId(targetId)).thenReturn(agencyModels);
        UserEntity approver = new UserEntity();
        approver.setId(fromId);
        approver.setRole(UserRole.AGENCY_ADMIN.toString());
//        Mockito.when(userProvider.checkUser(fromId)).thenReturn(approver);
//        Mockito.when(userProvider.isInSameAgency(targetId, fromId)).thenReturn(false);
        userService.mergeEmail(targetId, fromId);
    }

    /**
     * 合并agency
     * case: 要合并到的账户不是agency admin,抛异常
     */
    @Test(expected = BusinessException.class)
    public void testMergeEmail_approverNotAgencyAdmin() {
        String targetId = "u001";
        String fromId = "u002";
        UserEntity applicant = new UserEntity();
        applicant.setId(targetId);
        applicant.setRole(UserRole.AGENCY_ADMIN.toString());
        when(userProvider.checkUser(targetId)).thenReturn(applicant);
        List<AgencyModel> agencyModels = new ArrayList<>();
        AgencyModel agency = new AgencyModel();
        agency.setId("a001");
        agency.setName("a001");
        agencyModels.add(agency);
        when(userDao.getAgencyByAgencyAdminId(targetId)).thenReturn(agencyModels);
        UserEntity approver = new UserEntity();
        approver.setId(fromId);
        approver.setRole(UserRole.SITE_ADMIN.toString());
        when(userProvider.checkUser(fromId)).thenReturn(approver);
//        Mockito.when(userProvider.isInSameAgency(targetId, fromId)).thenReturn(false);
        userService.mergeEmail(targetId, fromId);
    }

    @Ignore
    @Test
    public void testMergeEmail() {
        String targetId = "u001";
        String fromId = "u002";
        UserEntity applicant = new UserEntity();
        applicant.setId(targetId);
        applicant.setRole(UserRole.AGENCY_ADMIN.toString());
        when(userProvider.checkUser(targetId)).thenReturn(applicant);
        List<AgencyModel> agencyModels = new ArrayList<>();
        AgencyModel agency = new AgencyModel();
        agency.setId("a001");
        agency.setName("a001");
        agencyModels.add(agency);
        when(userDao.getAgencyByAgencyAdminId(targetId)).thenReturn(agencyModels);
        UserEntity approver = new UserEntity();
        approver.setId(fromId);
        approver.setRole(UserRole.AGENCY_ADMIN.toString());
        when(userProvider.checkUser(fromId)).thenReturn(approver);
        when(userProvider.isInSameAgency(targetId, fromId)).thenReturn(false);
        CacheModel cache = new CacheModel();
        cache.setKey("key");
        cache.setValue("value");
        when(cacheService.create(Mockito.anyString(), Mockito.anyByte())).thenReturn(cache);
        when(mandrillService.getEmailTemplate(EmailTemplate.MERGE_ACCOUNT)).thenReturn("htmlString");
        userService.mergeEmail(targetId, fromId);
        Mockito.verify(mandrillService,Mockito.times(1)).sendAsync(any(EmailModel.class));
    }
    /**
     * 根据用户的邮箱搜索到合并的用户
     * case: 找到要合并的账户以及agency,centers
     */
    @Test
    public void testMergeSearchUser(){
        String email = "testEmail";
        String userId = "001";
        UserModel user = new UserModel();
        user.setId("002");
        user.setRole(UserRole.AGENCY_OWNER.toString());
        when(userProvider.isInSameAgency(user.getId(),userId)).thenReturn(false);
        when(userDao.getUserByEmail(email)).thenReturn(user);
        //要被合并的agency
        List<AgencyModel> agencyModels = new ArrayList<>();
        AgencyModel agency = new AgencyModel();
        agency.setId("a001");
        agency.setName("a001");
        agencyModels.add(agency);
        List<CenterModel> centers = new ArrayList<>();
        CenterModel centerModel = new CenterModel();
        centerModel.setId("c001");
        centerModel.setName("c001");
        centers.add(centerModel);
        when(userDao.getAgencyByAgencyAdminId(user.getId())).thenReturn(agencyModels);
        when(userDao.getCentersInAgency(agency.getId())).thenReturn(centers);
        MergeSearchUser mergeSearchUser = userService.getMergeSearchUser(email,userId);
        assertEquals(user.getId(),mergeSearchUser.getId());
        assertEquals(centerModel.getId(),mergeSearchUser.getAgency().getCenters().get(0).getId());
    }

    /**
     * 根据别名获取agency的快照
     * zjj 2016.8.29
     * @throws InvalidProtocolBufferException
     */
    @Test
    public void testGetSnapshotByAlias() throws InvalidProtocolBufferException {
        String userId = "u001";
        String alias = "2016-2017 Fall";
        AgencyModel agency = new AgencyModel();
        agency.setId("agency001");
        agency.setName("agency001");
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        LGSnapshot.StudentRecordSnapshot studentSnapshot = LGSnapshot.StudentRecordSnapshot.newBuilder()
                .setRecordId("r001")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .build();
        LGSnapshot.ClassSnapshot classSnapshot =LGSnapshot.ClassSnapshot.newBuilder()
                .setId("sg001")
                .setName("sg001")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .addRecords(studentSnapshot)
                .build();
        LGSnapshot.CenterSnapshot centerSnapshot = LGSnapshot.CenterSnapshot.newBuilder()
                .setId("sc001")
                .setName("sc001")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .addClasses(classSnapshot)
                .build();

        LGSnapshot.Properties property  = LGSnapshot.Properties.newBuilder()
                .setName("a001")
                .setValue("avalue001")
                .setType("atype001")
                .build();
        LGSnapshot.AgencySnapshot agencySnapshot = LGSnapshot.AgencySnapshot.newBuilder()
                .setId("a000001")
                .setName("s000001")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .addCenters(centerSnapshot)
                .addProperties(property)
                .build();
        AgencySnapshotEntity snapshot = new AgencySnapshotEntity();
        snapshot.setId("snapshotId");
        snapshot.setData(agencySnapshot.toByteArray());
        when(agencyDao.getSnapshot(agency.getId(),alias)).thenReturn(snapshot);
        UserEntity user = new UserEntity();
        when(userProvider.checkUser(Mockito.anyString())).thenReturn(user);
        AgencySnapshotModel snapshotModel = userService.getSnapshotByAlias(userId, alias, null, false, false);
        assertEquals("a000001",snapshotModel.getId());
        assertEquals("snapshotId",snapshotModel.getSnapShotId());
//        Assert.assertEquals("sc001",snapshotModel.getCenters().get(0).getId());
//        Assert.assertEquals("sg001",snapshotModel.getCenters().get(0).getGroups().get(0).getId());
    }

    /* 根据用户的邮箱或则学校名搜索整个agency
    * case:搜索的结果为空,返回null
    */
    @Test
    public void getAgencyByEmailOrCenterName_SearchNUll() {
        String searchStr = "";
        List<AgencyModel> agencyModels = userService.getAgencyByEmailOrCenterName(searchStr);
        assertEquals(null,agencyModels);
    }

    /**
     * 根据用户的邮箱/或则学校名搜索整个agency
     * case:正常搜索，并返回整个agency
     */
    @Test
    public void getAgencyByEmailOrCenterName_ByEmail() {
        String searchStr = "<EMAIL>";
        UserModel user = new UserModel();
        user.setId("u001");
        user.setDisplayName("u001");
        when(userDao.getUserByEmail(searchStr)).thenReturn(user);
        AgencyModel agency = new AgencyModel();
        agency.setId("a001");
        agency.setName("a001");
        when(userProvider.getAgencyByUserId(user.getId())).thenReturn(agency);

        CenterEntity center1 = new CenterEntity();
        center1.setId("c001");
        center1.setName("c001");
        List<CenterEntity> centerEntityList = new ArrayList<>();
        centerEntityList.add(center1);
        when(centerDao.getAllByAgencyId(agency.getId())).thenReturn(centerEntityList);

        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId("g001");
        groupEntity.setName("g001");
        List<GroupEntity> groupEntityList = new GrowthList<>();
        groupEntityList.add(groupEntity);
        when(groupDao.getAllByCenterId(center1.getId())).thenReturn(groupEntityList);
        List<AgencyModel> agencyModels = userService.getAgencyByEmailOrCenterName(searchStr);
        assertEquals(1,agencyModels.size());
        assertEquals(1,agencyModels.get(0).getCenters().size());
        assertEquals(1,agencyModels.get(0).getCenters().get(0).getGroups().size());
    }

    @Test
    public void getAgencyByEmailOrCenterName_ByName() {
        String searchStr = "testName";
        AgencyModel agency = new AgencyModel();
        agency.setId("a001");
        agency.setName("a001");
        List<AgencyModel> agencyList = new ArrayList<>();
        agencyList.add(agency);
        when(agencyDao.getAgencyByCenterName(searchStr)).thenReturn(agencyList);

        CenterEntity center1 = new CenterEntity();
        center1.setId("c001");
        center1.setName("c001");
        List<CenterEntity> centerEntityList = new ArrayList<>();
        centerEntityList.add(center1);
        when(centerDao.getAllByAgencyId(agency.getId())).thenReturn(centerEntityList);

        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId("g001");
        groupEntity.setName("g001");
        List<GroupEntity> groupEntityList = new GrowthList<>();
        groupEntityList.add(groupEntity);
        when(groupDao.getAllByCenterId(center1.getId())).thenReturn(groupEntityList);
        List<AgencyModel> agencyModels = userService.getAgencyByEmailOrCenterName(searchStr);
        assertEquals(1,agencyModels.size());
        assertEquals(1,agencyModels.get(0).getCenters().size());
        assertEquals(1,agencyModels.get(0).getCenters().get(0).getGroups().size());
    }

    @Test
    public void testDeleteUserFile(){
        String userId = "u001";
        String fileId = "f001";
        userService.deleteUserFile(fileId, userId);
        Mockito.verify(userDao, Mockito.timeout(1)).deleteUserFile(fileId);
        Mockito.verify(userProvider, Mockito.timeout(1)).checkUser(userId);
    }


    /**
     * 正常根据班级id获取老师，一个有头像，一个没头像
     */
    @Test
    public void testGetTeachers_WithOneAvatarUrl() {
        String groupId = "g001";
//        String userId = "u001";
        GroupEntry group = new GroupEntry();
        group.setId("g001");
        group.setName("groupName");

        UserModel teacher1 = new UserModel();
        teacher1.setId("t001");
        teacher1.setDisplayName("wang1");
        teacher1.setAvatarMediaUrl("wang.jpg");
        teacher1.setRole("COLLABORATOR");
        UserModel teacher2 = new UserModel();
        teacher2.setId("t002");
        teacher2.setDisplayName("wang2");
        teacher2.setAvatarMediaUrl(null);
        teacher2.setRole("COLLABORATOR");

        List<UserModel> teachers = new ArrayList<>();
        teachers.add(teacher1);
        teachers.add(teacher2);

        when(userDao.getTeachers(groupId)).thenReturn(teachers);
        when(groupDao.getGroup(groupId)).thenReturn(group);
        when(fileSystem.getPublicUrl(Mockito.anyString())).thenReturn("hello");

        List<TeacherResponse> teacherResponses = userService.getTeachers(Mockito.anyString(), groupId);

        Mockito.verify(groupDao, Mockito.times(1)).getGroup(groupId);
        Mockito.verify(userDao, Mockito.times(1)).getTeachers(groupId);

        assertEquals("t001", teacherResponses.get(0).getId());
        assertEquals("wang1", teacherResponses.get(0).getName());
        assertEquals("hello", teacherResponses.get(0).getAvatarUrl());

        assertEquals("t002", teacherResponses.get(1).getId());
        assertEquals("wang2", teacherResponses.get(1).getName());
        assertEquals(null, teacherResponses.get(1).getAvatarUrl());
    }

    /**
     * 班级Id为空
     */
    @Test(expected = BusinessException.class)
    public void testGetTeachers_NoGroupId() {
        userService.getTeachers("u001", "");
    }

    /**
     * 班级Id为空
     */
    @Test(expected = BusinessException.class)
    public void testGetTeachers_nullGroupId() {
        userService.getTeachers("u001", null);
    }

    /**
     * 正常根据班级id获取老师，没有班级
     */
    @Test(expected = BusinessException.class)
    public void testGetTeachers_NoGroup() {
        String groupId = "g001";

        when(groupDao.getGroup(groupId)).thenReturn(null);

        userService.getTeachers(Mockito.anyString(), groupId);

        Mockito.verify(groupDao, Mockito.times(1)).getGroup(groupId);
        Mockito.verify(userDao, Mockito.times(1)).getTeachers(groupId);
    }

    @Test
    public void testSetImTranslationOpen() {
        AcademyOpen open = new AcademyOpen();
        open.setOpenFlag(true);
        open.setUserId("123456");
        UserEntity user = new UserEntity();
        user.setId("123456");
        user.setIsDeleted(false);
        when(userRepository.findById(open.getUserId())).thenReturn(Optional.of(user));

        userService.setImTranslationOpen(open);

        Mockito.verify(userMetaDao, Mockito.times(1)).getMetas(Mockito.anyString(), Mockito.anyString());
        Mockito.verify(userMetaDao, Mockito.times(1)).insertMeta(Mockito.anyString(), Mockito.anyString(), Mockito.anyString());
    }

    @Test
    public void testGetImTranslationOpen() {
        List<UsersMetaDataEntity> metas = new ArrayList<>();
        UsersMetaDataEntity usersMetaDataEntity = new UsersMetaDataEntity();
        usersMetaDataEntity.setMetaKey(UserMetaKey.IM_TRANSLATION_FLAG.toString());
        usersMetaDataEntity.setMetaValue("true");
        metas.add(usersMetaDataEntity);
        when(userMetaDao.getMetas(UserMetaKey.IM_TRANSLATION_FLAG.toString(), "123456")).thenReturn(metas);

        Status status = userService.getImTranslationOpen("123456");

        Mockito.verify(userMetaDao, Mockito.times(1)).getMetas(Mockito.anyString(), Mockito.anyString());
        Assert.assertTrue(status.getStatus(), true);
    }

    /**
     * 获取具有质量等级note的老师
     * note全部是Good
     *
     */
    @Test()
    public void testGetNoteHadQualityLevelTeachers_isAllGood() {
        String alias = "2018-2019";

        UserModel teacher = new UserModel();
        teacher.setId("t001");
        teacher.setDisplayName("Xiao Zhang");
        teacher.setNoteId("n001");
        teacher.setNoteMetaValue("GOOD");
        teacher.setEmail("<EMAIL>");

        List<UserModel> teachers = new ArrayList<>();
        teachers.add(teacher);

        when(userDao.getNoteHadQualityLevelTeachers(alias)).thenReturn(teachers);

        List<TeacherAdminPortalResponse> responses = userService.getNoteHadQualityLevelTeachers(alias);


        Mockito.verify(userDao, Mockito.times(1)).getNoteHadQualityLevelTeachers(alias);
        assertEquals("<EMAIL>", responses.get(0).getEmail());
        assertEquals("Xiao Zhang", responses.get(0).getDisplayName());
        assertEquals("t001", responses.get(0).getId());
        assertEquals(0, responses.get(0).getBadNoteNum());
        assertEquals(1, responses.get(0).getGoodNoteNum());
        assertEquals(1.0, responses.get(0).getFpy(),0.1);
    }

    /**
     * 获取具有质量等级note的老师
     * note一半Good
     *
     */
    @Test()
    public void testGetNoteHadQualityLevelTeachers_halfGood() {
        String alias = "2018-2019";

        UserModel teacherWithNote = new UserModel();
        teacherWithNote.setId("t001");
        teacherWithNote.setDisplayName("Xiao Zhang");
        teacherWithNote.setNoteId("n001");
        teacherWithNote.setNoteMetaValue("GOOD");
        teacherWithNote.setEmail("<EMAIL>");

        UserModel teacherWithNote1 = new UserModel();
        teacherWithNote1.setId("t001");
        teacherWithNote1.setDisplayName("Xiao Zhang");
        teacherWithNote1.setNoteId("n002");
        teacherWithNote1.setNoteMetaValue("BAD");
        teacherWithNote1.setEmail("<EMAIL>");

        List<UserModel> teachers = new ArrayList<>();
        teachers.add(teacherWithNote);
        teachers.add(teacherWithNote1);

        when(userDao.getNoteHadQualityLevelTeachers(alias)).thenReturn(teachers);

        List<TeacherAdminPortalResponse> responses = userService.getNoteHadQualityLevelTeachers(alias);


        Mockito.verify(userDao, Mockito.times(1)).getNoteHadQualityLevelTeachers(alias);
        assertEquals("<EMAIL>", responses.get(0).getEmail());
        assertEquals("Xiao Zhang", responses.get(0).getDisplayName());
        assertEquals("t001", responses.get(0).getId());
        assertEquals(1, responses.get(0).getBadNoteNum());
        assertEquals(1, responses.get(0).getGoodNoteNum());
        assertEquals(0.5, responses.get(0).getFpy(),0.1);
    }

    /**
     * 获取具有质量等级note的老师
     * note全部是Bad
     *
     */
    @Test()
    public void testGetNoteHadQualityLevelTeachers_AllBad() {
        String alias = "2018-2019";

        UserModel teacherWithNote = new UserModel();
        teacherWithNote.setId("t001");
        teacherWithNote.setDisplayName("Xiao Zhang");
        teacherWithNote.setNoteId("n001");
        teacherWithNote.setNoteMetaValue("BAD");
        teacherWithNote.setEmail("<EMAIL>");

        UserModel teacherWithNote1 = new UserModel();
        teacherWithNote1.setId("t001");
        teacherWithNote1.setDisplayName("Xiao Zhang");
        teacherWithNote1.setNoteId("n002");
        teacherWithNote1.setNoteMetaValue("BAD");
        teacherWithNote1.setEmail("<EMAIL>");

        List<UserModel> teachers = new ArrayList<>();
        teachers.add(teacherWithNote);
        teachers.add(teacherWithNote1);

        when(userDao.getNoteHadQualityLevelTeachers(alias)).thenReturn(teachers);

        List<TeacherAdminPortalResponse> responses = userService.getNoteHadQualityLevelTeachers(alias);


        Mockito.verify(userDao, Mockito.times(1)).getNoteHadQualityLevelTeachers(alias);
        assertEquals("<EMAIL>", responses.get(0).getEmail());
        assertEquals("Xiao Zhang", responses.get(0).getDisplayName());
        assertEquals("t001", responses.get(0).getId());
        assertEquals(2, responses.get(0).getBadNoteNum());
        assertEquals(0, responses.get(0).getGoodNoteNum());
        assertEquals(0, responses.get(0).getFpy(),0.1);
    }

    /**
     * 测试获取用户需要引导的功能
     * case: 所有的功能都没有使用的情况
     */

    @Test
    public void testGetUserNeedGuideFeatures1() {
        // 数据准备
        String userId = "user01";
        String agencyId = "agencyId01";
        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setUsername(userId);
        currentUser.setRole("Agency Owner");
        currentUser.setAgencyId(agencyId);
        UserEntity user = new UserEntity();
        // userMetadata 实体
        List<UsersMetaDataEntity> allUserMeta = new ArrayList<>();
        // 功能开关 Metadata key
        String[] featureOpenKeys = {
                "CURRICULUM",
                "WEEKLY_PLAN",
                "UNIT_PLANNER",
                "ADAPT_UDL_AND_CLR",
                "WEEKLY_CREATE_AI_LESSON",
                "IS_IT_ACTIVATED_UNIT_PLANNER",
                "LEARNING_STORY_NOTE_OPEN",
                "PORTFOLIO_OPEN",
                "PRIVATE_CHAT_SETTING_OPEN",
                "STAFF_PRIVATE_CHAT_SETTING_OPEN",
                "PARENT_PRIVATE_CHAT_SETTING_OPEN",
                "PARENT_RESOURCES_OPEN"
        };
         List<String> openKeyList = Arrays.asList(featureOpenKeys);

        // 数据模拟
        when(userProvider.getCurrentUser()).thenReturn(currentUser);
        when(userProvider.checkUser(userId)).thenReturn(user);
        lenient().when(userMetaDao.getMetasByUserId(anyList(), eq(userId))).thenReturn(allUserMeta);
        when(agencyDao.getMetas(agencyId, openKeyList)).thenReturn(new ArrayList<>());

        // 调用
        UserNeedGuideFeaturesResponse needGuideFeaturesResponse = userService.getUserNeedGuideFeatures();

        // 验证
        assertEquals(true, needGuideFeaturesResponse.isHomePageGuide());
        assertEquals(true, needGuideFeaturesResponse.isEngagementGuide());
        assertEquals(true, needGuideFeaturesResponse.isPortfolioGuide());
        assertEquals(true, needGuideFeaturesResponse.isRateGuide());
        assertEquals(true, needGuideFeaturesResponse.isFamilyEngagementGuide());
        assertEquals(true, needGuideFeaturesResponse.isAssessmentProgressGuide());
        assertEquals(true, needGuideFeaturesResponse.isEventGuide());
        assertEquals(true, needGuideFeaturesResponse.isWeeklyPlanGuide());
        assertEquals(true, needGuideFeaturesResponse.isSurveyGuide());
        assertEquals(true, needGuideFeaturesResponse.isSurveyAddGuide());
        assertEquals(true, needGuideFeaturesResponse.isBatchFillInVHCCenterTip());
        assertEquals(true, needGuideFeaturesResponse.isBatchFillInVHCGroupTip());
        assertEquals(true, needGuideFeaturesResponse.isNewNavDialog());
        assertEquals(false, needGuideFeaturesResponse.isCurriculumTip());
        assertEquals(true, needGuideFeaturesResponse.isActionPlanGuide());
    }

    /**
     * 测试获取用户需要引导的功能
     * case: 所有的功能引导都已触发的情况
     */

    @Test
    public void testGetUserNeedGuideFeatures2() {
        // 数据准备
        String userId = "user01";
        String agencyId = "agencyId01";
        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setUsername(userId);
        currentUser.setAgencyId(agencyId);
        UserEntity user = new UserEntity();
        user.setRole("agency owner");

        // userMetadata 实体
        List<UsersMetaDataEntity> allUserMeta = new ArrayList<>();
        UsersMetaDataEntity homePageGuideUserMeta = new UsersMetaDataEntity();
        homePageGuideUserMeta.setUserId(userId);
        homePageGuideUserMeta.setMetaValue("true");
        homePageGuideUserMeta.setMetaKey("HOME_PAGE_GUIDE");
        allUserMeta.add(homePageGuideUserMeta);
        UsersMetaDataEntity engagementGuideUserMeta = new UsersMetaDataEntity();
        engagementGuideUserMeta.setUserId(userId);
        engagementGuideUserMeta.setMetaValue("true");
        engagementGuideUserMeta.setMetaKey("ENGAGEMENT_GUIDE");
        allUserMeta.add(engagementGuideUserMeta);
        UsersMetaDataEntity rateGuideUserMeta = new UsersMetaDataEntity();
        rateGuideUserMeta.setUserId(userId);
        rateGuideUserMeta.setMetaValue("true");
        rateGuideUserMeta.setMetaKey("RATE_GUIDE");
        allUserMeta.add(rateGuideUserMeta);
        UsersMetaDataEntity portfolioGuideUserMeta = new UsersMetaDataEntity();
        portfolioGuideUserMeta.setUserId(userId);
        portfolioGuideUserMeta.setMetaValue("true");
        portfolioGuideUserMeta.setMetaKey("PORTFOLIO_GUIDE");
        allUserMeta.add(portfolioGuideUserMeta);
        UsersMetaDataEntity FEGuideUserMeta = new UsersMetaDataEntity();
        FEGuideUserMeta.setUserId(userId);
        FEGuideUserMeta.setMetaValue("true");
        FEGuideUserMeta.setMetaKey("FAMILY_ENGAGEMENT_GUIDE");
        allUserMeta.add(FEGuideUserMeta);
        UsersMetaDataEntity APGuideUserMeta = new UsersMetaDataEntity();
        APGuideUserMeta.setUserId(userId);
        APGuideUserMeta.setMetaValue("true");
        APGuideUserMeta.setMetaKey("ASSESSMENT_PROGRESS_GUIDE");
        allUserMeta.add(APGuideUserMeta);
        UsersMetaDataEntity EventGuideUserMeta = new UsersMetaDataEntity();
        EventGuideUserMeta.setUserId(userId);
        EventGuideUserMeta.setMetaValue("true");
        EventGuideUserMeta.setMetaKey("EVENT_GUIDE");
        allUserMeta.add(EventGuideUserMeta);
        UsersMetaDataEntity VHCCenterGuideUserMeta = new UsersMetaDataEntity();
        VHCCenterGuideUserMeta.setUserId(userId);
        VHCCenterGuideUserMeta.setMetaValue("true");
        VHCCenterGuideUserMeta.setMetaKey("BATCH_FILL_IN_VHC_CENTER_TIPS");
        allUserMeta.add(VHCCenterGuideUserMeta);
        UsersMetaDataEntity VHCGroupGuideUserMeta = new UsersMetaDataEntity();
        VHCGroupGuideUserMeta.setUserId(userId);
        VHCGroupGuideUserMeta.setMetaValue("true");
        VHCGroupGuideUserMeta.setMetaKey("BATCH_FILL_IN_VHC_GROUP_TIPS");
        allUserMeta.add(VHCGroupGuideUserMeta);
        UsersMetaDataEntity surveyAddGuideUserMeta = new UsersMetaDataEntity();
        surveyAddGuideUserMeta.setUserId(userId);
        surveyAddGuideUserMeta.setMetaValue("true");
        surveyAddGuideUserMeta.setMetaKey("SURVEY_ADD_GUIDE");
        allUserMeta.add(surveyAddGuideUserMeta);
        UsersMetaDataEntity surveyGuideUserMeta = new UsersMetaDataEntity();
        surveyGuideUserMeta.setUserId(userId);
        surveyGuideUserMeta.setMetaValue("true");
        surveyGuideUserMeta.setMetaKey("SURVEY_GUIDE");
        allUserMeta.add(surveyGuideUserMeta);
        UsersMetaDataEntity curriculumTipUserMeta = new UsersMetaDataEntity();
        curriculumTipUserMeta.setUserId(userId);
        curriculumTipUserMeta.setMetaValue("true");
        curriculumTipUserMeta.setMetaKey("CURRICULUM_TIP");
        allUserMeta.add(curriculumTipUserMeta);
        UsersMetaDataEntity newNavUserMeta = new UsersMetaDataEntity();
        newNavUserMeta.setUserId(userId);
        newNavUserMeta.setMetaValue("true");
        newNavUserMeta.setMetaKey("NEW_NAV");
        allUserMeta.add(newNavUserMeta);
        UsersMetaDataEntity weeklyPlanGuideUserMeta = new UsersMetaDataEntity();
        weeklyPlanGuideUserMeta.setUserId(userId);
        weeklyPlanGuideUserMeta.setMetaValue("true");
        weeklyPlanGuideUserMeta.setMetaKey("WEEKLY_PLAN_GUIDE");
        allUserMeta.add(weeklyPlanGuideUserMeta);
        UsersMetaDataEntity unreadMessageEmailGuide = new UsersMetaDataEntity();
        unreadMessageEmailGuide.setUserId(userId);
        unreadMessageEmailGuide.setMetaValue("true");
        unreadMessageEmailGuide.setMetaKey("UNREAD_MESSAGE_EMAIL_GUIDE");
        allUserMeta.add(unreadMessageEmailGuide);
        UsersMetaDataEntity unitPlannerGuide = new UsersMetaDataEntity();
        unitPlannerGuide.setUserId(userId);
        unitPlannerGuide.setMetaValue("true");
        unitPlannerGuide.setMetaKey("UNIT_PLANNER_GUIDE");
        allUserMeta.add(unitPlannerGuide);
        UsersMetaDataEntity newUnitPlannerGuide = new UsersMetaDataEntity();
        newUnitPlannerGuide.setUserId(userId);
        newUnitPlannerGuide.setMetaValue("true");
        newUnitPlannerGuide.setMetaKey("NEW_UNIT_PLANNER_GUIDE");
        allUserMeta.add(newUnitPlannerGuide);
        UsersMetaDataEntity teacherPlannersettingGuide = new UsersMetaDataEntity();
        teacherPlannersettingGuide.setUserId(userId);
        teacherPlannersettingGuide.setMetaValue("true");
        teacherPlannersettingGuide.setMetaKey("TEACHER_CREATE_UNIT_PLANNER_GUIDE");
        allUserMeta.add(teacherPlannersettingGuide);
        UsersMetaDataEntity weeklyPlannersettingGuide = new UsersMetaDataEntity();
        weeklyPlannersettingGuide.setUserId(userId);
        weeklyPlannersettingGuide.setMetaValue("true");
        weeklyPlannersettingGuide.setMetaKey("WEEKLY_PLANNER_SETTING_GUIDE");
        allUserMeta.add(weeklyPlannersettingGuide);
        UsersMetaDataEntity adapUdlAndClrGuide = new UsersMetaDataEntity();
        adapUdlAndClrGuide.setUserId(userId);
        adapUdlAndClrGuide.setMetaValue("true");
        adapUdlAndClrGuide.setMetaKey("ADAPT_UDL_AND_CLR_GUIDE");
        allUserMeta.add(adapUdlAndClrGuide);
        UsersMetaDataEntity schoolReadinessMeasureReportGuide = new UsersMetaDataEntity();
        schoolReadinessMeasureReportGuide.setUserId(userId);
        schoolReadinessMeasureReportGuide.setMetaValue("true");
        schoolReadinessMeasureReportGuide.setMetaKey("SCHOOL_READINESS_MEASURE_REPORT_GUIDE");
        allUserMeta.add(schoolReadinessMeasureReportGuide);
        UsersMetaDataEntity showBatchAdaptLessonsGuide = new UsersMetaDataEntity();
        showBatchAdaptLessonsGuide.setUserId(userId);
        showBatchAdaptLessonsGuide.setMetaValue("true");
        showBatchAdaptLessonsGuide.setMetaKey("SHOW_BATCH_ADAPT_GUIDE");
        allUserMeta.add(showBatchAdaptLessonsGuide);
        UsersMetaDataEntity actionPlanGuide = new UsersMetaDataEntity();
        actionPlanGuide.setUserId(userId);
        actionPlanGuide.setMetaValue("true");
        actionPlanGuide.setMetaKey("ACTION_PLAN_GUIDE");
        allUserMeta.add(actionPlanGuide);
        UsersMetaDataEntity batchGenerateLessonTip = new UsersMetaDataEntity();
        batchGenerateLessonTip.setUserId(userId);
        batchGenerateLessonTip.setMetaValue("true");
        batchGenerateLessonTip.setMetaKey("SHOW_BATCH_GENERATE_LESSON_TIP");
        allUserMeta.add(batchGenerateLessonTip);
        UsersMetaDataEntity batchGenerateLessonExitTip = new UsersMetaDataEntity();
        batchGenerateLessonExitTip.setUserId(userId);
        batchGenerateLessonExitTip.setMetaValue("true");
        batchGenerateLessonExitTip.setMetaKey("SHOW_BATCH_GENERATE_LESSON_EXIT_TIP");
        allUserMeta.add(batchGenerateLessonExitTip);
        UsersMetaDataEntity simpleUnitGuide = new UsersMetaDataEntity();
        simpleUnitGuide.setUserId(userId);
        simpleUnitGuide.setMetaValue("true");
        simpleUnitGuide.setMetaKey("SHOW_SAMPLE_UNIT_GUIDE");
        allUserMeta.add(simpleUnitGuide);
        UsersMetaDataEntity unitProgress80Tip = new UsersMetaDataEntity();
        unitProgress80Tip.setUserId(userId);
        unitProgress80Tip.setMetaValue("true");
        unitProgress80Tip.setMetaKey("SHOW_UNIT_PROGRESS_80_TIP");
        allUserMeta.add(unitProgress80Tip);
        UsersMetaDataEntity previewAndAdaptGuide = new UsersMetaDataEntity();
        previewAndAdaptGuide.setUserId(userId);
        previewAndAdaptGuide.setMetaValue("true");
        previewAndAdaptGuide.setMetaKey("SHOW_PREVIEW_PLAN_DETAIL_ADAPT_GUIDE");
        allUserMeta.add(previewAndAdaptGuide);
        UsersMetaDataEntity unitPlannerAndAdaptGuide = new UsersMetaDataEntity();
        unitPlannerAndAdaptGuide.setUserId(userId);
        unitPlannerAndAdaptGuide.setMetaValue("true");
        unitPlannerAndAdaptGuide.setMetaKey("SHOW_UNIT_PLANNER_AND_ADAPT_GUIDE");
        allUserMeta.add(unitPlannerAndAdaptGuide);
        UsersMetaDataEntity batchAdaptNewTagGuide = new UsersMetaDataEntity();
        batchAdaptNewTagGuide.setUserId(userId);
        batchAdaptNewTagGuide.setMetaValue("true");
        batchAdaptNewTagGuide.setMetaKey("BATCH_ADAPTED_NEW_TAG_GUIDE");
        allUserMeta.add(batchAdaptNewTagGuide);

        UsersMetaDataEntity learningStoryGuide = new UsersMetaDataEntity();
        learningStoryGuide.setUserId(userId);
        learningStoryGuide.setMetaValue("true");
        learningStoryGuide.setMetaKey("LEARNING_STORY_HOME_PAGE_GUIDE");
        allUserMeta.add(learningStoryGuide);

        UsersMetaDataEntity deiBestPracticeGuide = new UsersMetaDataEntity();
        deiBestPracticeGuide.setUserId(userId);
        deiBestPracticeGuide.setMetaValue("true");
        deiBestPracticeGuide.setMetaKey("DEI_BEST_PRACTICE_NEW_TAG");
        allUserMeta.add(deiBestPracticeGuide);

        // 功能开关 Metadata key
        String[] featureOpenKeys = {
                "CURRICULUM",
                "WEEKLY_PLAN",
                "UNIT_PLANNER",
                "ADAPT_UDL_AND_CLR",
                "WEEKLY_CREATE_AI_LESSON",
                "IS_IT_ACTIVATED_UNIT_PLANNER",
                "LEARNING_STORY_NOTE_OPEN",
                "PORTFOLIO_OPEN",
                "PRIVATE_CHAT_SETTING_OPEN",
                "STAFF_PRIVATE_CHAT_SETTING_OPEN",
                "PARENT_PRIVATE_CHAT_SETTING_OPEN",
                "PARENT_RESOURCES_OPEN"
        };
        List<String> openKeyList = Arrays.asList(featureOpenKeys);

        // 数据模拟
        when(userProvider.getCurrentUser()).thenReturn(currentUser);
        when(userProvider.checkUser(userId)).thenReturn(user);
        when(userMetaDao.getMetasByUserId(anyList(), eq(userId))).thenReturn(allUserMeta);
        when(agencyDao.getMetas(agencyId, openKeyList)).thenReturn(new ArrayList<>());

        // 调用
        UserNeedGuideFeaturesResponse needGuideFeaturesResponse = userService.getUserNeedGuideFeatures();

        // 验证
        assertEquals(false, needGuideFeaturesResponse.isHomePageGuide());
        assertEquals(false, needGuideFeaturesResponse.isEngagementGuide());
        assertEquals(false, needGuideFeaturesResponse.isPortfolioGuide());
        assertEquals(false, needGuideFeaturesResponse.isRateGuide());
        assertEquals(false, needGuideFeaturesResponse.isFamilyEngagementGuide());
        assertEquals(false, needGuideFeaturesResponse.isAssessmentProgressGuide());
        assertEquals(false, needGuideFeaturesResponse.isEventGuide());
        assertEquals(false, needGuideFeaturesResponse.isWeeklyPlanGuide());
        assertEquals(false, needGuideFeaturesResponse.isSurveyGuide());
        assertEquals(false, needGuideFeaturesResponse.isSurveyAddGuide());
        assertEquals(false, needGuideFeaturesResponse.isBatchFillInVHCCenterTip());
        assertEquals(false, needGuideFeaturesResponse.isBatchFillInVHCGroupTip());
        assertEquals(false, needGuideFeaturesResponse.isNewNavDialog());
        assertEquals(false, needGuideFeaturesResponse.isCurriculumTip());
    }

    /**
     * 测试获取用户需要引导的功能
     * case: 部分功能引导被触发的情况
     */

    @Test
    public void testGetUserNeedGuideFeatures() {
        // 数据准备
        String userId = "user01";
        String agencyId = "agencyId01";
        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setUsername(userId);
        currentUser.setAgencyId(agencyId);
        UserEntity user = new UserEntity();
        user.setRole("agency owner");
        // userMetadata 实体
        List<UsersMetaDataEntity> allUserMeta = new ArrayList<>();
        UsersMetaDataEntity userMeta = new UsersMetaDataEntity();
        userMeta.setUserId(userId);
        userMeta.setMetaValue("true");
        userMeta.setMetaKey("HOME_PAGE_GUIDE");
        allUserMeta.add(userMeta);

        // 数据模拟
        when(userProvider.getCurrentUser()).thenReturn(currentUser);
        when(userProvider.checkUser(userId)).thenReturn(user);
        when(userMetaDao.getMetasByUserId(anyList(), eq(userId))).thenReturn(allUserMeta);

        // 调用
        UserNeedGuideFeaturesResponse needGuideFeaturesResponse = userService.getUserNeedGuideFeatures();

        // 验证
        assertEquals(false, needGuideFeaturesResponse.isHomePageGuide());
        assertEquals(true, needGuideFeaturesResponse.isEngagementGuide());
        assertEquals(true, needGuideFeaturesResponse.isPortfolioGuide());
        assertEquals(true, needGuideFeaturesResponse.isRateGuide());
        assertEquals(true, needGuideFeaturesResponse.isFamilyEngagementGuide());
        assertEquals(true,  needGuideFeaturesResponse.isAssessmentProgressGuide());
        assertEquals(true, needGuideFeaturesResponse.isEventGuide());
        assertEquals(true, needGuideFeaturesResponse.isWeeklyPlanGuide());
    }

    /**
     * 测试 Unit Planner 激活后功能提示弹窗显示
     */
    @Test
    public void testUnitPlannerActive() {
        // 数据准备
        String userId = "user01";
        String agencyId = "agencyId01";
        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setUsername(userId);
        currentUser.setAgencyId(agencyId);
        UserEntity user = new UserEntity();
        user.setRole("agency owner");
        // userMetadata 实体
        List<UsersMetaDataEntity> allUserMeta = new ArrayList<>();
        UsersMetaDataEntity userMeta = new UsersMetaDataEntity();
        userMeta.setUserId(userId);
        userMeta.setMetaValue("true");
        userMeta.setMetaKey("HOME_PAGE_GUIDE");
        allUserMeta.add(userMeta);

        // 功能开关 Metadata key
        String[] featureOpenKeys = {
                "CURRICULUM",
                "WEEKLY_PLAN",
                "UNIT_PLANNER",
                "ADAPT_UDL_AND_CLR",
                "WEEKLY_CREATE_AI_LESSON",
                "IS_IT_ACTIVATED_UNIT_PLANNER",
                "LEARNING_STORY_NOTE_OPEN",
                "PORTFOLIO_OPEN",
                "PRIVATE_CHAT_SETTING_OPEN",
                "STAFF_PRIVATE_CHAT_SETTING_OPEN",
                "PARENT_PRIVATE_CHAT_SETTING_OPEN",
                "PARENT_RESOURCES_OPEN"
        };
        List<String> openKeyList = Arrays.asList(featureOpenKeys);

        // 模拟机构为付费机构，且已开通了 Unit Planner 功能
        List<AgencyMetaDataEntity> agencyMetaDataEntities = new ArrayList<>();
        AgencyMetaDataEntity payAgencyMetaDataEntity = new AgencyMetaDataEntity();
        payAgencyMetaDataEntity.setMetaKey("IS_IT_ACTIVATED_UNIT_PLANNER");
        payAgencyMetaDataEntity.setMetaValue("true");
        agencyMetaDataEntities.add(payAgencyMetaDataEntity);
        AgencyMetaDataEntity unitPlanerOpen = new AgencyMetaDataEntity();
        unitPlanerOpen.setMetaKey("UNIT_PLANNER");
        unitPlanerOpen.setMetaValue("true");
        agencyMetaDataEntities.add(unitPlanerOpen);

        // 数据模拟
        when(userProvider.getCurrentUser()).thenReturn(currentUser);
        when(userProvider.checkUser(userId)).thenReturn(user);
        lenient().when(userMetaDao.getMetasByUserId(anyList(), eq(userId))).thenReturn(allUserMeta);
        when(agencyDao.getMetas(agencyId, openKeyList)).thenReturn(agencyMetaDataEntities);

        // 调用
        UserNeedGuideFeaturesResponse needGuideFeaturesResponse = userService.getUserNeedGuideFeatures();

        // 验证应该显示 Unit Planner 激活后的引导弹窗
        assertEquals(true, needGuideFeaturesResponse.isShowTeacherAndPrincipalUseUnitPlannerGuide());
    }

    @Test
    public void testGetStatistics() {
        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setAgencyId("001");
        currentUser.setRole("AGENCY_ADMIN");

        UserStatistics userStatistics = new UserStatistics();
        int count = 2;

        when(userProvider.getCurrentUser()).thenReturn(currentUser);
        when(centerDao.getCentersByAgencyId(currentUser.getAgencyId())).thenReturn(new ArrayList<>());
        when(userDao.getStatisticsByCenterIds(any())).thenReturn(userStatistics);
        when(userDao.getCountDetachedTeachers(any())).thenReturn(count);

        GetStatisticsResponse statistics = userService.getStatistics("", true);

        assertEquals(userStatistics.getSiteCount(), statistics.getSiteCount());
        assertEquals(userStatistics.getClassCount(), statistics.getClassCount());
        assertEquals(userStatistics.getTeacherCount() + count, statistics.getTeacherCount());
        assertEquals(userStatistics.getStudentCount(), statistics.getStudentCount());
    }

    @Test
    public void testGetStatistics2() {
        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setUsername("001");
        currentUser.setRole("SITE_ADMIN");

        UserStatistics userStatistics = new UserStatistics();
        int count = 2;

        when(userProvider.getCurrentUser()).thenReturn(currentUser);
        when(centerDao.getBySiteAdminId(currentUser.getId())).thenReturn(new ArrayList<>());
        when(userDao.getStatisticsByCenterIds(any())).thenReturn(userStatistics);
        when(userDao.getCountDetachedTeachers(any())).thenReturn(count);

        GetStatisticsResponse statistics = userService.getStatistics("", true);

        assertEquals(userStatistics.getSiteCount(), statistics.getSiteCount());
        assertEquals(userStatistics.getClassCount(), statistics.getClassCount());
        assertEquals(userStatistics.getTeacherCount() + count, statistics.getTeacherCount());
        assertEquals(userStatistics.getStudentCount(), statistics.getStudentCount());
    }

    @Test
    public void testGetStatistics3() {
        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setAgencyId("001");
        currentUser.setRole("AGENCY_ADMIN");
        currentUser.setType("GRANTEE");

        UserStatistics userStatistics = new UserStatistics();
        int count = 2;

        when(userProvider.getCurrentUser()).thenReturn(currentUser);
        when(centerDao.getCentersByAgencyId(currentUser.getAgencyId())).thenReturn(new ArrayList<>());
        when(userDao.getStatisticsByCenterIds(any())).thenReturn(userStatistics);
        when(userDao.getCountDetachedTeachers(any())).thenReturn(count);

        GetStatisticsResponse statistics = userService.getStatistics("001", true);

        assertEquals(userStatistics.getSiteCount(), statistics.getSiteCount());
        assertEquals(userStatistics.getClassCount(), statistics.getClassCount());
        assertEquals(userStatistics.getTeacherCount() + count, statistics.getTeacherCount());
        assertEquals(userStatistics.getStudentCount(), statistics.getStudentCount());
    }

    @Test
    public void testGetStatistics4() {
        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setAgencyId("001");
        currentUser.setRole("AGENCY_ADMIN");
        currentUser.setType("GRANTEE");

        List<AgencyIdentifierEntity> identifiers = new ArrayList<>();
        AgencyIdentifierEntity identifier = new AgencyIdentifierEntity();
        identifier.setId("001");
        identifiers.add(identifier);
        List<EnrollmentEntity> enrollment = new ArrayList<>();
        EnrollmentEntity enrollmentEntity = new EnrollmentEntity();
        enrollmentEntity.setId("001");
        enrollment.add(enrollmentEntity);
        List<CenterEntity> centers = new ArrayList<>();
        List<com.learninggenie.common.data.entity.GroupEntity> groups = new ArrayList<>();
        List<EnrollmentEntity> haveParentChildren = new ArrayList<>();
        List<com.learninggenie.common.data.entity.UserEntity> teachers = new ArrayList<>();

        when(userProvider.getCurrentUser()).thenReturn(currentUser);
        when(agencyDao.getIdentifierByUserId(any(),any())).thenReturn(identifiers);
        when(studentDao.getEnrollmentByAgencyAndIdentifier(any(),any())).thenReturn(enrollment);
        when(groupDao.getGroupByChildIds(any())).thenReturn(groups);
        when(centerDao.getCentersByChildIds(any())).thenReturn(centers);
        when(userDao.getTeachersByStudentIds(any())).thenReturn(teachers);
        GetStatisticsResponse statistics = userService.getStatistics("002", false);

        assertEquals(centers.size(), statistics.getSiteCount());
        assertEquals(groups.size(), statistics.getClassCount());
        assertEquals(teachers.size(), statistics.getTeacherCount());
        assertEquals(enrollment.size(), statistics.getStudentCount());
    }

    /**
     * 测试设置用户不再进行创建周计划功能引导
     */
    @Test
    public void testHideGuide() {

        // 准备数据
        final String userId = "001";
        List<String> features = new ArrayList<>();
        features.add("WEEKLY_PLAN_GUIDE");
        HideGuideRequest hideGuideRequest = new HideGuideRequest();
        hideGuideRequest.setFeatures(features);

        // 数据模拟
        when(userProvider.getCurrentUserId()).thenReturn(userId);

        // 接口模拟
        userService.hideGuide(hideGuideRequest);

        // 结果校验
        Mockito.verify(userMetaDao, Mockito.times(1)).setMeta(userId, "WEEKLY_PLAN_GUIDE", "true");
    }

    /**
     * 测试设置用户不再进行 Unit Planner 功能引导
     */
    @Test
    public void testHideUnitPlannerGuide() {

        // 准备数据
        final String userId = "001";
        List<String> features = new ArrayList<>();
        features.add("UNIT_PLANNER_GUIDE");
        HideGuideRequest hideGuideRequest = new HideGuideRequest();
        hideGuideRequest.setFeatures(features);

        // 数据模拟
        when(userProvider.getCurrentUserId()).thenReturn(userId);

        // 接口模拟
        userService.hideGuide(hideGuideRequest);

        // 结果校验
        Mockito.verify(userMetaDao, Mockito.times(1)).setMeta(userId, "UNIT_PLANNER_GUIDE", "true");
    }


    /**
     * 测试设置用户不再进行老师创建 Unit Planner 功能引导
     */
    @Test
    public void testHideTeacherCreateUnitPlannerGuide() {

        // 准备数据
        final String userId = "001";
        List<String> features = new ArrayList<>();
        features.add("TEACHER_CREATE_UNIT_PLANNER_GUIDE");
        HideGuideRequest hideGuideRequest = new HideGuideRequest();
        hideGuideRequest.setFeatures(features);

        // 数据模拟
        when(userProvider.getCurrentUserId()).thenReturn(userId);

        // 接口模拟
        userService.hideGuide(hideGuideRequest);

        // 结果校验
        Mockito.verify(userMetaDao, Mockito.times(1)).setMeta(userId, "TEACHER_CREATE_UNIT_PLANNER_GUIDE", "true");
    }

    /*
     * 测试获取所有的员工
     * case: 不包括机构拥有者
     */
    @Test
    public void testFilterStaffCase1() {
        // 数据准备
        String requestUserId = "U00001";
        List<UserModel> allStaffs = new ArrayList<>();
        UserModel staff = new UserModel();
        staff.setId("U00002");
        staff.setRole("SITE_ADMIN");
        UserModel owner = new UserModel();
        owner.setRole("AGENCY_OWNER");
        owner.setId("U00003");
        allStaffs.add(staff);
        allStaffs.add(owner);

        // 接口模拟
        userService.filterStaff(requestUserId, allStaffs, false);

        // 结果校验
        assertEquals(2, allStaffs.size());
    }

    /**
     * 测试获取所有的员工
     * case: 包括机构拥有者
     */
    @Test
    public void testFilterStaffCase2() {
        // 数据准备
        String requestUserId = "U00004";
        List<UserModel> allStaffs = new ArrayList<>();
        UserModel staff = new UserModel();
        staff.setId("U00005");
        staff.setRole("SITE_ADMIN");
        UserModel owner = new UserModel();
        owner.setRole("AGENCY_OWNER");
        owner.setId("U00006");
        allStaffs.add(staff);
        allStaffs.add(owner);

        // 接口模拟
        userService.filterStaff(requestUserId, allStaffs, true);

        // 结果校验
        assertEquals(2, allStaffs.size());
    }

    /**
     * 测试设置未读消息邮件通知开关
     * case: 打开未读消息邮件通知开关
     */
    @Test
    public void testSetUnreadMessageOpen() {
        // 准备数据
        String userId = "userId001";
        boolean open = true;
        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setUsername(userId);

        // 数据模拟
        when(userProvider.getCurrentUser()).thenReturn(currentUser);

        // 接口模拟
        SuccessResponse successResponse = userService.setUnreadMessageEmailOpen(open);

        // 验证
        verify(userMetaDao, times(1)).setMeta(userId, "UNREAD_MESSAGE_EMAIL_OPEN", String.valueOf(open));

        // 断言
        assertEquals(true, successResponse.isSuccess());
    }

    /**
     * 测试设置未读消息邮件通知开关
     * case: 关闭未读消息邮件通知开关
     */
    @Test
    public void testSetUnreadMessageOpenForClose() {
        // 准备数据
        String userId = "userId001";
        boolean open = false;
        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setUsername(userId);

        // 数据模拟
        when(userProvider.getCurrentUser()).thenReturn(currentUser);

        // 接口模拟
        SuccessResponse successResponse = userService.setUnreadMessageEmailOpen(open);

        // 验证
        verify(userMetaDao, times(1)).setMeta(userId, "UNREAD_MESSAGE_EMAIL_OPEN", String.valueOf(open));

        // 断言
        assertEquals(true, successResponse.isSuccess());
    }

    /**
     * 测试导出机构用户列表，参数都为空的情况
     */
    @Test(expected = BusinessException.class)
    public void testExportAgencyUsersParamEmpty() throws IOException {
        // 请求信息
        ExportAgencyUsersRequest request = new ExportAgencyUsersRequest();
        // 导出用户
        userService.exportAgencyUsers(request);
    }


    /**
     * 测试导出机构用户列表，邮箱为空的情况
     */
    @Test(expected = BusinessException.class)
    public void testExportAgencyUsersEmailsEmpty() throws IOException {
        // 要导出的用户角色
        List<String> exportRoles = new ArrayList<>();
        exportRoles.add(Role.AGENCY_OWNER.getDisplayName()); // 机构 Owner
        // 请求信息
        ExportAgencyUsersRequest request = new ExportAgencyUsersRequest();
        request.setRoles(exportRoles);
        request.setAdminEmails(",");
        // 导出用户
        userService.exportAgencyUsers(request);
    }

    /**
     * 测试导出机构用户列表，未查到用户的情况
     */
    @Test
    public void testExportAgencyUsersNoUsers() throws IOException {
        // 要导出的机构中管理员邮箱
        String adminEmailsString = "<EMAIL>";
        // 要导出的用户角色
        List<String> exportRoles = new ArrayList<>();
        exportRoles.add(Role.AGENCY_OWNER.getDisplayName()); // 机构 Owner

        // 请求信息
        ExportAgencyUsersRequest request = new ExportAgencyUsersRequest();
        request.setAdminEmails(adminEmailsString); // 要导出机构中管理员邮箱
        request.setRoles(exportRoles); // 要导出的用户角色

        // 模拟查询机构为空
        when(agencyDao.getAgenciesByAdminEmails(any())).thenReturn(new ArrayList<>());

        // 导出用户
        ExportAgencyUsersResponse response = userService.exportAgencyUsers(request);

        // 验证结果
        Assert.assertNull(response.getExcelUrl()); // 没有生成文件
        assertEquals(1, response.getErrorEmails().size()); // 一个错误邮箱
    }

    /**
     * 测试导出机构用户列表，正常的情况
     */
    @Test
    public void testExportAgencyUsers() throws IOException {
        // 要导出的机构中管理员邮箱
        String adminEmailsString = "<EMAIL>,<EMAIL>\<EMAIL>";
        // 要导出的用户角色
        List<String> exportRoles = new ArrayList<>();
        exportRoles.add(Role.AGENCY_OWNER.toString()); // 机构 Owner
        exportRoles.add(Role.AGENCY_ADMIN.toString()); // 机构管理员
        exportRoles.add(Role.SITE_ADMIN.toString()); // 园长
        exportRoles.add(Role.COLLABORATOR.toString()); // 老师
        exportRoles.add(Role.PARENT.toString()); // 家长

        // 请求信息
        ExportAgencyUsersRequest request = new ExportAgencyUsersRequest();
        request.setAdminEmails(adminEmailsString); // 要导出机构中管理员邮箱
        request.setRoles(exportRoles); // 要导出的用户角色

        // 模拟机构信息
        AgencyEntity agency = new AgencyEntity();
        agency.setId(UUID.randomUUID().toString()); // 机构 ID
        agency.setName("Test Export Agency"); // 机构名称
        List<AgencyEntity> agencies = new ArrayList<>();
        agencies.add(agency);
        // 模拟根据管理员邮箱查询机构
        when(agencyDao.getAgenciesByAdminEmails(any())).thenReturn(agencies);

        // 模拟机构管理员信息
        List<UserModel> agencyAdmins = new ArrayList<>();
        // 第一个管理员
        UserModel agencyAdmin1 = new UserModel();
        agencyAdmin1.setEmail("<EMAIL>"); // 邮箱
        agencyAdmin1.setRole(Role.AGENCY_OWNER.toString()); // 角色
        agencyAdmins.add(agencyAdmin1);
        // 第二个管理员
        UserModel agencyAdmin2 = new UserModel();
        agencyAdmin2.setEmail("<EMAIL>"); // 邮箱
        agencyAdmin2.setRole(Role.AGENCY_ADMIN.toString()); // 角色
        agencyAdmins.add(agencyAdmin2);
        // 模拟查询机构管理员
        when(userDao.getAgencyAdminsByAgencyIds(anyString())).thenReturn(agencyAdmins);

        // 模拟园长信息
        List<UserModel> siteAdmins = new ArrayList<>();
        UserModel siteAdmin1 = new UserModel();
        siteAdmin1.setEmail("<EMAIL>"); // 邮箱
        siteAdmin1.setRole(Role.SITE_ADMIN.toString()); // 角色
        siteAdmins.add(siteAdmin1);
        // 模拟查询园长
        when(userDao.getSiteAdminByAgencyIds(anyString())).thenReturn(siteAdmins);

        // 模拟老师信息
        List<UserModel> teachers = new ArrayList<>();
        UserModel teacher1 = new UserModel();
        teacher1.setEmail("<EMAIL>"); // 邮箱
        teacher1.setRole(Role.COLLABORATOR.toString()); // 角色
        teachers.add(teacher1);
        // 模拟查询老师
        when(userDao.getTeacherByAgencyIds(anyString())).thenReturn(teachers);

        // 模拟家长信息
        List<UserModel> parents = new ArrayList<>();
        UserModel parent1 = new UserModel();
        parent1.setEmail("<EMAIL>"); // 邮箱
        parent1.setRole(Role.PARENT.toString()); // 角色
        parents.add(parent1);
        // 模拟查询家长信息
        when(userDao.getOnboardedParentsByAgencyIds(anyString())).thenReturn(parents);

        // 生成的文件地址
        String excelUrl = "test-export-agency-users.xlsx";
        // 模拟上传文件
        when(fileSystem.uploadExcel(any(), anyString())).thenReturn(excelUrl);

        // 导出机构下用户
        ExportAgencyUsersResponse response = userService.exportAgencyUsers(request);

        // 验证结果
        assertEquals(excelUrl, response.getExcelUrl()); // Excel URL
        assertEquals(1, response.getErrorEmails().size()); // 存在一个错误的管理员邮箱
        assertEquals("<EMAIL>", response.getErrorEmails().get(0)); // 验证错误的管理员邮箱
    }

    /**
     * 测试老师角色获取学校
     * case: 角色为 Special 老师
     */
    @Test
    public void getCenterIdNameByUserId() {
        // 准备数据
        String userId = "userId001";
        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setRole(UserRole.COLLABORATOR.toString());
        // 方法模拟
        when(userProvider.checkUser(userId)).thenReturn(user);
        // 方法模拟(查询 Special 老师管理班级自己机构的学校)
        when(userDao.getCenterIdNameByTeacherId(userId)).thenReturn(new ArrayList<>());
        UserModel owner = new UserModel();
        owner.setId("ownerId001");
        // 方法模拟(查询 Special 老师所属机构)
        when(userDao.getAgencyOwnerByTeacherId(userId)).thenReturn(owner);
        List<AgencyEntity> agencyEntities = new ArrayList<>();
        AgencyEntity agencyEntity = new AgencyEntity();
        agencyEntity.setId("agencyId001");
        agencyEntities.add(agencyEntity);
        // 方法模拟(查询 Special 老师所属机构 Owner 管理的其他机构)
        when(agencyDao.getAgencyByIdentifierUserId(owner.getId())).thenReturn(agencyEntities);
        List<CenterWithIdName> appendCenters = new ArrayList<>();
        CenterWithIdName centerWithIdName = new CenterWithIdName();
        centerWithIdName.setId("centerId001");
        centerWithIdName.setName("centerName");
        appendCenters.add(centerWithIdName);
        // 方法模拟(查询 Special 老师关联其他机构下的学校)
        when(userDao.getCenterIdNameByIdentifiers(any(), any())).thenReturn(appendCenters);
        List<CenterEntity> centerEntities = new ArrayList<>();
        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setId("centerId001");
        centerEntities.add(centerEntity);
        when(centerDao.getAllByAgencyId("agencyId001")).thenReturn(centerEntities);
        // 调用方法
        List<CenterWithIdName> centers = userService.getCenterIdNameByUserId(userId, "agencyId001");

        // 验证
        assertEquals(centers.size(), 1); // 应该只返回一个学校
        CenterWithIdName center = centers.get(0); // 获取唯一的学校
        assertEquals(center.getId(), "centerId001"); // 学校的 Id 应该是 centerId001
        assertEquals(center.getName(), "centerName"); // 学校的名字应该是 centerName
        Assert.assertNull(center.getUserId()); // 学校的 userId 应该是 null
        assertEquals(center.getGroupWiths().size(), 0); // 学校的班级列表长度应该是 0

    }

    /**
     * 测试获取特殊教育者获取关联机构的所有学校
     * case: 角色为 Special Education
     */
    @Test
    public void getCenterGroupBySpecialTeacher() {
        // 准备 Special 老师数据
        String userId = "userId001";
        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setRole("AGENCY_OWNER");
        user.setType("GRANTEE");
        // 准备当前用户所在的机构信息
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("currentAgencyId001");
        agencyModel.setName("currentAgencyName");
        // 准备 Owner 数据
        UserModel owner = new UserModel();
        owner.setId("ownerId001");
        // 准备 Owner 所关联的其他机构的数据
        List<AgencyEntity> agencyEntities = new ArrayList<>();
        String agencyId = "agencyId001";
        AgencyEntity agencyEntity = new AgencyEntity();
        agencyEntity.setId(agencyId);
        agencyEntities.add(agencyEntity);
        List<String> agencyIds = new ArrayList<>();
        agencyIds.add(agencyId);
        // 准备 Special 老师关联其他机构下的学校班级数据
        List<CenterGroupModel> appendCenterGroupModels = new ArrayList<>();
        CenterGroupModel centerGroupModel = new CenterGroupModel();
        centerGroupModel.setCenterId("centerId001");
        centerGroupModel.setCenterName("centerName");
        appendCenterGroupModels.add(centerGroupModel);
        // 准备 Special 老师关联其他机构下的学校数据
        List<CenterModel> centerModels = new ArrayList<>();
        CenterModel centerModel = new CenterModel();
        centerModel.setId("centerId001");
        centerModel.setName("centerName");
        centerModels.add(centerModel);
        List<GroupFrameworkStatsModel> usedFrameworksByAgencyResult = new ArrayList<>();
        GroupFrameworkStatsModel groupFrameworkStatsModel = new GroupFrameworkStatsModel();
        groupFrameworkStatsModel.setFrameworkId("frameworkId0001");
        groupFrameworkStatsModel.setGroupId("groupId0001");
        groupFrameworkStatsModel.setFrameworkName("frameworkIdName");
        groupFrameworkStatsModel.setGroupName("groupName");
        usedFrameworksByAgencyResult.add(groupFrameworkStatsModel);
        // 是否包括关联机构
        boolean includeRelatedAgency = true;

        // 方法模拟
        when(userProvider.checkUser(userId)).thenReturn(user);
        // 方法模拟(查询当前用户所在的机构信息)
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        // 方法模拟（查询当前用户所在机构下的学校数据）
        when(userProvider.getCenterGroupByUserId(userId, false)).thenReturn(new ArrayList<>());
        // 方法模拟（查询机构管理员）
//        when(userDao.getAgencyOwnerByTeacherId(userId)).thenReturn(owner);

//        lenient().when(userDao.getAgencyOwnerByTeacherId(userId)).thenReturn(owner);


        when(userDao.getUserById(userId)).thenReturn(owner);

        // 方法模拟(查询 Special 老师所属机构 Owner 管理的其他机构)
        when(agencyDao.getAgencyByIdentifierUserId(owner.getId())).thenReturn(agencyEntities);
        // 方法模拟（查询 Special 老师关联其他机构下的学校班级数据）
        when(userDao.getCenterGroupsByUserIds(agencyIds, Collections.singletonList(userId))).thenReturn(appendCenterGroupModels);
        List<AgencyIdentifierEntity> identifiers = new ArrayList<>();
        AgencyIdentifierEntity identifier = new AgencyIdentifierEntity();
        identifier.setId("001");
        identifier.setName("name");
        identifiers.add(identifier);
        List<String> identifierIds = new ArrayList<>();
        identifierIds.add("name");
        when(agencyDao.getIdentifierByUserId(userId)).thenReturn(identifiers);
        when(userDao.getGranteeCenterGroupsByUserIds(agencyIds, identifierIds)).thenReturn(appendCenterGroupModels);

        // 方法模拟（转换学校班级数据）
        when(userProvider.convertCenterGroup(user.getRole(), appendCenterGroupModels, false)).thenReturn(centerModels);
        // 方法模拟（查询机构以设置使用 Modified Essential 的班级）
        when(domainDao.getEssentialGroupByAgency(agencyModel.getId())).thenReturn(new ArrayList<>());
        when(studentDao.getUsedFrameworksByAgency(agencyId)).thenReturn(usedFrameworksByAgencyResult);
        // 方法模拟（查询打开多框架设置的班级）
        when(frameworkProvider.getOpenMultiFrameworkGroupIdsByAgency(agencyId)).thenReturn(new LinkedHashSet<String>());
        // 方法模拟（查询试点学校数据）
        when(centerDao.getTrainingCenters(agencyModel.getId())).thenReturn(new ArrayList<>());
        // 方法模拟（查询试点班级数据）
        when(groupDao.getTrainingGroup(agencyModel.getId())).thenReturn(new ArrayList<>());

        // 调用方法
        List<CenterModel> centerModelsData = userService.getCenterGroupByUserId(userId, false, agencyId, includeRelatedAgency);

        // 验证方法只会返回一条数据
        assertEquals(centerModelsData.size(), 1);
        // 获取唯一的一条数据
        CenterModel model = centerModelsData.get(0);
        // 验证学校的 ID 应该是 centerId001
        assertEquals(model.getId(), "centerId001");
        // 验证学校的名称应该是 centerName
        assertEquals(model.getName(), "centerName");
        // 验证学校下的班级应该是空
        assertEquals(model.getGroups().size(), 0);
    }

    /**
     * 测试设置当前用户上次导入邀请家长的选项值方法
     */
    @Test
    public void testSetLastImportInviteParentsOption() {
        // 准备数据
        String userId = "userId0001";
        boolean isInviteParents = true;
        // 数据模拟
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        // 接口模拟
        userService.setLastImportInviteParentsOption(isInviteParents);
        // 验证设置元数据方法入参和调用次数
        verify(usersMetaDataDao).setMeta(userId, "LAST_IMPORT_INVITE_PARENTS_OPTION_VALUE", "true");
    }

    /**
     * 测试获取当前用户上次导入邀请家长的选项值方法
     */
    @Test
    public void testGetLastImportInviteParentsOption() {
        // 准备数据
        String userId = "userId001";
        // 数据模拟
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.getOpenValue(anyString(), anyString())).thenReturn(null);
        // 接口模拟
        LastImportInviteParentsOptionResponse response = userService.getLastImportInviteParentsOption();
        // 验证获取到的邀请家长选项为 false
        Assert.assertFalse(response.isInviteParents());
    }

    /**
     * 测试获取 SPED 教师关联机构小孩的聊天群组方法
     */
    @Test
    public void testGetSpecialEducationEnrollmentChatGroups() {
        // 模拟 UserDao 的数据
        UserModel owner = new UserModel(); // 当前机构 Owner
        owner.setId("ownerId");
        when(userDao.getAgencyOwnerByTeacherId(anyString())).thenReturn(owner);

        // 模拟 AgencyDao 的数据
        List<AgencyEntity> agencyEntities = new ArrayList<>();
        AgencyEntity agencyEntity = new AgencyEntity(); // 当前机构
        agencyEntity.setId("agencyId");
        agencyEntities.add(agencyEntity);
        when(agencyDao.getAgencyByIdentifierUserId(anyString())).thenReturn(agencyEntities);

        // 模拟 SPED 老师获取关联的孩子列表
        CenterEntity centerEntity1 = new CenterEntity(); // 创建一个学校
        centerEntity1.setId("centerId1");
        centerEntity1.setName("centerName1");
        GroupEntity group1 = new GroupEntity(); // 创建第一个班级
        group1.setId("groupId001");
        group1.setName("groupName001");
        group1.setCenter(centerEntity1);
        GroupEntity group2 = new GroupEntity(); // 创建第二个班级
        group2.setId("groupId002");
        group2.setName("groupName002");
        group2.setCenter(centerEntity1);
        List<EnrollmentEntity> enrollments = new ArrayList<>();
        EnrollmentEntity enrollment01 = new EnrollmentEntity(); // 创建第一个孩子
        enrollment01.setId("enrollmentId001");
        enrollment01.setGroup(group1);
        enrollment01.setAgencyId("agencyId");
        EnrollmentEntity enrollment02 = new EnrollmentEntity(); // 创建第二个孩子
        enrollment02.setId("enrollmentId002");
        enrollment02.setGroup(group2);
        enrollment02.setAgencyId("agencyId");
        enrollments.add(enrollment01);
        enrollments.add(enrollment02);
        when(userDao.getChildrenBySpecialEducationId(anyList(), anyString())).thenReturn(enrollments);
        when(regionService.isAmerica()).thenReturn(true);

        // 模拟 StudentDao.getChildChatGroups 的数据
        List<ChildChatModel> childChatGroups = new ArrayList<>();
        ChildChatModel childChatModel01 = new ChildChatModel(); // 创建第一个孩子的聊天群组
        childChatModel01.setGroupId("groupId001");
        childChatModel01.setChatGroupId("chatGroupId001");
        childChatModel01.setDisPlayName("disPlayName01");
        childChatModel01.setAvatarUrl("avatarUrl01");
        ChildChatModel childChatModel02 = new ChildChatModel(); // 创建第二个孩子的聊天群组
        childChatModel02.setGroupId("groupId002");
        childChatModel02.setChatGroupId("chatGroupId002");
        childChatModel02.setAvatarUrl("avatarUrl02");
        childChatModel02.setDisPlayName("disPlayName02");
        childChatGroups.add(childChatModel01);
        childChatGroups.add(childChatModel02);
        when(studentDao.getChildChatGroups(anyList(), anyInt())).thenReturn(childChatGroups);
        when(fileSystem.getPublicUrl(anyString())).thenReturn("mockedUrl");
        // 调用测试方法
        EducatorUserEnrollmentChatGroupResp result = userService.getSpecialEducationEnrollmentChatGroups("userId");

        // 结果校验
        List<String> chatGroupIdList = result.getChatGroupIdList(); // 从结果中获取聊天群组 ID 列表
        List<CenterChatModel> centerChatModelList = result.getCenterChatModelList(); // 从结果中获取学校聊天组信息列表
        assertEquals(2, chatGroupIdList.size()); // 验证有两个聊天群组 ID
        assertEquals("chatGroupId001", chatGroupIdList.get(0)); // 验证第一个聊天群组 ID
        assertEquals("chatGroupId002", chatGroupIdList.get(1)); // 验证第一个聊天群组 ID
        assertEquals(2, result.getSpecialGroupChatModel().getChildren().size()); // 混合班级群组数量
        assertEquals("Cross-Agency Assigned Children", result.getSpecialGroupChatModel().getName()); // 验证混合班级群组名称
        assertEquals("chatGroupId001", result.getSpecialGroupChatModel().getChildren().get(0).getChatGroupId()); // 验证混合班级群组第一个孩子的聊天群组 ID
        assertEquals("disPlayName01", result.getSpecialGroupChatModel().getChildren().get(0).getDisPlayName()); // 验证混合班级群组第一个孩子的显示名称
        assertEquals("groupId001", result.getSpecialGroupChatModel().getChildren().get(0).getGroupId()); // 验证混合班级群组第一个孩子的班级 ID
        assertEquals("centerName1", result.getSpecialGroupChatModel().getChildren().get(0).getCenterName()); // 验证混合班级群组第一个孩子的学校名称
        assertEquals("groupName001", result.getSpecialGroupChatModel().getChildren().get(0).getClassName()); // 验证混合班级群组第一个孩子的班级名称
        assertEquals("chatGroupId002", result.getSpecialGroupChatModel().getChildren().get(1).getChatGroupId()); // 验证混合班级群组第二个孩子的聊天群组 ID
        assertEquals("groupId002", result.getSpecialGroupChatModel().getChildren().get(1).getGroupId()); // 验证混合班级群组第二个孩子的班级 ID
        assertEquals("disPlayName02", result.getSpecialGroupChatModel().getChildren().get(1).getDisPlayName()); // 验证混合班级群组第二个孩子的显示名称
        assertEquals("centerName1", result.getSpecialGroupChatModel().getChildren().get(1).getCenterName()); // 验证混合班级群组第二个孩子的学校名称
        assertEquals("groupName002", result.getSpecialGroupChatModel().getChildren().get(1).getClassName()); // 验证混合班级群组第二个孩子的班级名称

//        Assert.assertEquals(1, centerChatModelList.size()); // 验证只有一个学校
//        Assert.assertEquals("centerId1", centerChatModelList.get(0).getId()); // 验证学校 ID
//        Assert.assertEquals("centerName1", centerChatModelList.get(0).getName()); // 验证学校名称
//        Assert.assertEquals(2, centerChatModelList.get(0).getGroups().size()); // 验证学校下有两个班级
//        Assert.assertEquals(2, centerChatModelList.get(0).getGroupCount().intValue()); // 验证学校下有两个班级
//        Assert.assertEquals("groupId001", centerChatModelList.get(0).getGroups().get(0).getId()); // 验证第一个班级 ID
//        Assert.assertEquals("groupName001", centerChatModelList.get(0).getGroups().get(0).getName()); // 验证第一个班级名称
//        Assert.assertEquals(1, centerChatModelList.get(0).getGroups().get(0).getChildCount().intValue()); // 验证第一个班级的孩子数量
//        Assert.assertEquals(1, centerChatModelList.get(0).getGroups().get(0).getChildren().size()); // 验证第一个班级下有一个孩子
//        Assert.assertEquals("chatGroupId001", centerChatModelList.get(0).getGroups().get(0).getChildren().get(0).getChatGroupId()); // 验证第一个班级下的孩子的聊天群组 ID
//        Assert.assertEquals(1, centerChatModelList.get(0).getGroups().get(1).getChildCount().intValue()); // 验证第二个班级的孩子数量
//        Assert.assertEquals(1, centerChatModelList.get(0).getGroups().get(1).getChildren().size()); // 验证第二个班级下有一个孩子
//        Assert.assertEquals("chatGroupId002", centerChatModelList.get(0).getGroups().get(1).getChildren().get(0).getChatGroupId()); // 验证第二个班级下的孩子的聊天群组 ID
    }
    @Test
    public void testHIdeTopNavbar() {
        String userId = "1";
        when(userProvider.getCurrentUserId()).thenReturn(userId);

        List<UsersMetaDataEntity> metas = Lists.newArrayList();
        when(userMetaDao.getMetas(UserMetaKey.SHOW_TOP_NAVBAR.toString(), userId)).thenReturn(metas);

        when(userMetaDao.insertMeta(UserMetaKey.SHOW_TOP_NAVBAR.toString(), TopNavbarStatus.HIDDEN.toString(), userId)).thenReturn("");

        SuccessResponse successResponse = userService.hideTopNavbar();
        Assert.assertNotNull(successResponse);
    }

    @Test
    public void testGetTopNavbarStatus() {
        String userId = "1";
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        // 获取机构信息
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("1");
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);

        // 上传功能开关
        when(userProvider.getAgencyOpenDefaultOpen(agencyModel.getId(), AgencyMetaKey.DRDP_UPLOAD.toString())).thenReturn(true);

        AgencyMetaDataEntity uploadMeta = new AgencyMetaDataEntity();
        uploadMeta.setAgencyId(agencyModel.getId());
        uploadMeta.setMetaValue(DrdpSettingValue.DRDP_UPLOAD_OPEN.toString());
        when(agencyDao.getMeta(agencyModel.getId(), DrdpSettingKey.UPLOAD_DRDP_SETTING.toString())).thenReturn(uploadMeta);

        UserMetaDataEntity showTopNavbarMeta = new UserMetaDataEntity();
        showTopNavbarMeta.setMetaKey(UserMetaKey.SHOW_TOP_NAVBAR.toString());
        showTopNavbarMeta.setMetaValue(TopNavbarStatus.SHOW.toString());
        when(userDao.getMetaData(userId, UserMetaKey.SHOW_TOP_NAVBAR.toString())).thenReturn(showTopNavbarMeta);

        UploadFailedResponse uploadFailedChildren = new UploadFailedResponse();
        uploadFailedChildren.setExistsUploadError(true);
        uploadFailedChildren.setUploadDRDP(true);
        when(scoreService.getUploadFailedChildren(false, true)).thenReturn(uploadFailedChildren);

        TopNavbarStatusResponse response = userService.getTopNavbarStatus();

        Assert.assertTrue(response.getFeatureStatus().stream().map(TopNavbarFeatureStatus::getStatus).anyMatch(TopNavbarStatus.SHOW::equals));
    }

    /**
     * 测试获取当前用户是否可以锁定孩子
     */
    @Test
    public void testCanLockChild() {
        // 准备测试数据
        String userId = "testUserId";
        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole(UserRole.COLLABORATOR.toString());
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("testAgencyId");
        AgencyMetaDataEntity uploadMeta = new AgencyMetaDataEntity();
        uploadMeta.setMetaValue(DrdpSettingValue.DRDP_UPLOAD_OPEN.toString());
        AgencyMetaDataEntity permissionMeta = new AgencyMetaDataEntity();
        permissionMeta.setMetaValue(DrdpSettingValue.LOCK_BY_TEACHER.toString());

        // 设置模拟对象的行为
        when(userProvider.checkUser(userId)).thenReturn(userEntity);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        when(userProvider.getAgencyOpenDefaultClose(agencyModel.getId(), AgencyMetaKey.PAID.toString())).thenReturn(true);
        when(userProvider.getAgencyOpenDefaultClose(agencyModel.getId(), AgencyMetaKey.TEACHER_BATCH_LOCK_CHILDREN_OPEN.toString())).thenReturn(true);
        when(userProvider.getAgencyOpenDefaultOpen(agencyModel.getId(), AgencyMetaKey.DRDP_UPLOAD.toString())).thenReturn(true);
        when(agencyDao.getMeta(agencyModel.getId(), DrdpSettingKey.UPLOAD_DRDP_SETTING.toString())).thenReturn(uploadMeta);
        when(agencyDao.getMeta(agencyModel.getId(), DrdpSettingKey.LOCK_UPLOAD_SETTING.toString())).thenReturn(permissionMeta);

        // 调用方法并获取返回结果
        CanLockChildResponse response = userService.canLockChild(userId);

        // 验证结果
        Assert.assertTrue(response.isCanLockChild()); // 验证可以锁定孩子
        Assert.assertTrue(response.isCanBatchLockChildren()); // 验证可以批量锁定孩子

        // 验证模拟对象的行为是否如预期
        verify(userProvider, times(1)).checkUser(userId); // 验证检查用户的行为调用了一次
        verify(userProvider, times(1)).getAgencyByUserId(userId); // 验证获取机构信息的行为调用了一次
        verify(userProvider, times(1)).getAgencyOpenDefaultClose(agencyModel.getId(), AgencyMetaKey.PAID.toString()); // 验证获取机构是否付费的行为调用了一次
        verify(userProvider, times(1)).getAgencyOpenDefaultClose(agencyModel.getId(), AgencyMetaKey.TEACHER_BATCH_LOCK_CHILDREN_OPEN.toString()); // 验证获取机构是否开启批量锁定孩子的行为调用了一次
        verify(userProvider, times(1)).getAgencyOpenDefaultOpen(agencyModel.getId(), AgencyMetaKey.DRDP_UPLOAD.toString()); // 验证获取机构是否开启上传功能的行为调用了一次
        verify(agencyDao, times(1)).getMeta(agencyModel.getId(), DrdpSettingKey.UPLOAD_DRDP_SETTING.toString()); // 验证获取机构上传功能设置的行为调用了一次
        verify(agencyDao, times(1)).getMeta(agencyModel.getId(), DrdpSettingKey.LOCK_UPLOAD_SETTING.toString()); // 验证获取机构上传功能设置的行为调用了一次
    }

    /**
     * 测试检查邮箱是否属于机构
     */
    @Test
    public void testCheckEmailBelongToAgency() {
        // 准备测试数据
        String email = "testEmail";
        String userId = "testUserId";
        String agencyId = "testAgencyId";
        String agencyId_01 = "testAgencyId_01";
        String userId_01 = "testUserId_01";
        String userId_02 = "testUserId_02";
        // 用户信息
        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        // 机构信息
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        AgencyModel agencyModel_01 = new AgencyModel();
        agencyModel_01.setId(agencyId_01);
        // 用户信息
        UserModel userModel = new UserModel();
        userModel.setEmail(email);
        userModel.setId(userId_01);
        userModel.setRole(UserRole.COLLABORATOR.toString());
        UserModel userModel1 = new UserModel();
        userModel1.setEmail(email);
        userModel1.setId(userId_02);
        userModel1.setRole(UserRole.PARENT.toString());

        // 数据模拟
//        when(userProvider.getCurrentUserId()).thenReturn(userId);
//        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
//        when(userDao.getUserByEmail(email)).thenReturn(userModel1);
//        when(userProvider.getAgencyByUserId(userModel.getId())).thenReturn(agencyModel);
        // 调用方法并获取返回结果
        SuccessResponse response = userService.checkEmailBelongToAgency(email);
        // 验证结果
        Assert.assertFalse(response.isSuccess());
//        when(userDao.getUserByEmail(email)).thenReturn(userModel);
//        when(userProvider.getAgencyByUserId(userModel.getId())).thenReturn(agencyModel_01);
//        SuccessResponse response1 = userService.checkEmailBelongToAgency(email);
//        Assert.assertFalse(response1.isSuccess());
//        when(userDao.getUserByEmail(email)).thenReturn(userModel);
//        when(userProvider.getAgencyByUserId(userModel.getId())).thenReturn(agencyModel);
//        SuccessResponse response2 = userService.checkEmailBelongToAgency(email);
//        Assert.assertTrue(response2.isSuccess());
    }

    /**
     * 测试创建单元计划功能开关
     * case: 开关默认值
     */
    @Test
    public void testGetUserOpenOfCreateUnitDefault() {
        // 当前登录人信息
        com.learninggenie.common.data.entity.UserEntity userEntity = new UserEntity();
        userEntity.setRole("AGENCY_ADMIN");
        String userId = "user01";
        AuthUserDetails authUserDetails = new AuthUserDetails();
        authUserDetails.setAgencyId("002");
        // 获取当前登录人所在机构下的所有 center
        List<AgencyCenterEntity> agencyCenters = new ArrayList<>();
        AgencyCenterEntity agencyCenterEntity = new AgencyCenterEntity();
        agencyCenterEntity.setCenterId("101");
        agencyCenters.add(agencyCenterEntity);
        List<String> centerIds = new ArrayList<>();
        centerIds.add("101");
        List<com.learninggenie.common.data.entity.contents.CenterEntity> centers = new ArrayList<>();
        com.learninggenie.common.data.entity.contents.CenterEntity center = new com.learninggenie.common.data.entity.contents.CenterEntity();
        center.setUserId("004");
        center.setId("101");
        center.setName("test001");
        centers.add(center);
        // 获取 cite admin
        List<com.learninggenie.common.data.entity.agencies.CenterUserEntity> citeAdmins = new ArrayList<>();
        com.learninggenie.common.data.entity.agencies.CenterUserEntity citeAdmin = new com.learninggenie.common.data.entity.agencies.CenterUserEntity();
        citeAdmin.setUserId("123");
        citeAdmin.setCenterId("101");
        citeAdmins.add(citeAdmin);
        // 获取直属 center 的老师
        List<com.learninggenie.common.data.entity.CenterUserEntity> centerUsers = new ArrayList<>();
        com.learninggenie.common.data.entity.CenterUserEntity centerUser = new com.learninggenie.common.data.entity.CenterUserEntity();
        centerUser.setUserId("123");
        centerUser.setCenterId("101");
        centerUsers.add(centerUser);
        // 获取班级老师
        List<com.learninggenie.common.data.entity.contents.GroupEntity> groups = new ArrayList<>();
        com.learninggenie.common.data.entity.contents.GroupEntity group = new com.learninggenie.common.data.entity.contents.GroupEntity();
        group.setCenterId("101");
        group.setId("104");
        groups.add(group);
        // 解决班级超过 2100 个，导致 in 查询老师班级关系失败的问题，将按照班级 Id 查询改为按照学校 Id 查询
        List<UserGroupEntity> groupUsers = new ArrayList<>();
        UserGroupEntity userGroupEntity = new UserGroupEntity();
        userGroupEntity.setUserId("123");
        userGroupEntity.setGroupId("104");
        groupUsers.add(userGroupEntity);
        // 去掉已删除老师
        List<com.learninggenie.common.data.entity.authentication.UserEntity> userEntities = new ArrayList<>();
        com.learninggenie.common.data.entity.authentication.UserEntity user1 = new com.learninggenie.common.data.entity.authentication.UserEntity();
        user1.setId("123");
        user1.setUserName("testUser");
        user1.setRole("Teacher");
        userEntities.add(user1);
        // 获取老师集合
        List<com.learninggenie.common.data.entity.authentication.UserProfileEntity> userProfiles = new ArrayList<>();
        com.learninggenie.common.data.entity.authentication.UserProfileEntity userProfile = new com.learninggenie.common.data.entity.authentication.UserProfileEntity();
        userProfile.setAvatarMediaId("009");
        userProfile.setUserId("123");
        userProfile.setDisplayName("testUser");
        userProfiles.add(userProfile);
        // 用户头像
        List<MediaEntity> mediaEntities = new ArrayList<>();
        MediaEntity mediaEntity = new MediaEntity();
        mediaEntity.setRelativePath("aaa");
        mediaEntity.setId("111");
        mediaEntities.add(mediaEntity);
        ArrayList<String> mediaIds = new ArrayList<>();
        mediaIds.add("222");

        // 设置模拟对象的行为
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.checkUser(userId)).thenReturn(userEntity);
        when(userProvider.getCurrentUser()).thenReturn(authUserDetails);
        when(agencyCenterDao.getByAgencyId(userProvider.getCurrentUser().getAgencyId())).thenReturn(agencyCenters);
        when(Center2Dao.getByCenterIds(centerIds)).thenReturn(centers);
        when(agencyCenterUserDao.getByCenterIdIn(centerIds)).thenReturn(citeAdmins);
        when(centerUserDao.getByCenterIdIn(centerIds)).thenReturn(centerUsers);
        when(contentsGroupDao.getByCenterIdIn(centerIds)).thenReturn(groups);
        when(groupDao.getUserGroupByCenterIds(centerIds)).thenReturn(groupUsers);
        when(user2Dao.getByUserId(anyList())).thenReturn(userEntities);
        when(userProfileDao.getByUserIdIn(anyList())).thenReturn(userProfiles);
        lenient().when(mediaEntityDao.listByIdIn(mediaIds)).thenReturn(mediaEntities);

        // 模拟 metaDataDao.getByUserIdsAndMetaKeysIn() 的返回值
        MetaDataEntity metaDataEntity1 = new MetaDataEntity();
        metaDataEntity1.setUserId("123");
        metaDataEntity1.setMetaKey("PERMISSION_LESSONS_WITHOUT_APPROVAL");
        metaDataEntity1.setMetaValue("true");
        MetaDataEntity metaDataEntity2 = new MetaDataEntity();
        metaDataEntity2.setUserId("123");
        metaDataEntity2.setMetaKey("PLAN_PRIVILEGE_USER");
        metaDataEntity2.setMetaValue("true");

        // 假设查询到了用户设置
        List<MetaDataEntity> metaDataEntities = new ArrayList<>();
        Collections.addAll(metaDataEntities,metaDataEntity1,metaDataEntity2);
        List<String> ids = new ArrayList<>();
        ids.add("123");
        when(metaDataDao.getByUserIdsAndMetaKeysIn(anyList(), anyList()))
                .thenReturn(metaDataEntities);

        // 调用被测试的方法
        GetUserOpenResponse response = userService.getUserOpen("PERMISSION_LESSONS_WITHOUT_APPROVAL,PLAN_PRIVILEGE_USER,CREATE_UNIT_PLANNER_OPEN");

        // 验证返回的 GetUserOpenResponse 是否符合预期
        assertEquals(1, response.getSettings().size());
        GetUserOpenModel setting = response.getSettings().get(0);
        assertEquals("123", setting.getUserId());
        assertEquals("testUser", setting.getUserName());
        assertEquals("Teacher", setting.getRole());
        Assert.assertArrayEquals(setting.getMetaValues(),new String[]{"true","true","true"});
    }

    /**
     * 测试创建单元计划功能开关
     * case: 开关关闭
     */
    @Test
    public void testGetUserOpenOfCreateUnitOff() {
        // 当前登录人信息
        com.learninggenie.common.data.entity.UserEntity userEntity = new UserEntity();
        userEntity.setRole("AGENCY_ADMIN");
        String userId = "user01";
        AuthUserDetails authUserDetails = new AuthUserDetails();
        authUserDetails.setAgencyId("002");
        // 获取当前登录人所在机构下的所有 center
        List<AgencyCenterEntity> agencyCenters = new ArrayList<>();
        AgencyCenterEntity agencyCenterEntity = new AgencyCenterEntity();
        agencyCenterEntity.setCenterId("101");
        agencyCenters.add(agencyCenterEntity);
        List<String> centerIds = new ArrayList<>();
        centerIds.add("101");
        List<com.learninggenie.common.data.entity.contents.CenterEntity> centers = new ArrayList<>();
        com.learninggenie.common.data.entity.contents.CenterEntity center = new com.learninggenie.common.data.entity.contents.CenterEntity();
        center.setUserId("004");
        center.setId("101");
        center.setName("test001");
        centers.add(center);
        // 获取 cite admin
        List<com.learninggenie.common.data.entity.agencies.CenterUserEntity> citeAdmins = new ArrayList<>();
        com.learninggenie.common.data.entity.agencies.CenterUserEntity citeAdmin = new com.learninggenie.common.data.entity.agencies.CenterUserEntity();
        citeAdmin.setUserId("123");
        citeAdmin.setCenterId("101");
        citeAdmins.add(citeAdmin);
        // 获取直属 center 的老师
        List<com.learninggenie.common.data.entity.CenterUserEntity> centerUsers = new ArrayList<>();
        com.learninggenie.common.data.entity.CenterUserEntity centerUser = new com.learninggenie.common.data.entity.CenterUserEntity();
        centerUser.setUserId("123");
        centerUser.setCenterId("101");
        centerUsers.add(centerUser);
        // 获取班级老师
        List<com.learninggenie.common.data.entity.contents.GroupEntity> groups = new ArrayList<>();
        com.learninggenie.common.data.entity.contents.GroupEntity group = new com.learninggenie.common.data.entity.contents.GroupEntity();
        group.setCenterId("101");
        group.setId("104");
        groups.add(group);
        // 解决班级超过 2100 个，导致 in 查询老师班级关系失败的问题，将按照班级 Id 查询改为按照学校 Id 查询
        List<UserGroupEntity> groupUsers = new ArrayList<>();
        UserGroupEntity userGroupEntity = new UserGroupEntity();
        userGroupEntity.setUserId("123");
        userGroupEntity.setGroupId("104");
        groupUsers.add(userGroupEntity);
        // 去掉已删除老师
        List<com.learninggenie.common.data.entity.authentication.UserEntity> userEntities = new ArrayList<>();
        com.learninggenie.common.data.entity.authentication.UserEntity user1 = new com.learninggenie.common.data.entity.authentication.UserEntity();
        user1.setId("123");
        user1.setUserName("testUser");
        user1.setRole("Teacher");
        userEntities.add(user1);
        // 获取老师集合
        List<com.learninggenie.common.data.entity.authentication.UserProfileEntity> userProfiles = new ArrayList<>();
        com.learninggenie.common.data.entity.authentication.UserProfileEntity userProfile = new com.learninggenie.common.data.entity.authentication.UserProfileEntity();
        userProfile.setAvatarMediaId("009");
        userProfile.setUserId("123");
        userProfile.setDisplayName("testUser");
        userProfiles.add(userProfile);
        // 用户头像
        List<MediaEntity> mediaEntities = new ArrayList<>();
        MediaEntity mediaEntity = new MediaEntity();
        mediaEntity.setRelativePath("aaa");
        mediaEntity.setId("111");
        mediaEntities.add(mediaEntity);
        ArrayList<String> mediaIds = new ArrayList<>();
        mediaIds.add("222");

        // 设置模拟对象的行为
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.checkUser(userId)).thenReturn(userEntity);
        when(userProvider.getCurrentUser()).thenReturn(authUserDetails);
        when(agencyCenterDao.getByAgencyId(userProvider.getCurrentUser().getAgencyId())).thenReturn(agencyCenters);
        when(Center2Dao.getByCenterIds(centerIds)).thenReturn(centers);
        when(agencyCenterUserDao.getByCenterIdIn(centerIds)).thenReturn(citeAdmins);
        when(centerUserDao.getByCenterIdIn(centerIds)).thenReturn(centerUsers);
        when(contentsGroupDao.getByCenterIdIn(centerIds)).thenReturn(groups);
        when(groupDao.getUserGroupByCenterIds(centerIds)).thenReturn(groupUsers);
        when(user2Dao.getByUserId(anyList())).thenReturn(userEntities);
        when(userProfileDao.getByUserIdIn(anyList())).thenReturn(userProfiles);
        lenient().when(mediaEntityDao.listByIdIn(mediaIds)).thenReturn(mediaEntities);

        // 模拟 metaDataDao.getByUserIdsAndMetaKeysIn() 的返回值
        MetaDataEntity metaDataEntity1 = new MetaDataEntity();
        metaDataEntity1.setUserId("123");
        metaDataEntity1.setMetaKey("PERMISSION_LESSONS_WITHOUT_APPROVAL");
        metaDataEntity1.setMetaValue("true");
        MetaDataEntity metaDataEntity2 = new MetaDataEntity();
        metaDataEntity2.setUserId("123");
        metaDataEntity2.setMetaKey("PLAN_PRIVILEGE_USER");
        metaDataEntity2.setMetaValue("true");
        MetaDataEntity metaDataEntity3 = new MetaDataEntity();
        metaDataEntity3.setUserId("123");
        metaDataEntity3.setMetaKey("CREATE_UNIT_PLANNER_OPEN");
        metaDataEntity3.setMetaValue("false");

        // 查询到了用户设置
        List<MetaDataEntity> metaDataEntities = new ArrayList<>();
        Collections.addAll(metaDataEntities,metaDataEntity1,metaDataEntity2,metaDataEntity3);
        List<String> ids = new ArrayList<>();
        ids.add("123");
        when(metaDataDao.getByUserIdsAndMetaKeysIn(anyList(), anyList()))
                .thenReturn(metaDataEntities);

        // 调用被测试的方法
        GetUserOpenResponse response = userService.getUserOpen("PERMISSION_LESSONS_WITHOUT_APPROVAL,PLAN_PRIVILEGE_USER,CREATE_UNIT_PLANNER_OPEN");

        // 验证返回的 GetUserOpenResponse 是否符合预期
        assertEquals(1, response.getSettings().size());
        GetUserOpenModel setting = response.getSettings().get(0);
        assertEquals("123", setting.getUserId());
        assertEquals("testUser", setting.getUserName());
        assertEquals("Teacher", setting.getRole());
        Assert.assertArrayEquals(setting.getMetaValues(),new String[]{"true","true","false"});
    }

    /**
     * 测试创建单元计划功能开关
     * case: 开关打开
     */
    @Test
    public void testGetUserOpenOfCreateUnitOpen() {
        // 当前登录人信息
        com.learninggenie.common.data.entity.UserEntity userEntity = new UserEntity();
        userEntity.setRole("AGENCY_ADMIN");
        String userId = "user01";
        AuthUserDetails authUserDetails = new AuthUserDetails();
        authUserDetails.setAgencyId("002");
        // 获取当前登录人所在机构下的所有 center
        List<AgencyCenterEntity> agencyCenters = new ArrayList<>();
        AgencyCenterEntity agencyCenterEntity = new AgencyCenterEntity();
        agencyCenterEntity.setCenterId("101");
        agencyCenters.add(agencyCenterEntity);
        List<String> centerIds = new ArrayList<>();
        centerIds.add("101");
        List<com.learninggenie.common.data.entity.contents.CenterEntity> centers = new ArrayList<>();
        com.learninggenie.common.data.entity.contents.CenterEntity center = new com.learninggenie.common.data.entity.contents.CenterEntity();
        center.setUserId("004");
        center.setId("101");
        center.setName("test001");
        centers.add(center);
        // 获取 cite admin
        List<com.learninggenie.common.data.entity.agencies.CenterUserEntity> citeAdmins = new ArrayList<>();
        com.learninggenie.common.data.entity.agencies.CenterUserEntity citeAdmin = new com.learninggenie.common.data.entity.agencies.CenterUserEntity();
        citeAdmin.setUserId("123");
        citeAdmin.setCenterId("101");
        citeAdmins.add(citeAdmin);
        // 获取直属 center 的老师
        List<com.learninggenie.common.data.entity.CenterUserEntity> centerUsers = new ArrayList<>();
        com.learninggenie.common.data.entity.CenterUserEntity centerUser = new com.learninggenie.common.data.entity.CenterUserEntity();
        centerUser.setUserId("123");
        centerUser.setCenterId("101");
        centerUsers.add(centerUser);
        // 获取班级老师
        List<com.learninggenie.common.data.entity.contents.GroupEntity> groups = new ArrayList<>();
        com.learninggenie.common.data.entity.contents.GroupEntity group = new com.learninggenie.common.data.entity.contents.GroupEntity();
        group.setCenterId("101");
        group.setId("104");
        groups.add(group);
        // 解决班级超过 2100 个，导致 in 查询老师班级关系失败的问题，将按照班级 Id 查询改为按照学校 Id 查询
        List<UserGroupEntity> groupUsers = new ArrayList<>();
        UserGroupEntity userGroupEntity = new UserGroupEntity();
        userGroupEntity.setUserId("123");
        userGroupEntity.setGroupId("104");
        groupUsers.add(userGroupEntity);
        // 去掉已删除老师
        List<com.learninggenie.common.data.entity.authentication.UserEntity> userEntities = new ArrayList<>();
        com.learninggenie.common.data.entity.authentication.UserEntity user1 = new com.learninggenie.common.data.entity.authentication.UserEntity();
        user1.setId("123");
        user1.setUserName("testUser");
        user1.setRole("Teacher");
        userEntities.add(user1);
        // 获取老师集合
        List<com.learninggenie.common.data.entity.authentication.UserProfileEntity> userProfiles = new ArrayList<>();
        com.learninggenie.common.data.entity.authentication.UserProfileEntity userProfile = new com.learninggenie.common.data.entity.authentication.UserProfileEntity();
        userProfile.setAvatarMediaId("009");
        userProfile.setUserId("123");
        userProfile.setDisplayName("testUser");
        userProfiles.add(userProfile);
        // 用户头像
        List<MediaEntity> mediaEntities = new ArrayList<>();
        MediaEntity mediaEntity = new MediaEntity();
        mediaEntity.setRelativePath("aaa");
        mediaEntity.setId("111");
        mediaEntities.add(mediaEntity);
        ArrayList<String> mediaIds = new ArrayList<>();
        mediaIds.add("222");

        // 设置模拟对象的行为
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.checkUser(userId)).thenReturn(userEntity);
        when(userProvider.getCurrentUser()).thenReturn(authUserDetails);
        when(agencyCenterDao.getByAgencyId(userProvider.getCurrentUser().getAgencyId())).thenReturn(agencyCenters);
        when(Center2Dao.getByCenterIds(centerIds)).thenReturn(centers);
        when(agencyCenterUserDao.getByCenterIdIn(centerIds)).thenReturn(citeAdmins);
        when(centerUserDao.getByCenterIdIn(centerIds)).thenReturn(centerUsers);
        when(contentsGroupDao.getByCenterIdIn(centerIds)).thenReturn(groups);
        when(groupDao.getUserGroupByCenterIds(centerIds)).thenReturn(groupUsers);
        when(user2Dao.getByUserId(anyList())).thenReturn(userEntities);
        when(userProfileDao.getByUserIdIn(anyList())).thenReturn(userProfiles);
        lenient().when(mediaEntityDao.listByIdIn(mediaIds)).thenReturn(mediaEntities);

        // 模拟 metaDataDao.getByUserIdsAndMetaKeysIn() 的返回值
        MetaDataEntity metaDataEntity1 = new MetaDataEntity();
        metaDataEntity1.setUserId("123");
        metaDataEntity1.setMetaKey("PERMISSION_LESSONS_WITHOUT_APPROVAL");
        metaDataEntity1.setMetaValue("true");
        MetaDataEntity metaDataEntity2 = new MetaDataEntity();
        metaDataEntity2.setUserId("123");
        metaDataEntity2.setMetaKey("PLAN_PRIVILEGE_USER");
        metaDataEntity2.setMetaValue("true");
        MetaDataEntity metaDataEntity3 = new MetaDataEntity();
        metaDataEntity3.setUserId("123");
        metaDataEntity3.setMetaKey("CREATE_UNIT_PLANNER_OPEN");
        metaDataEntity3.setMetaValue("true");

        // 查询到了用户设置
        List<MetaDataEntity> metaDataEntities = new ArrayList<>();
        Collections.addAll(metaDataEntities,metaDataEntity1,metaDataEntity2,metaDataEntity3);
        List<String> ids = new ArrayList<>();
        ids.add("123");
        when(metaDataDao.getByUserIdsAndMetaKeysIn(anyList(), anyList()))
                .thenReturn(metaDataEntities);

        // 调用被测试的方法
        GetUserOpenResponse response = userService.getUserOpen("PERMISSION_LESSONS_WITHOUT_APPROVAL,PLAN_PRIVILEGE_USER,CREATE_UNIT_PLANNER_OPEN");

        // 验证返回的 GetUserOpenResponse 是否符合预期
        assertEquals(1, response.getSettings().size());
        GetUserOpenModel setting = response.getSettings().get(0);
        assertEquals("123", setting.getUserId());
        assertEquals("testUser", setting.getUserName());
        assertEquals("Teacher", setting.getRole());
        Assert.assertArrayEquals(setting.getMetaValues(),new String[]{"true","true","true"});
    }

    @Test
    public void testGetAllStaff() {
        // Arrange
        String userId = "user1";
        String centerIds = "center1,center2";
        boolean includeAgencyOwner = true;

        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole("AGENCY_ADMIN");

        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("agency1");

        UserModel userModel = new UserModel();
        userModel.setId("user2");
        userEntity.setRole("AGENCY_ADMIN");

        AgencyMetaDataEntity agencyMetaDataEntity = new AgencyMetaDataEntity();
        agencyMetaDataEntity.setMetaKey("ENABLE_SITE_ADMIN_MANAGEMENT_ACCESS");
        agencyMetaDataEntity.setMetaValue("true");

        UsersMetaDataEntity usersMetaDataEntity = new UsersMetaDataEntity();
        usersMetaDataEntity.setUserId("user2");
        usersMetaDataEntity.setMetaKey("LANGUAGE");
        usersMetaDataEntity.setMetaValue("en");

        DLLLanguageModel dllLanguageModel = new DLLLanguageModel();
        dllLanguageModel.setCode("en");
        dllLanguageModel.setName("English");
        // 处理site admin
        when(userProvider.checkUser(userId.toUpperCase())).thenReturn(userEntity);
        when(userProvider.getAgencyByUserId(userId.toUpperCase())).thenReturn(agencyModel);
        when(userDao.getAgencyAdminsByAgencyId(agencyModel.getId())).thenReturn(new ArrayList<>());
        List<UserModel> uModelsSite = new ArrayList<>();
        UserModel siteAdmin = new UserModel();
        siteAdmin.setId("siteAdmin");
        siteAdmin.setRole("SITE_ADMIN");
        uModelsSite.add(siteAdmin);
        when(userDao.getSiteAdminsByCenterIds(anyList())).thenReturn(uModelsSite);

//        when(userDao.getAgencyAdminsByAgencyId(agencyModel.getId())).thenReturn(Collections.singletonList(userModel));
        when(agencyDao.getMeta(agencyModel.getId(), "ENABLE_SITE_ADMIN_MANAGEMENT_ACCESS")).thenReturn(agencyMetaDataEntity);
        when(dllDao.getLanguageModel()).thenReturn(Collections.singletonList(dllLanguageModel));

        // Act
        List<AgencyModel> result = userService.getAllStaff(userId, centerIds, includeAgencyOwner);

        // Assert
        verify(userProvider).checkUser(userId.toUpperCase());
        verify(userProvider).getAgencyByUserId(userId.toUpperCase());
        verify(userDao).getAgencyAdminsByAgencyId(agencyModel.getId());
        verify(agencyDao).getMeta(agencyModel.getId(), "ENABLE_SITE_ADMIN_MANAGEMENT_ACCESS");
        verify(usersMetaDataDao).getUserMateByUserIdsAndMetaKey(Arrays.asList(siteAdmin.getId()), "LANGUAGE");
        verify(dllDao).getLanguageModel();

        assert (result.size() == 1);
        assert (result.get(0).getId().equals(agencyModel.getId()));
        assert (result.get(0).getAgencyUsers().size() == 1);
        assert (result.get(0).getAgencyUsers().get(0).getId().equals(siteAdmin.getId()));
    }

    @Test
    public void testGetUserFiles() {
        // Arrange
        String userId = "user1";
        String type = "SFTP_VERIFY_DATA_EXCEL";
        String groupId = "group1";
        List<UserFileEntity> expectedUserFileEntities = new ArrayList<>();
        UserFileEntity userFileEntity = new UserFileEntity();
        userFileEntity.setUserId(userId);
        userFileEntity.setGroupId(groupId);
        userFileEntity.setType(type);

        expectedUserFileEntities.add(userFileEntity);
        when(userDao.getUserFileByUserGroupType(userId, groupId, type)).thenReturn(expectedUserFileEntities);

        // Act
        List<UserFileModel> result = userService.getUserFiles(userId, type, groupId, null);

        // Assert
        assertEquals(expectedUserFileEntities.size(), result.size());
        verify(userDao, times(1)).getUserFileByUserGroupType(userId, groupId, type);
    }

    @Test
    public void testGetUserFiles2() {
        // Arrange
        String userId = "user1";
        String type = "SFTP_VERIFY_DATA_EXCEL";
        String groupId = "group1";
        List<UserFileEntity> expectedUserFileEntities = new ArrayList<>();
        UserFileEntity userFileEntity = new UserFileEntity();
        userFileEntity.setUserId(userId);
        userFileEntity.setGroupId(groupId);
        userFileEntity.setType(type);
        com.learninggenie.common.data.entity.MediaEntity mediaEntity = new com.learninggenie.common.data.entity.MediaEntity();
        mediaEntity.setRelativePath("aaa");
        mediaEntity.setId("111");
        userFileEntity.setMedia(mediaEntity);
        when(fileSystem.getPublicUrl("aaa")).thenReturn("http://test.com/aaa");
        expectedUserFileEntities.add(userFileEntity);
        when(userDao.getUserFileByUserGroupType(userId, groupId, type)).thenReturn(expectedUserFileEntities);
        MediaEntity media = new MediaEntity();
        media.setRelativePath("aaa");
        media.setId("111");
        when(mediaEntityDao.getById(userFileEntity.getMedia().getId())).thenReturn(media);
        // Act
        List<UserFileModel> result = userService.getUserFiles(userId, type, groupId, null);

        // Assert
        assertEquals(expectedUserFileEntities.size(), result.size());
        verify(userDao, times(1)).getUserFileByUserGroupType(userId, groupId, type);
        assertEquals("http://test.com/aaa", result.get(0).getUrl());
    }

    /**
     * 测试获取学习故事模板
     */
    @Test
    public void testGetLearningStoryTemplates() {
        // 用户 id
        String userId = "USER_ID";
        // userData
        String userData = "[  \n" +
                "        {\"title\": \"Story\",  \n" +
                "         \"placeholder\": \"Describe what you have observed, highlighting children's initiatives, interactions, and key moments of discovery or creativity.\",  \n" +
                "         \"sortIndex\": 0},  \n" +
                "        {\"title\": \"What learning is happening?\",  \n" +
                "         \"placeholder\": \"Describe what you have observed in an activity, focusing on children's behavior that aligns with the measures you will be selecting.\",  \n" +
                "         \"sortIndex\": 1},  \n" +
                "        {\"title\": \"What comes next? / Future learning opportunities:\",  \n" +
                "         \"placeholder\": \"Outline the next steps to extend and support children's future learning.\",  \n" +
                "         \"sortIndex\": 2}  \n" +
                "    ]";
        String appData = "[  \n" +
                "        {\"title\": \"Story\",  \n" +
                "         \"placeholder\": \"Describe what you have observed, highlighting children's initiatives, interactions, and key moments of discovery or creativity.\",  \n" +
                "         \"sortIndex\": 0},  \n" +
                "        {\"title\": \"What learning is happening?\",  \n" +
                "         \"placeholder\": \"Describe what you have observed in an activity, focusing on children's behavior that aligns with the measures you will be selecting.\",  \n" +
                "         \"sortIndex\": 1},  \n" +
                "        {\"title\": \"What comes next? / Future learning opportunities:\",  \n" +
                "         \"placeholder\": \"Outline the next steps to extend and support children's future learning.\",  \n" +
                "         \"sortIndex\": 2},  \n" +
                "        {\"title\": \"How do you plan future lessons?\",  \n" +
                "         \"placeholder\": \"Discuss how you intend to plan future lessons based on children's behavior.\",  \n" +
                "         \"sortIndex\": 3}  \n" +
                "    ]";

        String userData02 = "[  \n" +
                "        {\"title\": \"Story\",  \n" +
                "         \"placeholder\": \"Describe what you have observed, highlighting children''s initiatives, interactions, and key moments of discovery or creativity.\",  \n" +
                "         \"sortIndex\": 0},  \n" +
                "        {\"title\": \"What learning is happening?\",  \n" +
                "         \"placeholder\": \"Describe what you have observed in an activity, focusing on children's behavior that aligns with the measures you will be selecting.\",  \n" +
                "         \"sortIndex\": 1},  \n" +
                "        {\"title\": \"What comes next? / Future learning opportunities:\",  \n" +
                "         \"placeholder\": \"Outline the next steps to extend and support children's future learning.\",  \n" +
                "         \"sortIndex\": 2},  \n" +
                "        {\"title\": \"How do you plan future lessons?\",  \n" +
                "         \"placeholder\": \"Discuss how you intend to plan future lessons based on children's behavior.\",  \n" +
                "         \"sortIndex\": 3}  \n" +
                "    ]";
        UserMetaDataEntity userMetaDataEntity = new UserMetaDataEntity();
        UserEntity user = new UserEntity();
        user.setId(userId);
        userMetaDataEntity.setUser(user);
        userMetaDataEntity.setMetaKey("USER_LEARNING_STORY_TEMPLATE");
        userMetaDataEntity.setMetaValue(userData);

        AppMetadataEntity appMetadata = new AppMetadataEntity();
        appMetadata.setMetaKey("LEARNING_STORY_TEMPLATE");
        appMetadata.setMetaValue(appData);

        // 设置模拟对象的行为
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userDao.getMetaData(userId, "USER_LEARNING_STORY_TEMPLATE")).thenReturn(userMetaDataEntity);
        when(metaDao.getAppMetaEntity("LEARNING_STORY_TEMPLATE")).thenReturn(appMetadata);
        LearningStoryTemplatesResponse response = userService.getLearningStoryTemplates();
        // 验证返回的 GetUserOpenResponse 是否符合预期
        Assert.assertEquals(3, response.getTemplates().size());
        Assert.assertEquals("Story", response.getTemplates().get(0).getTitle());
        Assert.assertEquals("Describe what you have observed, highlighting children's initiatives, interactions, and key moments of discovery or creativity.", response.getTemplates().get(0).getPlaceholder());
        Assert.assertEquals(0, response.getTemplates().get(0).getSortIndex().intValue());
        Assert.assertEquals("What learning is happening?", response.getTemplates().get(1).getTitle());
        Assert.assertEquals("Describe what you have observed in an activity, focusing on children's behavior that aligns with the measures you will be selecting.", response.getTemplates().get(1).getPlaceholder());
        Assert.assertEquals(1, response.getTemplates().get(1).getSortIndex().intValue());
        Assert.assertEquals("What comes next? / Future learning opportunities:", response.getTemplates().get(2).getTitle());
        Assert.assertEquals("Outline the next steps to extend and support children's future learning.", response.getTemplates().get(2).getPlaceholder());
        Assert.assertEquals(2, response.getTemplates().get(2).getSortIndex().intValue());

        when(userDao.getMetaData(userId, "USER_LEARNING_STORY_TEMPLATE")).thenReturn(null);
        when(metaDao.getAppMetaEntity("LEARNING_STORY_TEMPLATE")).thenReturn(appMetadata);
        LearningStoryTemplatesResponse response1 = userService.getLearningStoryTemplates();
        // 验证返回的 GetUserOpenResponse 是否符合预期
        Assert.assertEquals(4, response1.getTemplates().size());
        Assert.assertEquals("Story", response1.getTemplates().get(0).getTitle());
        Assert.assertEquals("Describe what you have observed, highlighting children's initiatives, interactions, and key moments of discovery or creativity.", response1.getTemplates().get(0).getPlaceholder());
        Assert.assertEquals(0, response1.getTemplates().get(0).getSortIndex().intValue());
        Assert.assertEquals("What learning is happening?", response1.getTemplates().get(1).getTitle());
        Assert.assertEquals("Describe what you have observed in an activity, focusing on children's behavior that aligns with the measures you will be selecting.", response1.getTemplates().get(1).getPlaceholder());
        Assert.assertEquals(1, response1.getTemplates().get(1).getSortIndex().intValue());
        Assert.assertEquals("What comes next? / Future learning opportunities:", response1.getTemplates().get(2).getTitle());
        Assert.assertEquals("Outline the next steps to extend and support children's future learning.", response1.getTemplates().get(2).getPlaceholder());
        Assert.assertEquals(2, response1.getTemplates().get(2).getSortIndex().intValue());
        Assert.assertEquals("How do you plan future lessons?", response1.getTemplates().get(3).getTitle());
        Assert.assertEquals("Discuss how you intend to plan future lessons based on children's behavior.", response1.getTemplates().get(3).getPlaceholder());
        Assert.assertEquals(3, response1.getTemplates().get(3).getSortIndex().intValue());


        UserMetaDataEntity userMetaDataEntity1 = new UserMetaDataEntity();
        UserEntity user1 = new UserEntity();
        user1.setId(userId);
        userMetaDataEntity1.setUser(user);
        userMetaDataEntity1.setMetaKey("USER_LEARNING_STORY_TEMPLATE");
        userMetaDataEntity1.setMetaValue(userData02);
        when(userDao.getMetaData(userId, "USER_LEARNING_STORY_TEMPLATE")).thenReturn(userMetaDataEntity1);
        when(metaDao.getAppMetaEntity("LEARNING_STORY_TEMPLATE")).thenReturn(appMetadata);
        LearningStoryTemplatesResponse response2 = userService.getLearningStoryTemplates();
        // 验证返回的 GetUserOpenResponse 是否符合预期
        Assert.assertEquals(4, response2.getTemplates().size());
        Assert.assertEquals("Story", response2.getTemplates().get(0).getTitle());
        Assert.assertEquals("Describe what you have observed, highlighting children's initiatives, interactions, and key moments of discovery or creativity.", response2.getTemplates().get(0).getPlaceholder());
        Assert.assertEquals(0, response2.getTemplates().get(0).getSortIndex().intValue());
        Assert.assertEquals("What learning is happening?", response2.getTemplates().get(1).getTitle());
        Assert.assertEquals("Describe what you have observed in an activity, focusing on children's behavior that aligns with the measures you will be selecting.", response2.getTemplates().get(1).getPlaceholder());
        Assert.assertEquals(1, response2.getTemplates().get(1).getSortIndex().intValue());
        Assert.assertEquals("What comes next? / Future learning opportunities:", response2.getTemplates().get(2).getTitle());
        Assert.assertEquals("Outline the next steps to extend and support children's future learning.", response2.getTemplates().get(2).getPlaceholder());
        Assert.assertEquals(2, response2.getTemplates().get(2).getSortIndex().intValue());
        Assert.assertEquals("How do you plan future lessons?", response2.getTemplates().get(3).getTitle());
        Assert.assertEquals("Discuss how you intend to plan future lessons based on children's behavior.", response2.getTemplates().get(3).getPlaceholder());
        Assert.assertEquals(3, response2.getTemplates().get(3).getSortIndex().intValue());
    }

    /**
     * 保存学习故事模板
     */
    @Test
    public void testSaveLearningStoryTemplates() {
        // 用户 id
        String userId = "USER_ID";
        // userData
        LearningStoryTemplatesResponse userData = new LearningStoryTemplatesResponse();
        List<LearningStoryModel> templates = new ArrayList<>();
        LearningStoryModel model1 = new LearningStoryModel();
        model1.setTitle("Story");
        model1.setSortIndex(1);
        templates.add(model1);
        userData.setTemplates(templates);
        // 设置模拟对象的行为
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        SuccessResponse response = userService.saveLearningStoryTemplates(userData);
        // 验证返回的 GetUserOpenResponse 是否符合预期
        verify(userDao).setMetaData(anyString(), anyString(), anyString());
        Assert.assertTrue(response.isSuccess());

    }

    /**
     * 单个修改员工密码
     */
    @Test
    public void testResetPassword() {
        String userId = "USER_ID";
        String manageId = "MANAGE_ID";
        String password = "123456789a";
        // 构建模拟参数
        UserEntity updateUser = new UserEntity();
        updateUser.setId(userId);
        updateUser.setEmail("<EMAIL>");
        updateUser.setUserName("updateUser");
        UserEntity manageUser = new UserEntity();
        manageUser.setId(manageId);
        manageUser.setEmail("<EMAIL>");
        // 模拟对象行为
        when(userRepository.findById(userId)).thenReturn(Optional.of(updateUser));
        when(userRepository.findById(manageId)).thenReturn(Optional.of(manageUser));
        when(dotNetPasswordEncoder.encode(password)).thenReturn("PASSWORD_SECURITY");
        when(mandrillService.getEmailTemplate(EmailTemplate.RESET_PASSWORD)).thenReturn("EMAIL_RESET_PASSWORD");
        // 执行
        PasswordModel passwordModel = userService.resetPassword(userId, manageId, password);
        // 断言验证结果
        Assert.assertEquals(password, passwordModel.getPassword());
    }

    /**
     * 批量修改员工密码
     */
    @Test
    public void testBatchUpdatePassword() {
        String userId = "USER_ID";
        String manageId = "MANAGE_ID";
        String password = "123456789a";
        // 构建模拟参数
        List<String> userIds = new ArrayList<>();
        userIds.add(userId);
        UpdatePasswordRequest updatePasswordRequest = new UpdatePasswordRequest();
        updatePasswordRequest.setUserIds(userIds);
        updatePasswordRequest.setPassword(password);
        UserEntity updateUser = new UserEntity();
        updateUser.setId(userId);
        updateUser.setEmail("<EMAIL>");
        updateUser.setUserName("updateUser");
        UserEntity manageUser = new UserEntity();
        manageUser.setId(manageId);
        manageUser.setEmail("<EMAIL>");
        manageUser.setLastName("LAST_NAME");
        // 模拟对象行为
        when(userRepository.findById(manageId)).thenReturn(Optional.of(manageUser));
        when(userRepository.findById(userId)).thenReturn(Optional.of(updateUser));
        when(dotNetPasswordEncoder.encode(password)).thenReturn("PASSWORD_SECURITY");
        when(mandrillService.getEmailTemplate(EmailTemplate.RESET_PASSWORD)).thenReturn("EMAIL_RESET_PASSWORD");
        // 执行
        userService.batchUpdatePassword(manageId, updatePasswordRequest);
    }

    /**
     * 查询国家下州信息
     */
    @Test
    public void testGetCountryState(){
        //模拟请求
        String country = "United States";

        // 模拟返回参数
        CountryStateResponse response = new CountryStateResponse();
        AppMetadataEntity entity = new AppMetadataEntity();
        String metaValue = "{\n" +
                "  \"name\": \"United States\",\n" +
                "  \"alpha_2\": \"US\",\n" +
                "  \"regions\": [\n" +
                "    {\n" +
                "      \"name\": \"Alaska\",\n" +
                "      \"code\": \"AK\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"name\": \"Alabama\",\n" +
                "      \"code\": \"AL\"\n" +
                "    }\n" +
                "  ]\n" +
                "}";
        entity.setMetaValue(metaValue);
        when(metaDao.getAppMetaEntity(anyString())).thenReturn(entity);

        // 调用方法
        CountryStateResponse result = userService.getCountryState(country);

        // 验证结果
        Assert.assertEquals("United States", result.getName());
    }

    @Test
    public void testGetGranteeStaffCenterByUserId() {
        // 用户 id
        String userId = "USER_ID";
        // 机构 id
        String agencyId = "AGENCY_ID";

        // 模拟数据设置
        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setId("CENTER_ID");
        centerEntity.setName("CENTER_NAME");
        List<CenterEntity> centerEntities = new ArrayList<>();
        centerEntities.add(centerEntity);

        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);

        List<UserModel> teachers = new ArrayList<>();
        UserModel teacher1 = new UserModel();
        teacher1.setId("USER_ID1");
        teacher1.setEmail("email_01");
        teachers.add(teacher1);

        List<UserModel> codesTeachers = new ArrayList<>();
        UserModel codesTeacher1 = new UserModel();
        codesTeacher1.setId("USER_ID2");
        codesTeacher1.setEmail("email_02");
        codesTeachers.add(codesTeacher1);

        List<UserModel> detachedTeachers = new ArrayList<>();
        UserModel detachedTeacher1 = new UserModel();
        detachedTeacher1.setId("USER_ID3");
        detachedTeacher1.setEmail("email_03");
        detachedTeachers.add(detachedTeacher1);

        List<String> centerIds = Collections.singletonList("CENTER_ID");
        List<String> enrollmentIds = Collections.singletonList("ENROLLMENT_ID");
        List<String> userIds = Arrays.asList("USER_ID1", "USER_ID2", "USER_ID3");

        List<AgencyEntity> agencyEntities = new ArrayList<>();
        AgencyEntity agencyEntity = new AgencyEntity();
        agencyEntity.setId(agencyId);
        agencyEntities.add(agencyEntity);


        // 设置模拟对象的行为
        when(userProvider.isGrantee(userId)).thenReturn(true);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        when(userProvider.getCurrentAgencyId()).thenReturn(agencyId);
        when(centerDao.getAllByAgencyId(agencyId)).thenReturn(centerEntities);
        when(userDao.getTeachersByCenterIds(centerIds)).thenReturn(teachers);
        when(userDao.getTeachersCodesByCenterIds(centerIds)).thenReturn(codesTeachers);
        when(userDao.getDetachedTeachers(centerIds)).thenReturn(detachedTeachers);
        when(studentDao.getEnrollmentIdsByMetaKeyAndMetaValues("SPECIAL_TEACHER", userIds)).thenReturn(enrollmentIds);
        when(centerDao.getCentersByChildIds(StringUtil.convertIdsToString(enrollmentIds))).thenReturn(centerEntities);
        when(agencyDao.getByCenterIds(centerIds)).thenReturn(agencyEntities);

        // 调用被测方法
        AgencyCenterInfoModel agencyCenterInfoModel = userService.getGranteeStaffCenterByUserId(userId, agencyId);

        // 验证结果
        Assert.assertNotNull(agencyCenterInfoModel);
        Assert.assertEquals(2, agencyCenterInfoModel.getCenterList().size());
        CenterModel centerModel = agencyCenterInfoModel.getCenterList().get(0);
        Assert.assertEquals("CENTER_ID", centerModel.getId());
        Assert.assertEquals("CENTER_NAME", centerModel.getName());
        Assert.assertEquals(1, agencyCenterInfoModel.getAgencyList().size());
        AgencyEntity agency = agencyCenterInfoModel.getAgencyList().get(0);
        Assert.assertEquals("AGENCY_ID", agency.getId());
    }

    @Test
    public void testGetGranteeTeacherByCenterId() {
        // 用户 id
        String userId = "USER_ID";
        // center id
        String centerId = "CENTER_ID";
        // center id
        String centerId02 = "CENTER_ID_02";

        // 学校实体
        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setId(centerId);
        centerEntity.setName("CENTER_NAME");

        // 机构信息
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("AGENCY_ID");

        // 员工信息
        List<UserModel> userModels = new ArrayList<>();
        UserModel userModel1 = new UserModel();
        userModel1.setId("USER_ID1");
        userModel1.setEmail("email_01");
        userModels.add(userModel1);

        // 模拟请求
        when(centerDao.getCenter(centerId)).thenReturn(centerEntity);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        when(centerDao.getCentersByAgencyId(agencyModel.getId())).thenReturn(Arrays.asList(centerEntity));
        when(userDao.getTeachersByCenterId(centerId)).thenReturn(userModels);
        List<TeacherResponse> teacherResponses = userService.getGranteeTeacherByCenterId(userId, centerId);
        Assert.assertEquals(1, teacherResponses.size());

        List<UserModel> teachers = new ArrayList<>();
        UserModel teacher1 = new UserModel();
        teacher1.setId("USER_ID1");
        teacher1.setEmail("email_01");
        teachers.add(teacher1);

        List<UserModel> codesTeachers = new ArrayList<>();
        UserModel codesTeacher1 = new UserModel();
        codesTeacher1.setId("USER_ID2");
        codesTeacher1.setEmail("email_02");
        codesTeachers.add(codesTeacher1);

        List<UserModel> detachedTeachers = new ArrayList<>();
        UserModel detachedTeacher1 = new UserModel();
        detachedTeacher1.setId("USER_ID3");
        detachedTeacher1.setEmail("email_03");
        detachedTeachers.add(detachedTeacher1);

//        List<String> centerIds = Collections.singletonList("CENTER_ID");
//        List<String> enrollmentIds = Collections.singletonList("ENROLLMENT_ID");
//        List<String> userIds = Arrays.asList("USER_ID1", "USER_ID2", "USER_ID3");

        List<String> centerIds = new ArrayList<>();
        centerIds.add("CENTER_ID_01");

        List<String> userIds = new ArrayList<>();
        userIds.add("USER_ID1");
        userIds.add("USER_ID2");
        userIds.add("USER_ID3");

        List<String> enrollmentIds = new ArrayList<>();
        enrollmentIds.add("ENROLLMENT_ID");
        enrollmentIds.add("ENROLLMENT_ID_01");

        List<EnrollmentMetaDataEntity> enrollmentMetaDataEntities = new ArrayList<>();
        EnrollmentMetaDataEntity enrollmentMetaDataEntity = new EnrollmentMetaDataEntity();
        EnrollmentEntity enrollment = new EnrollmentEntity();
        enrollment.setId("ENROLLMENT_ID");
        enrollmentMetaDataEntity.setEnrollment(enrollment);
        enrollmentMetaDataEntity.setMetaKey("SPECIAL_TEACHER");
        enrollmentMetaDataEntity.setMetaValue("USER_ID1");
        enrollmentMetaDataEntities.add(enrollmentMetaDataEntity);

        EnrollmentMetaDataEntity enrollmentMetaDataEntity2 = new EnrollmentMetaDataEntity();
        EnrollmentEntity enrollment2 = new EnrollmentEntity();
        enrollment2.setId("ENROLLMENT_ID_01");
        enrollmentMetaDataEntity2.setEnrollment(enrollment2);
        enrollmentMetaDataEntity2.setMetaKey("SPECIAL_TEACHER");
        enrollmentMetaDataEntity2.setMetaValue("USER_ID2");
        enrollmentMetaDataEntities.add(enrollmentMetaDataEntity2);

        CenterEntity centerEntity1 = new CenterEntity();
        centerEntity.setId(centerId02);
        centerEntity.setName("CENTER_NAME");

        when(centerDao.getCenter(centerId)).thenReturn(centerEntity);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        when(centerDao.getCentersByAgencyId(agencyModel.getId())).thenReturn(Arrays.asList(centerEntity1));
//        when(userDao.getTeachersByCenterIds(centerIds)).thenReturn(teachers);
//        when(userDao.getTeachersCodesByCenterIds(centerIds)).thenReturn(codesTeachers);
//        when(userDao.getDetachedTeachers(centerIds)).thenReturn(detachedTeachers);
//        when(studentDao.getEnrollmentIdsByMetaKeyAndMetaValues("SPECIAL_TEACHER", userIds)).thenReturn(enrollmentIds);
//        when(studentDao.getChildIdsByCenterIds(Collections.singletonList(centerId02))).thenReturn(enrollmentIds);
//        when(studentDao.getMetaByEnrollmentIds(enrollmentIds, "SPECIAL_TEACHER")).thenReturn(enrollmentMetaDataEntities);
//        when(userDao.getUsersByUserIds(userIds)).thenReturn(userModels);
        List<TeacherResponse> teacherResponses1 = userService.getGranteeTeacherByCenterId(userId, centerId);
        Assert.assertEquals(0, teacherResponses1.size());

    }

    /**
     * 测试 getBatchUpdateRateNeedGuide 方法
     */
    @Test
    public void testGetBatchUpdateRateNeedGuide() {
        // 准备测试数据
        String userId = "test-user-id";
        String agencyId = "test-agency-id";
        
        // Mock 当前用户
        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setUsername(userId);
        currentUser.setAgencyId(agencyId);
        
        // Mock InkindSchoolYearEntity - 创建时间早于2025年7月1日
        InkindSchoolYearEntity schoolYearEntity = new InkindSchoolYearEntity();
        schoolYearEntity.setId("school-year-id");
        schoolYearEntity.setAgencyId(agencyId);
        schoolYearEntity.setCreateAtUtc(TimeUtil.parse("2024-01-01", TimeUtil.format10));
        
        List<InkindSchoolYearEntity> schoolYearEntities = Arrays.asList(schoolYearEntity);
        
        // 设置 Mock 行为
        when(userProvider.getCurrentUser()).thenReturn(currentUser);
        when(userMetaDao.getMeta(userId, UserMetaKey.USER_BATCH_UPDATE_SUBMITTED_IN_KIND_RATE_GUIDE.toString()))
            .thenReturn(null); // 用户元数据不存在
        when(inkindDao.getSchoolYearByAgencyId(agencyId)).thenReturn(schoolYearEntities);
        
        // 执行测试
        InKindBatchUpdateRateGuideResponse result = userService.getBatchUpdateRateNeedGuide();
        
        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isShowBatchUpdateSubmittedInKindRateGuide());
    }
    
    /**
     * 测试 getBatchUpdateRateNeedGuide 方法 - 用户元数据已存在
     */
    @Test
    public void testGetBatchUpdateRateNeedGuide_UserMetaExists() {
        String userId = "test-user-id";
        String agencyId = "test-agency-id";
        
        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setUsername(userId);
        currentUser.setAgencyId(agencyId);
        
        UsersMetaDataEntity usersMeta = new UsersMetaDataEntity();
        usersMeta.setId("meta-id");
        usersMeta.setMetaKey(UserMetaKey.USER_BATCH_UPDATE_SUBMITTED_IN_KIND_RATE_GUIDE.toString());
        
        when(userProvider.getCurrentUser()).thenReturn(currentUser);
        when(userMetaDao.getMeta(userId, UserMetaKey.USER_BATCH_UPDATE_SUBMITTED_IN_KIND_RATE_GUIDE.toString()))
            .thenReturn(usersMeta);
        
        InKindBatchUpdateRateGuideResponse result = userService.getBatchUpdateRateNeedGuide();
        
        Assert.assertNotNull(result);
        Assert.assertFalse(result.isShowBatchUpdateSubmittedInKindRateGuide());
    }
    
    /**
     * 测试 getBatchUpdateRateNeedGuide 方法 - 多个schoolYearEntities的排序逻辑
     */
    @Test
    public void testGetBatchUpdateRateNeedGuide_MultipleSchoolYears() {
        String userId = "test-user-id";
        String agencyId = "test-agency-id";
        
        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setUsername(userId);
        currentUser.setAgencyId(agencyId);
        
        // Mock 多个InkindSchoolYearEntity，其中最早的创建时间早于2025年7月1日
        InkindSchoolYearEntity schoolYearEntity1 = new InkindSchoolYearEntity();
        schoolYearEntity1.setId("school-year-id-1");
        schoolYearEntity1.setAgencyId(agencyId);
        schoolYearEntity1.setCreateAtUtc(TimeUtil.parse("2025-08-01", TimeUtil.format10)); // 晚的日期
        
        InkindSchoolYearEntity schoolYearEntity2 = new InkindSchoolYearEntity();
        schoolYearEntity2.setId("school-year-id-2");
        schoolYearEntity2.setAgencyId(agencyId);
        schoolYearEntity2.setCreateAtUtc(TimeUtil.parse("2024-01-01", TimeUtil.format10)); // 早的日期
        
        InkindSchoolYearEntity schoolYearEntity3 = new InkindSchoolYearEntity();
        schoolYearEntity3.setId("school-year-id-3");
        schoolYearEntity3.setAgencyId(agencyId);
        schoolYearEntity3.setCreateAtUtc(TimeUtil.parse("2025-06-01", TimeUtil.format10)); // 中间的日期
        
        List<InkindSchoolYearEntity> schoolYearEntities = Arrays.asList(schoolYearEntity1, schoolYearEntity2, schoolYearEntity3);
        
        // 设置 Mock 行为
        when(userProvider.getCurrentUser()).thenReturn(currentUser);
        when(userMetaDao.getMeta(userId, UserMetaKey.USER_BATCH_UPDATE_SUBMITTED_IN_KIND_RATE_GUIDE.toString()))
            .thenReturn(null); // 用户元数据不存在
        when(inkindDao.getSchoolYearByAgencyId(agencyId)).thenReturn(schoolYearEntities);
        
        // 执行测试
        InKindBatchUpdateRateGuideResponse result = userService.getBatchUpdateRateNeedGuide();
        
        // 验证结果 - 应该使用最早的创建日期(2024-01-01)，早于2025年7月1日，所以应该显示引导
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isShowBatchUpdateSubmittedInKindRateGuide());
    }
    
    /**
     * 测试 getBatchUpdateRateNeedGuide 方法 - 最早创建日期晚于2025年7月1日
     */
    @Test
    public void testGetBatchUpdateRateNeedGuide_EarliestDateAfterCutoff() {
        String userId = "test-user-id";
        String agencyId = "test-agency-id";
        
        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setUsername(userId);
        currentUser.setAgencyId(agencyId);
        
        // Mock InkindSchoolYearEntity - 创建时间晚于2025年7月1日
        InkindSchoolYearEntity schoolYearEntity = new InkindSchoolYearEntity();
        schoolYearEntity.setId("school-year-id");
        schoolYearEntity.setAgencyId(agencyId);
        schoolYearEntity.setCreateAtUtc(TimeUtil.parse("2025-08-01", TimeUtil.format10));
        
        List<InkindSchoolYearEntity> schoolYearEntities = Arrays.asList(schoolYearEntity);
        
        // 设置 Mock 行为
        when(userProvider.getCurrentUser()).thenReturn(currentUser);
        when(userMetaDao.getMeta(userId, UserMetaKey.USER_BATCH_UPDATE_SUBMITTED_IN_KIND_RATE_GUIDE.toString()))
            .thenReturn(null); // 用户元数据不存在
        when(inkindDao.getSchoolYearByAgencyId(agencyId)).thenReturn(schoolYearEntities);
        
        // 执行测试
        InKindBatchUpdateRateGuideResponse result = userService.getBatchUpdateRateNeedGuide();
        
        // 验证结果 - 当最早创建日期晚于2025年7月1日时，应该不显示引导
        Assert.assertNotNull(result);
        Assert.assertFalse(result.isShowBatchUpdateSubmittedInKindRateGuide());
    }
    
    /**
     * 测试 getBatchUpdateRateNeedGuide 方法 - schoolYearEntities为空
     */
    @Test
    public void testGetBatchUpdateRateNeedGuide_SchoolYearEntitiesEmpty() {
        String userId = "test-user-id";
        String agencyId = "test-agency-id";
        
        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setUsername(userId);
        currentUser.setAgencyId(agencyId);
        
        // 设置 Mock 行为
        when(userProvider.getCurrentUser()).thenReturn(currentUser);
        when(userMetaDao.getMeta(userId, UserMetaKey.USER_BATCH_UPDATE_SUBMITTED_IN_KIND_RATE_GUIDE.toString()))
            .thenReturn(null); // 用户元数据不存在
        when(inkindDao.getSchoolYearByAgencyId(agencyId)).thenReturn(new ArrayList<>());
        
        // 执行测试
        InKindBatchUpdateRateGuideResponse result = userService.getBatchUpdateRateNeedGuide();
        
        // 验证结果 - 当schoolYearEntities为空时，应该不显示引导
        Assert.assertNotNull(result);
        Assert.assertFalse(result.isShowBatchUpdateSubmittedInKindRateGuide());
    }
    
    /**
     * 测试 getBatchUpdateRateNeedGuide 方法 - 当前用户为null
     */
    @Test(expected = BusinessException.class)
    public void testGetBatchUpdateRateNeedGuide_CurrentUserNull() {
        when(userProvider.getCurrentUser()).thenReturn(null);
        userService.getBatchUpdateRateNeedGuide();
    }
}


package com.learninggenie.api.service.impl;

import com.amazonaws.services.sns.model.CreatePlatformEndpointResult;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.common.data.dao.EndPoint;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.entity.UserEntity;
import com.learninggenie.common.data.enums.UserRole;
import com.learninggenie.common.data.model.EndPointEntity;
import com.learninggenie.common.data.repository.UserRepository;
import com.learninggenie.common.notification.LearningGenieSNSClient;
import com.learninggenie.common.notification.Platform;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Created by zjj on 2016/6/26.
 */
@RunWith(MockitoJUnitRunner.class)
public class NotificationServiceImplTest{
    @Mock
    private LearningGenieSNSClient learningGenieSNSClient;
    @Mock
    private EndPoint endPoint;
    @Mock
    private UserDaoImpl userDao;
    @Mock
    private UserRepository userRepository;
    @Mock
    private UserProvider userProvider;
    @InjectMocks
    private NotificationServiceImpl notificationService;

    /**
     * 创建endpoint
     * zjj 2016.6.26
     */
    @Test
    public void testCreatEndPoint(){
        String userId = "001";
        String type = "GCM";
        String token = "a123";
        String deviceId = "d001";
        CreatePlatformEndpointResult result = new CreatePlatformEndpointResult();
        result.setEndpointArn("123456");
        when(learningGenieSNSClient.createPlatformEndpoint(userId,token,type)).thenReturn(result);
        notificationService.createEndPoint(userId, token, deviceId, type);
        verify(endPoint,times(1)).saveEndPoint(any(EndPointEntity.class));
    }

    /**
     * 根据用户的id推送
     * zjj 2016.6.26
     */
    @Test
    public void testPushMessageByUserId(){
        String userId = "u001";
        String message = "message";
        List<EndPointEntity> endPointEntityList = new ArrayList<>();
        EndPointEntity endPointEntity = new EndPointEntity();
        endPointEntity.setUid(userId);
        endPointEntity.setDeviceToken("t001");
        endPointEntity.setType("GCM");
        endPointEntity.setCreateUtc("d001");
        endPointEntity.setEndPointARN("arn001");
        endPointEntityList.add(endPointEntity);
        when(endPoint.getEndPointByUserId(userId)).thenReturn(endPointEntityList);
        notificationService.pushNotificationByUserId(userId,message,null,null);
        verify(learningGenieSNSClient,times(1)).pushNotification(anyString(), any(Platform.class), anyString());
    }

    /**
     * 根据用户的邮箱进行推送
     * zjj 2016.6.26
     */
    @Test
    public void testPushMessageByUserEmail(){
        String userId = "u001";
        String message = "message";
        String email = "email001";
        UserEntity user = new UserEntity();
        user.setId(userId);
        user.setEmail(email);
        when(userProvider.checkUserByEmail(email)).thenReturn(user);
        List<EndPointEntity> endPointEntityList = new ArrayList<>();
        EndPointEntity endPointEntity = new EndPointEntity();
        endPointEntity.setUid(userId);
        endPointEntity.setDeviceToken("t001");
        endPointEntity.setType("GCM");
        endPointEntity.setCreateUtc("d001");
        endPointEntity.setEndPointARN("arn001");
        endPointEntityList.add(endPointEntity);
        when(endPoint.getEndPointByUserId(userId)).thenReturn(endPointEntityList);
        notificationService.pushNotificationByUserEmail(email,message,null,null);
        verify(learningGenieSNSClient,times(1)).pushNotification(anyString(), any(Platform.class), anyString());
    }

    /**
     * 根据角色进行推送
     * zjj 2016.6.26
     */
    @Test
    public void testPushMessageByUserRole(){
        String role = UserRole.AGENCY_ADMIN.toString();
        String userId = "u001";
        String message = "message";
        List<EndPointEntity> endPointEntityList = new ArrayList<>();
        EndPointEntity endPointEntity = new EndPointEntity();
        endPointEntity.setUid(userId);
        endPointEntity.setDeviceToken("t001");
        endPointEntity.setType("GCM");
        endPointEntity.setCreateUtc("d001");
        endPointEntity.setEndPointARN("arn001");
        endPointEntityList.add(endPointEntity);
        when(endPoint.getEndPointByRole(role)).thenReturn(endPointEntityList);
        notificationService.pushNotificationToOneRole(role,message,null,null);
        verify(learningGenieSNSClient,times(1)).pushNotification(anyString(), any(Platform.class), anyString());
    }

    /**
     * 推送给所有人
     * zjj 2016.6.26
     */
    @Test
    public void testPushMessageToEveryOne(){
        String userId = "u001";
        String message = "message";
        List<EndPointEntity> endPointEntityList = new ArrayList<>();
        EndPointEntity endPointEntity = new EndPointEntity();
        endPointEntity.setUid(userId);
        endPointEntity.setDeviceToken("t001");
        endPointEntity.setType("GCM");
        endPointEntity.setCreateUtc("d001");
        endPointEntity.setEndPointARN("arn001");
        endPointEntityList.add(endPointEntity);
        when(endPoint.getAllEndPoint()).thenReturn(endPointEntityList);
        notificationService.pushNotificationToAll(message,null,null);
        verify(learningGenieSNSClient,times(1)).pushNotification(anyString(), any(Platform.class), anyString());
    }

}
package com.learninggenie.api.service.lesson2.impl;

import com.google.protobuf.InvalidProtocolBufferException;
import com.learninggenie.api.model.lesson2.data.PeriodDomainAndMeasureModel;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.service.ReportService;
import com.learninggenie.common.data.dao.PortfolioDao;
import com.learninggenie.common.data.dao.StudentDao;
import com.learninggenie.common.data.model.ScoreTemplateEntity;
import com.learninggenie.common.data.model.SnapshotResponse;
import com.learninggenie.common.data.model.StudentSnapshotEntity;
import com.learninggenie.common.score.RatingService;
import com.learninggenie.common.utils.StringUtil;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * DataReview 单元测试
 */
@RunWith(MockitoJUnitRunner.class)
public class DataReviewServiceImplTest {

    @InjectMocks
    private DataReviewServiceImpl dataReviewService;

    @Mock
    private UserProvider userProvider;

    @Mock
    private ReportService reportService;

    @Mock
    private RatingService ratingService;

    @Mock
    private StudentDao studentDao;

    @Mock
    private PortfolioDao portfolioDao;


    /**
     * 测试获取小孩的测评点分、小孩的领域分、班级的领域分、班级的测评点分
     * Case: 班级框架为非 DRDP 框架
     */
    @Test
    public void testGetChildSnapshotByGroupIdAndPeriodAlias() throws InvalidProtocolBufferException {
        // 模拟数据
        List<String> snapshotIds = new ArrayList<>();
        snapshotIds.add("snapshotId"); // 快照 ID
        // 周期快照数据 Map
        Map<String, List<SnapshotResponse>> allStudentSnapshots = new HashMap<>();
        List<SnapshotResponse> snapshotResponses = new ArrayList<>();
        SnapshotResponse snapshotResponse = new SnapshotResponse();
        snapshotResponse.setId("snapshotId");
        snapshotResponses.add(snapshotResponse);
        allStudentSnapshots.put("periodAlias", snapshotResponses);
        // 学生快照对象
        List<StudentSnapshotEntity> snapshotByGroup = new ArrayList<>();
        StudentSnapshotEntity studentSnapshotEntity = new StudentSnapshotEntity();
        studentSnapshotEntity.setFrameworkId("frameworkId");
        snapshotByGroup.add(studentSnapshotEntity);
        // 评分模板对象
        ScoreTemplateEntity scoreTemplate = new ScoreTemplateEntity();
        scoreTemplate.setPortfolioId("frameworkId");

        // 方法调用模拟
        Mockito.when(userProvider.getCurrentUserId()).thenReturn("userId"); // 当前用户 ID
        Mockito.when(reportService.getAllStudentSnapshots(Mockito.any(), Mockito.any())).thenReturn(allStudentSnapshots); // 获取所有学生快照
        Mockito.when(studentDao.getSnapshotsByIds(StringUtil.convertIdsToString(snapshotIds))).thenReturn(snapshotByGroup); // 获取学生快照
        Mockito.when(portfolioDao.loadScoreTemplate("frameworkId")).thenReturn(scoreTemplate); // 获取评分模板

        // 调用方法
        PeriodDomainAndMeasureModel childSnapshotByGroupIdAndPeriodAlias = dataReviewService.getChildSnapshotByGroupIdAndPeriodAlias("centerId", "groupId", "periodAlias", false, false, null);

        // 断言返回结果
        Assert.assertEquals(childSnapshotByGroupIdAndPeriodAlias, null); // 返回结果应为 null
    }
}

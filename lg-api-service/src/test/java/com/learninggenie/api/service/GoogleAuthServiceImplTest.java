package com.learninggenie.api.service;

import com.google.auth.oauth2.AccessToken;
import com.google.auth.oauth2.GoogleCredentials;
import com.learninggenie.api.model.SuccessResponse;
import com.learninggenie.api.model.googleauth.CheckGoogleAuthRequest;
import com.learninggenie.api.provider.GoogleAuthProvider;
import com.learninggenie.api.service.impl.GoogleAuthServiceImpl;
import com.learninggenie.common.cache.CacheService;
import com.learninggenie.common.data.dao.users.MetaDataDao;
import com.learninggenie.common.data.entity.users.MetaDataEntity;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.util.Date;
import java.util.HashMap;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class GoogleAuthServiceImplTest {

    @InjectMocks
    private GoogleAuthServiceImpl googleAuthService;

    @Mock
    private GoogleAuthProvider googleAuthProvider;

    @Mock
    private MetaDataDao usersMetaDataDao;

    @Mock
    private CacheService cacheService;

    @Test
    // 当认证码不为空时，检查Google认证是否返回成功响应
    public void checkGoogleAuthReturnsSuccessResponseWhenAuthCodeIsNotEmpty() throws IOException {
        // 创建一个带有虚拟数据的测试请求对象
        CheckGoogleAuthRequest request = new CheckGoogleAuthRequest();
        request.setUserId("testUserId"); // 设置用户ID
        request.setScope("testScope"); // 设置范围
        request.setScopeKey("testScopeKey"); // 设置范围键
        request.setAuthCode("testAuthCode"); // 设置认证码

        ReflectionTestUtils.setField(googleAuthService,"redirectUriMap",new HashMap<String, String>(){{
            put("MAGIC-CURRICULUM","https://magicurriculum.learning-genie.com");
            put("FOLC","https://futureoflearningcollaborative.org");
            put("CURRICULUM-PLUGIN","https://h5.curriculumgenie.learning-genie.com");
            put("DEFAULT","https://web.learning-genie.com");
        }});
        // 当调用googleAuthProvider的getRefreshToken方法时，返回虚拟的刷新令牌
        when(googleAuthProvider.getRefreshToken(any(String.class), anyList(), eq("https://web.learning-genie.com"))).thenReturn("testRefreshToken");

        // 创建虚拟的Google凭据对象
        GoogleCredentials credentials = new GoogleCredentials(new AccessToken("testAccessToken", new Date()));
        // 当调用googleAuthProvider的getGoogleCredentialsByRefreshToken方法时，返回虚拟的凭据对象
        when(googleAuthProvider.getGoogleCredentialsByRefreshToken(any(String.class), anyList())).thenReturn(credentials);

        // 执行Google认证服务的方法，并获取结果
        SuccessResponse result = googleAuthService.checkGoogleAuth(request, new MockHttpServletRequest());

        // 断言结果为成功
        assertTrue(result.isSuccess());
    }

    @Test
    // 当认证码为空且元数据为null时，检查Google认证是否返回失败响应
    public void checkGoogleAuthReturnsFailureResponseWhenAuthCodeIsEmptyAndMetaDataIsNull() throws IOException {
        // 创建一个带有虚拟数据的测试请求对象
        CheckGoogleAuthRequest request = new CheckGoogleAuthRequest();
        request.setUserId("testUserId"); // 设置用户ID
        request.setScope("testScope"); // 设置范围

        ReflectionTestUtils.setField(googleAuthService,"redirectUriMap",new HashMap<String, String>(){{
            put("MAGIC-CURRICULUM","https://magicurriculum.learning-genie.com");
            put("FOLC","https://futureoflearningcollaborative.org");
            put("CURRICULUM-PLUGIN","https://h5.curriculumgenie.learning-genie.com");
            put("DEFAULT","https://web.learning-genie.com");
        }});
        // 当调用usersMetaDataDao的getByUserIdAndMetaKey方法时，返回null
        when(usersMetaDataDao.getByUserIdAndMetaKey(any(String.class), anyString())).thenReturn(null);

        // 执行Google认证服务的方法，并获取结果
        SuccessResponse result = googleAuthService.checkGoogleAuth(request, new MockHttpServletRequest());

        // 断言结果为失败
        assertFalse(result.isSuccess());
    }

    @Test
    // 当刷新令牌为空时，检查 Google 认证是否返回失败响应
    public void checkGoogleAuthReturnsFailureResponseWhenRefreshTokenIsEmpty() throws IOException {
        // 创建一个带有虚拟数据的测试请求对象
        CheckGoogleAuthRequest request = new CheckGoogleAuthRequest();
        request.setUserId("testUserId"); // 设置用户 ID
        request.setScope("testScope"); // 设置范围

        ReflectionTestUtils.setField(googleAuthService,"redirectUriMap",new HashMap<String, String>(){{
            put("MAGIC-CURRICULUM","https://magicurriculum.learning-genie.com");
            put("FOLC","https://futureoflearningcollaborative.org");
            put("CURRICULUM-PLUGIN","https://h5.curriculumgenie.learning-genie.com");
            put("DEFAULT","https://web.learning-genie.com");
        }});
        // 创建虚拟的元数据实体对象，其值为空字符串
        MetaDataEntity metaDataEntity = new MetaDataEntity();
        metaDataEntity.setMetaValue("");
        // 当调用usersMetaDataDao的getByUserIdAndMetaKey方法时，返回虚拟的元数据实体对象
        when(usersMetaDataDao.getByUserIdAndMetaKey(any(String.class), anyString())).thenReturn(metaDataEntity);

        // 执行Google认证服务的方法，并获取结果
        SuccessResponse result = googleAuthService.checkGoogleAuth(request, new MockHttpServletRequest());

        // 断言结果为失败
        assertFalse(result.isSuccess());
    }

}

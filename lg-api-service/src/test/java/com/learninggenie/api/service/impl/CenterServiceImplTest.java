package com.learninggenie.api.service.impl;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import com.learninggenie.api.model.BatchSetupStaffSourceIdRequest;
import com.learninggenie.api.model.CenterSearchViewModel;
import com.learninggenie.api.model.DrdpCenterMetaDataModel;
import com.learninggenie.api.model.GetStateAbbreviationsResponse;
import com.learninggenie.api.model.GetStateCountiesResponse;
import com.learninggenie.api.model.center.CenterGroupEnrollment;
import com.learninggenie.api.model.center.CenterResponse;
import com.learninggenie.api.model.center.CreateCenterRequest;
import com.learninggenie.api.model.center.CreateCenterResponse;
import com.learninggenie.api.model.center.UpdateCenterRequest;
import com.learninggenie.api.provider.CenterProvider;
import com.learninggenie.api.provider.EnrollmentProvider;
import com.learninggenie.api.provider.PortfolioProvider;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.service.MediaService;
import com.learninggenie.api.service.UserService;
import com.learninggenie.api.util.ValidateUtil;
import com.learninggenie.common.comm.CommService;
import com.learninggenie.common.data.dao.AgencyDao;
import com.learninggenie.common.data.dao.CenterDao;
import com.learninggenie.common.data.dao.CentersMetaDataDao;
import com.learninggenie.common.data.dao.DomainDao;
import com.learninggenie.common.data.dao.EnrollmentDao;
import com.learninggenie.common.data.dao.GroupDao;
import com.learninggenie.common.data.dao.MediaDao;
import com.learninggenie.common.data.dao.PaymentPlanDao;
import com.learninggenie.common.data.dao.StudentDao;
import com.learninggenie.common.data.dao.UsersMetaDataDao;
import com.learninggenie.common.data.dao.impl.CenterMapper;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.dto.ClassesTreeDataDto;
import com.learninggenie.common.data.entity.AgencyEntity;
import com.learninggenie.common.data.entity.AgencyIdentifierEntity;
import com.learninggenie.common.data.entity.CenterMetaDataEntity;
import com.learninggenie.common.data.entity.CenterPaymentPlanEntity;
import com.learninggenie.common.data.entity.MediaEntity;
import com.learninggenie.common.data.entity.UserEntity;
import com.learninggenie.common.data.entity.UserMetaDataEntity;
import com.learninggenie.common.data.enums.UserRole;
import com.learninggenie.common.data.model.AgencyModel;
import com.learninggenie.common.data.model.CenterEntity;
import com.learninggenie.common.data.model.CenterGroup;
import com.learninggenie.common.data.model.CenterGroupModel;
import com.learninggenie.common.data.model.CenterGroupUser;
import com.learninggenie.common.data.model.CenterWithIdName;
import com.learninggenie.common.data.model.DomainEntity;
import com.learninggenie.common.data.model.EnrollmentModel;
import com.learninggenie.common.data.model.GroupEntity;
import com.learninggenie.common.data.model.RequestCenterModel;
import com.learninggenie.common.data.model.StageEntity;
import com.learninggenie.common.data.model.UpdateStaffModel;
import com.learninggenie.common.data.model.UserModel;
import com.learninggenie.common.data.repository.UserRepository;
import com.learninggenie.common.filesystem.FileSystem;
import com.learninggenie.common.library.LibrariesAreaService;

@RunWith(MockitoJUnitRunner.class)
public class CenterServiceImplTest {
    @Mock
    private CenterMapper centerMapper;

    @Mock
    private UserProvider userProvider;

    @Mock
    private CenterProvider centerProvider;

    @Mock
    private CenterDao centerDao;

    @Mock
    private FileSystem fileSystem;

    @Mock
    private PaymentPlanDao paymentPlanDao;

    @Mock
    private CentersMetaDataDao centersMetaDataDao;

    @Mock
    private UserDaoImpl userDao;

    @Mock
    private MediaDao mediaDao;

    @Mock
    private MediaService mediaService;

    @Mock
    private StudentDao studentDao;

    @Mock
    private GroupDao groupDao;

    @Mock
    private AgencyDao agencyDao;

    @Mock
    private PortfolioProvider portfolioProvider;

    @Mock
    private DomainDao domainDao;

    @Mock
    private EnrollmentProvider enrollmentProvider;

    @Mock
    private LibrariesAreaService librariesAreaService;

    @Mock
    private UserRepository userRepository;

    @Mock
    private EnrollmentDao enrollmentDao;

    // @Mock
    // private PaymentService paymentService;

    @Mock
    private CommService commService;

    @Mock
    private UserService userService;

    @Mock
    private UsersMetaDataDao usersMetaDataDao;

    @InjectMocks
    private CenterServiceImpl centerService;

    private static final String USER_ID = "U001";
    /**
     * 测试查询学校
     * 秦浩然 创建于2016/03/05
     */
    @Test
    public void testGetMergeCenters() {
        //构造模糊查询到的所有学校的老师
        List<CenterGroupUser> centersWithTeacher = new ArrayList<>();
        //学校c001中有班级g001中两个老师t001,t002,班级g004中有一个老师t003
        CenterGroupUser teacher1 = new CenterGroupUser();//teacher1
        teacher1.setCenterId("c001");
        teacher1.setCenterName("Center 001");
        teacher1.setUid("o001");
        teacher1.setEmail("<EMAIL>");
        teacher1.setFirstName("Owner1First");
        teacher1.setLastName("Owner1Last");
        teacher1.setUname("Owner1First Owner1Last");
        teacher1.setGroupId("g001");
        teacher1.setGroupName("Group 001");
        teacher1.setTid("t001");
        teacher1.setTemail("<EMAIL>");
        teacher1.setTfirstName("Teacher1First");
        teacher1.settLastName("Teacher1Last");
        teacher1.setTname("Teacher1First Teacher1Last");
        centersWithTeacher.add(teacher1);
        CenterGroupUser teacher2 = new CenterGroupUser();//teacher2
        teacher2.setCenterId("c001");
        teacher2.setCenterName("Center 001");
        teacher2.setUid("o001");
        teacher2.setEmail("<EMAIL>");
        teacher2.setFirstName("Owner1First");
        teacher2.setLastName("Owner1Last");
        teacher2.setUname("Owner1First Owner1Last");
        teacher2.setGroupId("g001");
        teacher2.setGroupName("Group 001");
        teacher2.setTid("t002");
        teacher2.setTemail("<EMAIL>");
        teacher2.setTfirstName("Teacher2First");
        teacher2.settLastName("Teacher2Last");
        teacher2.setTname("Teacher2First Teacher2Last");
        centersWithTeacher.add(teacher2);
        CenterGroupUser teacher3 = new CenterGroupUser();//teacher3
        teacher3.setCenterId("c001");
        teacher3.setCenterName("Center 001");
        teacher3.setUid("o001");
        teacher3.setEmail("<EMAIL>");
        teacher3.setFirstName("Owner1First");
        teacher3.setLastName("Owner1Last");
        teacher3.setUname("Owner1First Owner1Last");
        teacher3.setGroupId("g004");
        teacher3.setGroupName("Group 002");
        teacher3.setTid("t003");
        teacher3.setTemail("<EMAIL>");
        teacher3.setTfirstName("Teacher3First");
        teacher3.settLastName("Teacher3Last");
        teacher3.setTname("Teacher3First Teacher3Last");
        centersWithTeacher.add(teacher3);
        // 构造园长的学校下所有班级
        // 学校c002下有两个班级g002,g003
        List<CenterGroup> centersWithGroup = new ArrayList<>();
        CenterGroup ownerGroup1 = new CenterGroup("c002", "Center 002", "o002", "Owner2Name", "<EMAIL>", "g002", "Group 002", "Owner2First", "Owner2Last");
        centersWithGroup.add(ownerGroup1);
        CenterGroup ownerGroup2 = new CenterGroup("c002", "Center 002", "o002", "Owner2Name", "<EMAIL>", "g003", "Group 003", "Owner2First", "Owner2Last");
        centersWithGroup.add(ownerGroup2);

        when(centerMapper.queryCentersByName("c")).thenReturn(centersWithTeacher);
        when(centerMapper.queryCentersByEmail("<EMAIL>")).thenReturn(centersWithGroup);

        CenterSearchViewModel result = centerService.getMergeCenters("<EMAIL>", "c");
        assertEquals(1, result.getMyCenters().size());//o002应该有一个学校c002
        assertEquals("c002", result.getMyCenters().get(0).getId());
        assertEquals("o002", result.getMyCenters().get(0).getDirector().getId());//学校c002的园长id应为o001
        assertEquals(2, result.getMyCenters().get(0).getGroups().size());//学校c002应该有两个班级
        assertEquals(1, result.getSearchCenters().size());//查询的列表应该有一个学校c001
        assertEquals("c001", result.getSearchCenters().get(0).getId());
        assertEquals(2, result.getSearchCenters().get(0).getGroups().size());//学校c001下应有两个班级
        assertEquals("o001", result.getSearchCenters().get(0).getDirector().getId());//学校c001的园长id应为o001
    }

    /**
     * 测试查询学校
     */
    @Test
    public void testGet() {
        final UserEntity userEntity = new UserEntity();
        userEntity.setId(USER_ID);
        userEntity.setType("GRANTEE");
        final CenterEntity centerModel = new CenterEntity();
        final List<CenterGroupModel> centerGroupModels = new ArrayList<>();
        com.learninggenie.common.data.entity.CenterEntity centerEntity = new com.learninggenie.common.data.entity.CenterEntity();
        MediaEntity media = new MediaEntity();
        List<GroupEntity> groupModels = new ArrayList<>();
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId("G001");
        groupEntity.setDomain(new DomainEntity());
        groupEntity.setCenter(new CenterEntity());
        groupEntity.setStage(new StageEntity());
        groupModels.add(groupEntity);
        List<AgencyIdentifierEntity> agencyIdentifiers = new ArrayList<>();
        List<EnrollmentModel> enrollmentModels = new ArrayList<>();
        EnrollmentModel enrollmentModel = new EnrollmentModel();
        enrollmentModel.setId("E001");
        enrollmentModel.setPrivatePhoto(false);
        enrollmentModels.add(enrollmentModel);


        when(userProvider.checkUser(any())).thenReturn(userEntity);
        when(centerProvider.getOpenValueDefaultClose(any(), any())).thenReturn(false);
        when(centerDao.getCountOfChildrenInThisCenter(any())).thenReturn(0);
        when(centerDao.getCenterByCenterId(any())).thenReturn(centerModel);
        when(centerDao.getCenterById(any())).thenReturn(centerEntity);
        when(userDao.getCenterGroupByCenterId(any())).thenReturn(centerGroupModels);
        when(mediaDao.getMediaById(any())).thenReturn(media);
        when(mediaService.getMediaUrl(any(), any())).thenReturn("");
        // when(studentDao.getMetaByCenterId(any(), any())).thenReturn(new ArrayList<>());
        when(groupDao.getGroupsByCenterId(any())).thenReturn(groupModels);
        when(agencyDao.getIdentifierByUserId(any())).thenReturn(agencyIdentifiers);
        when(studentDao.getChildrenByGroupId(anyString(), anyInt(), anyInt(), anyString(), anyList(), anyBoolean())).thenReturn(enrollmentModels);
        when(agencyDao.getByCenterId(any())).thenReturn(new AgencyEntity());
        when(portfolioProvider.hasScoreTemplate(any())).thenReturn(false);
        when(domainDao.getDomain(any())).thenReturn(new DomainEntity());

        centerService.get(USER_ID, "C001");

        verify(agencyDao, times(1)).getIdentifierByUserId(any());
    }


    /**
     * 测试查询班级
     * case：类型：SPECIAL_EDUCATION，角色：TEACHING_ASSISTANT
     */
    @Test
    public void testGetCase2() {
        // 数据准备
        String UserId = "U00002";
        final UserEntity userEntity = new UserEntity();
        userEntity.setId("U00003");
        userEntity.setId(UserId);
        userEntity.setRole("TEACHING_ASSISTANT");
        userEntity.setType("SPECIAL_EDUCATION");
        final CenterEntity centerModel = new CenterEntity();
        final List<CenterGroupModel> centerGroupModels = new ArrayList<>();
        com.learninggenie.common.data.entity.CenterEntity centerEntity = new com.learninggenie.common.data.entity.CenterEntity();
        centerEntity.setUser(userEntity);
        MediaEntity media = new MediaEntity();
        List<GroupEntity> groupModels = new ArrayList<>();
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId("G001");
        groupEntity.setDomain(new DomainEntity());
        groupEntity.setCenter(new CenterEntity());
        groupEntity.setStage(new StageEntity());
        groupModels.add(groupEntity);
        List<EnrollmentModel> enrollmentModels = new ArrayList<>();
        EnrollmentModel enrollmentModel = new EnrollmentModel();
        enrollmentModel.setId("E001");
        enrollmentModel.setPrivatePhoto(false);
        enrollmentModels.add(enrollmentModel);

        List<String> enrollmentIds = new ArrayList<>();

        Map<String, Map<String, Boolean>> childId2IepAndEldMap = new HashMap<>();
        Map<String, Boolean> eldMap = new HashMap<>();
        eldMap.put("ELD", true);
        childId2IepAndEldMap.put(UserId, eldMap);
        CenterPaymentPlanEntity centerPaymentPlan = new CenterPaymentPlanEntity();
        centerPaymentPlan.setPlanId("P001");
        centerPaymentPlan.setStatus("Status");


        // 接口模拟
        when(userProvider.checkUser(any())).thenReturn(userEntity);
        when(centerProvider.getOpenValueDefaultClose(any(), any())).thenReturn(false);
        when(centerDao.getCountOfChildrenInThisCenter(any())).thenReturn(0);
        when(centerDao.getCenterByCenterId(any())).thenReturn(centerModel);
        when(centerDao.getCenterById(any())).thenReturn(centerEntity);
        when(userDao.getCenterGroupByCenterId(any())).thenReturn(centerGroupModels);
        when(mediaDao.getMediaById(any())).thenReturn(media);
        when(mediaService.getMediaUrl(any(), any())).thenReturn("");
        // 获取小孩 Id 到 IEP 和 ELD 属性的映射
        when(enrollmentProvider.getIEPAndELDAttrsByCenterIds(anyList())).thenReturn(childId2IepAndEldMap);

        // 接口调用
        centerService.get(UserId, "C001");
        // 验证
        // 获取一次 小孩 ELD、IEP 信息
        verify(enrollmentProvider, times(1)).getIEPAndELDAttrsByCenterIds(anyList());
    }

    /**
     * 测试查询家园互动批量添加选择学校页面的树数据
     * case：角色：机构管理员
     */
    @Test
    public void getCenterTreeData() {
        // 数据准备
        String agencyId = "A001";
        String userId = "U0001";

        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);

        com.learninggenie.common.data.entity.UserEntity user = new UserEntity();
        user.setRole("AGENCY_ADMIN");
        List<ClassesTreeDataDto> result = new ArrayList<>();
        ClassesTreeDataDto classesTreeDataDto = new ClassesTreeDataDto();
        classesTreeDataDto.setLevel(2);
        classesTreeDataDto.setId(userId);
        classesTreeDataDto.setPid(userId);
        result.add(classesTreeDataDto);
        Map<String, Map<String, Boolean>> childIdToIepAndEldMap = new HashMap<>();
        Map<String, Boolean> eldMap = new HashMap<>();
        eldMap.put("ELD", true);
        Map<String, Boolean> iepMap = new HashMap<>();
        eldMap.put("IEP", true);
        childIdToIepAndEldMap.put(userId, eldMap);
        childIdToIepAndEldMap.put(userId, iepMap);

        // 接口模拟
        // 查询用户
        when(userRepository.findById(any())).thenReturn(Optional.of(user));
        //查询整个机构下的学校（机构管理员）
        when(centerDao.getCenterTreeData_agency(any())).thenReturn(result);
        // 获取学校下所有班级的信息
        when(centerDao.getGroupTreeDataByCenterId(any())).thenReturn(result);
        // 获取小孩 Id 到 IEP 和 ELD 属性的映射
        when(enrollmentProvider.getIEPAndELDAttrsByCenterIds(anyList())).thenReturn(childIdToIepAndEldMap);
        // 根据班级 Ids 列表获取班级下所有小孩的信息
        when(enrollmentDao.getBatchAddTreeData(anyList())).thenReturn(result);
        // 查询是否为 APP 用户
//        when(userProvider.isApp()).thenReturn(true);
        // 查询是否为授权老师
        when(userProvider.isGranteeTeacher(user)).thenReturn(true);

        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);

        centerService.getCenterTreeData(userId, agencyId);
        // 验证
        // 调用一次获取小孩 Id 到 IEP 和 ELD 属性的映射
        verify(enrollmentProvider, times(1)).getIEPAndELDAttrsByCenterIds(anyList());
    }

    /**
     * 测试查询家园互动批量添加选择学校页面的树数据
     * case：角色：教师
     */
    @Test
    public void getCenterTreeDataCase2() {
        // 数据准备
        String agencyId = "A0001";
        String userId = "U0002";

        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);

        com.learninggenie.common.data.entity.UserEntity user = new UserEntity();
        user.setRole("TEACHING_ASSISTANT");
        List<ClassesTreeDataDto> result = new ArrayList<>();
        ClassesTreeDataDto classesTreeDataDto = new ClassesTreeDataDto();
        classesTreeDataDto.setLevel(2);
        classesTreeDataDto.setId(userId);
        classesTreeDataDto.setPid(userId);
        result.add(classesTreeDataDto);
        Map<String, Map<String, Boolean>> childIdToIepAndEldMap = new HashMap<>();
        Map<String, Boolean> eldMap = new HashMap<>();
        eldMap.put("ELD", true);
        Map<String, Boolean> iepMap = new HashMap<>();
        eldMap.put("IEP", true);
        childIdToIepAndEldMap.put(userId, eldMap);
        childIdToIepAndEldMap.put(userId, iepMap);

        List<ClassesTreeDataDto> classesTreeDataDtoList = new ArrayList<>();

        // 接口模拟
        // 查询用户
        when(userRepository.findById(any())).thenReturn(Optional.of(user));
        //查询整个机构下的学校（机构管理员）
        // when(centerDao.getCenterTreeData_agency(any())).thenReturn(result);
        // 获取学校下所有班级的信息
        when(centerDao.getGroupTreeDataByTeacherIdAndCenterId(any(), any())).thenReturn(result);
        // 获取小孩 Id 到 IEP 和 ELD 属性的映射
        when(enrollmentProvider.getIEPAndELDAttrsByCenterIds(anyList())).thenReturn(childIdToIepAndEldMap);
        // 根据班级 Ids 列表获取班级下所有小孩的信息
        when(enrollmentDao.getBatchAddTreeData(anyList())).thenReturn(result);
        // 查询是否为 APP 用户
//        when(userProvider.isApp()).thenReturn(true);
        // 查询是否为授权老师
        when(userProvider.isGranteeTeacher(user)).thenReturn(true);

        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);

        centerService.getCenterTreeData(userId, agencyId);
        // 验证
        // 调用一次获取小孩 Id 到 IEP 和 ELD 属性的映射
        verify(enrollmentProvider, times(1)).getIEPAndELDAttrsByCenterIds(anyList());
    }

    @Test
    public void testGetCenterStaff () {

        AgencyModel agency = new AgencyModel();
        agency.setId("1");

        List<CenterWithIdName> centerIdNameList = new ArrayList<>();
        CenterWithIdName centerIdName = new CenterWithIdName();
        centerIdName.setId("1");
        centerIdName.setName("Center1");

        centerIdNameList.add(centerIdName);

        List<UserModel> centerStaffs = new ArrayList<>();
        UserModel staff1 = new UserModel();
        staff1.setId("1");
        staff1.setCenterId("1");

        centerStaffs.add(staff1);


        List<UserMetaDataEntity> userMetadataList = new ArrayList<>();
        UserMetaDataEntity staffMeta1 = new UserMetaDataEntity();
        staffMeta1.setMetaKey("SOURCE_ID");
        staffMeta1.setMetaValue("1");
        UserEntity user1 = new UserEntity();
        user1.setId("1");
        staffMeta1.setUser(user1);

        userMetadataList.add(staffMeta1);

        when (userProvider.getAgencyByUserId(anyString())).thenReturn(agency);
        when (userService.getCenterIdNameByUserId(anyString(), anyString())).thenReturn(centerIdNameList);
        when (userDao.getCenterStaffsByCenterIds(anyList())).thenReturn(centerStaffs);
        when (userDao.getMetadataByUserIdsAndKey(anyList(), anyString())).thenReturn(userMetadataList);

        centerService.getCenterStaff(anyString());

        verify(userProvider).getAgencyByUserId(anyString());
        verify(userService).getCenterIdNameByUserId(anyString(), anyString());
        verify(userDao).getCenterStaffsByCenterIds(anyList());
        verify(userDao).getMetadataByUserIdsAndKey(anyList(), anyString());
    }

    @Test
    public void testBatchSetStaffId () {
        List<UpdateStaffModel> updateStaffModels = new ArrayList<>();
        UpdateStaffModel updateStaffModel = new UpdateStaffModel();
        updateStaffModel.setStaffId("1");
        updateStaffModel.setMetaValue("1");

        updateStaffModels.add(updateStaffModel);
        usersMetaDataDao.setMeta(anyString(), anyString(), anyString());
        BatchSetupStaffSourceIdRequest request = new BatchSetupStaffSourceIdRequest();
        request.setUpdateStaffModels(updateStaffModels);
        centerService.batchSetStaffId(request);

        verify(usersMetaDataDao, atLeastOnce()).setMeta(anyString(), anyString(), anyString());

    }

    /**
     * 测试 MapCenter 方法
     */
    @Test
    public void testMapCenter() {
        // 数据准备
        String userId = "U00004";
        String domainId = "D00001";
        String frameworkId = "F00001";
        CenterResponse fakeCenter = new CenterResponse();
        com.learninggenie.api.model.center.CenterGroup group = new com.learninggenie.api.model.center.CenterGroup();
        CenterGroupEnrollment enrollment = new CenterGroupEnrollment();
        enrollment.setId(userId);
        group.setEnrollments(Arrays.asList(enrollment));
        fakeCenter.setGroups(Arrays.asList(group));

        List<GroupEntity> groups = new ArrayList<>();
        GroupEntity groupEntity = new GroupEntity();
        DomainEntity domainEntity = new DomainEntity();
        domainEntity.setId(domainId);
        groupEntity.setDomain(domainEntity);
        StageEntity stageEntity = new StageEntity();
        stageEntity.setId("S00001");
        groupEntity.setStage(stageEntity);
        groups.add(groupEntity);

        List<EnrollmentModel> enrollmentModels = new ArrayList<>();
        EnrollmentModel enrollmentModel = new EnrollmentModel();
        enrollmentModel.setId(userId);
        enrollmentModel.setPrivatePhoto(true);
        enrollmentModel.setFrameworkId(frameworkId);
        enrollmentModel.setFrameworkName("FrameworkName");
        enrollmentModel.setFrameworkLinkUrl("FrameworkPdf");
        enrollmentModels.add(enrollmentModel);

        int childCount = 1;
        Integer teacherCount = 1;
        Integer parentCount = 1;

        List<UserModel> teachersInvited = new ArrayList<>();
        boolean hasScoreTemplate = true;
        List<com.learninggenie.common.data.entity.CenterEntity> center = new ArrayList<>();

        Map<String, Map<String, Boolean>> childIdToIepAndEldMap = new HashMap<>();
        Map<String, Boolean> eldMap = new HashMap<>();
        eldMap.put("ELD", true);
        eldMap.put("IEP/IFSP", true);
        childIdToIepAndEldMap.put(userId, eldMap);

        ReflectionTestUtils.setField(centerService, "idPSE2015", frameworkId);
        ReflectionTestUtils.setField(centerService, "idITE2015", frameworkId);

        // 接口模拟
        when(studentDao.getChildrenByGroupId(any(), any())).thenReturn(enrollmentModels);
        // when(groupDao.getGroupById(any())).thenReturn(groupEntity);
        when(groupDao.getGroupsByGroupIds(anyList())).thenReturn(groups);
        when(userDao.getTeacherCountByGroupId(any())).thenReturn(teacherCount);
        when(userDao.getParentGroupCountByGroupId(any())).thenReturn(parentCount);
        when(userDao.getTeachersInvitedByGroupId(any())).thenReturn(teachersInvited);
        when(portfolioProvider.hasScoreTemplate(anyString())).thenReturn(hasScoreTemplate);
        when(centerDao.getCentersByGroupIds(anyList())).thenReturn(center);
        when(enrollmentProvider.getIEPAndELDAttrsByCenterIds(anyList())).thenReturn(childIdToIepAndEldMap);

        centerService.mapCenter(new ArrayList<>(), fakeCenter, null);

        // 验证
        verify(enrollmentProvider, times(1)).getIEPAndELDAttrsByCenterIds(anyList());
    }

    /**
     *
     * 测试对学校以及小孩进行排序，按照学校名称以及小孩的 displayName 进行排序
     */
    @Test
    public void testSortGroupEnrollmentByName() {
        // 数据准备
        // 学校数据列表
        // 学生列表
        List<CenterGroupEnrollment> enrollments = new ArrayList<>();
        // 学生数据
        CenterGroupEnrollment centerGroupEnrollment = new CenterGroupEnrollment();
        centerGroupEnrollment.setDisplayName("John");   // 学生姓名
        // 学生数据
        CenterGroupEnrollment centerGroupEnrollment1 = new CenterGroupEnrollment();
        centerGroupEnrollment1.setDisplayName("Mary");  // 学生姓名
        // 将学生数据添加到学生列表中
        enrollments.add(centerGroupEnrollment);
        enrollments.add(centerGroupEnrollment1);

        // 调用方法
        centerService.sortGroupEnrollmentByDisplayName(enrollments);

        // 验证学生列表中的学生数据是否按照学生姓名进行排序 期望结果： John Mary
        Assert.assertEquals("John", enrollments.get(0).getDisplayName());
        Assert.assertEquals("Mary", enrollments.get(1).getDisplayName());
    }


    @Test
    public void testGetStateAbbreviations() {
        // 设置测试数据
        List<String> stateAbbreviations = new ArrayList<>();
        // 添加您期望的州缩写数据

        // 设置模拟行为
        when(librariesAreaService.getStateAbbreviations()).thenReturn(stateAbbreviations);

        // 调用被测试方法
        GetStateAbbreviationsResponse result = centerService.getStateAbbreviations();

        // 验证模拟行为和结果
        assertEquals(stateAbbreviations, result.getStateAbbreviations());
    }

    @Test
    public void testGetStateCounties() {
        // 设置测试数据
        String stateAbbreviation = "yourStateAbbreviation";
        List<String> countyNames = new ArrayList<>();
        // 添加您期望的地区名称数据

        // 设置模拟行为
        when(librariesAreaService.getCountiesByStateCode(anyString())).thenReturn(countyNames);

        // 调用被测试方法
        GetStateCountiesResponse result = centerService.getStateCounties(stateAbbreviation);

        // 验证模拟行为和结果
        assertEquals(countyNames, result.getCountyNames());
    }


    @Test
    public void testCreateOrUpdateDrdpCenterMeta() {
        // 设置测试数据
        String centerId = "yourCenterId";
        DrdpCenterMetaDataModel drdpCenterMetaDataModel = new DrdpCenterMetaDataModel();
        // 设置 drdpCenterMetaDataModel 的属性值

        List<CenterMetaDataEntity> drdpCenterMetadata = new ArrayList<>();
        // 设置 drdpCenterMetadata 的数据

        when(centerDao.getDrdpCenterMetadata(centerId)).thenReturn(drdpCenterMetadata);

        // 调用被测试方法
        centerService.createOrUpdateDrdpCenterMeta(centerId, drdpCenterMetaDataModel);

        // 验证模拟行为
        verify(centerDao).getDrdpCenterMetadata(centerId);
        verify(centersMetaDataDao, times(drdpCenterMetadata.size())).updateMetaById(anyString(), anyString());
    }


    @Test
    public void testCreateCenter() {
        // 设置测试数据
        CreateCenterRequest request = new CreateCenterRequest();
        // 设置 request 的属性值

        String centerId = "yourCenterId";
        String userId = "yourUserId";

        ValidateUtil.validateName(request.getName(), 80);

        com.learninggenie.common.data.entity.UserEntity userEntity = new com.learninggenie.common.data.entity.UserEntity();
        userEntity.setId(userId);

        List<AgencyEntity> agencies = new ArrayList<>();
        // 设置 agencies 的数据
        AgencyEntity agency = new AgencyEntity();
        String agencyId = "agencyId";
        agency.setId(agencyId);
        String agencyName = "agencyName";
        agency.setName(agencyName);
        agencies.add(agency);

        when(userProvider.checkUser(request.getUserId())).thenReturn(userEntity);
        when(agencyDao.getAgencyByUserId(userEntity.getId())).thenReturn(agencies);

        // 调用被测试方法
        CreateCenterResponse result = centerService.createCenter(request);

        // 验证模拟行为
        verify(userProvider).checkUser(request.getUserId());
        verify(agencyDao).getAgencyByUserId(userEntity.getId());
        verify(centerDao).createCenter(any(CenterEntity.class));
        verify(paymentPlanDao).savePaymPlan(any(CenterPaymentPlanEntity.class));
    }

    @Test
    public void testUpdateCenter() {
        // 设置测试数据
        String centerId = "yourCenterId";
        UpdateCenterRequest request = new UpdateCenterRequest();
        // 设置 request 的属性值

        ValidateUtil.validateName(request.getName(), 80);

        String currentUserId = "yourCurrentUserId";
        com.learninggenie.common.data.entity.UserEntity userEntity = new com.learninggenie.common.data.entity.UserEntity();
        userEntity.setId(currentUserId);
        userEntity.setRole(UserRole.AGENCY_ADMIN.toString());

        when(userProvider.getCurrentUserId()).thenReturn(currentUserId);
        when(userProvider.checkUser(currentUserId)).thenReturn(userEntity);

        com.learninggenie.common.data.entity.CenterEntity centerEntity = new com.learninggenie.common.data.entity.CenterEntity();
        // 设置 centerEntity 的属性值
        when(centerProvider.checkCenter(centerId)).thenReturn(centerEntity);

        AgencyModel agency = new AgencyModel();
        // 设置 agency 的属性值
        when(userProvider.getAgencyByUserId(currentUserId)).thenReturn(agency);

        List<com.learninggenie.common.data.entity.CenterEntity> allCenters = new ArrayList<>();

        // 调用被测试方法
        centerService.updateCenter(centerId, request);

        // 验证模拟行为
        verify(userProvider).getCurrentUserId();
        verify(userProvider).checkUser(currentUserId);
        verify(centerProvider).checkCenter(centerId);
        verify(userProvider).getAgencyByUserId(currentUserId);
        verify(centerDao).updateCenter(any(CenterEntity.class));
    }

    /**
     * 测试通过 centerId 获取 center 信息
     */
    @Test
    public void testGetCenterByCenterId() {
        // 模拟输入参数
        String centerId = "exampleCenterId";

        // 模拟 centerMapper.getCenterByCenterId 方法调用和返回值
        List<RequestCenterModel> centerModelList = new ArrayList<>(); // 假设为空列表
        // 创建一个 CenterModel 对象，设置其属性值
        RequestCenterModel centerModel = new RequestCenterModel();
        // 设置 centerModel 的 ID
        centerModel.setId("CenterId");
        // 设置 centerModel 的 Name
        centerModel.setName("CenterName");
        // 设置 centerModel 的 TimeZone
        centerModel.setTimezone("Asia/Shanghai");
        // 设置 centerModel 的 report time
        centerModel.setSend_report_time("SendReportTime");
        // 设置 centerModel 的 media id
        centerModel.setLogo_media_id("MediaId");
        // 设置 centerModel 的 logo url
        centerModel.setLogo_url("LogoUrl");
        // 设置 centerModel 的 training
        centerModel.setTraining(false);
        // 设置 centerModel 的 source id
        centerModel.setSourceId("SourceId");
        // 设置 centerModel 的 address
        centerModel.setAddress("Address");
        // 设置 centerModel 的 city
        centerModel.setCity("City");
        // 设置 centerModel 的 state
        centerModel.setState("State");
        // 设置 centerModel 的 zip
        centerModel.setZip("Zip");
        // 设置 centerModel 的 type id
        centerModel.setTypeId("TypeId");
        // 设置 centerModel 的 county name
        centerModel.setCountyName("CountyName");
        // 添加进入 centerModelList
        centerModelList.add(centerModel);

        when(centerMapper.getCenterByCenterId(anyString())).thenReturn(centerModelList);

        // 模拟 centerDao.getMeta 方法调用和返回值
        CenterMetaDataEntity sourceIdMeta = new CenterMetaDataEntity();
        sourceIdMeta.setMetaValue("exampleSourceId");
        when(centerDao.getMeta(anyString(), anyString())).thenReturn(sourceIdMeta);

        // 模拟 centerDao.getDrdpCenterMetadata 方法调用和返回值
        List<CenterMetaDataEntity> drdpCenterMetadata = new ArrayList<>(); // 假设为空列表
        when(centerDao.getDrdpCenterMetadata(anyString())).thenReturn(drdpCenterMetadata);

        // 模拟 fileSystem.getPublicUrl 方法调用和返回值
        String publicUrl = "examplePublicUrl";
        when(fileSystem.getPublicUrl(anyString())).thenReturn(publicUrl);

        // 执行被测试的方法
        RequestCenterModel centerByCenterId = centerService.getCenterByCenterId(centerId);

        // 验证结果
        assertEquals(centerModelList.get(0).getId(), centerByCenterId.getId());
    }


    /**
     * 测试学校的 sourceId 是否存在
     */
    @Test
    public void testIsSourceIdExists() {
        // 模拟输入参数
        String centerId = "exampleCenterId";
        String sourceId = "exampleSourceId";
        List<com.learninggenie.common.data.entity.CenterEntity> agencyCenter = new ArrayList<>(); // 假设为空列表

        // 添加 center
        com.learninggenie.common.data.entity.CenterEntity center = new com.learninggenie.common.data.entity.CenterEntity();
        // 设置 center 的 ID
        center.setId("exampleCenterId");
        // 模拟 StringUtil.isEmptyOrBlank 方法调用和返回值
        boolean isEmptyOrBlank = false; // 假设不为空
        // 模拟 agencyCenter.stream().map().distinct().collect() 方法调用和返回值
        List<String> agencyCenterIds = new ArrayList<>(); // 假设为空列表

        // 模拟 centerDao.getMeta 方法调用和返回值
        List<CenterMetaDataEntity> centerMetaList = new ArrayList<>(); // 假设为空列表
        when(centerDao.getMeta(anyList(), anyString())).thenReturn(centerMetaList);

        // 执行被测试的方法
        boolean idExists = centerService.existedSourceId(centerId, sourceId, agencyCenter);

        // 验证结果
        assertFalse(idExists);
    }
}

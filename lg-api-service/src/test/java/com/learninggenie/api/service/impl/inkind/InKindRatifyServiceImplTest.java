package com.learninggenie.api.service.impl.inkind;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.toolkit.ChainWrappers;
import com.learninggenie.api.model.InkindChildWithStatsResponse;
import com.learninggenie.api.model.InkindResponse;
import com.learninggenie.api.model.inkind.*;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.provider.impl.InKindProviderImpl;
import com.learninggenie.api.security.AuthUserDetails;
import com.learninggenie.common.data.dao.*;
import com.learninggenie.common.data.dao.authentication.UserProfileDao;
import com.learninggenie.common.data.dao.impl.InKindReportApproveDaoImpl;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.entity.*;
import com.learninggenie.common.data.entity.authentication.UserProfileEntity;
import com.learninggenie.common.data.entity.inkind.InKindReportApprove;
import com.learninggenie.common.data.enums.InKindSourceEnum;
import com.learninggenie.common.data.enums.NotificationType;
import com.learninggenie.common.data.enums.UserRole;
import com.learninggenie.common.data.enums.inkind.ActivityTypeValue;
import com.learninggenie.common.data.enums.inkind.InKindReviewTypeEnum;
import com.learninggenie.common.data.enums.inkind.InKindUnit;
import com.learninggenie.common.data.mapper.dynamo.AgencyMetadataMapper;
import com.learninggenie.common.data.mapper.dynamo.NotificationMapper;
import com.learninggenie.common.data.mapper.mybatisplus.inkind.InKindReportApproveMapper;
import com.learninggenie.common.data.mapper.mybatisplus.inkind.InKindReportModelMapper;
import com.learninggenie.common.data.model.CenterGroupModel;
import com.learninggenie.common.data.model.CenterModel;
import com.learninggenie.common.data.model.MapModel;
import com.learninggenie.common.data.model.UserModel;
import com.learninggenie.common.data.model.dynamo.AgencyMetadata;
import com.learninggenie.common.data.model.dynamo.Notification;
import com.learninggenie.common.data.model.inkind.InKindReportModel;
import com.learninggenie.common.data.model.inkind.InkindDonor;
import com.learninggenie.common.filesystem.FileSystem;
import com.learninggenie.common.utils.TimeUtil;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

/**
 * InKindRatifyServiceImpl 单元测试
 */
@ExtendWith(MockitoExtension.class)
public class InKindRatifyServiceImplTest {

    private static final String BASE_MAPPER = "baseMapper";

    private static final String INKIND_REPORT_APPROVE_DAO = "inKindReportApproveDao";

    private static final String INKIND_REPORT_MODEL_MAPPER = "inKindReportModelMapper";

    private static final String TEACHER_APPROVE_AND_ADMIN_RATIFY = "TEACHER_APPROVE_AND_ADMIN_RATIFY";

    @InjectMocks
    private InKindRatifyServiceImpl inKindRatifyService;

    @Mock
    private UserProvider userProvider;

    @Mock
    private UserProfileDao userProfileDao;

    @Mock
    private AgencyMetadataMapper agencyMetadataMapper;

    @Mock
    private InkindDao inKindDao;

    @Mock
    private UserDaoImpl userDao;

    @Mock
    private UsersMetaDataDao usersMetaDataDao;

    @Mock
    private InKindReportApproveDaoImpl inKindReportApproveDao;

    @Mock
    private MediaDao mediaDao;

    @Mock
    private FileSystem fileSystem;

    @Mock
    private CenterDao centerDao;

    @Mock
    private GroupDao groupDao;

    @Mock
    private InKindProviderImpl inKindCommonService;

    @Mock
    private NotificationMapper notificationMapper;

    @Mock
    private StudentDao studentDao;

    @Mock
    private InKindReportApproveMapper inKindReportApproveMapper;

    @Mock
    private InKindReportModelMapper inKindReportModelMapper;

    private MockedStatic<ChainWrappers> chainWrappersMockedStatic;


    /**
     * 初始化 TableInfoHelper
     */
    @BeforeAll
    public static void beforeClass() {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), InKindReportApprove.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), InKindReportModel.class);
    }

    /**
     * 初始化 chainWrappersMockedStatic
     */
    @BeforeEach
    public void beforeMethod() {
        chainWrappersMockedStatic = mockStatic(ChainWrappers.class);
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 关闭 chainWrappersMockedStatic
     */
    @AfterEach
    public void afterMethod() {
        chainWrappersMockedStatic.close();
    }

    /**
     * 测试获取审核模式
     */
    @Test
    void testGetSetting() {
        // 数据准备
        final String agencyId = "A00001A";
        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setAgencyId(agencyId);

        final AgencyMetadata metadata = new AgencyMetadata();
        metadata.setMetaValue(TEACHER_APPROVE_AND_ADMIN_RATIFY);

        // 接口模拟
        // 获取当前用户信息
        Mockito.when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // 查找机构设置的审核模式
        Mockito.when(agencyMetadataMapper.get(any(), any())).thenReturn(metadata);

        InKindSettingResponse setting = inKindRatifyService.getSetting();

        // 验证
        verify(userProvider, times(1)).getCurrentUser();
        Assertions.assertEquals(TEACHER_APPROVE_AND_ADMIN_RATIFY, setting.getRatifiedMode());
    }

    /**
     * 测试获取机构当前学年
     */
    @Test
    void testGetCurrentSchoolYear() {
        // 数据准备
        final String agencyId = "A00021";
        final Date date = TimeUtil.parseDate("2020-09-01");
        List<InkindSchoolYearEntity> schoolYears = new ArrayList<>();
        InkindSchoolYearEntity inkindSchoolYearEntity = new InkindSchoolYearEntity();
        inkindSchoolYearEntity.setStartDate(date);
        schoolYears.add(inkindSchoolYearEntity);

        // 接口模拟
        // 获取机构所有学年(按开始时间倒序排序)
        Mockito.when(inKindDao.getSchoolYearByAgencyId(agencyId)).thenReturn(schoolYears);

        InkindSchoolYearEntity currentSchoolYear = inKindRatifyService.getCurrentSchoolYear(agencyId);

        // 验证
        verify(inKindDao, times(1)).getSchoolYearByAgencyId(agencyId);
        Assertions.assertEquals(date, currentSchoolYear.getStartDate());
    }

    /**
     * 测试设置审核模式.
     */
    @Test
    void testSetSetting() {
        // 数据准备
        final InKindSettingRequest request = new InKindSettingRequest();
        request.setRatifiedMode("TEACHER_APPROVE_SIGNATURE_AND_ADMIN_RATIFY");
        request.setSiteAdminThirdPartyApprovalOpen(true);
        final String agencyId = "A00011";
        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setAgencyId(agencyId);

        List<UserModel> admins = new ArrayList<>();
        List<UserModel> teachers = new ArrayList<>();
        List<UserModel> siteAdmins = new ArrayList<>();

        // 接口模拟
        // 获取当前用户信息
        Mockito.when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // 获取机构所有管理员
        Mockito.when(userDao.getAgencyAdminsByAgencyId(agencyId)).thenReturn(admins);
        // 获取机构所有老师
        Mockito.when(userDao.getTeacherByAgencyId(any())).thenReturn(teachers);
        // 获取学校下所有园长
        Mockito.when(userDao.getSiteAdminByAgencyId(any())).thenReturn(siteAdmins);

        inKindRatifyService.setSetting(request);

        // 验证 -- 方法调用次数
        // 保存机构审核模式和园长审核第三方捐赠数据权限
        verify(agencyMetadataMapper, Mockito.times(2)).save(any());
        // 清除审核模式已提示标记
        verify(usersMetaDataDao, Mockito.times(2)).batchDeleteMeta(any(), any());

    }

    /**
     * 测试获取审批列表的请求
     * 类型：ratify
     * 角色：机构管理员或机构创建者
     * 第三方捐赠：社区角色
     */
    @Test
    void testGetReviewList() {
        // 数据准备
        final String staffAvatarMediaId = "S00001";
        final String parentId = "P00001";
        final String relationship = "Father";

        final GetReviewListRequest request = new GetReviewListRequest();
        request.setType(InKindReviewTypeEnum.RATIFY.getName());
        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setRole(UserRole.AGENCY_ADMIN.toString());
        final String enrollmentId = "E0001";

        final Page<InKindReportModel> needRatifyList = new Page<>();
        InKindReportModel inKindReportModel = new InKindReportModel();
        inKindReportModel.setChildAvatarMediaId("CAA001");
        inKindReportModel.setStaffAvatarMediaId(staffAvatarMediaId);
        inKindReportModel.setEnrollmentId(enrollmentId);
        inKindReportModel.setParentId(parentId);
        inKindReportModel.setUnit(InKindUnit.HOUR.toString());
        inKindReportModel.setRateUnit(InKindUnit.HOUR.toString());
        needRatifyList.setRecords(Collections.singletonList(inKindReportModel));

        final List<MediaEntity> medias = new ArrayList<>();
        List<UserModel> parents = new ArrayList<>();
        UserModel userModel = new UserModel();
        userModel.setId(parentId);
        parents.add(userModel);
        List<InkindDonor> donorList = new ArrayList<>();
        InkindDonor inkindDonor = new InkindDonor();
        inkindDonor.setId(parentId);
        inkindDonor.setRelationship(relationship);
        donorList.add(inkindDonor);

        // 接口模拟
        // 获取当前用户信息
        Mockito.when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // 获取待批准的数据
        Mockito.when(inKindReportApproveDao.getNeedRatifyListByAgencyId(any(), any(), any())).thenReturn(needRatifyList);
        // 查询头像数据
        Mockito.when(mediaDao.getMedias(any())).thenReturn(medias);
        // 获取家长数据
        Mockito.when(userDao.getUsersByUserIdsWithDeleted(any())).thenReturn(parents);
        // 获取第三方捐赠人数据
        Mockito.when(inKindDao.getDonatorByIds(any())).thenReturn(donorList);
        // 获取孩子头像url
        Mockito.when(fileSystem.getChildAvatarUrl(any())).thenReturn("https://www.te1st.com");
        // 获取语言
        Mockito.when(userProvider.getCurrentLang()).thenReturn("zh_CN3");
        // 获取老师头像url
        Mockito.when(fileSystem.getUserAvatarUrl(any())).thenReturn("https://www.t2est.com");

        GetReviewListResponse reviewListResponse = inKindRatifyService.getReviewList(request);

        // 验证
        verify(inKindReportApproveDao, times(1)).getNeedRatifyListByAgencyId(any(), any(), any());

        Assertions.assertEquals(1, reviewListResponse.getInKindReviews().size());
        Assertions.assertEquals(parentId, reviewListResponse.getInKindReviews().get(0).getParentId());
        Assertions.assertEquals(enrollmentId, reviewListResponse.getInKindReviews().get(0).getEnrollmentId());
        Assertions.assertEquals(InKindUnit.HOUR.toString(), reviewListResponse.getInKindReviews().get(0).getRateUnit());
        Assertions.assertEquals(InKindUnit.HOUR.toString(), reviewListResponse.getInKindReviews().get(0).getUnit());
        Assertions.assertEquals(staffAvatarMediaId, reviewListResponse.getInKindReviews().get(0).getStaffAvatarMediaId());
        Assertions.assertEquals(relationship, reviewListResponse.getInKindReviews().get(0).getRelationship());
        Assertions.assertEquals("Community father", reviewListResponse.getInKindReviews().get(0).getDisplayRelationship());
    }

    /**
     * 测试获取审批列表的请求
     * 类型：ratify
     * 角色：园长
     * 第三方捐赠：家长角色
     */
    @Test
    void testGetReviewList2() {
        // 数据准备
        final String enrollmentId = "E0002";
        final String parentId = "P00002";

        GetReviewListRequest request = new GetReviewListRequest();
        request.setType(InKindReviewTypeEnum.RATIFY.getName());

        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setRole(UserRole.SITE_ADMIN.name());

        List<CenterEntity> centers = new ArrayList<>();
        final CenterEntity centerEntity = new CenterEntity();
        centerEntity.setId("C00002");
        centers.add(centerEntity);

        final Page<InKindReportModel> needRatifyList = new Page<>();
        final InKindReportModel inKindReportModel = new InKindReportModel();
        inKindReportModel.setChildAvatarMediaId("CA0001");
        inKindReportModel.setStaffAvatarMediaId("S00002");
        inKindReportModel.setEnrollmentId(enrollmentId);
        inKindReportModel.setParentId(parentId);
        inKindReportModel.setUnit(InKindUnit.HOUR.toString());
        inKindReportModel.setRateUnit(InKindUnit.HOUR.toString());
        needRatifyList.setRecords(Collections.singletonList(inKindReportModel));

        final List<MediaEntity> medias = new ArrayList<>();
        List<UserModel> parents = new ArrayList<>();
        UserModel userModel = new UserModel();
        userModel.setId(parentId);
        parents.add(userModel);
        List<InkindDonor> donorList = new ArrayList<>();
        InkindDonor inkindDonor = new InkindDonor();
        inkindDonor.setId(parentId);
        inkindDonor.setType("PARENT");
        donorList.add(inkindDonor);
        // 接口模拟
        // 获取当前用户信息
        Mockito.when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // 获取园长管理的学校
        Mockito.when(centerDao.getBySiteAdminId(any())).thenReturn(centers);
        // 获取待批准的数据
        Mockito.when(inKindReportApproveDao.getNeedRatifyListByCenterIds(any(), any(), any())).thenReturn(needRatifyList);
        // 查询头像数据
        Mockito.when(mediaDao.getMedias(any())).thenReturn(medias);
        // 获取家长数据
        Mockito.when(userDao.getUsersByUserIdsWithDeleted(any())).thenReturn(parents);
        // 获取第三方捐赠人数据
        Mockito.when(inKindDao.getDonatorByIds(any())).thenReturn(donorList);
        // 获取孩子头像url
        Mockito.when(fileSystem.getChildAvatarUrl(any())).thenReturn("https://www.test.com/nnn");
        // 获取语言
        Mockito.when(userProvider.getCurrentLang()).thenReturn("zh_CN4");
        // 获取老师头像url
        Mockito.when(fileSystem.getUserAvatarUrl(any())).thenReturn("https://www.test.com/nn");

        GetReviewListResponse reviewListResponse = inKindRatifyService.getReviewList(request);

        Assertions.assertEquals(1, reviewListResponse.getInKindReviews().size());
        Assertions.assertEquals(parentId, reviewListResponse.getInKindReviews().get(0).getParentId());
        Assertions.assertEquals(enrollmentId, reviewListResponse.getInKindReviews().get(0).getEnrollmentId());
        Assertions.assertEquals(InKindUnit.HOUR.toString(), reviewListResponse.getInKindReviews().get(0).getRateUnit());
        Assertions.assertEquals(InKindUnit.HOUR.toString(), reviewListResponse.getInKindReviews().get(0).getUnit());
        Assertions.assertEquals(inKindReportModel.getStaffAvatarMediaId(), reviewListResponse.getInKindReviews().get(0).getStaffAvatarMediaId());
        Assertions.assertNull(reviewListResponse.getInKindReviews().get(0).getRelationship());
        Assertions.assertEquals("", reviewListResponse.getInKindReviews().get(0).getDisplayRelationship());
    }

    /**
     * 测试获取审批列表的请求
     * 类型：pendingRatify
     * 角色：园长
     */
    @Test
    void testGetReviewList3() {
        // 数据准备
        final String enrollmentId = "E0003";
        final String parentId = "P00003";
        final String staffAvatarMediaId = "S00003";

        GetReviewListRequest request = new GetReviewListRequest();
        request.setType(InKindReviewTypeEnum.PENDING_RATIFY.getName());

        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setRole(UserRole.SITE_ADMIN.name());

        List<CenterEntity> centers = new ArrayList<>();
        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setId("C00003");
        centers.add(centerEntity);

        final Page<InKindReportModel> needRatifyList = new Page<>();
        InKindReportModel inKindReportModel = new InKindReportModel();
        inKindReportModel.setChildAvatarMediaId("CA0002");
        inKindReportModel.setStaffAvatarMediaId(staffAvatarMediaId);
        inKindReportModel.setEnrollmentId(enrollmentId);
        inKindReportModel.setParentId(parentId);
        inKindReportModel.setUnit(InKindUnit.HOUR.toString());
        inKindReportModel.setRateUnit(InKindUnit.HOUR.toString());
        needRatifyList.setRecords(Collections.singletonList(inKindReportModel));

        final List<MediaEntity> medias = new ArrayList<>();
        List<UserModel> parents = new ArrayList<>();
        UserModel userModel = new UserModel();
        userModel.setId(parentId);
        parents.add(userModel);
        List<InkindDonor> donorList = new ArrayList<>();

        // 接口模拟
        // 获取当前用户信息
        Mockito.when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // 获取园长管理的学校
        Mockito.when(centerDao.getBySiteAdminId(any())).thenReturn(centers);
        // 获取待批准的数据
        Mockito.when(inKindReportApproveDao.getPendingRatifyListByAgencyIdOrUserId(any(), any(), any(), any())).thenReturn(needRatifyList);
        // 查询头像数据
        Mockito.when(mediaDao.getMedias(any())).thenReturn(medias);
        // 获取家长数据
        Mockito.when(userDao.getUsersByUserIdsWithDeleted(any())).thenReturn(parents);
        // 获取第三方捐赠人数据
        Mockito.when(inKindDao.getDonatorByIds(any())).thenReturn(donorList);
        // 获取孩子头像url
        Mockito.when(fileSystem.getChildAvatarUrl(any())).thenReturn("https://www.test.com3");
        // 获取语言
        Mockito.when(userProvider.getCurrentLang()).thenReturn("zh_CN");
        // 获取老师头像url
        Mockito.when(fileSystem.getUserAvatarUrl(any())).thenReturn("https://www.test.com4");

        GetReviewListResponse reviewList = inKindRatifyService.getReviewList(request);

        Assertions.assertEquals(1, reviewList.getInKindReviews().size());
        Assertions.assertEquals(parentId, reviewList.getInKindReviews().get(0).getParentId());
        Assertions.assertEquals(enrollmentId, reviewList.getInKindReviews().get(0).getEnrollmentId());
        Assertions.assertEquals(InKindUnit.HOUR.toString(), reviewList.getInKindReviews().get(0).getRateUnit());
        Assertions.assertEquals(InKindUnit.HOUR.toString(), reviewList.getInKindReviews().get(0).getUnit());
        Assertions.assertEquals(staffAvatarMediaId, reviewList.getInKindReviews().get(0).getStaffAvatarMediaId());
        Assertions.assertEquals("", reviewList.getInKindReviews().get(0).getRelationship());
        Assertions.assertEquals("", reviewList.getInKindReviews().get(0).getDisplayRelationship());
    }

    /**
     * 测试获取审批列表的请求
     * 类型：pendingRatify
     * 角色：老师
     */
    @Test
    void testGetReviewList4() {
        // 数据准备
        final String enrollmentId = "E0004";
        final String parentId = "P00004";
        final String staffAvatarMediaId = "S00004";

        final GetReviewListRequest request = new GetReviewListRequest();
        request.setType(InKindReviewTypeEnum.PENDING_RATIFY.getName());

        final AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setRole("FAMILY_SERVICE");

        final List<GroupEntity> groups = new ArrayList<>();
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId("G00012");
        groups.add(groupEntity);

        final Page<InKindReportModel> needRatifyList = new Page<>();
        InKindReportModel inKindReportModel = new InKindReportModel();
        inKindReportModel.setChildAvatarMediaId("CA0004");
        inKindReportModel.setStaffAvatarMediaId(staffAvatarMediaId);
        inKindReportModel.setEnrollmentId(enrollmentId);
        inKindReportModel.setParentId(parentId);
        inKindReportModel.setUnit(InKindUnit.HOUR.toString());
        inKindReportModel.setRateUnit(InKindUnit.HOUR.toString());
        needRatifyList.setRecords(Collections.singletonList(inKindReportModel));

        final List<MediaEntity> medias = new ArrayList<>();
        List<UserModel> parents = new ArrayList<>();
        UserModel userModel = new UserModel();
        userModel.setId(parentId);
        parents.add(userModel);
        List<InkindDonor> donorList = new ArrayList<>();

        // 接口模拟
        // 获取当前用户信息
        Mockito.when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // 获取园长管理的学校
        Mockito.when(groupDao.getGroupByTeacher(any())).thenReturn(groups);
        // 获取待批准的数据
        Mockito.when(inKindReportApproveDao.getPendingRatifyListByAgencyIdOrUserId(any(), any(), any(), any())).thenReturn(needRatifyList);
        // 查询头像数据
        Mockito.when(mediaDao.getMedias(any())).thenReturn(medias);
        // 获取家长数据
        Mockito.when(userDao.getUsersByUserIdsWithDeleted(any())).thenReturn(parents);
        // 获取第三方捐赠人数据
        Mockito.when(inKindDao.getDonatorByIds(any())).thenReturn(donorList);
        // 获取孩子头像url
        Mockito.when(fileSystem.getChildAvatarUrl(any())).thenReturn("https://www.test.com5");
        // 获取语言
        Mockito.when(userProvider.getCurrentLang()).thenReturn("zh_CN2");
        // 获取老师头像url
        Mockito.when(fileSystem.getUserAvatarUrl(any())).thenReturn("https://www.test.com6");

        GetReviewListResponse reviewList = inKindRatifyService.getReviewList(request);

        Assertions.assertEquals(1, reviewList.getInKindReviews().size());
        Assertions.assertEquals(parentId, reviewList.getInKindReviews().get(0).getParentId());
        Assertions.assertEquals(enrollmentId, reviewList.getInKindReviews().get(0).getEnrollmentId());
        Assertions.assertEquals(InKindUnit.HOUR.toString(), reviewList.getInKindReviews().get(0).getRateUnit());
        Assertions.assertEquals(InKindUnit.HOUR.toString(), reviewList.getInKindReviews().get(0).getUnit());
        Assertions.assertEquals(staffAvatarMediaId, reviewList.getInKindReviews().get(0).getStaffAvatarMediaId());
        Assertions.assertEquals("", reviewList.getInKindReviews().get(0).getRelationship());
        Assertions.assertEquals("", reviewList.getInKindReviews().get(0).getDisplayRelationship());
    }

    /**
     * 测试获取需要签名的列表
     */
    @Test
    void testGetNeedSignatureList() {
        // 数据准备
        AuthUserDetails currentUser = new AuthUserDetails();
        final List<InKindReportModel> inKindReportModels = new ArrayList<>();

        // 接口模拟
        // 获取当前用户
        Mockito.when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // 获取自己已批准的数据
        Mockito.when(inKindReportApproveDao.getNeedSignatureListByUserId(any())).thenReturn(inKindReportModels);

        GetNeedSignListResponse needSignatureList = inKindRatifyService.getNeedSignatureList();
        // 验证
        Assertions.assertEquals(inKindReportModels, needSignatureList.getInKindReviews());
    }

    /**
     * 测试签名提交，管理员批准通过
     * case:
     * 活动类型：DONATION
     * 记住签名
     */
    @Test
    void testSignature() {
        // 调用真实方法
        InKindReportApproveDaoImpl inKindReportApproveDaoSpy = spy(inKindReportApproveDao);
        ReflectionTestUtils.setField(inKindReportApproveDaoSpy, INKIND_REPORT_MODEL_MAPPER, inKindReportModelMapper);
        ReflectionTestUtils.setField(inKindReportApproveDaoSpy, BASE_MAPPER, inKindReportApproveMapper);
        // 注入属性
        ReflectionTestUtils.setField(inKindRatifyService, INKIND_REPORT_APPROVE_DAO, inKindReportApproveDaoSpy);

        final LambdaUpdateChainWrapper<InKindReportApprove> lambdaUpdate = new LambdaUpdateChainWrapper<>(inKindReportApproveMapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaUpdateChain(inKindReportApproveMapper)).thenReturn(lambdaUpdate);

        SignatureRequest request = new SignatureRequest();
        request.setRememberSignature(true);
        request.setSignatureId("S00005");

        InKindReportModel inKindReportModel = new InKindReportModel();
        inKindReportModel.setId("R000012");
        inKindReportModel.setEnrollmentId("E00005");
        inKindReportModel.setType(ActivityTypeValue.DONATION.toString());
        inKindReportModel.setActivityGroupId("G00002");
        inKindReportModel.setUnit(InKindUnit.HOUR.toString());
        inKindReportModel.setRateUnit(InKindUnit.HOUR.toString());
        inKindReportModel.setValue(new BigDecimal("1"));
        inKindReportModel.setMoney(new BigDecimal("2"));
        request.setInKinds(Collections.singletonList(inKindReportModel));

        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setUsername("U00101");

        final List<InKindReportModel> reportModels = new ArrayList<>();
        reportModels.add(inKindReportModel);

        InkindSchoolYearEntity currentSchoolYear = new InkindSchoolYearEntity();
        currentSchoolYear.setShowRate(true);

        // 接口模拟
        // 获取当前用户
        Mockito.when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // 获取自己已批准的数据
        doReturn(reportModels).when(inKindReportApproveDaoSpy).getNeedSignatureListByUserId(any());
        // 获取当前学年
        Mockito.when(inKindCommonService.getCurrentSchoolYear(any())).thenReturn(currentSchoolYear);
        Mockito.when(userDao.getUserById(any())).thenReturn(new UserModel());

        InkindResponse signature = inKindRatifyService.signature(request);

        // 验证
        // 保存签名
        verify(userDao, times(1)).setMetaData(any(), any(), any());
        Assertions.assertEquals("Thanks for your hard work!\nYou have finished reviewing the In-Kind activities.", signature.getMessage());
    }

    /**
     * 测试签名提交，管理员批准通过
     * case:
     * 活动类型：AT_HOME
     * 清除签名
     */
    @Test
    void testSignature2() {
        // 数据准备
        // 调用待测试的方法
        // 调用真实方法
        InKindReportApproveDaoImpl inKindReportApproveDaoSpy = spy(inKindReportApproveDao);
        ReflectionTestUtils.setField(inKindReportApproveDaoSpy, INKIND_REPORT_MODEL_MAPPER, inKindReportModelMapper);
        ReflectionTestUtils.setField(inKindReportApproveDaoSpy, BASE_MAPPER, inKindReportApproveMapper);
        // 注入属性
        ReflectionTestUtils.setField(inKindRatifyService, INKIND_REPORT_APPROVE_DAO, inKindReportApproveDaoSpy);

        final LambdaUpdateChainWrapper<InKindReportApprove> lambdaUpdate = new LambdaUpdateChainWrapper<>(inKindReportApproveMapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaUpdateChain(inKindReportApproveMapper)).thenReturn(lambdaUpdate);

        final String reportId = "R00001";
        final String groupId = "G00003";

        SignatureRequest request = new SignatureRequest();
        request.setRememberSignature(true);
        request.setSignatureId("S00006");

        InKindReportModel inKindReportModel = new InKindReportModel();
        inKindReportModel.setId(reportId);
        inKindReportModel.setEnrollmentId("E000062");
        inKindReportModel.setType(ActivityTypeValue.AT_HOME.toString());
        inKindReportModel.setActivityGroupId(groupId);
        inKindReportModel.setActivityDate(new Date());
        inKindReportModel.setValue(new BigDecimal("100"));
        inKindReportModel.setUnit(InKindUnit.HOUR.toString());
        inKindReportModel.setRateValue(new BigDecimal("101"));
        inKindReportModel.setRateUnit(InKindUnit.HOUR.toString());
        inKindReportModel.setSource(InKindSourceEnum.BASE.getName());
        request.setInKinds(Collections.singletonList(inKindReportModel));
        InKindReportModel inKindReportModel2 = new InKindReportModel();
        inKindReportModel2.setId(reportId);
        inKindReportModel2.setEnrollmentId("E01");
        inKindReportModel2.setType(UserRole.SITE_ADMIN.name());
        inKindReportModel2.setActivityGroupId(groupId);
        inKindReportModel2.setActivityDate(new Date());
        inKindReportModel2.setValue(new BigDecimal("102"));
        inKindReportModel2.setUnit(InKindUnit.HOUR.toString());
        inKindReportModel2.setRateValue(new BigDecimal("3"));
        inKindReportModel2.setRateUnit(InKindUnit.HOUR.toString());
        inKindReportModel2.setSource(InKindSourceEnum.APPEND.getName());

        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setUsername("U000029");

        List<InKindReportModel> reportModels = new ArrayList<>();
        reportModels.add(inKindReportModel);
        reportModels.add(inKindReportModel2);

        final List<InkindActivityGroupEntity> activityGroups = new ArrayList<>();
        final InkindActivityGroupEntity activityGroup = new InkindActivityGroupEntity();
        activityGroup.setId(groupId);
        activityGroup.setUnit(InKindUnit.HOUR.toString());
        activityGroup.setLimitValue(true);
        activityGroup.setValue(new BigDecimal("4"));
        activityGroup.setSourceName(InKindSourceEnum.BASE.getName());
        activityGroup.setLimitValue(true);
        activityGroups.add(activityGroup);

        InkindSchoolYearEntity currentSchoolYear = new InkindSchoolYearEntity();
        currentSchoolYear.setShowRate(true);

        // 接口模拟
        // 获取当前用户
        Mockito.when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // 获取自己已批准的数据
        doReturn(reportModels).when(inKindReportApproveDaoSpy).getNeedSignatureListByUserId(any());
        // 获取活动组
        Mockito.when(inKindDao.getActivityGroup(anyList())).thenReturn(activityGroups);
        // 获取当前学年
        Mockito.when(inKindCommonService.getCurrentSchoolYear(any())).thenReturn(currentSchoolYear);

        Mockito.when(userDao.getUserById(any())).thenReturn(new UserModel());

        InkindResponse signature = inKindRatifyService.signature(request);

        // 验证
        Assertions.assertEquals("Success!", signature.getMessageTitle());
        Assertions.assertEquals("Thanks for your hard work!\n"
                + "You have finished reviewing the In-Kind activities.", signature.getMessage());
        // 批量保存新的操作记录
        verify(inKindReportApproveDaoSpy, times(1)).batchSave(any());
        // 设置状态
        verify(inKindDao, times(1)).batchUpdateReportStatus(any());
        // 设置金额
        verify(inKindDao, times(1)).batchUpdateReportMoney(any());
        // 签名
        verify(userDao, times(1)).setMetaData(any(), any(), any());
    }

    /**
     * 测试管理员进行审批
     */
    @Test
    void testRatify() {
        // 调用真实方法
        InKindReportApproveDaoImpl inKindReportApproveDaoSpy = spy(inKindReportApproveDao);
        ReflectionTestUtils.setField(inKindReportApproveDaoSpy, INKIND_REPORT_MODEL_MAPPER, inKindReportModelMapper);
        ReflectionTestUtils.setField(inKindReportApproveDaoSpy, BASE_MAPPER, inKindReportApproveMapper);
        // 注入属性
        ReflectionTestUtils.setField(inKindRatifyService, INKIND_REPORT_APPROVE_DAO, inKindReportApproveDaoSpy);

        final LambdaUpdateChainWrapper<InKindReportApprove> lambdaUpdate = new LambdaUpdateChainWrapper<>(inKindReportApproveMapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaUpdateChain(inKindReportApproveMapper)).thenReturn(lambdaUpdate);

        final String unit = InKindUnit.MILE.toString();
        final String rateUnit = InKindUnit.MILE.toString();
        final String reportId = "R00002";
        // 数据准备
        RatifyReportRequest request = new RatifyReportRequest();
        request.setSignature(true);
        final List<InKindReportModel> reports = new ArrayList<>();
        final InKindReportModel inKindReportModel = new InKindReportModel();
        inKindReportModel.setId(reportId);
        inKindReportModel.setApproveStatus("APPROVED");
        inKindReportModel.setSource(InKindSourceEnum.BASE.getName());
        inKindReportModel.setMoney(new BigDecimal("103"));
        inKindReportModel.setRateUnit(InKindUnit.HOUR.toString());
        inKindReportModel.setRateValue(new BigDecimal("30"));
        inKindReportModel.setValue(new BigDecimal("40"));
        inKindReportModel.setUnit(InKindUnit.MINUTE.toString());
        InKindReportModel inKindReportModel2 = new InKindReportModel();
        inKindReportModel2.setId(reportId);
        inKindReportModel2.setApproveStatus("REJECTED");
        inKindReportModel2.setSource(InKindSourceEnum.APPEND.getName());
        inKindReportModel2.setRateUnit(InKindUnit.HOUR.toString());
        inKindReportModel2.setRateValue(new BigDecimal("50"));
        inKindReportModel2.setValue(new BigDecimal("60"));
        inKindReportModel2.setUnit(InKindUnit.MINUTE.toString());
        InKindReportModel inKindReportModel3 = new InKindReportModel();
        inKindReportModel3.setId("R000033");
        inKindReportModel3.setApproveStatus("IGNORE");
        inKindReportModel3.setRateUnit(rateUnit);
        inKindReportModel3.setRateValue(new BigDecimal("70"));
        inKindReportModel3.setValue(new BigDecimal("80"));
        inKindReportModel3.setUnit(unit);
        reports.add(inKindReportModel);
        reports.add(inKindReportModel2);
        reports.add(inKindReportModel3);
        request.setReports(reports);
        // request.setStatus("APPROVED");
        request.setSignatureId("S00007");


        AuthUserDetails currentUser = new AuthUserDetails();
        UserProfileEntity userProfile = new UserProfileEntity();

        // 接口模拟
        // 获取当前用户
        Mockito.when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // 获取当前用户的信息
        Mockito.when(userProfileDao.getById(any())).thenReturn(userProfile);

        inKindRatifyService.ratify(request);

        // 验证 -- 方法调用次数
        // 批量更新基础报告状态
        verify(inKindDao, Mockito.times(1)).batchUpdateReportStatus(any());
        // 批量更新追加报告状态
        verify(inKindDao, Mockito.times(1)).batchUpdateAppendReportStatus(any());
        // 批量保存新的操作记录
        verify(inKindReportApproveDaoSpy, Mockito.times(1)).batchSave(any());
    }

    /**
     * 测试获取审批被拒绝列表
     */
    @Test
    void testGetReviseList() {
        // 数据准备
        final String relationship = "Mother";
        final String parentId = "P00005";

        final AuthUserDetails currentUser = new AuthUserDetails();
        final List<InKindReportModel> reports = new ArrayList<>();
        final InKindReportModel inKindReportModel = new InKindReportModel();
        inKindReportModel.setParentId(parentId);
        inKindReportModel.setEnrollmentId("E00006");
        inKindReportModel.setStaffAvatarMediaId("M00001");
        inKindReportModel.setValue(new BigDecimal("5"));
        inKindReportModel.setUnit(InKindUnit.HOUR.toString());
        inKindReportModel.setRateUnit(InKindUnit.HOUR.toString());
        inKindReportModel.setRateValue(new BigDecimal("6"));
        reports.add(inKindReportModel);

        List<UserModel> parents = new ArrayList<>();
        UserModel parent = new UserModel();
        parent.setId(parentId);
        parents.add(parent);
        List<InkindDonor> donorList = new ArrayList<>();
        InkindDonor donor = new InkindDonor();
        donor.setId("D00001");
        donor.setRelationship(relationship);
        donorList.add(donor);
        List<UserEnrollmentEntity> userEnrollmentList = new ArrayList<>();
        // 接口模拟
        // 获取当前用户
        Mockito.when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // 获取当前用户提交被拒绝的报告（非家长提交）
        Mockito.when(inKindReportApproveDao.getReviseList(any())).thenReturn(reports);
        // 获取家长数据
        Mockito.when(userDao.getUsersByUserIdsWithDeleted(any())).thenReturn(parents);
        // 获取第三方数据
        Mockito.when(inKindDao.getDonatorByIds(any())).thenReturn(donorList);
        // 获取家长列表
        Mockito.when(studentDao.getStudentParents(any(), any())).thenReturn(userEnrollmentList);
        // 设置孩子头像
        Mockito.when(fileSystem.getChildAvatarUrl(any())).thenReturn("http://www.b2aidu.com");
        // 设置老师头像
        Mockito.when(fileSystem.getUserAvatarUrl(any())).thenReturn("http://www.baid1u.com/1");

        GetReviseListResponse reviseList = inKindRatifyService.getReviseList();

        // 验证
        Assertions.assertEquals(1, reviseList.getReports().size());
        Assertions.assertEquals(parentId, reviseList.getReports().get(0).getParentId());
        Assertions.assertEquals(inKindReportModel.getEnrollmentId(), reviseList.getReports().get(0).getEnrollmentId());
        Assertions.assertEquals(inKindReportModel.getStaffAvatarMediaId(), reviseList.getReports().get(0).getStaffAvatarMediaId());
    }

    /**
     * 测试首页获取提醒信息
     */
    @Test
    void testGetRatifyByStaff() {
        // 数据准备
        String toId = "A00002";

        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setRole(UserRole.SITE_ADMIN.name());

        final List<Notification> notifications = new ArrayList<>();
        Notification notification = new Notification();
        notification.setFrom("N00001");
        notification.setTo(toId);
        notification.setType(NotificationType.TEACHER_REMIND_ADMIN_RATIFY.toString());
        notifications.add(notification);

        String centerId = "C00005";
        List<CenterEntity> centers = new ArrayList<>();
        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setId(centerId);
        centers.add(centerEntity);


        List<InKindReportModel> reports = new ArrayList<>();
        InKindReportModel inKindReportModel = new InKindReportModel();
        inKindReportModel.setApproveUserId(toId);
        reports.add(inKindReportModel);

        List<CenterGroupModel> centerGroups = new ArrayList<>();
        CenterGroupModel centerGroupModel = new CenterGroupModel();
        centerGroupModel.setCenterId(centerId);
        centerGroups.add(centerGroupModel);


        // 接口模拟
        // 获取当前用户
        Mockito.when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // 查找 Dynamo 中未失效的提醒记录
        Mockito.when(notificationMapper.listByTo(currentUser.getId(), NotificationType.TEACHER_REMIND_ADMIN_RATIFY.toString())).thenReturn(notifications);
        // 获取管理的学校 Id
        Mockito.when(centerDao.getBySiteAdminId(any())).thenReturn(centers);
        // 查找记录中的老师待批准报告
        Mockito.when(inKindReportApproveDao.getRatifyByStaff(any(), any())).thenReturn(reports);
        // 获取老师管理的学校和班级
        Mockito.when(userDao.getCenterGroupByTeacherId(any())).thenReturn(centerGroups);

        RatifyReportByStaffResponse response = inKindRatifyService.getRatifyByStaff();

        // 验证
        Assertions.assertEquals(1, response.getStaffDataList().size());
        Assertions.assertEquals(toId, response.getStaffDataList().get(0).getStaffId());
        Assertions.assertEquals(1, response.getStaffDataList().get(0).getCenterName().size());
        Assertions.assertEquals(1, response.getStaffDataList().get(0).getGroupName().size());


    }

    /**
     * 测试恢复到待审核状态
     */
    @Test
    void testRestoreSignature() {
        // 调用真实方法
        InKindReportApproveDaoImpl inKindReportApproveDaoSpy = spy(inKindReportApproveDao);
        ReflectionTestUtils.setField(inKindReportApproveDaoSpy, INKIND_REPORT_MODEL_MAPPER, inKindReportModelMapper);
        ReflectionTestUtils.setField(inKindReportApproveDaoSpy, BASE_MAPPER, inKindReportApproveMapper);
        // 注入属性
        ReflectionTestUtils.setField(inKindRatifyService, INKIND_REPORT_APPROVE_DAO, inKindReportApproveDaoSpy);

        final LambdaUpdateChainWrapper<InKindReportApprove> lambdaUpdate = new LambdaUpdateChainWrapper<>(inKindReportApproveMapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaUpdateChain(inKindReportApproveMapper)).thenReturn(lambdaUpdate);

        // 数据准备
        final ResubmitRequest request = new ResubmitRequest();
        List<InKindReportModel> reports = new ArrayList<>();
        InKindReportModel inKindReportModel = new InKindReportModel();
        inKindReportModel.setId("R00003");
        inKindReportModel.setSource(InKindSourceEnum.BASE.getName());
        reports.add(inKindReportModel);
        InKindReportModel inKindReportModel2 = new InKindReportModel();
        inKindReportModel2.setId("R0000112");
        inKindReportModel2.setSource(InKindSourceEnum.APPEND.getName());
        reports.add(inKindReportModel2);
        request.setReports(reports);

        AuthUserDetails currentUser = new AuthUserDetails();
        List<InKindReportModel> inKindReportModelList = new ArrayList<>();
        inKindReportModelList.add(inKindReportModel);
        inKindReportModelList.add(inKindReportModel2);

        // 接口模拟
        // 获取当前用户
        Mockito.when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // 查询报告
        doReturn(inKindReportModelList).when(inKindReportApproveDaoSpy).getReportByAgencyId(any(), any());
        inKindRatifyService.restoreSignature(request);

        // 验证 -- 方法调用次数
        // 设置基础报告为待审核状态
        verify(inKindDao, Mockito.times(1)).batchUpdateReportStatus(any());
        // 设置追加报告为待审核状态
        verify(inKindDao, Mockito.times(1)).batchUpdateAppendReportStatus(any());
        // 批量保存新的操作记录
        verify(inKindReportApproveDaoSpy, Mockito.times(1)).batchSave(any());

    }

    /**
     * 测试获取可提醒员工列表
     * case: type: ratify
     */
    @Test
    void testGetStaff() {
        // 数据准备
        final String enrollmentId = "E00011";
        final String agencyId = "A00001";
        final String userId = "A000022";
        // 手动触发缓存信息收集
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), InKindReportModel.class);
        GetStaffRequest request = new GetStaffRequest();
        request.setType(InKindReviewTypeEnum.RATIFY.getName());

        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setRole(UserRole.SITE_ADMIN.name());
        currentUser.setAgencyId(agencyId);

        List<UserModel> admins = new ArrayList<>();
        UserModel userModel = new UserModel();
        userModel.setId(userId);
        admins.add(userModel);
        List<UserModel> siteAdmins = new ArrayList<>();
        UserModel userModel2 = new UserModel();
        userModel2.setId(userId);
        siteAdmins.add(userModel2);

        final Page<InKindReportModel> needRatifyList = new Page<>();
        InKindReportModel inKindReportModel = new InKindReportModel();
        inKindReportModel.setChildAvatarMediaId("CA0006");
        inKindReportModel.setStaffAvatarMediaId("S00008");
        inKindReportModel.setEnrollmentId(enrollmentId);
        inKindReportModel.setParentId("P00006");
        inKindReportModel.setUnit(InKindUnit.HOUR.toString());
        inKindReportModel.setRateUnit(InKindUnit.HOUR.toString());
        needRatifyList.setRecords(Collections.singletonList(inKindReportModel));

        // 接口模拟
        // 获取当前用户
        Mockito.when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // 查找可以批准的管理员
        Mockito.when(userDao.getAgencyAdminsByAgencyId(any())).thenReturn(admins);
        Mockito.when(userDao.getSiteAdminsByCenterIds(any())).thenReturn(siteAdmins);
        // 设置头像
        Mockito.when(fileSystem.getUserAvatarUrl(any())).thenReturn("https://www.test.com7");
        Mockito.when(inKindReportApproveDao.getNeedRatifyListByAgencyId(anyString(), any(), any())).thenReturn(needRatifyList);

        GetStaffResponse response = inKindRatifyService.getStaff(request);

        // 验证
        Assertions.assertEquals(1, response.getStaffs().size());
        Assertions.assertEquals(userId, response.getStaffs().get(0).getId());
        verify(userDao, Mockito.times(1)).getAgencyAdminsByAgencyId(any());
        verify(userDao, Mockito.times(1)).getSiteAdminsByCenterIds(any());
    }

    /**
     * 测试获取可提醒员工列表
     * case:
     * type: approve
     * 角色: SITE_ADMIN
     */
    @Test
    void testGetStaff2() {
        // 数据准备
        // 手动触发缓存信息收集
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), InKindReportModel.class);
        GetStaffRequest request = new GetStaffRequest();
        request.setType("approve");

        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setRole(UserRole.SITE_ADMIN.name());

        List<InKindReportModel> reports = new ArrayList<>();
        InKindReportModel inKindReportModel = new InKindReportModel();
        inKindReportModel.setId("R00004");
        inKindReportModel.setSource(InKindSourceEnum.BASE.getName());
        reports.add(inKindReportModel);

        List<CenterModel> centers = new ArrayList<>();
        CenterModel centerModel = new CenterModel();
        centerModel.setId("C00001");
        centers.add(centerModel);

        List<UserModel> teachers = new ArrayList<>();
        UserModel userModel = new UserModel();
        userModel.setId("T000011");
        userModel.setAvatarMediaUrl("https://www.test.com8");
        teachers.add(userModel);

        // 接口模拟
        // 获取当前用户
        Mockito.when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // 获取报告
        Mockito.when(inKindReportApproveDao.getReportByAgencyId(any(), any())).thenReturn(reports);
        // 获取管理范围内的学校
        Mockito.when(centerDao.getCentersBySiteAdminId(any())).thenReturn(centers);

        inKindRatifyService.getStaff(request);

        // 验证
        verify(centerDao, Mockito.times(1)).getCentersBySiteAdminId(any());
        verify(inKindReportApproveDao, Mockito.times(1)).getReportByAgencyId(any(), any());
    }

    /**
     * 测试获取可提醒员工列表
     * case:
     * type: approve
     * 角色: AGENCY_ADMIN
     */
    @Test
    void testGetStaff3() {
        // 数据准备
        // 手动触发缓存信息收集
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), InKindReportModel.class);
        GetStaffRequest request = new GetStaffRequest();
        request.setType(InKindReviewTypeEnum.APPROVE.getName());
        final String userId = "T0000111";
        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setRole(UserRole.AGENCY_ADMIN.toString());

        List<InKindReportModel> reports = new ArrayList<>();
        InKindReportModel inKindReportModel1 = new InKindReportModel();
        inKindReportModel1.setId("R00005");
        inKindReportModel1.setSource(InKindSourceEnum.BASE.getName());
        reports.add(inKindReportModel1);
        InKindReportModel inKindReportModel2 = new InKindReportModel();
        inKindReportModel2.setId("R000022");
        inKindReportModel2.setApproveUserId(userId);
        inKindReportModel2.setSource(InKindSourceEnum.BASE.getName());
        reports.add(inKindReportModel2);

        List<UserModel> teachers = new ArrayList<>();
        UserModel userModel = new UserModel();
        userModel.setId(userId);
        userModel.setAvatarMediaUrl("https://www.test.com9");
        teachers.add(userModel);

        // 接口模拟
        // 获取当前用户
        Mockito.when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // 获取报告
        Mockito.when(inKindReportApproveDao.getReportByAgencyId(any(), any())).thenReturn(reports);

        inKindRatifyService.getStaff(request);

        // 验证
        verify(inKindReportApproveDao, Mockito.times(1)).getReportByAgencyId(any(), any());
    }

    /**
     * 测试获取过滤条件
     * case:
     * type: ratify
     * 角色: 机构管理员
     */
    @Test
    void testGetFilter() {
        // 数据准备
        // 手动触发缓存信息收集
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), InKindReportModel.class);

        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setRole(UserRole.AGENCY_ADMIN.toString());
        String centerId = "C01013";
        String approveUserId = "U000021";

        final Page<InKindReportModel> page = new Page<>();
        final List<InKindReportModel> needRatifyList = new ArrayList<>();
        InKindReportModel inKindReportModel = new InKindReportModel();
        inKindReportModel.setEnrollmentId("E00003");
        inKindReportModel.setApproveUserId(approveUserId);
        inKindReportModel.setParentId("PU00003");
        inKindReportModel.setCenterId(centerId);
        inKindReportModel.setGroupId("G00004");
        needRatifyList.add(inKindReportModel);
        page.setRecords(needRatifyList);
        page.setTotal(1);

        final List<UserModel> staffs = new ArrayList<>();
        UserModel userModel = new UserModel();
        userModel.setId(approveUserId);
        userModel.setCenterId(centerId);
        userModel.setRole("COLLABORATOR");
        userModel.setAvatarMediaUrl("https://www.test.com110");
        staffs.add(userModel);

        // 接口模拟
        // 获取当前用户
        Mockito.when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // 获取需要审批的报告列表
        Mockito.when(inKindReportApproveDao.getNeedRatifyListByAgencyId(any(), any(), any())).thenReturn(page);
        // 获取所有待批准人的信息
        Mockito.when(userDao.getUsersByUserIdsWithDeleted(any())).thenReturn(staffs);

        GetFilterResponse response = inKindRatifyService.getFilter(InKindReviewTypeEnum.RATIFY.getName());

        // 验证
        verify(inKindReportApproveDao, Mockito.times(1)).getNeedRatifyListByAgencyId(any(), any(), any());
        Assertions.assertEquals(1, response.getCenters().size());
        Assertions.assertEquals(centerId, response.getCenters().get(0).getId());
        Assertions.assertEquals(0, response.getStaffs().size());

    }

    /**
     * 测试获取过滤条件
     * type: ratify
     * 角色: 学校管理员
     */
    @Test
    void testGetFilter2() {
        // 数据准备
        // 手动触发缓存信息收集
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), InKindReportModel.class);

        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setRole(UserRole.SITE_ADMIN.name());
        String centerId = "C0003";
        String approveUserId = "U000023";

        final Page<InKindReportModel> page = new Page<>();
        final List<InKindReportModel> reportModels = new ArrayList<>();
        InKindReportModel inKindReportModel = new InKindReportModel();
        inKindReportModel.setEnrollmentId("E00002");
        inKindReportModel.setApproveUserId(approveUserId);
        inKindReportModel.setParentId("P00014");
        inKindReportModel.setCenterId(centerId);
        inKindReportModel.setGroupId("G00005");
        reportModels.add(inKindReportModel);
        page.setRecords(reportModels);
        page.setTotal(1);

        List<CenterEntity> centers = new ArrayList<>();
        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setId(centerId);
        centers.add(centerEntity);

        List<UserModel> staffs = new ArrayList<>();
        UserModel userModel = new UserModel();
        userModel.setId(approveUserId);
        userModel.setCenterId(centerId);
        staffs.add(userModel);

        // 接口模拟
        // 获取当前用户
        Mockito.when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // 获取需要审批的报告列表
        Mockito.when(inKindReportApproveDao.getNeedRatifyListByAgencyId(any(), any(), any())).thenReturn(page);
        // 获取机构
        Mockito.when(centerDao.getBySiteAdminId(any())).thenReturn(centers);

        GetFilterResponse response = inKindRatifyService.getFilter(InKindReviewTypeEnum.RATIFY.getName());

        // 验证
        verify(centerDao, Mockito.times(1)).getBySiteAdminId(any());
        verify(inKindReportApproveDao, Mockito.times(1)).getNeedRatifyListByAgencyId(any(), any(), any());
        Assertions.assertEquals(1, response.getCenters().size());
        Assertions.assertEquals(centerEntity.getId(), response.getCenters().get(0).getId());
        Assertions.assertEquals(0, response.getStaffs().size());
    }

    /**
     * 测试获取过滤条件
     * type: ratify
     * 角色: 教师
     */
    @Test
    void testGetFilter3() {
        // 数据准备
        // 手动触发缓存信息收集
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), InKindReportModel.class);

        final String approveUserId = "U000024";
        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setRole(UserRole.TEACHING_ASSISTANT.toString());

        List<CenterEntity> centers = new ArrayList<>();
        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setId("testGetFilter3C00001");
        centers.add(centerEntity);

        final Page<InKindReportModel> page = new Page<>();
        final List<InKindReportModel> reportModels = new ArrayList<>();
        InKindReportModel inKindReportModel = new InKindReportModel();
        inKindReportModel.setEnrollmentId("E1");
        inKindReportModel.setApproveUserId(approveUserId);
        inKindReportModel.setParentId("PU00001");
        inKindReportModel.setCenterId(centerEntity.getId());
        inKindReportModel.setGroupId("G00006");
        reportModels.add(inKindReportModel);
        page.setRecords(reportModels);
        page.setTotal(1);

        List<UserModel> staffs = new ArrayList<>();
        UserModel userModel = new UserModel();
        userModel.setId(approveUserId);
        userModel.setCenterId(centerEntity.getId());
        staffs.add(userModel);

        // 接口模拟
        // 获取当前用户
        Mockito.when(userProvider.getCurrentUser()).thenReturn(currentUser);

        GetFilterResponse response = inKindRatifyService.getFilter(InKindReviewTypeEnum.RATIFY.getName());

        // 验证
        Assertions.assertEquals(0, response.getCenters().size());
        Assertions.assertEquals(0, response.getStaffs().size());
    }

    /**
     * 测试获取过滤条件
     * type: pendingRatify
     * 角色: 学校管理员
     */
    @Test
    void testGetFilter4() {
        // 数据准备
        // 手动触发缓存信息收集
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), InKindReportModel.class);

        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setRole(UserRole.SITE_ADMIN.name());

        String centerId = "C001";
        String approveUserId = "U000025";

        final Page<InKindReportModel> page = new Page<>();
        final List<InKindReportModel> reportModels = new ArrayList<>();
        InKindReportModel inKindReportModel = new InKindReportModel();
        inKindReportModel.setEnrollmentId("E00001");
        inKindReportModel.setApproveUserId(approveUserId);
        inKindReportModel.setParentId("P000U3");
        inKindReportModel.setCenterId(centerId);
        inKindReportModel.setGroupId("G00007");
        reportModels.add(inKindReportModel);
        page.setRecords(reportModels);
        page.setTotal(1);

        List<CenterEntity> centers = new ArrayList<>();
        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setId(centerId);
        centers.add(centerEntity);

        List<UserModel> staffs = new ArrayList<>();
        UserModel userModel = new UserModel();
        userModel.setId(approveUserId);
        userModel.setCenterId(centerId);
        staffs.add(userModel);

        // 接口模拟
        // 获取当前用户
        Mockito.when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // 获取需要审批的报告列表
        Mockito.when(inKindReportApproveDao.getPendingRatifyListByAgencyIdOrUserId(any(), any(), any(), any())).thenReturn(page);
        // 获取机构
        Mockito.when(centerDao.getBySiteAdminId(any())).thenReturn(centers);

        GetFilterResponse pendingRatify = inKindRatifyService.getFilter(InKindReviewTypeEnum.PENDING_RATIFY.getName());

        // 验证
        Assertions.assertEquals(1, pendingRatify.getCenters().size());
        Assertions.assertEquals(centerEntity.getId(), pendingRatify.getCenters().get(0).getId());
    }

    /**
     * 测试获取过滤条件
     * type: pendingRatify
     * 角色: 教师
     */
    @Test
    void testGetFilter5() {
        // 数据准备
        // 手动触发缓存信息收集
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), InKindReportModel.class);

        final String approveUserId = "U000026";

        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setRole(UserRole.TEACHING_ASSISTANT.toString());

        List<CenterEntity> centers = new ArrayList<>();
        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setId("testGetFilter5C00001");
        centers.add(centerEntity);

        final Page<InKindReportModel> page = new Page<>();
        final List<InKindReportModel> reportModels = new ArrayList<>();
        InKindReportModel inKindReportModel = new InKindReportModel();
        inKindReportModel.setEnrollmentId("E001");
        inKindReportModel.setApproveUserId(approveUserId);
        inKindReportModel.setParentId("P000U4");
        inKindReportModel.setCenterId(centerEntity.getId());
        inKindReportModel.setGroupId("G00008");
        reportModels.add(inKindReportModel);
        page.setRecords(reportModels);
        page.setTotal(1);

        List<UserModel> staffs = new ArrayList<>();
        UserModel userModel = new UserModel();
        userModel.setId(approveUserId);
        userModel.setCenterId(centerEntity.getId());
        staffs.add(userModel);

        // 接口模拟
        // 获取当前用户
        Mockito.when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // 获取需要审批的报告列表
        Mockito.when(inKindReportApproveDao.getPendingRatifyListByAgencyIdOrUserId(any(), any(), any(), any())).thenReturn(page);

        GetFilterResponse pendingRatify = inKindRatifyService.getFilter(InKindReviewTypeEnum.PENDING_RATIFY.getName());

        // 验证
        Assertions.assertEquals(1, pendingRatify.getCenters().size());
        Assertions.assertEquals(centerEntity.getId(), pendingRatify.getCenters().get(0).getId());

    }

    /**
     * 测试获取小孩的待审核报告
     */
    @Test
    void testGetRatifyReports() {
        // 数据准备
        // 手动触发缓存信息收集
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), InKindReportModel.class);
        String enrollmentId = "U000011";

        EnrollmentEntity enrollment = new EnrollmentEntity();
        enrollment.setId(enrollmentId);
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId("G00009");
        enrollment.setGroup(groupEntity);

        final AuthUserDetails currentUser = new AuthUserDetails();

        final Page<InKindReportModel> page = new Page<>();
        final List<InKindReportModel> reportModels = new ArrayList<>();
        InKindReportModel inKindReportModel = new InKindReportModel();
        inKindReportModel.setId("R00006");
        inKindReportModel.setEnrollmentId(enrollmentId);
        inKindReportModel.setChildName("test");
        inKindReportModel.setValue(new BigDecimal("7"));
        inKindReportModel.setUnit(InKindUnit.HOUR.toString());
        inKindReportModel.setRateUnit(InKindUnit.HOUR.toString());
        inKindReportModel.setParentId("P0000U5");

        InKindReportModel inKindReportModel2 = new InKindReportModel();
        inKindReportModel2.setId("R000023");
        inKindReportModel2.setEnrollmentId("U000027");
        inKindReportModel2.setChildName("test2");
        inKindReportModel2.setValue(new BigDecimal("8"));
        inKindReportModel2.setUnit(InKindUnit.HOUR.toString());
        inKindReportModel2.setRateUnit(InKindUnit.HOUR.toString());
        inKindReportModel2.setParentId("U00004");
        reportModels.add(inKindReportModel);
        reportModels.add(inKindReportModel2);
        page.setRecords(reportModels);
        page.setTotal(1);

        final InkindSchoolYearEntity currentSchoolYear = new InkindSchoolYearEntity();

        List<MediaEntity> medias = new ArrayList<>();
        MediaEntity mediaEntity = new MediaEntity();
        mediaEntity.setId("M0001101");
        mediaEntity.setRelativePath("");
        medias.add(mediaEntity);

        List<UserModel> parents = new ArrayList<>();
        UserModel userModel = new UserModel();
        userModel.setId("U00003");
        userModel.setEnrollmentId(enrollmentId);
        parents.add(userModel);

        // 接口模拟
        // 获取小孩
        Mockito.when(studentDao.getChildWithGroupCenter(any())).thenReturn(enrollment);
        // 获取当前用户
        Mockito.when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // 查找待批准的报告
        Mockito.when(inKindReportApproveDao.getNeedRatifyListByGroupIds(any(), any(), any())).thenReturn(page);
        // 获取学年
        Mockito.when(inKindCommonService.getCurrentSchoolYear(any())).thenReturn(currentSchoolYear);
        // 获取头像
        Mockito.when(mediaDao.getMedias(any())).thenReturn(medias);
        // 获取家长
        Mockito.when(userDao.getAllParentsByStudentIdsV2(any())).thenReturn(parents);
        // 获取孩子头像
        Mockito.when(fileSystem.getChildAvatarUrl(any())).thenReturn("https://www.test.comn");
        // 获取小孩展示姓名
        Mockito.when(inKindCommonService.getEnrollmentDisplayName(any(), any(), any())).thenReturn("displayNameTest");
        // 获取小孩头像url
        Mockito.when(inKindCommonService.getEnrollmentAvatarUrl(any())).thenReturn("avatarTest");

        InkindChildWithStatsResponse ratifyReports = inKindRatifyService.getRatifyReports(enrollmentId, groupEntity.getId());

        // 验证
        Assertions.assertEquals(enrollmentId, ratifyReports.getEnrollmentId());
        Assertions.assertEquals(2, ratifyReports.getReports().size());
        Assertions.assertEquals(inKindReportModel.getId(), ratifyReports.getReports().get(0).getId());
        Assertions.assertEquals(inKindReportModel2.getId(), ratifyReports.getReports().get(1).getId());
    }

    /**
     * 测试转换报告
     * Unit:MINUTE
     * RateUnit:HOUR
     */
    @Test
    void testTransformReport() {
        // 数据准备
        final List<InKindReportModel> reportModels = new ArrayList<>();
        InKindReportModel inKindReportModel = new InKindReportModel();
        inKindReportModel.setUnit(InKindUnit.MINUTE.toString());
        inKindReportModel.setValue(new BigDecimal("9"));
        inKindReportModel.setRateUnit(InKindUnit.HOUR.toString());
        reportModels.add(inKindReportModel);

        // 接口模拟
        List<InkindReportModel> inkindReportModels = inKindRatifyService.transformReport(reportModels, null);

        // 验证
        Assertions.assertEquals(1, inkindReportModels.size());
        Assertions.assertEquals("0.15", inkindReportModels.get(0).getHourValue());
    }

    /**
     * 测试转换报告
     * Unit:HOUR
     * RateUnit:HOUR
     */
    @Test
    void testTransformReport2() {
        // 数据准备
        final List<InKindReportModel> reportModels = new ArrayList<>();
        InKindReportModel inKindReportModel = new InKindReportModel();
        inKindReportModel.setUnit(InKindUnit.HOUR.toString());
        String number = "20";
        inKindReportModel.setValue(new BigDecimal(number));
        inKindReportModel.setRateUnit(InKindUnit.HOUR.toString());
        reportModels.add(inKindReportModel);

        // 接口模拟
        List<InkindReportModel> inkindReportModels = inKindRatifyService.transformReport(reportModels, null);

        // 验证
        Assertions.assertEquals(1, inkindReportModels.size());
        Assertions.assertEquals(number, inkindReportModels.get(0).getHourValue());
    }

    /**
     * 测试转换报告
     * Unit:MILE
     * RateUnit:HOUR
     */
    @Test
    void testTransformReport3() {
        // 数据准备
        final List<InKindReportModel> reportModels = new ArrayList<>();
        InKindReportModel inKindReportModel = new InKindReportModel();
        inKindReportModel.setUnit(InKindUnit.MILE.toString());
        String number = "11";
        inKindReportModel.setValue(new BigDecimal(number));
        inKindReportModel.setRateUnit(InKindUnit.HOUR.toString());
        reportModels.add(inKindReportModel);

        // 接口模拟
        List<InkindReportModel> inkindReportModels = inKindRatifyService.transformReport(reportModels, null);

        // 验证
        Assertions.assertEquals(1, inkindReportModels.size());
        Assertions.assertEquals(number, inkindReportModels.get(0).getMileValue());
    }

    /**
     * 测试获取下一个小孩的待审核报告
     */
    @Test
    void testGetNextRatifyReports() {
        // 数据准备
        // 手动触发缓存信息收集
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), InKindReportModel.class);

        String enrollmentId = "E000011";
        String groupId = "G00010";
        EnrollmentEntity enrollment = new EnrollmentEntity();
        enrollment.setId(enrollmentId);
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId(groupId);
        enrollment.setGroup(groupEntity);

        final AuthUserDetails currentUser = new AuthUserDetails();

        final Page<InKindReportModel> page = new Page<>();
        final List<InKindReportModel> reportModels = new ArrayList<>();
        InKindReportModel inKindReportModel = new InKindReportModel();
        inKindReportModel.setEnrollmentId(enrollmentId);
        inKindReportModel.setChildName("test3");
        inKindReportModel.setValue(new BigDecimal("12"));
        inKindReportModel.setUnit(InKindUnit.HOUR.toString());
        inKindReportModel.setRateUnit(InKindUnit.HOUR.toString());
        inKindReportModel.setParentId("P00U3");
        InKindReportModel inKindReportModel2 = new InKindReportModel();
        inKindReportModel2.setEnrollmentId("U000028");
        inKindReportModel2.setChildName("ctest2");
        inKindReportModel2.setValue(new BigDecimal("13"));
        inKindReportModel2.setUnit(InKindUnit.HOUR.toString());
        inKindReportModel2.setRateUnit(InKindUnit.HOUR.toString());
        reportModels.add(inKindReportModel);
        reportModels.add(inKindReportModel2);
        page.setRecords(reportModels);
        page.setTotal(1);

        final InkindSchoolYearEntity currentSchoolYear = new InkindSchoolYearEntity();

        List<MediaEntity> medias = new ArrayList<>();
        MediaEntity mediaEntity = new MediaEntity();
        mediaEntity.setId("M0002101");
        mediaEntity.setRelativePath("");
        medias.add(mediaEntity);

        List<UserModel> parents = new ArrayList<>();
        UserModel userModel = new UserModel();
        userModel.setId("U003");
        parents.add(userModel);

        // 接口模拟
        // 获取小孩
        Mockito.when(studentDao.getChildWithGroupCenter(any())).thenReturn(enrollment);
        // 获取当前用户
        Mockito.when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // 查找待批准的报告
        Mockito.when(inKindReportApproveDao.getNeedRatifyListByGroupIds(any(), any(), any())).thenReturn(page);
        // 获取学年
        Mockito.when(inKindCommonService.getCurrentSchoolYear(any())).thenReturn(currentSchoolYear);
        // 获取头像
        Mockito.when(mediaDao.getMedias(any())).thenReturn(medias);
        // 获取家长
        Mockito.when(userDao.getAllParentsByStudentIdsV2(any())).thenReturn(parents);
        // 获取孩子头像
        Mockito.when(fileSystem.getChildAvatarUrl(any())).thenReturn("https://www.test.coma");

        InkindChildWithStatsResponse nextRatifyReports = inKindRatifyService.getNextRatifyReports(enrollmentId, groupId);

        // 验证
        Assertions.assertEquals(enrollmentId, nextRatifyReports.getEnrollmentId());
        Assertions.assertEquals(true, nextRatifyReports.getHasReview());
        Assertions.assertEquals(1, nextRatifyReports.getReports().size());
    }

    /**
     * 测试通知消息已读
     */
    @Test
    void testReadNotifications() {
        // 数据准备
        AuthUserDetails currentUser = new AuthUserDetails();
        List<Notification> notifications = new ArrayList<>();
        Notification notification = new Notification();
        notifications.add(notification);

        // 接口模拟
        // 获取当前用户
        Mockito.when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // 查找 Dynamo 中未失效的提醒记录
        Mockito.when(notificationMapper.listByTo(any(), any())).thenReturn(notifications);

        inKindRatifyService.readNotifications("TEACHER_REMIND_ADMIN_RATIFY");

        // 验证
        Mockito.verify(notificationMapper, Mockito.times(1)).batchSave(any());
    }

    /**
     * 测试获取 InKind 旧数据
     */
    @Test
    void testGetOldInKind() {
        // 数据准备
        // 手动触发缓存信息收集
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), InKindReportModel.class);

        final AuthUserDetails currentUser = new AuthUserDetails();
        final List<InKindReportModel> reports = new ArrayList<>();
        InKindReportModel inKindReportModel = new InKindReportModel();
        inKindReportModel.setId("R000062");
        inKindReportModel.setCenterId("testGetOldInKindC00001");
        inKindReportModel.setCenterName("centerTest");
        inKindReportModel.setGroupId("G00011");
        inKindReportModel.setEnrollmentId("E00007");
        inKindReportModel.setUnit(InKindUnit.MINUTE.toString());
        inKindReportModel.setRateUnit(InKindUnit.MINUTE.toString());
        reports.add(inKindReportModel);

        // 接口模拟
        // 获取当前用户
        Mockito.when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // 获取 Wrapper

        // 获取报告
        Mockito.when(inKindReportApproveDao.getReportByAgencyId(any(), any())).thenReturn(reports);

        GetOldInKindResponse oldInKind = inKindRatifyService.getOldInKind();

        // 验证
        Assertions.assertEquals(1, oldInKind.getClassNum());
        Assertions.assertEquals(1, oldInKind.getStudentNum());
        Assertions.assertEquals(1, oldInKind.getActivityNum());
        Assertions.assertEquals(1, oldInKind.getSiteInfoList().size());
        Assertions.assertEquals(inKindReportModel.getCenterName(), oldInKind.getSiteInfoList().get(0).getSiteName());
        Assertions.assertEquals(1, oldInKind.getSiteInfoList().get(0).getClassNum());
        Assertions.assertEquals(1, oldInKind.getSiteInfoList().get(0).getStudentNum());
        Assertions.assertEquals(1, oldInKind.getSiteInfoList().get(0).getActivityNum());
    }

    /**
     * 测试管理员拒绝后，重新提交
     * 审核模式：TEACHER_APPROVE_AND_ADMIN_RATIFY
     * 角色：机构管理员
     * 来源：BASE
     */
    @Test
    void testResubmit() {
        // 调用真实方法
        InKindReportApproveDaoImpl inKindReportApproveDaoSpy = spy(inKindReportApproveDao);
        ReflectionTestUtils.setField(inKindReportApproveDaoSpy, INKIND_REPORT_MODEL_MAPPER, inKindReportModelMapper);
        ReflectionTestUtils.setField(inKindReportApproveDaoSpy, BASE_MAPPER, inKindReportApproveMapper);
        // 注入属性
        ReflectionTestUtils.setField(inKindRatifyService, INKIND_REPORT_APPROVE_DAO, inKindReportApproveDaoSpy);

        final LambdaUpdateChainWrapper<InKindReportApprove> lambdaUpdate = new LambdaUpdateChainWrapper<>(inKindReportApproveMapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaUpdateChain(inKindReportApproveMapper)).thenReturn(lambdaUpdate);


        // 数据准备
        final ResubmitRequest request = new ResubmitRequest();
        final List<InKindReportModel> reports = new ArrayList<>();
        InKindReportModel inKindReportModel = new InKindReportModel();
        inKindReportModel.setId("R00007");
        inKindReportModel.setCenterId("testResubmitC00001");
        inKindReportModel.setType(UserRole.SITE_ADMIN.name());
        inKindReportModel.setEnrollmentId("E00008");
        inKindReportModel.setSource(InKindSourceEnum.BASE.getName());
        reports.add(inKindReportModel);
        request.setReports(reports);

        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setRole(UserRole.AGENCY_ADMIN.toString());
        AgencyMetadata metadata = new AgencyMetadata();
        metadata.setMetaValue(TEACHER_APPROVE_AND_ADMIN_RATIFY);

        // 接口模拟
        // 获取当前用户
        Mockito.when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // 查找机构审核模式
        Mockito.when(agencyMetadataMapper.get(any(), any())).thenReturn(metadata);

        inKindRatifyService.resubmit(request);

        // 验证
        // 批量更新报告状态
        Mockito.verify(inKindDao, Mockito.times(1)).batchUpdateReportStatus(any());
        // 批量保存新的操作记录
        Mockito.verify(inKindReportApproveDaoSpy, Mockito.times(1)).batchSave(any());
    }

    /**
     * 测试管理员拒绝后，重新提交
     * case2: 审核模式：TEACHER_APPROVE_AND_ADMIN_RATIFY
     * 角色：机构管理员
     * 来源：APPEND
     */
    @Test
    void testResubmit2() {
        // 调用真实方法
        InKindReportApproveDaoImpl inKindReportApproveDaoSpy = spy(inKindReportApproveDao);
        ReflectionTestUtils.setField(inKindReportApproveDaoSpy, INKIND_REPORT_MODEL_MAPPER, inKindReportModelMapper);
        ReflectionTestUtils.setField(inKindReportApproveDaoSpy, BASE_MAPPER, inKindReportApproveMapper);
        // 注入属性
        ReflectionTestUtils.setField(inKindRatifyService, INKIND_REPORT_APPROVE_DAO, inKindReportApproveDaoSpy);

        final LambdaUpdateChainWrapper<InKindReportApprove> lambdaUpdate = new LambdaUpdateChainWrapper<>(inKindReportApproveMapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaUpdateChain(inKindReportApproveMapper)).thenReturn(lambdaUpdate);

        // 数据准备
        final ResubmitRequest request = new ResubmitRequest();
        final List<InKindReportModel> reports = new ArrayList<>();
        InKindReportModel inKindReportModel = new InKindReportModel();
        inKindReportModel.setId("R00008");
        inKindReportModel.setCenterId("testResubmit2C00001");
        inKindReportModel.setType(ActivityTypeValue.DONATION.toString());
        inKindReportModel.setSource(InKindSourceEnum.APPEND.getName());
        reports.add(inKindReportModel);
        request.setReports(reports);

        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setRole(UserRole.AGENCY_ADMIN.toString());
        AgencyMetadata metadata = new AgencyMetadata();
        metadata.setMetaValue(TEACHER_APPROVE_AND_ADMIN_RATIFY);

        // 接口模拟
        // 获取当前用户
        Mockito.when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // 查找机构审核模式
        Mockito.when(agencyMetadataMapper.get(any(), any())).thenReturn(metadata);

        inKindRatifyService.resubmit(request);

        // 验证
        // 批量更新追加报告状态
        Mockito.verify(inKindDao, Mockito.times(1)).batchUpdateAppendReportStatus(any());
        // 批量保存新的操作记录
        Mockito.verify(inKindReportApproveDaoSpy, Mockito.times(1)).batchSave(any());
    }

    /**
     * 测试管理员拒绝后，重新提交
     * 审核模式：TEACHER_APPROVE_AND_ADMIN_RATIFY
     * 角色：老师
     */
    @Test
    void testResubmit3() {
        // 调用真实方法
        InKindReportApproveDaoImpl inKindReportApproveDaoSpy = spy(inKindReportApproveDao);
        ReflectionTestUtils.setField(inKindReportApproveDaoSpy, INKIND_REPORT_MODEL_MAPPER, inKindReportModelMapper);
        ReflectionTestUtils.setField(inKindReportApproveDaoSpy, BASE_MAPPER, inKindReportApproveMapper);
        // 注入属性
        ReflectionTestUtils.setField(inKindRatifyService, INKIND_REPORT_APPROVE_DAO, inKindReportApproveDaoSpy);

        final LambdaUpdateChainWrapper<InKindReportApprove> lambdaUpdate = new LambdaUpdateChainWrapper<>(inKindReportApproveMapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaUpdateChain(inKindReportApproveMapper)).thenReturn(lambdaUpdate);

        // 数据准备
        final ResubmitRequest request = new ResubmitRequest();
        final List<InKindReportModel> reports = new ArrayList<>();
        InKindReportModel inKindReportModel = new InKindReportModel();
        inKindReportModel.setId("R00009");
        inKindReportModel.setCenterId("testResubmit3C00001");
        inKindReportModel.setType(ActivityTypeValue.DONATION.toString());
        reports.add(inKindReportModel);
        request.setReports(reports);

        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setRole(UserRole.SITE_ADMIN.name());
        AgencyMetadata metadata = new AgencyMetadata();
        metadata.setMetaValue(TEACHER_APPROVE_AND_ADMIN_RATIFY);

        // 接口模拟
        // 获取当前用户
        Mockito.when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // 查找机构审核模式
        Mockito.when(agencyMetadataMapper.get(any(), any())).thenReturn(metadata);

        inKindRatifyService.resubmit(request);

        // 验证
        Mockito.verify(inKindReportApproveDaoSpy, Mockito.times(1)).batchSave(any());
    }

    /**
     * 测试管理员拒绝后，重新提交
     * 审核模式：TEACHER_APPROVE_AND_ADMIN_RATIFY
     * 角色：园长
     * 且且提交的不是捐赠也不是非家长提交的
     */
    @Test
    void testResubmit4() {
        // 调用真实方法
        InKindReportApproveDaoImpl inKindReportApproveDaoSpy = spy(inKindReportApproveDao);
        ReflectionTestUtils.setField(inKindReportApproveDaoSpy, INKIND_REPORT_MODEL_MAPPER, inKindReportModelMapper);
        ReflectionTestUtils.setField(inKindReportApproveDaoSpy, BASE_MAPPER, inKindReportApproveMapper);
        // 注入属性
        ReflectionTestUtils.setField(inKindRatifyService, INKIND_REPORT_APPROVE_DAO, inKindReportApproveDaoSpy);

        final LambdaUpdateChainWrapper<InKindReportApprove> lambdaUpdate = new LambdaUpdateChainWrapper<>(inKindReportApproveMapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaUpdateChain(inKindReportApproveMapper)).thenReturn(lambdaUpdate);

        // 数据准备
        final ResubmitRequest request = new ResubmitRequest();
        final List<InKindReportModel> reports = new ArrayList<>();
        InKindReportModel inKindReportModel = new InKindReportModel();
        inKindReportModel.setId("R000010");
        inKindReportModel.setCenterId("testResubmit4C00001");
        inKindReportModel.setType(UserRole.SITE_ADMIN.name());
        inKindReportModel.setEnrollmentId("E00009");
        reports.add(inKindReportModel);
        request.setReports(reports);

        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setRole(UserRole.SITE_ADMIN.name());
        final AgencyMetadata metadata = new AgencyMetadata();
        metadata.setMetaValue(TEACHER_APPROVE_AND_ADMIN_RATIFY);

        // 接口模拟
        // 获取当前用户
        Mockito.when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // 查找机构审核模式
        Mockito.when(agencyMetadataMapper.get(any(), any())).thenReturn(metadata);

        inKindRatifyService.resubmit(request);

        // 验证
        Mockito.verify(inKindReportApproveDaoSpy, Mockito.times(1)).batchSave(any());
    }

    /**
     * 测试管理员拒绝后，重新提交
     * 审核模式：TEACHER_APPROVE_SINGLE
     */
    @Test
    void testResubmit5() {
        // 调用真实方法
        InKindReportApproveDaoImpl inKindReportApproveDaoSpy = spy(inKindReportApproveDao);
        ReflectionTestUtils.setField(inKindReportApproveDaoSpy, INKIND_REPORT_MODEL_MAPPER, inKindReportModelMapper);
        ReflectionTestUtils.setField(inKindReportApproveDaoSpy, BASE_MAPPER, inKindReportApproveMapper);
        // 注入属性
        ReflectionTestUtils.setField(inKindRatifyService, INKIND_REPORT_APPROVE_DAO, inKindReportApproveDaoSpy);

        final LambdaUpdateChainWrapper<InKindReportApprove> lambdaUpdate = new LambdaUpdateChainWrapper<>(inKindReportApproveMapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaUpdateChain(inKindReportApproveMapper)).thenReturn(lambdaUpdate);

        // 数据准备
        final ResubmitRequest request = new ResubmitRequest();
        final List<InKindReportModel> reports = new ArrayList<>();
        InKindReportModel inKindReportModel = new InKindReportModel();
        inKindReportModel.setId("R000011");
        inKindReportModel.setCenterId("testResubmit5C00001");
        inKindReportModel.setType(ActivityTypeValue.DONATION.toString());
        reports.add(inKindReportModel);
        request.setReports(reports);

        AuthUserDetails currentUser = new AuthUserDetails();
        currentUser.setRole(UserRole.SITE_ADMIN.name());
        AgencyMetadata metadata = new AgencyMetadata();
        metadata.setMetaValue("TEACHER_APPROVE_SINGLE");

        // 接口模拟
        // 获取当前用户
        Mockito.when(userProvider.getCurrentUser()).thenReturn(currentUser);
        // 查找机构审核模式
        Mockito.when(agencyMetadataMapper.get(any(), any())).thenReturn(metadata);

        inKindRatifyService.resubmit(request);

        // 验证
        Mockito.verify(inKindReportApproveDaoSpy, Mockito.times(1)).batchSave(any());
    }

    /**
     * 测试 inKindReportModels 为空的情况。
     */
    @Test
    void testSetSignatureAndAttachmentWithEmptyModels() {
        List<InKindReportModel> inKindReportModels = Collections.emptyList();

        inKindRatifyService.setSignatureAndAttachment(inKindReportModels);

        assertEquals(0, inKindReportModels.size());
    }

    /**
     * 测试 inKindReportModels 中包含 parentSignatureId 为 null 的情况。
     */
    @Test
    void testSetSignatureAndAttachmentWithNullParentSignatureId() {
        InKindReportModel model = new InKindReportModel();
        model.setId("1");
        model.setParentSignatureId(null);
        List<InKindReportModel> inKindReportModels = Collections.singletonList(model);

        inKindRatifyService.setSignatureAndAttachment(inKindReportModels);

        assertEquals(1, inKindReportModels.size());
        assertEquals(null, inKindReportModels.get(0).getParentSignatureIdAbsoluteUrl());
    }

    /**
     * 测试 parentSignatureId 对应的 MediaEntity 为私有文件的情况
     */
    @Test
    void testSetSignatureAndAttachmentWithPrivateParentSignature() {
        InKindReportModel model = new InKindReportModel();
        model.setId("1");
        model.setParentSignatureId("123");
        List<InKindReportModel> inKindReportModels = Collections.singletonList(model);

        MediaEntity entity = new MediaEntity();
        entity.setId("123");
        entity.setPrivateFile(true);
        entity.setRelativePath("/private/path");

        when(inKindDao.getMediaByIds(anyList())).thenReturn(Collections.singletonList(entity));
        when(fileSystem.getPrivateUrl("/private/path")).thenReturn("privateUrl");

        inKindRatifyService.setSignatureAndAttachment(inKindReportModels);

        assertEquals("privateUrl", model.getParentSignatureIdAbsoluteUrl());
    }

    /**
     * 测试 parentSignatureId 对应的 MediaEntity 为公共文件的情况。
     */
    @Test
    void testSetSignatureAndAttachmentWithPublicParentSignature() {
        InKindReportModel model = new InKindReportModel();
        model.setId("1");
        model.setParentSignatureId("123");
        List<InKindReportModel> inKindReportModels = Collections.singletonList(model);

        MediaEntity entity = new MediaEntity();
        entity.setId("123");
        entity.setPrivateFile(false);
        entity.setRelativePath("/public/path");

        when(inKindDao.getMediaByIds(anyList())).thenReturn(Collections.singletonList(entity));
        when(fileSystem.getPublicUrl("/public/path")).thenReturn("publicUrl");

        inKindRatifyService.setSignatureAndAttachment(inKindReportModels);

        assertEquals("publicUrl", model.getParentSignatureIdAbsoluteUrl());
    }

    /**
     * 测试 parentSignatureId 对应的 MediaEntity 的 id 与 parentSignatureId 不匹配的情况。
     */
    @Test
    void testSetSignatureAndAttachmentWithNonMatchingParentSignature() {
        InKindReportModel model = new InKindReportModel();
        model.setId("1");
        model.setParentSignatureId("123");
        List<InKindReportModel> inKindReportModels = Collections.singletonList(model);

        MediaEntity entity = new MediaEntity();
        entity.setId("456");
        entity.setPrivateFile(true);
        entity.setRelativePath("/private/path");

        when(inKindDao.getMediaByIds(anyList())).thenReturn(Collections.singletonList(entity));

        inKindRatifyService.setSignatureAndAttachment(inKindReportModels);

        assertEquals(null, model.getParentSignatureIdAbsoluteUrl());
    }

    /**
     * 测试 attachment 对应的 MediaEntity 为私有文件的情况。
     */
    @Test
    void testSetSignatureAndAttachmentWithPrivateAttachment() {
        InKindReportModel model = new InKindReportModel();
        model.setId("1");
        model.setParentSignatureId("123");
        List<InKindReportModel> inKindReportModels = Collections.singletonList(model);

        MediaEntity parentSignatureEntity = new MediaEntity();
        parentSignatureEntity.setId("123");
        parentSignatureEntity.setPrivateFile(true);
        parentSignatureEntity.setRelativePath("/private/path");

        MapModel mapModel = new MapModel();
        mapModel.setKey("1");
        mapModel.setValue("456");

        MediaEntity attachmentEntity = new MediaEntity();
        attachmentEntity.setId("456");
        attachmentEntity.setPrivateFile(true);
        attachmentEntity.setRelativePath("/attachment/path");
        attachmentEntity.setFileName("attachmentFile");

        when(inKindDao.getMediaByIds(anyList())).thenReturn(Collections.singletonList(parentSignatureEntity));
        when(inKindDao.getReportIdAndMediaIdByReportId(anyList())).thenReturn(Collections.singletonList(mapModel));
        when(inKindDao.getMediasByReportIds(anyList())).thenReturn(Collections.singletonList(attachmentEntity));
        when(fileSystem.getPrivateUrl("/private/path")).thenReturn("privateUrl");
        when(fileSystem.getPrivateUrl("/attachment/path")).thenReturn("attachmentPrivateUrl");

        inKindRatifyService.setSignatureAndAttachment(inKindReportModels);

        assertEquals("privateUrl", model.getParentSignatureIdAbsoluteUrl());
        assertEquals("attachmentPrivateUrl", model.getAttachmentUrl());
        assertEquals("attachmentFile", model.getAttachmentName());
    }

    /**
     * 测试 attachment 对应的 MediaEntity 为公共文件的情况。
     */
    @Test
    void testSetSignatureAndAttachmentWithPublicAttachment() {
        InKindReportModel model = new InKindReportModel();
        model.setId("1");
        model.setParentSignatureId("123");
        List<InKindReportModel> inKindReportModels = Collections.singletonList(model);

        MediaEntity parentSignatureEntity = new MediaEntity();
        parentSignatureEntity.setId("123");
        parentSignatureEntity.setPrivateFile(true);
        parentSignatureEntity.setRelativePath("/private/path");

        MapModel mapModel = new MapModel();
        mapModel.setKey("1");
        mapModel.setValue("456");

        MediaEntity attachmentEntity = new MediaEntity();
        attachmentEntity.setId("456");
        attachmentEntity.setPrivateFile(false);
        attachmentEntity.setRelativePath("/attachment/path");
        attachmentEntity.setFileName("attachmentFile");

        when(inKindDao.getMediaByIds(anyList())).thenReturn(Collections.singletonList(parentSignatureEntity));
        when(inKindDao.getReportIdAndMediaIdByReportId(anyList())).thenReturn(Collections.singletonList(mapModel));
        when(inKindDao.getMediasByReportIds(anyList())).thenReturn(Collections.singletonList(attachmentEntity));
        when(fileSystem.getPrivateUrl("/private/path")).thenReturn("privateUrl");
        when(fileSystem.getPublicUrl("/attachment/path")).thenReturn("attachmentPublicUrl");

        inKindRatifyService.setSignatureAndAttachment(inKindReportModels);

        assertEquals("privateUrl", model.getParentSignatureIdAbsoluteUrl());
        assertEquals("attachmentPublicUrl", model.getAttachmentUrl());
        assertEquals("attachmentFile", model.getAttachmentName());
    }

    /**
     * 测试 attachment 对应的 MediaEntity 的 id 与 attachment 不匹配的情况。
     */
    @Test
    void testSetSignatureAndAttachmentWithNonMatchingAttachment() {
        InKindReportModel model = new InKindReportModel();
        model.setId("1");
        model.setParentSignatureId("123");
        List<InKindReportModel> inKindReportModels = Collections.singletonList(model);

        MediaEntity parentSignatureEntity = new MediaEntity();
        parentSignatureEntity.setId("123");
        parentSignatureEntity.setPrivateFile(true);
        parentSignatureEntity.setRelativePath("/private/path");

        MapModel mapModel = new MapModel();
        mapModel.setKey("1");
        mapModel.setValue("789");

        MediaEntity attachmentEntity = new MediaEntity();
        attachmentEntity.setId("456");
        attachmentEntity.setPrivateFile(true);
        attachmentEntity.setRelativePath("/attachment/path");

        when(inKindDao.getMediaByIds(anyList())).thenReturn(Collections.singletonList(parentSignatureEntity));
        when(inKindDao.getReportIdAndMediaIdByReportId(anyList())).thenReturn(Collections.singletonList(mapModel));
        when(inKindDao.getMediasByReportIds(anyList())).thenReturn(Collections.singletonList(attachmentEntity));
        when(fileSystem.getPrivateUrl("/private/path")).thenReturn("privateUrl");

        inKindRatifyService.setSignatureAndAttachment(inKindReportModels);

        assertEquals("privateUrl", model.getParentSignatureIdAbsoluteUrl());
        assertEquals(null, model.getAttachmentUrl());
        assertEquals(null, model.getAttachmentName());
    }

    /**
     * 测试 inKindReportModels 中包含多个 InKindReportModel 的情况。
     */
    @Test
    void testSetSignatureAndAttachmentWithMultipleModels() {
        InKindReportModel model1 = new InKindReportModel();
        model1.setId("1");
        model1.setParentSignatureId("123");
        InKindReportModel model2 = new InKindReportModel();
        model2.setId("2");
        model2.setParentSignatureId("456");
        List<InKindReportModel> inKindReportModels = Arrays.asList(model1, model2);

        MediaEntity parentSignatureEntity1 = new MediaEntity();
        parentSignatureEntity1.setId("123");
        parentSignatureEntity1.setPrivateFile(true);
        parentSignatureEntity1.setRelativePath("/private/path1");

        MediaEntity parentSignatureEntity2 = new MediaEntity();
        parentSignatureEntity2.setId("456");
        parentSignatureEntity2.setPrivateFile(false);
        parentSignatureEntity2.setRelativePath("/public/path2");

        MapModel mapModel1 = new MapModel();
        mapModel1.setKey("1");
        mapModel1.setValue("789");

        MapModel mapModel2 = new MapModel();
        mapModel2.setKey("2");
        mapModel2.setValue("101");

        MediaEntity attachmentEntity1 = new MediaEntity();
        attachmentEntity1.setId("789");
        attachmentEntity1.setPrivateFile(true);
        attachmentEntity1.setRelativePath("/attachment/path1");
        attachmentEntity1.setFileName("attachmentFile1");

        MediaEntity attachmentEntity2 = new MediaEntity();
        attachmentEntity2.setId("101");
        attachmentEntity2.setPrivateFile(false);
        attachmentEntity2.setRelativePath("/attachment/path2");
        attachmentEntity2.setFileName("attachmentFile2");

        when(inKindDao.getMediaByIds(anyList())).thenReturn(Arrays.asList(parentSignatureEntity1, parentSignatureEntity2));
        when(inKindDao.getReportIdAndMediaIdByReportId(anyList())).thenReturn(Arrays.asList(mapModel1, mapModel2));
        when(inKindDao.getMediasByReportIds(anyList())).thenReturn(Arrays.asList(attachmentEntity1, attachmentEntity2));
        when(fileSystem.getPrivateUrl("/private/path1")).thenReturn("privateUrl1");
        when(fileSystem.getPublicUrl("/public/path2")).thenReturn("publicUrl2");
        when(fileSystem.getPrivateUrl("/attachment/path1")).thenReturn("attachmentPrivateUrl1");
        when(fileSystem.getPublicUrl("/attachment/path2")).thenReturn("attachmentPublicUrl2");

        inKindRatifyService.setSignatureAndAttachment(inKindReportModels);

        assertEquals("privateUrl1", model1.getParentSignatureIdAbsoluteUrl());
        assertEquals("attachmentPrivateUrl1", model1.getAttachmentUrl());
        assertEquals("attachmentFile1", model1.getAttachmentName());

        assertEquals("publicUrl2", model2.getParentSignatureIdAbsoluteUrl());
        assertEquals("attachmentPublicUrl2", model2.getAttachmentUrl());
        assertEquals("attachmentFile2", model2.getAttachmentName());
    }

}

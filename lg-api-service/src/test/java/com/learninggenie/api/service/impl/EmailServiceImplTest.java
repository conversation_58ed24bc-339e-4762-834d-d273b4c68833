package com.learninggenie.api.service.impl;

import com.learninggenie.api.model.UploadFileRequest;
import com.learninggenie.api.model.email.SendEmailBaseRequest;
import com.learninggenie.api.model.email.SendEmailRequest;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.entity.MediaEntity;
import com.learninggenie.common.data.entity.UserEntity;
import com.learninggenie.common.data.model.CenterWithIdName;
import com.learninggenie.common.data.model.UserModel;
import com.learninggenie.common.data.repository.MediaRepository;
import com.learninggenie.common.filesystem.FileSystem;
import com.learninggenie.common.messaging.EmailModel;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.mockito.Mockito.*;

/**
 * Created by Zjj on 2017/3/6.
 */
@RunWith(MockitoJUnitRunner.class)
public class EmailServiceImplTest {
    @InjectMocks
    private EmailServiceImpl emailService;
    @Mock
    private com.learninggenie.common.messaging.EmailService service;
    @Mock
    private FileSystem fileSystem;
    @Mock
    private MediaRepository mediaRepository;
    @Mock
    private UserDaoImpl userDao;
    @Mock
    private UserProvider userProvider;

    @Test
    public void sendEmailWithAttachment() throws Exception {
        SendEmailBaseRequest request = new SendEmailBaseRequest();
        request.getTo().add("01@q.q");
        request.getTo().add("02@q.q");
        request.setSubject("subject");
        request.setContent("content");
        UploadFileRequest fileRequest = new UploadFileRequest();
        fileRequest.setFileId("f001");
        fileRequest.setFileName("f001.txt");
        request.getAttachments().add(fileRequest);
        UserEntity user = new UserEntity();
        user.setId("u001Z");
        user.setEmail("email@q.q");
        user.setFirstName("fName");
        user.setLastName("lName");
        when(userProvider.checkUser(anyString())).thenReturn(user);
        MediaEntity media = new MediaEntity();
        media.setRelativePath("path1");
        when(mediaRepository.findById(anyString())).thenReturn(Optional.of(media));
        InputStream inputStream = new InputStream() {
            @Override
            public int read() throws IOException {
                return -1;
            }
        };
        when(fileSystem.getFileStream(anyString())).thenReturn(inputStream);
        emailService.sendEmailWithAttachment(request,anyString());
        verify(service,times(1)).sendAsync(any(EmailModel.class));
    }

    @Test
    public void sendEmail() throws Exception {
        String userId = "u001";
        SendEmailRequest request = new SendEmailRequest();
        request.getTo().add("e1@q.q");
        request.getTo().add("e3@q.q");
        request.getTo().add("e2@q.q");
        request.setSubject("subject");
        request.setContent("content");
        UploadFileRequest fileRequest = new UploadFileRequest();
        fileRequest.setFileId("f001");
        fileRequest.setFileName("f001.txt");
        request.getAttachments().add(fileRequest);
        UserEntity user = new UserEntity();
        user.setId("u001Z");
        user.setEmail("email@q.q");
        user.setFirstName("fName");
        user.setLastName("lName");
        when(userProvider.checkUser(anyString())).thenReturn(user);
        MediaEntity media = new MediaEntity();
        media.setRelativePath("path1");
        when(mediaRepository.findById(anyString())).thenReturn(Optional.of(media));
        InputStream inputStream = new InputStream() {
            @Override
            public int read() throws IOException {
                return -1;
            }
        };
        when(fileSystem.getFileStream(anyString())).thenReturn(inputStream);
        emailService.sendEmail(request,userId);
        verify(service,times(1)).sendAsync(any(EmailModel.class));
    }

    @Test
    public void sendEmailToAllUsers() throws Exception {
        String userId = "u001";
        SendEmailRequest request = new SendEmailRequest();
        request.getTo().add("e1@q.q");
        request.getTo().add("e3@q.q");
        request.getTo().add("e2@q.q");
        request.setSubject("subject");
        request.setContent("content");
        request.setToAllParents(true);
        request.setToAllStaffs(true);
        UploadFileRequest fileRequest = new UploadFileRequest();
        fileRequest.setFileId("f001");
        fileRequest.setFileName("f001.txt");
        request.getAttachments().add(fileRequest);
        List<CenterWithIdName> centers = new ArrayList<>();
        CenterWithIdName center1 = new CenterWithIdName();
        center1.setId("c001");
        center1.setName("cName");
        centers.add(center1);
//        when(userDao.getCenterIdNameByAgencyAdmin(anyString())).thenReturn(centers);
        //all parents
        List<UserModel> parents = new ArrayList<>();
        UserModel parent1 = new UserModel();
        parent1.setEmail("p1");
        parents.add(parent1);
//        when(userDao.getParentsByCenterId(anyString())).thenReturn(parents);
        //all staffs
        List<UserModel> staffs = new ArrayList<>();
        UserModel siteAdmins = new UserModel();
        siteAdmins.setEmail("s1");
        staffs.add(siteAdmins);
        //teachers
        List<UserModel> teachers = new ArrayList<>();
        UserModel teacher = new UserModel();
        teacher.setEmail("p1");
        teachers.add(teacher);
//        when(userDao.getSiteAdminsByCenterIds(anyList())).thenReturn(staffs);
//        when(userDao.getTeachersByCenterIds(anyList())).thenReturn(teachers);
        UserEntity user = new UserEntity();
        user.setId("u001Z");
        user.setEmail("email@q.q");
        user.setFirstName("fName");
        user.setLastName("lName");
        when(userProvider.checkUser(anyString())).thenReturn(user);
        MediaEntity media = new MediaEntity();
        media.setRelativePath("path1");
        when(mediaRepository.findById(anyString())).thenReturn(Optional.of(media));
        InputStream inputStream = new InputStream() {
            @Override
            public int read() throws IOException {
                return -1;
            }
        };
        when(fileSystem.getFileStream(anyString())).thenReturn(inputStream);
        emailService.sendEmail(request,userId);
        verify(service,times(1)).sendAsync(any(EmailModel.class));
    }

}
package com.learninggenie.api.service.impl;


import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.model.MetaDataResponse;
import com.learninggenie.api.model.Status;
import com.learninggenie.api.model.ViewResponse;
import com.learninggenie.api.model.note.NoteModel;
import com.learninggenie.api.model.note.*;
import com.learninggenie.api.provider.CenterProvider;
import com.learninggenie.api.provider.NoteProvider;
import com.learninggenie.api.provider.RemoteProvider;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.service.AgencyService;
import com.learninggenie.api.service.NoteService;
import com.learninggenie.common.data.dao.*;
import com.learninggenie.common.data.dao.impl.FormsFormDaoImpl;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.dao.notes.NoteLearningStoryDao;
import com.learninggenie.common.data.entity.CenterEntity;
import com.learninggenie.common.data.entity.GroupEntity;
import com.learninggenie.common.data.entity.NoteEntity;
import com.learninggenie.common.data.entity.UserEntity;
import com.learninggenie.common.data.entity.*;
import com.learninggenie.common.data.entity.notes.NoteLearningStoryEntity;
import com.learninggenie.common.data.enums.AgencyMetaKey;
import com.learninggenie.common.data.enums.ApproveStatus;
import com.learninggenie.common.data.enums.UserRole;
import com.learninggenie.common.data.model.*;
import com.learninggenie.common.data.model.form.MediaFormEntity;
import com.learninggenie.common.data.model.note.NoteDomainModel;
import com.learninggenie.common.data.model.note.NoteMediaModel;
import com.learninggenie.common.data.model.note.NoteTagModel;
import com.learninggenie.common.data.model.note.*;
import com.learninggenie.common.data.repository.EnrollmentRepository;
import com.learninggenie.common.filesystem.FileSystem;
import com.learninggenie.common.region.RegionService;
import com.learninggenie.common.utils.TimeUtil;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicReference;
import java.util.Optional;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class NoteServiceImplTest {
    @InjectMocks
    private NoteServiceImpl noteServiceImpl;
    @Mock
    private NoteDao noteDao;
    @Mock
    private UserDaoImpl userDao;
    @Mock
    private DomainDao domainDao;
    @Mock
    private CenterDao centerDao;
    @Mock
    private GroupDao groupDao;
    @Mock
    private StudentDao studentDao;
    @Mock
    private UserProvider userProvider;
    @Mock
    private MediaDao mediaDao;
    @Mock
    private RemoteProvider remoteProvider;
    @Mock
    private EnrollmentRepository enrollmentRepository;
    @Mock
    private CenterProvider centerProvider;
    @Mock
    private AgencyService agencyService;
    @Mock
    private FileSystem fileSystem;

    @Mock
    private DropOffNoteServiceImpl dropOffNoteService;

    @Mock
    private NoteService noteService;

    @Mock
    private NoteProvider noteProvider;

    @Mock
    private EventDao eventDao;

    @Mock
    private RegionService regionService;

    @Mock
    private ReportDao reportDao;

    @Mock
    private TagDao tagDao;

    @Mock
    private LessonDao lessonDao;

    @Mock
    private MediaBookDao mediaBookDao;

    @Mock
    private DailyReportServiceImpl dailyReportService;

    @Mock
    private AgencyDao agencyDao;

    @Mock
    private NoteLearningStoryDao noteLearningStoryDao;

    @Mock
    private FormsFormDaoImpl formsFormDao;

    @Mock
    private FormsQuestionEntityDao questionEntityDao;

    @Mock
    private FormsOptionDao optionDao;

    @Test
    public void testUpdateNoteV2() {
        UpdateNoteRequest updateNoteRequest = new UpdateNoteRequest();
        updateNoteRequest.setId("1");
        updateNoteRequest.setUserId("001");
        updateNoteRequest.setCreateTime("2020-01-01 00:00:00");
        updateNoteRequest.setCurrentTime("2020-01-01 00:00:00");
        updateNoteRequest.setChildIds("001");
        updateNoteRequest.setChildren("001");
        NoteEntity noteEntity = new NoteEntity();
        noteEntity.setType("");

        when(noteDao.getNote(any())).thenReturn(noteEntity);
        when(domainDao.getDomainByNoteId(any())).thenReturn(new ArrayList<>());
        when(studentDao.getEnrollmentByNoteId(any())).thenReturn(new ArrayList<>());
        when(mediaDao.getMediaByNoteId(any())).thenReturn(new ArrayList<>());
        when(noteDao.getTagEntityByNoteId(any())).thenReturn(new ArrayList<>());
        when(groupDao.getGroupEntityByNoteId(any())).thenReturn(new ArrayList<>());
        when(centerDao.getCenterByNoteId(any())).thenReturn(new ArrayList<>());
        when(centerDao.getCenterByNoteId(any())).thenReturn(new ArrayList<>());
        when(agencyService.getPushStatus(any())).thenReturn(new Status());
        EnrollmentEntity enrollmentEntity = new EnrollmentEntity();
        enrollmentEntity.setId("001");
        GroupEntity group = new GroupEntity();
        group.setId("001");
        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setId("001");
        group.setCenter(centerEntity);

        enrollmentEntity.setGroup(group);
        when(enrollmentRepository.findById(any())).thenReturn(Optional.of(enrollmentEntity));
        when(userProvider.checkUser(any())).thenReturn(new UserEntity());
        when(centerProvider.getAcademyOpen(any(), any())).thenReturn(false);
        when(noteDao.getEnrollmentByOneNote(any())).thenReturn(new ArrayList<NoteEnrollment>() {
            {
                NoteEnrollment enrollment = new NoteEnrollment();
                enrollment.setEnrollmentId("002");
                add(enrollment);
            }
        });

        noteServiceImpl.updateNoteV2(updateNoteRequest);
        verify(remoteProvider, times(1))
                .callDashboardUpsertServer(any(), any(), any());


    }

    /**
     * 测试更新批量 Note
     */
    @Test
    public void testUpdateNoteV2BatchNote() {
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("00111");

        String userId = "001";
        UpdateNoteRequest updateNoteRequest = new UpdateNoteRequest();
        updateNoteRequest.setId("1");
        updateNoteRequest.setUserId(userId);
        updateNoteRequest.setCreateTime("2020-01-01 00:00:00");
        updateNoteRequest.setCurrentTime("2020-01-01 00:00:00");
        updateNoteRequest.setChildIds("001");
        updateNoteRequest.setChildren("001");
        updateNoteRequest.setRelatedId("relatedId");
        updateNoteRequest.setClasses("class1");
        NoteEntity noteEntity = new NoteEntity();
        noteEntity.setType("");

        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agencyModel);
        when(agencyDao.isBatchAddOpen(agencyModel.getId(), "BOOKLEARNINGMEDIA_BATCHCREATE")).thenReturn(false);
        when(noteDao.getNote(any())).thenReturn(noteEntity);
        UserModel userModel = new UserModel();
        userModel.setId(userId);
        when(userDao.getUserById(userId)).thenReturn(userModel);
        List<CenterEntity> selectedSchools = new ArrayList<>();
        when(centerDao.getCentersByGroupIds(anyList())).thenReturn(selectedSchools);
        when(domainDao.getDomainByNoteId(any())).thenReturn(new ArrayList<>());
        when(studentDao.getEnrollmentByNoteId(any())).thenReturn(new ArrayList<>());
        when(mediaDao.getMediaByNoteId(any())).thenReturn(new ArrayList<>());
        when(noteDao.getTagEntityByNoteId(any())).thenReturn(new ArrayList<>());
        when(groupDao.getGroupEntityByNoteId(any())).thenReturn(new ArrayList<>());
        when(centerDao.getCenterByNoteId(any())).thenReturn(new ArrayList<>());
        when(centerDao.getCenterByNoteId(any())).thenReturn(new ArrayList<>());
        when(agencyService.getPushStatus(any())).thenReturn(new Status());
        EnrollmentEntity enrollmentEntity = new EnrollmentEntity();
        enrollmentEntity.setId("001");
        GroupEntity group = new GroupEntity();
        group.setId("001");
        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setId("001");
        group.setCenter(centerEntity);

        enrollmentEntity.setGroup(group);
        when(enrollmentRepository.findById(any())).thenReturn(Optional.of(enrollmentEntity));
        when(userProvider.checkUser(any())).thenReturn(new UserEntity());
        when(centerProvider.getAcademyOpen(any(), any())).thenReturn(false);
        when(noteDao.getEnrollmentByOneNote(any())).thenReturn(new ArrayList<NoteEnrollment>() {
            {
                NoteEnrollment enrollment = new NoteEnrollment();
                enrollment.setEnrollmentId("002");
                add(enrollment);
            }
        });

        noteServiceImpl.updateNoteV2(updateNoteRequest);
        verify(remoteProvider, times(1))
                .callDashboardUpsertServer(any(), any(), any());


    }



    /**
     * 测试获取媒体信息
     * 参数错误
     */
    @Test
    public void testGetMediaInfoParamError() {
        noteServiceImpl.getMediaInfo(null);
        verify(mediaDao, times(0)).getNoteMediaById(anyString());
        verify(fileSystem, times(0)).getUncompressedPublicUrl(anyString());
        verify(fileSystem, times(0)).getPublicUrl(anyString());
    }

    /**
     * 测试获取媒体信息
     * ID 不存在
     */
    @Test
    public void testGetMediaInfoParamErrorMediaInvalid() {
        String mediaId = UUID.randomUUID().toString();
        when(mediaDao.getNoteMediaById(anyString())).thenReturn(null);
        noteServiceImpl.getMediaInfo(mediaId);
        verify(mediaDao, times(1)).getNoteMediaById(anyString());
        verify(fileSystem, times(0)).getUncompressedPublicUrl(anyString());
        verify(fileSystem, times(0)).getPublicUrl(anyString());
    }

    /**
     * 测试获取媒体信息
     * 正常情况，视频已压缩
     */
    @Test
    public void testGetMediaInfoParamErrorCompressed() {
        String mediaId = UUID.randomUUID().toString();
        // 媒体信息
        NoteMediaModel mediaEntity = new NoteMediaModel();
        mediaEntity.setCompressed(true);
        mediaEntity.setWeb(true);
        mediaEntity.setRelativePath("test");
        when(mediaDao.getNoteMediaById(anyString())).thenReturn(mediaEntity);
        noteServiceImpl.getMediaInfo(mediaId);
        verify(mediaDao, times(1)).getNoteMediaById(anyString());
        verify(fileSystem, times(0)).getUncompressedPublicUrl(anyString());
        verify(fileSystem, times(1)).getPublicUrl(anyString());
    }

    /**
     * 测试获取媒体信息
     * 正常情况，视频未压缩
     */
    @Test
    public void testGetMediaInfoParamNotCompress() {
        String mediaId = UUID.randomUUID().toString();
        // 媒体信息
        NoteMediaModel mediaEntity = new NoteMediaModel();
        mediaEntity.setCompressed(false);
        mediaEntity.setWeb(true);
        mediaEntity.setRelativePath("test");
        when(mediaDao.getNoteMediaById(anyString())).thenReturn(mediaEntity);
        noteServiceImpl.getMediaInfo(mediaId);
        verify(mediaDao, times(1)).getNoteMediaById(mediaId);
        verify(fileSystem, times(1)).getUncompressedPublicUrl(anyString());
        verify(fileSystem, times(0)).getPublicUrl(anyString());
    }


    /**
     * 测试获取媒体信息 单个班级
     */
    @Test
    public void testGetBatchAddTypeOneClass() {
        // Mock userDao
        UserModel userModel = new UserModel();
        userModel.setRole(UserRole.AGENCY_ADMIN.toString());
        when(userDao.getUserById(anyString())).thenReturn(userModel);

        // Mock centerDao
        List<CenterEntity> centerEntities = new ArrayList<>();
        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setId("001");
        centerEntities.add(centerEntity);
        when(centerDao.getCentersByGroupIds(anyList())).thenReturn(centerEntities);
//        when(centerDao.getCentersSizeByAgencyId(anyString())).thenReturn(1);

        // Mock groupDao
        when(groupDao.getGroupsNumByCenterId(anyString())).thenReturn(1);

        // Mock userProvider
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("001");
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agencyModel);

        // 调用方法
        String result = noteServiceImpl.getBatchAddType("001", "001");

        // 验证结果
        assertEquals("", result);
    }

    /**
     * 测试获取媒体信息 全部班级
     */
    @Test
    public void testGetBatchAddTypeAllClass() {
        // Mock userDao
        UserModel userModel = new UserModel();
        userModel.setRole(UserRole.AGENCY_ADMIN.toString());
        when(userDao.getUserById(anyString())).thenReturn(userModel);

        // Mock centerDao
        List<CenterEntity> centerEntities = new ArrayList<>();
        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setId("001");
        centerEntities.add(centerEntity);
        when(centerDao.getCentersByGroupIds(anyList())).thenReturn(centerEntities);
//        when(centerDao.getCentersSizeByAgencyId(anyString())).thenReturn(1);

        // Mock groupDao
        when(groupDao.getGroupsNumByCenterId(anyString())).thenReturn(2);

        // Mock userProvider
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("001");
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agencyModel);

        // 调用方法
        String result = noteServiceImpl.getBatchAddType("001,002", "001");

        // 验证结果
        assertEquals("All classes", result);
    }

    /**
     * 测试获取媒体信息 多个班级
     */
    @Test
    public void testGetBatchAddTypeMultiClass() {
        // Mock userDao
        UserModel userModel = new UserModel();
        userModel.setRole(UserRole.AGENCY_ADMIN.toString());
        when(userDao.getUserById(anyString())).thenReturn(userModel);

        // Mock centerDao
        List<CenterEntity> centerEntities = new ArrayList<>();
        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setId("001");
        centerEntities.add(centerEntity);
        when(centerDao.getCentersByGroupIds(anyList())).thenReturn(centerEntities);
//        when(centerDao.getCentersSizeByAgencyId(anyString())).thenReturn(1);

        // Mock groupDao
        when(groupDao.getGroupsNumByCenterId(anyString())).thenReturn(5);

        // Mock userProvider
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("001");
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agencyModel);

        // 调用方法
        String result = noteServiceImpl.getBatchAddType("001,002,003", "001");

        // 验证结果
        assertEquals("Multiple classes", result);
    }

    /**
     * 测试获取媒体信息 多个学校
     */
    @Test
    public void testGetBatchAddTypeMultiSite() {
        // Mock userDao
        UserModel userModel = new UserModel();
        userModel.setRole(UserRole.AGENCY_ADMIN.toString());
        when(userDao.getUserById(anyString())).thenReturn(userModel);

        // Mock centerDao
        List<CenterEntity> centerEntities = new ArrayList<>();
        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setId("001");

        CenterEntity centerEntity2 = new CenterEntity();
        centerEntity.setId("002");

        centerEntities.add(centerEntity);
        centerEntities.add(centerEntity2);

        when(centerDao.getCentersByGroupIds(anyList())).thenReturn(centerEntities);
        when(centerDao.getCentersSizeByAgencyId(anyString())).thenReturn(3);

        // Mock userProvider
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("001");
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agencyModel);

        // 调用方法
        String result = noteServiceImpl.getBatchAddType("001,002,003", "001");

        // 验证结果
        assertEquals("Multiple sites", result);
    }

    /**
     * 测试获取媒体信息 全部学校
     */
    @Test
    public void testGetBatchAddTypeAllSite() {
        // Mock userDao
        UserModel userModel = new UserModel();
        userModel.setRole(UserRole.AGENCY_ADMIN.toString());
        when(userDao.getUserById(anyString())).thenReturn(userModel);

        // Mock centerDao
        List<CenterEntity> centerEntities = new ArrayList<>();
        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setId("001");

        CenterEntity centerEntity2 = new CenterEntity();
        centerEntity.setId("002");

        CenterEntity centerEntity3 = new CenterEntity();
        centerEntity.setId("003");

        centerEntities.add(centerEntity);
        centerEntities.add(centerEntity2);
        centerEntities.add(centerEntity3);

        when(centerDao.getCentersByGroupIds(anyList())).thenReturn(centerEntities);
        when(centerDao.getCentersSizeByAgencyId(anyString())).thenReturn(3);

        // Mock userProvider
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("001");
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agencyModel);

        // 调用方法
        String result = noteServiceImpl.getBatchAddType("001,002,003", "001");

        // 验证结果
        assertEquals("All sites", result);
    }

    /**
     * 测试获取媒体信息 Site Admin 角色的多个学校
     */
    @Test
    public void testGetBatchAddTypeSiteAdmin() {
        // Mock userDao
        UserModel userModel = new UserModel();
        userModel.setRole(UserRole.SITE_ADMIN.toString());
        when(userDao.getUserById(anyString())).thenReturn(userModel);
        String userId = "001";
        // Mock centerDao
        List<CenterEntity> centerEntities = new ArrayList<>();
        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setId("001");

        CenterEntity centerEntity2 = new CenterEntity();
        centerEntity.setId("002");

        CenterEntity centerEntity3 = new CenterEntity();
        centerEntity.setId("003");

        centerEntities.add(centerEntity);
        centerEntities.add(centerEntity2);
        centerEntities.add(centerEntity3);

        when(centerDao.getCentersByGroupIds(anyList())).thenReturn(centerEntities);
        when(centerDao.getCentersSizeByAgencyId(anyString())).thenReturn(3);


        List<CenterModel> centerModels = new ArrayList<>();
        CenterModel centerModel1 = new CenterModel();
        centerModel1.setId("001");

        CenterModel centerModel2 = new CenterModel();
        centerModel2.setId("002");

        CenterModel centerModel3 = new CenterModel();
        centerModel3.setId("003");
        centerModels.add(centerModel1);
        centerModels.add(centerModel2);
        centerModels.add(centerModel3);

        when(centerDao.getCentersBySiteAdminId(userId)).thenReturn(centerModels);


        // Mock userProvider
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(userId);
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agencyModel);

        // 调用方法
        String result = noteServiceImpl.getBatchAddType("001,002,003", userId);

        // 验证结果
        assertEquals("All sites", result);
    }



    /**
     * 测试获取媒体信息 Site Admin 角色的多个学校
     */
    @Test
    public void testGetBatchAddTypeSiteAdminMultiSite() {
        // Mock userDao
        UserModel userModel = new UserModel();
        userModel.setRole(UserRole.SITE_ADMIN.toString());
        when(userDao.getUserById(anyString())).thenReturn(userModel);
        String userId = "001";
        // Mock centerDao
        List<CenterEntity> centerEntities = new ArrayList<>();
        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setId("001");

        CenterEntity centerEntity2 = new CenterEntity();
        centerEntity.setId("002");

        CenterEntity centerEntity3 = new CenterEntity();
        centerEntity.setId("003");

        centerEntities.add(centerEntity);
        centerEntities.add(centerEntity2);
        centerEntities.add(centerEntity3);

        when(centerDao.getCentersByGroupIds(anyList())).thenReturn(centerEntities);
        when(centerDao.getCentersSizeByAgencyId(anyString())).thenReturn(3);


        List<CenterModel> centerModels = new ArrayList<>();
        CenterModel centerModel1 = new CenterModel();
        centerModel1.setId("001");

        CenterModel centerModel2 = new CenterModel();
        centerModel2.setId("002");

        centerModels.add(centerModel1);
        centerModels.add(centerModel2);

        when(centerDao.getCentersBySiteAdminId(userId)).thenReturn(centerModels);


        // Mock userProvider
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(userId);
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agencyModel);

        // 调用方法
        String result = noteServiceImpl.getBatchAddType("001,002,003", userId);

        // 验证结果
        assertEquals("Multiple sites", result);
    }

    /**
     * 测试生成小孩家园互动 PDF
     * case: Drop Off Notes 数据为空
     */
    @Test
    public void testGenerateReportPdf() {
        // 数据准备
        // noteModel 列表
        List<NoteModel> noteModels = new ArrayList<>();
        NoteModel noteModel1 = new NoteModel();
        noteModel1.setApproveStatus(ApproveStatus.APPROVED.toString());
        noteModel1.setType("Activity");
        noteModels.add(noteModel1);
        NoteModel noteModel2 = new NoteModel();
        noteModel2.setApproveStatus(ApproveStatus.PENDING.toString());
        noteModels.add(noteModel2);
        noteModel1.setType("Potty");
        // 请求信息
        final String userId = "user01";
        final String fromDate = "2023-01-01";
        final String toDate = "2023-01-02";
        // user 实体
        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        // enrollment 实体
        EnrollmentEntity enrollmentEntity = new EnrollmentEntity();
        enrollmentEntity.setId("child01");
        // 方法模拟
        when(userProvider.getCurrentLang()).thenReturn("en-us");
        when(dropOffNoteService.getSubmittedFormDropOffNotes("child01", TimeUtil.format(fromDate, TimeUtil.dateFormat), TimeUtil.format(toDate, TimeUtil.dateFormat))).thenReturn(new ArrayList<>());
        when(enrollmentRepository.findById("child01")).thenReturn(Optional.ofNullable(enrollmentEntity));
        when(noteProvider.filterApproval(new ArrayList<>(), userEntity)).thenReturn(new ArrayList<>());
        when(noteProvider.getNotes("child01", new ArrayList<>(), null, new ArrayList<>(), new ArrayList<>(), false)).thenReturn(noteModels);
        when(eventDao.getEventByEnrollment("child01", fromDate, toDate)).thenReturn(new ArrayList<>());
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(regionService.isChina()).thenReturn(false);
        when(userProvider.checkUser(userId)).thenReturn(userEntity);
        when(studentDao.getMetas("child01", "External ID")).thenReturn(new ArrayList<>());
        when(fileSystem.getPublicUrl(anyString(), anyString())).thenReturn("publicUrl");
        ReflectionTestUtils.setField(noteServiceImpl, "pdfBucket",  "pdfBucket");
        ReflectionTestUtils.setField(noteServiceImpl, "endPoint",  "endPoint");
        ReflectionTestUtils.setField(noteServiceImpl, "pdfEndpoint",  "pdfEndpoint");

        // 调用方法
        noteServiceImpl.generateReportPdf("child01", fromDate, toDate, "");

        // 验证
        verify(reportDao, times(1)).createPdfConvertJob(any());
        verify(remoteProvider, times(1)).callPdfService(anyString(), anyList());
    }

    /**
     * 测试生成小孩家园互动 PDF
     * case: Drop Off Notes 数据不为空
     */
    @Test
    public void testGenerateReportPdf2() {
        // 数据准备
        // noteModel 列表
        List<NoteModel> noteModels = new ArrayList<>();
        NoteModel noteModel1 = new NoteModel();
        noteModel1.setApproveStatus(ApproveStatus.APPROVED.toString());
        noteModel1.setType("Activity");
        noteModels.add(noteModel1);
        NoteModel noteModel2 = new NoteModel();
        noteModel2.setApproveStatus(ApproveStatus.PENDING.toString());
        noteModels.add(noteModel2);
        noteModel1.setType("Potty");
        // 请求信息
        final String userId = "user01";
        final String fromDate = "2023-01-01";
        final String toDate = "2023-01-02";
        // user 实体
        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        // enrollment 实体
        EnrollmentEntity enrollmentEntity = new EnrollmentEntity();
        enrollmentEntity.setId("child01");
        List<NoteResponse> noteResponses = new ArrayList<>();
        NoteResponse noteResponse = new NoteResponse();
        noteResponse.setCreateAt("2023-01-03");
        noteResponse.setId("note001");
        noteResponse.setType("Activity");
        noteResponse.setQuestions(new ArrayList<>());
        noteResponses.add(noteResponse);
        // 小孩数据
        List<EnrollmentMetaDataEntity> metas = new ArrayList<>();
        EnrollmentMetaDataEntity enrollmentMetaDataEntity = new EnrollmentMetaDataEntity();
        enrollmentMetaDataEntity.setMetaValue("MetaValue001");
        metas.add(enrollmentMetaDataEntity);
        // 方法模拟
        when(userProvider.getCurrentLang()).thenReturn("en-us");
        lenient().when(dropOffNoteService.getSubmittedFormDropOffNotes("child01", TimeUtil.format(fromDate, TimeUtil.dateFormat), TimeUtil.format(toDate, TimeUtil.dateFormat))).thenReturn(noteResponses);
        when(enrollmentRepository.findById("child01")).thenReturn(Optional.ofNullable(enrollmentEntity));
        when(noteProvider.filterApproval(new ArrayList<>(), userEntity)).thenReturn(new ArrayList<>());
        when(noteProvider.getNotes("child01", new ArrayList<>(), null, new ArrayList<>(), new ArrayList<>(), false)).thenReturn(noteModels);
        when(eventDao.getEventByEnrollment("child01", fromDate, toDate)).thenReturn(new ArrayList<>());
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(regionService.isChina()).thenReturn(false);
        when(userProvider.checkUser(userId)).thenReturn(userEntity);
        when(studentDao.getMetas("child01", "External ID")).thenReturn(metas);
        when(dailyReportService.getDropOffNoteActions(any(), any(), anyList(), anyList())).thenReturn(new ArrayList<>());
        lenient().when(fileSystem.getPublicUrl("Activity")).thenReturn("publicUrl");
        ReflectionTestUtils.setField(noteServiceImpl, "pdfBucket",  "pdfBucket");
        ReflectionTestUtils.setField(noteServiceImpl, "endPoint",  "endPoint");
        ReflectionTestUtils.setField(noteServiceImpl, "pdfEndpoint",  "pdfEndpoint");
        lenient().when(fileSystem.getPublicUrl(Mockito.anyString(), Mockito.anyString())).thenReturn("htmlUrl");

        // 调用方法
        noteServiceImpl.generateReportPdf("child01", fromDate, toDate, "");

        // 验证
        verify(reportDao, times(1)).createPdfConvertJob(any());
        verify(remoteProvider, times(1)).callPdfService(anyString(), anyList());
    }

    /**
     * 测试转换 NoteResponse 列表
     */
    @Test
    void testConvertNoteResponseList() {
        // 模拟数据
        List<NoteResponse> noteResponses = new ArrayList<>();
        List<NoteEntity> noteEntityList = new ArrayList<>();
        NoteEntity noteEntity = new NoteEntity();
        noteEntity.setId("note001");
        noteEntity.setType("BOOK_VIDEO");
        noteEntityList.add(noteEntity);
        UserModel userModel = new UserModel();
        userModel.setId("user001");
        List<NoteDomainModel> noteDomainModels = new ArrayList<>();
        NoteDomainModel noteDomainModel = new NoteDomainModel();
        noteDomainModel.setId("domain001");
        noteDomainModel.setNoteId("note001");
        noteDomainModels.add(noteDomainModel);
        when(domainDao.getDomainByNoteIds(any())).thenReturn(noteDomainModels);
        List<UserEntity> userEntities = new ArrayList<>();
        UserEntity userEntity = new UserEntity();
        userEntity.setId("user001");
        userEntity.setLanguage("en-us");
        userEntities.add(userEntity);
        noteEntity.setUpdateByUser(userEntity);
        when(userDao.getUserLangByIds(any())).thenReturn(userEntities);
        List<NoteEnrollmentModel> noteEnrollmentModels = new ArrayList<>();
        NoteEnrollmentModel noteEnrollmentModel = new NoteEnrollmentModel();
        noteEnrollmentModel.setId("child001");
        noteEnrollmentModel.setNoteId("note001");
        noteEnrollmentModels.add(noteEnrollmentModel);
        when(studentDao.getEnrollmentByNoteIds(any())).thenReturn(noteEnrollmentModels);
        List<NoteTagModel> noteTagModels = new ArrayList<>();
        NoteTagModel noteTagModel = new NoteTagModel();
        noteTagModel.setId("tag001");
        noteTagModel.setNoteId("note001");
        noteTagModels.add(noteTagModel);
        when(tagDao.getTagByNoteIds(any())).thenReturn(noteTagModels);
        List<NoteMediaModel> noteMediaModels = new ArrayList<>();
        NoteMediaModel noteMediaModel = new NoteMediaModel();
        noteMediaModel.setId("media001");
        noteMediaModel.setNoteId("note001");
        noteMediaModels.add(noteMediaModel);
        NoteMediaModel noteMediaModel2 = new NoteMediaModel();
        noteMediaModel2.setId("media002");
        noteMediaModel2.setAnnexType("annex");
        noteMediaModel2.setNoteId("note001");
        noteMediaModels.add(noteMediaModel2);
        when(mediaDao.getMediaByNoteIds(any())).thenReturn(noteMediaModels);
        List<NoteLessonModel> noteLessonModels = new ArrayList<>();
        NoteLessonModel noteLessonModel = new NoteLessonModel();
        noteLessonModel.setId("lesson001");
        noteLessonModel.setNoteId("note001");
        noteLessonModels.add(noteLessonModel);
        when(lessonDao.getLessonByNoteIds(any())).thenReturn(noteLessonModels);
        List<NoteMetaDataModel> noteMetaDataModels = new ArrayList<>();
        NoteMetaDataModel noteMetaDataModel = new NoteMetaDataModel();
        noteMetaDataModel.setId("meta001");
        noteMetaDataModel.setNoteId("note001");
        noteMetaDataModels.add(noteMetaDataModel);
        when(noteDao.getNoteMetadataByNoteIds(any())).thenReturn(noteMetaDataModels);
        List<MediaShareRecordEntity> mediaShareRecordEntities = new ArrayList<>();
        MediaShareRecordEntity mediaShareRecordEntity = new MediaShareRecordEntity();
        mediaShareRecordEntity.setId("record001");
        mediaShareRecordEntity.setNoteId("note001");
        mediaShareRecordEntities.add(mediaShareRecordEntity);
        when(mediaBookDao.getInKindMediaShareRecords(any(), any())).thenReturn(mediaShareRecordEntities);
        when(noteProvider.formatLangCode(any())).thenReturn("en-US");
        MetaDataResponse metaDataResponse = new MetaDataResponse();
        metaDataResponse.setKey("bookvideo");
        when(noteProvider.encodeMeta(any(), any(), any(), any())).thenReturn(metaDataResponse);
        GoogleVideoModel googleVideoModel = new GoogleVideoModel();
        googleVideoModel.setId("video001");
        when(noteProvider.parseVideoInfo(any())).thenReturn(googleVideoModel);
        // 调用方法
        noteServiceImpl.convertNoteResponseList(noteEntityList, noteResponses, "domain001,domain002", userModel, "child001", true, "en-us");

        // 验证
        assertEquals(noteEntityList.size(), noteResponses.size());
        assertEquals(noteEntityList.get(0).getId(), noteResponses.get(0).getId());
    }

    /**
     * 测试获取小孩评分 Note
     * Case: 小孩框架为 IL CCSS-K,小孩未添加 Note 及评分，返回结果为空
     */
    @Test
    public void testGetDomainNotesByChildId() {
        // 准备数据
        final String userId = "user01"; // 模拟用户 ID
        UserEntity user = new UserEntity(); // 模拟用户实体
        user.setId(userId);
        user.setRole("AGENCY_ADMIN");
        final String childId = "child01"; // 模拟小孩 ID
        final String fromDate = "2023-01-01"; // 模拟开始时间'
        final String toDate = "2023-01-02"; // 模拟结束时间
        final String childFrameworkId = "E83EB44A-BD11-4003-B32A-79B17065A408"; // 模拟小孩框架 ID

        // 方法调用模拟
        when(userProvider.getCurrentUserId()).thenReturn(userId); // 模拟获取用户 ID
        when(userProvider.checkUser(userId)).thenReturn(user); // 模拟获取用户实体
        when(studentDao.getPeriodByTime(childId, fromDate, toDate)).thenReturn(null); // 模拟获取小孩周期
        when(studentDao.getChildFrameworkId(childId)).thenReturn(childFrameworkId); // 模拟获取小孩框架
        when(noteDao.getDomainNotesByChildId(childId, fromDate, toDate, true)).thenReturn(new ArrayList<>()); // 模拟获取小孩评分 Note

        // 调用方法
        ChildDomainEntity childDomain = noteServiceImpl.getDomainNotesByChildId(childId, fromDate, toDate);

        // 验证
        verify(userProvider, times(1)).getCurrentUserId(); // 验证是否调用了获取用户 ID 方法
        verify(userProvider, times(1)).checkUser(userId); // 验证是否调用了获取用户实体方法
        verify(studentDao, times(1)).getPeriodByTime(childId, fromDate, toDate); // 验证是否调用了获取小孩周期方法
        verify(studentDao, times(1)).getChildFrameworkId(childId); // 验证是否调用了获取小孩框架方法
        verify(noteDao, times(1)).getDomainNotesByChildId(childId, fromDate, toDate, true); // 验证是否调用了获取小孩评分 Note 方法
        assertEquals(childDomain.getDomains().size(), 0); // 验证返回结果为空
    }

    /**
     * 处理批量跟新 Note 任务测试方法
     * case: 页面编辑的时候，该任务正在执行中则抛出异常
     */
    @Test
    public void testBatchUpdateNoteThrowThrow() {
        // 模拟输入参数
        String noteId = "note001";
        UpdateNoteRequest noteRequest = new UpdateNoteRequest();
        noteRequest.setUserId("user01");
        noteRequest.setContentChange(true);
        noteRequest.setRelatedId("relatedId001");
        noteRequest.setTimeTask("");
        noteRequest.setClasses("class01,class02");
        noteRequest.setChildIds("child01,child02");

        // 模拟方法内部调用的返回值
        NoteEntity noteEntity = new NoteEntity();
        noteEntity.setIsDeleted(false);
        when(noteDao.getNote(noteId)).thenReturn(noteEntity);

        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("agency01");
        when(userProvider.getAgencyByUserId(noteRequest.getUserId())).thenReturn(agencyModel);

        when(agencyDao.isBatchAddOpen(agencyModel.getId(), "BOOKLEARNINGMEDIA_BATCHCREATE")).thenReturn(true);

        BatchAddNotes batchAddNotes = new BatchAddNotes();
        batchAddNotes.setStage("0");
        when(noteDao.getBatchAddNotesById(noteRequest.getRelatedId())).thenReturn(batchAddNotes);

        // 调用测试方法
        AtomicReference<String> result = new AtomicReference<>("");

        assertThrows(BusinessException.class, () -> {
            result.set(noteServiceImpl.batchUpdateNote(noteId, noteRequest));
        });

        // 验证方法的行为
        verify(noteDao, times(1)).getNote(noteId);
        verify(userProvider, times(1)).getAgencyByUserId(noteRequest.getUserId());
        verify(agencyDao, times(1)).isBatchAddOpen(agencyModel.getId(), "BOOKLEARNINGMEDIA_BATCHCREATE");
        verify(noteDao, times(1)).getBatchAddNotesById(noteRequest.getRelatedId());

        // 验证返回结果
        assertEquals("", result.get());
    }

    /**
     * 处理批量跟新 Note 任务测试方法
     * case: Note 内容改变并且有被移除的孩子
     */
    @Test
    public void testBatchUpdateNoteContentChangeHaveRemovedChild() {
        // 模拟输入参数
        String userId = "user01";
        String noteId = "note001";
        String relatedId = "relatedId001";
        UpdateNoteRequest noteRequest = new UpdateNoteRequest();
        noteRequest.setUserId(userId);
        noteRequest.setContentChange(true);
        noteRequest.setRelatedId(relatedId);
        noteRequest.setTimeTask("");
        noteRequest.setClasses("class01,class02");
        noteRequest.setChildIds("child01,child02,child05");
        noteRequest.setContentChange(true);

        // 模拟方法内部调用的返回值
        NoteEntity noteEntity = new NoteEntity();
        noteEntity.setIsDeleted(false);
        when(noteDao.getNote(noteId)).thenReturn(noteEntity);

        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("agency01");
        when(userProvider.getAgencyByUserId(noteRequest.getUserId())).thenReturn(agencyModel);

        when(agencyDao.isBatchAddOpen(agencyModel.getId(), "BOOKLEARNINGMEDIA_BATCHCREATE")).thenReturn(true);

        BatchAddNotes batchAddNotes = new BatchAddNotes();
        batchAddNotes.setId(relatedId);
        batchAddNotes.setStage("1");
        batchAddNotes.setChildId("child01,child02,child03,child04");
        batchAddNotes.setAllClassChildIds("child01,child02,child03,child04,child05");
        batchAddNotes.setClasses("class01,class02");
        when(noteDao.getBatchAddNotesById(noteRequest.getRelatedId())).thenReturn(batchAddNotes);

        UserModel userModel = new UserModel();
        userModel.setId(userId);
        when(userDao.getUserById(userId)).thenReturn(userModel);

        List<CenterEntity> selectedSchools = new ArrayList<>();
        when(centerDao.getCentersByGroupIds(anyList())).thenReturn(selectedSchools);

        List<EnrollmentModel> removedChildren = new ArrayList<>();
        EnrollmentModel enrollmentModel03 = new EnrollmentModel();
        enrollmentModel03.setId("child03");
        enrollmentModel03.setGroupId("class01");
        removedChildren.add(enrollmentModel03);
        EnrollmentModel enrollmentModel04 = new EnrollmentModel();
        enrollmentModel04.setId("child04");
        enrollmentModel04.setGroupId("class01");
        removedChildren.add(enrollmentModel04);
        when(studentDao.getUndeletedChildren(anyList())).thenReturn(removedChildren);

        doNothing().when(noteDao).updateBatchAddNotesStatus(any());
        doNothing().when(noteDao).updateNoteBatchAddType(anyString(), anyString());
        doNothing().when(noteDao).updateOneNoteAndBatchAddId(anyString(), anyString(), anyString(), anyList());

        // 调用测试方法
        String result = noteServiceImpl.batchUpdateNote(noteId, noteRequest);

        // 验证方法的行为
        verify(noteDao, times(1)).getNote(noteId);
        verify(userProvider, times(3)).getAgencyByUserId(noteRequest.getUserId());
        verify(agencyDao, times(1)).isBatchAddOpen(agencyModel.getId(), "BOOKLEARNINGMEDIA_BATCHCREATE");
        verify(noteDao, times(1)).getBatchAddNotesById(relatedId);
        verify(noteDao, times(1)).updateBatchAddNotesStatus(any());
        verify(noteDao, times(1)).updateNoteBatchAddType(anyString(), anyString());
        verify(noteDao, times(1)).updateOneNoteAndBatchAddId(anyString(), anyString(), anyString(), anyList());

        // 验证返回结果
        assertEquals("", result);
    }

    /**
     * 处理批量跟新 Note 任务测试方法
     * case: Note 没有内容改变并且有孩子的更新
     */
    @Test
    public void testBatchUpdateNoteNoContentChange() {
        // 模拟输入参数
        String userId = "user01";
        String noteId = "note001";
        String relatedId = "relatedId001";
        UpdateNoteRequest noteRequest = new UpdateNoteRequest();
        noteRequest.setUserId(userId);
        noteRequest.setContentChange(true);
        noteRequest.setRelatedId(relatedId);
        noteRequest.setTimeTask("");
        noteRequest.setClasses("class01,class02");
        noteRequest.setChildIds("child01,child02,child05");
        noteRequest.setContentChange(false);

        // 模拟方法内部调用的返回值
        NoteEntity noteEntity = new NoteEntity();
        noteEntity.setIsDeleted(false);
        when(noteDao.getNote(noteId)).thenReturn(noteEntity);

        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("agency01");
        when(userProvider.getAgencyByUserId(noteRequest.getUserId())).thenReturn(agencyModel);

        when(agencyDao.isBatchAddOpen(agencyModel.getId(), "BOOKLEARNINGMEDIA_BATCHCREATE")).thenReturn(true);

        BatchAddNotes batchAddNotes = new BatchAddNotes();
        batchAddNotes.setId(relatedId);
        batchAddNotes.setStage("1");
        batchAddNotes.setChildId("child01,child02,child03,child04");
        batchAddNotes.setAllClassChildIds("child01,child02,child03,child04,child05");
        batchAddNotes.setClasses("class01,class02");
        when(noteDao.getBatchAddNotesById(noteRequest.getRelatedId())).thenReturn(batchAddNotes);

        UserModel userModel = new UserModel();
        userModel.setId(userId);
        when(userDao.getUserById(userId)).thenReturn(userModel);

        List<CenterEntity> selectedSchools = new ArrayList<>();
        when(centerDao.getCentersByGroupIds(anyList())).thenReturn(selectedSchools);

        List<EnrollmentModel> removedChildren = new ArrayList<>();
        EnrollmentModel enrollmentModel03 = new EnrollmentModel();
        enrollmentModel03.setId("child03");
        enrollmentModel03.setGroupId("class01");
        removedChildren.add(enrollmentModel03);
        EnrollmentModel enrollmentModel04 = new EnrollmentModel();
        enrollmentModel04.setId("child04");
        enrollmentModel04.setGroupId("class01");
        removedChildren.add(enrollmentModel04);
        when(studentDao.getUndeletedChildren(anyList())).thenReturn(removedChildren);

        doNothing().when(noteDao).updateBatchAddNotesStatus(any());

        // 调用测试方法
        String result = noteServiceImpl.batchUpdateNote(noteId, noteRequest);

        // 验证方法的行为
        verify(noteDao, times(1)).getNote(noteId);
        verify(userProvider, times(2)).getAgencyByUserId(noteRequest.getUserId());
        verify(agencyDao, times(1)).isBatchAddOpen(agencyModel.getId(), "BOOKLEARNINGMEDIA_BATCHCREATE");
        verify(noteDao, times(1)).getBatchAddNotesById(relatedId);
        verify(noteDao, times(1)).updateBatchAddNotesStatus(any());

        // 验证返回结果
        assertEquals("", result);
    }

    /**
     * 测试生成学习故事 PDF
     */
    @Test
    public void testGenerateLearningStoryPDF() {
        // 模拟输入参数
        String userId = "user01";
        String noteId = "note001";
        String childId = "child01";
        List<NoteEntity> noteEntities = new ArrayList<>();
        NoteEntity noteEntity = new NoteEntity();
        noteEntity.setId(noteId);
        noteEntities.add(noteEntity);
        List<NoteModel> noteModels = new ArrayList<>();
        NoteModel noteModel = new NoteModel();
        noteModel.setId(noteId);
        noteModel.setCreateAtLocal("2020-01-01 00:00:00.000");
        List<ActionResponse> actionResponses = new ArrayList<>();
        ActionResponse actionResponse = new ActionResponse();
        actionResponse.setByUser("teacher01");
        actionResponses.add(actionResponse);
        noteModel.setActions(actionResponses);
        noteModels.add(noteModel);

        EnrollmentEntity enrollmentEntity = new EnrollmentEntity();
        enrollmentEntity.setId("001");
        GroupEntity group = new GroupEntity();
        group.setId("001");
        group.setName("group01");
        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setId("001");
        centerEntity.setName("center01");
        group.setCenter(centerEntity);
        enrollmentEntity.setGroup(group);

        ReflectionTestUtils.setField(noteServiceImpl, "s3Root", "https://s3.amazonaws.com/"); // 初始化 s3Root 值
        ReflectionTestUtils.setField(noteServiceImpl, "s3BucketName", "cn.learninggenie.prod"); // 初始化 s3BucketName 值
        ReflectionTestUtils.setField(noteServiceImpl, "pdfBucket", "cn.learninggenie.prod"); // 初始化 s3BucketName 值
        ReflectionTestUtils.setField(noteServiceImpl, "pdfEndpoint", "cn.learninggenie.prod"); // 初始化 s3BucketName 值
        when(fileSystem.getPublicUrl(anyString(), anyString())).thenReturn("https://s3.amazonaws.com/cn.learninggenie.prod/");
        // 模拟上传文件方法
        when(noteDao.getNote(noteId)).thenReturn(noteEntity);
        when(noteProvider.getNotes(childId, noteEntities, null, new ArrayList<>(), new ArrayList<>(), false)).thenReturn(noteModels);
        when(enrollmentRepository.findById(childId)).thenReturn(Optional.of(enrollmentEntity));
        noteServiceImpl.generateLearningStoryPDF(noteId, childId);
        verify(reportDao, times(1)).createPdfConvertJob(any());
    }

    /**
     * 测试生成学习故事
     */
    @Test
    public void testCreateNoteLearningStory() {
        // 模拟输入参数
        String noteId = "note001";
        CreateNoteRequest noteRequest = new CreateNoteRequest();
        List<LearningStoryModel> learningStoryModels = new ArrayList<>();
        LearningStoryModel learningStoryModel = new LearningStoryModel();
        learningStoryModel.setTitle("title01");
        learningStoryModel.setContent("content01");
        learningStoryModel.setSortIndex(0);
        learningStoryModels.add(learningStoryModel);
        noteRequest.setStoryContents(learningStoryModels);
        noteServiceImpl.createNoteLearningStory(noteRequest, noteId);
        verify(noteLearningStoryDao, times(1)).saveBatch(any());
    }

    /**
     * 测试检查学习故事变更
     */
    @Test
    public void testCheckLearningStoryChange() {
        // 准备数据
        String noteId = "note001";
        CreateNoteRequest noteRequest = new CreateNoteRequest();
        List<LearningStoryModel> learningStoryModels = new ArrayList<>();
        LearningStoryModel learningStoryModel = new LearningStoryModel();
        learningStoryModel.setTitle("title01");
        learningStoryModel.setContent("content01");
        learningStoryModel.setSortIndex(0);
        learningStoryModels.add(learningStoryModel);
        noteRequest.setStoryContents(learningStoryModels);

        NoteEntity noteEntity = new NoteEntity();
        noteEntity.setId(noteId);

        List<NoteLearningStoryEntity> noteLearningStoryEntities = new ArrayList<>();
        NoteLearningStoryEntity noteLearningStoryEntity = new NoteLearningStoryEntity();
        noteLearningStoryEntity.setNoteId(noteId);
        noteLearningStoryEntity.setSortIndex(0);
        noteLearningStoryEntities.add(noteLearningStoryEntity);

        when(noteLearningStoryDao.getNoteLearningStoryByNoteId(noteId)).thenReturn(new ArrayList<>());
        boolean isChanged01 = noteServiceImpl.checkLearningStoryChange(noteRequest, noteEntity);
        assertTrue(isChanged01);

        noteLearningStoryEntity.setTitle("");
        noteLearningStoryEntity.setContent("");
        when(noteLearningStoryDao.getNoteLearningStoryByNoteId(noteId)).thenReturn(noteLearningStoryEntities);
        boolean isChanged02 = noteServiceImpl.checkLearningStoryChange(noteRequest, noteEntity);
        assertTrue(isChanged02);

        noteLearningStoryEntity.setTitle("title01");
        when(noteLearningStoryDao.getNoteLearningStoryByNoteId(noteId)).thenReturn(noteLearningStoryEntities);
        boolean isChanged03 = noteServiceImpl.checkLearningStoryChange(noteRequest, noteEntity);
        assertTrue(isChanged03);

        noteLearningStoryEntity.setContent("content01");
        when(noteLearningStoryDao.getNoteLearningStoryByNoteId(noteId)).thenReturn(noteLearningStoryEntities);
        boolean isChanged04 = noteServiceImpl.checkLearningStoryChange(noteRequest, noteEntity);
        assertFalse(isChanged04);
    }

    /**
     * 测试更新学习故事
     */
    @Test
    public void testUpdateLearningStory() {
        // 准备数据
        String noteId01 = "note001";
        String noteId02 = "note002";
        UpdateNoteRequest noteRequest = new UpdateNoteRequest();
        List<LearningStoryModel> learningStoryModels = new ArrayList<>();
        LearningStoryModel learningStoryModel = new LearningStoryModel();
        learningStoryModel.setTitle("title01");
        learningStoryModel.setContent("content01");
        learningStoryModel.setSortIndex(0);
        learningStoryModels.add(learningStoryModel);
        noteRequest.setStoryContents(learningStoryModels);
        noteRequest.setNoteType("LEARNING_STORY");

        List<String> noteIds = new ArrayList<>();
        noteIds.add(noteId01);
        noteIds.add(noteId02);

        boolean learningStoryChange = true;
        noteServiceImpl.updateLearningStory(noteRequest, noteIds, learningStoryChange);
        verify(noteLearningStoryDao, times(1)).deleteNoteLearningStoryByNoteIds(noteIds);
        verify(noteLearningStoryDao, times(1)).saveBatch(any());
    }

    /**
     * 过滤 note 列表草稿数据
     */
    @Test
    void testProcessDraftData() {
        NoteModel noteModel1 = new NoteModel();
        noteModel1.setNoteType("LEARNING_STORY");
        noteModel1.setCreateUserId("USER001");
        NoteModel noteModel2 = new NoteModel();
        noteModel2.setNoteType("LEARNING_STORY_DRAFT");
        noteModel2.setCreateUserId("USER001");
        NoteModel noteModel3 = new NoteModel();
        noteModel3.setNoteType("LEARNING_STORY");
        noteModel3.setCreateUserId("USER002");
        NoteModel noteModel4 = new NoteModel();
        noteModel4.setNoteType("LEARNING_STORY_DRAFT");
        noteModel4.setCreateUserId("USER002");

        List<NoteModel> noteModels = new ArrayList<>();
        noteModels.add(noteModel1);
        noteModels.add(noteModel2);
        noteModels.add(noteModel3);
        noteModels.add(noteModel4);
        String currentUserId = "USER001";
        // 模拟获取当前用户 ID
        when(userProvider.getCurrentUserId()).thenReturn(currentUserId);
        List<NoteModel> noteModelList = noteServiceImpl.processDraftData(noteModels);
        assertEquals(3, noteModelList.size());
        assertEquals("LEARNING_STORY_DRAFT", noteModelList.get(0).getNoteType());
    }

    /**
     * 测试 learningMedia 视频结束后问卷弹出方法
     */
    @Test
    void testGetViewRemind() {
        // 初始化测试数据
        String enrollmentId = "EF22E586-F25A-4443-8B7A-DC5FDEF100DC";
        String noteId = "5A5ADF27-6C84-40F1-8088-5926E9F68316";
        String localTime = "2024-05-08 17:53:06.000";
        String timezone = "8";
        String currentUserId = "B8806885-8F15-4D93-9708-90B3E0DA369F";
        String shareId = "ABA5CFC7-1F74-4AF1-A616-003F66A58560";
        String channelId = "UCW4CF2v2UZyBNXAx7j2DKlQ";
        String videoId = "5C246BEE-53BC-41F1-B345-001E8B136A01";
        String formId = "C3AF2BC6-675E-4600-80CE-275F154D8A4E";
        int duration = 200;

        // 创建一个实体，用于模拟数据查询结果
        MediaShareRecordEntity mediaShareRecord = new MediaShareRecordEntity();
        mediaShareRecord.setId(shareId);
        // 模拟数据库查询，返回媒体分享记录
        when(mediaBookDao.getMediaShareRecord(enrollmentId, noteId)).thenReturn(mediaShareRecord);

        // 模拟获取当前用户 ID
        when(userProvider.getCurrentUserId()).thenReturn(currentUserId);

        // 创建一个观看记录列表，用于模拟数据库查询结果
        List<MediaWatchRecordEntity> mediaWatchRecords = new ArrayList<>();
        MediaWatchRecordEntity mediaWatchRecordEntity = new MediaWatchRecordEntity();
        mediaWatchRecordEntity.setId("B8806885-8F15-4D93-9708-90B3E0DA369F");
        mediaWatchRecordEntity.setShareId(shareId);
        mediaWatchRecordEntity.setCreateAtUtc(new Date());
        mediaWatchRecordEntity.setUpdateAtUtc(new Date());
        mediaWatchRecordEntity.setDuration(duration);
        mediaWatchRecords.add(mediaWatchRecordEntity);
        // 模拟数据库查询，返回观看记录列表
        when(mediaBookDao.getWatchRecords(currentUserId, mediaShareRecord.getId())).thenReturn(mediaWatchRecords);

        // 创建一个分享记录实体，用于模拟数据库查询结果
        MediaShareRecordEntity mediaShareRecordEntity = new MediaShareRecordEntity();
        mediaShareRecordEntity.setId(shareId);
        mediaShareRecordEntity.setVideoId(videoId);
        mediaShareRecordEntity.setNoteId(noteId);
        // 模拟数据库查询，返回媒体分享记录
        when(mediaBookDao.getShareRecord(shareId)).thenReturn(mediaShareRecordEntity);

        // 创建元数据列表，用于模拟数据库查询结果
        List<NoteMetaDataModel> noteMetadataList = new ArrayList<>();
        NoteMetaDataModel noteMetaDataModel = new NoteMetaDataModel();
        noteMetaDataModel.setValue("[{\n" +
                "  \"extEmbedCode\"&#58; \"<!DOCTYPE html><html style=\\\"height&#58;100%\\\"><head><script type=\\\"text/javascript\\\"src=\\\"https&#58;//www.youtube.com/player_api\\\"></script></head><body style=\\\"height&#58;100%\\\"><div id=\\\"ques\\\"style=\\\"position&#58;absolute;width&#58;100%;height&#58;100%;display&#58;none;background-color&#58;rgba(243&#44;243&#44;243&#44;.5);padding-top&#58;100px;padding-left&#58;50px;color&#58;#fff\\\"><form style=\\\"background-color&#58;#000;height&#58;100px;width&#58;200px;padding&#58;20px\\\">Like?<input type=\\\"radio\\\"name=\\\"group1\\\">Yes<input type=\\\"radio\\\"name=\\\"group1\\\">No Comments&#58;<input type=\\\"text\\\"><br><br><button type=\\\"submit\\\">Submit</button></form></div><!--placeholder--><div id=\\\"player\\\"></div></body></html><script>var videoId=\\\"5C246BEE-53BC-41F1-B345-001E8B136A01\\\";function onYouTubePlayerAPIReady(){var player; player=new YT.Player(\\\"player\\\"&#44;{height&#58;\\\"100%\\\"&#44;width&#58;\\\"100%\\\"&#44;videoId&#58;videoId&#44;events&#58;{onStateChange&#58;stateChange}})}function stateChange(){}/*placeholder*/</script>\"&#44;\n" +
                "  \"duration\"&#58; \"PT3M29S\"&#44;\n" +
                "  \"id\"&#58; {\n" +
                "    \"videoId\"&#58; \"5C246BEE-53BC-41F1-B345-001E8B136A01\"\n" +
                "  }&#44;\n" +
                "  \"etag\"&#58; \"xNXFEewEGImyhOjFVDcEIuWZiT0\"&#44;\n" +
                "  \"kind\"&#58; \"youtube#video\"&#44;\n" +
                "  \"channelId\"&#58; \"UCW4CF2v2UZyBNXAx7j2DKlQ\"&#44;\n" +
                "  \"snippet\"&#58; {\n" +
                "    \"PublishedAt\"&#58; \"2021-09-27T23&#58;45&#58;00.000Z\"&#44;\n" +
                "    \"title\"&#58; \"Know and Follow Rules By Cheri J. Meiners | Building Character Book For Kids\"&#44;\n" +
                "    \"description\"&#58; \"Know and Follow Rules read aloud for children&#44; written by Cheri J. Meiners and illustrated by Meredith Johnson. \\nThis book presents four basic rules to help kids understand the importance of rules and develop social skills such as empathy and respect.\"&#44;\n" +
                "    \"thumbnails\"&#58; {\n" +
                "      \"high\"&#58; {\n" +
                "        \"height\"&#58; 360&#44;\n" +
                "        \"url\"&#58; \"https&#58;//i.ytimg.com/vi/5C246BEE-53BC-41F1-B345-001E8B136A01/hqdefault.jpg\"&#44;\n" +
                "        \"width\"&#58; 480\n" +
                "      }&#44;\n" +
                "      \"maxres\"&#58; {\n" +
                "        \"height\"&#58; 720&#44;\n" +
                "        \"url\"&#58; \"https&#58;//i.ytimg.com/vi/5C246BEE-53BC-41F1-B345-001E8B136A01/maxresdefault.jpg\"&#44;\n" +
                "        \"width\"&#58; 1280\n" +
                "      }&#44;\n" +
                "      \"medium\"&#58; {\n" +
                "        \"height\"&#58; 180&#44;\n" +
                "        \"url\"&#58; \"https&#58;//i.ytimg.com/vi/5C246BEE-53BC-41F1-B345-001E8B136A01/mqdefault.jpg\"&#44;\n" +
                "        \"width\"&#58; 320\n" +
                "      }&#44;\n" +
                "      \"standard\"&#58; {\n" +
                "        \"height\"&#58; 480&#44;\n" +
                "        \"url\"&#58; \"https&#58;//i.ytimg.com/vi/5C246BEE-53BC-41F1-B345-001E8B136A01/sddefault.jpg\"&#44;\n" +
                "        \"width\"&#58; 640\n" +
                "      }&#44;\n" +
                "      \"default\"&#58; {\n" +
                "        \"height\"&#58; 90&#44;\n" +
                "        \"url\"&#58; \"https&#58;//i.ytimg.com/vi/5C246BEE-53BC-41F1-B345-001E8B136A01/default.jpg\"&#44;\n" +
                "        \"width\"&#58; 120\n" +
                "      }\n" +
                "    }&#44;\n" +
                "    \"publishedAt\"&#58; \"2021-09-27T23&#58;45&#58;00.000Z\"&#44;\n" +
                "    \"channelTitle\"&#58; \"Reading With KiKi\"&#44;\n" +
                "    \"liveBroadcastContent\"&#58; \"none\"\n" +
                "  }\n" +
                "}]");
        noteMetadataList.add(noteMetaDataModel);
        List<String> noteIdList = new ArrayList<>();
        noteIdList.add(noteId);
        // 模拟数据库查询，返回元数据列表
        when(noteDao.getMetadataByNoteIds(noteIdList)).thenReturn(noteMetadataList);

        // 创建一个机构实体，用于模拟数据库查询结果
        AgencyEntity agencyEntity = new AgencyEntity();
        agencyEntity.setId("32DA267F-9012-40AE-9079-002E40CB29EF");
        // 模拟数据库查询，返回机构实体
        when(agencyDao.getAgencyByParentChild(currentUserId, enrollmentId)).thenReturn(agencyEntity);

        // 模拟检查机构 learningMedia 是否开启
        when(userProvider.getAgencyOpenDefaultOpen(agencyEntity.getId(), AgencyMetaKey.CHILD_LEARNING_OPEN.toString())).thenReturn(true);

        // 创建一个媒体表单实体，用于模拟数据库查询结果
        MediaFormEntity mediaFormEntity = new MediaFormEntity();
        mediaFormEntity.setFormId(formId);
        // 模拟数据库查询，返回媒体表单实体
        when(formsFormDao.getMediaFormByVideo(channelId, videoId, agencyEntity.getId())).thenReturn(mediaFormEntity);
        // 创建一个表单实体，用于模拟数据库查询结果
        FormsFormEntity formsFormEntity = new FormsFormEntity();

        String formName = "test";
        String formDescription = "data for test";
        formsFormEntity.setName(formName);
        formsFormEntity.setDescription(formDescription);
        // 模拟数据库查询，返回表单实体
        when(formsFormDao.getById(formId)).thenReturn(formsFormEntity);
        // 创建一个选项实体列表，用于模拟数据库查询结果
        List<FormsOptionEntity> optionEntityList = new ArrayList<>();
        FormsOptionEntity formsOptionEntity = new FormsOptionEntity();
        String questionId = "7597A607-470D-EF11-ACAD-02037858CE37";
        formsOptionEntity.setQuestionId(questionId);
        optionEntityList.add(formsOptionEntity);
        // 模拟数据库查询，返回选项实体列表
        when(optionDao.getListByFormId(formId)).thenReturn(optionEntityList);
        // 创建一个问题实体列表，用于模拟数据库查询结果
        List<FormsQuestionEntity> questionEntityList = new ArrayList<>();
        FormsQuestionEntity formsQuestionEntity = new FormsQuestionEntity();
        formsQuestionEntity.setId(questionId);
        questionEntityList.add(formsQuestionEntity);
        // 模拟数据库查询，返回问题实体列表
        when(questionEntityDao.getListByFormsId(formId)).thenReturn(questionEntityList);

        // 执行测试方法
        ViewResponse result = noteServiceImpl.getViewRemind(enrollmentId, noteId, localTime, timezone);
        // 验证结果
        assertNotNull(result);
        assertEquals(shareId, result.getShareId());
        assertEquals(formId, result.getFormInfo().getId());
        assertEquals(questionId, result.getFormInfo().getQuestionList().get(0).getId());
        assertEquals(formDescription, result.getFormInfo().getDiscription());
        assertEquals(formName, result.getFormInfo().getFormName());
        assertEquals(questionId, result.getFormInfo().getQuestionList().get(0).getId());
    }
}


package com.learninggenie.api.service.impl;

import com.google.common.collect.Lists;
import com.google.protobuf.InvalidProtocolBufferException;
import com.learninggenie.common.data.dao.*;
import com.learninggenie.common.data.dao.enrollment.EnrollmentSnapshotDao;
import com.learninggenie.common.data.dao.impl.CenterMapper;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.entity.*;
import com.learninggenie.common.data.entity.UserEntity;
import com.learninggenie.common.data.enums.CenterMetaKey;
import com.learninggenie.common.data.enums.GroupMetaKey;
import com.learninggenie.common.data.enums.SnapshotStatus;
import com.learninggenie.common.data.model.*;
import com.learninggenie.common.data.model.CenterEntity;
import com.learninggenie.common.data.model.DomainEntity;
import com.learninggenie.common.data.model.GroupEntity;
import com.learninggenie.common.data.model.drdp.setting.CenterSetting;
import com.learninggenie.common.data.model.drdp.setting.CenterSettingData;
import com.learninggenie.common.data.model.drdp.setting.DRDPSetting;
import com.learninggenie.common.data.model.drdp2.DrdpDemographics;
import com.learninggenie.common.data.model.drdp2.DrdpStudentScoreModel;
import com.learninggenie.common.data.model.drdp2.GetDrdpBatchScoringResultResponse;
import com.learninggenie.common.data.model.record.UploadDRDPRecord;
import com.learninggenie.common.data.model.report.ActionPlanDetailsEntity;
import com.learninggenie.common.data.model.report.ActionPlanEntity;
import com.learninggenie.common.filesystem.FileSystem;
import com.learninggenie.common.report.AnalysisServiceImpl;
import com.learninggenie.common.report.CheckLockResult;
import com.learninggenie.common.report.LGSnapshot;
import com.learninggenie.common.score.DomainLevelResult;
import com.learninggenie.common.score.DomainScoreService;
import com.learninggenie.common.score.PortfolioScoreViewModel;
import com.learninggenie.common.score.RatingService;
import com.learninggenie.common.sharding.ShardingProvider;
import com.learninggenie.common.sync.DrdpV2Service;
import com.learninggenie.common.utils.JsonUtil;
import com.learninggenie.common.utils.TimeUtil;
import com.timgroup.statsd.StatsDClient;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.BeanUtils;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Created by hxl on 2023/05/18.
 * AnalysisServiceImpl 的测试类
 */

@RunWith(MockitoJUnitRunner.class)
public class AnalysisServiceImplTest {

    public static final String AGENCY_ID = "A001";

    public static final String PERIOD_ALIAS = "2022-2023 Spring";

    public static final String FROM_LOCAL = "2023-01-01 00:00:00";

    public static final String STRING = "2023-05-01 00:00:00";

    public static final String ENROLLMENT_ID = "E001";

    public static final String STUDENT_SNAPSHOT_SUCCEED = "student_snapshot_succeed";

    public static final String STUDENT_SNAPSHOT_DURATION = "student_snapshot_duration";

    @InjectMocks
    private AnalysisServiceImpl analysisService;

    @Mock
    private StudentDao studentDao;

    @Mock
    private AgencyDao agencyDao;

    @Mock
    private CenterDao centerDao;

    @Mock
    private StatsDClient statsDClient;

    @Mock
    private ShardingProvider shardingProvider;

    @Mock
    private DomainDao domainDao;

    @Mock
    private RatingService ratingService;

    @Mock
    private GroupDao groupDao;

    @Mock
    private CenterMapper centerMapper;

    @Mock
    private FileSystem fileSystem;

    @Mock
    private DomainScoreService domainScoreService;

    @Mock
    private UserDaoImpl userDao;

    @Mock
    private ReportDao reportDao;

    @Mock
    private RecordDao recordDao;

    @Mock
    private DrdpV2Service drdpV2Service;

    @Mock
    private JobDao jobDao;

    @Mock
    private EnrollmentSnapshotDao enrollmentSnapshotDao;


    /**
     * Created by hxl on 2023/05/18.
     * 测试 getProperties 方法
     */
    @Test
    public void testGetProperties() {

        when(agencyDao.isDRDPtech(anyString())).thenReturn(false);

        // Mock数据
        final String childId = "123";
        final String agencyId = "456";
        final List<StudentAttrEntity> attrs = new ArrayList<>();
        final StudentAttrEntity attr1 = new StudentAttrEntity();
        final String attr11 = "attr1";
        final String type1 = "type1";
        final String value1 = "value1";
        final String attr21 = "attr2";
        final String type2 = "type2";
        final String value2 = "value2";

        attr1.setAttrName(attr11);
        attr1.setAttrTypeValue(type1);
        attr1.setAttrValue(value1);
        final StudentAttrEntity attr2 = new StudentAttrEntity();
        attr2.setAttrName(attr21);
        attr2.setAttrTypeValue(type2);
        attr2.setAttrValue(value2);

        attrs.add(attr1);
        attrs.add(attr2);
        when(studentDao.getAttrsByChildIds(Mockito.anyList())).thenReturn(attrs);

        // 调用测试方法
        final List<LGSnapshot.Properties> properties = analysisService.getProperties(childId, agencyId);

        // 验证结果
        Assertions.assertEquals(2, properties.size());
        final LGSnapshot.Properties property1 = properties.get(0);
        Assertions.assertEquals(attr21, property1.getName());
        Assertions.assertEquals(value2, property1.getValue());
        Assertions.assertEquals(type2, property1.getType());
        final LGSnapshot.Properties property2 = properties.get(1);
        Assertions.assertEquals(attr11, property2.getName());
        Assertions.assertEquals(value1, property2.getValue());
        Assertions.assertEquals(type1, property2.getType());
    }

    /**
     * Created by hxl on 2023/06/09.
     * 测试 generateSnapshotsV2 方法
     */
    @Test
    public void testGenerateSnapshotsV2() throws Exception {
        // Mock 数据
        final List<StudentSnapshotEntity> studentSnapshotEntities = new ArrayList<>();
        final StudentSnapshotEntity entity1 = new StudentSnapshotEntity();
        final String id = UUID.randomUUID().toString();
        entity1.setId(id);
        entity1.setAgencyId(AGENCY_ID);
        entity1.setPeriodAlias(PERIOD_ALIAS);
        entity1.setFromAtLocal(TimeUtil.parse(FROM_LOCAL, TimeUtil.dateFormat));
        entity1.setToAtLocal(TimeUtil.parse(STRING, TimeUtil.dateFormat));
        final String groupId = UUID.randomUUID().toString();
        entity1.setGroupId(groupId);
        final String frameworkId = UUID.randomUUID().toString();
        entity1.setFrameworkId(frameworkId);
        entity1.setEnrollmentId(ENROLLMENT_ID);
        entity1.setStatus(SnapshotStatus.PENDING.toString());
        studentSnapshotEntities.add(entity1);

        // 设置Mock方法的返回值
        when(studentDao.getSnapshot(anyString())).thenReturn(entity1);
        // 设置 studentDao.getEnrollmentWithDelete(enrollmentId) 的返回值
        final EnrollmentModel enrollment1 = new EnrollmentModel();
        enrollment1.setId(ENROLLMENT_ID);
        final String john = "John";
        enrollment1.setFirstName(john);
        final String doe = "Doe";
        enrollment1.setLastName(doe);
        enrollment1.setGender("Male");
        final String date = "2022-01-01";
        enrollment1.setEnrollmentDate(date);
        final String withdrawnDate = "2022-12-31";
        enrollment1.setWithdrawnDate(withdrawnDate);
        enrollment1.setPrivatePhoto(false);
        final String avatarUrl = "https://www.learninggenie.com/avatars/123.png";
        enrollment1.setAvatarUrl(avatarUrl);

        when(studentDao.getEnrollmentWithDelete(Mockito.eq(ENROLLMENT_ID))).thenReturn(enrollment1);

        final String frameworkName = "DRDP2015-PRESCHOOL";
        final DomainEntity domainEntity1 = new DomainEntity(frameworkId, frameworkName, "PS", "001", "/icons/math.png", null, 1);
        domainEntity1.setHasScoreTemplate(true);
        domainEntity1.setType("subject");
        domainEntity1.setMultiType(false);
        domainEntity1.setLinkUrl("/subjects/math");
        domainEntity1.setDescription("Mathematics is the study of numbers, quantities, and shapes.");

        // 设置 domainDao.getDomain(studentSnapshotEntity.getFrameworkId()) 的返回值
        when(domainDao.getDomain(Mockito.eq(frameworkId))).thenReturn(domainEntity1);

        // 设置 ratingService.getScoreResults(childId, domainEntity.getId(), fromDate, toDate) 的返回值
        // 创建 LevelEntity 对象
        final LevelEntity level1 = new LevelEntity();
        final String number = "1";
        level1.setId(number);
        final String levelName = "Level 1";
        level1.setName(levelName);
        final String mastery = "Mastery";
        level1.setType(mastery);
        level1.setSortIndex(number);
        final String value = "50";
        level1.setValue(value);
        level1.setFinalValue(value);
        level1.setFinalName(mastery);
        level1.setTip("You have mastered this skill!");
        level1.setHidden(false);
        level1.setRated(true);
        level1.setLevelIndex(1);

        final LevelEntity level2 = new LevelEntity();
        final String levelId2 = "2";
        level2.setId(levelId2);
        final String levelName2 = "Level 2";
        level2.setName(levelName2);
        final String developing = "Developing";
        level2.setType(developing);
        level2.setSortIndex(levelId2);
        final String value1 = "25";
        level2.setValue(value1);
        level2.setFinalValue(value1);
        level2.setFinalName(developing);
        level2.setTip("You are developing this skill!");
        level2.setHidden(false);
        level2.setRated(true);
        level2.setLevelIndex(2);

        // 创建 DomainLevelResult 对象
        final DomainLevelResult result1 = new DomainLevelResult();
        result1.setDomainId(number);
        result1.setMeasureName("Mathematics");
        result1.setLevelId(number);
        result1.setMeasure("COG1");
        result1.setLevelName(levelName);

        final DomainLevelResult result2 = new DomainLevelResult();
        result2.setDomainId(levelId2);
        result2.setMeasureName("English Language Arts");
        result2.setMeasure("ATL-REG1");
        result2.setLevelId(levelId2);
        result2.setLevelName(levelName2);

        // 创建 PortfolioScoreViewModel 对象
        final PortfolioScoreViewModel portfolioScore = new PortfolioScoreViewModel();
        portfolioScore.setLevels(Arrays.asList(level1, level2));
        portfolioScore.setScoreResults(Arrays.asList(result1, result2));
        portfolioScore.setFinalRateAtUtc(new Date());
        when(ratingService.getScoreResults(Mockito.eq(ENROLLMENT_ID), Mockito.eq(frameworkId), anyString(), anyString())).thenReturn(portfolioScore);
        // 设置 groupDao.getGroupWithCenter(groupId) 的返回值
        // 创建 MediaEntity 对象
        final MediaEntity media = new MediaEntity();
        media.setId(number);
        final String relativePath = "/icons/group.png";
        media.setRelativePath(relativePath);
        media.setCreateAtUtc(new Date());
        final String snapshotPath = "/icons/group_snapshot.png";
        media.setSnapshotPath(snapshotPath);
        final String contentType = "image/png";
        media.setMimeType(contentType);
        media.setWidth(100);
        media.setHeight(100);
        media.setSize(1024L);
        media.setFileType("png");
        final String processedImgPath = "/icons/processed_group.png";
        media.setProcessedImgPath(processedImgPath);
        final String smallImgPath = "/icons/small_group.png";
        media.setSmallImgPath(smallImgPath);
        final String mediumImgPath = "/icons/medium_group.png";
        media.setMediumImgPath(mediumImgPath);
        final String image = "group.png";
        media.setFileName(image);
        media.setAnnexType("image");
        media.setHaveSmall(true);
        media.setHaveMedium(true);
        media.setVoiceTime("0:00");
        media.setDynamicData(null);
        media.setPrivateFile(false);
        media.setWeb(false);
        media.setCompressed(false);

        // 创建 GroupEntity 对象
        final GroupEntity group = new GroupEntity();
        group.setId(groupId);
        group.setName("Math Class");
        group.setMedia(media);
        group.setCreatedUtc(date);
        group.setIconPath(relativePath);

        final CenterEntity center = new CenterEntity();
        final String centerId = number;
        center.setId(centerId);
        final String centerName = "Center 1";
        center.setName(centerName);

        group.setCenter(center);
        group.setDomain(null);
        group.setClassDomain(null);
        group.setProgramDomain(null);
        group.setCreateAtUtc(new Date());
        group.setUpdateAtUtc(new Date());
        group.setStage(null);
        group.setTeachers(null);
        final List<ChildEntity> childEntities = new ArrayList<>();
        final ChildEntity childEntity = new ChildEntity();
        childEntity.setId(ENROLLMENT_ID);
        childEntity.setFirstName(john);
        childEntity.setLastName(doe);
        childEntities.add(childEntity);
        group.setChilds(childEntities);
        group.setIsDeleted(false);
        group.setInactive(false);
        group.setTraining(false);
        group.setChildCount(20);
        group.setGroupsMetaDataEntities(null);
        group.setGroupInvitationEntities(null);
        final Set<EnrollmentEntity> enrollmentEntities = new HashSet<>();
        final EnrollmentEntity enrollmentEntity = new EnrollmentEntity();
        BeanUtils.copyProperties(enrollment1, enrollmentEntity);
        enrollmentEntities.add(enrollmentEntity);
        group.setEnrollments(enrollmentEntities);
        group.setFrameworkId(frameworkId);
        group.setFrameworkName(frameworkName);
        group.setCenterName("ABC Learning Center");
        group.setPeriodGroupId(number);
        group.setCenterId(number);

        when(groupDao.getGroupWithCenter(Mockito.eq(groupId))).thenReturn(group);

        // 设置 centerMapper.getAgencyByCenterId(centerId) 的返回值
        final List<AgencyModel> agencyModels = new ArrayList<>();
        // 创建第一个 AgencyModel 实例并设置属性
        final AgencyModel agency1 = new AgencyModel();
        agency1.setId(number);
        agency1.setName("Agency Name 1");
        // 设置其他属性...

        // 创建第二个 AgencyModel 实例并设置属性
        final AgencyModel agency2 = new AgencyModel();
        agency2.setId(levelId2);
        agency2.setName("Agency Name 2");
        // 设置其他属性...

        // 将 AgencyModel 实例添加到列表中
        agencyModels.add(agency1);
        agencyModels.add(agency2);

        final List<UserEntity> groupTeachers = Lists.newArrayList();
        UserEntity teacher1 = new UserEntity();
        teacher1.setId("1");
        groupTeachers.add(teacher1);
        UserEntity teacher2 = new UserEntity();
        teacher1.setId("2");
        groupTeachers.add(teacher2);

        when(centerMapper.getAgencyByCenterId(Mockito.eq(centerId))).thenReturn(agencyModels);
        // 设置 fileSystem.getPublicUrl(avatarUrl) 的返回值
        when(fileSystem.getPublicUrl(Mockito.eq(avatarUrl))).thenReturn(avatarUrl);
        when(userDao.getTeacherByGroupId(groupId)).thenReturn(groupTeachers);
        // 执行测试方法
        analysisService.generateSnapshots_v2(studentSnapshotEntities);

        // 验证Mock方法是否被调用
        Mockito.verify(studentDao, Mockito.times(2)).updateSnapshot(entity1);
        Mockito.verify(statsDClient, Mockito.times(1)).increment(STUDENT_SNAPSHOT_SUCCEED);
        Mockito.verify(statsDClient, Mockito.times(2)).histogram(anyString(), Mockito.anyLong());
    }

    /**
     * Created by hxl on 2023/06/09.
     * 测试 generateSnapshotsV2 方法
     */
    @Test
    public void testGenerateSnapshotsV2WithNullEntity() throws Exception {
        // Mock 数据
        final List<StudentSnapshotEntity> studentSnapshotEntities = new ArrayList<>();
        final StudentSnapshotEntity entity1 = new StudentSnapshotEntity();
        final String id = UUID.randomUUID().toString();
        entity1.setId(id);
        entity1.setAgencyId(AGENCY_ID);
        entity1.setPeriodAlias("2022-2023 Summer");
        entity1.setEnrollmentId(ENROLLMENT_ID);
        final String groupId = UUID.randomUUID().toString();
        entity1.setGroupId(groupId);
        entity1.setStatus(SnapshotStatus.PENDING.toString());
        studentSnapshotEntities.add(entity1);

        // 设置Mock方法的返回值
        when(studentDao.getSnapshot(anyString())).thenReturn(null);

        // 执行测试方法
        analysisService.generateSnapshots_v2(studentSnapshotEntities);

        // 验证Mock方法是否被调用
        Mockito.verify(studentDao, Mockito.never()).updateSnapshot(any(StudentSnapshotEntity.class));
        Mockito.verify(statsDClient, Mockito.never()).increment(STUDENT_SNAPSHOT_SUCCEED);
        Mockito.verify(statsDClient, Mockito.never()).histogram(Mockito.eq(STUDENT_SNAPSHOT_DURATION), Mockito.anyLong());
    }

    /**
     * Created by hxl on 2023/06/09.
     * 测试 generateSnapshotsV2 方法
     */
    @Test
    @Ignore
    public void generateSnapshotsV2ShouldThrowExceptionWhenGetSnapshotFails() {
        // Arrange
        // Mock 数据
        final List<StudentSnapshotEntity> studentSnapshotEntities = new ArrayList<>();
        final StudentSnapshotEntity entity1 = new StudentSnapshotEntity();
        final String id = UUID.randomUUID().toString();
        entity1.setId(id);
        entity1.setAgencyId(AGENCY_ID);
        entity1.setPeriodAlias(PERIOD_ALIAS);
        entity1.setFromAtLocal(TimeUtil.parse(FROM_LOCAL, TimeUtil.dateFormat));
        entity1.setToAtLocal(TimeUtil.parse(STRING, TimeUtil.dateFormat));
        final String groupId = UUID.randomUUID().toString();
        entity1.setGroupId(groupId);
        final String frameworkId = UUID.randomUUID().toString();
        entity1.setFrameworkId(frameworkId);
        entity1.setEnrollmentId(ENROLLMENT_ID);
        entity1.setStatus(SnapshotStatus.PENDING.toString());
        studentSnapshotEntities.add(entity1);

        // 设置Mock方法的返回值
        when(studentDao.getSnapshot(anyString())).thenReturn(entity1);
        analysisService.generateSnapshots_v2(studentSnapshotEntities);
        // Act & Assert
        Mockito.verify(studentDao, Mockito.times(2)).updateSnapshot(studentSnapshotEntities.get(0));
        Mockito.verify(statsDClient, Mockito.times(1)).increment(Mockito.eq("student_snapshot_failed"), anyString());
        Mockito.verify(statsDClient, Mockito.never()).histogram(Mockito.eq(STUDENT_SNAPSHOT_DURATION), Mockito.anyLong());
    }

    @Test
    public void testCheckMeasures_allMeasuresHaveScores_success() {
        // 准备测试数据
        List<String> measureIdList = Arrays.asList("b", "a", "r");

        // 创建Mock对象
        List<StudentScoreModel> studentScoreModels = new ArrayList<>();
        StudentScoreModel studentScoreModel = new StudentScoreModel();
        studentScoreModel.setDomainId("r");
        StudentScoreModel studentScoreModel2 = new StudentScoreModel();
        studentScoreModel2.setDomainId("a");
        StudentScoreModel studentScoreModel3 = new StudentScoreModel();
        studentScoreModel3.setDomainId("b");
        studentScoreModels.add(studentScoreModel);
        studentScoreModels.add(studentScoreModel2);
        studentScoreModels.add(studentScoreModel3);

        // 调用被测试方法
        CheckLockResult result = analysisService.checkMeasures(studentScoreModels, measureIdList);

        // 验证结果
        assertTrue(result.isSuccess());
    }

    @Test
    public void testGetMissBaseInfo() {
        // Arrange
        EnrollmentEntity enrollmentEntity = new EnrollmentEntity();
        enrollmentEntity.setFirstName("");
        enrollmentEntity.setLastName("");
        enrollmentEntity.setGender("Unknown");

        // Act
        List<String> result = analysisService.getMissBaseInfo(enrollmentEntity);

        // Assert
        assertEquals(5, result.size());
        assertTrue(result.contains("First Name"));
        assertTrue(result.contains("Last Name"));
        assertTrue(result.contains("Date of Birth"));
        assertTrue(result.contains("Gender"));
        assertTrue(result.contains("Entry Date"));
    }

    @Test
    public void testUpdateSnapshotUploadStatus() {
        // Arrange
        UploadDRDPRecord record = new UploadDRDPRecord();
        record.setId("recordId001");
        record.setAgencyId("agencyId001");
        String uploadId = "1";
        Date utcNow = new Date();

        GetDrdpBatchScoringResultResponse result = new GetDrdpBatchScoringResultResponse();
        result.setStatus(200);
        List<DrdpStudentScoreModel> studentScores = new ArrayList<>();
        DrdpStudentScoreModel studentScore = new DrdpStudentScoreModel();
        studentScore.setFirstName("John");
        studentScore.setLastName("Doe");
        studentScore.setChildId("studentId001");
        studentScore.setDob("2018-01-01");
        studentScore.setGender("F");
        studentScore.setEnrollment("2023-01-01");
        DrdpDemographics drdpDemographics = new DrdpDemographics();
        drdpDemographics.setAgency("agency001");
        drdpDemographics.setSite("center001");
        drdpDemographics.setClassName("group001");
        studentScore.setDemographics(drdpDemographics);
        studentScores.add(studentScore);
        result.setStudentScores(studentScores);

        when(drdpV2Service.getBatchScoringResult(uploadId)).thenReturn(result);

        List<StudentSnapshotEntity> snapshots = new ArrayList<>();
        StudentSnapshotEntity snapshot = new StudentSnapshotEntity();
        snapshot.setId("snapshotId001");
        snapshot.setAgencyId(record.getAgencyId());
        snapshot.setUploadId(uploadId);
        snapshot.setGroupId("groupId001");
        snapshot.setEnrollmentId("studentId001");
        LGSnapshot.StudentSnapshot studentSnapshot = LGSnapshot.StudentSnapshot.newBuilder()
                .setFirstName("John")
                .setLastName("Doe")
                .setEnrollmentDate(TimeUtil.parse("2023-01-01", TimeUtil.format10).getTime())
                .setBirthday(TimeUtil.parse("2018-01-01", TimeUtil.format10).getTime())
                .setGender(LGSnapshot.StudentSnapshot.GenderType.FEMALE)
                .setId("studentId001")
                .setGroup(LGSnapshot.ClassSnapshot.newBuilder().setId("groupId001")
                        .setName("group001")
                        .setCreatedAt(TimeUtil.parse("2023-01-01", TimeUtil.format10).getTime())
                        .setUpdatedAt(TimeUtil.parse("2023-01-01", TimeUtil.format10).getTime())
                        .setCenter(LGSnapshot.CenterSnapshot.newBuilder().setId("centerId001")
                                .setName("center001")
                                .setCreatedAt(TimeUtil.parse("2023-01-01", TimeUtil.format10).getTime())
                                .setUpdatedAt(TimeUtil.parse("2023-01-01", TimeUtil.format10).getTime())
                                .setAgency(LGSnapshot.AgencySnapshot.newBuilder().setId("agencyId001")
                                        .setName("agency001")
                                        .setCreatedAt(TimeUtil.parse("2023-01-01", TimeUtil.format10).getTime())
                                        .setUpdatedAt(TimeUtil.parse("2023-01-01", TimeUtil.format10).getTime())
                                        .build())
                                .build())
                        .build())
                .build();
        snapshot.setData(studentSnapshot.toByteArray());

        snapshots.add(snapshot);
        when(studentDao.getSnapshotsByUploadId(uploadId)).thenReturn(snapshots);

        EnrollmentEntity enrollmentEntity = new EnrollmentEntity();
        enrollmentEntity.setFirstName("John");
        enrollmentEntity.setLastName("Doe");
        enrollmentEntity.setBirthDate(TimeUtil.parse("2018-01-01", TimeUtil.format10));
        enrollmentEntity.setEnrollmentDate(TimeUtil.parse("2023-01-01", TimeUtil.format10));
        enrollmentEntity.setGender("FEMALE");
        when(studentDao.getById("studentId001")).thenReturn(enrollmentEntity);

        com.learninggenie.common.data.entity.CenterEntity centerEntity = new com.learninggenie.common.data.entity.CenterEntity();
        centerEntity.setId("centerId001");
        centerEntity.setName("center001");
        when(centerDao.getCenter("centerId001")).thenReturn(centerEntity);

        CenterMetaDataEntity centerMetaData = new CenterMetaDataEntity();
        centerMetaData.setMetaKey(CenterMetaKey.DRDP_CENTER_ALIAS.toString() + "_DRDPSETTINGID001");
        centerMetaData.setMetaValue("center001");
        when(centerDao.getMeta("centerId001", CenterMetaKey.DRDP_CENTER_ALIAS.toString() + "_DRDPSETTINGID001")).thenReturn(centerMetaData);


        GroupEntry group = new GroupEntry();
        group.setId("groupId001");
        group.setName("group001");
        when(groupDao.getGroup("groupId001")).thenReturn(group);


        GroupMetaDataEntity replaceGroupMeta = new GroupMetaDataEntity();
        replaceGroupMeta.setMetaKey(GroupMetaKey.DRDP_REPLACED_GROUP_NAME.toString());
        replaceGroupMeta.setMetaValue("{\n" +
                "  \"original\": \"group001\",\n" +
                "  \"specialCharacters\": [\n" +
                "    \"group001\"\n" +
                "  ],\n" +
                "  \"replaceCharacter\": \"group001\",\n" +
                "  \"replaced\": \"group001\"\n" +
                "}");
        when(groupDao.getMeta(group.getId(), GroupMetaKey.DRDP_REPLACED_GROUP_NAME.toString())).thenReturn(replaceGroupMeta);

        List<DRDPSetting> drdpSettings = new ArrayList<>();
        DRDPSetting drdpSetting = new DRDPSetting();
        drdpSetting.setId("drdpSettingId001");
        drdpSetting.setAgencyId(record.getAgencyId());
        drdpSetting.setAgencyAlias("test_agency");
        drdpSetting.setComplete(true);
        CenterSettingData centerSettingData = new CenterSettingData();
        List<CenterSetting> centerSettings = new ArrayList<>();
        CenterSetting centerSetting = new CenterSetting();
        centerSetting.setId("centerSettingId001");
        centerSetting.setGroupIds(Collections.singletonList("groupId001"));
        centerSettings.add(centerSetting);
        centerSettingData.setCenterSettings(centerSettings);
        drdpSetting.setCenterSetting(JsonUtil.toJson(centerSettingData));
        drdpSettings.add(drdpSetting);
        when(agencyDao.getDRDPSettingByAgency(record.getAgencyId())).thenReturn(drdpSettings);

        // Act
        analysisService.updateSnapshotUploadStatus(record, uploadId, utcNow);

        verify(studentDao, times(1)).updateSnapshotUploadStatusByIds(any(), any(), any());
        // Assert
        // Add your assertions here based on what you expect the method to do
    }

    @Test
    public void testCheckMeasures_notAllMeasuresHaveScores_success() {
        // 准备测试数据
        List<String> measureIdList = Arrays.asList("a", "b", "v");

        // 创建Mock对象
        List<StudentScoreModel> studentScoreModels = new ArrayList<>();
        StudentScoreModel studentScoreModel = new StudentScoreModel();
        studentScoreModel.setDomainId("a");
        StudentScoreModel studentScoreModel2 = new StudentScoreModel();
        studentScoreModel2.setDomainId("b");
        studentScoreModels.add(studentScoreModel);
        studentScoreModels.add(studentScoreModel2);
        CheckLockResult result = analysisService.checkMeasures(studentScoreModels, measureIdList);

        // 验证结果
        assertFalse(result.isSuccess());
    }

    @Test
    public void testCheckMeasures_emptyStudentScoreModels_success() {
        // 准备测试数据
        List<StudentScoreModel> studentScoreModels = new ArrayList<>();
        List<String> measureIdList = Arrays.asList("1", "2", "3");

        // 调用被测试方法
        CheckLockResult result = analysisService.checkMeasures(studentScoreModels, measureIdList);

        // 验证结果
        assertFalse(result.isSuccess());
    }

    @Test
    public void testCheckMeasures_emptyMeasureIdList_success() {
        // 准备测试数据
        List<StudentScoreModel> studentScoreModels = new ArrayList<>();
        StudentScoreModel studentScoreModel = new StudentScoreModel();
        studentScoreModel.setDomainId("1");
        StudentScoreModel studentScoreModel2 = new StudentScoreModel();
        studentScoreModel.setDomainId("2");
        StudentScoreModel studentScoreModel3 = new StudentScoreModel();
        studentScoreModel.setDomainId("3");
        List<String> measureIdList = new ArrayList<>();

        // 调用被测试方法
        CheckLockResult result = analysisService.checkMeasures(studentScoreModels, measureIdList);

        // 验证结果
        assertTrue(result.isSuccess());
    }

    /**
     * 测试 restoreLatestActionPlan 方法
     * <p>
     * case 1: 验证存在可恢复的行动计划，行动计划关联测评点范围内新旧快照评分和测评点相同的情况下，会找回行动计划
     *
     * @throws InvalidProtocolBufferException InvalidProtocolBufferException
     */
    @Test
    public void testRestoreLatestActionPlan_restoreSuccess() throws InvalidProtocolBufferException {
        // case 1: 验证存在可恢复的行动计划，行动计划关联测评点范围内新旧快照评分和测评点相同的情况下，会更新行动计划的快照 ID
        // 数据准备
        String periodAlias = "2023-2024 Fall";
        // 创建行动计划实体
        ActionPlanEntity latestActionPlan = new ActionPlanEntity();
        latestActionPlan.setId("latestActionPlanId");
        latestActionPlan.setSnapshotId("latestActionPlanSnapshotId");
        // 创建行动计划详情，包含两个测评点 002 和 003
        List<ActionPlanDetailsEntity> actionPlanDetails = new ArrayList<>();
        ActionPlanDetailsEntity actionPlanDetail01 = new ActionPlanDetailsEntity();
        actionPlanDetail01.setMeasureId("002");
        actionPlanDetails.add(actionPlanDetail01);
        ActionPlanDetailsEntity actionPlanDetail02 = new ActionPlanDetailsEntity();
        actionPlanDetail02.setMeasureId("003");
        actionPlanDetails.add(actionPlanDetail02);

        // 创建 4 个测评点评分
        LGSnapshot.Score score1 = LGSnapshot.Score.newBuilder()
                .setDomainId("001")
                .setDomainName("001")
                .setDomainAbbr("001")
                .setScoreId("1")
                .setScore("1")
                .build();
        LGSnapshot.Score score2 = LGSnapshot.Score.newBuilder()
                .setDomainId("002")
                .setDomainName("002")
                .setDomainAbbr("002")
                .setScoreId("2")
                .setScore("2")
                .build();
        LGSnapshot.Score score3 = LGSnapshot.Score.newBuilder()
                .setDomainId("003")
                .setDomainName("003")
                .setDomainAbbr("003")
                .setScoreId("3")
                .setScore("3")
                .build();
        LGSnapshot.Score score4 = LGSnapshot.Score.newBuilder()
                .setDomainId("004")
                .setDomainName("004")
                .setDomainAbbr("004")
                .setScoreId("4")
                .setScore("4")
                .build();

        // 创建快照的当前评分记录，包含了行动计划关联的所有的测评点
        LGSnapshot.RatingRecords currentRatingRecords = CommRatingRecordsBuilder()
                .addScores(score1)
                .addScores(score2)
                .addScores(score3)
                .build();

        // 创建旧快照的当前评分记录，包含了行动计划关联的所有的测评点
        LGSnapshot.RatingRecords oldRatingRecords = CommRatingRecordsBuilder()
                .addScores(score1)
                .addScores(score2)
                .addScores(score3)
                .addScores(score4)
                .build();
        // 创建当前的学生快照
        LGSnapshot.StudentSnapshot currentStudentSnapshot = CommStudentSnapshotBuilder()
                .setRatingRecords(currentRatingRecords)
                .build();
        // 创建旧的学生快照
        LGSnapshot.StudentSnapshot oldStudentSnapshot = CommStudentSnapshotBuilder()
                .setRatingRecords(oldRatingRecords)
                .build();
        // 创建旧的学生快照实体
        StudentSnapshotEntity oldStudentSnapshotEntity = new StudentSnapshotEntity();
        oldStudentSnapshotEntity.setData(oldStudentSnapshot.toByteArray());

        // 接口模拟
        when(reportDao.getLatestActionPlanWithDeleted(anyString(), anyString(), anyString())).thenReturn(latestActionPlan);
        when(reportDao.findActionPlanDetailByPlanId(anyString())).thenReturn(actionPlanDetails);
        when(studentDao.getSnapshotWithDeleted(anyString())).thenReturn(oldStudentSnapshotEntity);

        // 调用测试接口
        analysisService.restoreLatestActionPlan("currentStudentSnapshotId", currentStudentSnapshot, periodAlias);

        // 结果验证
        Mockito.verify(reportDao, Mockito.times(1)).restoreAndUpdateActionPlanSnapshotIdById(any(), any()); // 不会更新行动计划的快照 ID
    }

    /**
     * 测试 restoreLatestActionPlan 方法
     * <p>
     * case 1: 验证存在可恢复的行动计划，行动计划关联测评点范围内新旧测评点相同快照评分不相同的情况下，不会找回行动计划
     * case 2: 验证存在可恢复的行动计划，行动计划关联测评点范围内新旧快照测评点不相同的情况下，不会找回行动计划
     *
     * @throws InvalidProtocolBufferException InvalidProtocolBufferException
     */
    @Test
    public void restoreLatestActionPlan_restoreFailScoreIsDifferent() throws InvalidProtocolBufferException {
        // case 1: 验证存在可恢复的行动计划，行动计划关联测评点范围内新旧测评点相同快照评分不相同的情况下，不会找回行动计划

        // 数据准备
        String periodAlias = "2023-2024 Fall";
        // 创建行动计划实体
        ActionPlanEntity latestActionPlan = new ActionPlanEntity();
        latestActionPlan.setId("latestActionPlanId");
        latestActionPlan.setSnapshotId("latestActionPlanSnapshotId");
        // 创建行动计划详情，包含两个测评点 002 和 003
        List<ActionPlanDetailsEntity> actionPlanDetails = new ArrayList<>();
        ActionPlanDetailsEntity actionPlanDetail01 = new ActionPlanDetailsEntity();
        actionPlanDetail01.setMeasureId("002");
        ActionPlanDetailsEntity actionPlanDetail02 = new ActionPlanDetailsEntity();
        actionPlanDetail02.setMeasureId("003");
        actionPlanDetails.add(actionPlanDetail01);
        actionPlanDetails.add(actionPlanDetail02);

        // 创建 5 个测评点评分，其中 002 测评点有两个不同的评分
        LGSnapshot.Score score1 = LGSnapshot.Score.newBuilder()
                .setDomainId("001")
                .setDomainName("001")
                .setDomainAbbr("001")
                .setScoreId("1")
                .setScore("1")
                .build();
        LGSnapshot.Score score2 = LGSnapshot.Score.newBuilder()
                .setDomainId("002")
                .setDomainName("002")
                .setDomainAbbr("002")
                .setScoreId("2")
                .setScore("2")
                .build();
        // 创建当前的评分测评点和当前评分测评点一致，但分数不同
        LGSnapshot.Score currentScore2 = LGSnapshot.Score.newBuilder()
                .setDomainId("002")
                .setDomainName("002")
                .setDomainAbbr("002")
                .setScoreId("22")
                .setScore("22")
                .build();
        LGSnapshot.Score score3 = LGSnapshot.Score.newBuilder()
                .setDomainId("003")
                .setDomainName("003")
                .setDomainAbbr("003")
                .setScoreId("3")
                .setScore("3")
                .build();
        LGSnapshot.Score score4 = LGSnapshot.Score.newBuilder()
                .setDomainId("004")
                .setDomainName("004")
                .setDomainAbbr("004")
                .setScoreId("4")
                .setScore("4")
                .build();

        // 创建快照的当前评分记录，和行动计划关联的相同的测评点 002 设置的分数和当前评分记录不同
        LGSnapshot.RatingRecords currentRatingRecords = CommRatingRecordsBuilder()
                .addScores(score1)
                .addScores(currentScore2)
                .addScores(score3)
                .build();

        // 创建旧快照的当前评分记录，包含了行动计划关联的所有的测评点
        LGSnapshot.RatingRecords oldRatingRecords = CommRatingRecordsBuilder()
                .addScores(score1)
                .addScores(score2) // 设置和当前评分记录不同的分数
                .addScores(score3)
                .addScores(score4)
                .build();
        // 创建当前的学生快照
        LGSnapshot.StudentSnapshot currentStudentSnapshot = CommStudentSnapshotBuilder()
                .setRatingRecords(currentRatingRecords)
                .build();
        // 创建旧的学生快照
        LGSnapshot.StudentSnapshot oldStudentSnapshot = CommStudentSnapshotBuilder()
                .setRatingRecords(oldRatingRecords)
                .build();
        // 创建旧的学生快照实体
        StudentSnapshotEntity oldStudentSnapshotEntity = new StudentSnapshotEntity();
        oldStudentSnapshotEntity.setData(oldStudentSnapshot.toByteArray());

        // 接口模拟
        when(reportDao.getLatestActionPlanWithDeleted(anyString(), anyString(), anyString())).thenReturn(latestActionPlan);
        when(reportDao.findActionPlanDetailByPlanId(anyString())).thenReturn(actionPlanDetails);
        when(studentDao.getSnapshotWithDeleted(anyString())).thenReturn(oldStudentSnapshotEntity);

        // 调用测试接口
        analysisService.restoreLatestActionPlan("currentStudentSnapshotId", currentStudentSnapshot, periodAlias);

        // 结果验证
        Mockito.verify(reportDao, Mockito.never()).restoreAndUpdateActionPlanSnapshotIdById(any(), any()); // 不会更新行动计划的快照 ID

        // case 2: 验证存在可恢复的行动计划，行动计划关联测评点范围内新旧快照测评点不相同的情况下，不会找回行动计划

        // 数据准备
        // 创建快照的当前评分记录，缺少行动计划关联的测评点 002
        currentRatingRecords = CommRatingRecordsBuilder()
                .addScores(score1)
                .addScores(score3)
                .addScores(score4)
                .build();

        // 创建旧快照的当前评分记录，包含了行动计划关联的所有的测评点
        oldRatingRecords = CommRatingRecordsBuilder()
                .addScores(score1)
                .addScores(score2)
                .addScores(score3)
                .addScores(score4)
                .build();

        // 重新创建当前的学生快照
        currentStudentSnapshot = currentStudentSnapshot.toBuilder().clearRatingRecords().setRatingRecords(currentRatingRecords).build();
        // 重新创建旧的学生快照
        oldStudentSnapshot = oldStudentSnapshot.toBuilder().clearRatingRecords().setRatingRecords(oldRatingRecords).build();
        // 重新创建旧的学生快照实体
        oldStudentSnapshotEntity = new StudentSnapshotEntity();
        oldStudentSnapshotEntity.setData(oldStudentSnapshot.toByteArray());

        // 接口模拟
        when(reportDao.getLatestActionPlanWithDeleted(anyString(), anyString(), anyString())).thenReturn(latestActionPlan);
        when(reportDao.findActionPlanDetailByPlanId(anyString())).thenReturn(actionPlanDetails);
        when(studentDao.getSnapshotWithDeleted(anyString())).thenReturn(oldStudentSnapshotEntity);

        // 调用测试接口
        analysisService.restoreLatestActionPlan("currentStudentSnapshotId", currentStudentSnapshot, periodAlias);

        // 结果验证
        Mockito.verify(reportDao, Mockito.never()).restoreAndUpdateActionPlanSnapshotIdById(any(), any()); // 不会更新行动计划的快照 ID
    }

    /**
     * 创建通用的有必填项默认值的学生快照构造器
     *
     * @return 学生快照构造器
     */
    private static LGSnapshot.StudentSnapshot.Builder CommStudentSnapshotBuilder() {
        com.learninggenie.common.report.LGSnapshot.ClassSnapshot classSnapshot = LGSnapshot.ClassSnapshot.newBuilder()
                .setId("1")
                .setName("classSnapshot")
                .setCreatedAt(1)
                .setUpdatedAt(1)
                .build();
        return LGSnapshot.StudentSnapshot.newBuilder()
                .setId("studentId")
                .setFirstName("enrollmentFirstName")
                .setGroup(classSnapshot);
    }

    /**
     * 创建通用的有必填项默认值的评分记录构造器
     *
     * @return 评分记录构造器
     */
    private static LGSnapshot.RatingRecords.Builder CommRatingRecordsBuilder() {
        return LGSnapshot.RatingRecords.newBuilder()
                .setFramework("framework")
                .setFrameworkId("frameworkId")
                .setPeriodAlias("2023-2024 Fall")
                .setFrom(1)
                .setTo(1);
    }

    @Test
    public void testGetGroupDRDPSettingMap() {
        String agencyId = UUID.randomUUID().toString();
        String groupId = UUID.randomUUID().toString();
        List<DRDPSetting> drdpSettings = new ArrayList<>();
        DRDPSetting drdpSetting = new DRDPSetting();
        drdpSetting.setAgencyId(agencyId);
        drdpSetting.setAgencyAlias("test_agency");
        drdpSetting.setComplete(true);
        CenterSettingData centerSettingData = new CenterSettingData();
        List<CenterSetting> centerSettings = new ArrayList<>();
        CenterSetting centerSetting = new CenterSetting();
        centerSetting.setId(UUID.randomUUID().toString());
        centerSetting.setGroupIds(Collections.singletonList(groupId));
        centerSettings.add(centerSetting);
        centerSettingData.setCenterSettings(centerSettings);
        drdpSetting.setCenterSetting(JsonUtil.toJson(centerSettingData));
        drdpSettings.add(drdpSetting);
        when(agencyDao.getDRDPSettingByAgency(agencyId)).thenReturn(drdpSettings);

        Map<String, DRDPSetting> result = analysisService.getGroupDRDPSettingMap(agencyId);
        Assert.assertEquals(result.get(groupId.toUpperCase()).getAgencyAlias(), "test_agency");
    }

    @Test
    public void testUpdateUploadStatus() {
        List<UploadDRDPRecord> records = new ArrayList<>();
        UploadDRDPRecord record = new UploadDRDPRecord();
        record.setId(UUID.randomUUID().toString());
        record.setStatus("PENDING");
        record.setUploadId("1");
        record.setAgencyId(UUID.randomUUID().toString());
        records.add(record);
        when(recordDao.getPendingUploadDRDPRecord()).thenReturn(records);

        GetDrdpBatchScoringResultResponse result = new GetDrdpBatchScoringResultResponse();
        result.setStatus(200);
        List<DrdpStudentScoreModel> studentScores = new ArrayList<>();
        DrdpStudentScoreModel studentScore = new DrdpStudentScoreModel();
        DrdpDemographics drdpDemographics = new DrdpDemographics();
        drdpDemographics.setAgency("test_agency");
        studentScore.setDemographics(drdpDemographics);
        studentScores.add(studentScore);
        result.setStudentScores(studentScores);
        when(drdpV2Service.getBatchScoringResult("1")).thenReturn(result);

        List<StudentSnapshotEntity> snapshots = new ArrayList<>();
        StudentSnapshotEntity snapshot = new StudentSnapshotEntity();
        snapshot.setId(UUID.randomUUID().toString());
        snapshot.setAgencyId(record.getAgencyId());
        snapshot.setUploadId("1");
        snapshot.setGroupId(UUID.randomUUID().toString());

        snapshots.add(snapshot);
        when(studentDao.getSnapshotsByUploadId("1")).thenReturn(snapshots);

        List<DRDPSetting> drdpSettings = new ArrayList<>();
        DRDPSetting drdpSetting = new DRDPSetting();
        drdpSetting.setAgencyId(record.getAgencyId());
        drdpSetting.setAgencyAlias("test_agency");
        drdpSetting.setComplete(true);
        CenterSettingData centerSettingData = new CenterSettingData();
        List<CenterSetting> centerSettings = new ArrayList<>();
        CenterSetting centerSetting = new CenterSetting();
        centerSetting.setId(UUID.randomUUID().toString());
        centerSetting.setGroupIds(Collections.singletonList(snapshot.getGroupId()));
        centerSettings.add(centerSetting);
        centerSettingData.setCenterSettings(centerSettings);
        drdpSetting.setCenterSetting(JsonUtil.toJson(centerSettingData));
        drdpSettings.add(drdpSetting);
        // when(agencyDao.getDRDPSettingByAgency(record.getAgencyId())).thenReturn(drdpSettings);

        analysisService.updateDRDPUploadStatus();
    }

    @Test
    public void testUpdateUploadStatus_no_snapshots() {
        List<UploadDRDPRecord> records = new ArrayList<>();
        UploadDRDPRecord record = new UploadDRDPRecord();
        record.setId(UUID.randomUUID().toString());
        record.setStatus("PENDING");
        record.setUploadId("1");
        record.setAgencyId(UUID.randomUUID().toString());
        records.add(record);
        when(recordDao.getPendingUploadDRDPRecord()).thenReturn(records);

        GetDrdpBatchScoringResultResponse result = new GetDrdpBatchScoringResultResponse();
        result.setStatus(200);
        List<DrdpStudentScoreModel> studentScores = new ArrayList<>();
        DrdpStudentScoreModel studentScore = new DrdpStudentScoreModel();
        DrdpDemographics drdpDemographics = new DrdpDemographics();
        drdpDemographics.setAgency("test_agency");
        studentScore.setDemographics(drdpDemographics);
        studentScores.add(studentScore);
        result.setStudentScores(studentScores);
        when(drdpV2Service.getBatchScoringResult("1")).thenReturn(result);

        List<StudentSnapshotEntity> snapshots = new ArrayList<>();

        when(studentDao.getSnapshotsByUploadId("1")).thenReturn(snapshots);

        List<DRDPSetting> drdpSettings = new ArrayList<>();
        DRDPSetting drdpSetting = new DRDPSetting();
        drdpSetting.setAgencyId(record.getAgencyId());
        drdpSetting.setAgencyAlias("test_agency");
        drdpSetting.setComplete(true);
        CenterSettingData centerSettingData = new CenterSettingData();
        List<CenterSetting> centerSettings = new ArrayList<>();
        CenterSetting centerSetting = new CenterSetting();
        centerSetting.setId(UUID.randomUUID().toString());
        centerSetting.setGroupIds(Collections.singletonList(UUID.randomUUID().toString()));
        centerSettings.add(centerSetting);
        centerSettingData.setCenterSettings(centerSettings);
        drdpSetting.setCenterSetting(JsonUtil.toJson(centerSettingData));
        drdpSettings.add(drdpSetting);
//        Mockito.when(agencyDao.getDRDPSettingByAgency(record.getAgencyId())).thenReturn(drdpSettings);

        analysisService.updateDRDPUploadStatus();
    }

    @Test
    public void testUpdateUploadStatus_no_children() {
        List<UploadDRDPRecord> records = new ArrayList<>();
        UploadDRDPRecord record = new UploadDRDPRecord();
        record.setId(UUID.randomUUID().toString());
        record.setStatus("PENDING");
        record.setUploadId("1");
        record.setAgencyId(UUID.randomUUID().toString());
        records.add(record);
        when(recordDao.getPendingUploadDRDPRecord()).thenReturn(records);

        GetDrdpBatchScoringResultResponse result = new GetDrdpBatchScoringResultResponse();
        result.setStatus(200);
        List<DrdpStudentScoreModel> studentScores = new ArrayList<>();
        result.setStudentScores(studentScores);
        when(drdpV2Service.getBatchScoringResult("1")).thenReturn(result);

        List<StudentSnapshotEntity> snapshots = new ArrayList<>();
        StudentSnapshotEntity snapshot = new StudentSnapshotEntity();
        snapshot.setId(UUID.randomUUID().toString());
        snapshot.setAgencyId(record.getAgencyId());
        snapshot.setUploadId("1");
        snapshot.setGroupId(UUID.randomUUID().toString());

        snapshots.add(snapshot);
//        Mockito.when(studentDao.getSnapshotsByUploadId("1")).thenReturn(snapshots);

        List<DRDPSetting> drdpSettings = new ArrayList<>();
        DRDPSetting drdpSetting = new DRDPSetting();
        drdpSetting.setAgencyId(record.getAgencyId());
        drdpSetting.setAgencyAlias("test_agency");
        drdpSetting.setComplete(true);
        CenterSettingData centerSettingData = new CenterSettingData();
        List<CenterSetting> centerSettings = new ArrayList<>();
        CenterSetting centerSetting = new CenterSetting();
        centerSetting.setId(UUID.randomUUID().toString());
        centerSetting.setGroupIds(Collections.singletonList(snapshot.getGroupId()));
        centerSettings.add(centerSetting);
        centerSettingData.setCenterSettings(centerSettings);
        drdpSetting.setCenterSetting(JsonUtil.toJson(centerSettingData));
        drdpSettings.add(drdpSetting);
//        Mockito.when(agencyDao.getDRDPSettingByAgency(record.getAgencyId())).thenReturn(drdpSettings);

        analysisService.updateDRDPUploadStatus();
    }
}

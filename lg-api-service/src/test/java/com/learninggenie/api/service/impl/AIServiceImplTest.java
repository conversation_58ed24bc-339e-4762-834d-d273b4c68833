package com.learninggenie.api.service.impl;

import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.model.ai.AILoginResponse;
import com.learninggenie.api.model.ai.EvidenceRequest;
import com.learninggenie.api.model.ai.EvidenceResponse;
import com.learninggenie.api.model.user.LoginRequest;
import com.learninggenie.common.data.dao.AIDao;
import com.learninggenie.common.data.model.ai.AIActionModel;
import com.learninggenie.common.data.model.ai.AIUserModel;
import com.learninggenie.common.data.model.ai.MeasureLevelEvidenceModel;
import com.learninggenie.common.utils.TimeUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;

import static org.junit.Assert.assertEquals;

@RunWith(MockitoJUnitRunner.class)
public class AIServiceImplTest {
    @Mock
    private AIDao aiDao;
    @InjectMocks
    private AIServiceImpl aiService;

    /**
     * AI登陆
     * 邮箱错误
     */
    @Test(expected = BusinessException.class)
    public void login_emailError() throws Exception {
        LoginRequest request = new LoginRequest();
        request.setEmail("<EMAIL>");
        request.setPassword("aaa");


        Mockito.when(aiDao.getUserByEmail("<EMAIL>")).thenReturn(null);

        aiService.login(request, null);
    }

    /**
     * AI登陆
     * 密码错误
     */
    @Test(expected = BusinessException.class)
    public void login_passwordError() throws Exception {
        LoginRequest request = new LoginRequest();
        request.setEmail("<EMAIL>");
        request.setPassword("111");

        AIUserModel user = new AIUserModel();
        user.setEmail("<EMAIL>");
        user.setPassword("05204ab6a237645d3865de5c17ac03e6dc9a022b22f08d73");

        Mockito.when(aiDao.getUserByEmail("<EMAIL>")).thenReturn(user);

        aiService.login(request, null);
    }

    /**
     * AI登陆
     * 登陆成功
     */
    @Test
    public void login_success() throws Exception {
        LoginRequest request = new LoginRequest();
        request.setEmail("<EMAIL>");
        request.setPassword("12345678abc");

        AIUserModel user = new AIUserModel();
        user.setEmail("<EMAIL>");
        user.setPassword("05204ab6a237645d3865de5c17ac03e6dc9a022b22f08d73");

        Mockito.when(aiDao.getUserByEmail("<EMAIL>")).thenReturn(user);

        AILoginResponse response = aiService.login(request, null);

        assertEquals(request.getEmail(), response.getEmail());
    }

    /**
     * 设置句子evidence的tags
     * 用户未找到
     */
    @Test(expected = BusinessException.class)
    public void setEvidenceTags_noUser() throws Exception {
        EvidenceRequest request = new EvidenceRequest();
        request.setEvidenceId(1);
        request.setTags("1234567");

        AIUserModel user = new AIUserModel();
        user.setId("u001");
        user.setPassword("05204ab6a237645d3865de5c17ac03e6dc9a022b22f08d73");

//        Mockito.when(aiDao.getUserById("u001")).thenReturn(null);

        aiService.setEvidenceTags(request, null);

        Mockito.verify(aiDao, Mockito.times(1)).getUserById("u001");
    }

    /**
     * 设置句子evidence的tags
     * 句子evidence未找到
     */
    @Test(expected = BusinessException.class)
    public void setEvidenceTags_noEvidence() throws Exception {
        EvidenceRequest request = new EvidenceRequest();
        request.setEvidenceId(1);
        request.setTags("aaa");

        AIUserModel user = new AIUserModel();
        user.setId("u001");
        user.setPassword("YWFh");

        MeasureLevelEvidenceModel evidenceModel = new MeasureLevelEvidenceModel();
        evidenceModel.setId(1);
        evidenceModel.setEvidence("aa");

        Mockito.when(aiDao.getUserById("u001")).thenReturn(user);
        Mockito.when(aiDao.getEvidence(1, "u001")).thenReturn(null);

        aiService.setEvidenceTags(request, "u001");

        Mockito.verify(aiDao, Mockito.times(1)).getUserById("u001");
        Mockito.verify(aiDao, Mockito.times(1)).getEvidence(1, "u001");
    }

    /**
     * 设置句子evidence的tags
     * 成功创建CREATE
     */
    @Test
    public void setEvidenceTags_createSuccess() throws Exception {
        EvidenceRequest request = new EvidenceRequest();
        request.setEvidenceId(1);
        request.setTags("ATL1");

        AIUserModel user = new AIUserModel();
        user.setId("u001");
        user.setPassword("YWFh");
        user.setCurrentTaskId("t001");

        MeasureLevelEvidenceModel evidenceModel = new MeasureLevelEvidenceModel();
        evidenceModel.setId(1);
        evidenceModel.setEvidence("aa");
        evidenceModel.setTaskId("t001");

        AIActionModel tagsAction = new AIActionModel();
        tagsAction.setId("a001");
        tagsAction.setAction("CREATE");
        tagsAction.setUserId("u001");
        tagsAction.setEvidenceId(1);
        tagsAction.setObjectType("TAG");
        tagsAction.setData("ATL1");

        Mockito.when(aiDao.getUserById("u001")).thenReturn(user);
        Mockito.when(aiDao.getEvidence(1, "u001")).thenReturn(evidenceModel);
        Mockito.when(aiDao.getEvidenceTagsAction(1, "u001", "t001")).thenReturn(null);

        aiService.setEvidenceTags(request, "u001");

        Mockito.verify(aiDao, Mockito.times(1)).getUserById("u001");
        Mockito.verify(aiDao, Mockito.times(1)).getEvidence(1, "u001");
        Mockito.verify(aiDao, Mockito.times(1)).createEvidenceTagsAction(Mockito.any());
    }

    /**
     * 设置句子evidence的tags
     * 不成功update
     */
    @Test
    public void setEvidenceTags_updateFailed() throws Exception {
        EvidenceRequest request = new EvidenceRequest();
        request.setEvidenceId(1);
        request.setTags("ATL1");

        AIUserModel user = new AIUserModel();
        user.setId("u001");
        user.setPassword("YWFh");

        MeasureLevelEvidenceModel evidenceModel = new MeasureLevelEvidenceModel();
        evidenceModel.setId(1);
        evidenceModel.setEvidence("aa");

        AIActionModel tagsAction = new AIActionModel();
        tagsAction.setId("a001");
        tagsAction.setAction("CREATE");
        tagsAction.setUserId("u001");
        tagsAction.setEvidenceId(1);
        tagsAction.setObjectType("TAG");
        tagsAction.setData("ATL1");

        Mockito.when(aiDao.getUserById("u001")).thenReturn(user);
        Mockito.when(aiDao.getEvidence(1, "u001")).thenReturn(evidenceModel);
//        Mockito.when(aiDao.getEvidenceTagsAction(1, "u001", "t001")).thenReturn(tagsAction);

        aiService.setEvidenceTags(request, "u001");

        Mockito.verify(aiDao, Mockito.times(1)).getUserById("u001");
        Mockito.verify(aiDao, Mockito.times(1)).getEvidence(1, "u001");
        Mockito.verify(aiDao, Mockito.times(1)).createEvidenceTagsAction(Mockito.any());
        Mockito.verify(aiDao, Mockito.times(0)).updateEvidenceTagsAction(Mockito.any());
        Mockito.verify(aiDao, Mockito.times(1)).updateLastSet("u001", 1);
    }

    /**
     * 设置句子evidence的tags
     * 成功update
     */
    @Test
    public void setEvidenceTags_updateSuccess() throws Exception {
        EvidenceRequest request = new EvidenceRequest();
        request.setEvidenceId(1);
        request.setTags("ATL1");

        AIUserModel user = new AIUserModel();
        user.setId("u001");
        user.setPassword("YWFh");
        user.setCurrentTaskId("t001");

        MeasureLevelEvidenceModel evidenceModel = new MeasureLevelEvidenceModel();
        evidenceModel.setId(1);
        evidenceModel.setEvidence("aa");
        evidenceModel.setTaskId("t001");

        AIActionModel tagsAction = new AIActionModel();
        tagsAction.setId("a001");
        tagsAction.setAction("CREATE");
        tagsAction.setUserId("u001");
        tagsAction.setEvidenceId(1);
        tagsAction.setObjectType("TAG");
        tagsAction.setData("ATL1,ALT2");

        Mockito.when(aiDao.getUserById("u001")).thenReturn(user);
        Mockito.when(aiDao.getEvidence(1, "u001")).thenReturn(evidenceModel);
        Mockito.when(aiDao.getEvidenceTagsAction(1, "u001", "t001")).thenReturn(tagsAction);

        aiService.setEvidenceTags(request, "u001");

        Mockito.verify(aiDao, Mockito.times(1)).getUserById("u001");
        Mockito.verify(aiDao, Mockito.times(1)).getEvidence(1, "u001");
        Mockito.verify(aiDao, Mockito.times(0)).createEvidenceTagsAction(Mockito.any());
        Mockito.verify(aiDao, Mockito.times(1)).updateEvidenceTagsAction(Mockito.any());
        Mockito.verify(aiDao, Mockito.times(1)).updateLastSet("u001", 1);
    }

    /**
     * 获取句子evidence的信息
     * 成功获取
     */
    @Test
    public void getEvidence_success() throws Exception {
        EvidenceRequest request = new EvidenceRequest();
        request.setEvidenceId(1);
        request.setTags("ATL1");

        AIUserModel user = new AIUserModel();
        user.setId("u001");
        user.setPassword("YWFh");
        user.setStart(1);
        user.setEnd(5);
        user.setLastSet(0);
        user.setCurrentTaskId("t001");

        MeasureLevelEvidenceModel evidenceModel = new MeasureLevelEvidenceModel();
        evidenceModel.setId(1);
        evidenceModel.setEvidence("aa");
        Date utc = TimeUtil.getUtcNow();
        evidenceModel.setCreateAtUtc(utc);
        evidenceModel.setUpdateAtUtc(utc);
        evidenceModel.setTaskId("t001");

        AIActionModel tagsAction = new AIActionModel();
        tagsAction.setId("a001");
        tagsAction.setAction("CREATE");
        tagsAction.setUserId("u001");
        tagsAction.setEvidenceId(1);
        tagsAction.setObjectType("TAG");
        tagsAction.setData("ATL1,ALT2");

        Mockito.when(aiDao.getUserById("u001")).thenReturn(user);
        Mockito.when(aiDao.getEvidence(1, "u001")).thenReturn(evidenceModel);
        Mockito.when(aiDao.getEvidenceTagsAction(1, "u001", "t001")).thenReturn(tagsAction);
        Mockito.when(aiDao.getEvidenceCount()).thenReturn(20);

        EvidenceResponse response = aiService.getEvidence(1, "u001");

        Mockito.verify(aiDao, Mockito.times(1)).getUserById("u001");
        Mockito.verify(aiDao, Mockito.times(1)).getEvidence(1, "u001");
        Mockito.verify(aiDao, Mockito.times(1)).getEvidenceTagsAction(1, "u001", "t001");

        assertEquals("aa", response.getEvidence());
        assertEquals(1, response.getId());
//        assertEquals(0, response.getPreId());
//        assertEquals(0, response.getNextId());
        assertEquals(0, response.getTotal());
        assertEquals(20, response.getEvidenceTotal());
    }

    /**
     * 获取句子evidence的信息
     * 成功获取
     */
    @Test(expected = BusinessException.class)
    public void getEvidence_noUser() throws Exception {
        EvidenceRequest request = new EvidenceRequest();
        request.setEvidenceId(1);
        request.setTags("ATL1");

        AIUserModel user = new AIUserModel();
        user.setId("u001");
        user.setPassword("YWFh");
        user.setStart(1);
        user.setEnd(5);
        user.setLastSet(0);

        Mockito.when(aiDao.getUserById("u001")).thenReturn(null);

        aiService.getEvidence(1, "u001");

        Mockito.verify(aiDao, Mockito.times(1)).getUserById("u001");
        Mockito.verify(aiDao, Mockito.times(0)).getEvidence(1, "u001");
        Mockito.verify(aiDao, Mockito.times(0)).getEvidenceTagsAction(1, "u001", "t001");
    }

    /**
     * 获取句子evidence的信息
     * 没有句子
     */
    @Test(expected = BusinessException.class)
    public void getEvidence_noEvidence() throws Exception {
        EvidenceRequest request = new EvidenceRequest();
        request.setEvidenceId(1);
        request.setTags("ATL1");

        AIUserModel user = new AIUserModel();
        user.setId("u001");
        user.setPassword("YWFh");
        user.setStart(1);
        user.setEnd(5);
        user.setLastSet(0);

        MeasureLevelEvidenceModel evidenceModel = new MeasureLevelEvidenceModel();
        evidenceModel.setId(1);
        evidenceModel.setEvidence("aa");
        Date utc = TimeUtil.getUtcNow();
        evidenceModel.setCreateAtUtc(utc);
        evidenceModel.setUpdateAtUtc(utc);

        AIActionModel tagsAction = new AIActionModel();
        tagsAction.setId("a001");
        tagsAction.setAction("CREATE");
        tagsAction.setUserId("u001");
        tagsAction.setEvidenceId(1);
        tagsAction.setObjectType("TAG");
        tagsAction.setData("ATL1,ALT2");

        Mockito.when(aiDao.getUserById("u001")).thenReturn(user);
        Mockito.when(aiDao.getEvidence(1, "u001")).thenReturn(null);
//        Mockito.when(aiDao.getEvidenceCount()).thenReturn(20);

        EvidenceResponse response = aiService.getEvidence(1, "u001");

        Mockito.verify(aiDao, Mockito.times(1)).getUserById("u001");
        Mockito.verify(aiDao, Mockito.times(1)).getEvidence(1, "u001");
        Mockito.verify(aiDao, Mockito.times(0)).getEvidenceTagsAction(1, "u001", "t001");

        assertEquals(20, response.getEvidenceTotal());
        assertEquals(null, response.getEvidence());
    }

    /**
     * 获取句子evidence的信息
     * 用户没有权限
     */
    @Test(expected = BusinessException.class)
    public void getEvidence_noAuthentic() throws Exception {
        EvidenceRequest request = new EvidenceRequest();
        request.setEvidenceId(1);
        request.setTags("ATL1");

        AIUserModel user = new AIUserModel();
        user.setId("u001");
        user.setPassword("YWFh");
        user.setStart(3);
        user.setEnd(5);
        user.setLastSet(0);

        MeasureLevelEvidenceModel evidenceModel = new MeasureLevelEvidenceModel();
        evidenceModel.setId(1);
        evidenceModel.setEvidence("aa");
        Date utc = TimeUtil.getUtcNow();
        evidenceModel.setCreateAtUtc(utc);
        evidenceModel.setUpdateAtUtc(utc);

        AIActionModel tagsAction = new AIActionModel();
        tagsAction.setId("a001");
        tagsAction.setAction("CREATE");
        tagsAction.setUserId("u001");
        tagsAction.setEvidenceId(1);
        tagsAction.setObjectType("TAG");
        tagsAction.setData("ATL1,ALT2");

        Mockito.when(aiDao.getUserById("u001")).thenReturn(user);
        Mockito.when(aiDao.getEvidence(1, "u001")).thenReturn(null);
//        Mockito.when(aiDao.getEvidenceCount()).thenReturn(20);

        aiService.getEvidence(1, "u001");

        Mockito.verify(aiDao, Mockito.times(1)).getUserById("u001");
        Mockito.verify(aiDao, Mockito.times(0)).getEvidence(1, "u001");
        Mockito.verify(aiDao, Mockito.times(0)).getEvidenceTagsAction(1, "u001", "t001");
    }

}
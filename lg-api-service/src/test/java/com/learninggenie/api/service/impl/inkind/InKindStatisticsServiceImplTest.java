package com.learninggenie.api.service.impl.inkind;

import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.provider.impl.InKindProviderImpl;
import com.learninggenie.api.service.DashboardService;
import com.learninggenie.common.data.dao.*;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.dao.inkind.InKindReportGrantDao;
import com.learninggenie.common.data.dao.inkind.InKindReportGroupDao;
import com.learninggenie.common.data.dao.inkind.InKindsGoalGrantDao;
import com.learninggenie.common.data.dao.inkind.InKindsGoalGroupDao;
import com.learninggenie.common.data.entity.AgencyEntity;
import com.learninggenie.common.data.entity.InkindSchoolYearEntity;
import com.learninggenie.common.data.mapper.dynamo.AgencyMetadataMapper;
import com.learninggenie.common.utils.TimeUtil;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Date;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class InKindStatisticsServiceImplTest {
    @InjectMocks
    InKindStatisticsServiceImpl inKindStatisticsService;

    @Mock
    private InKindProviderImpl inKindCommonService;

    @Mock
    private UserProvider userProvider;

    @Mock
    private AgencyDao agencyDao;

    @Mock
    private CenterDao centerDao;

    @Mock
    private GroupDao groupDao;

    @Mock
    private StudentDao studentDao;

    @Mock
    private UserDaoImpl userDao;

    @Mock
    private InkindDao inkindDao;

    @Mock
    private AgencyMetadataMapper agencyMetadataMapper;

    @Mock
    private InKindsGoalGroupDao inKindsGoalGroupDao;

    @Mock
    private InKindReportGroupDao inKindReportGroupDao;

    @Mock
    private InKindReportGrantDao inKindReportGrantDao;

    @Mock
    private DashboardService dashboardService;

    @Mock
    private InKindsGoalGrantDao inKindsGoalGrantDao;

    @Test
    void getStats_InKindAssign() {
        // 查询 In-Kind 任务的日期时间按照本地时间
        String enrollmentId = "1";
        String schoolYearId = "71db37ce-e307-46db-87e6-f15cfb54b8ee";
        String date = "2022-05";

        Date utcNow = TimeUtil.getUtcNow();
        Date parse = TimeUtil.parse(date, "yyyy-MM");
        Date fromDate = TimeUtil.getFirstDayOfMonth(parse);
        Date toDate = TimeUtil.getLastDayOfMonth(parse);
        AgencyEntity agency = new AgencyEntity();
        agency.setId("a1");
        InkindSchoolYearEntity schoolYear = new InkindSchoolYearEntity();
        schoolYear.setId(schoolYearId);
        schoolYear.setSchoolYear("2022-2023");
        schoolYear.setStartDate(TimeUtil.addDays(utcNow, 1));
        schoolYear.setShowRate(true);
        // 模拟数据
        when(agencyDao.getAgencyByChildId_V2(enrollmentId)).thenReturn(agency);
        try (MockedStatic<TimeUtil> timeUtil = mockStatic(TimeUtil.class)) {
            timeUtil.when(() -> TimeUtil.getFirstDayOfMonth(parse)).thenReturn(fromDate);
            timeUtil.when(() -> TimeUtil.getLastDayOfMonth(parse)).thenReturn(toDate);
            timeUtil.when(() -> TimeUtil.parse(date, "yyyy-MM")).thenReturn(parse);
            when(inkindDao.getCurrentAssignIdAndDateRange(any(), any(), any(), any())).thenReturn(new ArrayList<>());
            when(inkindDao.getSchoolYearById(schoolYearId)).thenReturn(schoolYear);

            inKindStatisticsService.getStats(date, enrollmentId, schoolYearId, "2022-05-19", "2022-05-19");
            // 验证获取当前时间的任务列表的入参是否正确
            verify(inkindDao).getCurrentAssignIdAndDateRange(enrollmentId, agency.getId(), fromDate, toDate);
        }
    }

}

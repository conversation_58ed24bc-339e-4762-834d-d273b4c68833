package com.learninggenie.api.service.impl;

import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.exception.ErrorCode;
import com.learninggenie.api.service.lti.State;
import com.learninggenie.api.service.lti.handler.LTIRequestHandler;
import com.learninggenie.common.cache.CacheService;
import com.learninggenie.common.data.dao.MetaDao;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.web.client.RestTemplate;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

class LTIServiceImplTest {
    
    @Mock
    private LTIRequestHandler handler;
    
    @Mock
    private MetaDao metaDao;
    
    @Mock
    private RestTemplate restTemplate;
    
    @Mock
    private CacheService cacheService;
    
    @Mock
    private Map<String, String> request;
    
    private LTIServiceImpl ltiService;
    
    /**
     * 测试支持的 LTI 平台
     */
    @Test
    public void shouldBeSupport() {
        String iss = "https://support.com";
        when(request.get("iss")).thenReturn(iss);
        when(handler.isSupport(iss)).thenReturn(true);
        when(handler.handleOIDCInitiationRequest(request)).thenReturn(null);
        
        assertEquals(ltiService.initOIDC(request), null);
        verify(handler).isSupport(iss);
        verify(handler).handleOIDCInitiationRequest(request);
    }
    
    /**
     * 测试未支持的 LTI 平台
     */
    @Test
    public void shouldNotBeSupport() {
        String iss = "https://unsupport.com";
        when(request.get("iss")).thenReturn(iss);
        when(handler.isSupport(iss)).thenReturn(false);
        when(handler.handleOIDCInitiationRequest(request)).thenReturn(null);
        
        assertEquals(
            assertThrows(BusinessException.class,
                () -> ltiService.initOIDC(request)).getErrorCode(),
            ErrorCode.UNSUPPORTED_LTI_PLATFORM);
        
        verify(handler).isSupport(iss);
        verify(handler, times(0)).handleOIDCInitiationRequest(request);
    }
    
    /**
     * 能从 cache 中获取到对应的 State 对象，然后调用对应的 handler 进行处理
     */
    @Test
    public void shouldGetState() {
        String iss = "https://support.com";
        when(handler.handleAuthenticationResponse(request)).thenReturn(null);
        when(request.get("state")).thenReturn("state1");
        when(cacheService.getObj("state1", State.class)).thenReturn(new State(iss, "nonce1"));
        when(handler.isSupport(iss)).thenReturn(true);
        
        assertEquals(ltiService.loginRedirect(request), null);
        verify(handler).isSupport(iss);
        verify(handler).handleAuthenticationResponse(request);
        verify(cacheService).getObj("state1", State.class);
        verify(request).get("state");
    }
    
    /**
     * 不能从 cache 中获取到对应的 State 对象，抛出异常
     */
    @Test
    public void shouldNotGetState() {
        when(handler.handleAuthenticationResponse(request)).thenReturn(null);
        when(request.get("state")).thenReturn("state1");
        when(cacheService.getObj(anyString(), eq(State.class))).thenReturn(null);
        when(handler.isSupport(null)).thenReturn(false);
        
        assertEquals(
            assertThrows(BusinessException.class,
                () -> ltiService.loginRedirect(request)).getErrorCode(),
            ErrorCode.OIDC_AUTHENTICATION_FAILED);
        
        verify(handler).isSupport(null);
        verify(handler, times(0)).handleAuthenticationResponse(request);
        verify(cacheService).getObj("state1", State.class);
        verify(request).get("state");
    }
    
    private AutoCloseable closeable;
    
    @BeforeEach
    public void openMocks() throws NoSuchFieldException, IllegalAccessException {
        closeable = MockitoAnnotations.openMocks(this);
        ltiService = new LTIServiceImpl(
            Arrays.asList(handler),
            restTemplate,
            metaDao
        );
        
        Field cs = ltiService.getClass().getDeclaredField("cacheService");
        cs.setAccessible(true);
        cs.set(ltiService, cacheService);
    }
    
    @AfterEach
    public void releaseMocks() throws Exception {
        closeable.close();
    }
    
}
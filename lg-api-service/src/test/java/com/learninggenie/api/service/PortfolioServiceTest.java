package com.learninggenie.api.service;

import com.learninggenie.api.config.TestBase;
import com.learninggenie.common.data.dao.PortfolioDao;
import com.learninggenie.common.data.model.ScoreTemplateEntity;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.*;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

@Ignore
public class PortfolioServiceTest extends TestBase {
    @InjectMocks


    @Autowired
    private PortfolioService portfolioService;

    @Mock
    private PortfolioDao portfolioDao;

    private ScoreTemplateEntity templateEntityBase;
    private List<ScoreTemplateEntity> templateEntities;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        templateEntities=new ArrayList<>();
        String portfolioId="001";
        String levels="test levels data";
        String domainLevels="test domain levels data";

        templateEntityBase =new ScoreTemplateEntity();
        templateEntityBase.setPortfolioId(levels);
        templateEntityBase.setDomainLevelsJson(domainLevels);

        Mockito.when(portfolioDao.loadScoreTemplate(portfolioId)).thenReturn(templateEntityBase);
        Mockito.when(portfolioDao.saveScoreTemplate(ArgumentMatchers.any(ScoreTemplateEntity.class))).thenAnswer(new Answer<ScoreTemplateEntity>() {
            @Override
            public ScoreTemplateEntity answer(InvocationOnMock invocationOnMock) throws Throwable {
                Object[] args = invocationOnMock.getArguments();
                ScoreTemplateEntity entity=(ScoreTemplateEntity)args[0];
                templateEntities.add(entity);
                return entity;
            }
        });
    }

    @Test
    public void testGetScoreTemplate() {
        ScoreTemplateEntity rel=portfolioService.getScoreTemplate("001");
        Assert.assertNotNull("the obtained score template shouldn't be null.",rel);
        Assert.assertEquals(templateEntityBase.getPortfolioId(), rel.getPortfolioId());
    }

    @Test
    public void testGetScoreTemplateByInvalidId(){
        ScoreTemplateEntity rel=portfolioService.getScoreTemplate("errorId");
        Assert.assertNull("the obtained score template should be null.",rel);
    }

    @Test
    public void testSaveScoreTemplate() {
        templateEntities.clear();
        portfolioService.saveScoreTemplate(templateEntityBase);
        Assert.assertTrue(templateEntities.size()==1);
    }


}

package com.learninggenie.api.service.impl.inkind;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.toolkit.ChainWrappers;
import com.learninggenie.api.model.*;
import com.learninggenie.api.model.inkind.InKindDonateApproveRequest;
import com.learninggenie.api.model.inkind.InkindCenterGroupRequest;
import com.learninggenie.api.model.inkind.InkindRemindChildResponse;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.security.AuthUserDetails;
import com.learninggenie.api.provider.InKindProvider;
import com.learninggenie.common.comm.CommService;
import com.learninggenie.common.data.dao.*;
import com.learninggenie.common.data.dao.impl.InKindReportApproveDaoImpl;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.dao.inkind.*;
import com.learninggenie.common.data.dao.inkind.impl.InKindsGoalSchoolYearDaoImpl;
import com.learninggenie.common.data.dao.users.UserReminderDao;
import com.learninggenie.common.data.entity.*;
import com.learninggenie.common.data.entity.GroupEntity;
import com.learninggenie.common.data.entity.UserEntity;
import com.learninggenie.common.data.entity.inkind.InKindReportApprove;
import com.learninggenie.common.data.entity.inkind.InKindsGoalSchoolYear;
import com.learninggenie.common.data.enums.InKindStageEnum;
import com.learninggenie.common.data.enums.InkindApproveStatus;
import com.learninggenie.common.data.enums.UserMetaKey;
import com.learninggenie.common.data.enums.inkind.ApproveRole;
import com.learninggenie.common.data.enums.inkind.InKindApproveMode;
import com.learninggenie.common.data.enums.inkind.InKindGoalSchoolYearStep;
import com.learninggenie.common.data.mapper.mybatisplus.inkind.InKindReportApproveMapper;
import com.learninggenie.common.data.mapper.mybatisplus.inkind.InKindReportModelMapper;
import com.learninggenie.common.data.mapper.mybatisplus.inkind.InKindsGoalSchoolYearMapper;
import com.learninggenie.common.data.model.*;
import com.learninggenie.common.data.model.dynamo.AgencyMetadata;
import com.learninggenie.common.data.model.inkind.InKindReportModel;
import com.learninggenie.common.data.model.inkind.ReportReviewModel;
import com.learninggenie.common.filesystem.FileSystem;
import com.learninggenie.common.region.RegionService;
import com.learninggenie.common.utils.*;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.learninggenie.common.data.enums.inkind.InKindApproveMode.TEACHER_APPROVE_AND_ADMIN_RATIFY;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class InKindApprovalServiceImplTest {
    @InjectMocks
    InKindApprovalServiceImpl inKindApprovalService;

    @Mock
    private InKindProvider inKindProvider;

    @Mock
    private UserProvider userProvider;

    @Mock
    private AgencyDao agencyDao;

    @Mock
    private CenterDao centerDao;

    @Mock
    private GroupDao groupDao;

    @Mock
    private StudentDao studentDao;

    @Mock
    private UserDaoImpl userDao;

    @Mock
    private InkindDao inkindDao;

    @Mock
    private InKindReportApproveDao inKindReportApproveDao;

    @Mock
    private InKindReportGrantDao inKindReportGrantDao;

    @Mock
    private InKindsGoalSchoolYearDao inKindsGoalSchoolYearDao;

    @Mock
    private UserReminderDao userReminderDao;

    @Mock
    private FileSystem fileSystem;

    @Mock
    private PushNotificationDao pushNotificationDao;

    @Mock
    private CommService commService;

    @Mock
    private RegionService regionService;

    @Mock
    InvitationsEnrollmentInvitationDao invitationsEnrollmentInvitationDao;

    @Mock
    InKindsGoalSchoolYearDaoImpl inKindsGoalSchoolYearDaoImpl;

    @Mock
    InKindReportApproveDaoImpl inKindReportApproveDaoImpl;

    @Mock
    InKindsGoalSchoolYearMapper inKindsGoalSchoolYearMapper;

    @Mock
    InKindReportModelMapper inKindReportModelMapper;

    @Mock
    InKindReportApproveMapper inKindReportApproveMapper;

    private static final String BASE_MAPPER = "baseMapper";

    private MockedStatic<ChainWrappers> chainWrappersMockedStatic;

    /**
     * 初始化 TableInfoHelper
     */
    @BeforeAll
    public static void beforeClass() {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), InKindReportModel.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), InKindReportApprove.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), InKindsGoalSchoolYear.class);
    }

    /**
     * 注册 MockedStatic
     */
    @BeforeEach
    public void beforeMethod() {
        // 打印
        chainWrappersMockedStatic = mockStatic(ChainWrappers.class);
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 关闭 MockedStatic
     */
    @AfterEach
    public void afterMethod() {
        // 打印
        chainWrappersMockedStatic.close();
    }

    /**
     * 测试审核报告
     * case: 审核通过，记住签名
     */
    @Test
    void testApproveDonateInKindCase1() {
        // 数据准备 -- 入参
        String userId = "U0001";
        String agencyId = "A0001";

        AuthUserDetails authUserDetails = new AuthUserDetails();
        authUserDetails.setAgencyId(agencyId);

        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        agencyModel.setName("test Agency");

        InKindDonateApproveRequest request = new InKindDonateApproveRequest();
        request.setStatus("PENDING");
        request.setApproveSignatureId("S0001");
        request.setApproveUserId(userId);
        request.setApproveUserName("test user");
        request.setDonateIds(Arrays.asList("D0001", "D0002", "D0003"));
        request.setRememberSignature(true);

        List<InkindReportAppendEntity> inkindReportAppendEntityList = new ArrayList<>();
        InkindReportAppendEntity inkindReportAppendEntity = new InkindReportAppendEntity();
        inkindReportAppendEntity.setAgencyId(agencyId);
        inkindReportAppendEntity.setId("D0001");
        inkindReportAppendEntity.setApproveStatus("PENDING");
        inkindReportAppendEntityList.add(inkindReportAppendEntity);

        InkindReportAppendEntity inkindReportAppendEntity1 = new InkindReportAppendEntity();
        inkindReportAppendEntity1.setAgencyId(agencyId);
        inkindReportAppendEntity1.setId("D0002");
        inkindReportAppendEntity1.setApproveStatus("DISCARD");
        inkindReportAppendEntityList.add(inkindReportAppendEntity1);

        InkindReportAppendEntity inkindReportAppendEntity2 = new InkindReportAppendEntity();
        inkindReportAppendEntity2.setAgencyId(agencyId);
        inkindReportAppendEntity2.setId("D0003");
        inkindReportAppendEntity2.setApproveStatus("PENDING");
        inkindReportAppendEntity2.setLinkId("L0001");
        inkindReportAppendEntityList.add(inkindReportAppendEntity2);

        // 调用真实方法
        InKindReportApproveDaoImpl inKindReportApproveDaoSpy = spy(inKindReportApproveDaoImpl);
        ReflectionTestUtils.setField(inKindReportApproveDaoSpy, "inKindReportModelMapper", inKindReportModelMapper);
        ReflectionTestUtils.setField(inKindReportApproveDaoSpy, BASE_MAPPER, inKindReportApproveMapper);
        final LambdaUpdateChainWrapper<InKindReportApprove> lambdaUpdate = new LambdaUpdateChainWrapper<>(inKindReportApproveMapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaUpdateChain(inKindReportApproveMapper)).thenReturn(lambdaUpdate);

        // 注入属性
        ReflectionTestUtils.setField(inKindApprovalService, "inKindReportApproveDao", inKindReportApproveDaoSpy);

        // 批量保存签名记录
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.getCurrentUser()).thenReturn(authUserDetails);
        when(inkindDao.getAppendReportByIds(request.getDonateIds())).thenReturn(inkindReportAppendEntityList);
        doNothing().when(inKindReportApproveDaoSpy).batchSave(anyList());

        // 调用接口
        SuccessResponse successResponse = inKindApprovalService.approveDonateInKind(request);

        // 参数校验 -- 记住签名 保存捐赠记录
        ArgumentCaptor<List<InKindReportApprove>> argument = ArgumentCaptor.forClass(List.class);
        verify(inKindReportApproveDaoSpy, times(1)).batchSave(argument.capture());
        List<InKindReportApprove> approvesResp = argument.getValue();
        Assertions.assertEquals(2, approvesResp.size());
        Assertions.assertEquals("D0001", approvesResp.get(0).getReportId());
        Assertions.assertEquals("PENDING", approvesResp.get(0).getFormStatus());
        Assertions.assertEquals("APPROVED", approvesResp.get(0).getToStatus());
        Assertions.assertEquals("append", approvesResp.get(0).getSource());
        Assertions.assertEquals("RATIFY_SIGNATURE", approvesResp.get(0).getType());
        Assertions.assertEquals("U0001", approvesResp.get(0).getUserId());
        Assertions.assertEquals("S0001", approvesResp.get(0).getSignatureId());
        Assertions.assertEquals("A0001", approvesResp.get(0).getAgencyId());
        Assertions.assertEquals("A0001", approvesResp.get(0).getTenantId());

        Assertions.assertEquals("D0003", approvesResp.get(1).getReportId());
        Assertions.assertEquals("PENDING", approvesResp.get(1).getFormStatus());
        Assertions.assertEquals("APPROVED", approvesResp.get(1).getToStatus());
        Assertions.assertEquals("append", approvesResp.get(1).getSource());
        Assertions.assertEquals("APPROVE_SIGNATURE", approvesResp.get(1).getType());
        Assertions.assertEquals("U0001", approvesResp.get(1).getUserId());
        Assertions.assertEquals("S0001", approvesResp.get(1).getSignatureId());
        Assertions.assertEquals("A0001", approvesResp.get(1).getAgencyId());
        Assertions.assertEquals("A0001", approvesResp.get(1).getTenantId());


        Assertions.assertEquals(MSG.t("INKIND_APPROVE_REPEAT_PART", 1, 3), successResponse.getMessage());

        ArgumentCaptor<String> arg1 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> arg2 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> arg3 = ArgumentCaptor.forClass(String.class);
        verify(userDao, times(1)).setMetaData(arg1.capture(), arg2.capture(), arg3.capture());
        Assertions.assertEquals(userId, arg1.getValue());
        Assertions.assertEquals(UserMetaKey.INKIND_SIGNATURE_ID.toString(), arg2.getValue());
        Assertions.assertEquals(request.getApproveSignatureId(), arg3.getValue());

        ArgumentCaptor<List<InkindReportAppendEntity>> arg4 = ArgumentCaptor.forClass(List.class);
        verify(inkindDao, times(1)).batchUpdateDonateAppendReport(arg4.capture());
        Assertions.assertEquals(2, arg4.getValue().size());
        InkindReportAppendEntity inkindReportAppendResp1 = arg4.getValue().get(0);
        Assertions.assertEquals("D0001", inkindReportAppendResp1.getId());
        Assertions.assertEquals("A0001", inkindReportAppendResp1.getAgencyId());
        Assertions.assertEquals("APPROVED", inkindReportAppendResp1.getApproveStatus());
        Assertions.assertEquals("EFFECTIVE", inkindReportAppendResp1.getStatus());
        Assertions.assertNull(inkindReportAppendResp1.getApproveSignatureId());
        Assertions.assertNull(inkindReportAppendResp1.getApproveUserId());
        Assertions.assertNull(inkindReportAppendResp1.getApproveUserName());
        Assertions.assertNull(inkindReportAppendResp1.getLinkId());
        InkindReportAppendEntity inkindReportAppendResp2 = arg4.getValue().get(1);
        Assertions.assertEquals("D0003", inkindReportAppendResp2.getId());
        Assertions.assertEquals("A0001", inkindReportAppendResp2.getAgencyId());
        Assertions.assertEquals("APPROVED", inkindReportAppendResp2.getApproveStatus());
        Assertions.assertEquals("EFFECTIVE", inkindReportAppendResp2.getStatus());
        Assertions.assertEquals("S0001", inkindReportAppendResp2.getApproveSignatureId());
        Assertions.assertEquals("U0001", inkindReportAppendResp2.getApproveUserId());
        Assertions.assertEquals("test user", inkindReportAppendResp2.getApproveUserName());
        Assertions.assertEquals("L0001", inkindReportAppendResp2.getLinkId());
    }

    /**
     * 测试审核报告
     * case: 审核拒绝，不记住签名
     */
    @Test
    void testApproveDonateInKindCase2() {
        // 数据准备 -- 入参
        String userId = "U0001";
        String agencyId = "A0001";

        AuthUserDetails authUserDetails = new AuthUserDetails();
        authUserDetails.setAgencyId(agencyId);

        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        agencyModel.setName("test Agency");

        InKindDonateApproveRequest request2 = new InKindDonateApproveRequest();
        request2.setStatus("DISCARD");
        request2.setApproveSignatureId("S0001");
        request2.setApproveUserId(userId);
        request2.setApproveUserName("test user");
        request2.setDonateIds(Arrays.asList("D0001", "D0002", "D0003"));
        request2.setRememberSignature(false);


        List<InkindReportAppendEntity> inkindReportAppendEntityList = new ArrayList<>();
        InkindReportAppendEntity inkindReportAppendEntity = new InkindReportAppendEntity();
        inkindReportAppendEntity.setAgencyId(agencyId);
        inkindReportAppendEntity.setId("D0001");
        inkindReportAppendEntity.setApproveStatus("PENDING");
        inkindReportAppendEntityList.add(inkindReportAppendEntity);

        InkindReportAppendEntity inkindReportAppendEntity1 = new InkindReportAppendEntity();
        inkindReportAppendEntity1.setAgencyId(agencyId);
        inkindReportAppendEntity1.setId("D0002");
        inkindReportAppendEntity1.setApproveStatus("DISCARD");
        inkindReportAppendEntityList.add(inkindReportAppendEntity1);

        InkindReportAppendEntity inkindReportAppendEntity2 = new InkindReportAppendEntity();
        inkindReportAppendEntity2.setAgencyId(agencyId);
        inkindReportAppendEntity2.setId("D0003");
        inkindReportAppendEntity2.setApproveStatus("PENDING");
        inkindReportAppendEntity2.setLinkId("L0001");
        inkindReportAppendEntityList.add(inkindReportAppendEntity2);

        // 调用真实方法
        InKindReportApproveDaoImpl inKindReportApproveDaoSpy = spy(inKindReportApproveDaoImpl);
        ReflectionTestUtils.setField(inKindReportApproveDaoSpy, "inKindReportModelMapper", inKindReportModelMapper);
        ReflectionTestUtils.setField(inKindReportApproveDaoSpy, BASE_MAPPER, inKindReportApproveMapper);
        final LambdaUpdateChainWrapper<InKindReportApprove> lambdaUpdate = new LambdaUpdateChainWrapper<>(inKindReportApproveMapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaUpdateChain(inKindReportApproveMapper)).thenReturn(lambdaUpdate);

        // 注入属性
        ReflectionTestUtils.setField(inKindApprovalService, "inKindReportApproveDao", inKindReportApproveDaoSpy);

        // 批量保存签名记录
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.getCurrentUser()).thenReturn(authUserDetails);
        when(inkindDao.getAppendReportByIds(request2.getDonateIds())).thenReturn(inkindReportAppendEntityList);
        doNothing().when(inKindReportApproveDaoSpy).batchSave(anyList());

        SuccessResponse successResponse = inKindApprovalService.approveDonateInKind(request2);

        // 参数校验 -- 清除签名 保存捐赠记录
        ArgumentCaptor<List<InKindReportApprove>> argument = ArgumentCaptor.forClass(List.class);
        verify(inKindReportApproveDaoSpy, times(1)).batchSave(argument.capture());
        List<InKindReportApprove> approvesResp = argument.getValue();
        Assertions.assertEquals(1, approvesResp.size());
        Assertions.assertEquals("D0002", approvesResp.get(0).getReportId());
        Assertions.assertEquals("DISCARD", approvesResp.get(0).getFormStatus());
        Assertions.assertEquals("APPROVED", approvesResp.get(0).getToStatus());
        Assertions.assertEquals("append", approvesResp.get(0).getSource());
        Assertions.assertEquals("RATIFY_SIGNATURE", approvesResp.get(0).getType());
        Assertions.assertEquals("U0001", approvesResp.get(0).getUserId());
        Assertions.assertEquals("S0001", approvesResp.get(0).getSignatureId());
        Assertions.assertEquals("A0001", approvesResp.get(0).getAgencyId());
        Assertions.assertEquals("A0001", approvesResp.get(0).getTenantId());

        Assertions.assertEquals(MSG.t("INKIND_APPROVE_REPEAT_PART", 2, 3), successResponse.getMessage());
        ArgumentCaptor<String> argument1 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> argument2 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> argument3 = ArgumentCaptor.forClass(String.class);
        verify(userDao, times(1)).setMetaData(argument1.capture(), argument2.capture(), argument3.capture());
        Assertions.assertEquals(userId, argument1.getValue());
        Assertions.assertEquals(UserMetaKey.INKIND_SIGNATURE_ID.toString(), argument2.getValue());
        Assertions.assertEquals("", argument3.getValue());

        ArgumentCaptor<List<InkindReportAppendEntity>> argument4 = ArgumentCaptor.forClass(List.class);
        verify(inkindDao, times(1)).batchUpdateDonateAppendReport(argument4.capture());
        Assertions.assertEquals(1, argument4.getValue().size());
        InkindReportAppendEntity inkindReportAppendResp3 = argument4.getValue().get(0);
        Assertions.assertEquals("D0002", inkindReportAppendResp3.getId());
        Assertions.assertEquals("A0001", inkindReportAppendResp3.getAgencyId());
        Assertions.assertEquals("APPROVED", inkindReportAppendResp3.getApproveStatus());
        Assertions.assertEquals("EFFECTIVE", inkindReportAppendResp3.getStatus());
        Assertions.assertNull(inkindReportAppendResp3.getApproveSignatureId());
        Assertions.assertNull(inkindReportAppendResp3.getApproveUserId());
        Assertions.assertNull(inkindReportAppendResp3.getApproveUserName());
        Assertions.assertNull(inkindReportAppendResp3.getLinkId());
    }

    /**
     * 测试用例：审核弃置
     */
    @Test
    void discardInKindDonation() {
        // 数据准备 -- 入参
        String userId = "U0001";
        String agencyId = "A0001";

        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        agencyModel.setName("test Agency");

        InKindDonateApproveRequest request = new InKindDonateApproveRequest();
        request.setStatus("PENDING");
        request.setApproveSignatureId("S0001");
        request.setApproveUserId(userId);
        request.setApproveUserName("test user");
        request.setDonateIds(Arrays.asList("D0001"));
        request.setRememberSignature(true);

        InKindDonateApproveRequest request1 = new InKindDonateApproveRequest();
        request1.setStatus("PENDING");
        request1.setApproveSignatureId("S0001");
        request1.setApproveUserId(userId);
        request1.setApproveUserName("test user");
        request1.setDonateIds(Arrays.asList("D0002"));
        request1.setRememberSignature(false);

        InKindDonateApproveRequest request2 = new InKindDonateApproveRequest();
        request2.setStatus("DISCARD");
        request2.setApproveSignatureId("S0001");
        request2.setApproveUserId(userId);
        request2.setApproveUserName("test user");
        request2.setDonateIds(Arrays.asList(""));
        request2.setRememberSignature(false);


        List<InkindReportAppendEntity> inkindReportAppendEntityList = new ArrayList<>();
        InkindReportAppendEntity inkindReportAppendEntity = new InkindReportAppendEntity();
        inkindReportAppendEntity.setAgencyId(agencyId);
        inkindReportAppendEntity.setId("D0001");
        inkindReportAppendEntity.setApproveStatus("PENDING");
        inkindReportAppendEntityList.add(inkindReportAppendEntity);

        List<InkindReportAppendEntity> inkindReportAppendEntityList1 = new ArrayList<>();
        InkindReportAppendEntity inkindReportAppendEntity1 = new InkindReportAppendEntity();
        inkindReportAppendEntity1.setAgencyId(agencyId);
        inkindReportAppendEntity1.setId("D0002");
        inkindReportAppendEntity1.setApproveStatus("PENDING");
        inkindReportAppendEntityList1.add(inkindReportAppendEntity1);

        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(inkindDao.getAppendReportByIds(request.getDonateIds())).thenReturn(inkindReportAppendEntityList);
        when(inkindDao.getAppendReportByIds(request1.getDonateIds())).thenReturn(inkindReportAppendEntityList1);

//        Mockito.when(userDao.setMetaData(userId, "signatureId", request.getApproveSignatureId())).thenReturn(1);
        // 数据准备 -- 接口模拟
        inKindApprovalService.discardDonateInKind(request);
        inKindApprovalService.discardDonateInKind(request1);
    }

    /**
     * 测试审核设置草稿(审核通过或忽略的 In-kind，但未签名)
     * case: 用户角色为机构管理员，审核模式为（教师批准签名和管理员批准）TEACHER_APPROVE_SIGNATURE_AND_ADMIN_RATIFY
     * 报告审核状态为（审核通过）APPROVED
     */
    @Test
    void testSetInkindDraftV2Case1() {
        // 模拟数据
        String enrollmentId = "E0001";
        String groupId = "G0001";
        String userId = "U0001";
        String agencyId = "A0001";
        String role = "AGENCY_ADMIN";
        String approveMode = InKindApproveMode.TEACHER_APPROVE_SIGNATURE_AND_ADMIN_RATIFY.toString();
        AgencyMetadata metadata = new AgencyMetadata();
        metadata.setMetaValue(approveMode);
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);

        EnrollmentEntity enrollment = new EnrollmentEntity();
        enrollment.setId(enrollmentId);

        InkindRequest request = new InkindRequest();
        request.setEnrollmentId(enrollmentId);
        request.setGroupId(groupId);
        List<InkindBaseModel> inkindBaseModels = new ArrayList<>();
        InkindBaseModel inkindBaseModel = new InkindBaseModel();
        inkindBaseModel.setId("I0001");
        inkindBaseModel.setApproveStatus(InkindApproveStatus.APPROVED.toString());
        inkindBaseModels.add(inkindBaseModel);
        request.setInkinds(inkindBaseModels);

        AuthUserDetails authUserDetails = new AuthUserDetails();
        authUserDetails.setAgencyId(agencyId);
        authUserDetails.setRole(role);
        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole(role);
        UserModel user = new UserModel();
        user.setId(userId);
        user.setRole(role);

        // 调用真实方法
        InKindReportApproveDaoImpl inKindReportApproveDaoSpy = spy(inKindReportApproveDaoImpl);

        ReflectionTestUtils.setField(inKindReportApproveDaoSpy, "inKindReportModelMapper", inKindReportModelMapper);
        ReflectionTestUtils.setField(inKindReportApproveDaoSpy, BASE_MAPPER, inKindReportApproveMapper);
        final LambdaUpdateChainWrapper<InKindReportApprove> lambdaUpdate = new LambdaUpdateChainWrapper<>(inKindReportApproveMapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaUpdateChain(inKindReportApproveMapper)).thenReturn(lambdaUpdate);

        // 注入属性
        ReflectionTestUtils.setField(inKindApprovalService, "inKindReportApproveDao", inKindReportApproveDaoSpy);

        // 接口模拟
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        // Mockito.when(userProvider.checkUser(userId)).thenReturn(userEntity);
        // Mockito.when(userDao.getAgencyByAgencyAdminId(user.getId())).thenReturn(Arrays.asList(agencyModel));
        when(userProvider.getCurrentUser()).thenReturn(authUserDetails);
        when(studentDao.getChildWithGroupCenter(enrollmentId)).thenReturn(enrollment);
        // when(agencyMetadataMapper.get(anyString(), anyString())).thenReturn(metadata);
        when(inKindProvider.getApproveMode(agencyId)).thenReturn(InKindApproveMode.TEACHER_APPROVE_SIGNATURE_AND_ADMIN_RATIFY);
        doNothing().when(inkindDao).batchUpdateDraftStatusV2(anyList());
        doNothing().when(inKindReportApproveDaoSpy).batchSave(anyList());
        when(userDao.getUserById(any())).thenReturn(user);


        // 调用方法
        InkindResponse response = inKindApprovalService.setInKindDraft(request, userId);

        // 结果校验
        List<MapModel> mapModel = response.getData();
        // Assertions.assertEquals(mapModel.getKey(), "inkindApproveMode");
        // Assertions.assertEquals(mapModel.getValue(), approveMode);

        ArgumentCaptor<List> arg1 = ArgumentCaptor.forClass(List.class);
        verify(inkindDao, times(1)).batchUpdateDraftStatusV2(arg1.capture());
        List<InkindBaseModel> inkindBaseModels1 = arg1.getValue();
        Assertions.assertEquals(1, inkindBaseModels1.size());
        Assertions.assertEquals("I0001", inkindBaseModels1.get(0).getId());
        Assertions.assertEquals("APPROVED", inkindBaseModels1.get(0).getDraftStatus());
        Assertions.assertEquals("U0001", inkindBaseModels1.get(0).getApproveUserId());
        Assertions.assertEquals("APPROVE", inkindBaseModels1.get(0).getStatus());

        ArgumentCaptor<List> arg2 = ArgumentCaptor.forClass(List.class);
        verify(inKindReportApproveDaoSpy, times(1)).batchSave(arg2.capture());
        List<InKindReportApprove> inKindReportApproves = arg2.getValue();
        Assertions.assertEquals(1, inKindReportApproves.size());
        Assertions.assertEquals("I0001", inKindReportApproves.get(0).getReportId());
        Assertions.assertEquals("PENDING", inKindReportApproves.get(0).getFormStatus());
        Assertions.assertEquals("APPROVED", inKindReportApproves.get(0).getToStatus());
        Assertions.assertEquals("base", inKindReportApproves.get(0).getSource());
        Assertions.assertEquals("APPROVE_REPORT", inKindReportApproves.get(0).getType());
        Assertions.assertEquals(agencyId, inKindReportApproves.get(0).getAgencyId());
        Assertions.assertEquals(agencyId, inKindReportApproves.get(0).getTenantId());
    }

    /**
     * 测试审核设置草稿(审核通过或忽略的 In-kind，但未签名)
     * case: 用户角色为老师，审核模式为（教师批准和管理员批准）TEACHER_APPROVE_AND_ADMIN_RATIFY
     * 报告审核状态为（审核通过）IGNORE
     */
    @Test
    void testSetInkindDraftV2Case2() {
        // 模拟数据
        String enrollmentId = "E0001";
        String groupId = "G0001";
        String userId = "U0001";
        String agencyId = "A0001";
        String role = "TEACHING_ASSISTANT";
        String approveMode = TEACHER_APPROVE_AND_ADMIN_RATIFY.toString();
        AgencyMetadata metadata = new AgencyMetadata();
        metadata.setMetaValue(approveMode);
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);

        EnrollmentEntity enrollment = new EnrollmentEntity();
        enrollment.setId(enrollmentId);

        InkindRequest request = new InkindRequest();
        request.setEnrollmentId(enrollmentId);
        request.setGroupId(groupId);
        List<InkindBaseModel> inkindBaseModels = new ArrayList<>();
        InkindBaseModel inkindBaseModel = new InkindBaseModel();
        inkindBaseModel.setId("I0001");
        inkindBaseModel.setApproveStatus(InkindApproveStatus.IGNORE.toString());
        inkindBaseModels.add(inkindBaseModel);
        request.setInkinds(inkindBaseModels);

        AuthUserDetails authUserDetails = new AuthUserDetails();
        authUserDetails.setAgencyId(agencyId);
        authUserDetails.setRole(role);
        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole(role);
        UserModel user = new UserModel();
        user.setId(userId);
        user.setRole(role);

        Date date1 = TimeUtil.parse("2023-01-01", TimeUtil.format10);
        Date date2 = TimeUtil.parse("2020-01-01", TimeUtil.format10);
        List<InkindSchoolYearEntity> schoolYears = new ArrayList<>();
        InkindSchoolYearEntity schoolYearEntity = new InkindSchoolYearEntity();
        schoolYearEntity.setStartDate(date1);
        schoolYearEntity.setShowRate(false);
        InkindSchoolYearEntity historySchoolYear = new InkindSchoolYearEntity();
        historySchoolYear.setStartDate(date2);
        historySchoolYear.setShowRate(false);
        schoolYears.add(schoolYearEntity);
        schoolYears.add(historySchoolYear);

        // 一共五种角色, 审批的报告也对应五种角色
        List<ReportReviewModel> reviewModels = new ArrayList<>();
        ReportReviewModel reviewModel1 = new ReportReviewModel();
        reviewModel1.setActivityTypeId("A0001");
        reviewModel1.setApproveRole(ApproveRole.ADMINS.toString());
        // 当前角色的groupId
        reviewModel1.setGroupId(groupId);
        reviewModels.add(reviewModel1);
        ReportReviewModel reviewModel2 = new ReportReviewModel();
        reviewModel2.setActivityTypeId("A0002");
        reviewModel2.setApproveRole(ApproveRole.ADMINS.toString());
        reviewModel2.setGroupId("S0001");
        reviewModels.add(reviewModel2);
        ReportReviewModel reviewModel3 = new ReportReviewModel();
        reviewModel3.setActivityTypeId(groupId);
        reviewModel3.setApproveRole(ApproveRole.TEACHER_ASSISTANTS.toString());
        reviewModel3.setGroupId(groupId);
        reviewModels.add(reviewModel3);
        ReportReviewModel reviewModel4 = new ReportReviewModel();
        reviewModel4.setActivityTypeId("A0004");
        reviewModel4.setApproveRole(ApproveRole.TEACHER_ASSISTANTS.toString());
        reviewModel4.setGroupId(groupId);
        reviewModels.add(reviewModel4);
        ReportReviewModel reviewModel5 = new ReportReviewModel();
        reviewModel5.setActivityTypeId("A0005");
        reviewModel5.setApproveRole(ApproveRole.FAMILY_SERVICE_STAFF.toString());
        reviewModel5.setGroupId("F0001");
        reviewModels.add(reviewModel5);

        // 调用真实方法
        InKindReportApproveDaoImpl inKindReportApproveDaoSpy = spy(inKindReportApproveDaoImpl);

        ReflectionTestUtils.setField(inKindReportApproveDaoSpy, "inKindReportModelMapper", inKindReportModelMapper);
        ReflectionTestUtils.setField(inKindReportApproveDaoSpy, BASE_MAPPER, inKindReportApproveMapper);
        final LambdaUpdateChainWrapper<InKindReportApprove> lambdaUpdate = new LambdaUpdateChainWrapper<>(inKindReportApproveMapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaUpdateChain(inKindReportApproveMapper)).thenReturn(lambdaUpdate);

        // 注入属性
        ReflectionTestUtils.setField(inKindApprovalService, "inKindReportApproveDao", inKindReportApproveDaoSpy);

        // mockGetAgencyId(userEntity, agencyId);
        // 接口模拟
        when(userProvider.getCurrentUser()).thenReturn(authUserDetails);
        // when(inkindDao.getSchoolYearByAgencyId(agencyId)).thenReturn(schoolYears);
        // when(inkindDao.getReviewBySchoolYearId(any())).thenReturn(reviewModels);
        when(studentDao.getChildWithGroupCenter(enrollmentId)).thenReturn(enrollment);
        when(inKindProvider.checkReview(userId, request.getGroupId())).thenReturn(true);
        // when(agencyMetadataMapper.get(anyString(), anyString())).thenReturn(metadata);
        when(inKindProvider.getApproveMode(agencyId)).thenReturn(TEACHER_APPROVE_AND_ADMIN_RATIFY);

        doNothing().when(inkindDao).batchUpdateDraftStatusV2(anyList());
        doNothing().when(inKindReportApproveDaoSpy).batchSave(anyList());
        when(userDao.getUserById(any())).thenReturn(user);
        // 调用方法
        InkindResponse response = inKindApprovalService.setInKindDraft(request, userId);

        // 结果校验
        List<MapModel> mapModel = response.getData();
        // Assertions.assertEquals(mapModel.getKey(), "inkindApproveMode");
        // Assertions.assertEquals(mapModel.getValue(), approveMode);

        ArgumentCaptor<List> arg1 = ArgumentCaptor.forClass(List.class);
        verify(inkindDao, times(1)).batchUpdateDraftStatusV2(arg1.capture());
        List<InkindBaseModel> inkindBaseModels1 = arg1.getValue();
        Assertions.assertEquals(1, inkindBaseModels1.size());
        Assertions.assertEquals("I0001", inkindBaseModels1.get(0).getId());
        Assertions.assertEquals("IGNORE", inkindBaseModels1.get(0).getDraftStatus());
        Assertions.assertEquals("U0001", inkindBaseModels1.get(0).getApproveUserId());
        Assertions.assertEquals("EFFECTIVE", inkindBaseModels1.get(0).getStatus());

        ArgumentCaptor<List> arg2 = ArgumentCaptor.forClass(List.class);
        verify(inKindReportApproveDaoSpy, times(1)).batchSave(arg2.capture());
        List<InKindReportApprove> inKindReportApproves = arg2.getValue();
        Assertions.assertEquals(1, inKindReportApproves.size());
        Assertions.assertEquals("I0001", inKindReportApproves.get(0).getReportId());
        Assertions.assertEquals("PENDING", inKindReportApproves.get(0).getFormStatus());
        Assertions.assertEquals("IGNORE", inKindReportApproves.get(0).getToStatus());
        Assertions.assertEquals("base", inKindReportApproves.get(0).getSource());
        Assertions.assertEquals("APPROVE_REPORT", inKindReportApproves.get(0).getType());
        Assertions.assertEquals(agencyId, inKindReportApproves.get(0).getAgencyId());
        Assertions.assertEquals(agencyId, inKindReportApproves.get(0).getTenantId());
    }

    /**
     * 测试审核设置草稿(审核通过或忽略的 In-kind，但未签名)
     * case: 用户角色为老师，审核模式为（教师批准签字）TEACHER_APPROVE_SINGLE
     * 报告审核状态为（审核通过）APPROVED
     */
    @Test
    void testSetInkindDraftV2Case3() {
        // 模拟数据
        String enrollmentId = "E0001";
        String groupId = "G0001";
        String userId = "U0001";
        String agencyId = "A0001";
        String role = "TEACHING_ASSISTANT";
        String approveMode = InKindApproveMode.TEACHER_APPROVE_SINGLE.toString();
        AgencyMetadata metadata = new AgencyMetadata();
        metadata.setMetaValue(approveMode);
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);

        EnrollmentEntity enrollment = new EnrollmentEntity();
        enrollment.setId(enrollmentId);

        InkindRequest request = new InkindRequest();
        request.setEnrollmentId(enrollmentId);
        request.setGroupId(groupId);
        List<InkindBaseModel> inkindBaseModels = new ArrayList<>();
        InkindBaseModel inkindBaseModel = new InkindBaseModel();
        inkindBaseModel.setId("I0001");
        inkindBaseModel.setApproveStatus(InkindApproveStatus.APPROVED.toString());
        inkindBaseModels.add(inkindBaseModel);
        request.setInkinds(inkindBaseModels);
        inkindBaseModel.setDraftStatus("APPROVED");
        inkindBaseModel.setApproveUserId("U0001");
        inkindBaseModel.setStatus("APPROVE");
        AuthUserDetails authUserDetails = new AuthUserDetails();
        authUserDetails.setAgencyId(agencyId);
        authUserDetails.setRole(role);
        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole(role);
        UserModel user = new UserModel();
        user.setId(userId);
        user.setRole(role);

        Date date1 = TimeUtil.parse("2023-01-01", TimeUtil.format10);
        Date date2 = TimeUtil.parse("2020-01-01", TimeUtil.format10);
        List<InkindSchoolYearEntity> schoolYears = new ArrayList<>();
        InkindSchoolYearEntity schoolYearEntity = new InkindSchoolYearEntity();
        schoolYearEntity.setStartDate(date1);
        schoolYearEntity.setShowRate(true);
        InkindSchoolYearEntity historySchoolYear = new InkindSchoolYearEntity();
        historySchoolYear.setStartDate(date2);
        historySchoolYear.setShowRate(true);
        schoolYears.add(schoolYearEntity);
        schoolYears.add(historySchoolYear);

        // 一共五种角色, 审批的报告也对应五种角色
        List<ReportReviewModel> reviewModels = new ArrayList<>();
        ReportReviewModel reviewModel1 = new ReportReviewModel();
        reviewModel1.setActivityTypeId("A0001");
        reviewModel1.setApproveRole(ApproveRole.ADMINS.toString());
        // 当前角色的groupId
        reviewModel1.setGroupId(groupId);
        reviewModels.add(reviewModel1);
        ReportReviewModel reviewModel2 = new ReportReviewModel();
        reviewModel2.setActivityTypeId("A0002");
        reviewModel2.setApproveRole(ApproveRole.ADMINS.toString());
        reviewModel2.setGroupId("S0001");
        reviewModels.add(reviewModel2);
        ReportReviewModel reviewModel3 = new ReportReviewModel();
        reviewModel3.setActivityTypeId(groupId);
        reviewModel3.setApproveRole(ApproveRole.TEACHER_ASSISTANTS.toString());
        reviewModel3.setGroupId(groupId);
        reviewModels.add(reviewModel3);
        ReportReviewModel reviewModel4 = new ReportReviewModel();
        reviewModel4.setActivityTypeId("A0004");
        reviewModel4.setApproveRole(ApproveRole.TEACHER_ASSISTANTS.toString());
        reviewModel4.setGroupId(groupId);
        reviewModels.add(reviewModel4);
        ReportReviewModel reviewModel5 = new ReportReviewModel();
        reviewModel5.setActivityTypeId("A0005");
        reviewModel5.setApproveRole(ApproveRole.FAMILY_SERVICE_STAFF.toString());
        reviewModel5.setGroupId("F0001");
        reviewModels.add(reviewModel5);

        // 调用真实方法
        InKindReportApproveDaoImpl inKindReportApproveDaoSpy = spy(inKindReportApproveDaoImpl);
        ReflectionTestUtils.setField(inKindReportApproveDaoSpy, "inKindReportModelMapper", inKindReportModelMapper);
        ReflectionTestUtils.setField(inKindReportApproveDaoSpy, BASE_MAPPER, inKindReportApproveMapper);
        final LambdaUpdateChainWrapper<InKindReportApprove> lambdaUpdate = new LambdaUpdateChainWrapper<>(inKindReportApproveMapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaUpdateChain(inKindReportApproveMapper)).thenReturn(lambdaUpdate);

        // 注入属性
        ReflectionTestUtils.setField(inKindApprovalService, "inKindReportApproveDao", inKindReportApproveDaoSpy);

        // mockGetAgencyId(userEntity, agencyId);
        // 接口模拟
        when(userProvider.getCurrentUser()).thenReturn(authUserDetails);
        // when(inkindDao.getSchoolYearByAgencyId(agencyId)).thenReturn(schoolYears);
        // when(inkindDao.getReviewBySchoolYearId(any())).thenReturn(reviewModels);
        when(studentDao.getChildWithGroupCenter(enrollmentId)).thenReturn(enrollment);
        when(inKindProvider.checkReview(userId, request.getGroupId())).thenReturn(true);
        // when(agencyMetadataMapper.get(anyString(), anyString())).thenReturn(metadata);
        when(inKindProvider.getApproveMode(agencyId)).thenReturn(InKindApproveMode.TEACHER_APPROVE_SINGLE);
        doNothing().when(inkindDao).batchUpdateDraftStatusV2(anyList());
        doNothing().when(inKindReportApproveDaoSpy).batchSave(anyList());
        when(userDao.getUserById(any())).thenReturn(user);

        // 调用方法
        InkindResponse response = inKindApprovalService.setInKindDraft(request, userId);

        // 结果校验
        List<MapModel> mapModel = response.getData();
        // Assertions.assertEquals("inkindApproveMode", mapModel.getKey());
        // Assertions.assertEquals(approveMode, mapModel.getValue());

        ArgumentCaptor<List> arg1 = ArgumentCaptor.forClass(List.class);
        verify(inkindDao, times(1)).batchUpdateDraftStatusV2(arg1.capture());
        List<InkindBaseModel> inkindBaseModels1 = arg1.getValue();
        Assertions.assertEquals(1, inkindBaseModels1.size());
        Assertions.assertEquals("I0001", inkindBaseModels1.get(0).getId());
        Assertions.assertEquals("APPROVED", inkindBaseModels1.get(0).getDraftStatus());
        Assertions.assertEquals("U0001", inkindBaseModels1.get(0).getApproveUserId());
        Assertions.assertEquals("APPROVE", inkindBaseModels1.get(0).getStatus());

        ArgumentCaptor<List> arg2 = ArgumentCaptor.forClass(List.class);
        verify(inKindReportApproveDaoSpy, times(1)).batchSave(arg2.capture());
        List<InKindReportApprove> inKindReportApproves = arg2.getValue();
        Assertions.assertEquals(1, inKindReportApproves.size());
        Assertions.assertEquals("I0001", inKindReportApproves.get(0).getReportId());
        Assertions.assertEquals("PENDING", inKindReportApproves.get(0).getFormStatus());
        Assertions.assertEquals("APPROVED", inKindReportApproves.get(0).getToStatus());
        Assertions.assertEquals("base", inKindReportApproves.get(0).getSource());
        Assertions.assertEquals("APPROVE_REPORT", inKindReportApproves.get(0).getType());
        Assertions.assertEquals(agencyId, inKindReportApproves.get(0).getAgencyId());
        Assertions.assertEquals(agencyId, inKindReportApproves.get(0).getTenantId());
    }


    /**
     * 测试审核拒绝捐赠
     */
    @Test
    void testReviseDonate() {
        // 参数准备
        InKindDonateApproveRequest request = new InKindDonateApproveRequest();
        List<String> donateIds = new ArrayList<>();
        donateIds.add("D0001");
        donateIds.add("D0002");
        donateIds.add("D0003");
        // 被拒绝的报告
        List<InKindReportModel> reportModels = new ArrayList<>();
        InKindReportModel inkindReportModel1 = new InKindReportModel();
        inkindReportModel1.setId("I0001");
        inkindReportModel1.setApproveComment("comment");
        reportModels.add(inkindReportModel1);
        request.setDonateIds(donateIds);
        request.setReportModels(reportModels);

        List<InkindReportAppendEntity> appendReports = new ArrayList<>();
        InkindReportAppendEntity appendReport1 = new InkindReportAppendEntity();
        appendReport1.setId("A0001");
        appendReport1.setLinkId("D0001");
        appendReport1.setApproveStatus("PENDING");
        appendReports.add(appendReport1);
        InkindReportAppendEntity appendReport2 = new InkindReportAppendEntity();
        appendReport2.setId("A0002");
        appendReport2.setLinkId("");
        appendReport2.setApproveStatus("PENDING");
        appendReports.add(appendReport2);

        // 接口模拟
        when(inkindDao.getAppendReportByIds(request.getDonateIds())).thenReturn(appendReports);
        doNothing().when(inkindDao).batchUpdateDonateDiscardAppendReport(anyList());

        // 调用方法
        SuccessResponse response = inKindApprovalService.reviseDonate(request);

        Assertions.assertEquals(MSG.t("INKIND_APPROVE_REPEAT_PART", 1, donateIds.size()), response.getMessage());

        ArgumentCaptor<List> arg1 = ArgumentCaptor.forClass(List.class);
        verify(inkindDao, times(1)).batchUpdateDonateDiscardAppendReport(arg1.capture());
        List<InkindReportAppendEntity> inkindReportAppends = arg1.getValue();
        Assertions.assertEquals(2, inkindReportAppends.size());
        InkindReportAppendEntity inkindReportAppendEntity1 = inkindReportAppends.get(0);
        Assertions.assertEquals("A0001", inkindReportAppendEntity1.getId());
        Assertions.assertEquals("D0001", inkindReportAppendEntity1.getLinkId());
        Assertions.assertEquals("DISCARD", inkindReportAppendEntity1.getApproveStatus());
        Assertions.assertEquals("RATIFY", inkindReportAppendEntity1.getStatus());
        InkindReportAppendEntity inkindReportAppendEntity2 = inkindReportAppends.get(1);
        Assertions.assertEquals("A0002", inkindReportAppendEntity2.getId());
        Assertions.assertTrue(StringUtil.isEmptyOrBlank(inkindReportAppendEntity2.getLinkId()));
        Assertions.assertEquals("REJECTED", inkindReportAppendEntity2.getApproveStatus());
        Assertions.assertEquals("APPROVE", inkindReportAppendEntity2.getStatus());
    }


    /**
     * Web 端提交审核通过或忽略的 In-kind
     * <p>
     * case1: 管理员在（教师批准和管理员批准）TEACHER_APPROVE_AND_ADMIN_RATIFY 的审核模式下提交不是忽略的报告
     */
    @Test
    void testApprovalInkindWeb() {
        // 准备数据
        String userId = "U0001";
        String agencyId = "A0001";
        String role = "AGENCY_ADMIN";
        AgencyMetadata metadata = new AgencyMetadata();
        metadata.setMetaValue("TEACHER_APPROVE_AND_ADMIN_RATIFY");
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);

        InkindRequest request = new InkindRequest();
        String approveSignatureId = "S0001";
        request.setApproveSignatureId(approveSignatureId);
        String inkind1 = "I0001";
        request.setInkindIds(Collections.singletonList(inkind1));
        request.setRememberSignature(true);

        UserModel user = new UserModel();
        user.setId(userId);
        user.setRole(role);
        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole(role);
        AuthUserDetails authUserDetails = new AuthUserDetails();
        authUserDetails.setAgencyId(agencyId);

        List<InkindReportEntity> inkindBases = new ArrayList<>();
        InkindReportEntity inkindBase = new InkindReportEntity();
        inkindBase.setId(inkind1);
        inkindBase.setActivityTypeId("T0001");
        inkindBase.setGroupId("G0001");
        inkindBase.setEnrollmentId("E0001");
        inkindBase.setApproveStatus("APPROVED");
        inkindBase.setUnit("HOUR");
        inkindBase.setValue(BigDecimal.valueOf(10));
        inkindBase.setRateUnit("HOUR");
        inkindBase.setRateValue(BigDecimal.valueOf(10));
        inkindBase.setActivityGroupId("G0001");
        inkindBases.add(inkindBase);

        List<InkindActivityGroupEntity> activityGroups = new ArrayList<>();
        InkindActivityGroupEntity activityGroup = new InkindActivityGroupEntity();
        activityGroup.setLimitValue(true);
        activityGroup.setUnit("HOUR");
        activityGroup.setId("G0001");
        activityGroup.setValue(BigDecimal.valueOf(20));
        activityGroups.add(activityGroup);

        // 调用真实方法
        InKindReportApproveDaoImpl inKindReportApproveDaoSpy = spy(inKindReportApproveDaoImpl);
        ReflectionTestUtils.setField(inKindReportApproveDaoSpy, "inKindReportModelMapper", inKindReportModelMapper);
        ReflectionTestUtils.setField(inKindReportApproveDaoSpy, BASE_MAPPER, inKindReportApproveMapper);
        final LambdaUpdateChainWrapper<InKindReportApprove> lambdaUpdate = new LambdaUpdateChainWrapper<>(inKindReportApproveMapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaUpdateChain(inKindReportApproveMapper)).thenReturn(lambdaUpdate);

        // 注入属性
        ReflectionTestUtils.setField(inKindApprovalService, "inKindReportApproveDao", inKindReportApproveDaoSpy);

        // 模拟接口
        when(userProvider.getCurrentUser()).thenReturn(authUserDetails);
        when(userDao.getUserById(anyString())).thenReturn(user);
        when(inkindDao.getDraftReportByIds(anyList(), anyString())).thenReturn(inkindBases);
        // when(agencyMetadataMapper.get(any(), any())).thenReturn(metadata);
        when(inKindProvider.getApproveMode(agencyId)).thenReturn(TEACHER_APPROVE_AND_ADMIN_RATIFY);

        doNothing().when(inkindDao).batchUpdateReportApprove(anyList());
        doNothing().when(inKindReportApproveDaoSpy).batchSave(anyList());
        doNothing().when(userDao).setMetaData(userId, "INKIND_SIGNATURE_ID", request.getApproveSignatureId());
        when(userProvider.getCurrentUserId()).thenReturn(userId);

        // 调用方法
        InkindResponse response = inKindApprovalService.approvalInkindWeb(request, userId);

        // 验证结果
        ArgumentCaptor<List<InkindReportEntity>> arg1 = ArgumentCaptor.forClass(List.class);
        verify(inkindDao, times(1)).batchUpdateReportApprove(arg1.capture());
        List<InkindReportEntity> singnedInkinds = arg1.getValue();
        Assertions.assertEquals(1, singnedInkinds.size());
        Assertions.assertEquals("I0001", singnedInkinds.get(0).getId());
        Assertions.assertEquals("T0001", singnedInkinds.get(0).getActivityTypeId());
        Assertions.assertEquals("G0001", singnedInkinds.get(0).getActivityGroupId());
        Assertions.assertEquals("E0001", singnedInkinds.get(0).getEnrollmentId());
        Assertions.assertEquals("G0001", singnedInkinds.get(0).getGroupId());
        Assertions.assertEquals("10", singnedInkinds.get(0).getValue().toString());
        Assertions.assertEquals("APPROVED", singnedInkinds.get(0).getApproveStatus());
        Assertions.assertEquals("EFFECTIVE", singnedInkinds.get(0).getStatus());

        ArgumentCaptor<List<InKindReportApprove>> arg = ArgumentCaptor.forClass(List.class);
        verify(inKindReportApproveDaoSpy, times(1)).batchSave(arg.capture());
        List<InKindReportApprove> approvesResp = arg.getValue();
        Assertions.assertEquals(1, approvesResp.size());
        Assertions.assertEquals("I0001", approvesResp.get(0).getReportId());
        Assertions.assertEquals("APPROVED", approvesResp.get(0).getFormStatus());
        Assertions.assertEquals("APPROVED", approvesResp.get(0).getToStatus());
        Assertions.assertEquals("base", approvesResp.get(0).getSource());
        Assertions.assertEquals("APPROVE_EFFECTIVE_SIGNATURE", approvesResp.get(0).getType());
        Assertions.assertEquals("S0001", approvesResp.get(0).getSignatureId());
        Assertions.assertEquals("A0001", approvesResp.get(0).getAgencyId());
        Assertions.assertEquals("A0001", approvesResp.get(0).getTenantId());

        List<MapModel> data = response.getData();
        Assertions.assertEquals(4, data.size());
        Assertions.assertEquals("Children", data.get(0).getKey());
        Assertions.assertEquals("1", data.get(0).getValue());
        Assertions.assertEquals("Activities", data.get(1).getKey());
        Assertions.assertEquals("1", data.get(1).getValue());
        Assertions.assertEquals("Total Time", data.get(2).getKey());
        Assertions.assertEquals("10.0 hrs", data.get(2).getValue());
        Assertions.assertEquals("Total Mileage", data.get(3).getKey());
        Assertions.assertEquals("0 mile", data.get(3).getValue());
        Assertions.assertEquals("Success!", response.getMessageTitle());
        Assertions.assertEquals("Thanks for your hard work!\n" + "You have finished reviewing the In-Kind activities.", response.getMessage());
    }

    /**
     * mock 获取机构 id 的方法
     *
     * @param user 用户
     * @param agencyId 机构 id
     */
    private void mockGetAgencyId(UserEntity user, String agencyId) {
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);
        String userId = user.getId();
        when(inKindProvider.getAgencyId()).thenReturn(agencyId);
    }

    /**
     * 测试教师端红点提示
     * case1: 用户角色为管理员
     */
    @Test
    void testGetEducatorPointByAdmin() {
        // 准备数据
        String timeZone = "8";
        String date = "2023-04-25";
        String userId = "U0001";
        String role = "AGENCY_ADMIN";
        String agencyId = "A0001";

        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole(role);

        List<InkindSchoolYearEntity> schoolYears = new ArrayList<>();
        InkindSchoolYearEntity schoolYearEntity = new InkindSchoolYearEntity();
        schoolYearEntity.setStartDate(TimeUtil.parseDate("2020-01-04"));
        schoolYearEntity.setShowRate(true);
        schoolYears.add(schoolYearEntity);

        AuthUserDetails authUserDetails = new AuthUserDetails();
        authUserDetails.setAgencyId(agencyId);
        authUserDetails.setRole(role);
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);

        AgencyMetaDataEntity metaDataEntity = new AgencyMetaDataEntity();
        metaDataEntity.setMetaValue("1");

        List<CenterModel> centerModels = new LinkedList<>();
        CenterModel center1 = new CenterModel();
        center1.setGroupId("G0001");
        centerModels.add(center1);
        CenterModel center2 = new CenterModel();
        center2.setGroupId("G0002");
        centerModels.add(center2);

        List<InKindsGoalSchoolYear> inKindsGoalSchoolYears = new ArrayList<>();
        InKindsGoalSchoolYear year = new InKindsGoalSchoolYear();
        year.setSchoolYear("2022-2023");
        inKindsGoalSchoolYears.add(year);

        List<InkindReportEntity> reports = new ArrayList<>();
        InkindReportEntity report = new InkindReportEntity();
        report.setId("I0001");
        report.setApproveStatus("APPROVED");
        reports.add(report);

        // 调用真实方法
        InKindsGoalSchoolYearDaoImpl inKindsGoalSchoolYearDaoSpy = spy(inKindsGoalSchoolYearDaoImpl);
        ReflectionTestUtils.setField(inKindsGoalSchoolYearDaoSpy, BASE_MAPPER, inKindsGoalSchoolYearMapper);
        final LambdaQueryChainWrapper<InKindsGoalSchoolYear> lambdaQuery = new LambdaQueryChainWrapper<>(inKindsGoalSchoolYearMapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain((inKindsGoalSchoolYearMapper))).thenReturn(lambdaQuery);

        // 注入属性
        ReflectionTestUtils.setField(inKindApprovalService, "inKindsGoalSchoolYearDao", inKindsGoalSchoolYearDaoSpy);

        // 调用 getAgencyId() 的 mock 方法
        mockGetAgencyId(userEntity, agencyId);

        // 接口模拟
        when(userProvider.getCurrentUser()).thenReturn(authUserDetails);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        // when(agencyDao.getMeta(agencyId, "INKIND_OPEN")).thenReturn(metaDataEntity);
        when(inkindDao.getSchoolYearByAgencyId(agencyId)).thenReturn(schoolYears);
        when(centerDao.getCenterAndGroupsByAgencyUserId(userId)).thenReturn(centerModels);
        when(inkindDao.isExistReportByGroupIds(Arrays.asList(StringUtil.split("g0001,g0002")), InkindApproveStatus.PENDING, InKindStageEnum.APPROVE)).thenReturn(true);
        when(inKindsGoalSchoolYearDaoSpy.lambdaQuery().eq(InKindsGoalSchoolYear::getAgencyId, agencyId).eq(InKindsGoalSchoolYear::getStep, InKindGoalSchoolYearStep.STEP3_GOAL_COMPLETE.getValue()).count()).thenReturn(1L);
        // userDao.getMetaData(userId, UserMetaKey.INKIND_USER_ATTR_STATUS.toString());
        when(userDao.getMetaData(userId, UserMetaKey.INKIND_USER_ATTR_STATUS.toString())).thenReturn(null);
        when(inKindReportApproveDao.isExistNeedRatifyByAgencyId(any(), any())).thenReturn(true);
        when(inkindDao.getDonateAppendReportCountByAgencyId(any(), any(), any())).thenReturn(1);
        // 获取当前学年
        Mockito.when(inKindProvider.getCurrentSchoolYear(any())).thenReturn(schoolYearEntity);
        Mockito.when(inKindProvider.getAgencyOpen(agencyId, "INKIND_OPEN")).thenReturn(true);
        // 调用方法
        InkindPointResponse point = inKindApprovalService.getEducatorPoint(date, userId, timeZone);

        // 验证结果
        Assertions.assertEquals(true, point.getHasPoint());
        Assertions.assertEquals(true, point.getInkindDisplayStatus());
        Assertions.assertEquals(true, point.isInkindGoalSettingComplete());
        Assertions.assertEquals(true, point.isInkindOpenStatus());
        InkindUserAttrModel status = point.getInkindUserStatus();
        Assertions.assertEquals(true, status.getNewStatus());
        Assertions.assertEquals(true, status.getFristSetting());
        Assertions.assertEquals(true, status.getFristWelcome());
        Assertions.assertEquals(true, status.getFristSignature());
        Assertions.assertEquals(true, status.getFristCenterSync());
        Assertions.assertEquals(false, status.getFirstGuide());
        Assertions.assertEquals(false, status.getGuiding());
    }

    /**
     * 测试教师端红点提示
     * case2: 用户角色为老师
     */
    @Test
    void testGetEducatorPointByTeacher() {
        // 准备数据
        String timeZone = "8";
        String date = "2023-04-25";
        String userId = "U0001";
        String role = "TEACHING_ASSISTANT";
        String agencyId = "A0001";

        UserEntity userEntity = new UserEntity();
        userEntity.setId(userId);
        userEntity.setRole(role);

        List<InkindSchoolYearEntity> schoolYears = new ArrayList<>();
        InkindSchoolYearEntity schoolYearEntity = new InkindSchoolYearEntity();
        schoolYearEntity.setStartDate(TimeUtil.parseDate("2020-01-03"));
        schoolYearEntity.setShowRate(true);
        schoolYears.add(schoolYearEntity);

        AuthUserDetails authUserDetails = new AuthUserDetails();
        authUserDetails.setAgencyId(agencyId);
        authUserDetails.setRole(role);
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId(agencyId);

        AgencyMetaDataEntity metaDataEntity = new AgencyMetaDataEntity();
        metaDataEntity.setMetaValue("1");

        List<CenterModel> centerModels = new LinkedList<>();
        CenterModel center1 = new CenterModel();
        center1.setGroupId("G0001");
        centerModels.add(center1);
        CenterModel center2 = new CenterModel();
        center2.setGroupId("G0002");
        centerModels.add(center2);

        List<InKindsGoalSchoolYear> inKindsGoalSchoolYears = new ArrayList<>();
        InKindsGoalSchoolYear year = new InKindsGoalSchoolYear();
        year.setSchoolYear("2022-2023");
        inKindsGoalSchoolYears.add(year);

        List<InkindReportEntity> reports = new ArrayList<>();
        InkindReportEntity report = new InkindReportEntity();
        report.setId("I0001");
        report.setApproveStatus("APPROVED");
        report.setActivityTypeId("A0003");
        report.setGroupId("G0001");
        reports.add(report);

        // 一共五种角色, 审批的报告也对应五种角色
        List<ReportReviewModel> reviewModels = new ArrayList<>();
        ReportReviewModel reviewModel1 = new ReportReviewModel();
        reviewModel1.setActivityTypeId("A0001");
        reviewModel1.setApproveRole("AGENCY_ADMIN");
        // 当前角色的groupId
        reviewModel1.setGroupId("G0001");
        reviewModels.add(reviewModel1);
        ReportReviewModel reviewModel2 = new ReportReviewModel();
        reviewModel2.setActivityTypeId("A0002");
        reviewModel2.setApproveRole(ApproveRole.ADMINS.toString());
        reviewModel2.setGroupId("S0001");
        reviewModels.add(reviewModel2);
        ReportReviewModel reviewModel3 = new ReportReviewModel();
        reviewModel3.setActivityTypeId("A0003");
        reviewModel3.setApproveRole(ApproveRole.TEACHER_ASSISTANTS.toString());
        reviewModel3.setGroupId("G0001");
        reviewModels.add(reviewModel3);
        ReportReviewModel reviewModel4 = new ReportReviewModel();
        reviewModel4.setActivityTypeId("A0004");
        reviewModel4.setApproveRole(ApproveRole.TEACHER_ASSISTANTS.toString());
        reviewModel4.setGroupId("T0001");
        reviewModels.add(reviewModel4);

        // 调用真实方法
        InKindsGoalSchoolYearDaoImpl inKindsGoalSchoolYearDaoSpy = spy(inKindsGoalSchoolYearDaoImpl);
        ReflectionTestUtils.setField(inKindsGoalSchoolYearDaoSpy, BASE_MAPPER, inKindsGoalSchoolYearMapper);
        final LambdaQueryChainWrapper<InKindsGoalSchoolYear> lambdaQuery = new LambdaQueryChainWrapper<>(inKindsGoalSchoolYearMapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain((inKindsGoalSchoolYearMapper))).thenReturn(lambdaQuery);

        // 注入属性
        ReflectionTestUtils.setField(inKindApprovalService, "inKindsGoalSchoolYearDao", inKindsGoalSchoolYearDaoSpy);

        // 调用 getAgencyId() 的 mock 方法
        mockGetAgencyId(userEntity, agencyId);

        // 接口模拟
        when(userProvider.getCurrentUser()).thenReturn(authUserDetails);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        // when(agencyDao.getMeta(agencyId, "INKIND_OPEN")).thenReturn(metaDataEntity);
        when(inkindDao.getSchoolYearByAgencyId(agencyId)).thenReturn(schoolYears);
        when(centerDao.getCenterAndGroupsByTeacherId(userId)).thenReturn(centerModels);
        when(inkindDao.getReportByGroupIds(Arrays.asList(StringUtil.split("g0001,g0002")), InkindApproveStatus.PENDING, InKindStageEnum.APPROVE)).thenReturn(reports);
        when(inKindsGoalSchoolYearDaoSpy.lambdaQuery().eq(InKindsGoalSchoolYear::getAgencyId, agencyId).eq(InKindsGoalSchoolYear::getStep, InKindGoalSchoolYearStep.STEP3_GOAL_COMPLETE.getValue()).count()).thenReturn(1L);
        // when(inkindDao.getReviewBySchoolYearIds(any())).thenReturn(reviewModels);
        when(userDao.getMetaData(userId, UserMetaKey.INKIND_USER_ATTR_STATUS.toString())).thenReturn(null);
        // 获取当前学年
        Mockito.when(inKindProvider.getCurrentSchoolYear(any())).thenReturn(schoolYearEntity);
        Mockito.when(inKindProvider.getAgencyOpen(agencyId, "INKIND_OPEN")).thenReturn(true);
        List<MapModel> approve = reviewModels.stream().map(x -> new MapModel(x.getActivityTypeId(), x.getGroupId())).collect(Collectors.toList());
        when(inKindProvider.getHistoryApproveModel(userId)).thenReturn(approve);
        // 调用方法
        InkindPointResponse point = inKindApprovalService.getEducatorPoint(date, userId, timeZone);

        // 验证结果
        Assertions.assertEquals(true, point.getHasPoint());
        Assertions.assertEquals(true, point.getInkindDisplayStatus());
        Assertions.assertEquals(true, point.isInkindGoalSettingComplete());
        Assertions.assertEquals(true, point.isInkindOpenStatus());
        InkindUserAttrModel status = point.getInkindUserStatus();
        Assertions.assertEquals(true, status.getNewStatus());
        Assertions.assertEquals(true, status.getFristSetting());
        Assertions.assertEquals(true, status.getFristWelcome());
        Assertions.assertEquals(true, status.getFristSignature());
        Assertions.assertEquals(true, status.getFristCenterSync());
        Assertions.assertEquals(false, status.getFirstGuide());
        Assertions.assertEquals(false, status.getGuiding());
    }


    /**
     * 测试获取提醒的孩子
     */
    @Test
    void testGetRemindChild() {
        // 准备数据
        InkindCenterGroupRequest request = new InkindCenterGroupRequest();
        List<String> grouopIds = new ArrayList<>();
        grouopIds.add("G0001");
        request.setGroupIds(grouopIds);
        String userId = "U0001";
        String enrollmentId = "E0001";
        String EnrollmentId2 = "E0002";

        Date date1 = TimeUtil.parse("2020-01-01", TimeUtil.format10);

        UserEntity user = new UserEntity();
        user.setId("U0001");
        user.setRole("AGENCY_ADMIN");

        List<GroupEntity> groups = new ArrayList<>();
        GroupEntity group = new GroupEntity();
        group.setId("G0001");
        groups.add(group);

        List<AgencyModel> agencyModels = new ArrayList<>();
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("A0001");
        agencyModels.add(agencyModel);

        List<ReportReviewModel> reviewModels = new ArrayList<>();
        ReportReviewModel reviewModel = new ReportReviewModel();
        reviewModel.setGroupId("G0001");
        reviewModels.add(reviewModel);

        List<EnrollmentModel> children = new ArrayList<>();
        EnrollmentModel child = new EnrollmentModel();
        child.setId(enrollmentId);
        child.setFirstName("ChildName");
        child.setId(EnrollmentId2);
        child.setFirstName("ChildName");
        children.add(child);

        List<InkindSchoolYearEntity> schoolYears = new ArrayList<>();
        InkindSchoolYearEntity schoolYear = new InkindSchoolYearEntity();
        schoolYear.setId("S0001");
        schoolYear.setStartDate(date1);
        schoolYears.add(schoolYear);

        List<InkindReportEntity> reports = new ArrayList<>();

        List<UserEnrollmentEntity> studentParents = new ArrayList<>();

        List<UserEnrollmentModel> linkParentIdByChildIds = new ArrayList<>();
        UserEnrollmentModel userEnrollmentModel = new UserEnrollmentModel();
        userEnrollmentModel.setEnrollmentId(enrollmentId);
        userEnrollmentModel.setParentName("ParentName");

        // 模拟接口
        // 获取当前用户 Id
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        // 检查当前用户
        when(userProvider.checkUser(userId)).thenReturn(user);
        // 获取机构
        // when(userDao.getAgencyByAgencyAdminId(any())).thenReturn(agencyModels);
        // 获取学年
        // when(inkindDao.getSchoolYearByAgencyId(any())).thenReturn(schoolYears);
        // 查询审批的报告用于下面计算是否达到目标
        when(inkindDao.getReviewBySchoolYearId(any())).thenReturn(reviewModels);
        // 查询用户所辖班级内所有小孩
        when(studentDao.getChildrenWithAvatarByGroupIds(anyList())).thenReturn(children);
        // 获取时间
        when(userProvider.getTimezoneOffsetNum()).thenReturn(1);
        // 获取报告
        when(inkindDao.getReportByGroupIds(anyList(), anyString(), anyString())).thenReturn(reports);
        // 获取需要发送提醒的小孩对应的家长
        when(studentDao.getStudentParents(anyString())).thenReturn(studentParents);
        // 获取已连接的家长
        when(invitationsEnrollmentInvitationDao.getLinkParentIdByChildIds(anyList())).thenReturn(linkParentIdByChildIds);
        // 获取当前学年
        Mockito.when(inKindProvider.getCurrentSchoolYear(any())).thenReturn(schoolYear);
        Mockito.when(inKindProvider.getAgencyId()).thenReturn(agencyModel.getId());
        InkindRemindChildResponse remindChild = inKindApprovalService.getRemindChild(request);

        // 验证
        Assertions.assertEquals(1, remindChild.getChildIds().size());
        Assertions.assertEquals("E0002", remindChild.getChildIds().get(0));
    }

}

package com.learninggenie.api.service.lesson2.impl;

import com.learninggenie.common.data.entity.dll.SubjectEntity;
import com.learninggenie.common.data.entity.lesson2.*;
import com.learninggenie.common.data.entity.lesson2.curriculum.*;
import com.learninggenie.common.data.entity.lesson2.plan.*;
import com.learninggenie.common.data.model.JobsJobEntity;
import com.learninggenie.common.data.enums.*;
import com.learninggenie.api.model.lesson2.*;
import com.learninggenie.common.utils.TimeUtil;

import java.util.*;

/**
 * 课程资源服务测试数据构建器
 * 用于创建课程资源服务（CurriculumResourceService）相关的测试数据
 * 包括课程单元、计划、课程、任务等实体对象
 *
 * <AUTHOR> Assistant
 * @date 2024-03-20
 */
public class CurriculumResourceTestDataBuilder {

    /**
     * 创建课程单元实体
     * 用于测试获取单元资源、下载材料等功能
     *
     * @param unitId 单元ID
     * @return 课程单元实体
     */
    public static CurriculumUnitEntity createCurriculumUnit(String unitId) {
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setId(unitId);
        unit.setCurriculumId("curriculum_001");
        unit.setTitle("Test Unit");
        unit.setNumber(1);
        unit.setTenantId("tenant_001");
        return unit;
    }

    /**
     * 创建课程单元计划实体
     * 用于测试单元计划相关功能
     *
     * @param unitId 单元ID
     * @param planId 计划ID
     * @param number 计划序号
     * @return 课程单元计划实体
     */
    public static CurriculumUnitPlanEntity createCurriculumUnitPlan(String unitId, String planId, int number) {
        CurriculumUnitPlanEntity unitPlan = new CurriculumUnitPlanEntity();
        unitPlan.setId(UUID.randomUUID().toString());
        unitPlan.setUnitId(unitId);
        unitPlan.setPlanId(planId);
        unitPlan.setNumber(number);
        return unitPlan;
    }

    /**
     * 创建计划实体
     * 用于测试周计划相关功能
     *
     * @param planId 计划ID
     * @param week 周数
     * @return 计划实体
     */
    public static PlanEntity createPlan(String planId, int week) {
        PlanEntity plan = new PlanEntity();
        plan.setId(planId);
        plan.setTheme("Week " + week + " Theme");
        plan.setWeek(week);
        return plan;
    }

    /**
     * 创建计划项实体
     * 用于测试每日课程计划相关功能
     *
     * @param planId 计划ID
     * @param lessonId 课程ID
     * @param dayOfWeek 星期几（1-7）
     * @return 计划项实体
     */
    public static ItemEntity createPlanItem(String planId, String lessonId, int dayOfWeek) {
        ItemEntity item = new ItemEntity();
        item.setId(UUID.randomUUID().toString());
        item.setPlanId(planId);
        item.setLessonId(lessonId);
        item.setDayOfWeek(dayOfWeek);
        item.setName("Lesson " + lessonId);
        return item;
    }

    /**
     * 创建课程实体
     * 用于测试课程相关功能
     *
     * @param lessonId 课程ID
     * @return 课程实体
     */
    public static LessonEntity createLesson(String lessonId) {
        LessonEntity lesson = new LessonEntity();
        lesson.setId(lessonId);
        lesson.setName("Test Lesson " + lessonId);
        return lesson;
    }

    /**
     * 创建主题实体
     * 用于测试词汇表相关功能
     *
     * @param lessonId 课程ID
     * @param content 词汇内容
     * @param sortIndex 排序索引
     * @return 主题实体
     */
    public static SubjectEntity createSubject(String lessonId, String content, int sortIndex) {
        SubjectEntity subject = new SubjectEntity();
        subject.setId(UUID.randomUUID().toString());
        subject.setSourceId(lessonId);
        subject.setContent(content);
        subject.setSortIndex(sortIndex);
        return subject;
    }

    /**
     * 创建任务实体
     * 用于测试下载任务相关功能
     *
     * @param jobId 任务ID
     * @param status 任务状态
     * @return 任务实体
     */
    public static JobsJobEntity createJobsJob(String jobId, String status) {
        JobsJobEntity job = new JobsJobEntity();
        job.setId(jobId);
        job.setType(JobType.LESSON_QUIZ_WORD.toString());
        job.setStatus(status);
        job.setCreateAtUtc(TimeUtil.getUtcNow());
        job.setUpdateAtUtc(TimeUtil.getUtcNow());
        job.setRunCount(0);
        return job;
    }

    /**
     * 创建课程实体
     * 用于测试课程体系相关功能
     *
     * @param curriculumId 课程体系ID
     * @return 课程体系实体
     */
    public static CurriculumEntity createCurriculum(String curriculumId) {
        CurriculumEntity curriculum = new CurriculumEntity();
        curriculum.setId(curriculumId);
        curriculum.setName("Test Curriculum");
        return curriculum;
    }
} 
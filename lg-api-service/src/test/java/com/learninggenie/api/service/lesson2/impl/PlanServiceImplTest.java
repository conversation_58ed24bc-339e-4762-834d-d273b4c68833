package com.learninggenie.api.service.lesson2.impl;


import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.api.model.SuccessResponse;
import com.learninggenie.api.model.curriculum.PlanModel;
import com.learninggenie.api.model.curriculum.*;
import com.learninggenie.api.model.curriculum.test.CreatePlanOverviewTestRequest;
import com.learninggenie.api.model.lesson2.enums.TranslateConfigKey;
import com.learninggenie.api.model.lesson2.plan.*;
import com.learninggenie.api.model.prompt.CreatePromptTestResponse;
import com.learninggenie.api.provider.OpenAIProvider;
import com.learninggenie.api.provider.PromptProvider;
import com.learninggenie.api.provider.RemoteProvider;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.security.AuthUserDetails;
import com.learninggenie.api.service.lesson2.CurriculumService;
import com.learninggenie.api.service.lesson2.LessonService;
import com.learninggenie.api.service.lesson2.LessonsAdaptInfoService;
import com.learninggenie.api.service.lesson2.TranslateService;
import com.learninggenie.common.ai.service.OpenAIService;
import com.learninggenie.common.cache.CacheService;
import com.learninggenie.common.data.dao.DomainDao;
import com.learninggenie.common.data.dao.GroupDao;
import com.learninggenie.common.data.dao.GroupsMetaDataDao;
import com.learninggenie.common.data.dao.UsersMetaDataDao;
import com.learninggenie.common.data.dao.dll.SubjectDao;
import com.learninggenie.common.data.dao.dll.SubjectLanguageDao;
import com.learninggenie.common.data.dao.dll.SubjectMediaDao;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.dao.lesson2.*;
import com.learninggenie.common.data.dao.medias.MediaEntityDao;
import com.learninggenie.common.data.dao.prompts.PromptDao;
import com.learninggenie.common.data.dao.prompts.PromptUsageObjectDao;
import com.learninggenie.common.data.dao.prompts.PromptUsageRecordDao;
import com.learninggenie.common.data.dao.users.MetaDataDao;
import com.learninggenie.common.data.entity.PageList;
import com.learninggenie.common.data.dao.utils.LanguageDao;
import com.learninggenie.common.data.entity.UserEntity;
import com.learninggenie.common.data.entity.dll.SubjectEntity;
import com.learninggenie.common.data.entity.dll.SubjectLanguageEntity;
import com.learninggenie.common.data.entity.dll.SubjectMediaEntity;
import com.learninggenie.common.data.entity.lesson2.LessonAdaptRecordEntity;
import com.learninggenie.common.data.entity.lesson2.LessonEntity;
import com.learninggenie.common.data.entity.lesson2.curriculum.*;
import com.learninggenie.common.data.entity.lesson2.plan.*;
import com.learninggenie.common.data.entity.medias.MediaEntity;
import com.learninggenie.common.data.entity.prompt.PromptEntity;
import com.learninggenie.common.data.entity.prompt.PromptTestRecordEntity;
import com.learninggenie.common.data.entity.prompt.PromptUsageObjectEntity;
import com.learninggenie.common.data.entity.prompt.PromptUsageRecordEntity;
import com.learninggenie.common.data.entity.utils.LanguageEntity;
import com.learninggenie.common.data.enums.AgencyMetaKey;
import com.learninggenie.common.data.enums.TaskStatus;
import com.learninggenie.common.data.enums.UserMetaKey;
import com.learninggenie.common.data.enums.lesson2.CategoryType;
import com.learninggenie.common.data.enums.lesson2.PlanType;
import com.learninggenie.common.data.enums.prompt.PromptScene;
import com.learninggenie.common.data.model.*;
import com.learninggenie.common.data.model.openai.completion.chat.ChatCompletionResult;
import com.learninggenie.common.filesystem.FileSystem;
import com.learninggenie.common.framwork.FrameworkProvider;
import com.learninggenie.common.utils.JsonUtil;
import com.learninggenie.common.utils.TimeUtil;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.*;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.Silent.class)
public class PlanServiceImplTest {

    @InjectMocks
    private PlanServiceImpl planService;

    @Mock
    private UserProvider userProvider;

    @Mock
    private PlanDao planDao;

    @Mock
    private PlanCategoryDao planCategoryDao;

    @Mock
    private PlanItemEnrollmentDao planItemEnrollmentDao;

    @Mock
    private SubjectDao subjectDao;

    @Mock
    private CurriculumPlanApplyDao curriculumPlanApplyDao;

    @Mock
    private PlanCenterTagDao planCenterTagDao;

    @Mock
    private FrameworkProvider frameworkProvider;

    @Mock
    private PlanCenterDao planCenterDao;

    @Mock
    private UsersMetaDataDao usersMetaDataDao;

    @Mock
    private GroupsMetaDataDao groupsMetaDataDao;

    @Mock
    private MetaDataDao metaDataDao;

    @Mock
    private PlanInterpretDao planInterpretDao;

    @Mock
    private MediaEntityDao mediaEntityDao;

    @Mock
    private FileSystem fileSystem;

    @Mock
    private PlanUserDao planUserDao;

    @Mock
    private UserDaoImpl userDao;

    @Mock
    private CurriculumUnitPlanDao curriculumUnitPlanDao;

    @Mock
    private CurriculumUnitDao curriculumUnitDao;

    @Mock
    private PlanItemMeasureDao planItemMeasureDao;
    @Mock
    private PlanItemDao planItemDao;

    @Mock
    private UnitPlannerService unitPlannerService;

    @Mock
    private DomainDao domainDao;
    @Mock
    private LessonAgeDao lessonAgeDao;
    @Mock
    private LessonMeasureDao lessonMeasureDao;
    @Mock
    private LessonThemeDao lessonThemeDao;

    @Mock
    private LessonDao lessonDao;

    @Mock
    private LessonService lessonService;

    @Mock
    private PromptProvider promptProvider;

    @Mock
    private CurriculumService curriculumService;

    @Mock
    private OpenAIProvider openAIProvider;

    @Mock
    private OpenAIService openAIService;

    @Mock
    private PromptUsageRecordDao promptUsageRecordDao;

    @Mock
    private PromptUsageObjectDao promptUsageObjectDao;

    @Mock
    private PromptDao promptDao;

    @Mock
    private CacheService cacheService;

    @Mock
    private LessonAdaptRecordDao lessonAdaptRecordDao;

    @Mock
    private RemoteProvider remoteProvider;

    @Mock
    private PlanReflectionDao planReflectionDao;

    @Mock
    private GroupDao groupDao;

    @Mock
    private CurriculumBookDao curriculumBookDao;

    @Mock
    private CurriculumVocabularyDao curriculumVocabularyDao;

    @Mock
    private CurriculumAttachmentDao curriculumAttachmentDao;

    @Mock
    private SubjectLanguageDao subjectLanguageDao;

    @Mock
    private LanguageDao languageDao;

    @Mock
    private SubjectMediaDao subjectMediaDao;

    @Mock
    private TranslateService translateService;
    
    @Mock
    private LessonsAdaptInfoService lessonsAdaptInfoService;


    @Test
    public void getAgencyWeekPlanTemplateTest() {
        // 数据准备
        // 机构ID
        String agencyId = "agencyId";
        // 周计划ID
        String planId = "planId";
        // 分组ID
        String categoryId = "categoryId";
        // 当前用户模拟
        AuthUserDetails user = new AuthUserDetails();
        user.setAgencyId(agencyId);
        PlanEntity planEntity = new PlanEntity();
        planEntity.setId(planId);
        // 分组信息模拟
        CategoryEntity categoryEntity = new CategoryEntity();
        categoryEntity.setId(categoryId);
        categoryEntity.setAgencyId(agencyId);
        categoryEntity.setPlanId(planId);
        categoryEntity.setSortNum(1);
        categoryEntity.setType(CategoryType.BOTTOM_DAY_COL.toString());
        List<CategoryEntity> categoryEntities = new ArrayList<>();
        categoryEntities.add(categoryEntity);
        // 数据模拟
        when(planDao.getAgencyCurrentWeekPlanTemplate(agencyId)).thenReturn(planEntity);
        when(userProvider.checkCurrentUser()).thenReturn(user);
        when(userProvider.getAgencyOpenDefaultOpen(agencyId, AgencyMetaKey.WEEKLY_PLAN_TEMPLATE_EDIT_ONLY_ADMIN.toString())).thenReturn(true);
        when(planCategoryDao.listByPlanId(planId)).thenReturn(categoryEntities);
        when(userProvider.getAgencyOpenDefaultClose(agencyId, AgencyMetaKey.WEEKLY_PLAN_TEMPLATE_LAST_REFLECTION_OPEN.toString())).thenReturn(false);

        // 调用
        AgencyWeekPlanTemplateResponse response = planService.getAgencyWeekPlanTemplate();

        // 验证
        Assert.assertEquals(response.isNeedGuide(), false);
        Assert.assertEquals(response.isAdminSetup(), true);
        Assert.assertEquals(response.isLastReflectionOpen(), false);
    }

    @Test
    public void saveAgencyWeekPlanTemplateTest() {
        // 数据准备
        String requestJson = "{\"planId\":\"planId\",\"categories\":[{\"id\":\"categoryId\",\"name\":\"Activity\",\"type\":\"BOTTOM_DAY_COL\",\"sortNum\":1,\"planId\":\"planId\"},{\"id\":\"\",\"name\":\"newAddedCategoryId\",\"type\":\"BOTTOM_DAY_COL\",\"sortNum\":2,\"planId\":\"planId\"},{\"id\":\"updateCategoryId\",\"name\":\"Activity\",\"type\":\"BOTTOM_DAY_COL\",\"sortNum\":3,\"planId\":\"planId\"}],\"customThemeRowName\":\"test\"}";
        SaveAgencyWeekPlanTemplateRequest request = JsonUtil.fromJson(requestJson, SaveAgencyWeekPlanTemplateRequest.class);
        // 机构ID
        final String agencyId = "agencyId";
        // 周计划ID
        final String planId = "planId";
        // 分组ID
        final String categoryId = "categoryId";
        // 自定义主题行名称
        final String customThemeRowName = "test";
        // 当前用户模拟
        AuthUserDetails user = new AuthUserDetails();
        user.setAgencyId(agencyId);
        // 分组实体
        List<CategoryEntity> categoryEntities = new ArrayList<>();
        // 不变的分组
        CategoryEntity categoryEntity = new CategoryEntity();
        categoryEntity.setName("Activity");
        categoryEntity.setId(categoryId);
        categoryEntity.setPlanId(planId);
        categoryEntity.setAgencyId(agencyId);
        categoryEntity.setSortNum(1);
        categoryEntity.setType(CategoryType.BOTTOM_DAY_COL.toString());
        categoryEntities.add(categoryEntity);
        // 要删除的分组
        CategoryEntity deleteCategoryEntity = new CategoryEntity();
        deleteCategoryEntity.setPlanId(planId);
        deleteCategoryEntity.setId("deleteCategoryId" + categoryId);
        deleteCategoryEntity.setAgencyId(agencyId);
        deleteCategoryEntity.setSortNum(2);
        categoryEntities.add(deleteCategoryEntity);
        // 更新的分组
        CategoryEntity updateCategoryEntity = new CategoryEntity();
        updateCategoryEntity.setPlanId(planId);
        updateCategoryEntity.setId("updateCategoryId");
        updateCategoryEntity.setAgencyId(agencyId);
        updateCategoryEntity.setSortNum(3);
        categoryEntities.add(updateCategoryEntity);
        // 数据模拟
        when(planCategoryDao.listByPlanId(planId)).thenReturn(categoryEntities);
        when(userProvider.checkCurrentUser()).thenReturn(user);
        when(planDao.updateCustomThemeRowName(planId, customThemeRowName)).thenReturn(true);
        // 调用
        planService.saveAgencyWeekPlanTemplate(request);
        // 验证调用分组创建、删除、更新次数
        verify(planCategoryDao, times(1)).batchUpdate(anyList());
        verify(planCategoryDao, times(1)).batchDelete(anyList());
        verify(planCategoryDao, times(1)).batchCreate(anyList());
        verify(planDao, times(1)).updateCustomThemeRowName(planId, customThemeRowName);
    }

    @Test
    public void testListInterprets() {
        // 数据准备
        String planId = "planId";
        String userId = "userId";
        List<String> mediaIds = new ArrayList<>();
        mediaIds.add("mediaId");
        List<MediaEntity> mediaEntities = new ArrayList<>();
        MediaEntity mediaEntity = new MediaEntity();
        mediaEntity.setId("mediaId");
        mediaEntity.setFileType("mp4");
        mediaEntity.setRelativePath("relativePath");
        mediaEntity.setWeb(true);
        mediaEntity.setCompressed(true);
        mediaEntities.add(mediaEntity);
        List<PlanInterpretEntity> interprets = new ArrayList<>();
        PlanInterpretEntity interpret = new PlanInterpretEntity();
        interpret.setPlanId(planId);
        interpret.setId("interpretId");
        interpret.setMediaId("mediaId");
        interprets.add(interpret);
        ListInterpretResponse response = new ListInterpretResponse();
        response.setGuided(false);
        response.setShowNewBadge(true);

        // 模拟数据
        when(planInterpretDao.listByPlanId(planId)).thenReturn(interprets);
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(mediaEntityDao.listByIdIn(mediaIds)).thenReturn(mediaEntities);
        when(userProvider.getOpenValueDefaultClose(userId, UserMetaKey.PLAN_INTERPRETATION_USER_GUIDED.toString())).thenReturn(false);
        when(userProvider.getOpenValueDefaultOpen(userId, UserMetaKey.PLAN_INTERPRETATION_NEW_BADGE.toString())).thenReturn(true);
        when(fileSystem.getUncompressedPublicUrl(anyString())).thenReturn("uncompressedPublicUrl");
        when(fileSystem.getPublicUrl(anyString())).thenReturn("publicUrl");
        when(fileSystem.getUncompressedPublicUrl(anyString())).thenReturn("uncompressedPublicUrl");
        when(fileSystem.getPublicUrl(anyString())).thenReturn("publicUrl");

        // 调用
        ListInterpretResponse result = planService.listInterprets(planId);

        // 验证
        Assert.assertEquals(response.getGuided(), result.getGuided());
        Assert.assertEquals(response.getShowNewBadge(), result.getShowNewBadge());
        Assert.assertEquals(1, result.getInterpretations().size());
    }

    @Test
    public void testSetIntroduced() {
        // 数据准备
        String userId = "userId";

        // 数据模拟
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(metaDataDao.getByUserIdAndMetaKey(userId, UserMetaKey.PLAN_INTERPRETATION_USER_GUIDED.toString())).thenReturn(null);

        // 调用
        planService.setIntroduced();

        // 验证
        Mockito.verify(metaDataDao, times(1)).saveMeta(any());
    }

    @Test
    public void testHideNewBadge() {
        // 数据准备
        String userId = "userId";

        // 数据模拟
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(metaDataDao.getByUserIdAndMetaKey(userId, UserMetaKey.PLAN_INTERPRETATION_NEW_BADGE.toString())).thenReturn(null);

        // 调用
        planService.hideNewBadge();

        // 验证
        Mockito.verify(metaDataDao, times(1)).saveMeta(any());
    }

    /**
     * 测试创建周计划
     */
    @Test
    public void mockCreateWeekPlans() {
        // 模拟请求参数
        CreatePlansRequest request = new CreatePlansRequest();
        List<PlanModel> planModelList = new ArrayList<>();
        request.setUnitId(UUID.randomUUID().toString());
        PlanModel planModel = new PlanModel();
        //planModel.setId(UUID.randomUUID().toString());
        planModel.setTheme("theme");
        planModel.setConfirmed(true);
        planModelList.add(planModel);
        request.setPlans(planModelList);
        // 模拟创建人对象
        AuthUserDetails user = new AuthUserDetails();
        user.setAgencyId(UUID.randomUUID().toString());
        user.setUsername("username");
        Mockito.when(userProvider.checkCurrentUser()).thenReturn(user);
        // 模拟周计划默认模版
        PlanEntity planEntity = new PlanEntity();
        planEntity.setId(UUID.randomUUID().toString());
        Mockito.when(planDao.getCurriculumGenieTemplate()).thenReturn(planEntity);
        // 模拟分类
        List<CategoryEntity> categoryEntities = new ArrayList<>();
        for (int i = 0; i < 4; i++) {
            CategoryEntity categoryEntity = new CategoryEntity();
            categoryEntity.setId(UUID.randomUUID().toString());
            categoryEntity.setPlanId(planEntity.getId());
            categoryEntity.setAgencyId(UUID.randomUUID().toString());
            categoryEntity.setSortNum(i);
            if (i == 3) {
                categoryEntity.setType("TOP_WEEK_COL");
            } else {
                categoryEntity.setType("BOTTOM_DAY_COL");
            }
            categoryEntities.add(categoryEntity);
        }
        Mockito.when(planCategoryDao.listByPlanId(anyString())).thenReturn(categoryEntities);
        // 获取单元下已有的周计划
        List<CurriculumUnitPlanEntity> existingUnitPlans = new ArrayList<>();
        CurriculumUnitPlanEntity existingUnitPlan = new CurriculumUnitPlanEntity();
        existingUnitPlan.setId(UUID.randomUUID().toString());
        existingUnitPlan.setUnitId(request.getUnitId());
        existingUnitPlan.setPlanId(planEntity.getId());
        existingUnitPlans.add(existingUnitPlan);
        when(curriculumUnitPlanDao.getPlansByUnitId(anyString())).thenReturn(existingUnitPlans);
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setId(request.getUnitId());
        unit.setGrade("TK (4-5)");
        when(curriculumUnitDao.getById(anyString())).thenReturn(unit);
        CreatePlansResponse weekPlans = planService.createWeekPlans(request);
        // 验证
        Assertions.assertNotNull(weekPlans.getPlanIds());
        // 验证创建周计划
        Mockito.verify(planDao, times(1)).createBatch(any());
        // 验证周计划内容
        Assertions.assertEquals(1, weekPlans.getPlanIds().size());
    }

    /**
     * 测试更新周计划
     */
    @Test
    public void mockUpdateWeekPlans() {
        // 模拟请求参数
        CreatePlansRequest request = new CreatePlansRequest();
        List<PlanModel> planModelList = new ArrayList<>();
        request.setUnitId(UUID.randomUUID().toString());
        PlanModel planModel = new PlanModel();
        planModel.setId(UUID.randomUUID().toString());
        planModel.setTheme("theme");
        planModel.setOverview("no class overview");
        planModelList.add(planModel);
        request.setPlans(planModelList);
        request.setClearPlanItems(true);
        // 获取需要更新的周计划 ID 集合
        List<String> updatePlanIds = request.getPlans().stream().map(PlanModel::getId).collect(Collectors.toList());
        // 周计划下的分类
        List<CategoryEntity> categoryEntities = new ArrayList<>();
        CategoryEntity categoryEntity = new CategoryEntity();
        categoryEntity.setId("categoryId001");
        categoryEntity.setPlanId(planModel.getId());
        categoryEntities.add(categoryEntity);
        // 周计划下的活动集合
        List<ItemEntity> itemEntities = new ArrayList<>();
        ItemEntity itemEntity = new ItemEntity();
        itemEntity.setId(UUID.randomUUID().toString());
        itemEntity.setPlanId(planModel.getId());
        itemEntity.setName("class overview");
        itemEntities.add(itemEntity);
        // 模板周计划
        PlanEntity planTemplate = new PlanEntity();
        planTemplate.setId("planTemplate001");
        // 模板分类集合
        List<CategoryEntity> categoryEntityList = new ArrayList<>();
        CategoryEntity topWeekColCategory = new CategoryEntity();
        topWeekColCategory.setId("topWeekColCategoryId01");
        topWeekColCategory.setType("TOP_WEEK_COL");
        categoryEntityList.add(topWeekColCategory);
        List<ItemEntity> planItems = new ArrayList<>();
        ItemEntity planItem = new ItemEntity();
        planItem.setId("planItem001");
        planItem.setDeleted(false);
        planItem.setName("overview00001");
        planItems.add(planItem);

        // 模拟创建人对象
        AuthUserDetails user = new AuthUserDetails();
        user.setAgencyId(UUID.randomUUID().toString());
        user.setUsername("username");
        Mockito.when(userProvider.checkCurrentUser()).thenReturn(user);
        // 模拟数据库原始数据
        PlanEntity planEntity = new PlanEntity();
        planEntity.setId(UUID.randomUUID().toString());
        planEntity.setOverview("class overview");
        Mockito.when(planDao.getCurriculumGenieTemplate()).thenReturn(planTemplate);
        Mockito.when(planCategoryDao.listByPlanId(planTemplate.getId())).thenReturn(categoryEntityList);
        Mockito.when(planItemDao.listByPlanIdsAndCategoryId(updatePlanIds, topWeekColCategory.getId())).thenReturn(planItems);
        Mockito.when(planDao.get(anyString())).thenReturn(planEntity);
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setId(request.getUnitId());
        unit.setGrade("TK (4-5)");
        when(curriculumUnitDao.getById(anyString())).thenReturn(unit);
        // Mockito.when(planCategoryDao.queryTopWeekColCategoryIdsByPlanIds(updatePlanIds)).thenReturn(categoryEntities);
        // when(planItemDao.listByCategoryIds(categoryEntities.stream().map(CategoryEntity::getId).collect(Collectors.toList()))).thenReturn(itemEntities);
        CreatePlansResponse weekPlans = planService.updateWeekPlans(request);
        // 验证
        Assertions.assertNotNull(weekPlans.getPlanIds());
        // 验证更新周计划
        Mockito.verify(planDao, times(1)).updatePlans(any());
        Mockito.verify(planItemDao, times(1)).updateItems(any());
        // 验证周计划内容
        Assertions.assertEquals(1, weekPlans.getPlanIds().size());
        Assertions.assertEquals(planModel.getId(), weekPlans.getPlanIds().get(0));
    }

    /**
     * 测试创建周计划概览
     * case: 没有 Center 活动项
     */
    @Test
    public void mockCreatePlanItems() {
        // 模拟请求参数
        CreatePlanItemsRequest request = this.commonPlanItems(false);
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setId(request.getUnitId());
        unit.setGrade("TK (4-5)");
        when(curriculumUnitDao.getById(anyString())).thenReturn(unit);

        CreatePlanItemsResponse planItems = planService.createPlanItems(request);
        // 验证
        Assertions.assertNotNull(planItems.getPlanItemIds());
        // 验证创建周计划概览
        Mockito.verify(planItemDao, times(1)).batchCreate(anyList());
        // 验证周计划概览内容
        Assertions.assertEquals(7, planItems.getPlanItemIds().size());
    }

    /**
     * 测试创建周计划概览
     * case: 批量添加 Center 活动
     */
    @Test
    public void mockCreatePlanItemsWithBatchAddCenterItems() {
        // 模拟请求参数
        CreatePlanItemsRequest request = this.commonPlanItems(true);
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setId(request.getUnitId());
        unit.setGrade("TK (4-5)");
        unit.setUseDomain(false);
        when(curriculumUnitDao.getById(anyString())).thenReturn(unit);

        CreatePlanItemsResponse planItems = planService.createPlanItems(request);
        // 验证
        Assertions.assertNotNull(planItems.getPlanItemIds());
        // 验证创建周计划概览
        Mockito.verify(planItemDao, times(1)).batchCreate(anyList());
        // 验证周计划概览内容
        Assertions.assertEquals(7, planItems.getPlanItemIds().size());
    }

    /**
     * 测试更新周计划概览
     */
    @Test
    public void mockUpdatePlanItems() {
        CreatePlanItemsRequest request = this.commonUpdatePlanItems();
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setId(request.getUnitId());
        unit.setGrade("TK (4-5)");
        when(curriculumUnitDao.getById(anyString())).thenReturn(unit);
        List<ItemEntity> planItemIds = new ArrayList<>();
        request.getItems().forEach(v -> {
            ItemEntity item = new ItemEntity();
            item.setId(v.getId().toUpperCase());
            planItemIds.add(item);
        });
        PlanEntity planEntity = new PlanEntity();
        when(planDao.get(anyString())).thenReturn(planEntity);
        when(planItemDao.getByIds(anyList())).thenReturn(planItemIds);
        CreatePlanItemsResponse planItems = planService.updatePlanItems(request);
        // 验证
        Assertions.assertNotNull(planItems.getPlanItemIds());
        // 验证更新周计划概览
        Mockito.verify(planItemDao, times(1)).updateItems(anyList());
        // 验证周计划概览内容
        Assertions.assertEquals(7, planItems.getPlanItemIds().size());
    }

    private CreatePlanItemsRequest commonPlanItems(boolean batchAddCenterItem) {
        // 模拟请求参数
        CreatePlanItemsRequest request = new CreatePlanItemsRequest();
        String planId = UUID.randomUUID().toString();
        request.setPlanId(planId);
        request.setFrameworkId(UUID.randomUUID().toString());
        request.setUnitId(UUID.randomUUID().toString());
        request.setClearItemLesson(true);
        List<PlanItemModel> planItemModelList = new ArrayList<>();
        for (int i = 0; i < 7; i++) {
            PlanItemModel planItemModel = new PlanItemModel();
            planItemModel.setTitle("title");
            planItemModel.setDescription("description");
            planItemModel.setActivityType("activityType");
            planItemModel.setActivitySortNum(i);
            List<String> measures = new ArrayList<>();
            measures.add("abbreviation");
            planItemModel.setMeasures(measures);
            planItemModel.setId(UUID.randomUUID().toString());
            if (i == 0) {
                planItemModel.setDay("Monday");
            } else if (i == 1) {
                planItemModel.setDay("Tuesday");
            } else if (i == 2) {
                planItemModel.setDay("Wednesday");
            } else if (i == 3) {
                planItemModel.setDay("Thursday");
            } else if (i == 4) {
                planItemModel.setDay("Friday");
            } else if (i == 5) {
                planItemModel.setDay("Saturday");
                planItemModel.setId(null);
            } else {
                planItemModel.setDay("Sunday");
            }
            // 是否验证的是批量添加 Center 活动
            if (batchAddCenterItem) {
                planItemModel.setCenterId("centerId" + i);
            }
            planItemModelList.add(planItemModel);
        }
        request.setItems(planItemModelList);
        // 模拟创建人对象
        AuthUserDetails user = new AuthUserDetails();
        user.setAgencyId(UUID.randomUUID().toString());
        user.setUsername("username");
        Mockito.when(userProvider.checkCurrentUser()).thenReturn(user);
        // 模拟数据库原始数据
        ItemEntity itemEntity = new ItemEntity();
        String mediaId = UUID.randomUUID().toString();
        itemEntity.setMediaIds(mediaId);
        Mockito.when(planItemDao.get(anyString())).thenReturn(itemEntity);

        // 模拟周计划默认模版
        PlanEntity planEntity = new PlanEntity();
        planEntity.setId(UUID.randomUUID().toString());
        Mockito.when(planDao.getCurriculumGenieTemplate()).thenReturn(planEntity);
        // 模拟分类
        List<CategoryEntity> categoryEntities = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            CategoryEntity categoryEntity = new CategoryEntity();
            categoryEntity.setId(UUID.randomUUID().toString());
            categoryEntity.setPlanId(planEntity.getId());
            categoryEntity.setAgencyId(UUID.randomUUID().toString());
            categoryEntity.setSortNum(i);
            categoryEntity.setName("activityType");
            categoryEntities.add(categoryEntity);
        }
        Mockito.when(planCategoryDao.listByPlanId(anyString())).thenReturn(categoryEntities);

        List<DomainEntity> domainEntities = new ArrayList<>();
        DomainEntity domainEntity = new DomainEntity();
        domainEntity.setId(UUID.randomUUID().toString());
        domainEntity.setAbbreviation("abbreviation");
        domainEntities.add(domainEntity);
        Mockito.when(domainDao.getAllChildDomains(anyString())).thenReturn(domainEntities);

        // 模拟测评点实体类数据
        List<ItemMeasureEntity> itemMeasureEntities = new ArrayList<>();
        ItemMeasureEntity itemMeasure = new ItemMeasureEntity();
        itemMeasure.setMeasureId(mediaId);
        itemMeasureEntities.add(itemMeasure);
        Mockito.when(planItemMeasureDao.listByItemId(anyString())).thenReturn(itemMeasureEntities);

        // 获取周计划下已有活动项
        List<ItemEntity> existingItems = new ArrayList<>();
        ItemEntity existingItem = new ItemEntity();
        existingItem.setId(UUID.randomUUID().toString());
        ItemEntity existingCenterItem = new ItemEntity();
        existingCenterItem.setId(UUID.randomUUID().toString());
        existingCenterItem.setCenterId("centerId");
        existingItems.add(existingCenterItem);
        existingItems.add(existingItem);
        when(planItemDao.nonTextTypeItemIdsAndCentersIdByPlanId(anyString())).thenReturn(existingItems);
        return request;
    }

    private CreatePlanItemsRequest commonUpdatePlanItems() {
        // 模拟请求参数
        CreatePlanItemsRequest request = new CreatePlanItemsRequest();
        String planId = UUID.randomUUID().toString();
        request.setPlanId(planId);
        request.setFrameworkId(UUID.randomUUID().toString());
        request.setUnitId(UUID.randomUUID().toString());
        request.setClearItemLesson(true);
        List<PlanItemModel> planItemModelList = new ArrayList<>();
        for (int i = 0; i < 7; i++) {
            PlanItemModel planItemModel = new PlanItemModel();
            planItemModel.setActivitySortNum(i);
            planItemModel.setTitle("title");
            planItemModel.setDescription("description");
            planItemModel.setActivityType("activityType");
            List<String> measures = new ArrayList<>();
            measures.add("abbreviation");
            planItemModel.setMeasures(measures);
            planItemModel.setId(UUID.randomUUID().toString());
            if (i == 0) {
                planItemModel.setDay("Monday");

            } else if (i == 1) {
                planItemModel.setDay("Tuesday");
            } else if (i == 2) {
                planItemModel.setDay("Wednesday");
            } else if (i == 3) {
                planItemModel.setDay("Thursday");
            } else if (i == 4) {
                planItemModel.setDay("Friday");
            } else if (i == 5) {
                planItemModel.setDay("Saturday");
            } else {
                planItemModel.setDay("Sunday");
            }
            planItemModelList.add(planItemModel);
        }
        request.setItems(planItemModelList);
        // 模拟创建人对象
        AuthUserDetails user = new AuthUserDetails();
        user.setAgencyId(UUID.randomUUID().toString());
        user.setUsername("username");
        Mockito.when(userProvider.checkCurrentUser()).thenReturn(user);
        // 模拟数据库原始数据
        ItemEntity itemEntity = new ItemEntity();
        String mediaId = UUID.randomUUID().toString();
        itemEntity.setMediaIds(mediaId);
        itemEntity.setLessonId(UUID.randomUUID().toString());
        Mockito.when(planItemDao.get(anyString())).thenReturn(itemEntity);

        // 模拟周计划默认模版
        PlanEntity planEntity = new PlanEntity();
        planEntity.setId(UUID.randomUUID().toString());
        Mockito.when(planDao.getCurriculumGenieTemplate()).thenReturn(planEntity);
        // 模拟分类
        List<CategoryEntity> categoryEntities = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            CategoryEntity categoryEntity = new CategoryEntity();
            categoryEntity.setId(UUID.randomUUID().toString());
            categoryEntity.setPlanId(planEntity.getId());
            categoryEntity.setAgencyId(UUID.randomUUID().toString());
            categoryEntity.setSortNum(i);
            categoryEntity.setName("activityType");
            categoryEntities.add(categoryEntity);
        }
        Mockito.when(planCategoryDao.listByPlanId(anyString())).thenReturn(categoryEntities);

        List<DomainEntity> domainEntities = new ArrayList<>();
        DomainEntity domainEntity = new DomainEntity();
        domainEntity.setId(UUID.randomUUID().toString());
        domainEntity.setAbbreviation("abbreviation");
        domainEntities.add(domainEntity);
        Mockito.when(domainDao.getAllChildDomains(anyString())).thenReturn(domainEntities);

        // 模拟测评点实体类数据
        List<ItemMeasureEntity> itemMeasureEntities = new ArrayList<>();
        ItemMeasureEntity itemMeasure = new ItemMeasureEntity();
        itemMeasure.setMeasureId(mediaId);
        itemMeasureEntities.add(itemMeasure);
        Mockito.when(planItemMeasureDao.listByItemId(anyString())).thenReturn(itemMeasureEntities);

        // 获取周计划下已有活动项
        List<ItemEntity> existingItems = new ArrayList<>();
        ItemEntity existingItem = new ItemEntity();
        existingItem.setId(UUID.randomUUID().toString());
        existingItems.add(existingItem);
        when(planItemDao.listByPlanId(anyString())).thenReturn(existingItems);
        return request;
    }

    /**
     * 测试异步生成周计划概览
     * case: Unit 单元下只有一周的情况
     */
    @Test
    public void mockGeneratePlanOverviewStreamForOneWeek() {
        // 模拟请求参数
        // 模拟单元信息
        CurriculumUnitEntity unitEntity = new CurriculumUnitEntity();
        unitEntity.setId(UUID.randomUUID().toString());
        unitEntity.setFrameworkId(UUID.randomUUID().toString());
        unitEntity.setWeekCount(1);
        unitEntity.setGrade("Toddler (2-3)");
        unitEntity.setUseDomain(false);
        // 创建模拟的 DomainEntity 列表
        List<DomainEntity> mockDomains = new ArrayList<>();
        // 添加一些模拟的 DomainEntity 对象到列表中
        DomainEntity domain1 = new DomainEntity();
        domain1.setId("4BD0392C-55D3-EB11-9C19-4CCC6ACF6129");
        domain1.setName("Approaches to Learning–Self-Regulation");
        domain1.setAbbreviation("ATL-REG");
        domain1.setParentId("********-BDCE-E411-AF66-02C72B94B99B");
        domain1.setNodes(new ArrayList<>());
        when(curriculumUnitDao.getUnitById(anyString())).thenReturn(unitEntity);
        when(frameworkProvider.getFrameworkMeasuresTree(anyString())).thenReturn(mockDomains);
        // 模拟周计划概览当前版本 Prompt
        PromptEntity promptEntity = new PromptEntity();
        promptEntity.setPromptTemplate("test prompt for one week");
        promptEntity.setVersion("V1.0");
        promptEntity.setId(UUID.randomUUID().toString());
        when(promptProvider.getActivePromptByScene(anyString())).thenReturn(promptEntity);
        when(promptProvider.getPromptSceneByProject(any())).thenReturn(PromptScene.PLAN_OVERVIEW_WITH_MEASURES);
        when(promptProvider.getPromptSceneByAgeGroup(any(), anyString())).thenReturn(PromptScene.UNIT_OVERVIEW);
        // 调用方法
        SseEmitter sseEmitter = planService.generatePlanOverviewStream(UUID.randomUUID().toString());
        // 断言
        Assert.assertNotNull(sseEmitter);
        // 验证保存了概览信息
        Mockito.verify(openAIProvider, Mockito.times(1)).createStreamChatCompletion(anyString(), any(), any(), any());
        Mockito.verify(curriculumUnitDao, Mockito.times(1)).getUnitById(anyString());
    }

    /**
     * 测试异步生成周计划概览
     * case: Unit 单元下存在多个周的情况
     */
    @Test
    public void mockGeneratePlanOverviewStreamMoreWeek() {
        // 模拟请求参数
        // 模拟单元信息
        CurriculumUnitEntity unitEntity = new CurriculumUnitEntity();
        unitEntity.setId(UUID.randomUUID().toString());
        unitEntity.setFrameworkId(UUID.randomUUID().toString());
        unitEntity.setGrade("Toddler (2-3)");
        unitEntity.setWeekCount(2);
        unitEntity.setUseDomain(false);
        when(curriculumUnitDao.getUnitById(anyString())).thenReturn(unitEntity);
        // 模拟周计划概览当前版本 Prompt
        PromptEntity promptEntity = new PromptEntity();
        promptEntity.setPromptTemplate("test prompt for more week");
        promptEntity.setVersion("V1.0");
        promptEntity.setId(UUID.randomUUID().toString());
        // 创建模拟的 DomainEntity 列表
        List<DomainEntity> mockDomains = new ArrayList<>();
        // 添加一些模拟的 DomainEntity 对象到列表中
        DomainEntity domain1 = new DomainEntity();
        domain1.setId("4BD0392C-55D3-EB11-9C19-4CCC6ACF6129");
        domain1.setName("Approaches to Learning–Self-Regulation");
        domain1.setAbbreviation("ATL-REG");
        domain1.setParentId("********-BDCE-E411-AF66-02C72B94B99B");
        domain1.setNodes(new ArrayList<>());
        // Foundation 信息
        String foundationInfo = "foundationInfo";

        when(curriculumService.getCustomFoundationInfo(anyString())).thenReturn(foundationInfo);
        when(promptProvider.getPromptSceneByProject(any())).thenReturn(PromptScene.LESSON_OVERVIEW_ACTIVITY_ALL_STEP2);
        when(promptProvider.getActivePromptByScene(anyString())).thenReturn(promptEntity);
        when(promptProvider.getPromptSceneByAgeGroup(any(), anyString())).thenReturn(PromptScene.UNIT_OVERVIEW);
        when(frameworkProvider.getFrameworkMeasuresTree(anyString())).thenReturn(mockDomains);
        // 调用方法
        SseEmitter sseEmitter = planService.generatePlanOverviewStream(UUID.randomUUID().toString());
        // 断言
        Assert.assertNotNull(sseEmitter);
        // 验证保存了概览信息
        Mockito.verify(openAIProvider, Mockito.times(1)).createStreamChatCompletion(anyString(), any(), any(), any());
        Mockito.verify(curriculumUnitDao, Mockito.times(1)).getUnitById(anyString());
    }

    /**
     * 测试重新生成单元概览
     */
    @Test
    public void testGenerateSinglePlanOverviewStream() {
        String unitId = "unit-id-01";
        String weekNumber = "1";

        // 测试一共一周的情况
        CurriculumUnitEntity unitEntity = new CurriculumUnitEntity();
        unitEntity.setId(UUID.randomUUID().toString());
        unitEntity.setFrameworkId(UUID.randomUUID().toString());
        unitEntity.setWeekCount(1);
        unitEntity.setGrade("Toddler (2-3)");
        // 创建模拟的 DomainEntity 列表
        List<DomainEntity> mockDomains = new ArrayList<>();
        // 添加一些模拟的 DomainEntity 对象到列表中
        DomainEntity domain1 = new DomainEntity();
        domain1.setId("4BD0392C-55D3-EB11-9C19-4CCC6ACF6129");
        domain1.setName("Approaches to Learning–Self-Regulation");
        domain1.setAbbreviation("ATL-REG");
        domain1.setParentId("********-BDCE-E411-AF66-02C72B94B99B");
        domain1.setNodes(new ArrayList<>());
        when(curriculumUnitDao.getUnitById(anyString())).thenReturn(unitEntity);
        // 单元周计划
        List<CurriculumUnitPlanEntity> unitPlanEntities = new ArrayList<>();
        when(curriculumUnitPlanDao.getPlansByUnitId(unitId)).thenReturn(unitPlanEntities);
        // 模拟周计划概览当前版本 Prompt
        PromptEntity promptEntity = new PromptEntity();
        promptEntity.setPromptTemplate("test prompt for one week");
        promptEntity.setVersion("V1.0");
        promptEntity.setId(UUID.randomUUID().toString());
        when(promptProvider.getPromptSceneByAgeGroup(any(), anyString())).thenReturn(PromptScene.SINGLE_WEEK_PLAN_OVERVIEW);
        when(promptProvider.getActivePromptByScene(anyString())).thenReturn(promptEntity);
        when(promptProvider.getPromptSceneByProject(any())).thenReturn(PromptScene.SINGLE_WEEK_PLAN_OVERVIEW);
        when(frameworkProvider.getFrameworkMeasuresTree(anyString())).thenReturn(mockDomains);
        // 调用方法
        SseEmitter sseEmitter = planService.generateSinglePlanOverviewStream(unitId, weekNumber);
        // 验证结果
        Assert.assertNotNull(sseEmitter);
        verify(openAIProvider, times(1)).createStreamChatCompletion(anyString(), any(), any(), any());
        // 清理
        clearInvocations(openAIProvider, curriculumUnitDao);

        // 测试一共多周的情况
        unitEntity.setWeekCount(2);
        when(curriculumUnitDao.getUnitById(anyString())).thenReturn(unitEntity);
        // 调用方法
        sseEmitter = planService.generateSinglePlanOverviewStream(unitId, weekNumber);
        // 验证结果
        Assert.assertNotNull(sseEmitter);
        verify(openAIProvider, times(1)).createStreamChatCompletion(anyString(), any(), any(), any());
    }

    /**
     * 测试生成单元周计划概览
     */
    @Test
    public void mockGeneratePlanOverview() {
        String unitId = UUID.randomUUID().toString();
        String frameworkId = UUID.randomUUID().toString();
        // 单元信息
        CurriculumUnitEntity unitEntity = new CurriculumUnitEntity();
        unitEntity.setId(unitId);
        unitEntity.setWeekCount(2);
        unitEntity.setTitle("title");
        unitEntity.setCity("city");
        unitEntity.setState("state");
        unitEntity.setDescription("description");
        unitEntity.setGrade("grade");
        unitEntity.setFrameworkId(frameworkId);
        unitEntity.setOverview("overview");
        unitEntity.setTrajectory("trajectory");
        unitEntity.setConcepts("concepts");
        unitEntity.setGuidingQuestions("guidingQuestions");
        Mockito.when(curriculumUnitDao.getUnitById(anyString())).thenReturn(unitEntity);
        // 获取单元概览当前版本 Prompt
        PromptEntity promptEntity = new PromptEntity();
        promptEntity.setPromptTemplate("{{ total number of week }} {{ unit name }} {{ city }} {{ state }} {{ age group }}" +
                "{{ overview }}{{ unit trajectory }}{{ concepts }}{{ unit description }}{{ guiding questions }}");
        promptEntity.setVersion("V1.0");
        promptEntity.setScene(PromptScene.UNIT_OVERVIEW.toString());
        Mockito.when(promptProvider.getActivePromptByScene(anyString())).thenReturn(promptEntity);

        ChatCompletionResult completionResult = new ChatCompletionResult();
        when(openAIService.createChatCompletion(any())).thenReturn(completionResult);
        // 请求信息
        GeneratePlanOverviewRequest request = new GeneratePlanOverviewRequest();
        request.setUnitId(unitId);
        request.setCreateSource("AUTO");
        // 更新生成记录信息
        PromptUsageRecordEntity promptUsageRecord = new PromptUsageRecordEntity();
        promptUsageRecord.setId(UUID.randomUUID().toString());
        when(promptProvider.updatePromptUsageRecord(any(), anyString(), any())).thenReturn(promptUsageRecord);
        request.setUsageRecordEntity(promptUsageRecord);
        // 调用方法
        GeneratePlanOverviewResponse response = planService.generatePlanOverview(request);
        // 断言
        Assert.assertNotNull(response);
        // 验证数据
        Assertions.assertEquals(promptUsageRecord.getId(), response.getPromptUsageId());
    }

    /**
     * 测试生成单元周计划概览
     */
    @Test
    public void mockGeneratePlanOverview2() {
        String unitId = UUID.randomUUID().toString();
        String frameworkId = UUID.randomUUID().toString();
        // 单元信息
        CurriculumUnitEntity unitEntity = new CurriculumUnitEntity();
        unitEntity.setId(unitId);
        unitEntity.setWeekCount(2);
        unitEntity.setTitle("title");
        unitEntity.setCity("city");
        unitEntity.setState("state");
        unitEntity.setDescription("description");
        unitEntity.setGrade("grade");
        unitEntity.setFrameworkId(frameworkId);
        unitEntity.setOverview("overview");
        unitEntity.setTrajectory("trajectory");
        unitEntity.setConcepts("concepts");
        unitEntity.setGuidingQuestions("guidingQuestions");
        Mockito.when(curriculumUnitDao.getUnitById(anyString())).thenReturn(unitEntity);
        // 获取单元概览当前版本 Prompt
        PromptEntity promptEntity = new PromptEntity();
        promptEntity.setPromptTemplate("{{ total number of week }} {{ unit name }} {{ city }} {{ state }} {{ age group }}" +
                "{{ overview }}{{ unit trajectory }}{{ concepts }}{{ unit description }}{{ guiding questions }}");
        promptEntity.setVersion("V1.0");
        promptEntity.setScene(PromptScene.UNIT_OVERVIEW.toString());
        Mockito.when(promptProvider.getActivePromptByScene(anyString())).thenReturn(promptEntity);

        ChatCompletionResult completionResult = new ChatCompletionResult();
        when(openAIService.createChatCompletion(any())).thenReturn(completionResult);
        // 请求信息
        GeneratePlanOverviewRequest request = new GeneratePlanOverviewRequest();
        request.setUnitId(unitId);
        request.setCreateSource("AUTO");
        // 更新生成记录信息
        PromptUsageRecordEntity promptUsageRecord = new PromptUsageRecordEntity();
        promptUsageRecord.setId(UUID.randomUUID().toString());
        when(promptProvider.createPromptUsageRecord(anyString(), any(), any(), any(), any(), anyString())).thenReturn(promptUsageRecord);
        // 调用方法
        GeneratePlanOverviewResponse response = planService.generatePlanOverview(request);
        // 断言
        Assert.assertNotNull(response);
        // 验证数据
        Assertions.assertEquals(promptUsageRecord.getId(), response.getPromptUsageId());
    }

    /**
     * 测试评价周计划概览生成内容
     */
    @Test
    public void mockEvaluatePlanOverview() {
        String promptId = UUID.randomUUID().toString();
        // 记录信息
        PromptUsageRecordEntity usageRecordEntity = new PromptUsageRecordEntity();
        usageRecordEntity.setPromptId(promptId);
        usageRecordEntity.setAdditionalData("{\"unitId\":\"a88cc1c0-0104-48ec-b51e-2d9ae8b6756e\"}");
        usageRecordEntity.setCompletion("completion");
        when(promptUsageRecordDao.getById(anyString())).thenReturn(usageRecordEntity);
        // 评价记录信息
        PromptUsageObjectEntity usageObjectEntity = new PromptUsageObjectEntity();
        usageObjectEntity.setUsageRecordId(promptId);
        when(promptUsageObjectDao.getByUseObjectAndObjectId(anyString(), anyString())).thenReturn(usageObjectEntity);
        // 调用方法
        EvaluatePromptResponse response = planService.evaluatePlanOverview(promptId);
        // 断言
        Assert.assertNotNull(response);
        // 验证数据
        Assertions.assertEquals(usageRecordEntity.getCompletion(), response.getResult());
    }

    /**
     * 测试评价周计划概览生成内容
     */
    @Test
    public void mockEvaluatePlanOverview2() {
        String promptId = UUID.randomUUID().toString();
        String unitId = UUID.randomUUID().toString();
        // 记录信息
        PromptUsageRecordEntity usageRecordEntity = new PromptUsageRecordEntity();
        usageRecordEntity.setPromptId(promptId);
        usageRecordEntity.setAdditionalData("{\"unitId\":\"a88cc1c0-0104-48ec-b51e-2d9ae8b6756e\",\"lessonId\":\"a88cc1c0-0104-48ec-b51e-2d9ae8b6756e\"}");
        usageRecordEntity.setCompletion("completion");
        when(promptUsageRecordDao.getById(anyString())).thenReturn(usageRecordEntity);
        // 评价记录信息
        PromptUsageObjectEntity usageObjectEntity = new PromptUsageObjectEntity();
        usageObjectEntity.setUsageRecordId(promptId);
        when(promptUsageObjectDao.getByUseObjectAndObjectId(anyString(), anyString())).thenReturn(null);
        // 单元信息
        CurriculumUnitEntity unitEntity = new CurriculumUnitEntity();
        unitEntity.setId(unitId);
        unitEntity.setWeekCount(2);
        unitEntity.setTitle("title");
        unitEntity.setCity("city");
        unitEntity.setState("state");
        unitEntity.setDescription("description");
        unitEntity.setGrade("grade");
        unitEntity.setOverview("overview");
        unitEntity.setTrajectory("trajectory");
        unitEntity.setConcepts("concepts");
        unitEntity.setGuidingQuestions("guidingQuestions");
        Mockito.when(curriculumUnitDao.getUnitById(anyString())).thenReturn(unitEntity);
        // 获取单元概览当前版本 Prompt
        PromptEntity promptEntity = new PromptEntity();
        promptEntity.setEvaluatePromptTemplate("{{ unit name }}{{ city }}{{ state }}{{ age group }}" +
                "{{ generated unit overview, trajectory, concepts and guiding questions }}{{ generated weekly themes and overview }}");
        promptEntity.setVersion("V1.0");
        promptEntity.setScene(PromptScene.UNIT_OVERVIEW.toString());
        Mockito.when(promptDao.getById(anyString())).thenReturn(promptEntity);

        PromptUsageRecordEntity evaluateRecordEntity = new PromptUsageRecordEntity();
        evaluateRecordEntity.setId(UUID.randomUUID().toString());
        evaluateRecordEntity.setCompletion("Total Score: 90/100");
        when(openAIProvider.createChatCompletion(anyString(), any(), any(), any(), any())).thenReturn(evaluateRecordEntity);
        // 调用方法
        EvaluatePromptResponse response = planService.evaluatePlanOverview(promptId);
        // 断言
        Assert.assertNotNull(response);
        // 验证数据
        Assertions.assertEquals(evaluateRecordEntity.getCompletion(), response.getResult());
        verify(promptUsageRecordDao, times(1)).updateById(any());
    }

    @Test
    public void mockCreatePlanOverviewTest() {
        // 请求信息
        CreatePlanOverviewTestRequest request = new CreatePlanOverviewTestRequest();
        String promptId = UUID.randomUUID().toString();
        int testCount = 10;
        String unitId = UUID.randomUUID().toString();
        request.setUnitId(unitId);
        request.setTestCount(testCount);
        request.setPromptId(promptId);
        // 生成记录信息
        PromptTestRecordEntity promptTestRecord = new PromptTestRecordEntity();
        promptTestRecord.setId(UUID.randomUUID().toString());
        when(promptProvider.createPromptTestRecord(anyString(), anyInt(), anyString())).thenReturn(promptTestRecord);
        // 调用方法
        CreatePromptTestResponse response = planService.createPlanOverviewTest(request);
        // 断言
        Assert.assertNotNull(response);
        // 验证数据
        Assert.assertEquals(promptTestRecord.getId(), response.getTestRecordId());
    }


    @Test
    public void testCreateCurriculumPlan() {
        // 数据准备
        String frameworkId = "frameworkId";
        String userId = "userId";
        String agencyId = "agencyId";
        String planId = "planId";
        AuthUserDetails user = new AuthUserDetails();
        user.setAgencyId(agencyId);
        user.setUsername(userId);
        user.setRole("AGENCY_OWNER");
        PlanEntity planEntity = new PlanEntity();
        planEntity.setId("planId");
        planEntity.setAgencyId(agencyId);
        planEntity.setFrameworkId(frameworkId);
        List<CategoryEntity> categoryEntities = new ArrayList<>();
        CategoryEntity categoryEntity = new CategoryEntity();
        categoryEntity.setName("Activity");
        categoryEntity.setId("categoryId");
        categoryEntity.setPlanId(planId);
        categoryEntity.setAgencyId(agencyId);
        categoryEntity.setSortNum(1);
        categoryEntity.setType(CategoryType.BOTTOM_DAY_COL.toString());
        categoryEntities.add(categoryEntity);
        // mock
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.checkCurrentUser()).thenReturn(user);
        when(userProvider.getAgencyOpenDefaultOpen("agencyId", AgencyMetaKey.WEEKLY_PLAN_TEMPLATE_EDIT_ONLY_ADMIN.toString())).thenReturn(false);
        when(planDao.getUserLatestPlan("userId")).thenReturn(planEntity);
        when(planCategoryDao.listByPlanId(planId)).thenReturn(categoryEntities);
        when(usersMetaDataDao.getMeta(userId, UserMetaKey.WEEKLY_PLAN_GUIDE.toString())).thenReturn(null);
        // 调用
        CreatePlanResponse response = planService.createCurriculumWeekPlan(frameworkId);
        // 验证
        verify(planCategoryDao, times(1)).batchCreate(anyList());
        verify(planDao, times(1)).create(any());
        verify(planUserDao, times(1)).create(any());
        // 断言
        Assert.assertEquals(response.isFirstVisit(), true);
    }

    /**
     * 测试创建单元课程周计划.
     * case: 使用机构模板
     */
    @Test
    public void testCreateCurriculumPlan2() {
        // 数据准备
        final String frameworkId = "frameworkId";
        final String userId = "userId";
        final String agencyId = "agencyId";
        final String planId = "planId";
        AuthUserDetails user = new AuthUserDetails();
        user.setAgencyId(agencyId);
        user.setUsername(userId);
        user.setRole("AGENCY_OWNER");
        PlanEntity planEntity = new PlanEntity();
        planEntity.setId(planId);
        planEntity.setAgencyId(agencyId);
        planEntity.setFrameworkId(frameworkId);
        List<CategoryEntity> categoryEntities = new ArrayList<>();
        CategoryEntity categoryEntity = new CategoryEntity();
        categoryEntity.setName("Activity");
        categoryEntity.setId("categoryId");
        categoryEntity.setPlanId(planId);
        categoryEntity.setAgencyId(agencyId);
        categoryEntity.setSortNum(1);
        categoryEntity.setType(CategoryType.BOTTOM_DAY_COL.toString());
        categoryEntities.add(categoryEntity);
        // mock
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.checkCurrentUser()).thenReturn(user);
        when(userProvider.getAgencyOpenDefaultOpen("agencyId", AgencyMetaKey.WEEKLY_PLAN_TEMPLATE_EDIT_ONLY_ADMIN.toString())).thenReturn(true);
        when(planDao.getAgencyCurrentWeekPlanTemplate(agencyId)).thenReturn(planEntity);
        when(planCategoryDao.listByPlanId(planId)).thenReturn(categoryEntities);
        when(usersMetaDataDao.getMeta(userId, UserMetaKey.WEEKLY_PLAN_GUIDE.toString())).thenReturn(null);
        // 调用
        CreatePlanResponse response = planService.createCurriculumWeekPlan(frameworkId);
        // 验证
        verify(planCategoryDao, times(1)).batchCreate(anyList());
        verify(planDao, times(1)).create(any());
        verify(planUserDao, times(1)).create(any());
        // 断言
        Assert.assertEquals(response.isFirstVisit(), true);
    }

    /**
     * 测试创建单元课程周计划.
     * case: 使用系统模板
     */
    @Test
    public void testCreateCurriculumPlan3() {
        // 数据准备
        final String frameworkId = "frameworkId";
        final String userId = "userId";
        final String agencyId = "agencyId";
        final String planId = "planId";
        AuthUserDetails user = new AuthUserDetails();
        user.setAgencyId(agencyId);
        user.setUsername(userId);
        user.setRole("AGENCY_OWNER");
        PlanEntity planEntity = new PlanEntity();
        planEntity.setId(planId);
        planEntity.setAgencyId(agencyId);
        planEntity.setFrameworkId(frameworkId);
        List<CategoryEntity> categoryEntities = new ArrayList<>();
        CategoryEntity categoryEntity = new CategoryEntity();
        categoryEntity.setName("Activity");
        categoryEntity.setId("categoryId");
        categoryEntity.setPlanId(planId);
        categoryEntity.setAgencyId(agencyId);
        categoryEntity.setSortNum(1);
        categoryEntity.setType(CategoryType.BOTTOM_DAY_COL.toString());
        categoryEntities.add(categoryEntity);
        // mock
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.checkCurrentUser()).thenReturn(user);
        when(userProvider.getAgencyOpenDefaultOpen("agencyId", AgencyMetaKey.WEEKLY_PLAN_TEMPLATE_EDIT_ONLY_ADMIN.toString())).thenReturn(true);
        when(planDao.getAgencyCurrentWeekPlanTemplate(agencyId)).thenReturn(null);
        when(planDao.getSystemTemplate()).thenReturn(planEntity);
        when(planCategoryDao.listByPlanId(planId)).thenReturn(categoryEntities);
        when(usersMetaDataDao.getMeta(userId, UserMetaKey.WEEKLY_PLAN_GUIDE.toString())).thenReturn(null);
        // 调用
        CreatePlanResponse response = planService.createCurriculumWeekPlan(frameworkId);
        // 验证
        verify(planCategoryDao, times(1)).batchCreate(anyList());
        verify(planDao, times(1)).create(any());
        verify(planUserDao, times(1)).create(any());
        // 断言
        Assert.assertEquals(response.isFirstVisit(), true);
    }


    /**
     * 测试结束应用流程.
     */
    @Test
    public void testEndApplyFlowPath() {
        // 数据准备
        final String batchId = "batchId";
        CurriculumPlanApplyEntity applyEntity = new CurriculumPlanApplyEntity();
        applyEntity.setBatchId(batchId);
        applyEntity.setPlanId("planId");
        applyEntity.setDeleted(false);
        List<CurriculumPlanApplyEntity> applyEntities = new ArrayList<>();
        applyEntities.add(applyEntity);
        // mock
        when(curriculumPlanApplyDao.getApplyRecordsByBatchId(batchId)).thenReturn(applyEntities);
        // 调用
        final SuccessResponse response = planService.endApplyFlowPath(batchId);
        // 验证
        verify(curriculumPlanApplyDao, times(1)).updateBatchById(any());
        // 断言
        Assert.assertEquals(response.isSuccess(), true);
    }

    /**
     * 测试删除 center 组标签.
     */
    @Test
    public void testDeleteTag() {
        // 数据准备
        final String name = "name";
        final String userId = "userId";
        // mock
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        // 调用
        final SuccessResponse response = planService.deleteTag(name);
        // 验证
        verify(planCenterTagDao, times(1)).deleteByNameAndUserId(name, userId);
        // 断言
        Assert.assertEquals(response.isSuccess(), true);
    }

    /**
     * 测试用户创建周计划
     * case: 新用户首次创建周计划 FirstVisit 为 true
     * case: 老用户创建周计划 FirstVisit 为 false
     */
    @Test
    public void testCreateWeeklyPlan() {

        // 数据准备
        final String frameworkId = "framework001";
        final String userId = "user001";
        final String agencyId = "agency001";
        final String planId = "plan001";
        CreatePlanRequest createPlanRequest = new CreatePlanRequest();
        createPlanRequest.setGroupId("group001");
        AuthUserDetails user = new AuthUserDetails();
        user.setAgencyId(agencyId);
        user.setUsername(userId);
        user.setRole("AGENCY_OWNER");
        PlanEntity planEntity = new PlanEntity();
        planEntity.setId(planId);
        planEntity.setAgencyId(agencyId);
        planEntity.setFrameworkId(frameworkId);
        List<CategoryEntity> categoryEntities = new ArrayList<>();
        CategoryEntity categoryEntity = new CategoryEntity();
        categoryEntity.setName("Activity");
        categoryEntity.setId("category001");
        categoryEntity.setPlanId(planId);
        categoryEntity.setAgencyId(agencyId);
        categoryEntity.setSortNum(1);
        categoryEntity.setType("BOTTOM_DAY_COL");
        categoryEntities.add(categoryEntity);

        // 模拟数据
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.checkCurrentUser()).thenReturn(user);
        when(userProvider.getAgencyOpenDefaultOpen("agency001", "WEEKLY_PLAN_TEMPLATE_EDIT_ONLY_ADMIN")).thenReturn(true);
        when(planDao.getAgencyCurrentWeekPlanTemplate(agencyId)).thenReturn(null);
        when(planDao.getSystemTemplate()).thenReturn(planEntity);
        when(planCategoryDao.listByPlanId(planId)).thenReturn(categoryEntities);
        when(usersMetaDataDao.getMeta(userId, "WEEKLY_PLAN_GUIDE")).thenReturn(null);

        // 调用接口
        CreatePlanResponse response = planService.create(createPlanRequest);

        // 验证
        verify(planCategoryDao, times(1)).batchCreate(anyList());
        verify(planDao, times(1)).create(any());
        verify(planUserDao, times(1)).create(any());

        // 结果校验
        Assert.assertEquals(response.isFirstVisit(), true);
    }

    /**
     * 测试批量创建 center 组
     */
    @Test
    public void testAddCenters() {
        // 模拟请求参数
        AddCentersRequest request = new AddCentersRequest();
        request.setPlanId("planId");
        request.setCenterNames(Arrays.asList("Math", "Blocks"));
        // 模拟用户信息
        AuthUserDetails user = new AuthUserDetails();
        user.setAgencyId("agencyId");
        user.setUsername("userId");

        // 模拟数据库原始数据
        when(userProvider.getCurrentUser()).thenReturn(user);

        // 调用测试方法
        planService.addCenters(request);

        // 验证批量删除 center 方法的调用次数为 1
        verify(planCenterDao, times(1)).batchDeleteByPlanId("planId");
        // 验证批量保存 center 方法的调用次数为 1
        verify(planCenterDao, times(1)).saveBatch(anyList());
    }

    /**
     * 测试更新周计划 Item 项
     */
    @Test
    public void testUpdatePlanItem() {
        // 创建模拟数据
        // 创建请求对象
        CreateItemRequest request = new CreateItemRequest();
        request.setId("id");
        request.setPlanId("planId");
        request.setName("newName");
        request.setCategoryId("newCategoryId");
        request.setDayOfWeek(1);
        request.setSortNum(0);
        request.setLessonId("lessonId");
        // 创建用户对象
        AuthUserDetails authUserDetails = new AuthUserDetails();
        authUserDetails.setAgencyId("agencyId");
        authUserDetails.setUsername("userId");
        authUserDetails.setRole("COLLABORATOR");
        // 创建用户实体
        UserEntity userEntity = new UserEntity();
        userEntity.setId("userId");
        userEntity.setRole("COLLABORATOR");
        // 创建周计划实体
        PlanEntity planEntity = new PlanEntity();
        planEntity.setId("planId");
        // 创建周计划项目实体
        ItemEntity planItemEntity = new ItemEntity();
        planItemEntity.setId("id");
        planItemEntity.setDayOfWeek(2);
        planItemEntity.setSortNum(1);
        planItemEntity.setPlanId("planId");
        planItemEntity.setCategoryId("categoryId");
        planItemEntity.setCenterId("centerId");
        planItemEntity.setName("name");
        planItemEntity.setLink("link2");
        // 创建课程实体
        LessonEntity lessonEntity = new LessonEntity();
        lessonEntity.setId("lessonId");
        // 创建旧位置的周计划项目实体列表
        List<ItemEntity> oldPositionItems = new ArrayList<>();
        ItemEntity oldPositionItem = new ItemEntity();
        oldPositionItem.setId("oldPositionItemId");
        oldPositionItem.setDayOfWeek(1);
        oldPositionItem.setSortNum(1);
        oldPositionItem.setPlanId("planId");
        oldPositionItem.setCategoryId("categoryId");
        oldPositionItem.setCenterId("centerId");
        oldPositionItems.add(oldPositionItem);
        // 创建新位置的周计划项目实体列表
        List<ItemEntity> newPositionItems = new ArrayList<>();
        ItemEntity newPositionItem = new ItemEntity();
        newPositionItem.setId("newPositionItemId");
        newPositionItem.setDayOfWeek(1);
        newPositionItem.setSortNum(1);
        newPositionItem.setPlanId("planId");
        newPositionItems.add(newPositionItem);

        // 模拟调用方法
        when(userProvider.checkCurrentUser()).thenReturn(authUserDetails);
        when(cacheService.get(anyString())).thenReturn(null);
        when(userProvider.checkUser("userId")).thenReturn(userEntity);
        when(planDao.get("planId")).thenReturn(planEntity);
        when(planItemDao.get("id")).thenReturn(planItemEntity);
        when(planItemMeasureDao.listByItemId("id")).thenReturn(new ArrayList<>());
        when(planItemEnrollmentDao.listByItemId(any())).thenReturn(new ArrayList<>());
        when(lessonDao.getById("lessonId")).thenReturn(lessonEntity);
        when(subjectDao.listBySourceIdIn(any())).thenReturn(new ArrayList<>());
        when(planItemDao.listByCategoryIdAndDayOfWeek("newCategoryId", 1)).thenReturn(newPositionItems);
        when(planItemDao.listByCenterId("centerId")).thenReturn(oldPositionItems);

        // 调用测试方法
        planService.updateItem(request);

        // 验证更新方法的调用次数
        verify(planItemDao, times(1)).update(any());
        verify(planItemDao, times(1)).batchUpdate(any());
    }

    /**
     * 测试获取单元下的周计划详情
     */
    @Test
    public void testGetUnitPlanDetail() {
        // 准备数据
        // 定义周计划 ID
        String planId = "planId001";
        String theme = "planThemeName";
        String overview = "planOverview";
        Integer week = 1;
        String frameworkId = "frameworkId001";
        String itemId1 = "itemEntityId001";
        String itemId2 = "itemEntityId002";
        String categoryId1 = "curriculumGenieTemplateCategoryId001";
        String categoryId2 = "curriculumGenieTemplateCategoryId002";
        String lessonId1 = "lessonId001";
        String lessonId2 = "lessonId002";
        String centerId1 = "centerId001";
        String centerId2 = "centerId002";
        // 定义周计划实体
        PlanEntity planEntity = new PlanEntity();
        planEntity.setId(planId);
        planEntity.setWeek(week);
        planEntity.setTheme(theme);
        planEntity.setOverview(overview);
        planEntity.setPlanFrameworkId(frameworkId);
        // 定义周计划模板实体对象
        PlanEntity curriculumGenieTemplate = new PlanEntity();
        curriculumGenieTemplate.setId("curriculumGenieTemplateId001");
        // 定义默认模板的分类
        List<CategoryEntity> curriculumGenieTemplateCategories = new ArrayList<>();
        CategoryEntity curriculumGenieTemplateCategory1 = new CategoryEntity();
        curriculumGenieTemplateCategory1.setId(categoryId1);
        curriculumGenieTemplateCategory1.setSortNum(1);
        CategoryEntity curriculumGenieTemplateCategory2 = new CategoryEntity();
        curriculumGenieTemplateCategory2.setId(categoryId2);
        curriculumGenieTemplateCategory2.setSortNum(2);
        curriculumGenieTemplateCategories.add(curriculumGenieTemplateCategory1);
        curriculumGenieTemplateCategories.add(curriculumGenieTemplateCategory2);
        // 定义周计划 Item 项
        List<ItemEntity> itemEntities = new ArrayList<>();
        ItemEntity itemEntity1 = new ItemEntity();
        itemEntity1.setId(itemId1);
        itemEntity1.setPlanId(planId);
        itemEntity1.setLessonId(lessonId1);
        itemEntity1.setCenterId("centerId001");
        itemEntity1.setFrameworkId("frameworkId002");
        itemEntity1.setCategoryId(categoryId1);
        ItemEntity itemEntity2 = new ItemEntity();
        itemEntity2.setId(itemId2);
        itemEntity2.setPlanId(planId);
        itemEntity2.setCenterId("centerId002");
        itemEntity2.setLessonId(lessonId2);
        itemEntity2.setFrameworkId("frameworkId003");
        itemEntity2.setCategoryId(categoryId1);
        itemEntities.add(itemEntity1);
        itemEntities.add(itemEntity2);
        // 定义周计划所有项目中关联的测评点信息
        List<ItemMeasureDetailEntity> itemMeasureDetailEntities = new ArrayList<>();
        ItemMeasureDetailEntity itemMeasureDetailEntity1 = new ItemMeasureDetailEntity();
        itemMeasureDetailEntity1.setMeasureId("measureId001");
        itemMeasureDetailEntity1.setMeasureName("measureName001");
        itemMeasureDetailEntity1.setItemId(itemId1);
        itemMeasureDetailEntity1.setCore(true);
        itemMeasureDetailEntity1.setItemId(itemId1);
        ItemMeasureDetailEntity itemMeasureDetailEntity2 = new ItemMeasureDetailEntity();
        itemMeasureDetailEntity2.setMeasureId("measureId002");
        itemMeasureDetailEntity2.setItemId(itemId2);
        itemMeasureDetailEntity2.setMeasureName("measureName002");
        itemMeasureDetailEntity2.setItemId(itemId2);
        itemMeasureDetailEntities.add(itemMeasureDetailEntity1);
        itemMeasureDetailEntities.add(itemMeasureDetailEntity2);
        // 定义用户 ID
        String userId = "userId001";
        // 定义机构实体对象
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("agencyId001");
        // 定义 LessonIds 集合
        List<String> lessonIds = new ArrayList<>();
        lessonIds.add(lessonId1);
        lessonIds.add(lessonId2);
        List<String> centerIds = new ArrayList<>();
        centerIds.add(centerId1);
        centerIds.add(centerId2);
        List<PlanCenterEntity> planCenterEntities = new ArrayList<>();
        PlanCenterEntity planCenterEntity1 = new PlanCenterEntity();
        planCenterEntity1.setId(centerId1);
        planCenterEntity1.setName("centerName001");
        planCenterEntity1.setPlanId("planId001");
        planCenterEntity1.setCreateUserId(userId);
        PlanCenterEntity planCenterEntity2 = new PlanCenterEntity();
        planCenterEntity2.setId(centerId2);
        planCenterEntity2.setPlanId("planId001");
        planCenterEntity2.setCreateUserId(userId);
        planCenterEntity2.setName("centerName002");
        planCenterEntities.add(planCenterEntity1);
        planCenterEntities.add(planCenterEntity2);
        // 测评点集合
        List<KeyMeasureEntity> keyMeasures = new ArrayList<>();
        KeyMeasureEntity keyMeasureEntity = new KeyMeasureEntity();
        keyMeasureEntity.setMeasureId("measureId001");
        keyMeasureEntity.setAgencyId(agencyModel.getId());
        keyMeasureEntity.setDomainId("domainId001");
        keyMeasureEntity.setKeyMeasureSettingId("keyMeasureSettingId001");
        keyMeasures.add(keyMeasureEntity);
        //
        List<PlanCenterEntity> allPlanCenterEntities = new ArrayList<>();
        PlanCenterEntity allPlanCenterEntity1 = new PlanCenterEntity();
        allPlanCenterEntity1.setId("allCenterId001");
        allPlanCenterEntity1.setName("allCenterName001");
        PlanCenterEntity allPlanCenterEntity2 = new PlanCenterEntity();
        allPlanCenterEntity2.setId("allCenterId002");
        allPlanCenterEntity2.setName("allCenterName002");
        allPlanCenterEntities.add(allPlanCenterEntity1);
        allPlanCenterEntities.add(allPlanCenterEntity2);
        CurriculumUnitPlanEntity curriculumUnitPlanEntity = new CurriculumUnitPlanEntity();
        curriculumUnitPlanEntity.setPlanId(planId);
        curriculumUnitPlanEntity.setUnitId("unitId001");
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setId("unitId001");
        unit.setGrade("TK (4-5)");
        // 模拟数据
        when(planDao.get(planId)).thenReturn(planEntity);
        when(planDao.getCurriculumGenieTemplate()).thenReturn(curriculumGenieTemplate);
        when(planCategoryDao.listByPlanId(curriculumGenieTemplate.getId())).thenReturn(curriculumGenieTemplateCategories);
        when(planItemDao.listByPlanId(planId)).thenReturn(itemEntities);
        when(planItemMeasureDao.listItemMeasureDetails(planId)).thenReturn(itemMeasureDetailEntities);
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        when(userProvider.getAgencyOpenDefaultClose(agencyModel.getId(), "DRDP_KEY_MEASURE_SETTING")).thenReturn(true);
        when(domainDao.getKeyMeasuresSetting(agencyModel.getId(), Collections.singletonList("frameworkId003"))).thenReturn(keyMeasures);
        when(lessonDao.getByIds(lessonIds)).thenReturn(new ArrayList<>());
        when(planCenterDao.queryPlanCentersByPlanId(planId)).thenReturn(allPlanCenterEntities);
        when(curriculumUnitPlanDao.getUnitPlanByPlanId(planId)).thenReturn(curriculumUnitPlanEntity);
        when(curriculumUnitDao.getById(anyString())).thenReturn(unit);
        when(planCenterDao.listByIds(anyList())).thenReturn(planCenterEntities);

        // 调用接口
        PlanDetailResponse unitPlanDetail = planService.getUnitPlanDetail(planId);

        // 验证结果
        Assert.assertEquals(null != unitPlanDetail, true);
        Assert.assertEquals(unitPlanDetail.getTheme(), theme);
        Assert.assertEquals(unitPlanDetail.getWeek(), week);
        Assert.assertEquals(unitPlanDetail.getFrameworkId(), frameworkId);
    }

    /**
     * 保存 center 活动变化设置测试
     * Case: 保存用户自定义设置
     */
    @Test
    public void testSaveCentersActivityChangeSettingWithCreate() {
        // 模拟请求参数
        SaveActivityChangeSettingRequest request = new SaveActivityChangeSettingRequest();
        request.setSystemType(true);
        request.setIsStation(false);

        when(userProvider.getCurrentUserId()).thenReturn("userId"); // 模拟当前用户
        when(usersMetaDataDao.getMeta("userId", "PLAN_CENTERS_CHANGE_SETTING")).thenReturn(null); // 模拟获取用户自定义设置

        // 调用测试方法
        planService.saveCentersActivityChangeSetting(request);

        // 验证保存 metadata 方法的调用次数为 1
        verify(usersMetaDataDao, times(1)).setMeta("userId", "PLAN_CENTERS_CHANGE_SETTING", JsonUtil.toJson(request));
    }

    /**
     * 保存 center 活动变化设置测试
     * Case: 更新用户自定义设置
     */
    @Test
    public void testSaveCentersActivityChangeSettingWithUpdate() {
        // 模拟请求参数
        SaveActivityChangeSettingRequest request = new SaveActivityChangeSettingRequest();
        request.setSystemType(true);
        request.setIsStation(false);
        request.setCenterNames(Arrays.asList("Math", "Blocks"));

        when(userProvider.getCurrentUserId()).thenReturn("userId"); // 模拟当前用户
        when(usersMetaDataDao.getMeta("userId", "PLAN_CENTERS_CHANGE_SETTING")).thenReturn(new UsersMetaDataEntity()); // 模拟获取用户自定义设置

        // 调用测试方法
        planService.saveCentersActivityChangeSetting(request);

        // 验证更新 metadata 方法的调用次数为 1
        verify(usersMetaDataDao, times(1)).updateMeta("PLAN_CENTERS_CHANGE_SETTING", JsonUtil.toJson(request), "userId");
    }

    /**
     * 获取 center 活动变化设置测试
     * Case: 用户未设置过，返回系统设置
     */
    @Test
    public void testGetCentersActivityChangeSetting() {
        // 模拟用户数据
        when(userProvider.getCurrentUserId()).thenReturn("userId"); // 模拟当前用户
        when(usersMetaDataDao.getMeta("userId", "PLAN_CENTERS_CHANGE_SETTING")).thenReturn(null); // 模拟获取用户自定义设置

        // 调用测试方法
        GetCentersActivityChangeSettingResponse response = planService.getCentersActivityChangeSetting(false);

        // 断言响应的活动类型设置应为系统设置
        Assertions.assertEquals(true, response.getSystemType());
    }

    /**
     * 获取 center 活动变化设置测试
     * Case: 用户设置过，返回用户设置
     */
    @Test
    public void testGetCentersActivityChangeSetting2() {
        // 模拟用户设置的 center 组变化数据
        UsersMetaDataEntity usersMetaDataEntity = new UsersMetaDataEntity();
        usersMetaDataEntity.setMetaKey("PLAN_CENTERS_CHANGE_SETTING");
        usersMetaDataEntity.setMetaValue("{\"systemType\":false,\"centerNames\":[\"Math\",\"Blocks\"]}");

        // 模拟用户数据
        when(userProvider.getCurrentUserId()).thenReturn("userId"); // 模拟当前用户
        when(usersMetaDataDao.getMeta("userId", "PLAN_CENTERS_CHANGE_SETTING")).thenReturn(usersMetaDataEntity); // 模拟获取用户自定义设置

        // 调用测试方法
        GetCentersActivityChangeSettingResponse response = planService.getCentersActivityChangeSetting(false);

        // 断言响应的活动类型设置应为用户设置
        Assertions.assertEquals(false, response.getSystemType());
        Assertions.assertEquals(Arrays.asList("Math", "Blocks"), response.getCenterNames());
    }

    /**
     * 测试根据自定义设置获取本周不变的和变化的 centers 组数据
     */
    @Test
    public void testGetUnchangeableCenterActivityOverviews() {
        // 模拟数据
        String currentPlanId = "currentPlanId"; // 当前周计划 ID
        String lastWeeklyPlanId = "lastPlanId"; // 上一周计划 ID
        List<PlanCenterEntity> lastPlanCenters = new ArrayList<>(); // 上一周计划的 center 组
        List<PlanCenterEntity> currentPlanCenterEntities = new ArrayList<>(); // 当前周计划的 center 组
        PlanCenterEntity currentPlanCenter = new PlanCenterEntity();
        currentPlanCenter.setId("centerId");
        currentPlanCenter.setName("Math");
        currentPlanCenterEntities.add(currentPlanCenter);

        // 模拟用户设置的 center 组变化数据
        UsersMetaDataEntity usersMetaDataEntity = new UsersMetaDataEntity();
        usersMetaDataEntity.setMetaKey("PLAN_CENTERS_CHANGE_SETTING");
        usersMetaDataEntity.setMetaValue("{\"systemType\":false,\"centerNames\":[\"Math\",\"Blocks\"]}");

        // 模拟用户数据
        when(userProvider.getCurrentUserId()).thenReturn("userId"); // 模拟当前用户
        when(usersMetaDataDao.getMeta("userId", "PLAN_CENTERS_CHANGE_SETTING")).thenReturn(usersMetaDataEntity); // 模拟获取用户自定义设置
        when(planCenterDao.queryPlanCentersByPlanId(lastWeeklyPlanId)).thenReturn(lastPlanCenters); // 模拟获取上一周计划的 center 组
        when(planCenterDao.queryPlanCentersByPlanId(currentPlanId)).thenReturn(currentPlanCenterEntities); // 模拟获取当前周计划的 center 组
        when(planItemDao.listCenterItemsByPlanId(lastWeeklyPlanId)).thenReturn(new ArrayList<>()); // 模拟获取上一周计划的 center 组下的活动项

        // 调用测试方法
        GetPlanCentersActivityOverviewResponse unchangeableCenterActivityOverviews = planService.getUnchangeableCenterActivityOverviews(currentPlanId, 2, lastWeeklyPlanId, false);

        // 验证数据
        Assertions.assertEquals(unchangeableCenterActivityOverviews.getChangedItems().size(), 1); // 验证变化的 center 组数量应为1
        Assertions.assertEquals(unchangeableCenterActivityOverviews.getChangedItems().get(0).getCenterGroupName(), "Math"); // 验证不变化的 center 组数量应为1
        Assertions.assertEquals(unchangeableCenterActivityOverviews.getUnchangedItems().size(), 0); // 验证不变化的 center 组数量应为0
    }


    /**
     * 测试根据 includeCenterLesson 设置获取本周所有的课程，如果 includeCenterLesson 为 true，就返回所有的课程，否则返回不包含 center 组的课程
     * 测试场景：includeCenterLesson 为 true
     */
    @Test
    public void testGetPlanLessons() {
        // 准备数据
        // 定义周计划 ID
        String planId = UUID.randomUUID().toString();

        // 定义周计划实体
        PlanEntity planEntity = new PlanEntity();
        // 定义周计划项目实体
        ItemEntity itemEntity = new ItemEntity();
        // 设置 ID
        itemEntity.setId(UUID.randomUUID().toString());
        // 设置 category
        itemEntity.setCategoryId(UUID.randomUUID().toString());
        // 设置周计划项目课程的 ID
        String lessonId = UUID.randomUUID().toString();
        itemEntity.setLessonId(lessonId);

        // 定义周计划项目实体
        ItemEntity itemEntity2 = new ItemEntity();
        // 设置 ID
        itemEntity2.setId(UUID.randomUUID().toString());
        // 设置 centerId
        String centerId = UUID.randomUUID().toString();
        // 设置 centerId
        itemEntity2.setCenterId(centerId);
        // 设置周计划项目课程的 ID
        String lessonId2 = UUID.randomUUID().toString();
        itemEntity2.setLessonId(lessonId2);
        // 定义 center 组实体
        LessonEntity lessonEntity = new LessonEntity();
        // 设置创建者 ID
        lessonEntity.setCreateUserId(UUID.randomUUID().toString());
        lessonEntity.setId(lessonId);

        // 定义课程实体
        LessonEntity lessonEntity2 = new LessonEntity();
        // 设置创建者 ID
        String createUserId = UUID.randomUUID().toString();
        lessonEntity2.setCreateUserId(createUserId);
        lessonEntity2.setId(lessonId2);

        // 定义 center 组实体
        PlanCenterEntity planCenterEntity = new PlanCenterEntity();
        // 设置 ID
        planCenterEntity.setId(centerId);
        // 设置 center 的创建者 ID
        planCenterEntity.setCreateUserId(createUserId);
        // 设置 center 组的所属 planId
        planCenterEntity.setPlanId(planId);


        // 模拟数据
        // 当调用 planDao.get 方法时，返回 planEntity
        Mockito.when(planDao.get(planId)).thenReturn(planEntity);
        // 当调用 planItemDao.listByPlanId 方法时，返回 itemEntity
        Mockito.when(planItemDao.listByPlanId(planId)).thenReturn(Arrays.asList(itemEntity, itemEntity2));
        // 当调用 lessonDao.getByIds 方法时，返回 lessonEntity
        Mockito.when(lessonDao.getByIds(anyList())).thenReturn(Arrays.asList(lessonEntity, lessonEntity2));
        // 当调用 planCenterDao.listByIds 方法时，返回 planCenterEntity
        Mockito.when(planCenterDao.listByIds(anySet())).thenReturn(Arrays.asList(planCenterEntity));
        // 当调用 planCenterDao.queryPlanCentersByPlanId 方法时，返回 planCenterEntity
        Mockito.when(planCenterDao.queryPlanCentersByPlanId(planId)).thenReturn(Arrays.asList(planCenterEntity));

        // 调用测试方法
        GetPlanLessonsResponse response = planService.getPlanLessons(planId, true);

        // 验证结果
        Mockito.verify(planDao, Mockito.times(1)).get(planId);
        Mockito.verify(planItemDao, Mockito.times(1)).listByPlanId(planId);
        Mockito.verify(lessonDao, Mockito.times(1)).getByIds(anyList());
        Mockito.verify(planCenterDao, Mockito.times(1)).listByIds(anySet());
        Mockito.verify(planCenterDao, Mockito.times(1)).queryPlanCentersByPlanId(planId);

        // 断言结果
        Assertions.assertNotNull(response);
        Assertions.assertFalse(response.getLessons().isEmpty());
    }

    /**
     * 测试根据 includeCenterLesson 设置获取本周所有的课程，如果 includeCenterLesson 为 true，就返回所有的课程，否则返回不包含 center 组的课程
     * 测试场景：includeCenterLesson 为 true，但是没有 planId
     */
    @Test(expected = BusinessException.class)
    public void testGetPlanLessonsWithNullPlanId() {
        // Call the method with null planId
        planService.getPlanLessons(null, true);
    }

    /**
     * 测试根据 includeCenterLesson 设置获取本周所有的课程，如果 includeCenterLesson 为 true，就返回所有的课程，否则返回不包含 center 组的课程
     * 测试场景：includeCenterLesson 为 true，但是没有 planId
     */
    @Test(expected = BusinessException.class)
    public void testGetPlanLessonsWithEmptyPlanId() {
        // Call the method with empty planId
        planService.getPlanLessons("", true);
    }

    /**
     * 测试根据 includeCenterLesson 设置获取本周所有的课程，如果 includeCenterLesson 为 true，就返回所有的课程，否则返回不包含 center 组的课程
     * 测试场景：数据库中没有对应的 plan
     */
    @Test(expected = BusinessException.class)
    public void testGetPlanLessonsWithNonExistentPlanId() {
        // Prepare data
        String planId = UUID.randomUUID().toString();

        // Mock behaviors
        Mockito.when(planDao.get(planId)).thenReturn(null);

        // Call the method
        planService.getPlanLessons(planId, true);
    }

    /**
     * 测试创建批量改编课程任务
     */
    @Test
    public void testCreateBatchAdaptLessonTask() {
        // 准备数据，模拟请求参数
        CreateBatchAdaptLessonTaskRequest request = new CreateBatchAdaptLessonTaskRequest();
        request.setGroupId("groupId"); // 设置班级 ID
        request.setPlanId("planId"); // 设置周计划 ID
        List<AdaptItemModel> adaptItems = new ArrayList<>();
        AdaptItemModel adaptItemModel = new AdaptItemModel();
        adaptItemModel.setLessonId("lessonId01");
        adaptItemModel.setItemId("item01");
        adaptItems.add(adaptItemModel);
        request.setAdaptItems(adaptItems); // 设置改编课程项

        // 模拟方法调用
        when(userProvider.getCurrentUserId()).thenReturn("userId");
        when(userProvider.getCurrentAgencyId()).thenReturn("agencyId");

        // 调用测试方法
        SuccessResponse response = planService.createBatchAdaptLessonTask(request);

        // 验证方法调用次数
        verify(lessonAdaptRecordDao, times(1)).saveBatch(anyList()); // 验证保存改编课程任务方法调用次数为1
        verify(lessonAdaptRecordDao, times(1)).updateStatusByIds(anyList(), eq(TaskStatus.PENDING.toString())); // 验证更新改编课程任务状态方法调用次数为1
        verify(remoteProvider, times(1)).callAdaptLessonTaskService(anyString()); // 验证调用远程服务方法调用次数为1
        Assert.assertTrue(response.isSuccess()); // 验证响应结果应为成功
    }

    /**
     * 测试改编课程任务
     */
    @Test
    public void testAdaptLesson() {
        // 模拟任务 ID
        String taskId = "taskId";
        // 调用测试方法
        SuccessResponse successResponse = planService.adaptLesson(taskId);
        // 验证改变课程方法调用次数为 1
        verify(lessonService, times(1)).adaptLesson(taskId);
        // 断言结果为成功，且任务 ID 为 taskId
        Assertions.assertTrue(successResponse.isSuccess());
        Assertions.assertEquals(successResponse.getId(), taskId);
    }

    /**
     * 测试批量改编课程任务
     */
    @Test
    public void testBatchAdaptLessonTasks() {
        // 准备数据
        String taskIds = "task01,task02,task03";

        // 调用测试方法
        planService.batchAdaptLessonTasks(taskIds);

        // 验证方法调用次数
        verify(lessonAdaptRecordDao, times(1)).updateStatusByIds(anyList(), eq(TaskStatus.PENDING.toString())); // 验证更新改编课程任务状态方法调用次数为1
        verify(remoteProvider, times(3)).callAdaptLessonTaskService(anyString()); // 验证调用远程服务方法调用次数为 3
    }

    /**
     * 测试获取批量改编课程任务列表
     */
    @Test
    public void testGetBatchAdaptLessons() {
        // 数据模拟
        String batchId = "batchId"; // 批量改编 ID
        // 模拟改编记录实体
        List<LessonAdaptRecordEntity> lessonAdaptRecords = new ArrayList<>();
        LessonAdaptRecordEntity record01 = new LessonAdaptRecordEntity();
        record01.setId("record01");
        record01.setItemId("item01");
        record01.setBatchId(batchId);
        record01.setLessonId("lessonId");
        record01.setStatus(TaskStatus.SUCCESS.toString());
        record01.setAdaptedLessonId("adaptedLessonId");
        lessonAdaptRecords.add(record01);
        // 模拟周计划项实体
        List<ItemEntity> items = new ArrayList<>();
        ItemEntity item01 = new ItemEntity();
        item01.setId("item01");
        item01.setName("lesson01");
        item01.setPlanId("planId");
        item01.setDayOfWeek(1);
        item01.setSortNum(1);
        item01.setCategoryId("categoryId");
        item01.setCenterId("centerId");
        items.add(item01);
        // 模拟周计划 Center 分组实体
        List<PlanCenterEntity> planCenters = new ArrayList<>();
        PlanCenterEntity center = new PlanCenterEntity();
        center.setId("centerId");
        center.setName("centerName");
        planCenters.add(center);

        // 模拟方法调用
        when(lessonAdaptRecordDao.getAdaptRecordsByBatchId(batchId)).thenReturn(lessonAdaptRecords);
        when(planItemDao.getByIds(Collections.singletonList("item01"))).thenReturn(items);
        when(planCenterDao.listByCenterIdIn(Collections.singletonList("centerId"))).thenReturn(planCenters);

        // 调用测试方法
        GetBatchAdaptLessonsResponse response = planService.getBatchAdaptLessons(batchId);

        // 断言验证
        Assertions.assertEquals(response.getTasks().size(), 1); // 任务数量应为 1
        Assertions.assertEquals(response.getTasks().get(0).getLessonId(), "adaptedLessonId"); // 第一个任务的课程 ID 应为改编完成的课程 ID
        Assertions.assertEquals(response.getTasks().get(0).getLessonTitle(), "lesson01"); // 第一个任务的课程名称应为 lesson01
        Assertions.assertEquals(response.getTasks().get(0).getCenterName(), "centerName"); // 第一个任务的 Center 分组名称应为 centerName
        Assertions.assertTrue(response.isCompleted()); // 任务状态应为完成
    }

    /**
     * 测试隐藏班级信息确认弹窗
     * case: 用户未隐藏过班级信息确认弹窗
     */
    @Test
    public void testHideGroupInfoConfirmTipWithoutMetaData() {
        // 准备数据
        String groupId = "groupId001";
        String userId = "userId001";

        // 模拟方法调用
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(usersMetaDataDao.getMeta(userId, "NOT_SHOW_CLASS_CONFIRMATION")).thenReturn(null);

        // 调用测试方法
        SuccessResponse response = planService.hideGroupInfoConfirmTip(groupId);

        // 验证方法调用次数
        verify(usersMetaDataDao, times(1)).insertMeta("NOT_SHOW_CLASS_CONFIRMATION", "[\"groupId001\"]", userId);
        // 断言结果
        Assert.assertTrue(response.isSuccess());
    }

    /**
     * 测试隐藏班级信息确认弹窗
     * case: 用户隐藏其他班级信息确认弹窗
     */
    @Test
    public void testHideGroupInfoConfirmTipChangeOtherGroup() {
        // 准备数据
        String groupId = "groupId001";
        String userId = "userId001";
        UsersMetaDataEntity metaDataEntity = new UsersMetaDataEntity();
        metaDataEntity.setMetaKey("NOT_SHOW_CLASS_CONFIRMATION");
        metaDataEntity.setMetaValue("[\"groupId002\"]");
        metaDataEntity.setUserId(userId);

        // 模拟方法调用
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(usersMetaDataDao.getMeta(userId, "NOT_SHOW_CLASS_CONFIRMATION")).thenReturn(metaDataEntity);

        // 调用测试方法
        SuccessResponse response = planService.hideGroupInfoConfirmTip(groupId);

        // 验证方法调用次数
        verify(usersMetaDataDao, times(1)).updateMeta("NOT_SHOW_CLASS_CONFIRMATION", "[\"groupId002\",\"groupId001\"]", userId);
        // 断言结果
        Assert.assertTrue(response.isSuccess());
    }

    /**
     * 测试设置班级改编课程是否开启老师分组
     * case: 开启老师分组
     */
    @Test
    public void testSetTeacherGroupEnabledWithStartFeature() {
        // 准备数据
        String groupId = "groupId001";
        String userId = "userId001";

        // 模拟方法调用
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(groupsMetaDataDao.getMeta(groupId, "ENABLED_TEACHER_GROUP")).thenReturn(null);

        // 调用测试方法
        SuccessResponse response = planService.setTeacherGroupEnabled(groupId, true);

        // 验证方法调用次数
        verify(groupsMetaDataDao, times(1)).insertMeta(groupId, "ENABLED_TEACHER_GROUP", "true");
        // 断言结果
        Assert.assertTrue(response.isSuccess());
    }

/**
     * 测试设置班级改编课程是否开启老师分组
     * case: 关闭老师分组
     */
    @Test
    public void testSetTeacherGroupEnabledWithCloseFeature() {
        // 准备数据
        String groupId = "groupId001";
        String userId = "userId001";
        GroupEntity group = new GroupEntity();
        group.setId(groupId);
        GroupsMetaDataEntity metaDataEntity = new GroupsMetaDataEntity();
        metaDataEntity.setMetaKey("ENABLED_TEACHER_GROUP");
        metaDataEntity.setGroup(group);
        metaDataEntity.setMetaValue("true");

        // 模拟方法调用
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(groupsMetaDataDao.getMeta(groupId, "ENABLED_TEACHER_GROUP")).thenReturn(metaDataEntity);

        // 调用测试方法
        SuccessResponse response = planService.setTeacherGroupEnabled(groupId, false);

        // 验证方法调用次数
        verify(groupsMetaDataDao, times(1)).updateMeta(groupId, "ENABLED_TEACHER_GROUP", "false");
        // 断言结果
        Assert.assertTrue(response.isSuccess());
    }

    /**
     * 测试获取班级改编课程设置
     * case: 用户未设置过，返回系统默认设置状态值
     */
    @Test
    public void testGetGroupAdaptSettingWithoutMetaData() {
        // 准备数据
        String groupId = "groupId001";
        String userId = "userId001";

        // 模拟方法调用
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(usersMetaDataDao.getMeta(userId, "NOT_SHOW_CLASS_CONFIRMATION")).thenReturn(null);
        when(groupsMetaDataDao.getMeta(groupId, "ENABLED_TEACHER_GROUP")).thenReturn(null);

        // 调用测试方法
        GetGroupAdaptSettingResponse response = planService.getGroupAdaptSetting(groupId);

        // 断言结果
        Assert.assertFalse(response.isShowConfirmTip());
        Assert.assertFalse(response.isTeacherGroupEnabled());
    }

    /**
     * 测试获取班级改编课程设置
     * case: 用户设置过，返回用户设置状态值
     */
    @Test
    public void testGetGroupAdaptSetting() {
        // 准备数据
        String groupId = "groupId001";
        String userId = "userId001";
        UsersMetaDataEntity metaDataEntity = new UsersMetaDataEntity();
        metaDataEntity.setMetaKey("NOT_SHOW_CLASS_CONFIRMATION");
        metaDataEntity.setMetaValue("[\"groupId001\"]");
        metaDataEntity.setUserId(userId);
        GroupEntity group = new GroupEntity();
        group.setId(groupId);
        GroupsMetaDataEntity groupsMetaDataEntity = new GroupsMetaDataEntity();
        groupsMetaDataEntity.setMetaKey("ENABLED_TEACHER_GROUP");
        groupsMetaDataEntity.setGroup(group);
        groupsMetaDataEntity.setMetaValue("true");

        // 模拟方法调用
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(usersMetaDataDao.getMeta(userId, "NOT_SHOW_CLASS_CONFIRMATION")).thenReturn(metaDataEntity);
        when(groupsMetaDataDao.getMeta(groupId, "ENABLED_TEACHER_GROUP")).thenReturn(groupsMetaDataEntity);

        // 调用测试方法
        GetGroupAdaptSettingResponse groupAdaptSetting = planService.getGroupAdaptSetting(groupId);

        // 断言结果
        Assert.assertTrue(groupAdaptSetting.isShowConfirmTip());
        Assert.assertTrue(groupAdaptSetting.isTeacherGroupEnabled());
    }

    /**
     * 验证获取 engagement resources
     */
    @Test
    public void testListEngagementResources() {
        String planId = "test-planId001";
        PlanEntity planEntity = new PlanEntity();
        planEntity.setId(planId);
        planEntity.setFromAtLocal(TimeUtil.parseDate("2024-06-10"));
        planEntity.setToAtLocal((TimeUtil.parseDate("2024-06-20")));

        List<CurriculumBookEntity> curriculumBookEntities = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            CurriculumBookEntity curriculumBookEntity = new CurriculumBookEntity();
            curriculumBookEntity.setId("test-curriculumBookId001");
            curriculumBookEntity.setPlanId(planId);
            curriculumBookEntity.setUnitId("test-unitId001");
            curriculumBookEntity.setPlanItemId("test-planItemId001");
            curriculumBookEntity.setBook("book-" + i);
            if (i == 0) {
                curriculumBookEntity.setBookType("BOOK");
            } else {
                curriculumBookEntity.setBookType("VIDEOBOOK");
            }
            curriculumBookEntities.add(curriculumBookEntity);
        }

        List<CurriculumAttachmentEntity> curriculumAttachmentEntities = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            CurriculumAttachmentEntity curriculumAttachmentEntity = new CurriculumAttachmentEntity();
            curriculumAttachmentEntity.setId("test-curriculumAttachmentId001");
            curriculumAttachmentEntity.setPlanId(planId);
            curriculumAttachmentEntity.setUnitId("test-unitId001");
            curriculumAttachmentEntity.setPlanItemId("test-planItemId001");
            curriculumAttachmentEntity.setMediaId("test-mediaId001");
            curriculumAttachmentEntity.setType("type-" + i);
            curriculumAttachmentEntities.add(curriculumAttachmentEntity);
        }

        List<CurriculumVocabularyEntity> curriculumVocabularyEntities = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            CurriculumVocabularyEntity curriculumVocabularyEntity = new CurriculumVocabularyEntity();
            curriculumVocabularyEntity.setId("test-curriculumVocabularyId001");
            curriculumVocabularyEntity.setPlanId(planId);
            curriculumVocabularyEntity.setUnitId("test-unitId001");
            curriculumVocabularyEntity.setPlanItemId("test-planItemId001");
            curriculumVocabularyEntity.setContent("content-" + i);
            curriculumVocabularyEntities.add(curriculumVocabularyEntity);
        }

        List<ItemEntity> itemEntities = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            ItemEntity itemEntity = new ItemEntity();
            itemEntity.setId("test-itemId001");
            itemEntity.setPlanId(planId);
            itemEntity.setName("name-" + i);
            itemEntity.setCategoryId("test-categoryId001");
            itemEntity.setDayOfWeek(i);
            itemEntity.setDescription("description-" + i);
            itemEntity.setLessonId("test-lessonId00" + i);
            itemEntities.add(itemEntity);
        }

        List<LessonEntity> lessons = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            LessonEntity lessonEntity = new LessonEntity();
            lessonEntity.setId("test-lessonId00" + i);
            lessonEntity.setName("name-" + i);
            lessonEntity.setType("type-" + i);
            lessonEntity.setAttachmentMediaIds("test-attachmentMediaIds001");
            lessonEntity.setCoverMediaIds("test-coverMediaIds001");
            lessonEntity.setCoverExternalMediaUrlId("test-coverExternalMediaUrlId001");
            lessonEntity.setHomeActivities("test-homeActivities001");
            lessonEntity.setUniversalDesignForLearning("test-universalDesignForLearning001");
            lessons.add(lessonEntity);
        }

        List<SubjectEntity> subjects = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            SubjectEntity subjectEntity = new SubjectEntity();
            subjectEntity.setId("test-subjectId00" + i);
            subjectEntity.setTitle("title-" + i);
            subjectEntity.setContent("content-" + i);
            subjectEntity.setSourceId("test-sourceId00" + i);
            subjectEntity.setSourceType("sourceType-" + i);
            subjectEntity.setCreateUserId("test-createUserId00" + i);
            subjectEntity.setCreateAtUtc(new Date());
            subjectEntity.setDeleted(false);
            subjects.add(subjectEntity);
        }

        List<SubjectLanguageEntity> subjectLanguages = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            SubjectLanguageEntity subjectLanguageEntity = new SubjectLanguageEntity();
            subjectLanguageEntity.setId("test-subjectLanguageId00" + i);
            subjectLanguageEntity.setSubjectId("test-subjectId00" + i);
            subjectLanguageEntity.setLangCode("langCode-" + i);
            subjectLanguageEntity.setContent("content-" + i);
            subjectLanguages.add(subjectLanguageEntity);
        }

        List<LanguageEntity> languages = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            LanguageEntity languageEntity = new LanguageEntity();
            languageEntity.setId("test-languageId00" + i);
            languageEntity.setLangCode("langCode-" + i);
            languageEntity.setName("name-" + i);
            languageEntity.setOriginalName("originalName-" + i);
            languageEntity.setSupport(true);
            languageEntity.setTtsCode("ttsCode-" + i);
            languageEntity.setTtsVoice("ttsVoice-" + i);
            languages.add(languageEntity);
        }

        List<SubjectMediaEntity> subjectMedias = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            SubjectMediaEntity subjectMediaEntity = new SubjectMediaEntity();
            subjectMediaEntity.setId("test-subjectMediaId00" + i);
            subjectMediaEntity.setSubjectId("test-subjectId00" + i);
            subjectMediaEntity.setMediaId("test-mediaId00" + i);
            subjectMedias.add(subjectMediaEntity);
        }

        List<MediaEntity> medias = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            MediaEntity mediaEntity = new MediaEntity();
            mediaEntity.setId("test-mediaId00" + i);
            mediaEntity.setLocalId("test-localId00" + i);
            mediaEntity.setCreateAtUtc(new Date());
            mediaEntity.setCreateByUserId("test-createByUserId00" + i);
            mediaEntity.setMimeType("mimeType-" + i);
            mediaEntity.setSize(100L);
            mediaEntity.setAnnexType("annexType-" + i);
            mediaEntity.setWeb(true);
            mediaEntity.setCompressed(true);
            mediaEntity.setHaveSmall(true);
            mediaEntity.setHaveMedium(true);
            mediaEntity.setPrivateFile(true);
            mediaEntity.setPartitionKey((short) 1);
            mediaEntity.setFileType("fileType-" + i);
            mediaEntity.setSnapshotPath("snapshotPath-" + i);
            mediaEntity.setRelativePath("relativePath-" + i);
            mediaEntity.setDynamicData("dynamicData-" + i);
            mediaEntity.setFileName("fileName-" + i);
            mediaEntity.setWidth(100);
            mediaEntity.setHeight(100);
            mediaEntity.setVoiceTime("voiceTime-" + i);
            medias.add(mediaEntity);
        }

        String url = "demo-url";

        // 模拟请求
        lenient().when(planDao.get(planId)).thenReturn(planEntity);
        lenient().when(curriculumBookDao.listCustomByPlanId(planId)).thenReturn(curriculumBookEntities);
        lenient().when(curriculumAttachmentDao.listCustomByPlanId(planId)).thenReturn(curriculumAttachmentEntities);
        lenient().when(curriculumVocabularyDao.listCustomByPlanId(planId)).thenReturn(curriculumVocabularyEntities);
        lenient().when(planItemDao.listByPlanId(planId)).thenReturn(itemEntities);
        lenient().when(lessonDao.getByIds(itemEntities.stream().map(ItemEntity::getLessonId).collect(Collectors.toList()))).thenReturn(lessons);
        lenient().when(subjectDao.listBySourceIdIn(anyList())).thenReturn(subjects);
        lenient().when(subjectLanguageDao.listBySubjectIdIn(anyList())).thenReturn(subjectLanguages);
        lenient().when(languageDao.listByLangCodes(subjectLanguages.stream().map(SubjectLanguageEntity::getLangCode).collect(Collectors.toList()))).thenReturn(languages);
        lenient().when(subjectMediaDao.listBySubjectIdIn(subjects.stream().map(SubjectEntity::getId).collect(Collectors.toList()))).thenReturn(subjectMedias);
        lenient().when(mediaEntityDao.listByIdIn(anyList())).thenReturn(medias);
        lenient().when(fileSystem.getPublicUrl(anyString())).thenReturn(url);

        // 调用接口
        ListEngagementResourcesResponse response = planService.listEngagementResources(planId);
        // 校验
        assertNotNull(response);
    }

    /**
     * 测试批量更新周计划项课程
     */
    @Test
    public void testBatchUpdatePlanItemLesson() {
        // 请求体
        BatchUpdateUnitPlanItemLessonRequest request = JsonUtil.fromJson("{\"unitId\":\"unitId\",\"plans\":[{\"planId\":\"plan1\",\"items\":[{\"itemId\":\"item1\",\"lessonId\":\"lesson1\",\"lessonName\":\"lesson1\"},{\"itemId\":\"item2\",\"lessonId\":\"lesson2\",\"lessonName\":\"lesson2\"}]},{\"planId\":\"plan2\",\"items\":[{\"itemId\":\"item3\",\"lessonId\":\"lesson3\",\"lessonName\":\"lesson3\"},{\"itemId\":\"item4\",\"lessonId\":\"lesson4\",\"lessonName\":\"lesson4\"}]}]}", BatchUpdateUnitPlanItemLessonRequest.class);

        when(planItemDao.getByIds(anyList())).thenReturn(new ArrayList<>());

        // 调用测试方法
        planService.batchUpdateUnitPlanItemLesson(request);

        // 验证方法调用次数
        verify(planItemDao, times(1)).batchUpdate(anyList());
        verify(planDao, times(1)).updatePlansAdaptStatus(anyList());
        verify(lessonDao, times(1)).batchPublishAndUpdateAdaptStatus(anyList());
        verify(curriculumUnitDao, times(1)).updateUnitAdaptStatus(anyString());
    }

    @Test
    public void testList() {
        // 模拟数据
        ListPlansRequest request = JsonUtil.fromJson("{\n" +
                "  \"week\": 27,\n" +
                "  \"centerId\": \"center_3\",\n" +
                "  \"groupId\": \"group_7\",\n" +
                "  \"status\": \"Active\",\n" +
                "  \"userId\": \"user_4\",\n" +
                "  \"keyword\": \"keyword_92\",\n" +
                "  \"type\": \"Template\",\n" +
                "  \"page\": 8,\n" +
                "  \"pageSize\": 16,\n" +
                "  \"orderKey\": \"CLASS\",\n" +
                "  \"orderType\": \"ASC\",\n" +
                "  \"filterFollowed\": true,\n" +
                "  \"createBy\": \"creator_1\"\n" +
                "}", ListPlansRequest.class);
        PageList<PlanEntity> pageList = new PageList<>();
        pageList.setTotal(10L);
        pageList.setRecords(new ArrayList<>());
        pageList.setPageNum(1L);
        pageList.setPageSize(1L);

        // 模拟方法调用
        when(userProvider.checkCurrentUser()).thenReturn(new AuthUserDetails());
        when(planDao.list(any(), any(), any(), any(),
                any(), any(), any(), any(), any(), any(),
                any(), any(), any(), any(), any())).thenReturn(pageList);
        when(userProvider.getTimezoneOffsetNum()).thenReturn(8);
        when(planUserDao.listPlanUserDetailsByPlanIds(anyList())).thenReturn(new ArrayList<>());
        when(planReflectionDao.listByObjectIds(any(), any(), any())).thenReturn(new ArrayList<>());
        when(userProvider.getOpenValueDefaultClose(anyString(), any())).thenReturn(true);
        when(groupDao.getGroupsByTeacherId(any())).thenReturn(new ArrayList<>());
        when(groupDao.getGroupByCenterId(any())).thenReturn(new ArrayList<>());
        when(groupDao.getGroupsBySiteAdminId(any())).thenReturn(new ArrayList<>());

        // 调用测试方法
        ListPlansResponse listPlansResponse = planService.list(request);

        // 断言结果
        Assert.assertNotNull(listPlansResponse);
        Assert.assertNotNull(listPlansResponse.getItems());
    }

    /**
     * 测试获取周计划详情
     */
    @Test
    public void testGetWithTranslation() {
        // 构造执行参数
        String id = "08757C51-1967-4EC9-B8B9-DC9A1F1B80F3";
        String source = "NORMAL_VIEW";
        Boolean isDetect = false;
        String langCode = "zh-CN";
        PlanEntity planEntity = new PlanEntity();
        planEntity.setId("ddd");
        planEntity.setType(PlanType.NORMAL.toString());
        planEntity.setStatus("A_Dr");
        String userId = "xxx";
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("yyy");
        AuthUserDetails userDetails = new AuthUserDetails();
        userDetails.setUsername("zzz");
        UserModel userModel = new UserModel();
        userModel.setId("xxx");
        List<UserModel> userModels = new ArrayList<>();
        userModels.add(userModel);

        // 设置执行结果
        when(planDao.get(id)).thenReturn(planEntity);
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.checkCurrentUser()).thenReturn(userDetails);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        when(userDao.getUsersByUserIdsWithDeleted(anyList())).thenReturn(userModels);
        when(translateService.translateObjectData(any(PlanDetailResponse.class), any(TranslateConfigKey.class), any(Class.class), anyString())).thenReturn(new PlanDetailResponse());

        // 调用测试方法
        PlanDetailResponse response = planService.getWithTranslation(id, false, null, null, source, langCode, isDetect);

        // 验证不为空
        Assert.assertNotNull(response);
    }

    /**
     * 测试获取 Unit Planner 部分的周计划详情
     */
    @Test
    public void testGetUnitPlanDetailWithTranslation() {
        // 构造执行参数
        String id = "08757C51-1967-4EC9-B8B9-DC9A1F1B80F3";
        String source = "NORMAL_VIEW";
        Boolean isDetect = false;
        String langCode = "zh-CN";
        PlanEntity planEntity = new PlanEntity();
        planEntity.setId("ddd");
        planEntity.setType(PlanType.NORMAL.toString());
        planEntity.setStatus("A_Dr");
        String userId = "xxx";
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("yyy");
        AuthUserDetails userDetails = new AuthUserDetails();
        userDetails.setUsername("zzz");
        CurriculumUnitPlanEntity curriculumUnitPlanEntity = new CurriculumUnitPlanEntity();
        curriculumUnitPlanEntity.setId("aaa");
        curriculumUnitPlanEntity.setUnitId("bbb");
        CurriculumUnitEntity curriculumUnitEntity = new CurriculumUnitEntity();
        curriculumUnitEntity.setId("lll");
        curriculumUnitEntity.setGrade("vvv");
        List<CategoryEntity> categoryEntities = new ArrayList<>();
        PlanEntity latestPlan = new PlanEntity();
        latestPlan.setId("hhh");

        // 设置执行结果
        when(curriculumUnitDao.getById(anyString())).thenReturn(curriculumUnitEntity);
        when(planDao.get(id)).thenReturn(planEntity);
        when(planCategoryDao.listByPlanId(anyString())).thenReturn(categoryEntities);
        when(curriculumUnitPlanDao.getUnitPlanByPlanId(id)).thenReturn(curriculumUnitPlanEntity);
        when(planDao.getCurriculumGenieTemplate()).thenReturn(latestPlan);
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.checkCurrentUser()).thenReturn(userDetails);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);
        when(translateService.translateObjectData(any(PlanDetailResponse.class), any(TranslateConfigKey.class), any(Class.class), anyString())).thenReturn(new PlanDetailResponse());

        // 调用测试方法
        PlanDetailResponse response = planService.getUnitPlanDetailWithTranslation(id, langCode, isDetect);

        // 验证不为空
        Assert.assertNotNull(response);
    }
}


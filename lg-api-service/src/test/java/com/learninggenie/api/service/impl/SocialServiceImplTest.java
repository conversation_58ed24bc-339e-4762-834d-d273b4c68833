package com.learninggenie.api.service.impl;

import com.learninggenie.api.config.WeChatConfig;
import com.learninggenie.api.model.LoginResponse;
import com.learninggenie.api.model.SocialLoginResponse;
import com.learninggenie.api.model.wechat.WeChatUserInfoResponse;
import com.learninggenie.common.data.dao.SocialUserDao;
import com.learninggenie.common.data.entity.SocialUserEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * @author: ZL
 * @Date: Created in 10:20 2018/3/12
 */
@Ignore
@RunWith(MockitoJUnitRunner.class)
public class SocialServiceImplTest {
    @Mock
    private SocialUserDao socialUserDao;
    @Mock
    private RequestConfig requestConfig;
    @Mock
    private WeChatConfig weChatConfig;
    @InjectMocks
    private SocialServiceImpl socialService;

    @Before
    public void initMocks(){
        this.weChatConfig = new WeChatConfig();
        weChatConfig.setAppWebId("a001");
        weChatConfig.setAppWebSecret("as001");
        ReflectionTestUtils.setField(socialService,"weChatConfig",weChatConfig);
    }

    private SocialUserEntity prepareSocialUserEntity(){
        SocialUserEntity socialUserEntity = new SocialUserEntity();
        socialUserEntity.setUserId("u001");
        socialUserEntity.setType("t001");
        socialUserEntity.setSocialId("s001");
        return socialUserEntity;
    }
    private SocialLoginResponse<WeChatUserInfoResponse> prepareSocialLoginResponse(){
        SocialLoginResponse<WeChatUserInfoResponse>  socialLoginResponse = new SocialLoginResponse<>();
        socialLoginResponse.setUserId("u001");
        socialLoginResponse.setBind(true);
        socialLoginResponse.setLoginResponse(new LoginResponse());
        socialLoginResponse.setSocialUserInfo(new WeChatUserInfoResponse());
        return socialLoginResponse;
    }

    /**
     * case : bind方法参数UserId为null
     */
    @Test
    public void testBind_UserIdIsNull(){
        SocialUserEntity socialUserEntity = this.prepareSocialUserEntity();
        socialUserEntity.setUserId(null);
        socialUserDao.save(socialUserEntity);
        socialService.bind(socialUserEntity.getUserId(),socialUserEntity.getType(),socialUserEntity.getSocialId(),null);
    }

    /**
     * case : bind方法参数Type为null
     */
    @Test
    public void testBind_TypeIsNull(){
        SocialUserEntity socialUserEntity = this.prepareSocialUserEntity();
        socialUserEntity.setType(null);
        socialUserDao.save(socialUserEntity);
        socialService.bind(socialUserEntity.getUserId(),socialUserEntity.getType(),socialUserEntity.getSocialId(), null);
    }

    /**
     * case : bind方法参数SocialId为null
     */
    @Test
    public void testBind_SocialIdIsNull(){
        SocialUserEntity socialUserEntity = this.prepareSocialUserEntity();
        socialUserEntity.setType(null);
        socialUserDao.save(socialUserEntity);
        socialService.bind(socialUserEntity.getUserId(),socialUserEntity.getType(),socialUserEntity.getSocialId(), null);
    }

    /**
     * case : 测试findUserIdByTypeAndSocialId
     */
    @Test
    public void testFindUserIdByTypeAndSocialId(){
        SocialUserEntity socialUserEntity = this.prepareSocialUserEntity();
        when(socialUserDao.query(anyString(),anyString())).thenReturn(socialUserEntity);
        SocialUserEntity query = socialUserDao.query(socialUserEntity.getType(), socialUserEntity.getSocialId());
        Assert.assertEquals("u001",query.getUserId());
    }


    /**
     * case : 测试WeCharLogin参数code为null
     */
    @Test(expected = NullPointerException.class)
    public void testWeChatLogin_CodeIsNull() throws IOException {
        WeChatConfig weChatConfig = new WeChatConfig();
        weChatConfig.setAppWebId("a001");
        weChatConfig.setAppWebSecret("as001");
        HttpClient httpClient = HttpClients.createDefault();
        String accessTokenUrl = "https://api.weixin.qq.com/sns/oauth2/access_token?appid={appid}&secret={secret}&code={code}&grant_type=authorization_code"
                .replace("{appid}", weChatConfig.getAppWebId())
                .replace("{secret}", weChatConfig.getAppWebSecret())
                .replace("{code}", null);
        HttpGet httpGet = new HttpGet(accessTokenUrl);
        httpGet.setConfig(requestConfig);
        httpClient.execute(httpGet);
    }
    /**
     * case : 测试WeCharLogin参数code为""
     */
    @Test
    public void testWeChatLogin_CodeIsBlank() throws IOException {
        WeChatConfig weChatConfig = new WeChatConfig();
        weChatConfig.setAppWebId("a001");
        weChatConfig.setAppWebSecret("as001");
        String result = "";
        HttpClient httpClient = HttpClients.createDefault();
        String accessTokenUrl = "https://api.weixin.qq.com/sns/oauth2/access_token?appid={appid}&secret={secret}&code={code}&grant_type=authorization_code"
                .replace("{appid}", weChatConfig.getAppWebId())
                .replace("{secret}", weChatConfig.getAppWebSecret())
                .replace("{code}", "");
        HttpGet httpGet = new HttpGet(accessTokenUrl);
        httpGet.setConfig(requestConfig);
        HttpResponse httpResponse = httpClient.execute(httpGet);
        if(httpResponse.getStatusLine().getStatusCode()==200){
            result=EntityUtils.toString(httpResponse.getEntity(), "UTF-8");
        }
        when(socialUserDao.query(anyString(),anyString())).thenReturn(this.prepareSocialUserEntity());
        Assert.assertEquals(false,result.contains("u001"));
    }

    /**
     * case : 测试WeChatLogin微信登陆
     */
    @Ignore
    @Test
    public void testWeChatLogin(){
        SocialLoginResponse<WeChatUserInfoResponse> loginResponse = this.prepareSocialLoginResponse();
        when(socialUserDao.query(anyString(),anyString())).thenReturn(this.prepareSocialUserEntity());
        SocialUserEntity query = socialUserDao.query("s001", "t001");
        Assert.assertEquals("u001",query.getUserId());
        loginResponse = socialService.weChatLogin("c001",null);
        Assert.assertEquals(true,loginResponse.getBind());
        Assert.assertEquals("u001",loginResponse.getUserId());
    }
}

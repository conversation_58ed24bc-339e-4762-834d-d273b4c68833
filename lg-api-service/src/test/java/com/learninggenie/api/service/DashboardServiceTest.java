package com.learninggenie.api.service;

import com.learninggenie.api.config.TestBase;
import com.learninggenie.common.data.entity.GroupEntity;
import com.learninggenie.common.data.entity.UserEntity;
import com.learninggenie.common.data.repository.GroupRepository;
import com.learninggenie.common.data.repository.UserRepository;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.URL;
import java.net.URLConnection;
import java.util.List;
import java.util.Map;

@Ignore
public class DashboardServiceTest extends TestBase {
   @Autowired
    GroupRepository groupRepository;
    @Autowired
    UserRepository userRepository;
    @Test
    public void testByGroup(){
        List<GroupEntity> groups = groupRepository.findAll() ;
        String url = "http://lgdashboardtest.elasticbeanstalk.com/api/v1/portfolios/statistics?groupIds=%s&portfolioId=%s&startDate=2014-08-26&endDate=2015-08-26";
        for(GroupEntity g:groups){
             String id = g.getId();
             String  portfolioId = g.getDomain().getId();
            String url1 = String.format(url,id,portfolioId);
            sendGet(url1, "");
        }

    }
    @Test
    public void testUser(){
        List<UserEntity> users = userRepository.findAll();
        String url = "http://lgdashboardtest.elasticbeanstalk.com/api/v1/users/dashboard?email=%s&startDate=2014-08-26&endDate=2015-08-26";
        for(UserEntity u:users){
            if(u.getRole()==null||u.getRole().equalsIgnoreCase("parent")||u.getIsDeleted()==true){
                continue;
            }
            String url1 = String.format(url,u.getEmail());
            sendGet(url1, "");
        }
    }
    public static String sendGet(String url, String param) {
        String result = "";
        BufferedReader in = null;
        try {
            String urlNameString = url + "?" + param;
            URL realUrl = new URL(urlNameString);
            // 打开和URL之间的连接
            URLConnection connection = realUrl.openConnection();
            // 设置通用的请求属性
            connection.setRequestProperty("accept", "*/*");
            connection.setRequestProperty("connection", "Keep-Alive");
            connection.setRequestProperty("user-agent",
                    "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            // 建立实际的连接
            connection.connect();
            // 获取所有响应头字段
            Map<String, List<String>> map = connection.getHeaderFields();
            // 遍历所有的响应头字段
           /* for (String key : map.keySet()) {
                //System.out.println(key + "--->" + map.get(key));
            }*/
            System.out.println(map.get(null).get(0).split(" ")[1]);
           /* int i = Integer.parseInt(map.get(null).get(0).split(" ")[1]);
            if(i>=300){
                throw new Exception("status:"+i);
            }*/
            // 定义 BufferedReader输入流来读取URL的响应
            in = new BufferedReader(new InputStreamReader(
                    connection.getInputStream()));
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
        } catch (Exception e) {
           // System.out.println("err！" + e.getMessage());
            e.printStackTrace();
        }
        // 使用finally块来关闭输入流
        finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (Exception e2) {
                e2.printStackTrace();
            }
        }
        return result;
    }
}

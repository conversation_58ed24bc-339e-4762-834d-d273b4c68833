package com.learninggenie.api.service.posthog;

import com.learninggenie.common.data.k12.K12PosthogTracker;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PosthogServiceTest {

    @Test
    public void testPosthog() {
        K12PosthogTracker k12PosthogTracker = new K12PosthogTracker("phc_gZlpbH62YTB68XDkO3cKDX4k3shmdjUwGs8WwIYlUUP");
//        k12PosthogTracker.posthogCapture("<EMAIL>", "<EMAIL>");
    }
}

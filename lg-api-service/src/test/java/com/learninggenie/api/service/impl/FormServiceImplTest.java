package com.learninggenie.api.service.impl;

import com.learninggenie.api.model.DownFileResponse;
import com.learninggenie.api.model.form.*;
import com.learninggenie.api.model.googleslides.ReplaceRelation;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.service.EmailService;
import com.learninggenie.api.service.MediaService;
import com.learninggenie.api.util.FileUploadUtil;
import com.learninggenie.common.comm.CommService;
import com.learninggenie.common.data.dao.*;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.dto.GroupWithCenter;
import com.learninggenie.common.data.entity.CenterEntity;
import com.learninggenie.common.data.entity.EnrollmentEntity;
import com.learninggenie.common.data.entity.EnrollmentMetaDataEntity;
import com.learninggenie.common.data.entity.UserEntity;
import com.learninggenie.common.data.entity.UserProfileEntity;
import com.learninggenie.common.data.enums.UserRole;
import com.learninggenie.common.data.enums.form.FormSendType;
import com.learninggenie.common.data.enums.form.FormStatus;
import com.learninggenie.common.data.model.*;
import com.learninggenie.common.data.model.form.common.*;
import com.learninggenie.common.data.model.user.UserDeviceModel;
import com.learninggenie.common.utils.ResourceUtil;
import com.learninggenie.common.utils.TimeUtil;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.core.env.Environment;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Created by hxl on 2023/04/23.
 * FormServiceImpl 的测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class FormServiceImplTest {

    @InjectMocks
    private FormServiceImpl formService;
    @Mock
    private UsersMetaDataDao userMetaDao;

    @Mock
    private GroupDao groupDao;

    @Mock
    private FormDao formDao;

    @Mock
    private UserProvider userProvider;
    @Mock
    private StudentDao studentDao;

    private MockedStatic<FileUploadUtil> fileUtil;

    @Mock
    private UserDaoImpl userDao;

    @Mock
    private CommService commService;

    @Mock
    private EmailService emailService;

    @Mock
    private Environment env;

    @Mock
    private CenterDao centerDao;
    @Mock
    private MediaService mediaService;

    @Before
    public void before() {
        fileUtil = mockStatic(FileUploadUtil.class);
    }
    @After
    public void after() {
        fileUtil.close();
    }

    @Ignore
    @Test
    public void testNotifyStaff() {
        // 问卷 ID
        String formId = UUID.randomUUID().toString();
        // 提醒内容
        String content = "notify";
        // 问卷信息
        FormModel formModel = new FormModel();
        formModel.setId(formId);
        when(formDao.getFormById(formId)).thenReturn(formModel);
        // 请求信息
        NotifyParentRequest request = new NotifyParentRequest();
        request.setFormId(formId);
        request.setContent(content);
        // 用户 ID
        String userId = UUID.randomUUID().toString();
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        // 用户
        UserModel user = new UserModel();
        when(userDao.getUserById(userId)).thenReturn(user);
        // 已填写员工记录
        List<ResponseRecord> responseRecords = new ArrayList<>();
        when(formDao.getResponseRecord(formId)).thenReturn(responseRecords);
        // 员工问卷列表
        List<StaffFormModel> staffFormModels = new ArrayList<>();
        when(formDao.getStaffFormWithStaffDetailByFormId(formId)).thenReturn(staffFormModels);
        // 学校列表
        List<CenterModel> centers = new ArrayList<>();
        when(userProvider.getCenterGroupByUserId(userId, false)).thenReturn(centers);
        // 推送设备信息列表
        List<UserDeviceModel> notifyStaffs = new ArrayList<>();
        when(formDao.getNotifyStaff(anyList())).thenReturn(notifyStaffs);

        // 草稿状态
        formModel.setStatus(FormStatus.DRAFT.toString());
        formService.notifyStaff(request);
        // 直接返回，不执行后面代码
        verify(formDao, times(0)).getResponseRecord(formId);

        // 已发布状态
        formModel.setStatus(FormStatus.PUBLISHED.toString());
        // 管理员角色
        user.setRole(UserRole.AGENCY_OWNER.toString());
        formService.notifyStaff(request);
        // 获取填写记录
        verify(formDao, times(1)).getResponseRecord(formId);
        // 重置提醒状态
        verify(formDao, times(1)).resetUnSubmittedStaffFormNotifyStatus(formId);
        verify(formDao, times(0)).resetUnSubmittedFormNotifyStatusByCenters(eq(formId), anyList());

        // 其他角色
        user.setRole(UserRole.SITE_ADMIN.toString());
        formService.notifyStaff(request);
        // 重置提醒状态
        verify(formDao, times(1)).resetUnSubmittedFormNotifyStatusByCenters(eq(formId), anyList());
        // 没有设备信息，不推送消息
        verify(commService, times(0)).sendSNSMessages(any());

        // 有设备信息，推送消息
        UserDeviceModel userDeviceModel = new UserDeviceModel();
        userDeviceModel.setUserId(UUID.randomUUID().toString());
        userDeviceModel.setDeviceToken("token");
        notifyStaffs.add(userDeviceModel);
        formService.notifyStaff(request);
        // 发送一次推送
        verify(commService, times(1)).sendSNSMessages(any());
        // 不发送邮件
        verify(emailService, times(0)).sendAsync(any());

        // 发送邮件
        request.setSendEmail(true);
        // 接收人
        StaffFormModel staffFormModel = new StaffFormModel();
        staffFormModel.setStaffId(UUID.randomUUID().toString());
        staffFormModel.setStaffName("Teacher");
        staffFormModels.add(staffFormModel);
        // 设置环境
        when(env.getProperty(anyString())).thenReturn("prod");
        // 问卷名称
        formModel.setName("Form");
        // 读取邮件模板
        try (MockedStatic<ResourceUtil> resourceUtil = mockStatic(ResourceUtil.class)) {
            resourceUtil.when(() -> ResourceUtil.getResourceAsString(anyString())).thenReturn("html");
            formService.notifyStaff(request);
            // 发送一次邮件
            verify(emailService, times(1)).sendAsync(any());
        }
    }

    @Test
    @Ignore
    public void testGenerateResponsePdf_agencyLevel() {}

    @Test
    @Ignore
    public void testGenerateResponseCSV() throws IOException {
        // Create a mock FormFilterRequest object
        FormFilterRequest request = new FormFilterRequest();
        request.setFormId("formId");
        request.setAttrs(new ArrayList<AttrBase>());
        request.setCenterIds(new ArrayList<String>());
        request.setGroupIds(new ArrayList<String>());
        request.setAllStaff(false);

        // Create a mock UserEntity object
        UserEntity user = new UserEntity();
        user.setId("userId");
        user.setRole(UserRole.AGENCY_OWNER.toString());

        // Create a mock UserProvider object
        when(userProvider.getCurrentUserId()).thenReturn(user.getId());
        when(userProvider.checkUser(user.getId())).thenReturn(user);

        // Create a mock FormDao object

        // 问卷 ID
        String formId = UUID.randomUUID().toString();
        // 问卷信息
        FormModel formModel = new FormModel();
        formModel.setId(formId);
        when(formDao.getFormById(formId)).thenReturn(formModel);
        // 请求信息
        request.setFormId(formId);
        // 用户 ID
        String userId = user.getId();
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        // 用户
        UserModel user1 = new UserModel();
        // 生成一个机构
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("3ccd5446-b8f8-42b5-b875-5539e52ef2a3");
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);

        List<EnrollmentMetaDataEntity> enrollmentMetaDataList = new ArrayList<>();

        EnrollmentMetaDataEntity enrollmentMetaData1 = new EnrollmentMetaDataEntity();
        EnrollmentEntity enrollment1 = new EnrollmentEntity();
        enrollment1.setId("enrollmentId1");
        enrollment1.setFirstName("FirstName");
        enrollment1.setMiddleName("MiddleName");
        enrollment1.setLastName("LastName");

        EnrollmentEntity enrollment2 = new EnrollmentEntity();
        enrollment2.setId("enrollmentId2");
        enrollment2.setFirstName("FirstName2");
        enrollment2.setMiddleName("MiddleName2");
        enrollment2.setLastName("LastName2");


        enrollmentMetaData1.setEnrollment(enrollment1);
        enrollmentMetaData1.setUserId(userId);
        enrollmentMetaData1.setGroupId("groupId1");

        EnrollmentMetaDataEntity enrollmentMetaData2 = new EnrollmentMetaDataEntity();
        enrollmentMetaData2.setEnrollment(enrollment2);
        enrollmentMetaData2.setUserId(userId);
        enrollmentMetaData2.setGroupId("groupId2");

        enrollmentMetaDataList.add(enrollmentMetaData1);
        enrollmentMetaDataList.add(enrollmentMetaData2);

        when(studentDao.getMetasByChildIds(anyList(), anyString())).thenReturn(enrollmentMetaDataList);
        // 已填写员工记录
        List<ResponseRecord> responseRecords = new ArrayList<>();
        // 员工问卷列表
        List<StaffFormModel> staffFormModels = new ArrayList<>();
        // 学校列表
        List<CenterModel> centers = new ArrayList<>();
        when(userProvider.getCenterGroupByUserId(userId, false)).thenReturn(centers);
        // 推送设备信息列表
        List<UserDeviceModel> notifyStaffs = new ArrayList<>();

        // 管理员角色
        user.setRole(UserRole.AGENCY_OWNER.toString());
        // 文件上传
        ReflectionTestUtils.setField(formService, "s3BucketName",  "s3BucketName");
        ReflectionTestUtils.setField(formService, "s3Root",  "s3Root");
        fileUtil.when(() -> FileUploadUtil.upload(anyString(), any(), anyString(), any())).thenReturn("url");
        DownFileResponse response = formService.generateResponseCSV(request);

        // 验证 response 中 url的存在
        assertNotNull(response.getUrl());
    }

    @Test
    public void testGenerateResponseExcel() throws IOException {
        // Create a mock FormFilterRequest object
        FormFilterRequest request = new FormFilterRequest();
        request.setFormId("formId");
        request.setAttrs(new ArrayList<AttrBase>());
        request.setCenterIds(new ArrayList<String>());
        request.setGroupIds(new ArrayList<String>());
        request.setAllStaff(false);

        // Create a mock UserEntity object
        UserEntity user = new UserEntity();
        user.setId("userId");
        user.setRole(UserRole.AGENCY_OWNER.toString());

        // Create a mock UserProvider object
        when(userProvider.getCurrentUserId()).thenReturn(user.getId());
        when(userProvider.checkUser(user.getId())).thenReturn(user);

        // Create a mock FormDao object

        // 问卷 ID
        String formId = UUID.randomUUID().toString();
        // 问卷信息
        FormModel formModel = new FormModel();
        formModel.setId(formId);
        when(formDao.getFormById(formId)).thenReturn(formModel);
        // 请求信息
        request.setFormId(formId);
        // 用户 ID
        String userId = user.getId();
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        // 用户
        UserModel user1 = new UserModel();
        // 生成一个机构
        AgencyModel agencyModel = new AgencyModel();
        agencyModel.setId("3ccd5446-b8f8-42b5-b875-5539e52ef2a3");
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agencyModel);

        List<EnrollmentMetaDataEntity> enrollmentMetaDataList = new ArrayList<>();

        EnrollmentMetaDataEntity enrollmentMetaData1 = new EnrollmentMetaDataEntity();
        EnrollmentEntity enrollment1 = new EnrollmentEntity();
        enrollment1.setId("enrollmentId1");
        enrollment1.setFirstName("FirstName");
        enrollment1.setMiddleName("MiddleName");
        enrollment1.setLastName("LastName");

        EnrollmentEntity enrollment2 = new EnrollmentEntity();
        enrollment2.setId("enrollmentId2");
        enrollment2.setFirstName("FirstName2");
        enrollment2.setMiddleName("MiddleName2");
        enrollment2.setLastName("LastName2");


        enrollmentMetaData1.setEnrollment(enrollment1);
        enrollmentMetaData1.setUserId(userId);
        enrollmentMetaData1.setGroupId("groupId1");

        EnrollmentMetaDataEntity enrollmentMetaData2 = new EnrollmentMetaDataEntity();
        enrollmentMetaData2.setEnrollment(enrollment2);
        enrollmentMetaData2.setUserId(userId);
        enrollmentMetaData2.setGroupId("groupId2");

        enrollmentMetaDataList.add(enrollmentMetaData1);
        enrollmentMetaDataList.add(enrollmentMetaData2);

        when(studentDao.getMetasByChildIds(anyList(), anyString())).thenReturn(enrollmentMetaDataList);
        // 已填写员工记录
        List<ResponseRecord> responseRecords = new ArrayList<>();
        // 员工问卷列表
        List<StaffFormModel> staffFormModels = new ArrayList<>();
        // 学校列表
        List<CenterModel> centers = new ArrayList<>();
        when(userProvider.getCenterGroupByUserId(userId, false)).thenReturn(centers);
        // 推送设备信息列表
        List<UserDeviceModel> notifyStaffs = new ArrayList<>();

        // 管理员角色
        user.setRole(UserRole.AGENCY_OWNER.toString());
        // 文件上传
        ReflectionTestUtils.setField(formService, "s3BucketName",  "s3BucketName");
        ReflectionTestUtils.setField(formService, "s3Root",  "s3Root");
        fileUtil.when(() -> FileUploadUtil.upload(anyString(), any(), anyString(), any())).thenReturn("url");

        // 验证 response 中 url的存在
        DownFileResponse response = formService.generateResponseExcel(request);
        assertNotNull(response.getUrl());
    }

    @Test
    public void testGetForms() {
        // Set up mock data
        String userId = "testUserId";
        String agencyId = "testAgencyId";
        UserEntity user = new UserEntity();
        user.setId(userId);
        AgencyModel agency = new AgencyModel();
        agency.setId(agencyId);
        when(userProvider.getCurrentUserId()).thenReturn(userId);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        when(userProvider.checkUser(userId)).thenReturn(user);
        when(userProvider.getAgencyByUserId(userId)).thenReturn(agency);
        when(userProvider.getCenterGroupByUserId(userId, false)).thenReturn(new ArrayList<>());
        when(userMetaDao.getMeta(anyString(), anyString())).thenReturn(null);

        // Call the method
        FormListRequest request = new FormListRequest();
        request.setType("testType");
        FormListResponse response = formService.getForms(request);

        // Verify the results
        assertNotNull(response);
    }

    @Ignore
    @Test
    public void testCreateForm() {
        // Set up mock data
        String userId = "testUserId";
        String agencyId = "testAgencyId";
        UserEntity user = new UserEntity();
        user.setId(userId);
        AgencyModel agency = new AgencyModel();
        agency.setId(agencyId);
        // Setup
        CreateFormRequest request = new CreateFormRequest();
        request.setToDateStr("9999-05-01 13:24:55.000");
        request.setRole(FormSendType.PARENT.toString());
        request.setChildIds(Arrays.asList("child1", "child2"));
        request.setGroupIds(Arrays.asList("group1", "group2"));
        request.setDescription("test Description");

        // 生成几个 QuesitonModel 放入的 request中
        List<QuestionModel> questions = new ArrayList<>();

        // OptionModel
        OptionModel option1 = new OptionModel();
        option1.setId("1");
        option1.setQuestionId("Q1");
        option1.setFormId("F1");
        option1.setName("Option A");
        option1.setExclusive(false);
        option1.setSortNum(1);
        option1.setHasContent(false);
        option1.setContentDesc(null);
        option1.setContentType(null);
        option1.setContentRule(null);
        option1.setCreateAtUtc(new Date());
        option1.setUpdateAtUtc(new Date());
        option1.setDeleted(false);
        option1.setSelected(false);
        option1.setContent(null);
        option1.setResponseCount(0);
        option1.setResponseRate("0%");
        option1.setResponseRateValue(0.0);
        option1.setResponseAllCount(0);
        option1.setAbnormal(false);
        option1.setAttributeRate(new ArrayList<>());
        option1.setCreateIds(new ArrayList<>());
        option1.setMediaId(null);
        option1.setMediaUrl(null);

        OptionModel option2 = new OptionModel();
        option2.setId("2");
        option2.setQuestionId("Q1");
        option2.setFormId("F1");
        option2.setName("Option B");
        option2.setExclusive(false);
        option2.setSortNum(2);
        option2.setHasContent(false);
        option2.setContentDesc(null);
        option2.setContentType(null);
        option2.setContentRule(null);
        option2.setCreateAtUtc(new Date());
        option2.setUpdateAtUtc(new Date());
        option2.setDeleted(false);
        option2.setSelected(false);
        option2.setContent(null);
        option2.setResponseCount(0);
        option2.setResponseRate("0%");
        option2.setResponseRateValue(0.0);
        option2.setResponseAllCount(0);
        option2.setAbnormal(false);
        option2.setAttributeRate(new ArrayList<>());
        option2.setCreateIds(new ArrayList<>());
        option2.setMediaId(null);
        option2.setMediaUrl(null);

        QuestionModel question1 = new QuestionModel();
        QuestionModel question2 = new QuestionModel();
        question1.setId("1");
        question1.setPid(null);
        question1.setFormId("form-1");
        question1.setQuestionGroupId("group-1");
        question1.setName("What is your age?");
        question1.setDescription("Please select your age from the options below.");
        question1.setType("SingleSelect");
        question1.setTypeName("Single Select");
        question1.setNum("Q1");
        question1.setSortNum(1);
        question1.setRequired(true);
        question1.setTextRegular(null);
        question1.setCreateAtUtc(new Date());
        question1.setUpdateAtUtc(new Date());
        question1.setDeleted(false);
        question1.setContent(null);
        question1.setContentCount(0);
        question1.setResponseCount(0);
        question1.setSendCount(0);
        question1.setSubQuestions(Collections.singletonList(question2));
        question1.setOptions(Arrays.asList(
                option1, option2
        ));
        question1.setContents(null);
        question1.setAttributeOptions(null);
        question1.setVideoId(null);
        question1.setVideoName(null);
        question1.setVideoStartTime(0);
        question1.setMediaId(null);
        question1.setMediaUrl(null);
        question1.setLastSelectSortNum(null);
        question1.setSelectOptionId(null);

        question2.setId("2");
        question2.setPid("1");
        question2.setFormId("form-1");
        question2.setQuestionGroupId("group-1");
        question2.setName("What is your gender?");
        question2.setDescription("Please select your gender from the options below.");
        question2.setType("SingleSelect");
        question2.setTypeName("Single Select");
        question2.setNum("Q2");
        question2.setSortNum(2);
        question2.setRequired(true);
        question2.setTextRegular(null);
        question2.setCreateAtUtc(new Date());
        question2.setUpdateAtUtc(new Date());
        question2.setDeleted(false);
        question2.setContent(null);
        question2.setContentCount(0);
        question2.setResponseCount(0);
        question2.setSendCount(0);
        question2.setSubQuestions(Collections.singletonList(question1));
        question2.setOptions(Arrays.asList(
                option1, option2
        ));
        question2.setContents(null);
        question2.setAttributeOptions(null);
        question2.setVideoId(null);
        question2.setVideoName(null);
        question2.setVideoStartTime(0);
        question2.setMediaId(null);
        question2.setMediaUrl(null);
        question2.setLastSelectSortNum(null);
        question2.setSelectOptionId(null);

        questions.add(question1);
        questions.add(question2);
        request.setQuestions(questions);
        request.setQuestions(questions);

        user.setProfile(new UserProfileEntity());
        user.getProfile().setDisplayName("Test User");

        GroupWithCenter groupWithCenter1 = new GroupWithCenter();
        groupWithCenter1.setGroupId("group1");
        groupWithCenter1.setCenterName("Test Center 1");

        GroupWithCenter groupWithCenter2 = new GroupWithCenter();
        groupWithCenter2.setGroupId("group2");
        groupWithCenter2.setCenterName("Test Center 2");

        List<GroupWithCenter> groupWithCenters = Arrays.asList(groupWithCenter1, groupWithCenter2);

        Mockito.when(userProvider.getCurrentUserId()).thenReturn("user1");
        Mockito.when(userProvider.checkUser("user1")).thenReturn(user);
        Mockito.when(userProvider.getAgencyByUserId("user1")).thenReturn(agency);
        Mockito.when(userProvider.getTimezoneOffsetNum()).thenReturn(0);
        Mockito.when(groupDao.getGroupAndCenterNamesByIds(Mockito.anyString())).thenReturn(groupWithCenters);
        // Mockito.when(studentDao.getAllChildrenByIds(Mockito.anyList())).thenReturn(new ArrayList<EnrollmentModel>());

        // Test
        FormModel form = formService.createForm(request);

        // Verify
        Assert.assertNotNull(form);
        Assert.assertFalse(form.isDisableAnonymous());
        Assert.assertFalse(form.isAnonymous());
        Mockito.verify(formDao, Mockito.times(1)).createForm(Mockito.any(CreateFormRequest.class));
    }

    /**
     * 测试属性过滤
     */
    @Test
    public void testGetAddFixedAttrFilter() {
        // Test
        formService.addFixedAttrFilter(new ArrayList<>());
    }

    @Test
    public void testGenerateSurveySlideData() {
        String agencyId = UUID.randomUUID().toString();
        // 获取功能开过
        when(userProvider.getAgencyOpenDefaultClose(anyString(), anyString())).thenReturn(true);

        List<CenterEntity> centerEntities = new ArrayList<>();
        CenterEntity centerEntity = new CenterEntity();
        centerEntity.setId("centerEntityId");
        centerEntities.add(centerEntity);
        // 获取机构下所有学校
        when(centerDao.getAllByAgencyId(anyString())).thenReturn(centerEntities);

        AgencyModel agency = new AgencyModel();
        agency.setId("agencyId");
        when(userProvider.getCurrentUserId()).thenReturn("userId");
        when(userProvider.getAgencyByUserId(anyString())).thenReturn(agency);
        UserEntity user = new UserEntity();
        user.setRole("AGENCY_ADMIN");
        when(userProvider.checkUser(anyString())).thenReturn(user);

        // 数据准备
        String uuid1 = UUID.randomUUID().toString();
        String uuid2 = UUID.randomUUID().toString();
        List<FormModel> parentForms = new ArrayList<>();
        FormModel formModel = new FormModel();
        formModel.setId(uuid1);
        parentForms.add(formModel);
        FormModel formModel2 = new FormModel();
        formModel.setId(uuid2);
        parentForms.add(formModel2);
        when(formDao.getSimpleFormDataByDate(anyString(), anyString(), anyString(), anyString(), anyString())).thenReturn(parentForms);

        List<String> scopeFormIds = new ArrayList<>(Arrays.asList(uuid1, uuid2));
        when(formDao.getFormIdsByCenters(anyList())).thenReturn(scopeFormIds);

        FormModel mostResponseForm = new FormModel();
        mostResponseForm.setId(UUID.randomUUID().toString());
        // 获取回复最多包含统计的问卷
        when(formDao.getMostResponseAndStatisticalForm(anyList())).thenReturn(mostResponseForm);
        when(formDao.getFormById(anyString())).thenReturn(mostResponseForm);
        when(mediaService.convertHtmlToImage(anyString(), anyString())).thenReturn("url");

        // 调用方法
        List<ReplaceRelation> replaceRelations = formService.generateSurveySlideData(agencyId, TimeUtil.getUtcNow(), TimeUtil.getUtcNow());

        // 验证结果
        assertEquals(6, replaceRelations.size());
        verify(formDao, times(1)).getFormById(anyString());
        verify(mediaService, times(1)).convertHtmlToImage(anyString(), anyString());
    }
}

package com.learninggenie.api.dao;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.toolkit.ChainWrappers;
import com.learninggenie.common.data.dao.impl.InKindReportApproveDaoImpl;
import com.learninggenie.common.data.entity.inkind.InKindReportApprove;
import com.learninggenie.common.data.enums.InKindSourceEnum;
import com.learninggenie.common.data.mapper.mybatisplus.inkind.InKindReportApproveMapper;
import com.learninggenie.common.data.mapper.mybatisplus.inkind.InKindReportModelMapper;
import com.learninggenie.common.data.model.inkind.InKindReportModel;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.Assert;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;


/**
 * InKindReportApproveDao 单元测试类.
 */
@ExtendWith(MockitoExtension.class)
class InKindReportApproveDaoImplTest {

    private static final String BASE_MAPPER = "baseMapper";

    private static final String INKIND_REPORT_MODEL_MAPPER = "inKindReportModelMapper";

    private static MockedStatic<ChainWrappers> chainWrappersMockedStatic;

    @InjectMocks
    private InKindReportApproveDaoImpl inKindReportApproveDaoImpl;

    @Mock
    private InKindReportApproveMapper inKindReportApproveMapper;

    @Mock
    private InKindReportModelMapper inKindReportModelMapper;

    /**
     * 初始化 TableInfoHelper
     */
    @BeforeAll
    public static void beforeClass() {
        chainWrappersMockedStatic = mockStatic(ChainWrappers.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), InKindReportModel.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), InKindReportApprove.class);
    }

    /**
     * 关闭 chainWrappersMockedStatic
     */
    @AfterAll
    public static void afterClass() {
        chainWrappersMockedStatic.close();
    }


    /**
     * 测试获取待批准的报告列表
     */
    @Test
    void testGetNeedRatifyListByAgencyId() {
        // 准备测试数据
        final String agencyId = "A00004";
        Page<InKindReportModel> list = new Page<>();
        list.setTotal(1L);

        // 调用待测试的方法
        ReflectionTestUtils.setField(inKindReportApproveDaoImpl, INKIND_REPORT_MODEL_MAPPER, inKindReportModelMapper);
        ReflectionTestUtils.setField(inKindReportApproveDaoImpl, BASE_MAPPER, inKindReportModelMapper);
        final LambdaQueryChainWrapper<InKindReportModel> lambdaUpdate = new LambdaQueryChainWrapper<>(inKindReportModelMapper);
        lambdaUpdate.setEntity(new InKindReportModel());
        lambdaUpdate.setEntityClass(InKindReportModel.class);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(inKindReportModelMapper)).thenReturn(lambdaUpdate);
        Mockito.when(inKindReportModelMapper.getNeedRatifyListByAgencyId(any(), any(), any())).thenReturn(list);

        final Page<InKindReportModel> page = inKindReportApproveDaoImpl.getNeedRatifyListByAgencyId(agencyId);

        // 验证结果
        Assert.assertNotNull(page);
    }

    /**
     * 测试获取待批准的报告列表
     * 参数:机构 ID,查询条件,分页
     */
    @Test
    void testGetNeedRatifyListByAgencyId2() {
        // 调用真实方法
        InKindReportModelMapper inKindReportModelMapperSpy = spy(inKindReportModelMapper);
        // 注入属性
        ReflectionTestUtils.setField(inKindReportApproveDaoImpl, INKIND_REPORT_MODEL_MAPPER, inKindReportModelMapperSpy);

        // 准备测试数据
        final String agencyId = "A00001";
        Page<InKindReportModel> list = new Page<>();
        list.setTotal(1L);
        Page page = new Page(1, 10);

        // 调用待测试的方法
        final LambdaQueryChainWrapper<InKindReportModel> lambdaUpdate = new LambdaQueryChainWrapper<>(inKindReportModelMapperSpy);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(inKindReportModelMapperSpy)).thenReturn(lambdaUpdate);
        Mockito.doReturn(list).when(inKindReportModelMapperSpy).getNeedRatifyListByAgencyId(any(), any(), any());
        final Page<InKindReportModel> pageData = inKindReportApproveDaoImpl.getNeedRatifyListByAgencyId(agencyId, new LambdaQueryWrapper<>(), page);

        // 验证结果
        Assert.assertNotNull(pageData);

    }

    /**
     * 测试存在待批准的报告方法
     */
    @Test
    void testIsExistNeedRatifyByAgencyId() {
        // 调用真实方法
        InKindReportModelMapper inKindReportModelMapperSpy = spy(inKindReportModelMapper);
        // 注入属性
        ReflectionTestUtils.setField(inKindReportApproveDaoImpl, INKIND_REPORT_MODEL_MAPPER, inKindReportModelMapperSpy);
        final LambdaQueryChainWrapper<InKindReportModel> lambdaUpdate = new LambdaQueryChainWrapper<>(inKindReportModelMapperSpy);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(inKindReportModelMapperSpy)).thenReturn(lambdaUpdate);
        // 准备测试数据
        Mockito.doReturn("needRatifyId").when(inKindReportModelMapperSpy).getTopNeedRatifyIdByAgencyId(any(), any());
        // 调用待测试的方法
        boolean isExistNeedRatify = inKindReportApproveDaoImpl.isExistNeedRatifyByAgencyId("A1", new LambdaQueryWrapper<>());
        // 验证结果
        Assertions.assertTrue(isExistNeedRatify); // 断言存在待批准的报告
    }

    /**
     * 测试获取待批准的报告列表
     * 参数:学校 ID
     */
    @Test
    void testGetNeedRatifyListByCenterIds() {
        // 调用真实方法
        InKindReportModelMapper inKindReportModelMapperSpy = spy(inKindReportModelMapper);
        // 注入属性
        ReflectionTestUtils.setField(inKindReportApproveDaoImpl, INKIND_REPORT_MODEL_MAPPER, inKindReportModelMapperSpy);

        // 准备测试数据
        Page<InKindReportModel> list = new Page<>();
        list.setTotal(1L);
        List<String> centerIds = Collections.singletonList("centerId1");

        // 调用待测试的方法
        final LambdaQueryChainWrapper<InKindReportModel> lambdaUpdate = new LambdaQueryChainWrapper<>(inKindReportModelMapperSpy);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(inKindReportModelMapperSpy)).thenReturn(lambdaUpdate);
        Mockito.doReturn(list).when(inKindReportModelMapperSpy).getNeedRatifyListByCenterIds(any(), any(), any());
        final Page<InKindReportModel> res = inKindReportApproveDaoImpl.getNeedRatifyListByCenterIds(centerIds);

        // 验证结果
        Assert.assertNotNull(res);

    }

    /**
     * 测试获取待批准的报告列表
     * 参数:学校 ID,查询条件,分页
     */
    @Test
    void testGetNeedRatifyListByCenterIds2() {
        // 调用真实方法
        InKindReportModelMapper inKindReportModelMapperSpy = spy(inKindReportModelMapper);
        // 注入属性
        ReflectionTestUtils.setField(inKindReportApproveDaoImpl, INKIND_REPORT_MODEL_MAPPER, inKindReportModelMapperSpy);

        // 准备测试数据
        Page<InKindReportModel> list = new Page<>();
        list.setTotal(1L);
        Page page = new Page<>(1, 10);
        List<String> centerIds = Collections.singletonList("centerId2");

        // 调用待测试的方法
        final LambdaQueryChainWrapper<InKindReportModel> lambdaUpdate = new LambdaQueryChainWrapper<>(inKindReportModelMapperSpy);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(inKindReportModelMapperSpy)).thenReturn(lambdaUpdate);
        Mockito.doReturn(list).when(inKindReportModelMapperSpy).getNeedRatifyListByCenterIds(any(), any(), any());
        final Page<InKindReportModel> res = inKindReportApproveDaoImpl.getNeedRatifyListByCenterIds(centerIds, new LambdaQueryWrapper<>(), page);

        // 验证结果
        Assert.assertNotNull(res);

    }

    /**
     * 测试获取待批准的报告列表
     * 参数:班级 ID
     */
    @Test
    void testGetNeedRatifyListByGroupIds() {
        // 调用真实方法
        InKindReportModelMapper inKindReportModelMapperSpy = spy(inKindReportModelMapper);
        // 注入属性
        ReflectionTestUtils.setField(inKindReportApproveDaoImpl, INKIND_REPORT_MODEL_MAPPER, inKindReportModelMapperSpy);

        // 准备测试数据
        Page<InKindReportModel> list = new Page<>();
        list.setTotal(1L);
        List<String> groupIds = Collections.singletonList("groupId2");

        // 调用待测试的方法
        final LambdaQueryChainWrapper<InKindReportModel> lambdaUpdate = new LambdaQueryChainWrapper<>(inKindReportModelMapperSpy);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(inKindReportModelMapperSpy)).thenReturn(lambdaUpdate);
        Mockito.doReturn(list).when(inKindReportModelMapperSpy).getNeedRatifyListByGroupIds(any(), any(), any());
        final Page<InKindReportModel> res = inKindReportApproveDaoImpl.getNeedRatifyListByGroupIds(groupIds);

        // 验证结果
        Assert.assertNotNull(res);

    }

    /**
     * 测试获取待批准的报告列表
     * 参数:学校 ID,查询条件,分页
     */
    @Test
    void testGetNeedRatifyListByGroupIds2() {
        // 调用真实方法
        InKindReportModelMapper inKindReportModelMapperSpy = spy(inKindReportModelMapper);
        // 注入属性
        ReflectionTestUtils.setField(inKindReportApproveDaoImpl, INKIND_REPORT_MODEL_MAPPER, inKindReportModelMapperSpy);

        // 准备测试数据
        Page<InKindReportModel> list = new Page<>();
        list.setTotal(1L);
        Page page = new Page<>(1, 10);
        List<String> groupIds = Collections.singletonList("groupId1");

        // 调用待测试的方法
        final LambdaQueryChainWrapper<InKindReportModel> lambdaUpdate = new LambdaQueryChainWrapper<>(inKindReportModelMapperSpy);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(inKindReportModelMapperSpy)).thenReturn(lambdaUpdate);
        Mockito.doReturn(list).when(inKindReportModelMapperSpy).getNeedRatifyListByGroupIds(any(), any(), any());
        final Page<InKindReportModel> res = inKindReportApproveDaoImpl.getNeedRatifyListByGroupIds(groupIds, new LambdaQueryWrapper<>(), page);

        // 验证结果
        Assert.assertNotNull(res);

    }

    /**
     * 测试获取等待批准的报告列表
     */
    @Test
    void testGetPendingRatifyListByAgencyIdOrUserId() {
        // 调用真实方法
        InKindReportModelMapper inKindReportModelMapperSpy = spy(inKindReportModelMapper);
        // 注入属性
        ReflectionTestUtils.setField(inKindReportApproveDaoImpl, INKIND_REPORT_MODEL_MAPPER, inKindReportModelMapperSpy);

        // 准备测试数据
        final String agencyId = "A00002";
        final String userId = "userId";
        Page<InKindReportModel> list = new Page<>();
        list.setTotal(1L);
        Page page = new Page<>(1, 10);

        // 调用待测试的方法
        final LambdaQueryChainWrapper<InKindReportModel> lambdaUpdate = new LambdaQueryChainWrapper<>(inKindReportModelMapperSpy);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(inKindReportModelMapperSpy)).thenReturn(lambdaUpdate);
        Mockito.doReturn(list).when(inKindReportModelMapperSpy).getPendingRatifyListByAgencyIdOrUserId(any(), any(), any(), any());
        final Page<InKindReportModel> res = inKindReportApproveDaoImpl.getPendingRatifyListByAgencyIdOrUserId(agencyId, userId, new LambdaQueryWrapper<>(), page);

        // 验证结果
        Assert.assertNotNull(res);
    }

    /**
     * 测试获取待批准的报告列表
     */
    @Test
    void testGetNeedSignatureListByUserId() {
        // 准备测试数据
        List<InKindReportApprove> list = new ArrayList<>();
        InKindReportApprove inKindReportApprove = new InKindReportApprove();
        inKindReportApprove.setSource(InKindSourceEnum.BASE.getName());
        InKindReportApprove inKindReportApprove2 = new InKindReportApprove();
        inKindReportApprove2.setSource(InKindSourceEnum.APPEND.getName());
        list.add(inKindReportApprove);
        list.add(inKindReportApprove2);

        // 调用待测试的方法
        ReflectionTestUtils.setField(inKindReportApproveDaoImpl, BASE_MAPPER, inKindReportApproveMapper);
        final LambdaQueryChainWrapper<InKindReportApprove> lambdaUpdate = new LambdaQueryChainWrapper<>(inKindReportApproveMapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(inKindReportApproveMapper)).thenReturn(lambdaUpdate);
        Mockito.doReturn(list).when(inKindReportApproveMapper).selectList(any());
        final List<InKindReportModel> res = inKindReportApproveDaoImpl.getNeedSignatureListByUserId("userId1");

        // 验证结果
        Assert.assertNotNull(res);
        verify(inKindReportModelMapper).getReportByIds(any(), any());
        verify(inKindReportModelMapper).getAppendReportByIds(any(), any());

    }

    /**
     * 测试获取被拒绝的报告列表
     */
    @Test
    void testGetReviseList() {
        // 准备测试数据

        // 调用待测试的方法
        ReflectionTestUtils.setField(inKindReportApproveDaoImpl, INKIND_REPORT_MODEL_MAPPER, inKindReportModelMapper);
        ReflectionTestUtils.setField(inKindReportApproveDaoImpl, BASE_MAPPER, inKindReportApproveMapper);
        final LambdaQueryChainWrapper<InKindReportApprove> lambdaUpdate = new LambdaQueryChainWrapper<>(inKindReportApproveMapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(inKindReportApproveMapper)).thenReturn(lambdaUpdate);
        List<InKindReportModel> reviseList = inKindReportApproveDaoImpl.getReviseList("U0001");

        // 验证结果
        Assert.assertNotNull(reviseList);
    }

    /**
     * 测试获取员工提交审批的报告列表
     */
    @Test
    void testGetRatifyByStaff() {
        // 准备测试数据

        // 调用待测试的方法
        ReflectionTestUtils.setField(inKindReportApproveDaoImpl, INKIND_REPORT_MODEL_MAPPER, inKindReportModelMapper);
        ReflectionTestUtils.setField(inKindReportApproveDaoImpl, BASE_MAPPER, inKindReportApproveMapper);
        final LambdaQueryChainWrapper<InKindReportApprove> lambdaUpdate = new LambdaQueryChainWrapper<>(inKindReportApproveMapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(inKindReportApproveMapper)).thenReturn(lambdaUpdate);
        final List<InKindReportModel> ratifyByStaff = inKindReportApproveDaoImpl.getRatifyByStaff(Collections.singletonList("U00001"), Collections.singletonList("C00001"));

        // 验证结果
        Assert.assertNotNull(ratifyByStaff);
    }

    /**
     * 测试获取报告列表
     */
    @Test
    void testGetReportByAgencyId() {
        // 准备测试数据

        // 调用待测试的方法
        ReflectionTestUtils.setField(inKindReportApproveDaoImpl, INKIND_REPORT_MODEL_MAPPER, inKindReportModelMapper);
        ReflectionTestUtils.setField(inKindReportApproveDaoImpl, BASE_MAPPER, inKindReportApproveMapper);
        final LambdaQueryChainWrapper<InKindReportApprove> lambdaUpdate = new LambdaQueryChainWrapper<>(inKindReportApproveMapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(inKindReportApproveMapper)).thenReturn(lambdaUpdate);
        List<InKindReportModel> report = inKindReportApproveDaoImpl.getReportByAgencyId("A00111", new LambdaQueryWrapper());

        // 验证结果
        Assert.assertNotNull(report);
    }

    /**
     * 测试批量保存
     */
    @Test
    void testBatchSave() {
        // 准备测试数据
        List<InKindReportApprove> approves = new ArrayList<>();
        final InKindReportApprove approve = new InKindReportApprove();
        approve.setReportId("1");
        approves.add(approve);

        // 调用待测试的方法
        ReflectionTestUtils.setField(inKindReportApproveDaoImpl, INKIND_REPORT_MODEL_MAPPER, inKindReportModelMapper);
        ReflectionTestUtils.setField(inKindReportApproveDaoImpl, BASE_MAPPER, inKindReportApproveMapper);
        final LambdaQueryChainWrapper<InKindReportApprove> lambdaUpdate = new LambdaQueryChainWrapper<>(inKindReportApproveMapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(inKindReportApproveMapper)).thenReturn(lambdaUpdate);
        inKindReportApproveDaoImpl.batchSave(approves);

        // 验证结果
        verify(inKindReportApproveMapper, times(1)).insert(approve);
    }

    /**
     * 测试根据 reportId 获取审批记录
     */
    @Test
    void testGetRatifyReportList() {
        // 准备测试数据
        final List<String> ids = Collections.singletonList("reportId1");

        // 调用待测试的方法
        ReflectionTestUtils.setField(inKindReportApproveDaoImpl, INKIND_REPORT_MODEL_MAPPER, inKindReportModelMapper);
        ReflectionTestUtils.setField(inKindReportApproveDaoImpl, BASE_MAPPER, inKindReportApproveMapper);
        final LambdaQueryChainWrapper<InKindReportApprove> lambdaUpdate = new LambdaQueryChainWrapper<>(inKindReportApproveMapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(inKindReportApproveMapper)).thenReturn(lambdaUpdate);
        List<InKindReportApprove> report = inKindReportApproveDaoImpl.getReportApproveList(ids);

        // 验证结果
        Assert.assertNotNull(report);
    }

    /**
     * 测试根据 reportId 获取最新的审批记录
     */
    @Test
    void testGetNewestReportApproveByReportId() {
        // 调用待测试的方法
        ReflectionTestUtils.setField(inKindReportApproveDaoImpl, INKIND_REPORT_MODEL_MAPPER, inKindReportModelMapper);
        ReflectionTestUtils.setField(inKindReportApproveDaoImpl, BASE_MAPPER, inKindReportApproveMapper);
        final LambdaQueryChainWrapper<InKindReportApprove> lambdaUpdate = new LambdaQueryChainWrapper<>(inKindReportApproveMapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(inKindReportApproveMapper)).thenReturn(lambdaUpdate);

        List<InKindReportApprove> report = inKindReportApproveDaoImpl.getNewestReportApproveByReportId(Collections.singletonList("reportId2"));
        // 验证结果
        Assert.assertNotNull(report);
    }

    /**
     * 测试根据 reportId 获取管理员已经签名的记录
     */
    @Test
    void testGetRatifySignatureReportApproveList() {
        // 调用待测试的方法
        ReflectionTestUtils.setField(inKindReportApproveDaoImpl, INKIND_REPORT_MODEL_MAPPER, inKindReportModelMapper);
        ReflectionTestUtils.setField(inKindReportApproveDaoImpl, BASE_MAPPER, inKindReportApproveMapper);
        final LambdaQueryChainWrapper<InKindReportApprove> lambdaUpdate = new LambdaQueryChainWrapper<>(inKindReportApproveMapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(inKindReportApproveMapper)).thenReturn(lambdaUpdate);

        final List<InKindReportApprove> report = inKindReportApproveDaoImpl.getRatifySignatureReportApproveList(Collections.singletonList("reportId3"));

        // 验证结果
        Assert.assertNotNull(report);
    }

}

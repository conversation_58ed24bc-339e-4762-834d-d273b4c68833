package com.learninggenie.mycare;


import com.learninggenie.api.util.OnCareXmlUtil;
import junit.framework.TestCase;
import org.junit.Ignore;
import org.junit.Test;

@Ignore
public class GetChildrenTest extends TestCase{
    // @Ignore
    @Test
    public void testGetData() throws Exception {
        IntegrationServices services = new IntegrationServices();
        IntegrationServicesSoap servicesSoap = services.getIntegrationServicesSoap();
        String locations = servicesSoap.getLocations(
                "<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\"> " +
                        "    <soap:Body> " +
                        "        <GetLocations xmlns=\"http://www.oncareoffice.com/\">" +
                        "            <data>" +
                        "                <Input>" +
                        "                    <Credentials>" +
                        "                        <UserName>LG_demo</UserName>" +
                        "                        <Password>LbRd28Uhq</Password>" +
                        "                    </Credentials>" +
                        "                </Input>" +
                        "            </data>" +
                        "        </GetLocations> " +
                        "    </soap:Body>" +
                        "</soap:Envelope>");
        System.out.print(locations);
    }

    @Test
    public void testGetLocation() throws Exception {
        IntegrationServices services = new IntegrationServices();
        IntegrationServicesSoap servicesSoap = services.getIntegrationServicesSoap();

        String data1 = servicesSoap.getClassrooms(" <Input>" +
                "                    <Credentials>" +
                "                        <UserName>LG_demo</UserName>" +
                "                        <Password>LbRd28Uhq</Password>" +
                "                    </Credentials>" +
                "                </Input>");

        System.out.print( OnCareXmlUtil.parseGroupXml(data1,"1233","456").size());
    }
}
package com.learninggenie.task;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.toolkit.ChainWrappers;
import com.learninggenie.api.provider.RemoteProvider;
import com.learninggenie.common.data.dao.StudentDao;
import com.learninggenie.common.data.dao.dashboard.StatisticsPaUpdateRecordDao;
import com.learninggenie.common.data.dao.dashboard.impl.StatisticsPaUpdateRecordDaoImpl;
import com.learninggenie.common.data.dao.export.ExportSftpSettingDao;
import com.learninggenie.common.data.entity.dashbord.StatisticsPaUpdateRecordEntity;
import com.learninggenie.common.data.entity.export.ExportSftpRecord;
import com.learninggenie.common.data.entity.export.ExportSftpSetting;
import com.learninggenie.common.data.enums.AutoExportDateType;
import com.learninggenie.common.data.mapper.mybatisplus.dashboard.StatisticsPaUpdateRecordMapper;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.After;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ExportSftpTaskTest {

    @Mock
    private ExportSftpSettingDao exportSftpSettingDao;

    @Mock
    private StudentDao studentDao;

    @Spy
    private StatisticsPaUpdateRecordDaoImpl statisticsPaUpdateRecordDao;

    @Mock
    private StatisticsPaUpdateRecordMapper statisticsPaUpdateRecordMapper;

    @Mock
    private RemoteProvider remoteProvider;

    private MockedStatic<ChainWrappers> chainWrappersMockedStatic;

    /**
     * 初始化 TableInfoHelper
     */
    @BeforeClass
    public static void beforeClass() {
//        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), InKindReportApprove.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), StatisticsPaUpdateRecordEntity.class);
    }

    /**
     * 初始化 chainWrappersMockedStatic
     */
    @Before
    public void beforeMethod() {
        chainWrappersMockedStatic = mockStatic(ChainWrappers.class);
    }

    /**
     * 关闭 chainWrappersMockedStatic
     */
    @After
    public void afterMethod() {
        chainWrappersMockedStatic.close();
    }

    @Test
    public void updateDashboardData_UpdatesDashboardData_WhenGroupJsonIsNotEmpty() {
        ExportSftpSetting setting = new ExportSftpSetting();
        setting.setId("setting1");
        setting.setTenantId("agency1");
        setting.setGroupJson("{\"centerModels\": [{\"groupModels\": [{\"groupId\": \"group1\"}]}]}");

        when(exportSftpSettingDao.getById(anyString())).thenReturn(setting);
        when(studentDao.getChildIdsByGroupIds(anyList())).thenReturn(Arrays.asList("child1", "child2", "child3"));


        ReflectionTestUtils.setField(statisticsPaUpdateRecordDao, "baseMapper", statisticsPaUpdateRecordMapper);

        final LambdaUpdateChainWrapper<StatisticsPaUpdateRecordEntity> lambdaUpdate = new LambdaUpdateChainWrapper<>(statisticsPaUpdateRecordMapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaUpdateChain(statisticsPaUpdateRecordMapper)).thenReturn(lambdaUpdate);


        ExportSftpRecord pendingRecord = new ExportSftpRecord();
        pendingRecord.setSettingId("setting1");
        pendingRecord.setTenantId("agency1");

        ExportSftpTask.updateDashboardData(exportSftpSettingDao, pendingRecord, studentDao, statisticsPaUpdateRecordDao, remoteProvider);

        verify(statisticsPaUpdateRecordDao, times(1)).lambdaUpdate();
        verify(remoteProvider, times(1)).callUpdatePdDashboardServer(anyString());
    }

    @Test
    public void updateDashboardData_DoesNotUpdateDashboardData_WhenGroupJsonIsEmpty() {
        ExportSftpSetting setting = new ExportSftpSetting();
        setting.setId("setting1");
        setting.setTenantId("agency1");
        setting.setGroupJson("");

        when(exportSftpSettingDao.getById(anyString())).thenReturn(setting);

        ExportSftpRecord pendingRecord = new ExportSftpRecord();
        pendingRecord.setSettingId("setting1");
        pendingRecord.setTenantId("agency1");
        ExportSftpTask.updateDashboardData(exportSftpSettingDao, pendingRecord, studentDao, statisticsPaUpdateRecordDao, remoteProvider);

        verify(statisticsPaUpdateRecordDao, times(0)).lambdaUpdate();
        verify(remoteProvider, times(1)).callUpdatePdDashboardServer(anyString());
    }

    @Test
    public void conversionDate_ReturnsConvertedDate_WhenAutoExportDateTypeIsCustomDateRange() {
        String autoExportDate = "2022-01-01,2022-01-03";

        String result = ExportSftpTask.conversionDate(AutoExportDateType.CUSTOM_DATE_RANGE, autoExportDate);

        assertEquals("2022-01-01,2022-01-02,2022-01-03", result);
    }

    @Test
    public void conversionDate_ReturnsOriginalDate_WhenAutoExportDateTypeIsNotCustomDateRange() {
        String autoExportDate = "2022-01-01";

        String result = ExportSftpTask.conversionDate(AutoExportDateType.CUSTOM_DATE_ARRAY, autoExportDate);

        assertEquals("2022-01-01", result);
    }
}
package com.learninggenie.task.snapshot;

import com.learninggenie.common.data.model.StudentSnapshotEntity;
import com.learninggenie.common.data.model.dashboard.CenterGroupChildCount;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class UpdateLockScoreTaskTest {

    @Mock
    private JdbcTemplate jdbcTemplate;

    @InjectMocks
    private UpdateLockScoreTask updateLockScoreTask;


    /**
     * 测试获取所有未被删除、lockScore 不为空的快照 Id
     */
    @Test
    public void testgetSnapshotIdList() {
        // 数据准备
        ReflectionTestUtils.setField(updateLockScoreTask, "jdbcTemplate", jdbcTemplate);

        List<String> snapshotIdList = new ArrayList<>();
        snapshotIdList.add("S00001");

        // 接口模拟
        when(jdbcTemplate.queryForList(anyString(), eq(String.class))).thenReturn(snapshotIdList);
        updateLockScoreTask.getSnapshotIdList();

        // 验证
        verify(jdbcTemplate, times(1)).queryForList(anyString(), eq(String.class));
    }

    /**
     * 测试 批量更新快照信息
     */
    @Test
    public void testBatchUpdate() {
        // 数据准备
        ReflectionTestUtils.setField(updateLockScoreTask, "jdbcTemplate", jdbcTemplate);
        List<String> snapshotIdList = new ArrayList<>();
        snapshotIdList.add("S00002");

        StudentSnapshotEntity studentSnapshotEntity = new StudentSnapshotEntity();
        studentSnapshotEntity.setData(new byte[]{1, 2, 3, 4, 5, 6, 7, 8, 9, 0});
        studentSnapshotEntity.setLockScore("[{\"childCount\":0,\"notesCount\":1,\"measureNotesCount\":56,\"distinctCenterMeasureCount\":56,\"distinctScoreMeasureCount\":56,\"distinctCenterKeyMeasureCount\":0,\"distinctScoreKeyMeasureCount\":0,\"iepDomainCount\":0,\"distinctIEPDomainCount\":0,\"distinctIEPRatingCount\":0,\"keyMeasureNotesCount\":0,\"openUpload\":false,\"iep\":true,\"eld\":false,\"keyMeasureNum\":0}]");

        // 接口模拟
        // when(jdbcTemplate.queryForObject(any(), any(), eq(StudentSnapshotEntity.class))).thenReturn(studentSnapshotEntity);
        updateLockScoreTask.batchUpdate(snapshotIdList);

        // 验证
        Assertions.assertEquals(1, snapshotIdList.size());
    }

    /**
     * 测试 更新数据
     */
    @Test
    public void testUpdateData() {
        // 数据准备
        ReflectionTestUtils.setField(updateLockScoreTask, "jdbcTemplate", jdbcTemplate);
        String snapshotId = "S00003";
        CenterGroupChildCount lockedScore = new CenterGroupChildCount();
        int i = 1;
        List<String> snapshotIdList = new ArrayList<>();
        snapshotIdList.add(snapshotId);
        boolean update = true;

        // 接口模拟
        // when(jdbcTemplate.batchUpdate(anyString(), any(BatchPreparedStatementSetter.class))).thenReturn(new int[]{1});
        updateLockScoreTask.updateData(snapshotId, lockedScore, i, snapshotIdList, update);

        // 验证
        Assertions.assertEquals(1, snapshotIdList.size());
    }

    /**
     * 测试 获取 LockedScore 对象
     */
    @Test
    public void testGetLockedScore() {
        // 数据准备
        StudentSnapshotEntity studentSnapshotEntity = new StudentSnapshotEntity();
        studentSnapshotEntity.setLockScore("[{\"childCount\":0,\"notesCount\":1,\"measureNotesCount\":56,\"distinctCenterMeasureCount\":56,\"distinctScoreMeasureCount\":56,\"distinctCenterKeyMeasureCount\":0,\"distinctScoreKeyMeasureCount\":0,\"iepDomainCount\":0,\"distinctIEPDomainCount\":0,\"distinctIEPRatingCount\":0,\"keyMeasureNotesCount\":0,\"openUpload\":false,\"iep\":true,\"eld\":false,\"keyMeasureNum\":1}]");

        // 接口模拟
        CenterGroupChildCount result = updateLockScoreTask.getLockedScore(studentSnapshotEntity);

        // 验证
        Assertions.assertEquals(1, result.getKeyMeasureNum());
    }


}
package com.learninggenie.task.dashboard;

import com.learninggenie.api.provider.RemoteProvider;
import com.learninggenie.common.data.dao.AgencyDao;
import com.learninggenie.common.sharding.ShardingProvider;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

/**
 * 使用 Mockito 扩展进行单元测试
 */
@ExtendWith(MockitoExtension.class)
public class DailyUpdateDataTaskTest {
    /**
     * 待测试的 DailyUpdateDataTask 实例
     */
    private DailyUpdateDataTask dailyUpdateDataTask;

    /**
     * 模拟 JdbcTemplate 实例
     */
    @Mock
    private JdbcTemplate shardingTemplate;

    /**
     * 模拟 ShardingProvider 实例
     */
    @Mock
    private ShardingProvider shardingProvider;

    /**
     * 模拟 RemoteProvider 实例
     */
    @Mock
    private RemoteProvider remoteProvider;

    /**
     * 模拟 AgencyDao 实例
     */
    @Mock
    private AgencyDao agencyDao;

    /**
     * 在每个测试开始前的设置
     */
    @BeforeEach
    public void setup() {
        // 创建待测试的 DailyUpdateDataTask 实例
        dailyUpdateDataTask = new DailyUpdateDataTask();
        // 使用反射工具设置待测试实例的私有字段
        ReflectionTestUtils.setField(dailyUpdateDataTask, "shardingTemplate", shardingTemplate);
        ReflectionTestUtils.setField(dailyUpdateDataTask, "shardingProvider", shardingProvider);
        ReflectionTestUtils.setField(dailyUpdateDataTask, "remoteProvider", remoteProvider);
        ReflectionTestUtils.setField(dailyUpdateDataTask, "agencyDao", agencyDao);
    }

    /**
     * 测试 updateFEDashboardData 方法
     */
    @Test
    public void testUpdateFEDashboardData() {
        // 模拟 SQL 查询结果
        List<DailyUpdateDataTask.UpdateFEData> updateFEDataList = new ArrayList<>();
        DailyUpdateDataTask.UpdateFEData updateFEData = new DailyUpdateDataTask.UpdateFEData();
        updateFEData.setAgencyId("agency1");
        updateFEData.setEnrollmentId("enrollment1");
        updateFEData.setDate(LocalDate.now());
        updateFEDataList.add(updateFEData);

        // 当调用 shardingTemplate.query 方法时，返回模拟的查询结果
        when(shardingTemplate.query(anyString(), any(Object[].class), any(RowMapper.class))).thenReturn(updateFEDataList);
        // 当调用 shardingProvider.convertShardingSqlWithPartitionKey 方法时，返回预设的 SQL 查询语句
        when(shardingProvider.convertShardingSqlWithPartitionKey(anyString(), anyInt())).thenReturn(DailyUpdateDataTask.SQL_GET_NO_UPDATE_FE_CHILD);
        // 当调用 remoteProvider.callTotalEngagementServer 方法时，返回 null
        when(remoteProvider.callTotalEngagementServer(anyList(), anyString())).thenReturn(null);

        // 调用待测试的方法
        dailyUpdateDataTask.updateFEDashboardData();

        // 验证 shardingTemplate.query 方法是否被正确调用
        verify(shardingTemplate, times(50)).query(anyString(), any(Object[].class), any(RowMapper.class));

        // 验证 shardingProvider.convertShardingSqlWithPartitionKey 方法是否被正确调用
        verify(shardingProvider, times(50)).convertShardingSqlWithPartitionKey(eq(DailyUpdateDataTask.SQL_GET_NO_UPDATE_FE_CHILD), anyInt());

        // 验证 updateFEDataList 是否不为空
        assertEquals(false, CollectionUtils.isEmpty(updateFEDataList));
    }

    /**
     * 测试 updatePDDashboardData 方法
     */
    @Test
    public void testUpdatePADashboardData() {
        // 模拟 agencyIds 列表
        List<String> agencyIds = new ArrayList<>();
        agencyIds.add("agency1");
        agencyIds.add("agency2");

        // 模拟 queryForList 方法返回 agencyIds 列表
        when(shardingTemplate.queryForList(anyString(), eq(String.class))).thenReturn(agencyIds);

        // 模拟 callUpdatePdDashboardServer 方法不做任何事情
        doNothing().when(remoteProvider).callUpdatePdDashboardServer(anyString());

        // 调用待测试的方法
        DailyUpdateDataTask.updatePADashboardData();

        // 验证 queryForList 方法是否被正确调用
        verify(shardingTemplate).queryForList("SELECT DISTINCT TOP 1000 AgencyId FROM [dbo].[statistics_pa_update_record] WHERE [Status] = '0'", String.class);

        // 验证 callUpdatePdDashboardServer 方法是否为每个 agencyId 被调用
        verify(remoteProvider, times(2)).callUpdatePdDashboardServer(anyString());
    }
}
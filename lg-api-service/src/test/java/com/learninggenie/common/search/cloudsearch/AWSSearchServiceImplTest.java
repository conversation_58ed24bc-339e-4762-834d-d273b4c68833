package com.learninggenie.common.search.cloudsearch;

import com.amazonaws.services.cloudsearchdomain.model.SearchRequest;
import com.amazonaws.services.cloudsearchdomain.model.SearchResult;
import com.learninggenie.common.data.entity.PageList;
import com.learninggenie.common.data.entity.lesson2.LessonEntity;
import com.learninggenie.common.data.model.search.LessonSearchRequest;
import com.learninggenie.common.utils.JsonUtil;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class AWSSearchServiceImplTest {

    @InjectMocks
    private AWSSearchServiceImpl searchService;

    @Mock
    private CloudSearchClient lessonClient;

    @Captor
    ArgumentCaptor<SearchRequest> lessonSearchCaptor;

    @Test
    public void searchLessons() {
        // 数据准备 -- 入参
        LessonSearchRequest request = JsonUtil.fromJson("{\"pageSize\":8,\"pageNum\":1,\"keyword\":\"number line\",\"orderKey\":\"\",\"ages\":[],\"measureIds\":[],\"themeIds\":[],\"frameworkIds\":[],\"mappedFrameworkIds\":[\"A169DBFA-CD98-46AC-B337-35607CC1EB29\",\"51544A61-4074-438A-90DB-82E5B02FD661\"],\"searchType\":\"MINE\",\"status\":\"PUBLISHED\",\"createUserId\":\"8A383FAA-808A-4D16-81E7-0E09A96636CB\"}", LessonSearchRequest.class);
        // 数据准备 -- 接口模拟
        SearchResult searchResult = JsonUtil.fromJson("{\"status\":{\"timems\":14,\"rid\":\"kcKPvLEwp2UK1Ct5\"},\"hits\":{\"found\":27,\"start\":0,\"hit\":[{\"id\":\"AD10A098-10D9-4E18-B7F6-053B9C41987F\",\"fields\":{\"activity_time\":[\"20\"],\"create_user_id\":[\"8A383FAA-808A-4D16-81E7-0E09A96636CB\"],\"like_count\":[\"0\"],\"cover_media_ids\":[\"60C56F88-7C97-4977-955A-1CDA33C249B0\"],\"is_inactive\":[\"0\"],\"framework_id\":[\"A21BC800-2FF5-E411-AF66-02C72B94B99B\"],\"type\":[\"PERSONAL\"],\"_score\":[\"300882.88\"],\"is_deleted\":[\"0\"],\"age_group_names\":[\"[\\\"K (5-6)\\\"]\"],\"name\":[\"Number Line\"],\"prepare_time\":[\"20\"],\"favorite_count\":[\"0\"],\"status\":[\"PUBLISHED\"]},\"exprs\":{},\"highlights\":{}},{\"id\":\"5CD58E25-7261-4866-A618-A2490DC931A9\",\"fields\":{\"activity_time\":[\"15\"],\"create_user_id\":[\"8A383FAA-808A-4D16-81E7-0E09A96636CB\"],\"like_count\":[\"0\"],\"cover_media_ids\":[\"EA22EDDB-929A-40E1-9FD1-5C3C66A98FA8\"],\"is_inactive\":[\"0\"],\"framework_id\":[\"********-BDCE-E411-AF66-02C72B94B99B\"],\"type\":[\"PERSONAL\"],\"_score\":[\"10000.0\"],\"is_deleted\":[\"0\"],\"age_group_names\":[\"[\\\"TK (4-5)\\\"]\"],\"name\":[\"Cutting lines\"],\"prepare_time\":[\"10\"],\"favorite_count\":[\"0\"],\"status\":[\"PUBLISHED\"]},\"exprs\":{},\"highlights\":{}},{\"id\":\"A4DB21C8-B706-4F50-990E-478B9F0459E9\",\"fields\":{\"activity_time\":[\"20\"],\"create_user_id\":[\"8A383FAA-808A-4D16-81E7-0E09A96636CB\"],\"like_count\":[\"0\"],\"cover_media_ids\":[\"74C5CD0B-166F-4BDF-957E-60E1974EFEA9\"],\"is_inactive\":[\"0\"],\"framework_id\":[\"A21BC800-2FF5-E411-AF66-02C72B94B99B\"],\"type\":[\"PERSONAL\"],\"_score\":[\"17.179462\"],\"is_deleted\":[\"0\"],\"age_group_names\":[\"[\\\"K (5-6)\\\"]\"],\"name\":[\"Sleepy Sam\"],\"prepare_time\":[\"10\"],\"favorite_count\":[\"0\"],\"status\":[\"PUBLISHED\"]},\"exprs\":{},\"highlights\":{}},{\"id\":\"9C0AF60C-F1AA-4A81-AB5B-5FF9634E7593\",\"fields\":{\"activity_time\":[\"15\"],\"create_user_id\":[\"8A383FAA-808A-4D16-81E7-0E09A96636CB\"],\"like_count\":[\"0\"],\"cover_media_ids\":[\"E4B6A98E-EBD7-4DE3-9D72-E8D15A637F32\"],\"is_inactive\":[\"0\"],\"framework_id\":[\"A21BC800-2FF5-E411-AF66-02C72B94B99B\"],\"type\":[\"PERSONAL\"],\"_score\":[\"17.179462\"],\"is_deleted\":[\"0\"],\"age_group_names\":[\"[\\\"K (5-6)\\\"]\"],\"name\":[\"Magazine Shape Sorting\"],\"prepare_time\":[\"20\"],\"favorite_count\":[\"0\"],\"status\":[\"PUBLISHED\"]},\"exprs\":{},\"highlights\":{}},{\"id\":\"C678F267-D970-46F9-B3AB-1262908621AB\",\"fields\":{\"activity_time\":[\"20\"],\"create_user_id\":[\"8A383FAA-808A-4D16-81E7-0E09A96636CB\"],\"like_count\":[\"0\"],\"cover_media_ids\":[\"BFE16C83-DBE0-4F38-BC5F-704B268515EB\"],\"is_inactive\":[\"0\"],\"framework_id\":[\"********-BDCE-E411-AF66-02C72B94B99B\"],\"type\":[\"PERSONAL\"],\"_score\":[\"16.238636\"],\"is_deleted\":[\"0\"],\"age_group_names\":[\"[\\\"TK (4-5)\\\"]\"],\"name\":[\"Simple Addition\"],\"prepare_time\":[\"10\"],\"favorite_count\":[\"0\"],\"status\":[\"PUBLISHED\"]},\"exprs\":{},\"highlights\":{}},{\"id\":\"5EE5921F-8389-4589-98B9-E4E87CAF977E\",\"fields\":{\"activity_time\":[\"15\"],\"create_user_id\":[\"8A383FAA-808A-4D16-81E7-0E09A96636CB\"],\"like_count\":[\"0\"],\"cover_media_ids\":[\"8DD15D5D-F8E4-4117-8356-5E1BEACC5A6E\"],\"is_inactive\":[\"0\"],\"framework_id\":[\"********-BDCE-E411-AF66-02C72B94B99B\"],\"type\":[\"PERSONAL\"],\"_score\":[\"15.603818\"],\"is_deleted\":[\"0\"],\"age_group_names\":[\"[\\\"PS/PK (3-4)\\\"]\"],\"name\":[\"Counting Transportation Syllables\"],\"prepare_time\":[\"5\"],\"favorite_count\":[\"0\"],\"status\":[\"PUBLISHED\"]},\"exprs\":{},\"highlights\":{}},{\"id\":\"693CE379-1CE7-4D41-BE6C-F2D3D10546BC\",\"fields\":{\"activity_time\":[\"15\"],\"create_user_id\":[\"8A383FAA-808A-4D16-81E7-0E09A96636CB\"],\"like_count\":[\"1\"],\"cover_media_ids\":[\"83673025-5C34-473A-9FA7-881DB4D02234\"],\"is_inactive\":[\"0\"],\"framework_id\":[\"E163164F-BDCE-E411-AF66-02C72B94B99B\"],\"type\":[\"PERSONAL\"],\"_score\":[\"15.493623\"],\"is_deleted\":[\"0\"],\"age_group_names\":[\"[\\\"Toddler (1-3)\\\"]\"],\"name\":[\"Counting Eggs\"],\"prepare_time\":[\"10\"],\"favorite_count\":[\"1\"],\"status\":[\"PUBLISHED\"]},\"exprs\":{},\"highlights\":{}},{\"id\":\"F9326B2E-9C1A-47A6-BB53-2CCB124708F3\",\"fields\":{\"activity_time\":[\"15\"],\"create_user_id\":[\"8A383FAA-808A-4D16-81E7-0E09A96636CB\"],\"like_count\":[\"0\"],\"cover_media_ids\":[\"801427F0-0C43-4767-A048-39ACCBFF4223\"],\"is_inactive\":[\"0\"],\"framework_id\":[\"********-BDCE-E411-AF66-02C72B94B99B\"],\"type\":[\"PERSONAL\"],\"_score\":[\"14.8358345\"],\"is_deleted\":[\"0\"],\"age_group_names\":[\"[\\\"TK (4-5)\\\"]\"],\"name\":[\"Pumpkin Shapes\"],\"prepare_time\":[\"15\"],\"favorite_count\":[\"0\"],\"status\":[\"PUBLISHED\"]},\"exprs\":{},\"highlights\":{}}]},\"facets\":{},\"stats\":{},\"sdkResponseMetadata\":{\"metadata\":{}},\"sdkHttpMetadata\":{\"allHeaders\":{\"transfer-encoding\":[\"chunked\"],\"Connection\":[\"keep-alive\"],\"Content-Type\":[\"application/json; charset\\u003dUTF-8\"]},\"httpHeaders\":{\"Connection\":\"keep-alive\",\"Content-Type\":\"application/json; charset\\u003dUTF-8\",\"transfer-encoding\":\"chunked\"},\"httpStatusCode\":200}}", SearchResult.class);
        Mockito.when(lessonClient.search(Mockito.any())).thenReturn(searchResult);
        // 调用
        PageList<LessonEntity> page = searchService.searchLessons(request);
        // 校验 -- 校验 CloudSearch search 入参
        Mockito.verify(lessonClient, Mockito.times(1)).search(lessonSearchCaptor.capture());
        Assert.assertTrue(lessonSearchCaptor.getValue().getQuery().contains("number")); // 搜索表达式应该包含 number
        Assert.assertTrue(lessonSearchCaptor.getValue().getQuery().contains("line")); // 搜索表达式应该包含 line
        Assert.assertTrue(lessonSearchCaptor.getValue().getQuery().contains("number line")); // 搜索表达式应该包含 number line 短语
        // 校验 -- 出参校验
        Assert.assertEquals(page.getTotal(), searchResult.getHits().getFound()); // 总数应该等于匹配数
    }
}
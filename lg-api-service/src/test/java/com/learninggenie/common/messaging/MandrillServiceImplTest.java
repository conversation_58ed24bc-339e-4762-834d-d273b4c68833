package com.learninggenie.common.messaging;


import com.microtripit.mandrillapp.lutung.MandrillApi;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class MandrillServiceImplTest {

    @InjectMocks
    private MandrillServiceImpl mandrillService;

    @Mock
    private MandrillApi mandrillApi;

    /**
     * 测试发送邮件
     */
    @Test
    public void testSendAsync() {
        // 模拟参数
        EmailModel email = new EmailModel();
        email.setSubject("Adapt Your Class with Universal Design for Learning and Cultural Responsiveness!"); // 邮件主题
        email.setHtml("text"); // 邮件内容
        email.setTo("<EMAIL>");
        email.setFromEmail("<EMAIL>");
        email.setImportant(true); // 设置邮件重要性
        // 模拟参数
        ReflectionTestUtils.setField(mandrillService, "appSupportEmail", "<EMAIL>");
        ReflectionTestUtils.setField(mandrillService, "appName", "Learning Genie");

        // 模拟方法调用
        mandrillService.sendAsync(email);
    }

    /**
     * 测试发送邮件
     */
    @Test
    public void testSendAsync1() {
        // 模拟参数
        EmailModel email = new EmailModel();
        email.setSubject("Adapt Your Class with Universal Design for Learning and Cultural Responsiveness!"); // 邮件主题
        email.setHtml("text"); // 邮件内容
        email.setTo("<EMAIL>");
        email.setImportant(true); // 设置邮件重要性
        // 模拟参数
        ReflectionTestUtils.setField(mandrillService, "appSupportEmail", "<EMAIL>");
        ReflectionTestUtils.setField(mandrillService, "appName", "Learning Genie");

        // 模拟方法调用
        mandrillService.sendAsync(email);
    }

}

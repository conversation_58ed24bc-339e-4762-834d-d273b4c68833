package com.learninggenie.common.report;

import com.learninggenie.common.data.dao.AgencyDao;
import com.learninggenie.common.data.dao.DomainDao;
import com.learninggenie.common.data.dao.PortfolioDao;
import com.learninggenie.common.data.dao.StudentDao;
import com.learninggenie.common.data.entity.AgencyEntity;
import com.learninggenie.common.data.entity.AgencyMetaDataEntity;
import com.learninggenie.common.data.enums.AgencyMetaKey;
import com.learninggenie.common.data.model.DomainEntity;
import com.learninggenie.common.data.model.KeyMeasureEntity;
import com.learninggenie.common.data.model.ScoreTemplateEntity;
import com.learninggenie.common.score.PortfolioScoreViewModel;
import com.learninggenie.common.score.RatingService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@RunWith(MockitoJUnitRunner.class)
public class AnalysisServiceImplTest {

    @InjectMocks
    private AnalysisServiceImpl analysisService;

    @Mock
    private DomainDao domainDao;

    @Mock
    private PortfolioDao portfolioDao;

    @Mock
    private AgencyDao agencyDao;

    @Mock
    private RatingService ratingService;

    @Mock
    private StudentDao studentDao;

    /**
     * Case: 机构开启了完成所有测评点才能锁定开关，且完成了所有测评点评分
     * 结果: 返回 true
     */
    @Test
    public void testCheckMeasures() {
        // 准备数据
        String agencyId = UUID.randomUUID().toString(); // 机构 Id
        String childId = UUID.randomUUID().toString(); // 学生 Id
        String groupId = UUID.randomUUID().toString(); // 班级 Id
        String fromDate = "2019-01-01"; // 开始时间
        String toDate = "2019-01-31"; // 结束时间
        String frameworkId = UUID.randomUUID().toString(); // 框架 Id
        // 框架实体
        DomainEntity framework = new DomainEntity();
        framework.setId(frameworkId);
        // mock 框架实体
        when(domainDao.getDomain(frameworkId)).thenReturn(framework);
        // 评分模版实体
        ScoreTemplateEntity scoreTemplate = new ScoreTemplateEntity();
        scoreTemplate.setPortfolioId(frameworkId); // 设置框架 Id
        // mock 评分模版实体
        when(portfolioDao.loadScoreTemplate(frameworkId)).thenReturn(scoreTemplate);
        // 机构实体
        AgencyEntity agency = new AgencyEntity();
        agency.setId(agencyId);
        // mock 机构实体
        when(agencyDao.getByGroupId(groupId)).thenReturn(agency);
        // 框架下所有测评点信息
        List<DomainEntity> domainEntities = new ArrayList<>();
        String measureId = UUID.randomUUID().toString();
        // 框架下测评点实体
        DomainEntity domainEntity = new DomainEntity();
        domainEntity.setId(measureId); // 设置测评点 Id
        domainEntities.add(domainEntity);
        // mock 框架下所有测评点信息
        when(domainDao.getAllChildDomains(frameworkId)).thenReturn(domainEntities);
        // 学生评分信息
        PortfolioScoreViewModel portfolioScoreViewModel = new PortfolioScoreViewModel();
        // mock 学生评分信息
        when(ratingService.getScoreResults(childId, frameworkId, fromDate, toDate)).thenReturn(portfolioScoreViewModel);
        // 机构的 Meta 实体
        AgencyMetaDataEntity meta = new AgencyMetaDataEntity();
        meta.setMetaValue("true");
        // mock 机构的 Meta 实体
        when(agencyDao.getMeta(agencyId, AgencyMetaKey.COMPLETE_RATING_REQUIRED_FOR_CHILD_LOCK.toString())).thenReturn(meta);

        // 调用方法
        CheckLockResult lockResult = analysisService.checkMeasures(childId, groupId, fromDate, toDate, frameworkId);

        // 验证结果 完成所有评分
        Assert.assertTrue(lockResult.isSuccess());
    }

    /**
     * Case: 机构开启了核心测评点开关，且只完成了核心测评点评分
     * 结果: 返回 true
     */
    @Test
    public void testCheckMeasures1() {
        // 准备数据
        String agencyId = UUID.randomUUID().toString(); // 机构 Id
        String childId = UUID.randomUUID().toString(); // 学生 Id
        String groupId = UUID.randomUUID().toString(); // 班级 Id
        String fromDate = "2019-01-01"; // 开始时间
        String toDate = "2019-01-31"; // 结束时间
        String frameworkId = UUID.randomUUID().toString(); // 框架 Id
        // 框架实体
        DomainEntity framework = new DomainEntity();
        framework.setId(frameworkId);
        // mock 框架实体
        when(domainDao.getDomain(frameworkId)).thenReturn(framework);
        // 评分模版实体
        ScoreTemplateEntity scoreTemplate = new ScoreTemplateEntity();
        scoreTemplate.setPortfolioId(frameworkId); // 设置框架 Id
        // mock 评分模版实体
        when(portfolioDao.loadScoreTemplate(frameworkId)).thenReturn(scoreTemplate);
        // 机构实体
        AgencyEntity agency = new AgencyEntity();
        agency.setId(agencyId);
        // mock 机构实体
        when(agencyDao.getByGroupId(groupId)).thenReturn(agency);
        // 框架下所有测评点信息
        List<DomainEntity> domainEntities = new ArrayList<>();
        String measureId = UUID.randomUUID().toString();
        // 框架下测评点实体
        DomainEntity domainEntity = new DomainEntity();
        domainEntity.setId(measureId); // 设置测评点 Id
        domainEntities.add(domainEntity);
        // mock 框架下所有测评点信息
        when(domainDao.getAllChildDomains(frameworkId)).thenReturn(domainEntities);
        // 学生评分信息
        PortfolioScoreViewModel portfolioScoreViewModel = new PortfolioScoreViewModel();
        // mock 学生评分信息
        when(ratingService.getScoreResults(childId, frameworkId, fromDate, toDate)).thenReturn(portfolioScoreViewModel);
        // 机构的 Meta 实体
        AgencyMetaDataEntity meta = new AgencyMetaDataEntity();
        meta.setMetaValue("false");
        // mock 机构的 Meta 实体
        when(agencyDao.getMeta(agencyId, AgencyMetaKey.COMPLETE_RATING_REQUIRED_FOR_CHILD_LOCK.toString())).thenReturn(meta);
        // 开启核心测评点机构实体
        AgencyMetaDataEntity agencyMetaData = new AgencyMetaDataEntity();
        agencyMetaData.setMetaValue("true");
        // mock 开启核心测评点机构实体
        when(agencyDao.getMeta(agencyId, AgencyMetaKey.DRDP_KEY_MEASURE_SETTING.toString())).thenReturn(agencyMetaData);
        // 核心测评点实体列表
        List<KeyMeasureEntity> keyMeasuresSetting = new ArrayList<>();
        // 核心测评点实体
        KeyMeasureEntity keyMeasureEntity = new KeyMeasureEntity();
        keyMeasureEntity.setMeasureId(measureId); // 设置测评点 Id
        keyMeasuresSetting.add(keyMeasureEntity);
        // mock 核心测评点实体列表
        when(domainDao.getKeyMeasuresSetting(agencyId, frameworkId)).thenReturn(keyMeasuresSetting);

        // 调用方法
        CheckLockResult lockResult = analysisService.checkMeasures(childId, groupId, fromDate, toDate, frameworkId);

        // 验证结果 完成核心测评点评分
        Assert.assertTrue(lockResult.isSuccess());
    }
}

package com.learninggenie.common.utils;

import com.learninggenie.common.data.entity.GroupPeriodEntity;
import com.learninggenie.common.data.entity.RatingPeriodEntity;
import com.learninggenie.common.data.model.PeriodsPerodEntity;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;


import java.util.ArrayList;
import java.util.Date;
import java.util.List;


import static org.mockito.Mockito.mockStatic;

/**
 * PeriodUtil 测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class PeriodUtilTest {

    /**
     * 测试给周期排序
     */
    @Test
    public void testSortPeriods() {
        // 准备数据
        List<RatingPeriodEntity> periodEntities = new ArrayList<>();
        RatingPeriodEntity periodEntity1 = new RatingPeriodEntity();
        Date parseDate = TimeUtil.parseDate("2021-01-01");
        periodEntity1.setFromAtLocal(parseDate); // 设置周期开始时间
        periodEntities.add(periodEntity1);
        RatingPeriodEntity periodEntity2 = new RatingPeriodEntity();
        Date parseDate2 = TimeUtil.parseDate("2021-01-02"); // 设置周期开始时间
        periodEntity2.setFromAtLocal(parseDate2);
        periodEntities.add(periodEntity2);

        // 调用测试方法
        PeriodUtil.sortPeriods(periodEntities);

        // 断言排序后的结果
        assert periodEntities.get(0).getFromAtLocal().equals(parseDate);
    }

    /**
     * 测试两个周期是否冲突
     */
    @Test
    public void testIsConflict() {
        // 准备数据
        GroupPeriodEntity groupPeriodEntity = new GroupPeriodEntity();
        Date parseDate = TimeUtil.parseDate("2021-01-01");
        groupPeriodEntity.setFromAtLocal(parseDate); // 设置周期开始时间
        Date parseDate2 = TimeUtil.parseDate("2021-03-01");
        groupPeriodEntity.setToAtLocal(parseDate2); // 设置周期结束时间
        List<GroupPeriodEntity> groupPeriodEntities = new ArrayList<>();
        GroupPeriodEntity groupPeriodEntity1 = new GroupPeriodEntity();
        Date parseDate3 = TimeUtil.parseDate("2021-02-01");
        groupPeriodEntity1.setFromAtLocal(parseDate3); // 设置周期开始时间
        Date parseDate4 = TimeUtil.parseDate("2021-04-01");
        groupPeriodEntity1.setToAtLocal(parseDate4); // 设置周期结束时间
        groupPeriodEntities.add(groupPeriodEntity1);

        // 调用测试方法
        boolean conflict = PeriodUtil.isConflict(groupPeriodEntity, groupPeriodEntities);

        // 断言结果
        assert conflict;
    }

    /**
     * 测试两个周期日期是否相同
     */
    @Test
    public void testIsEqualsDate() {
        // 准备数据
        Date from1 = TimeUtil.parseDate("2021-01-01");
        Date to1 = TimeUtil.parseDate("2021-03-01");
        Date from2 = TimeUtil.parseDate("2021-01-01");
        Date to2 = TimeUtil.parseDate("2021-03-01");

        // 调用测试方法
        boolean equalsDate = PeriodUtil.isEqualsDate(from1, to1, from2, to2);

        // 断言结果
        assert equalsDate;
    }

    /**
     * 测试班级周期是否相等
     */
    @Test
    public void testIsEqualsGroupPeriods() {
        // 准备数据
        List<GroupPeriodEntity> groupPeriodEntities = new ArrayList<>();
        GroupPeriodEntity groupPeriodEntity = new GroupPeriodEntity();
        Date parseDate = TimeUtil.parseDate("2021-01-01");
        groupPeriodEntity.setFromAtLocal(parseDate); // 设置周期开始时间
        Date parseDate2 = TimeUtil.parseDate("2021-03-01");
        groupPeriodEntity.setToAtLocal(parseDate2); // 设置周期结束时间
        groupPeriodEntities.add(groupPeriodEntity);
        List<GroupPeriodEntity> groupPeriodEntities2 = new ArrayList<>();
        GroupPeriodEntity groupPeriodEntity1 = new GroupPeriodEntity();
        groupPeriodEntity1.setFromAtLocal(parseDate); // 设置周期开始时间
        Date parseDate3 = TimeUtil.parseDate("2021-04-01");
        groupPeriodEntity1.setToAtLocal(parseDate3); // 设置周期结束时间
        groupPeriodEntities2.add(groupPeriodEntity1);

        // 调用测试方法
        boolean equalsGroupPeriods = PeriodUtil.isEqualsGroupPeriods(groupPeriodEntities, groupPeriodEntities2);

        // 断言结果
        assert !equalsGroupPeriods;
    }

    /**
     * 测试班级周期是否冲突
     */
    @Test
    public void testIsDuplicateGroupPeriodDate() {
        // 准备数据
        GroupPeriodEntity groupPeriodEntity = new GroupPeriodEntity();
        Date parseDate = TimeUtil.parseDate("2021-01-01");
        groupPeriodEntity.setFromAtLocal(parseDate); // 设置周期开始时间
        Date parseDate2 = TimeUtil.parseDate("2021-03-01");
        groupPeriodEntity.setToAtLocal(parseDate2); // 设置周期结束时间
        List<GroupPeriodEntity> groupPeriodEntities = new ArrayList<>();
        GroupPeriodEntity groupPeriodEntity1 = new GroupPeriodEntity();
        groupPeriodEntity1.setFromAtLocal(parseDate); // 设置周期开始时间
        Date parseDate3 = TimeUtil.parseDate("2021-04-01");
        groupPeriodEntity1.setToAtLocal(parseDate3); // 设置周期结束时间
        groupPeriodEntities.add(groupPeriodEntity1);

        // 调用测试方法
        boolean duplicateGroupPeriodDate = PeriodUtil.isDuplicateGroupPeriodDate(groupPeriodEntity, groupPeriodEntities);

        // 断言结果
        assert !duplicateGroupPeriodDate;
    }

    /**
     * 测试获取当前学年
     */
    @Test
    public void testGetCurrentSchoolYear() {
        // 准备数据
        String currentTime = "1969-05-01 00:00:00.000";

        // 调用测试方法
        String currentSchoolYear = PeriodUtil.getCurrentSchoolYear(currentTime);

        // 断言结果
        assert "1968-1969".equals(currentSchoolYear);
    }

    /**
     * 测试根据时间，计算该时间所处的学年
     */
    @Test
    public void testGetCurrentSchoolYearByDate() {
        // 准备数据
        Date date = TimeUtil.parseDate("2021-01-01");

        // 调用测试方法
        String currentSchoolYearByDate = PeriodUtil.getCurrentSchoolYear(date);

        // 断言结果
        assert "2020-2021".equals(currentSchoolYearByDate);
    }

    /**
     * 测试检查周期是否在活动周期内
     */
    @Test
    public void testCheckActivePeriod() {
        // 准备数据
        List<GroupPeriodEntity> periodEntities = new ArrayList<>();
        GroupPeriodEntity periodEntity = new GroupPeriodEntity();
        Date parseDate = TimeUtil.parseDate("2021-01-01");
        periodEntity.setFromAtLocal(parseDate); // 设置周期开始时间
        Date parseDate2 = TimeUtil.parseDate("2021-03-01");
        periodEntity.setToAtLocal(parseDate2); // 设置周期结束时间
        periodEntity.setIsActive(true);
        periodEntities.add(periodEntity);

        // 调用测试方法
        PeriodUtil.checkActivePeriod(periodEntities);

        // 断言结果
        assert periodEntities.get(0).getIsActive();
    }

    /**
     * 测试检查周期是否在活动周期内
     */
    @Test
    public void testCheckActivePeriod1() {
        // 准备数据
        List<GroupPeriodEntity> periodEntities = new ArrayList<>();
        GroupPeriodEntity periodEntity = new GroupPeriodEntity();
        Date parseDate = TimeUtil.parseDate("2021-01-01");
        periodEntity.setFromAtLocal(parseDate); // 设置周期开始时间
        Date parseDate2 = TimeUtil.parseDate("2021-03-01");
        periodEntity.setToAtLocal(parseDate2); // 设置周期结束时间
        periodEntities.add(periodEntity);

        // 调用测试方法
        PeriodUtil.checkActivePeriod(periodEntities);

        // 断言结果
        assert periodEntities.get(0).getIsActive();
    }

    /**
     * 测试设置活跃周期
     */
    @Test
    public void testSetActivePeriod() {
        // 准备数据
        List<PeriodsPerodEntity> periodEntities = new ArrayList<>();
        PeriodsPerodEntity periodEntity = new PeriodsPerodEntity();
        periodEntity.setIsActive(true);
        byte b = 1;
        periodEntity.setActive(b);
        Date parseDate = TimeUtil.parseDate("2021-01-01");
        periodEntity.setFromAtLocal(parseDate); // 设置周期开始时间
        Date parseDate2 = TimeUtil.parseDate("2021-03-01");
        periodEntity.setToAtLocal(parseDate2); // 设置周期结束时间
        periodEntities.add(periodEntity);

        // 调用测试方法
        PeriodUtil.setActivePeriod(periodEntities);

        // 断言结果
        assert periodEntities.get(0).getActive() == 1;
    }

    /**
     * 测试设置活跃周期
     */
    @Test
    public void testSetEnrollmentActivePeriod() {
        // 准备数据
        List<RatingPeriodEntity> periodEntities = new ArrayList<>();
        RatingPeriodEntity periodEntity = new RatingPeriodEntity();
        periodEntity.setActived(true);
        Date parseDate = TimeUtil.parseDate("2021-01-01");
        periodEntity.setFromAtLocal(parseDate); // 设置周期开始时间
        Date parseDate2 = TimeUtil.parseDate("2021-03-01");
        periodEntity.setToAtLocal(parseDate2); // 设置周期结束时间
        periodEntity.setAlias("2020-2021");
        periodEntities.add(periodEntity);

        // 调用测试方法
        PeriodUtil.setEnrollmentActivePeriod(periodEntities);

        // 断言结果
        assert periodEntities.get(0).isActived();
    }

    /**
     * 测试转换周期
     */
    @Test
    public void testConvertGroupPeriod() {
        // 准备数据
        GroupPeriodEntity groupPeriodEntity = new GroupPeriodEntity();
        Date parseDate = TimeUtil.parseDate("2021-01-01");
        groupPeriodEntity.setFromAtLocal(parseDate); // 设置周期开始时间
        Date parseDate2 = TimeUtil.parseDate("2021-03-01");
        groupPeriodEntity.setToAtLocal(parseDate2); // 设置周期结束时间
        groupPeriodEntity.setIsActive(true);
        groupPeriodEntity.setAlias("2020-2021");
        String childId = "adcef91b-6978-4a05-9093-e79905c6a26d";

        // 调用测试方法
        RatingPeriodEntity ratingPeriodEntity = PeriodUtil.convertGroupPeriod(groupPeriodEntity, childId);

        // 断言结果
        assert ratingPeriodEntity.getEnrollmentId().equals(childId);
    }

    /**
     * 测试转换周期别名
     */
    @Test
    public void testConvertAlias() {
        // 准备数据
        String alias = "Spring 2024";

        // 调用测试方法
        String convertAlias = PeriodUtil.convertAliasType(alias);

        // 断言结果
        assert "2023-2024 Time3".equals(convertAlias);
    }

    /**
     * 测试转换周期别名
     */
    @Test
    public void testConvertAlias2() {
        // 准备数据
        String alias = "2023-2024 Time1";

        // 调用测试方法
        String convertAlias = PeriodUtil.convertAliasType(alias);

        // 断言结果
        assert "2023-2024 Fall".equals(convertAlias);
    }

    /**
     * 测试转换周期别名
     */
    @Test
    public void testSwitchAliasType() {
        // 准备数据
        String alias = "2023-2024 Time1";

        // 调用测试方法
        String convertAlias = PeriodUtil.switchAliasType(alias, 1);

        // 断言结果
        assert "2023-2024 Spring".equals(convertAlias);
    }

    /**
     * 测试获取当前学年
     */
    @Test
    public void testGetCurrentSchoolYear2() {
        // 准备数据
        Date mockDate = TimeUtil.parseDate("2021-01-01");
        MockedStatic<TimeUtil> mockedStatic = mockStatic(TimeUtil.class);
        mockedStatic.when(TimeUtil::getUtcNow).thenReturn(mockDate);
        mockedStatic.when(()-> TimeUtil.getIntMonth(mockDate)).thenReturn(1);
        mockedStatic.when(()-> TimeUtil.getYear(mockDate)).thenReturn(2021);

        // 调用测试方法
        String currentSchoolYear = PeriodUtil.getCurrentSchoolYear();
        mockedStatic.close();

        // 断言结果
        assert "2020-2021".equals(currentSchoolYear);
    }

    /**
     * 测试获取下个学年
     */
    @Test
    public void testGetNextSchoolYear() {
        // 准备数据
        Date mockDate = TimeUtil.parseDate("2021-01-01");
        MockedStatic<TimeUtil> mockedStatic = mockStatic(TimeUtil.class);
        mockedStatic.when(TimeUtil::getUtcNow).thenReturn(mockDate);
        mockedStatic.when(()-> TimeUtil.getIntMonth(mockDate)).thenReturn(1);
        mockedStatic.when(()-> TimeUtil.getYear(mockDate)).thenReturn(2021);

        // 调用测试方法
        String currentSchoolYear = PeriodUtil.getNextSchoolYear();
        mockedStatic.close();

        // 断言结果
        assert "2021-2022".equals(currentSchoolYear);
    }

    /**
     * 测试获取前一个学年
     */
    @Test
    public void testGetLastSchoolYear() {
        // 准备数据
        Date mockDate = TimeUtil.parseDate("2021-01-01");
        MockedStatic<TimeUtil> mockedStatic = mockStatic(TimeUtil.class);
        mockedStatic.when(TimeUtil::getUtcNow).thenReturn(mockDate);
        mockedStatic.when(()-> TimeUtil.getIntMonth(mockDate)).thenReturn(1);
        mockedStatic.when(()-> TimeUtil.getYear(mockDate)).thenReturn(2021);

        // 调用测试方法
        String currentSchoolYear = PeriodUtil.getLastSchoolYear();
        mockedStatic.close();

        // 断言结果
        assert "2019-2020".equals(currentSchoolYear);
    }

    /**
     * 测试设置当前学年活跃周期
     */
    @Test
    public void testSetCurrentSchoolYearActivePeriod() {
        // 准备数据
        List<PeriodsPerodEntity> periodEntities = new ArrayList<>();
        PeriodsPerodEntity periodEntity = new PeriodsPerodEntity();
        periodEntity.setIsActive(true);
        byte b = 1;
        periodEntity.setActive(b);
        Date parseDate = TimeUtil.parseDate("2021-01-01");
        periodEntity.setFromAtLocal(parseDate); // 设置周期开始时间
        Date parseDate2 = TimeUtil.parseDate("2021-03-01");
        periodEntity.setToAtLocal(parseDate2); // 设置周期结束时间
        periodEntities.add(periodEntity);

        // 调用测试方法
        PeriodUtil.setCurrentSchoolYearActivePeriod(periodEntities);

        // 断言结果
        assert periodEntities.get(0).getActive() == 0;

    }

    /**
     * 测试周期列表中是否有活跃周期
     */
    @Test
    public void testHasActivePeriod() {
        // 准备数据
        List<RatingPeriodEntity> periodEntities = new ArrayList<>();
        RatingPeriodEntity periodEntity = new RatingPeriodEntity();
        periodEntity.setActived(true);
        Date parseDate = TimeUtil.parseDate("2021-01-01");
        periodEntity.setFromAtLocal(parseDate); // 设置周期开始时间
        Date parseDate2 = TimeUtil.parseDate("2021-03-01");
        periodEntity.setToAtLocal(parseDate2); // 设置周期结束时间
        periodEntity.setAlias("2020-2021");
        periodEntities.add(periodEntity);

        // 调用测试方法
        boolean hasActivePeriod = PeriodUtil.hasActivePeriod(periodEntities);

        // 断言结果
        assert hasActivePeriod;
    }

    /**
     * 测试直接获取UTC 时间下的当前学年和下一个学年的所有周期
     */
    @Test
    public void testGetAlias() {
        // 准备数据
        Date mockDate = TimeUtil.parseDate("2021-01-01");
        MockedStatic<TimeUtil> mockedStatic = mockStatic(TimeUtil.class);
        mockedStatic.when(TimeUtil::getUtcNow).thenReturn(mockDate);
        mockedStatic.when(()-> TimeUtil.getIntMonth(mockDate)).thenReturn(1);
        mockedStatic.when(()-> TimeUtil.getYear(mockDate)).thenReturn(2021);
        mockedStatic.when(()-> TimeUtil.getDayInMonth(mockDate)).thenReturn(1);

        // 调用测试方法
        List<String> alias = PeriodUtil.getAlias();
        mockedStatic.close();

        // 断言结果
        assert alias.size() == 16;
    }

    /**
     * 测试没有相互覆盖的周期
     */
    @Test
    public void testUnableOverwrite() {
        // 准备数据
        Date newFromDate = TimeUtil.parseDate("2021-01-01");
        Date newToDate = TimeUtil.parseDate("2021-03-01");
        Date overlapFromDate = TimeUtil.parseDate("2021-02-01");
        Date overlapToDate = TimeUtil.parseDate("2021-04-01");
        Date localNow = TimeUtil.parseDate("2021-01-20");

        // 调用测试方法
        boolean unableOverwrite = PeriodUtil.unableOverwrite(newFromDate, newToDate, overlapFromDate, overlapToDate, localNow);

        // 断言结果
        assert !unableOverwrite;
    }
}

// package com.learninggenie.common.utils;
//
// import org.junit.Test;
// import org.junit.runner.RunWith;
// import org.mockito.junit.MockitoJUnitRunner;
//
// import java.io.ByteArrayInputStream;
// import java.io.IOException;
// import java.io.InputStreamReader;
// import java.nio.charset.StandardCharsets;
// import java.util.List;
//
// /**
//  * FileUtil 测试类
//  */
// @RunWith(MockitoJUnitRunner.class)
// public class FileUtilTest {
//
//     /**
//      * parseCsvFile 方法测试
//      */
//     @Test
//     public void parseCsvFileTest() throws IOException {
//         // 参数准备
//         // 含有 BOM 头的 csv 文件内容
//         String csvContent = "﻿id,name,age\n1,张三,20\n2,李四,21";
//         try (InputStreamReader inputReader = new InputStreamReader(new ByteArrayInputStream(csvContent.getBytes(StandardCharsets.UTF_8)))) {
//             // 方法调用
//             List<String[]> csvStringList = FileUtil.parseCsvFile(inputReader);
//             // 结果验证
//             assert csvStringList.size() == 3; // 验证行数
//             // 验证第一行内容
//             assert csvStringList.get(0).length == 3;
//             assert csvStringList.get(0)[0].equals("id");
//             assert csvStringList.get(0)[1].equals("name");
//             assert csvStringList.get(0)[2].equals("age");
//             // 验证第二行内容
//             assert csvStringList.get(1).length == 3;
//             assert csvStringList.get(1)[0].equals("1");
//             assert csvStringList.get(1)[1].equals("张三");
//             assert csvStringList.get(1)[2].equals("20");
//             // 验证第三行内容
//             assert csvStringList.get(2).length == 3;
//             assert csvStringList.get(2)[0].equals("2");
//             assert csvStringList.get(2)[1].equals("李四");
//             assert csvStringList.get(2)[2].equals("21");
//         }
//     }
// }

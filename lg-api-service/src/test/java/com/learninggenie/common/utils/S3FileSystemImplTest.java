package com.learninggenie.common.utils;

import com.learninggenie.common.data.dao.MediaDao;
import com.learninggenie.common.data.entity.MediaEntity;
import com.learninggenie.common.data.model.CreateMediaWithThumbnailRequest;
import com.learninggenie.common.filesystem.S3FileSystemImpl;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.env.Environment;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

/**
 * S3 文件上传测试类
 */
@ExtendWith(MockitoExtension.class)
class S3FileSystemImplTest {
    @InjectMocks
    private S3FileSystemImpl fileSystem;

    @Mock
    private MediaDao mediaDao;

    @Mock
    private Environment env;

    /**
     * 测试获取公共访问地址
     * case: 传人文件路径，获取公共访问地址
     */
    @Test
    void testGetPublicUrl() {
        // 设置初始值
        ReflectionTestUtils.setField(fileSystem, "s3Server", "https://s3.amazonaws.com");
        ReflectionTestUtils.setField(fileSystem, "s3Bucket", "com.learning-genie.prod.us");

        // 调用测试方法
        String publicUrl = fileSystem.getPublicUrl("test.txt");

        // 结果验证
        assertEquals("https://s3.amazonaws.com/com.learning-genie.prod.us/test.txt", publicUrl);
    }

    /**
     * 测试获取公共访问地址
     * case: 传入 S3 桶名与文件路径，获取公共访问地址
     */
    @Test
    void testGetPublicUrl2() {
        // 设置初始值
        ReflectionTestUtils.setField(fileSystem, "s3Server", "https://s3.amazonaws.com");

        // 调用测试方法
        String publicUrl = fileSystem.getPublicUrl("com.learning-genie.prod.us", "test.txt");

        // 结果验证
        assertEquals("https://s3.amazonaws.com/com.learning-genie.prod.us/test.txt", publicUrl);
    }


    @Test
    void createMediaWithThumbnail() throws IOException {
        // 创建测试数据
        String expectedBucket = "testBucket";
        when(env.getProperty("s3.bucket")).thenReturn(expectedBucket);

        // 创建请求参数
        CreateMediaWithThumbnailRequest request = new CreateMediaWithThumbnailRequest();
        request.setType("jpg");
        request.setFileName("test.jpg");
        request.setKey("testKey");
        request.setHeight(100);
        request.setWidth(100);
        request.setSize(1000L);

        // 初始化参数
        ReflectionTestUtils.setField(fileSystem, "defaultVideoCoverRelativePath", "default/path");

        // 调用测试方法
        MediaEntity result = fileSystem.createMediaWithThumbnail(request);

        // 结果验证
        assertEquals("testKey", result.getRelativePath()); // 验证相对路径是不是 testKey
        assertEquals("jpg", result.getFileType()); // 验证文件类型是不是 jpg
        assertEquals("test.jpg", result.getFileName()); // 验证文件名是不是 test.jpg
        assertEquals(100, result.getHeight()); // 验证高度是不是 100
        assertEquals(100, result.getWidth()); // 验证宽度是不是 100
        assertEquals(1000L, result.getSize()); // 验证大小是不是 1000
    }
}
package com.learninggenie.common.utils;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Objects;

/**
 *  字符串工具类测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class StringUtilTest {

    /**
     * 测试传参为 null 的情况
     */
    @Test
    public void formatContentNullTest() {
        // 方法调用
        String result = StringUtil.formatContentNull(null);

        // 结果验证
        assert result == null;
    }

    /**
     * 测试传参内容为 null 的情况
     */
    @Test
    public void formatContentNullTest1() {
        // 数据准备
        String content = "null";

        // 方法调用
        String result = StringUtil.formatContentNull(content);

        // 结果验证
        assert Objects.equals(result, "");
    }

    /**
     * 测试传参内容不为 null 的情况
     */
    @Test
    public void formatContentNullTest2() {
        // 数据准备
        String content = "test";
        // 方法调用
        String result = StringUtil.formatContentNull(content);

        // 结果验证
        assert Objects.equals(result, "test");
    }

    @Test
    public void testCamelCase() {
        // 方法调用
        String result = StringUtil.camelCase("hello_world", "_");

        Assert.assertEquals("HelloWorld", result);
    }

    /**
     * 测试 encodeUrl 方法
     */
    @Test
    public void testEncodeUrl() {
        String result = StringUtil.encodeUrl("https://www.wiki.com/hello world");
        Assert.assertEquals("https://www.wiki.com/hello%20world", result);
    }

    /**
     * 测试字符串拼接方法
     */
    @Test
    public void testJoinString() {
        // 方法调用
        String result = StringUtil.joinString("hello", "world");

        // 结果验证
        Assert.assertEquals("helloworld", result);
    }

    /**
     * 测试移除字符串中的 a 标签和内容方法
     */
    @Test
    public void testRemoveHtmlATagAndContent() {
        // 方法调用
        String result = StringUtil.removeHtmlATagAndContent("<a>hello</a>world");

        // 结果验证
        Assert.assertEquals("world", result);
    }
}

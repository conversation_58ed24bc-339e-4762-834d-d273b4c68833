package com.learninggenie.common.utils;

import com.learninggenie.common.data.model.DateRangeModel;
import com.learninggenie.common.data.model.IntervalModel;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import static org.junit.jupiter.api.Assertions.*;

/**
 * TimeUtil 测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class TimeUtilTest {

    /**
     * getUTCTimeStr 测试方法
     */
    @Test
    public void getUTCTimeStrTest() {
        // 方法调用
        String utcTimeStr = TimeUtil.getUTCTimeStr();

        // 结果验证 验证字符串格式 yyyy-MM-dd HH:mm:ss
        assert utcTimeStr.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}");
    }

    /**
     * getNowUtcStr 测试方法
     */
    @Test
    public void getNowUtcStrTest() {
        // 方法调用
        String utcTimeStr = TimeUtil.getNowUtcStr("yyyy-MM-dd HH:mm:ss");

        // 结果验证 验证字符串格式 yyyy-MM-dd HH:mm:ss
        assert utcTimeStr.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}");
    }

    /**
     * 测试获取某个月的第一天 月从 0 开始
     * case: 2024 年 1 月
     * 验证结果: 2024-02-01
     */
    @Test
    public void testAcquireFirstDayOfMonth() {
        // 准备数据
        int year = 2024;
        int month = 1;

        // 方法调用
        String firstDayOfMonth = TimeUtil.acquireFirstDayOfMonth(year, month);

        // 结果验证
        assert "2024-02-01".equals(firstDayOfMonth);
    }

    /**
     * 测试获取本月的最后一天 月从 0 开始
     * case: 2024 年 0 月
     * 验证结果: 2024-01-31
     */
    @Test
    public void testAcquireLastDayOfMonth() {
        // 准备数据
        int year = 2024;
        int month = 0;

        // 方法调用
        String firstDayOfMonth = TimeUtil.acquireLastDayOfMonth(year, month);

        // 结果验证
        assert "2024-01-31".equals(firstDayOfMonth);
    }

    /**
     * 测试获取某个时间月份的英文缩写
     * case: 2024-01-31
     * 验证结果: Jan
     */
    @Test
    public void testGetMon() {
        // 准备数据
        String month = "2024-01-31";

        // 方法调用
        String mon = TimeUtil.getMon(month);

        // 结果验证
        assert "Jan".equals(mon);
    }

    /**
     * 测试获取某个月的英文月份缩写, 月从 0 开始
     * case: 1
     * 验证结果: Feb
     */
    @Test
    public void testGetMonthNameUs() {
        // 准备数据
        int month = 1;

        // 方法调用
        String monthNameUs = TimeUtil.getMonthNameUs(month);

        // 结果验证
        assert "Feb".equals(monthNameUs);
    }

    /**
     * 测试获取某个月的英文月份缩写, 月从 0 开始
     * case: 2023-01-01
     * 验证结果: Jan
     */
    @Test
    public void testGetShortMonthNameUs() {
        // 准备数据
        Date date = TimeUtil.parse("2023-01-01", TimeUtil.format10);

        // 方法调用
        String monthNameUs = TimeUtil.getShortMonthNameUs(date);
        // 结果验证
        assert "Jan".equals(monthNameUs);
    }

    /**
     * 测试日期拼接, 月从 0 开始
     * case: 2024-01
     * 验证结果: Jan 2024
     */
    @Test
    public void testGetYearAndMonth() {
        // 准备数据
        int year = 2024;
        int month = 0;

        // 方法调用
        String yearAndMonth = TimeUtil.getYearAndMonth(year, month);

        // 结果验证
        assert "Jan 2024".equals(yearAndMonth);
    }

    /**
     * 测试将字符串按照指定格式转换为日期
     * case: 2024-01-31 String
     * 验证结果: 2024-01-31 Date
     */
    @Test
    public void testTryParse() {
        // 准备数据
        String dateStr = "2024-01-31";

        // 方法调用
        Date date = TimeUtil.tryParse(dateStr, TimeUtil.format10);

        // 结果验证
        assertEquals(dateStr, TimeUtil.format(date, TimeUtil.format10));

    }

    /**
     * 测试将字符串按照指定格式转换为日期
     * case: 2024-01-31 String
     * 验证结果: 2024-01-31 Date
     */
    @Test
    public void testParse() {
        // 准备数据
        String dateStr = "2024-01-31";

        // 方法调用
        Date date = TimeUtil.parse(dateStr, TimeUtil.format10, Locale.ENGLISH);

        // 结果验证
        assertEquals(dateStr, TimeUtil.format(date, TimeUtil.format10));
    }

    /**
     * 测试将字符串按照指定格式转换为日期, 如果字符串为空则返回 null
     * case: 2024-01-31 String
     * 验证结果: 2024-01-31 Date
     */
    @Test
    public void testParseIgnoreEmpty() {
        // 准备数据
        String dateStr = "2024-01-31";

        // 方法调用
        Date date = TimeUtil.parseIgnoreEmpty(dateStr, TimeUtil.format10);

        // 结果验证
        assertEquals(dateStr, TimeUtil.format(date, TimeUtil.format10));
    }

    /**
     * 测试时区与 UTC 的时区差
     * case: America/Los_Angeles
     * 验证结果: -8
     */
    @Test
    public void testGetOffsetNumByTimeZone() {
        // 准备数据
        String timeZone = "America/Los_Angeles";

        // 方法调用
        int offsetNumByTimeZone = TimeUtil.getOffsetNumByTimeZone(timeZone);

        // 结果验证
        assertEquals(-8, offsetNumByTimeZone);
    }

    /**
     * 测试时区与 UTC 的时区差
     * case: America/Los_Angeles
     * 验证结果: -8 字符串
     */
    @Test
    public void testGetOffsetStrByTimeZone() {
        // 准备数据
        String timeZone = "America/Los_Angeles";

        // 方法调用
        String offsetNumByTimeZone = TimeUtil.getOffsetStrByTimeZone(timeZone);

        // 结果验证
        assertEquals("-8", offsetNumByTimeZone);
    }

    /**
     * 测试获取某个时间的月份
     * case: 2023-01-01
     * 验证结果: 1
     */
    @Test
    public void testGetDatePeriod() {
        // 准备数据
        Date date = TimeUtil.parse("2023-01-01", TimeUtil.format10);

        // 方法调用
        int datePeriod = TimeUtil.getDatePeriod(date);

        // 结果验证
        assertEquals(1, datePeriod);
    }

    /**
     * 测试获取某个时间的小时
     * case: 2023-01-01 05:20:00
     * 验证结果: 5
     */
    @Test
    public void testGetHourOfDay() {
        // 准备数据
        Date date = TimeUtil.parse("2023-01-01 05:20:00", TimeUtil.dateFormat);

        // 方法调用
        int hourOfDay = TimeUtil.getHourOfDay(date);

        // 结果验证
        assertEquals(5, hourOfDay);
    }

    /**
     * 测试获取某个时间的年限
     * case: 2024-01-31
     * 验证结果: 2024
     */
    @Test
    public void testGetYear() {
        // 准备数据
        String date = "2024-01-31";

        // 方法调用
        int year = TimeUtil.getYear(date);

        // 结果验证
        assertEquals(2024, year);
    }

    /**
     * 测试给指定时间增加指定年份
     * case: 2023-01-01 05:20:00
     * 验证结果: 2024-01-01
     */
    @Test
    public void testAddYears() {
        // 准备数据
        Date date = TimeUtil.parse("2023-01-01 05:20:00", TimeUtil.dateFormat);
        // 增加年份
        int years = 1;

        // 方法调用
        Date addYears = TimeUtil.addYears(date, years);

        // 结果验证
        assertEquals("2024-01-01", TimeUtil.format(addYears, TimeUtil.format10));
    }

    /**
     * 测试时间按照指定格式转换为字符串
     * case: 2023-01-01 05:20:00
     * 验证结果: 2023-01-01 05:20:00
     */
    @Test
    public void testFormatLocale() {
        // 准备数据
        Date date = TimeUtil.parse("2023-01-01 05:20:00", TimeUtil.dateFormat);

        // 方法调用
        String formatLocale = TimeUtil.formatLocale(date, TimeUtil.format10);

        // 结果验证
        assertEquals("2023-01-01", formatLocale);
    }

    /**
     * 测试时间从指定格式转换为另一种格式
     * case: 2024-01-01
     * 验证结果: 2024/01/01
     */
    @Test
    public void testConvertFormatWithLocale() {
        // 准备数据
        String dateStr = "2024-01-01";

        // 方法调用
        String convertFormatWithLocale = TimeUtil.convertFormatWithLocale(dateStr, TimeUtil.format10, TimeUtil.format5);

        // 结果验证
        assertEquals("2024/01/01", convertFormatWithLocale);
    }

    /**
     * 测试获取一天的开始时间
     * case: 2024-01-01 05:20:00
     * 验证结果: 2024-01-01 00:00:00
     */
    @Test
    public void testGetStartOfDay() {
        // 准备数据
        SimpleDateFormat format2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateStr = "2024-01-01 05:20:00";

        // 方法调用
        try {
            String startOfDay = TimeUtil.getStartOfDay(dateStr, format2);

            // 结果验证
            assertEquals("2024-01-01 00:00:00", startOfDay);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 测试获取一天的结束时间
     * case: 2024-02-01 05:20:00
     * 验证结果: 2024-02-01 23:59:59
     */
    @Test
    public void testGetEndOfDay() {
        // 准备数据
        SimpleDateFormat format2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateStr = "2024-02-01 05:20:00";

        // 方法调用
        try {
            String endOfDay = TimeUtil.getEndOfDay(dateStr, format2);

            // 结果验证
            assertEquals("2024-02-01 23:59:59", endOfDay);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 测试判断两个时间的日期部分是否相等
     * case: 2023-01-01 05:20:00 2023-01-01 06:20:00
     * 验证结果: true
     */
    @Test
    public void testEqualsDate() {
        // 准备数据
        Date date1 = TimeUtil.parse("2023-01-01 05:20:00", TimeUtil.dateFormat);
        Date date2 = TimeUtil.parse("2023-01-01 06:20:00", TimeUtil.dateFormat);

        // 方法调用
        boolean equalsDate = TimeUtil.equalsDate(date1, date2);

        // 结果验证
        assertTrue(equalsDate);
    }

    /**
     * 测试计算学生年龄
     * case: 学生出生日期: 2019-01-01 完成日期: 2022-01-01
     * 验证结果: 36
     */
    @Test
    public void testGetAgeMonth() {
        // 准备数据
        String completeDate = "2022-01-01";
        String birthDate = "2019-01-01";
        long toDate = 1672521600000L;

        // 方法调用
        int ageMonth = TimeUtil.getAgeMonth(completeDate, birthDate, toDate);

        // 结果验证
        assertEquals(36, ageMonth);
    }

    /**
     * 测试根据月份计算年龄
     * case: 37
     * 验证结果: 3 years 1 month
     */
    @Test
    public void testGetAgeByAgeMonth() {
        // 准备数据
        int ageMonth = 37;

        // 方法调用
        String age = TimeUtil.getAgeByAgeMonth(ageMonth, Locale.ENGLISH.toString());

        // 结果验证
        assertEquals("3 years 1 month", age);
    }

    /**
     * 测试获取两个时间的天数差
     * case: 2023-01-01 05:20:00 2023-01-02 06:20:00
     * 验证结果: 1
     */
    @Test
    public void testGetDateDays() {
        // 准备数据
        Date from = TimeUtil.parse("2023-01-01 05:20:00", TimeUtil.dateFormat);
        Date to = TimeUtil.parse("2023-01-02 06:20:00", TimeUtil.dateFormat);

        // 方法调用
        int dateDays = TimeUtil.getDateDays(from, to);

        // 结果验证
        assertEquals(1, dateDays);
    }

    /**
     * 测试获取指定时间八个月之后的时间
     * case: 2023-01-01
     * 验证结果: 2023-09-01
     */
    @Test
    public void testGetCutoffDate() {
        // 准备数据
        Date date = TimeUtil.parse("2023-01-01", TimeUtil.format10);

        // 方法调用
        Date cutoffDate = TimeUtil.getCutoffDate(date);

        // 结果验证
        assertEquals("2023-09-01", TimeUtil.format(cutoffDate, TimeUtil.format10));
    }

    /**
     * 测试将本地时间按照指定时区转换为 UTC 时间
     * case: 2023-01-01 05:20:00
     * 验证结果: 2023-01-01 13:20:00
     */
    @Test
    public void testConvertLocalToUtc() {
        // 准备数据
        Date date = TimeUtil.parse("2023-01-01 05:20:00", TimeUtil.dateFormat);
        String timeZone = "America/Los_Angeles";

        // 方法调用
        Date utcDate = TimeUtil.convertLocalToUtc(date, timeZone);

        // 结果验证
        assertEquals("2023-01-01 13:20:00", TimeUtil.format(utcDate, TimeUtil.dateFormat));
    }

    /**
     * 测试获取指定时区与 UTC 的时区差 单位为毫秒
     * case: America/Los_Angeles
     * 验证结果: -28800000
     */
    @Test
    public void testGetTimeZoneRawOffset() {
        // 准备数据
        String timeZone = "America/Los_Angeles";

        // 方法调用
        int timeZoneRawOffset = TimeUtil.getTimeZoneRawOffset(timeZone);

        // 结果验证
        assertEquals(-28800000, timeZoneRawOffset);
    }

    /**
     * 测试获取指定时间的月份
     * case: 2023-01-01 05:20:00
     * 验证结果: JANUARY
     */
    @Test
    public void testGetMonth() {
        // 准备数据
        Date date = TimeUtil.parse("2023-01-01 05:20:00", TimeUtil.dateFormat);

        // 方法调用
        String month = TimeUtil.getMonth(date);

        // 结果验证
        assertEquals("JANUARY", month);
    }

    /**
     * 测试根据秒数获取时分秒
     * case: 3661
     * 验证结果: 01:01:01
     */
    @Test
    public void testParseDuration() {
        // 准备数据
        long second = 3661L;

        // 方法调用
        String duration = TimeUtil.parseDuration(second);

        // 结果验证
        assertEquals("01:01:01", duration);
    }

    /**
     * 测试将 Google 时长转换为秒数
     * case: PT1H1M1S
     * 验证结果: 3661
     */
    @Test
    public void testParseGoogleDuration() {
        // 准备数据
        String duration = "PT1H1M1S";

        // 方法调用
        long googleDuration = TimeUtil.parseGoogleDuration(duration);

        // 结果验证
        assertEquals(3661L, googleDuration);
    }

    /**
     * 测试某个时间是否在指定时间之前
     * case: 2024-01-01 05:20:00 2025-01-01 05:20:00
     * 验证结果: true
     */
    @Test
    public void testEqualsOrBefore() {
        // 准备数据
        String strDate = "2024-01-01 05:20:00";
        String strTargetDate = "2025-1-01 05:20:00";

        // 方法调用
        boolean equalsOrBefore = TimeUtil.equalsOrBefore(strDate, strTargetDate);

        // 结果验证
        assertTrue(equalsOrBefore);
    }

    /**
     * 测试将时间戳转换为日期
     * case: 1672521600000
     * 验证结果: 2023-01-01 21:20:00
     */
    @Test
    public void testTimeStamp2Date() {
        // 准备数据
        long timeStamp = 1672579200000L;

        // 方法调用
        String date = TimeUtil.timeStamp2Date(timeStamp, null);

        // 因为使用的是时间戳，本地时间和服务器时间存在差异，所以只能验证日期部分
        // 结果验证
        assertEquals("2023-01-01", date.substring(0, 10));
    }

    /**
     * 测试获取一段时间中最后一个工作日
     * case: 2023-01-01 2023-01-31
     * 验证结果: 2023-01-31
     */
    @Test
    public void testGetLastWorkingDay() {
        // 准备数据
        Date from = TimeUtil.parse("2023-01-01", TimeUtil.format10);
        Date to = TimeUtil.parse("2023-01-31", TimeUtil.format10);

        // 方法调用
        Date lastWorkingDay = TimeUtil.getLastWorkingDay(from, to);

        // 结果验证
        assertEquals("2023-01-31", TimeUtil.format(lastWorkingDay, TimeUtil.format10));
    }

    /**
     * 测试获取一段时间中第一个工作日
     * case: 2023-01-01 2023-01-31
     * 验证结果: 2023-01-02
     */
    @Test
    public void testGetFirstWorkingDay() {
        // 准备数据
        Date from = TimeUtil.parse("2023-01-01", TimeUtil.format10);
        Date to = TimeUtil.parse("2023-01-31", TimeUtil.format10);

        // 方法调用
        Date firstWorkingDay = TimeUtil.getFirstWorkingDay(from, to);

        // 结果验证
        assertEquals("2023-01-02", TimeUtil.format(firstWorkingDay, TimeUtil.format10));
    }

    /**
     * 测试获取日期月份的缩写
     * case: 01
     * 验证结果: Jan.
     */
    @Test
    public void testGetMonthStrAbbr() {
        // 准备数据
        String dateStr = "01";

        // 方法调用
        String monthStrAbbr = TimeUtil.getMonthStrAbbr(dateStr);

        // 结果验证
        assertEquals("Jan.", monthStrAbbr);
    }

    /**
     * 测试获取两个日期之间的所有日期拆分成 interval 个月
     */
    @Test
    public void testGetBetweenDates() {
        // 准备数据
        Date start = TimeUtil.parse("2024-01-01", TimeUtil.format10);
        Date end = TimeUtil.parse("2024-03-15", TimeUtil.format10);

        // 方法调用
        List<Date> betweenDates = TimeUtil.getBetweenDates(start, end, 1);

        // 结果验证
        assertEquals(4, betweenDates.size());
    }

    /**
     *  测试获取两个日期之间的所有日期拆分成 约定 季度
     */
    @Test
    public void testGetQuarterBetweenDates() {
        // 准备数据
        Date start = TimeUtil.parse("2019-05-05", TimeUtil.format10);
        Date end = TimeUtil.parse("2020-03-15", TimeUtil.format10);

        // 方法调用
        List<Date> betweenDates = TimeUtil.getQuarterBetweenDates(start, end);

        // 结果验证
        assertEquals(5, betweenDates.size());
    }

    /**
     *  测试获取两个日期之间的所有日期拆分成半年
     */
    @Test
    public void testGetHalfYearBetweenDates() {
        // 准备数据
        Date start = TimeUtil.parse("2019-05-05", TimeUtil.format10);
        Date end = TimeUtil.parse("2020-03-15", TimeUtil.format10);

        // 方法调用
        List<Date> betweenDates = TimeUtil.getHalfYearBetweenDates(start, end);

        // 结果验证
        assertEquals(4, betweenDates.size());
    }

    /**
     * 测试获取两个日期之间的每一天
     */
    @Test
    public void testGetMonthAllDay() {
        // 准备数据
        Date start = TimeUtil.parse("2024-01-01", TimeUtil.format10);
        Date end = TimeUtil.parse("2024-01-15", TimeUtil.format10);

        // 方法调用
        List<Date> betweenDates = TimeUtil.getMonthAllDay(start, end);

        // 结果验证
        assertEquals(13, betweenDates.size());
    }

    /**
     * 测试将日期按照指定格式转换为字符串
     */
    @Test
    public void testRegularMatchStringtoStrDate() {
        // 准备数据
        String date = "01/01/2024";

        // 方法调用
        String strDate = TimeUtil.regularMatchStringtoStrDate(date);

        // 结果验证
        assertEquals("2024-01-01", strDate);
    }

    /**
     * 测试将日期按照指定格式转换为字符串
     */
    @Test
    public void testRegularMatchStringtoStrDate2() {
        // 准备数据
        String date = "2024/01/01";

        // 方法调用
        String strDate = TimeUtil.regularMatchStringtoStrDate(date);

        // 结果验证
        assertEquals("2024-01-01", strDate);
    }

    /**
     * 测试两个日期之间的分钟
     */
    @Test
    public void testGetMinutesOfTwoDate() {
        // 准备数据
        Date start = TimeUtil.parse("2024-01-01 05:20:00", TimeUtil.dateFormat);
        Date end = TimeUtil.parse("2024-01-01 06:20:00", TimeUtil.dateFormat);

        // 方法调用
        int minutesOfTwoDate = TimeUtil.getMinutesOfTwoDate(end, start);

        // 结果验证
        assertEquals(60, minutesOfTwoDate);
    }

    /**
     * 测试某年共有几周
     */
    @Test
    public void testGetWeeksInYear() {
        // 准备数据
        int year = 2024;

        // 方法调用
        int weeksInYear = TimeUtil.getWeeksInYear(year, true);

        // 结果验证
        assertEquals(53, weeksInYear);
    }

    /**
     * 测试获取两个日期的天数间隔
     */
    @Test
    public void testGetDaysBetweenTimeV2() {
        // 准备数据
        Date start = TimeUtil.parse("2023-01-01 05:20:00", TimeUtil.dateFormat);
        Date end = TimeUtil.parse("2024-01-02 06:20:00", TimeUtil.dateFormat);

        // 方法调用
        long daysBetweenTime = TimeUtil.getDaysBetweenTimeV2(start, end);

        // 结果验证
        assertEquals(366, daysBetweenTime);
    }

    /**
     * 测试获取两个日期的周数间隔
     */
    @Test
    public void testGetWeeksBetweenTime() {
        // 准备数据
        Date start = TimeUtil.parse("2024-01-01 05:20:00", TimeUtil.dateFormat);
        Date end = TimeUtil.parse("2024-01-18 06:20:00", TimeUtil.dateFormat);

        // 方法调用
        long weeksBetweenTime = TimeUtil.getWeeksBetweenTime(start, end);

        // 结果验证
        assertEquals(2, weeksBetweenTime);
    }

    /**
     * 测试获取两个日期的周数间隔(跨年)
     */
    @Test
    public void testGetWeeksBetweenTime2() {
        // 准备数据
        Date start = TimeUtil.parse("2023-01-01 05:20:00", TimeUtil.dateFormat);
        Date end = TimeUtil.parse("2024-01-02 06:20:00", TimeUtil.dateFormat);

        // 方法调用
        long weeksBetweenTime = TimeUtil.getWeeksBetweenTime(start, end);

        // 结果验证
        assertEquals(52, weeksBetweenTime);
    }

    /**
     * 测试获取时间是所在年的第几周
     */
    @Test
    public void testGetWeekOfYear() {
        // 准备数据
        Date date = TimeUtil.parse("2024-01-01 05:20:00", TimeUtil.dateFormat);

        // 方法调用
        int weekOfYear = TimeUtil.getWeekOfYear(date);

        // 结果验证
        assertEquals(1, weekOfYear);
    }

    /**
     * 测试获取两个日期相差的月数
     */
    @Test
    public void testGetMonthsBetweenTime() {
        // 准备数据
        Date start = TimeUtil.parse("2023-01-01 05:20:00", TimeUtil.dateFormat);
        Date end = TimeUtil.parse("2023-06-02 06:20:00", TimeUtil.dateFormat);

        // 方法调用
        int betweenTime = TimeUtil.getMonthsBetweenTime(end, start);

        // 结果验证
        assertEquals(-5, betweenTime);
    }

    @Test
    public void testGetIntervalByEndDate() {
        // 准备数据
        Date start = TimeUtil.parse("2023-01-01 05:20:00", TimeUtil.dateFormat);

        // 方法调用
        IntervalModel intervalModel = TimeUtil.getIntervalByEndDate(start, 2);

        // 结果验证
        assertEquals("2022-12-30 05:20:00.000", intervalModel.getFromStr());
    }


    /**
     * 测试获得当前天的最开始时间
     */
    @Test
    public void testGetDayStart() {
        // 准备数据
        Date date = TimeUtil.parse("2023-01-01 05:20:00", TimeUtil.dateFormat);

        // 方法调用
        Date dayStart = TimeUtil.getDayStart(date);

        // 结果验证
        assertEquals("2023-01-01 00:00:00", TimeUtil.format(dayStart, TimeUtil.dateFormat));
    }

    /**
     * 测试获得当前天的最后时间
     */
    @Test
    public void testGetDayEnd() {
        // 准备数据
        Date date = TimeUtil.parse("2023-01-01 05:20:00", TimeUtil.dateFormat);

        // 方法调用
        Date dayEnd = TimeUtil.getDayEnd(date);

        // 结果验证
        assertEquals("2023-01-01 23:59:59", TimeUtil.format(dayEnd, TimeUtil.dateFormat));
    }

    /**
     * 测试获取两个时间之间所有年份
     */
    @Test
    public void testGetYearsBetween() {
        // 准备数据
        Date start = TimeUtil.parse("2023-01-01 05:20:00", TimeUtil.dateFormat);
        Date end = TimeUtil.parse("2024-01-02 06:20:00", TimeUtil.dateFormat);

        // 方法调用
        List<Integer> yearsBetween = TimeUtil.getYearsBetween(start, end);

        // 结果验证
        assertEquals(2, yearsBetween.size());
    }

    /**
     * 测试获取当前时间之前一周或多周的开始时间和结束时间
     */
    @Test
    public void testGetWeekStartAndEnd() {
        // 准备数据
        int week = 1;

        // 方法调用
        DateRangeModel weekStartAndEnd = TimeUtil.getWeekStartAndEnd(week, TimeUtil.format10);

        // 结果验证
        assertNotNull(weekStartAndEnd);
    }

}

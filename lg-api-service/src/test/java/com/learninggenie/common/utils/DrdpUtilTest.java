package com.learninggenie.common.utils;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.learninggenie.common.data.entity.StudentAttrEntity;
import com.learninggenie.common.data.model.StudentAttr;
import com.learninggenie.common.data.model.drdp2.DrdpRatingPeriodModel;
import com.learninggenie.common.data.model.drdp2.DrdpReplaceModel;
import com.learninggenie.common.utils.drdp.DRDPApiUtil;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;
import java.util.Map;
import java.util.Set;

@RunWith(MockitoJUnitRunner.class)
public class DrdpUtilTest {

    @Test
    public void testGetErrorIndex() {
        Integer result = DRDPApiUtil.getErrorIndex("row: 2");
        Assert.assertNotNull(result);
        Assert.assertEquals("2", result.toString());
    }

    @Test
    public void testCountyError() {
        boolean countyError = DRDPApiUtil.isCountyError("any string");
        Assert.assertFalse(countyError);
    }

    @Test
    public void testConvertIepAttr_notIep() {
        Map<String, String> ratingData = Maps.newHashMap();
        ratingData.put("iepOrIfsp", "0");
        DRDPApiUtil.convertIepAttr(ratingData, "iepOrIfsp", "0");

        Assert.assertEquals("2", ratingData.get("iepOrIfsp"));
    }

    @Test
    public void testConvertIepAttr_iep() {
        Map<String, String> ratingData = Maps.newHashMap();
        ratingData.put("iepOrIfsp", "1");
        DRDPApiUtil.convertIepAttr(ratingData, "iepOrIfsp", "1");

        Assert.assertEquals("1", ratingData.get("iepOrIfsp"));
    }

    @Test
    public void testGetDrdpSupportStates() {
        List<String> result = DRDPApiUtil.getDrdpSupportStates();

        Assert.assertNotNull(result);
        Assert.assertTrue(result.contains("CA"));
    }

    @Test
    public void testGetRatingSet() {
        Set<String> result = DRDPApiUtil.getRatingSet();
        Assert.assertNotNull(result);
    }

    @Test
    public void testSSidError() {
        boolean result = DRDPApiUtil.isSsidError("any string");
        Assert.assertFalse(result);
    }

    @Test
    public void testAgeGradeTemplateError() {
        boolean result = DRDPApiUtil.isAgeGradeTemplateError("any string");
        Assert.assertFalse(result);
    }

    @Test
    public void testGetItAgeGradeCode() {
        String result = DRDPApiUtil.getAgeGradeCode("ITC");
        Assert.assertEquals("IT", result);
    }

    @Test
    public void testGetPsAgeGradeCode() {
        String result = DRDPApiUtil.getAgeGradeCode("PSC");
        Assert.assertEquals("PS", result);
    }

    @Test
    public void testGetSaAgeGradeCode() {
        String result = DRDPApiUtil.getAgeGradeCode("SAC");
        Assert.assertEquals("SA", result);
    }

    @Test
    public void testGetkAgeGradeCode() {
        String result = DRDPApiUtil.getAgeGradeCode("KC");
        Assert.assertEquals("K", result);
    }

    @Test
    public void testGetAgeGradeCode_Empty_1() {
        String result = DRDPApiUtil.getAgeGradeCode("");
        Assert.assertEquals("", result);
    }

    @Test
    public void testGetAgeGradeCode_Empty_2() {
        String result = DRDPApiUtil.getAgeGradeCode("AnyString");
        Assert.assertEquals("", result);
    }

    @Test
    public void testGetItAgeGradeName () {
        String result = DRDPApiUtil.getAgeGradeName("IT");
        Assert.assertEquals("Infant Toddler", result);
    }

    @Test
    public void testGetPsAgeGradeName () {
        String result = DRDPApiUtil.getAgeGradeName("PS");
        Assert.assertEquals("Preschool", result);
    }

    @Test
    public void testGetSaAgeGradeName () {
        String result = DRDPApiUtil.getAgeGradeName("SA");
        Assert.assertEquals("School Age", result);
    }

    @Test
    public void testGetKAgeGradeName() {
        String result = DRDPApiUtil.getAgeGradeName("K");
        Assert.assertEquals("Kindergarten", result);
    }

    @Test
    public void testGetAgeGradeName_Empty_1() {
        String result = DRDPApiUtil.getAgeGradeName("");
        Assert.assertEquals("", result);
    }

    @Test
    public void testGetAgeGradeName_Empty_2() {
        String result = DRDPApiUtil.getAgeGradeName("AnyString");
        Assert.assertEquals("", result);
    }

    @Test
    public void testMatchSsid() {
        boolean result = DRDPApiUtil.isMatchSsid("any string");
        Assert.assertFalse(result);
    }

    @Test
    public void testDrdp2SupportProgramName() {
        boolean result = DRDPApiUtil.isDrdp2SupportProgramName("any string", "IT");
        Assert.assertFalse(result);
    }

    @Test
    public void testGetDrdp2Race() {
        String result = DRDPApiUtil.getDrdp2Race("Chinese");
        Assert.assertEquals("Chinese", result);
    }

    @Test
    public void testGetDrdp2LanguageCode() {
        String result = DRDPApiUtil.getDrdp2LanguageCode("English");
        Assert.assertEquals("100", result);
    }

    @Test
    public void testGetDrdp2programName() {
        String result = DRDPApiUtil.getDrdp2ProgramName("Bilingual Program");
        Assert.assertEquals("bilingual", result);
    }

    @Test
    public void testReplaceSpecialCharacter() {
        DrdpReplaceModel result = DRDPApiUtil.replaceSpecialCharacters("A#B");
        Assert.assertNotNull(result);
        Assert.assertEquals("A#B", result.getOriginal());
        Assert.assertEquals("_", result.getReplaceCharacter());
        Assert.assertEquals(1, result.getSpecialCharacters().size());
        Assert.assertEquals("A_B", result.getReplaced());
    }

    @Test
    public void testConvertDrdp2ChildAttr() {
        List<StudentAttrEntity> attrs = Lists.newArrayList();
        StudentAttrEntity languageAttr = new StudentAttrEntity();
        languageAttr.setAttrName("Language");
        languageAttr.setAttrValue("English");

        attrs.add(languageAttr);

        StudentAttrEntity enrolSubsidizedAttr = new StudentAttrEntity();
        enrolSubsidizedAttr.setAttrName("Enrol_subsidized");
        enrolSubsidizedAttr.setAttrValue("yes");

        attrs.add(enrolSubsidizedAttr);

        StudentAttrEntity programNameAttr = new StudentAttrEntity();
        programNameAttr.setAttrName("Program Name");
        programNameAttr.setAttrValue("Bilingual Program");

        attrs.add(programNameAttr);

        StudentAttrEntity iepAttr = new StudentAttrEntity();
        iepAttr.setAttrName("IEP/IFSP");
        iepAttr.setAttrValue("yes");

        attrs.add(iepAttr);

        StudentAttrEntity eldAttr = new StudentAttrEntity();
        eldAttr.setAttrName("ELD");
        eldAttr.setAttrValue("yes");

        attrs.add(eldAttr);

        StudentAttrEntity raceAttr = new StudentAttrEntity();
        raceAttr.setAttrName("Race");
        raceAttr.setAttrValue("Chinese");

        attrs.add(raceAttr);

        StudentAttrEntity ssidAttr = new StudentAttrEntity();
        ssidAttr.setAttrName("Statewide Student Identifier");
        ssidAttr.setAttrValue("1234567890");

        attrs.add(ssidAttr);

        Map<String, String> ratingMap = Maps.newHashMap();
        DRDPApiUtil.convertDrdp2ChildAttr(attrs, ratingMap, "ITC", false);

        Assert.assertEquals("100", ratingMap.get("homeLanguage"));
        Assert.assertEquals("1", ratingMap.get("subsidizedTuition"));
        Assert.assertEquals("bilingual", ratingMap.get("programName"));
        Assert.assertEquals("1", ratingMap.get("iepOrIfsp"));
        Assert.assertEquals("1", ratingMap.get("otherThanEnglish"));
        Assert.assertEquals("Chinese", ratingMap.get("race"));
        Assert.assertEquals("1234567890", ratingMap.get("ssid"));

        DRDPApiUtil.convertDrdp2ChildAttr(attrs, ratingMap, "ITC", true);

        Assert.assertEquals("", ratingMap.get("ssid"));
    }

    @Test
    public void testGetNeedFixedErrorTypes() {
        List<String> needFixedErrorTypes = DRDPApiUtil.getNeedFixedErrorTypes();
        Assert.assertNotNull(needFixedErrorTypes);
    }

    @Test
    public void testConvertGender_returnEmpty() {
        String result = DRDPApiUtil.convertGender("");
        Assert.assertEquals("", result);
    }

    @Test
    public void testConvertGender_returnF() {
        String result = DRDPApiUtil.convertGender("FEMALE");
        Assert.assertEquals("F", result);
    }

    @Test
    public void testConvertGender_returnM() {
        String result = DRDPApiUtil.convertGender("Male");
        Assert.assertEquals("M", result);
    }

    @Test
    public void testConvertGender_returnN() {
        String result = DRDPApiUtil.convertGender("NONBINARY");
        Assert.assertEquals("N", result);
    }

    @Test
    public void testCompleteDateIsFuture() {
        boolean result = DRDPApiUtil.isCompleteFutureError("any string");
        Assert.assertFalse(result);
    }

    @Test
    public void testGetDrdpAliasSchoolYear_returnEmptyString() {
        String result = DRDPApiUtil.getSchoolYearByDrdpAlias("");
        Assert.assertEquals("", result);
    }

    @Test
    public void testGetFallPeriodSchoolYear() {
        String result = DRDPApiUtil.getSchoolYearByDrdpAlias("Fall 2023");
        Assert.assertEquals("2023-2024", result);
    }

    @Test
    public void testGetWinterPeriodSchoolYear() {
        String result = DRDPApiUtil.getSchoolYearByDrdpAlias("Winter 2023-24");
        Assert.assertEquals("2023-2024", result);
    }

    @Test
    public void testGetSpringPeriodSchoolYear() {
        String result = DRDPApiUtil.getSchoolYearByDrdpAlias("Spring 2024");
        Assert.assertEquals("2023-2024", result);
    }

    @Test
    public void testGetSummerPeriodSchoolYear() {
        String result = DRDPApiUtil.getSchoolYearByDrdpAlias("Summer 2024");
        Assert.assertEquals("2023-2024", result);
    }

    @Test
    public void testGetDrdpDefaultPeriod_return_null() {
        DrdpRatingPeriodModel result = DRDPApiUtil.getDefaultRatingPeriod("");
        Assert.assertNull(result);
    }

    @Test
    public void testGetFallPeriodDefaultPeriod() {
        DrdpRatingPeriodModel result = DRDPApiUtil.getDefaultRatingPeriod("Fall 2023");
        Assert.assertNotNull(result);
        Assert.assertEquals("Fall 2023", result.getDrdpAlias());
        Assert.assertEquals("08/01/2023", result.getMinimumDate());
        Assert.assertEquals("12/31/2023", result.getMaximumDate());
    }

    @Test
    public void testGetWinterPeriodDefaultPeriod() {
        DrdpRatingPeriodModel result = DRDPApiUtil.getDefaultRatingPeriod("Winter 2023-24");
        Assert.assertNotNull(result);
        Assert.assertEquals("Winter 2023-24", result.getDrdpAlias());
        Assert.assertEquals("11/01/2023", result.getMinimumDate());
        Assert.assertEquals("03/31/2024", result.getMaximumDate());
    }

    @Test
    public void testGetSpringPeriodDefaultPeriod() {
        DrdpRatingPeriodModel result = DRDPApiUtil.getDefaultRatingPeriod("Spring 2024");
        Assert.assertNotNull(result);
        Assert.assertEquals("Spring 2024", result.getDrdpAlias());
        Assert.assertEquals("01/01/2024", result.getMinimumDate());
        Assert.assertEquals("06/30/2024", result.getMaximumDate());
    }

    @Test
    public void testGetSummerPeriodDefaultPeriod() {
        DrdpRatingPeriodModel result = DRDPApiUtil.getDefaultRatingPeriod("Summer 2024");
        Assert.assertNotNull(result);
        Assert.assertEquals("Summer 2024", result.getDrdpAlias());
        Assert.assertEquals("04/01/2024", result.getMinimumDate());
        Assert.assertEquals("08/31/2024", result.getMaximumDate());
    }
}

package com.learninggenie.common.utils;

import com.learninggenie.api.util.ExcelUtil;
import junit.framework.TestCase;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;

@RunWith(MockitoJUnitRunner.class)
public class ExcelUtilTest extends TestCase {

    @Test
    public void testAddPicture() {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet();

        ExcelUtil.addPicture(sheet, ExcelUtilTest.class.getClassLoader().getResourceAsStream("rating-view-error-tip1.png"), 0, 0, 7, 9);

        Assert.assertEquals(1, workbook.getAllPictures().size());

        try {
            workbook.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}

package com.learninggenie.common.bi.impl;

import com.learninggenie.common.data.dao.AgencyDao;
import com.learninggenie.common.data.dao.CenterDao;
import com.learninggenie.common.data.dao.GroupDao;
import com.learninggenie.common.data.dao.bi.BIDailyActivityDao;
import com.learninggenie.common.data.dao.bi.BIDao;
import com.learninggenie.common.data.dao.bi.BIUserDailyActivityDao;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.entity.AgencyEntity;
import com.learninggenie.common.data.entity.AgencyMetaDataEntity;
import com.learninggenie.common.data.entity.GroupEntity;
import com.learninggenie.common.data.entity.bi.activity.BIDailyActivityEntity;
import com.learninggenie.common.data.entity.bi.activity.BIUserDailyActivityEntity;
import com.learninggenie.common.data.enums.bi.Metric;
import com.learninggenie.common.data.model.AgencyModel;
import com.learninggenie.common.data.model.UserModel;
import com.learninggenie.common.data.model.bi.activity.DailyActivityModel;
import com.learninggenie.common.data.model.bi.activity.GetActivityStatsDataRequest;
import com.learninggenie.common.data.model.bi.activity.GetActivityStatsDataResponse;
import com.learninggenie.common.sharding.ShardingProvider;
import com.learninggenie.common.utils.TimeUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * BIServiceImpl 测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class BIServiceImplTest {

    @InjectMocks
    private BIServiceImpl biService;

    @Mock
    private BIDao biDao;

    @Mock
    private BIDailyActivityDao biDailyActivityDao;

    @Mock
    private BIUserDailyActivityDao biUserDailyActivityDao;

    @Mock
    private CenterDao centerDao;

    @Mock
    private ShardingProvider shardingProvider;

    @Mock
    private UserDaoImpl userDao;

    @Mock
    private AgencyDao agencyDao;

    @Mock
    private GroupDao groupDao;

    /**
     *  测试更新机构日活数据
     */
    @Test
    public void testUpdateAgencyDailyActivityData() {
        // 准备入参
        // 机构 ID
        String agencyId = "7d24b313-1e0d-4358-aa51-62874dd35bb1";
        // 开始日期
        Date beginLocalTime = TimeUtil.parse("2024-01-15", TimeUtil.format10);
        // 结束日期
        Date endLocalTime = TimeUtil.parse("2024-01-28", TimeUtil.format10);
        // 机构时区
        String centerTimezone = "America/Los_Angeles";
        // 模拟获取机构时区
        when(centerDao.getFirstCenterTimeZoneByAgencyId(agencyId)).thenReturn(centerTimezone);
        // 开关记录
        AgencyMetaDataEntity metaDataEntity = new AgencyMetaDataEntity();
        metaDataEntity.setAgencyId(agencyId);
        metaDataEntity.setMetaValue("1");
        when(agencyDao.getMeta(anyString(), anyString())).thenReturn(metaDataEntity);
        // 活跃数据
        List<DailyActivityModel> dailyActivityModels = new ArrayList<>();
        DailyActivityModel dailyActivityModel = new DailyActivityModel();
        String childId = "3d7179c6-0c54-4265-aeab-2f7e812040bc"; // 学生 ID
        String groupId = "f3e3e3e3-0c54-4265-aeab-2f7e812040bc"; // 班级 ID
        String centerId = "c3e3e3e3-0c54-4265-aeab-2f7e812040bc"; // 学校 ID
        String activityUserId = "a3e3e3e3-0c54-4265-aeab-2f7e812040bc"; // 操作用户 ID
        dailyActivityModel.setChildId(childId); // 设置学生 ID
        dailyActivityModel.setGroupId(groupId); // 设置班级 ID
        dailyActivityModel.setCenterId(centerId); // 设置学校 ID
        dailyActivityModel.setAgencyId(agencyId); // 设置机构 ID
        dailyActivityModel.setActivityUserId(activityUserId); // 设置操作用户 ID
        dailyActivityModel.setActivityAtUtc(beginLocalTime); // 设置活跃 UTC 时间
        dailyActivityModel.setActivityAtLocal(beginLocalTime); // 设置活跃本地时间
        dailyActivityModels.add(dailyActivityModel);
        // 模拟观察记录活跃数据
        when(biDao.getUpdatePortfolioDailyActivitiesByAgencyIdAndDateRange(anyString(), anyString(), anyString())).thenReturn(dailyActivityModels);
        // 模拟评分活跃数据
        when(biDao.getScoreDailyActivitiesByAgencyIdAndDateRange(anyString(), anyString(), anyString())).thenReturn(dailyActivityModels);
        // 模拟锁定活跃数据
        when(biDao.getLockDailyActivitiesByAgencyIdAndDateRange(anyString(), anyString(), anyString())).thenReturn(dailyActivityModels);
        // 模拟下载报告活跃数据
        when(biDao.getDownloadPortfolioReportDailyActivitiesByAgencyIdAndDateRange(anyString(), anyString(), anyString())).thenReturn(dailyActivityModels);
        // 模拟老师更新家园互动活跃数据
        when(biDao.getUpdateEngagementDailyActivitiesByAgencyIdAndDateRange(anyString(), anyString(), anyString())).thenReturn(dailyActivityModels);
        // 模拟老师发送聊天消息活跃数据
        when(biDao.getTeacherSendTwoWayMessageDailyActivitiesByAgencyIdAndDateRange(anyString(), anyString(), anyString())).thenReturn(dailyActivityModels);
        // 模拟家长发送聊天消息活跃数据
        when(biDao.getParentSendTwoWayMessageDailyActivitiesByAgencyIdAndDateRange(anyString(), anyString(), anyString())).thenReturn(dailyActivityModels);
        // 模拟家长查看家园互动活跃数据
        when(biDao.getParentViewEngagementDailyActivitiesByAgencyIdAndDateRange(anyString(), anyString(), anyString())).thenReturn(dailyActivityModels);
        // 模拟老师签到/签退活跃数据
        when(biDao.getTeacherAttendanceDailyActivitiesByAgencyIdAndDateRange(anyString(), anyString(), anyString())).thenReturn(dailyActivityModels);
        // 模拟老师创建 InKind 任务活跃数据
        when(biDao.getTeacherInKindAssignmentDailyActivitiesByAgencyIdAndDateRange(anyString(), anyString(), anyString())).thenReturn(dailyActivityModels);
        // 模拟老师审核 InKind 活跃数据
        when(biDao.getTeacherInKindReviewDailyActivitiesByAgencyIdAndDateRange(anyString(), anyString(), anyString())).thenReturn(dailyActivityModels);
        // 模拟家长提交 InKind 报告活跃数据
        when(biDao.getParentSubmitInKindDailyActivitiesByAgencyIdAndDateRange(anyString(), anyString(), anyString())).thenReturn(dailyActivityModels);
        // 模拟老师更新 DLL 记录活跃数据
        when(biDao.getTeacherUpdateDLLDailyActivitiesByAgencyIdAndDateRange(anyString(), anyString(), anyString())).thenReturn(dailyActivityModels);
        // 模拟老师更新课程活跃数据
        when(biDao.getTeacherUpdateLessonDailyActivitiesByAgencyIdAndDateRange(anyString(), anyString(), anyString())).thenReturn(dailyActivityModels);
        // 模拟老师更新周计划活跃数据
        when(biDao.getTeacherUpdateWeekPlanDailyActivitiesByAgencyIdAndDateRange(anyString(), anyString(), anyString())).thenReturn(dailyActivityModels);
        // 模拟老师更新周计划反思活跃数据
        when(biDao.getTeacherUpdateWeekPlanReflectionDailyActivitiesByAgencyIdAndDateRange(anyString(), anyString(), anyString())).thenReturn(dailyActivityModels);
        // 模拟老师更新睡眠检查记录活跃数据
        when(biDao.getTeacherUpdateISTDailyActivitiesByAgencyIdAndDateRange(anyString(), anyString(), anyString())).thenReturn(dailyActivityModels);

        // 调用方法
        biService.updateAgencyDailyActivityData(agencyId, beginLocalTime, endLocalTime);

        // 验证结果 biDailyActivityDao.batchSave 的次数
        verify(biDailyActivityDao, times(17)).batchSave(anyList());
    }

    /**
     * 测试获取活跃度统计信息,类型是机构人员
     */
    @Test
    public void testGetActivityStatsData() {
        // 准备入参
        GetActivityStatsDataRequest request = new GetActivityStatsDataRequest();
        String agencyId = "78C5C21C-B0D2-4D36-8F28-8A04DBC21D65"; // 机构 ID
        String beginDate = "01/15/2024"; // 开始时间
        String endDate = "01/28/2024"; // 结束时间
        String module = "ATTENDANCE"; // 模块
        String userType = "EDUCATOR"; // 用户类型
        String email = "<EMAIL>";
        request.setAgencyId(agencyId);
        request.setBeginDate(beginDate);
        request.setEndDate(endDate);
        request.setModule(module);
        request.setUserType(userType);
        request.setEmail(email);
        // 用户模型
        UserModel userModel = new UserModel();
        String userId = "eb2b5b9d-4000-4dee-805e-e40bd95871e6"; // 用户 ID
        userModel.setId(userId);
        // 模拟获取用户信息
        when(userDao.getUserByEmail(anyString())).thenReturn(userModel);
        // 机构模型
        AgencyModel agency = new AgencyModel();
        agency.setId(agencyId);
        // 模拟获取机构信息
        when(shardingProvider.getAgencyByUserId(anyString())).thenReturn(agency);
        // 机构信息
        AgencyEntity agencyEntity = new AgencyEntity();
        String agencyName = "LearningGenie"; // 机构名称
        agencyEntity.setName(agencyName);
        // 模拟获取机构信息
        when(agencyDao.getByIdWithDeleted(anyString())).thenReturn(agencyEntity);
        // 班级信息
        List<GroupEntity> allGroups = new ArrayList<>();
        GroupEntity groupEntity = new GroupEntity();
        String groupId = "21f4bfa6-195e-431c-923c-94d7d8f20e99";
        groupEntity.setId(groupId);
        allGroups.add(groupEntity);
        // 模拟获取班级信息
        when(groupDao.getGroupByAgencyIdExcludeTrainingAndEmpty(anyString())).thenReturn(allGroups);
        // 班级信息元数据
        AgencyMetaDataEntity metaDataEntity = new AgencyMetaDataEntity();
        metaDataEntity.setMetaValue(groupId);
        // 模拟获取班级信息元数据
        when(agencyDao.getMeta(anyString(), anyString())).thenReturn(metaDataEntity);
        // 机构中的老师
        List<UserModel> teachers = new ArrayList<>();
        UserModel teacher = new UserModel();
        String teacherId = "3eb0fb58-4739-44fd-b59d-ed80c4512d42";
        teacher.setId(teacherId);
        teachers.add(teacher);
        // 模拟获取机构中的老师
        when(userDao.getTeacherByAgencyId(anyString())).thenReturn(teachers);
        // 机构中的园长
        List<UserModel> siteAdmins = new ArrayList<>();
        UserModel siteAdmin = new UserModel();
        String siteAdminId = "854cdad7-2f0a-4a2c-9be5-6e8a77d750ed";
        siteAdmin.setId(siteAdminId);
        siteAdmins.add(siteAdmin);
        // 模拟获取机构中的园长
        when(userDao.getSiteAdminByAgencyId(anyString())).thenReturn(siteAdmins);
        // 机构中的管理员
        List<UserModel> agencyAdmins = new ArrayList<>();
        UserModel agencyAdmin = new UserModel();
        String agencyAdminId = "f3e3e3e3-0c54-4265-aeab-2f7e812040bc";
        agencyAdmin.setId(agencyAdminId);
        agencyAdmins.add(agencyAdmin);
        // 模拟获取机构中的管理员
        when(userDao.getAgencyAdminsByAgencyId(anyString())).thenReturn(agencyAdmins);
        // 日活记录
        List<BIDailyActivityEntity> dailyActivityEntities = new ArrayList<>();
        BIDailyActivityEntity dailyActivityEntity = new BIDailyActivityEntity();
        String childId = "05569791-2a79-4608-9d14-5e57d4f63b4d"; // 学生 ID
        dailyActivityEntity.setAgencyId(agencyId);
        dailyActivityEntity.setSortKey("sortKey");
        dailyActivityEntity.setActivityTime("2024-01-16 00:00:00.000");
        dailyActivityEntity.setModule(module);
        dailyActivityEntity.setMetric("ATTEND");
        dailyActivityEntity.setChildId(childId);
        dailyActivityEntity.setGroupId(groupId);
        dailyActivityEntity.setCenterId("centerId");
        dailyActivityEntity.setActivityUserId(userId);
        dailyActivityEntity.setUserType(userType);
        dailyActivityEntity.setCreateAtUtc("2024-01-16 00:00:00.000");
        dailyActivityEntity.setUpdateAtUtc("2024-01-16 00:00:00.000");
        dailyActivityEntities.add(dailyActivityEntity);
        // 模拟获取日活记录
        when(biDailyActivityDao.getDailyActivities(anyString(), anyString(), anyString(), any(), any())).thenReturn(dailyActivityEntities);

        // 调用方法
        GetActivityStatsDataResponse response = biService.getActivityStatsData(request);

        // 验证结果
        assertEquals(agencyName, response.getAgencyInfo().getAgencyName());
    }

    /**
     * 测试获取活跃度统计信息,类型是家长
     */
    @Test
    public void testGetActivityStatsData2() {
        // 准备入参
        GetActivityStatsDataRequest request = new GetActivityStatsDataRequest();
        String agencyId = "78C5C21C-B0D2-4D36-8F28-8A04DBC21D65"; // 机构 ID
        String beginDate = "01/15/2024"; // 开始时间
        String endDate = "01/28/2024"; // 结束时间
        String module = "IN_KIND"; // 模块
        String userType = "PARENT"; // 用户类型
        String email = "<EMAIL>";
        request.setAgencyId(agencyId);
        request.setBeginDate(beginDate);
        request.setEndDate(endDate);
        request.setModule(module);
        request.setUserType(userType);
        request.setEmail(email);
        // 用户模型
        UserModel userModel = new UserModel();
        String userId = "eb2b5b9d-4000-4dee-805e-e40bd95871e6"; // 用户 ID
        userModel.setId(userId);
        // 模拟获取用户信息
        when(userDao.getUserByEmail(anyString())).thenReturn(userModel);
        // 机构模型
        AgencyModel agency = new AgencyModel();
        agency.setId(agencyId);
        // 模拟获取机构信息
        when(shardingProvider.getAgencyByUserId(anyString())).thenReturn(agency);
        // 机构信息
        AgencyEntity agencyEntity = new AgencyEntity();
        String agencyName = "LearningGenie"; // 机构名称
        agencyEntity.setName(agencyName);
        // 模拟获取机构信息
        when(agencyDao.getByIdWithDeleted(anyString())).thenReturn(agencyEntity);
        // 班级信息
        List<GroupEntity> allGroups = new ArrayList<>();
        GroupEntity groupEntity = new GroupEntity();
        String groupId = "21f4bfa6-195e-431c-923c-94d7d8f20e99";
        groupEntity.setId(groupId);
        allGroups.add(groupEntity);
        // 模拟获取班级信息
        when(groupDao.getGroupByAgencyIdExcludeTrainingAndEmpty(anyString())).thenReturn(allGroups);
        // 打开 InKind 班级列表
        List<String> groupIds = new ArrayList<>();
        groupIds.add(groupId);
        // 模拟获取 InKind 班级列表
        when(groupDao.getInKindGroupByAgencyId(anyString())).thenReturn(groupIds);
        // 有家长的小孩数量
        int hasParentChildCount = 10;
        // 模拟获取有家长的小孩数量
        when(userDao.getHasParentChildCountByAgencyId(anyString())).thenReturn(hasParentChildCount);
        // 日活记录
        List<BIDailyActivityEntity> dailyActivityEntities = new ArrayList<>();
        BIDailyActivityEntity dailyActivityEntity = new BIDailyActivityEntity();
        String childId = "05569791-2a79-4608-9d14-5e57d4f63b4d"; // 学生 ID
        dailyActivityEntity.setAgencyId(agencyId);
        dailyActivityEntity.setSortKey("sortKey");
        dailyActivityEntity.setActivityTime("2024-01-16 00:00:00.000");
        dailyActivityEntity.setModule(module);
        dailyActivityEntity.setMetric("ATTEND");
        dailyActivityEntity.setChildId(childId);
        dailyActivityEntity.setGroupId(groupId);
        dailyActivityEntity.setCenterId("centerId");
        dailyActivityEntity.setActivityUserId(userId);
        dailyActivityEntity.setUserType(userType);
        dailyActivityEntity.setCreateAtUtc("2024-01-16 00:00:00.000");
        dailyActivityEntity.setUpdateAtUtc("2024-01-16 00:00:00.000");
        dailyActivityEntities.add(dailyActivityEntity);
        // 模拟获取日活记录
        when(biDailyActivityDao.getDailyActivities(anyString(), anyString(), anyString(), any(), any())).thenReturn(dailyActivityEntities);

        // 调用方法
        GetActivityStatsDataResponse response = biService.getActivityStatsData(request);

        // 验证结果
        assertEquals(agencyName, response.getAgencyInfo().getAgencyName());
    }

    /**
     * 测试获取单元课程活跃统计信息
     */
    @Test
    public void testGetUnitPlannerActivityStatsData() {
        // 准备入参
        GetActivityStatsDataRequest request = new GetActivityStatsDataRequest();
        String agencyId = "unit-planner-agency-id"; // 机构 ID
        String beginDate = "01/15/2024"; // 开始时间
        String endDate = "01/28/2024"; // 结束时间
        String module = "UNIT_PLANNER"; // 模块
        String userType = "EDUCATOR"; // 用户类型
        String email = "<EMAIL>";
        request.setAgencyId(agencyId);
        request.setBeginDate(beginDate);
        request.setEndDate(endDate);
        request.setModule(module);
        request.setUserType(userType);
        request.setEmail(email);
        // 用户模型
        UserModel userModel = new UserModel();
        String userId = "unit-planner-agency-user-id"; // 用户 ID
        userModel.setId(userId);
        // 模拟获取用户信息
        when(userDao.getUserByEmail(anyString())).thenReturn(userModel);
        // 机构模型
        AgencyModel agency = new AgencyModel();
        agency.setId(agencyId);
        // 模拟获取机构信息
        when(shardingProvider.getAgencyByUserId(anyString())).thenReturn(agency);
        // 机构信息
        AgencyEntity agencyEntity = new AgencyEntity();
        String agencyName = "unit-planner-agency"; // 机构名称
        agencyEntity.setName(agencyName);
        // 模拟获取机构信息
        when(agencyDao.getByIdWithDeleted(anyString())).thenReturn(agencyEntity);
        // 班级信息
        List<GroupEntity> allGroups = new ArrayList<>();
        GroupEntity groupEntity = new GroupEntity();
        String groupId = "unit-planner-group-id";
        groupEntity.setId(groupId);
        allGroups.add(groupEntity);
        // 模拟获取班级信息
        when(groupDao.getGroupByAgencyIdExcludeTrainingAndEmpty(anyString())).thenReturn(allGroups);
        // 班级信息元数据
        AgencyMetaDataEntity metaDataEntity = new AgencyMetaDataEntity();
        metaDataEntity.setMetaValue(groupId);
        // 机构中的老师
        List<UserModel> teachers = new ArrayList<>();
        UserModel teacher = new UserModel();
        String teacherId = "unit-planner-teacher-id";
        teacher.setId(teacherId);
        teachers.add(teacher);
        // 模拟获取机构中的老师
        when(userDao.getTeacherByAgencyId(anyString())).thenReturn(teachers);
        // 机构中的园长
        List<UserModel> siteAdmins = new ArrayList<>();
        UserModel siteAdmin = new UserModel();
        String siteAdminId = "unit-planner-site-admin-id";
        siteAdmin.setId(siteAdminId);
        siteAdmins.add(siteAdmin);
        // 模拟获取机构中的园长
        when(userDao.getSiteAdminByAgencyId(anyString())).thenReturn(siteAdmins);
        // 机构中的管理员
        List<UserModel> agencyAdmins = new ArrayList<>();
        UserModel agencyAdmin = new UserModel();
        String agencyAdminId = "unit-planner-agency-admin-id";
        agencyAdmin.setId(agencyAdminId);
        agencyAdmins.add(agencyAdmin);
        // 模拟获取机构中的管理员
        when(userDao.getAgencyAdminsByAgencyId(anyString())).thenReturn(agencyAdmins);
        // 日活记录
        List<BIDailyActivityEntity> dailyActivityEntities = new ArrayList<>();
        BIDailyActivityEntity dailyActivityEntity = new BIDailyActivityEntity();
        String childId = "05569791-2a79-4608-9d14-5e57d4f63b4d"; // 学生 ID
        dailyActivityEntity.setAgencyId(agencyId);
        dailyActivityEntity.setSortKey("sortKey");
        dailyActivityEntity.setActivityTime("2024-01-16 00:00:00.000");
        dailyActivityEntity.setModule(module);
        dailyActivityEntity.setMetric("ATTEND");
        dailyActivityEntity.setChildId(childId);
        dailyActivityEntity.setGroupId(groupId);
        dailyActivityEntity.setCenterId("centerId");
        dailyActivityEntity.setActivityUserId(userId);
        dailyActivityEntity.setUserType(userType);
        dailyActivityEntity.setCreateAtUtc("2024-01-16 00:00:00.000");
        dailyActivityEntity.setUpdateAtUtc("2024-01-16 00:00:00.000");
        dailyActivityEntities.add(dailyActivityEntity);
        // 用户日活记录
        List<BIUserDailyActivityEntity> userDailyActivities = new ArrayList<>();
        BIUserDailyActivityEntity userDailyActivity = new BIUserDailyActivityEntity();
        userDailyActivity.setEvent(Metric.CREATE_UNIT.toString());
        userDailyActivity.setActivityDate("2024-01-16");
        userDailyActivity.setEventCount(2);
        userDailyActivity.setUserId(userId);
        userDailyActivities.add(userDailyActivity);
        when(biUserDailyActivityDao.getDailyActivities(anyString(), any(), any(), any(), any())).thenReturn(userDailyActivities);

        // 调用方法
        GetActivityStatsDataResponse response = biService.getActivityStatsData(request);

        // 验证结果
        assertEquals(agencyName, response.getAgencyInfo().getAgencyName());
    }
}

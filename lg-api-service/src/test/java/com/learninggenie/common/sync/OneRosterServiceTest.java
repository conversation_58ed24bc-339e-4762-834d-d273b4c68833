package com.learninggenie.common.sync;


import com.learninggenie.common.data.enums.api.OAuthType;
import com.learninggenie.common.data.model.roster.one.DemographicsModel;
import com.learninggenie.common.data.model.roster.one.UserModel;
import com.learninggenie.common.data.model.roster.one.UserWrapperModel;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * OneRosterService 测试
 */
@RunWith(MockitoJUnitRunner.class)
public class OneRosterServiceTest {

    /**
     * 测试获取所有家长信息
     */
    @Ignore
    @Test
    public void testGetAllUsers() {
        // 准备数据
        String key = "eec4fe1fb9573e29158fed39";
        String secret = "843f7686242ba6989a0a99f2";
        String tokenUrl = "";
        String baseUrl = "https://classlinkcertification3-vn-v2.rosterserver.com/ims/oneroster/v1p1";
        OAuthType oAuthType = OAuthType.OAUTH_1;
        OneRosterService oneRosterService = new OneRosterService(key, secret, tokenUrl, baseUrl, oAuthType);

        // 调用方法
        List<UserModel> users = oneRosterService.getAllUsers("parent");

        // 断言
        assertEquals(0, users.size());
    }

    /**
     * 测试获取单个家长信息
     */
    @Ignore
    @Test
    public void testGetSingleUser() {
        // 准备数据
        String key = "eec4fe1fb9573e29158fed39";
        String secret = "843f7686242ba6989a0a99f2";
        String tokenUrl = "";
        String baseUrl = "https://classlinkcertification3-vn-v2.rosterserver.com/ims/oneroster/v1p1";
        OAuthType oAuthType = OAuthType.OAUTH_1;
        OneRosterService oneRosterService = new OneRosterService(key, secret, tokenUrl, baseUrl, oAuthType);

        // 调用方法
        UserWrapperModel singleUser = oneRosterService.getSingleUser("28329");

        // 断言
        assertEquals("28329", singleUser.getUser().getSourcedId());
    }

    /**
     * 测试批量获取学生属性
     */
    @Ignore
    @Test
    public void testGetDemographicsAllStudents() {
        // 准备数据
        String key = "eec4fe1fb9573e29158fed39";
        String secret = "843f7686242ba6989a0a99f2";
        String tokenUrl = "";
        String baseUrl = "https://classlinkcertification3-vn-v2.rosterserver.com/ims/oneroster/v1p1";
        OAuthType oAuthType = OAuthType.OAUTH_1;
        OneRosterService oneRosterService = new OneRosterService(key, secret, tokenUrl, baseUrl, oAuthType);

        // 调用方法
        List<DemographicsModel> allStudents = oneRosterService.getDemographicsAllStudents();

        // 断言
        assertEquals(164, allStudents.size());
    }
}

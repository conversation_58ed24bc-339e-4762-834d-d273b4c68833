package com.learninggenie.common.comm;

import com.learninggenie.common.cache.CacheModel;
import com.learninggenie.common.cache.CacheService;
import com.learninggenie.common.comm.quickblox.model.message.DialogLatestMessage;
import com.learninggenie.common.comm.quickblox.model.message.MessageStartTimeResponse;
import com.learninggenie.common.data.dao.AgencyDao;
import com.learninggenie.common.data.dao.GroupDao;
import com.learninggenie.common.data.dao.MetaDao;
import com.learninggenie.common.data.dao.StudentDao;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.entity.AgencyEntity;
import com.learninggenie.common.data.entity.AgencyMetaDataEntity;
import com.learninggenie.common.data.entity.GroupEntity;
import com.learninggenie.common.data.enums.ChildMetaKey;
import com.learninggenie.common.data.enums.UserRole;
import com.learninggenie.common.data.model.UserEntity;
import com.learninggenie.common.data.model.UserModel;
import com.learninggenie.common.data.model.communication.ChatGroupModel;
import com.learninggenie.common.data.model.communication.ChildHasParentsResponse;
import com.learninggenie.common.data.repository.UserRepository;
import com.learninggenie.common.filesystem.FileSystem;
import com.learninggenie.common.region.RegionService;
import com.learninggenie.common.utils.JsonUtil;
import com.learninggenie.common.utils.TimeUtil;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

/**
 * 聊天服务测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class CommServiceImplTest {

    @InjectMocks
    private CommServiceImpl commService;

    @Mock
    private StudentDao studentDao;

    @Mock
    private UserDaoImpl userDao;

    @Mock
    private GroupDao groupDao;

    @Mock
    private FileSystem fileSystem;

    @Mock
    private RegionService regionService;

    @Mock
    private CacheService cacheService;

    @Mock
    private AgencyDao agencyDao;

    @Mock
    private MetaDao metaDao;

    @Mock
    private UserRepository userRepository;

    /**
     * 测试不传入参数的情况
     */
    @Test
    public void testGetMessageStartTime() {
        // 数据准备
        // 设置系统配置的初始化时间
        String startTime = "2024-07-01 00:00:00";
        when(metaDao.getAppMeta(Mockito.anyString())).thenReturn(startTime);
        // 调用方法
        MessageStartTimeResponse messageStartTime = commService.getMessageStartTime("");

        // 结果验证
        assert messageStartTime.getStartTime().equals("2024-07-01 00:00:00");
    }

    /**
     * 测试不传入参数的情况且系统设置时间也为空
     */
    @Test
    public void testGetMessageStartTime1() {
        // 数据准备
        // 设置系统配置的初始化时间
        when(metaDao.getAppMeta(Mockito.anyString())).thenReturn(null);
        // 模拟系统现在的时间
        LocalDateTime fixedDateTime = LocalDateTime.parse("2023-07-01T00:00:00.000");
        MockedStatic<LocalDateTime> localDateTimeMockedStatic = mockStatic(LocalDateTime.class);
        // 模拟 LocalDateTime.now() 方法返回固定的时间
        localDateTimeMockedStatic.when(LocalDateTime::now).thenReturn(fixedDateTime);
        // 模拟 LocalDateTime.of() 方法返回固定的时间
        localDateTimeMockedStatic.when(() -> LocalDateTime.of(anyInt(), anyInt(), anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(fixedDateTime);

        // 调用方法
        MessageStartTimeResponse messageStartTime = commService.getMessageStartTime("");
        // 关闭模拟
        localDateTimeMockedStatic.close();

        // 断言
        assert messageStartTime.getStartTime().equals("2023-07-01 00:00:00");
    }

    /**
     * 测试传入参数且缓存中有数据的情况
     */
    @Test
    public void testGetMessageStartTime2() {
        // 数据准备
        // 设置聊天组 id 对应的机构 id
        String agencyId = "f495b383-79e1-4347-9180-6ab3ea53f3f5";
        when(studentDao.getAgencyIdByChatGroupId(Mockito.anyString())).thenReturn(agencyId);
        // 设置缓存中的数据
        CacheModel cacheModel = new CacheModel();
        cacheModel.setKey("CHAT_RECORD_START_TIME_" + agencyId);
        cacheModel.setValue("2025-07-01");
        when(cacheService.get(Mockito.anyString())).thenReturn(cacheModel);

        // 调用方法
        MessageStartTimeResponse messageStartTime = commService.getMessageStartTime("6e034e31-eff8-4f69-8eb1-f83bd7bba4ca");

        // 结果验证
        assert messageStartTime.getStartTime().equals("2025-07-01");
    }

    /**
     * 测试传入参数且缓存中没有数据的情况
     */
    @Test
    public void testGetMessageStartTime3() {
        // 数据准备
        // 设置聊天组 id 对应的机构 id
        String agencyId = "f495b383-79e1-4347-9180-6ab3ea53f3f5";
        when(studentDao.getAgencyIdByChatGroupId(Mockito.anyString())).thenReturn(agencyId);
        // 设置缓存中的为空的数据
        when(cacheService.get(Mockito.anyString())).thenReturn(null);
        // 设置数据库中的数据
        AgencyMetaDataEntity metaDataEntity = new AgencyMetaDataEntity();
        metaDataEntity.setMetaKey("CHAT_START_TIME");
        metaDataEntity.setMetaValue("2026-07-01");
        when(agencyDao.getMeta(Mockito.anyString(), Mockito.anyString())).thenReturn(metaDataEntity);

        // 调用方法
        MessageStartTimeResponse messageStartTime = commService.getMessageStartTime("6e034e31-eff8-4f69-8eb1-f83bd7bba4ca");

        // 结果验证
        assert messageStartTime.getStartTime().equals("2026-07-01");
    }

    /**
     * 测试传入参数且缓存中没有数据且数据库中没有数据的情况
     */
    @Test
    public void testGetMessageStartTime4() {
        // 数据准备
        // 设置系统配置的初始化时间
        String startTime = "2027-07-01 00:00:00";
        when(metaDao.getAppMeta(Mockito.anyString())).thenReturn(startTime);
        // 设置聊天组 id 对应的机构 id
        String agencyId = "f495b383-79e1-4347-9180-6ab3ea53f3f5";
        when(studentDao.getAgencyIdByChatGroupId(Mockito.anyString())).thenReturn(agencyId);
        // 设置缓存中的为空的数据
        when(cacheService.get(Mockito.anyString())).thenReturn(null);
        // 设置数据库中的数据为空
        when(agencyDao.getMeta(Mockito.anyString(), Mockito.anyString())).thenReturn(null);

        // 调用方法
        MessageStartTimeResponse messageStartTime = commService.getMessageStartTime("6e034e31-eff8-4f69-8eb1-f83bd7bba4ca");

        // 结果验证
        assert messageStartTime.getStartTime().equals("2027-07-01 00:00:00");
    }

    /**
     * 测试 GetUserChatGroupsByGroupId
     */
    @Test
    public void testGetUserChatGroupsByGroupId() {
        // 创建测试数据
        String userId = "teacherUserId"; // 替换为实际的教师用户ID
        String groupId = "groupId"; // 替换为实际的班级ID
        String groupId01 = "groupId01"; // 替换为实际的班级ID

        // 创建模拟对象
        UserModel user = new UserModel();
        user.setRole(UserRole.COLLABORATOR.toString()); // 设置用户为教师

        List<GroupEntity> groups = new ArrayList<>();
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setId(groupId); // 设置班级ID
        groups.add(groupEntity);

        List<GroupEntity> groups1 = new ArrayList<>();
        GroupEntity groupEntity1 = new GroupEntity();
        groupEntity1.setId(groupId01); // 设置班级ID
        groups1.add(groupEntity1);

        AgencyEntity agencyEntity = new AgencyEntity();
        String agencyId = "agencyId";
        agencyEntity.setId(agencyId); // 设置机构ID

        List<ChildWithChatGroupModel> models = new ArrayList<>();
        // 添加模拟的ChildWithChatGroupModel对象到models列表中
        ChildWithChatGroupModel model = new ChildWithChatGroupModel();
        model.setId("ChildWithChatGroupModelId");
        model.setFirstName("FirstName");
        model.setLastName("LastName");
        model.setDisplayName("DisplayName");
        model.setCreateAtUtc(TimeUtil.getUtcNow());
        model.setUserId("UserId");
        model.setuFirstName("FirstName");
        model.setuLastName("LastName");
        model.setuDisplayName("DisplayName");
        model.setEmail("Email");
        model.setRole("Role");
        model.setChatGroupId("ChatGroupId");
        model.setGroupId("GroupId");
        model.setMediaId("MediaId");
        model.setRelativePath("RelativePath");
        model.setRelationDeleted(false);
        model.setUserDeleted(false);
        models.add(model);

        List<ChildWithChatGroupModel> models1 = new ArrayList<>();
        // 添加模拟的ChildWithChatGroupModel对象到models列表中
        ChildWithChatGroupModel model1 = new ChildWithChatGroupModel();
        model1.setId("ChildWithChatGroupModelId");
        model1.setFirstName("FirstName");
        model1.setLastName("LastName");
        model1.setDisplayName("DisplayName");
        model1.setCreateAtUtc(TimeUtil.getUtcNow());
        model1.setUserId("UserId");
        model1.setuFirstName("FirstName");
        model1.setuLastName("LastName");
        model1.setuDisplayName("DisplayName");
        model1.setEmail("Email");
        model1.setRole("Role");
        model1.setChatGroupId("ChatGroupId");
        model1.setGroupId(groupId01);
        model1.setMediaId("MediaId");
        model1.setRelativePath("RelativePath");
        model1.setRelationDeleted(false);
        model1.setUserDeleted(false);
        models1.add(model);

        // 设置模拟对象的行为
        when(userDao.getUserById(userId)).thenReturn(user);
        when(groupDao.getGroupByTeacher(userId)).thenReturn(groups);
        // 定义 owner
        UserModel owner = new UserModel();
        String ownerId = "ownerId";
        owner.setId(ownerId);
        when(userDao.getAgencyOwnerByTeacherId(userId)).thenReturn(owner);
        ArrayList<AgencyEntity> agencyEntities = new ArrayList<>();
        AgencyEntity agency = new AgencyEntity();
        agency.setId(agencyId);
        agencyEntities.add(agency);
        when(agencyDao.getAgencyByIdentifierUserId(ownerId)).thenReturn(agencyEntities);
        when(userDao.getGroupsBySpecialEducationId(anyList(), anyList())).thenReturn(new ArrayList<>());
        when(agencyDao.getByGroupId(groupId)).thenReturn(agencyEntity);
        when(studentDao.getChildChatGroupByGroupId(groupId, 2)).thenReturn(models);
        when(fileSystem.getChildAvatarUrl(Mockito.anyString())).thenReturn("avatarUrl");

        DialogLatestMessage dialogLatestMessage = new DialogLatestMessage("en-Us", "SenderId", "SenderName", "ContentType", "MessageId");
        String cacheValue = JsonUtil.toJson(dialogLatestMessage);
        // 设置缓存中的数据
        CacheModel cacheModel = new CacheModel();
        cacheModel.setValue(cacheValue);
        when(cacheService.get(Mockito.anyString())).thenReturn(cacheModel);

        // 调用方法
        List<ChatGroupModel> result = commService.getUserChatGroupsByGroupId(userId, groupId, "");

        // 进行断言
        Assert.assertEquals(models.size(), result.size());
        // 添加更多的断言，根据实际返回结果来断言

        // 验证模拟对象的方法是否被调用
        Mockito.verify(userDao, Mockito.times(1)).getUserById(userId);
        Mockito.verify(groupDao, Mockito.times(1)).getGroupByTeacher(userId);
        Mockito.verify(userDao, Mockito.times(1)).getGroupsBySpecialEducationId(anyList(), anyList());
        Mockito.verify(groupDao, Mockito.never()).getGroupsBySiteAdminId(userId);
        Mockito.verify(agencyDao, Mockito.times(1)).getByGroupId(groupId);
        Mockito.verify(studentDao, Mockito.times(1)).getChildChatGroupByGroupId(groupId, 2);
        Mockito.verify(fileSystem, Mockito.times(models.size())).getChildAvatarUrl(anyString());

        when(userDao.getGroupsBySpecialEducationId(anyList(), anyList())).thenReturn(groups1);
        List<String> groupIds = new ArrayList<>();
        groupIds.add(groupId01);
        when(studentDao.getChildChatGroupByGroupIds(groupIds, 2)).thenReturn(models1);
        // 调用方法
        List<ChatGroupModel> result1 = commService.getUserChatGroupsByGroupId(userId, "", ChildMetaKey.SPECIAL_TEACHER.toString());
        // 进行断言
        Assert.assertEquals(models.size(), result1.size());
    }

    @Test
    public void testGetChildWithParent() {
        // 创建测试数据
        String userId = "userId"; // 替换为实际的用户ID
        String groupId = "groupId"; // 替换为实际的群组ID
        String agencyId = "agencyId"; // 替换为实际的机构ID
        String groupId01 = "groupId01"; // 替换为实际的群组ID

        // 定义 owner
        UserModel owner = new UserModel();
        String ownerId = "ownerId";
        owner.setId(ownerId);
        // 机构信息班级信息
        List<String> teacherGroupIds = new ArrayList<>();
        teacherGroupIds.add(groupId);

        List<String> chatGroupIds = new ArrayList<>();
        chatGroupIds.add(groupId);
        chatGroupIds.add(groupId01);

        // 设置用户信息
        com.learninggenie.common.data.entity.UserEntity user = new com.learninggenie.common.data.entity.UserEntity();
        user.setId(userId);
        user.setFirstName("firstName");
        user.setLastName("lastName");
        user.setRole("COLLABORATOR");

        ArrayList<AgencyEntity> agencyEntities = new ArrayList<>();
        AgencyEntity agency = new AgencyEntity();
        agency.setId(agencyId);
        agencyEntities.add(agency);

        List<GroupEntity> groups1 = new ArrayList<>();
        GroupEntity groupEntity1 = new GroupEntity();
        groupEntity1.setId(groupId01); // 设置班级ID
        groups1.add(groupEntity1);

        List<ChildWithChatGroupModel> models = new ArrayList<>();
        // 添加模拟的ChildWithChatGroupModel对象到models列表中
        ChildWithChatGroupModel model = new ChildWithChatGroupModel();
        model.setId("ChildWithChatGroupModelId");
        model.setFirstName("FirstName");
        model.setLastName("LastName");
        model.setDisplayName("DisplayName");
        model.setCreateAtUtc(TimeUtil.getUtcNow());
        model.setUserId("UserId");
        model.setuFirstName("FirstName");
        model.setuLastName("LastName");
        model.setuDisplayName("DisplayName");
        model.setEmail("Email");
        model.setRole("Role");
        model.setChatGroupId("ChatGroupId");
        model.setGroupId("GroupId");
        model.setMediaId("MediaId");
        model.setRelativePath("RelativePath");
        model.setRelationDeleted(false);
        model.setUserDeleted(false);
        models.add(model);

        when(userRepository.findById(userId)).thenReturn(Optional.of(user));
        when(groupDao.getGroupIdsByTeacherId(anyString())).thenReturn(teacherGroupIds);
        when(userDao.getAgencyOwnerByTeacherId(userId)).thenReturn(owner);
        when(agencyDao.getAgencyByIdentifierUserId(ownerId)).thenReturn(agencyEntities);
        when(userDao.getGroupsBySpecialEducationId(anyList(), anyList())).thenReturn(groups1);
        when(studentDao.getChildWithChatGroupByUserId(chatGroupIds, 2)).thenReturn(models);
        ChildHasParentsResponse response = commService.getChildWithParent(userId);
        Assert.assertEquals(models.size(), response.getChildHasParents().size());
    }
}

package com.learninggenie.common.data.dao.impl;



import com.learninggenie.common.data.dto.student.StudentDTO;
import com.learninggenie.common.data.enums.StatusType;
import com.learninggenie.common.data.mapper.StudentMapper;
import com.learninggenie.common.data.model.StudentSnapshotEntity;
import com.learninggenie.common.utils.PeriodUtil;
import com.learninggenie.common.utils.StringUtil;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import static com.google.common.base.CharMatcher.any;
import static com.learninggenie.common.data.dao.impl.StudentDaoImpl.MAPPER_SNAPSHOT;
import static io.reactivex.Single.never;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;


/**
 * 学生dao层测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class StudentDaoImplTest {
    @InjectMocks
    private StudentDaoImpl studentDao;

    @Mock
    private JdbcTemplate jdbcTemplate;

    /**
     * 测试根据聊天组 Id 获取机构 Id
     */
    @Test
    public void tesGetAgencyIdByChatGroupId() {
        // 准备测试数据
        String chatGroupId = "c275a2b6-35c5-4aeb-a9d5-23e5ba738579";
        String expectedAgencyId = "f495b383-79e1-4347-9180-6ab3ea53f3f5";
        when(jdbcTemplate.queryForObject(
                        StudentMapper.SQL_SELECT_ENROLLMENT_BY_AGENCY_ID_AND_GROUP_ID,
                        new Object[]{chatGroupId},
                        String.class))
                .thenReturn(expectedAgencyId);

        // 方法调用
        String result = studentDao.getAgencyIdByChatGroupId(chatGroupId);

        // 结果验证
        assertEquals(expectedAgencyId, result);
    }

    @Test
    public void testGetProcessingSnapshots() {
        String agencyId = UUID.randomUUID().toString();
        String schoolYear = PeriodUtil.getCurrentSchoolYear();
        List<StudentSnapshotEntity> snapshots = new ArrayList<>();
        when(jdbcTemplate.query(StudentMapper.SQL_GET_PROCESSING_SNAPSHOTS, new Object[]{agencyId, StringUtil.convertLikeParam(schoolYear)},
                MAPPER_SNAPSHOT)).thenReturn(snapshots);
        List<StudentSnapshotEntity> result = studentDao.getProcessingSnapshots(agencyId, schoolYear);

        assertEquals(0, result.size());
    }

    @Test
    public void testUpdateSnapshotUploadStatusByIds_empty_snapshots() {
        List<String> snapshotIds = Collections.emptyList();
        StatusType status = StatusType.PROCESSING;
        studentDao.updateSnapshotUploadStatusByIds(snapshotIds, status, new Date());
        verify(jdbcTemplate, Mockito.never()).update(anyString(), Mockito.anyList());
    }

    @Test
    public void testUpdateSnapshotUploadStatusByIds() {
        List<String> snapshotIds = Arrays.asList(UUID.randomUUID().toString());
        StatusType status = StatusType.PROCESSING;
        Date now = new Date();
        studentDao.updateSnapshotUploadStatusByIds(snapshotIds, status, now);
        String sql = StudentMapper.SQL_UPDATE_SNAPSHOT_UPLOAD_STATUS_BY_IDS.replace("${snapshotIds}", StringUtil.convertIdsToString(snapshotIds));
        verify(jdbcTemplate).update(sql, status.toString(), now);
    }

    @Test
    public void testGetSnapshotsByUploadId() {
        String uploadId = UUID.randomUUID().toString();
        when(jdbcTemplate.query(StudentMapper.SQL_GET_SNAPSHOTS_BY_UPLOAD_ID, new Object[]{uploadId}, MAPPER_SNAPSHOT)).thenReturn(Collections.emptyList());

        List<StudentSnapshotEntity> result = studentDao.getSnapshotsByUploadId(uploadId);

        assertEquals(0, result.size());
    }

    /**
     * 测试输入 childIds 为 null 的情况。
     * 期望返回 null。
     */
    @Test
    public void testListGroupIdsByChildIdsWithNullChildIds() {
        List<StudentDTO> result = studentDao.listGroupIdsByChildIds(null);
        assertNull(result);
    }

    /**
     * 测试输入 childIds 为空列表的情况。
     * 期望返回 null。
     */
    @Test
    public void testListGroupIdsByChildIdsWithEmptyChildIds() {
        List<StudentDTO> result = studentDao.listGroupIdsByChildIds(Collections.emptyList());
        assertNull(result);
    }

    /**
     * 测试输入 childIds 为非空列表，并且 jdbcTemplate.query 返回非空列表的情况。
     * 期望返回正确的结果。
     */
    @Test
    public void testListGroupIdsByChildIdsWithNonEmptyChildIdsAndNonEmptyResult() {
        List<String> childIds = Arrays.asList("id1", "id2", "id3");
        List<StudentDTO> expectedResult = Arrays.asList(new StudentDTO(), new StudentDTO());

        String sql = "SELECT * FROM students WHERE child_id IN ('id1', 'id2', 'id3')";
        when(jdbcTemplate.query(anyString(), eq(StudentMapper.STUDENT_DTO))).thenReturn(expectedResult);

        List<StudentDTO> result = studentDao.listGroupIdsByChildIds(childIds);
        assertNotNull(result);
        assertEquals(expectedResult, result);
    }

    /**
     * 测试输入 childIds 为非空列表，并且 jdbcTemplate.query 返回空列表的情况。
     * 期望返回空列表。
     */
    @Test
    public void testListGroupIdsByChildIdsWithNonEmptyChildIdsAndEmptyResult() {
        List<String> childIds = Arrays.asList("id1", "id2", "id3");

        String sql = "SELECT * FROM students WHERE child_id IN ('id1', 'id2', 'id3')";
        when(jdbcTemplate.query(anyString(), eq(StudentMapper.STUDENT_DTO))).thenReturn(Collections.emptyList());

        List<StudentDTO> result = studentDao.listGroupIdsByChildIds(childIds);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}

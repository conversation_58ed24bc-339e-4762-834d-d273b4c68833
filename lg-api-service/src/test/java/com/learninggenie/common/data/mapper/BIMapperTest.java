package com.learninggenie.common.data.mapper;

import com.learninggenie.common.data.model.bi.activity.DailyActivityModel;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.util.List;

/**
 *  BIMapper 测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class BIMapperTest {

    @InjectMocks
    private BIMapper biMapper;

    @Mock
    private ResultSetMetaData resultSetMetaData;

    @Mock
    private ResultSet resultSet;

    /**
     * 测试获取活跃模块信息
     */
    @Test
    public void testMapperDailyActivityModel() throws Exception {
        // 列数
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(6);
        // 列名 childId
        final String childId = "ChildId";
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(childId);
        // 列名 groupId
        final String groupId = "GroupId";
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(groupId);
        // 列名 centerId
        final String centerId = "CenterId";
        Mockito.when(resultSetMetaData.getColumnName(3)).thenReturn(centerId);
        // 列名 agencyId
        final String agencyId = "AgencyId";
        Mockito.when(resultSetMetaData.getColumnName(4)).thenReturn(agencyId);
        // 列名 activityUserId
        final String activityUserId = "UserId";
        Mockito.when(resultSetMetaData.getColumnName(5)).thenReturn(activityUserId);
        // 列名 activityAtUtc
        final String activityAtUtc = "ActivityAtUtc";
        Mockito.when(resultSetMetaData.getColumnName(6)).thenReturn(activityAtUtc);
        // 设置结果集元数据
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);
        // 模拟结果集
        final String testChildId = "5e71f5ae-be81-4362-ac2d-0434418d7829";
        final String testGroupId = "f495b383-79e1-4347-9180-6ab3ea53f3f5";
        final String testCenterId = "f495b383-79e1-4347-9180-6ab3ea53f3f5";
        final String testAgencyId = "f495b383-79e1-4347-9180-6ab3ea53f3f5";
        final String testActivityUserId = "f495b383-79e1-4347-9180-6ab3ea53f3f5";
        // 设置结果集
        Mockito.when(resultSet.getString(childId)).thenReturn(testChildId);
        Mockito.when(resultSet.getString(groupId)).thenReturn(testGroupId);
        Mockito.when(resultSet.getString(centerId)).thenReturn(testCenterId);
        Mockito.when(resultSet.getString(agencyId)).thenReturn(testAgencyId);
        Mockito.when(resultSet.getString(activityUserId)).thenReturn(testActivityUserId);

        // 调用测试方法
        RowMapper<DailyActivityModel> dailyActivity = BIMapper.MAPPER_DAILY_ACTIVITY;
        // 获取结果
        DailyActivityModel dailyActivityModel = dailyActivity.mapRow(resultSet, 0);
        // 断言
        // 验证 childId
        Assert.assertEquals(testChildId, dailyActivityModel.getChildId());
        // 验证 groupId
        Assert.assertEquals(testGroupId, dailyActivityModel.getGroupId());
        // 验证 centerId
        Assert.assertEquals(testCenterId, dailyActivityModel.getCenterId());
        // 验证 agencyId
        Assert.assertEquals(testAgencyId, dailyActivityModel.getAgencyId());
        // 验证 activityUserId
        Assert.assertEquals(testActivityUserId, dailyActivityModel.getActivityUserId());


    }


}

package com.learninggenie.common.data.dao.lesson2.impl;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.toolkit.ChainWrappers;
import com.learninggenie.common.data.entity.lesson2.plan.PlanEntity;
import com.learninggenie.common.data.mapper.mybatisplus.lesson2.PlanMapper;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.BeforeAll;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.mockito.Mockito.*;

/**
 * PlanDaoImpl 测试
 */
@RunWith(MockitoJUnitRunner.class)
public class PlanDaoImplTest {

    @InjectMocks
    private PlanDaoImpl planDao;

    @Mock
    private PlanMapper planMapper;

    private MockedStatic<ChainWrappers> chainWrappersMockedStatic;


    /**
     * 初始化 TableInfoHelper
     */
    @BeforeAll
    public static void beforeClass() {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), PlanEntity.class);
    }



    /**
     * 测试创建周计划
     */
    @Test
    public void testCreate() {
        // 创建一个实际的 PlanEntity 对象，或者也可以使用 mock 方式创建
        PlanEntity planEntity = new PlanEntity();
        // 设置实体对象的属性

        // 调用 create 方法
        planDao.create(planEntity);

        // 验证 PlanMapper 的 save 方法被调用了一次，并传入了正确的参数
        verify(planMapper, times(1)).insert(planEntity);
    }

    /**
     * 测试获取周计划
     */
    @Test
    public void testGetById() {
        // 准备测试数据
        String id = "1";
        PlanEntity expectedPlanEntity = new PlanEntity();
        when(planMapper.selectById(id)).thenReturn(expectedPlanEntity);

        // 方法调用
        planDao.get(id);

        // 结果验证
        verify(planMapper, times(1)).selectById(id);
    }

    /**
     * 测试获取周计划列表
     */
    @Test
    public void testGetByIds() {
        // 准备测试数据
        List<String> ids = new ArrayList<>();
        ids.add("1");
        ids.add("2");
        List<PlanEntity> expectedPlanEntityList = new ArrayList<>();
        when(planMapper.selectBatchIds(ids)).thenReturn(expectedPlanEntityList);

        // 方法调用
        planDao.getByIds(ids);

        // 结果验证
        verify(planMapper, times(1)).selectBatchIds(ids);
    }

    /**
     * 测试通过班级、周次获取周计划
     */
    @Test
    public void testListByGroupWeek() {
        PlanMapper mapper = this.planMapper;
        ReflectionTestUtils.setField(planDao, "baseMapper", mapper);
        chainWrappersMockedStatic = mockStatic(ChainWrappers.class);
        final LambdaQueryChainWrapper<PlanEntity> lambdaQuery = new LambdaQueryChainWrapper<>(mapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(mapper)).thenReturn(lambdaQuery);
        List<PlanEntity> planEntities = planDao.listByGroupWeek("9fee7670-94a0-446a-a82e-bc3aa5ad7705", 2);
        chainWrappersMockedStatic.close();
        Assert.assertEquals(0, planEntities.size());
    }

    /**
     * 测试更新周计划
     */
    @Test
    public void testUpdate() {
        PlanEntity planEntity = new PlanEntity();
        planDao.update(planEntity);
        verify(planMapper, times(1)).updateById(planEntity);
    }

    /**
     * 测试更新周计划的更新时间
     */
    @Test
    public void testUpdateUpdateAtUtc() {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), PlanEntity.class);
        PlanMapper mapper = this.planMapper;
        ReflectionTestUtils.setField(planDao, "baseMapper", mapper);
        chainWrappersMockedStatic = mockStatic(ChainWrappers.class);
        final LambdaUpdateChainWrapper<PlanEntity> lambdaQuery = new LambdaUpdateChainWrapper<>(mapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaUpdateChain(mapper)).thenReturn(lambdaQuery);
        planDao.updateUpdateAtUtc("9fee7670-94a0-446a-a82e-bc3aa5ad7705");
        chainWrappersMockedStatic.close();
    }

    /**
     * 测试更新周计划的更新时间和生成周计划草稿的状态
     */
    @Test
    public void testUpdateNoteDraftStatusAndUpdateAtUtc() {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), PlanEntity.class);
        PlanMapper mapper = this.planMapper;
        ReflectionTestUtils.setField(planDao, "baseMapper", mapper);
        chainWrappersMockedStatic = mockStatic(ChainWrappers.class);
        final LambdaUpdateChainWrapper<PlanEntity> lambdaQuery = new LambdaUpdateChainWrapper<>(mapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaUpdateChain(mapper)).thenReturn(lambdaQuery);
        planDao.updateNoteDraftStatusAndUpdateAtUtc("9fee7670-94a0-446a-a82e-bc3aa5ad7705", "9fee7670-94a0-446a-a82e-bc3aa5ad7705");
        chainWrappersMockedStatic.close();
    }

    /**
     * 测试更新周计划状态
     */
    @Test
    public void testUpdateStatus() {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), PlanEntity.class);
        PlanMapper mapper = this.planMapper;
        ReflectionTestUtils.setField(planDao, "baseMapper", mapper);
        chainWrappersMockedStatic = mockStatic(ChainWrappers.class);
        final LambdaUpdateChainWrapper<PlanEntity> lambdaQuery = new LambdaUpdateChainWrapper<>(mapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaUpdateChain(mapper)).thenReturn(lambdaQuery);
        planDao.updateStatus("9fee7670-94a0-446a-a82e-bc3aa5ad7705", "Pending", new Date(), new Date(), new Date());
        chainWrappersMockedStatic.close();
    }

    /**
     * 测试更新周计划审核日期
     */
    @Test
    public void testUpdateReviewDate() {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), PlanEntity.class);
        PlanMapper mapper = this.planMapper;
        ReflectionTestUtils.setField(planDao, "baseMapper", mapper);
        chainWrappersMockedStatic = mockStatic(ChainWrappers.class);
        final LambdaUpdateChainWrapper<PlanEntity> lambdaQuery = new LambdaUpdateChainWrapper<>(mapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaUpdateChain(mapper)).thenReturn(lambdaQuery);
        planDao.updateReviewDate("9fee7670-94a0-446a-a82e-bc3aa5ad7705", new Date());
        chainWrappersMockedStatic.close();
    }

    /**
     * 测试删除周计划
     */
    @Test
    public void testDelete() {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), PlanEntity.class);
        PlanMapper mapper = this.planMapper;
        ReflectionTestUtils.setField(planDao, "baseMapper", mapper);
        chainWrappersMockedStatic = mockStatic(ChainWrappers.class);
        final LambdaUpdateChainWrapper<PlanEntity> lambdaQuery = new LambdaUpdateChainWrapper<>(mapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaUpdateChain(mapper)).thenReturn(lambdaQuery);
        planDao.delete("9fee7670-94a0-446a-a82e-bc3aa5ad7705");
        chainWrappersMockedStatic.close();
    }

    /**
     * 测试获取系统预置周计划结构模板
     */
    @Test
    public void testGetCurriculumGenieTemplate() {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), PlanEntity.class);
        PlanMapper mapper = this.planMapper;
        ReflectionTestUtils.setField(planDao, "baseMapper", mapper);
        chainWrappersMockedStatic = mockStatic(ChainWrappers.class);
        final LambdaQueryChainWrapper<PlanEntity> lambdaQuery = new LambdaQueryChainWrapper<>(mapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(mapper)).thenReturn(lambdaQuery);
        planDao.getCurriculumGenieTemplate();
        chainWrappersMockedStatic.close();
    }

    /**
     * 测试获取课程单元预置周计划结构模板
     */
    @Test
    public void testGetSystemTemplate() {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), PlanEntity.class);
        PlanMapper mapper = this.planMapper;
        ReflectionTestUtils.setField(planDao, "baseMapper", mapper);
        chainWrappersMockedStatic = mockStatic(ChainWrappers.class);
        final LambdaQueryChainWrapper<PlanEntity> lambdaQuery = new LambdaQueryChainWrapper<>(mapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(mapper)).thenReturn(lambdaQuery);
        planDao.getSystemTemplate();
        chainWrappersMockedStatic.close();
    }

    /**
     * 测试获取机构的周计划结构模板
     */
    @Test
    public void testGetAgencyCurrentWeekPlanTemplate() {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), PlanEntity.class);
        PlanMapper mapper = this.planMapper;
        ReflectionTestUtils.setField(planDao, "baseMapper", mapper);
        chainWrappersMockedStatic = mockStatic(ChainWrappers.class);
        final LambdaQueryChainWrapper<PlanEntity> lambdaQuery = new LambdaQueryChainWrapper<>(mapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(mapper)).thenReturn(lambdaQuery);
        planDao.getAgencyCurrentWeekPlanTemplate("c57969a7-2749-4de6-949d-a36f47e66c91");
        chainWrappersMockedStatic.close();
    }

    /**
     * 测试获取机构所有的周计划模板
     */
    @Test
    public void testGetAgencyWeekPlanTemplates() {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), PlanEntity.class);
        PlanMapper mapper = this.planMapper;
        ReflectionTestUtils.setField(planDao, "baseMapper", mapper);
        chainWrappersMockedStatic = mockStatic(ChainWrappers.class);
        final LambdaQueryChainWrapper<PlanEntity> lambdaQuery = new LambdaQueryChainWrapper<>(mapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(mapper)).thenReturn(lambdaQuery);
        List<PlanEntity> planEntityList = planDao.getAgencyWeekPlanTemplates("c57969a7-2749-4de6-949d-a36f47e66c91");
        chainWrappersMockedStatic.close();
        Assert.assertEquals(0, planEntityList.size());
    }

    /**
     * 测试批量获取周计划
     */
    @Test
    public void testListPlansByIds() {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), PlanEntity.class);
        PlanMapper mapper = this.planMapper;
        ReflectionTestUtils.setField(planDao, "baseMapper", mapper);
        chainWrappersMockedStatic = mockStatic(ChainWrappers.class);
        final LambdaQueryChainWrapper<PlanEntity> lambdaQuery = new LambdaQueryChainWrapper<>(mapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(mapper)).thenReturn(lambdaQuery);
        List<PlanEntity> planEntityList = planDao.listPlansByIds(Collections.singletonList("c57969a7-2749-4de6-949d-a36f47e66c91"));
        chainWrappersMockedStatic.close();
        Assert.assertEquals(0, planEntityList.size());
    }
    /**
     * 测试批量获取周计划
     */
    @Test
    public void testListPlansByIds2() {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), PlanEntity.class);
        PlanMapper mapper = this.planMapper;
        ReflectionTestUtils.setField(planDao, "baseMapper", mapper);
        chainWrappersMockedStatic = mockStatic(ChainWrappers.class);
        final LambdaQueryChainWrapper<PlanEntity> lambdaQuery = new LambdaQueryChainWrapper<>(mapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(mapper)).thenReturn(lambdaQuery);
        List<PlanEntity> planEntityList = planDao.listPlansByIds(null);
        chainWrappersMockedStatic.close();
        Assert.assertEquals(0, planEntityList.size());
    }

    /**
     * 测试批量获取周计划
     */
    @Test
    public void testListAllPlansByIds() {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), PlanEntity.class);
        PlanMapper mapper = this.planMapper;
        ReflectionTestUtils.setField(planDao, "baseMapper", mapper);
        chainWrappersMockedStatic = mockStatic(ChainWrappers.class);
        final LambdaQueryChainWrapper<PlanEntity> lambdaQuery = new LambdaQueryChainWrapper<>(mapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(mapper)).thenReturn(lambdaQuery);
        List<PlanEntity> planEntityList = planDao.listAllPlansByIds(Collections.singletonList("c57969a7-2749-4de6-949d-a36f47e66c91"));
        chainWrappersMockedStatic.close();
        Assert.assertEquals(0, planEntityList.size());
    }

    /**
     * 测试批量获取周计划
     */
    @Test
    public void testListAllPlansByIds2() {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), PlanEntity.class);
        PlanMapper mapper = this.planMapper;
        ReflectionTestUtils.setField(planDao, "baseMapper", mapper);
        chainWrappersMockedStatic = mockStatic(ChainWrappers.class);
        final LambdaQueryChainWrapper<PlanEntity> lambdaQuery = new LambdaQueryChainWrapper<>(mapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(mapper)).thenReturn(lambdaQuery);
        List<PlanEntity> planEntityList = planDao.listAllPlansByIds(null);
        chainWrappersMockedStatic.close();
        Assert.assertEquals(0, planEntityList.size());
    }

    /**
     * 测试获取机构下某时间周后创建的周计划列表
     */
    @Test
    public void testListPlansAfterDateByAgencyId() {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), PlanEntity.class);
        PlanMapper mapper = this.planMapper;
        ReflectionTestUtils.setField(planDao, "baseMapper", mapper);
        chainWrappersMockedStatic = mockStatic(ChainWrappers.class);
        final LambdaQueryChainWrapper<PlanEntity> lambdaQuery = new LambdaQueryChainWrapper<>(mapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(mapper)).thenReturn(lambdaQuery);
        List<PlanEntity> planEntityList = planDao.listPlansAfterDateByAgencyId("c57969a7-2749-4de6-949d-a36f47e66c91", new Date());
        chainWrappersMockedStatic.close();
        Assert.assertEquals(0, planEntityList.size());
    }
}

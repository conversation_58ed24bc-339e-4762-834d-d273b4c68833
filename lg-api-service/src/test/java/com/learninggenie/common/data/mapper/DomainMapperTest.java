package com.learninggenie.common.data.mapper;

import com.learninggenie.common.data.model.note.NoteDomainModel;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;

/**
 * domainMapper 测试
 */
@RunWith(MockitoJUnitRunner.class)
public class DomainMapperTest {

    @InjectMocks
    private DomainMapper domainMapper;

    @Mock
    private ResultSetMetaData resultSetMetaData;

    @Mock
    private ResultSet resultSet;

    /**
     * 测试根据观察记录获取相应的领域.
     */
    @Test
    public void testNoteDomainEntityRowMapper() throws Exception {
        // 列数
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(11);
        // 列名 id
        final String id = "Id";
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(id);
        // 列名 Name
        final String name = "Name";
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(name);
        // 列名 Abbreviation
        final String abbreviation = "Abbreviation";
        Mockito.when(resultSetMetaData.getColumnName(3)).thenReturn(abbreviation);
        // 列名 MeasureNumber
        final String measureNumber = "MeasureNumber";
        Mockito.when(resultSetMetaData.getColumnName(4)).thenReturn(measureNumber);
        // 列名 IconPath
        final String iconPath = "IconPath";
        Mockito.when(resultSetMetaData.getColumnName(5)).thenReturn(iconPath);
        // 列名 SortIndex
        final String sortIndex = "SortIndex";
        Mockito.when(resultSetMetaData.getColumnName(6)).thenReturn(sortIndex);
        // 列名 LinkUrl
        final String linkUrl = "LinkUrl";
        Mockito.when(resultSetMetaData.getColumnName(7)).thenReturn(linkUrl);
        // 列名 NoteId
        final String noteId = "NoteId";
        Mockito.when(resultSetMetaData.getColumnName(8)).thenReturn(noteId);
        // 列名 UseCondition
        final String useCondition = "UseCondition";
        Mockito.when(resultSetMetaData.getColumnName(9)).thenReturn(useCondition);
        // 列名 ParentId
        final String parentId = "ParentId";
        Mockito.when(resultSetMetaData.getColumnName(10)).thenReturn(parentId);
        // 列名 NewParentId
        final String newParentId = "NewParentId";
        Mockito.when(resultSetMetaData.getColumnName(11)).thenReturn(newParentId);
        // 设置结果集元数据
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);
        // 模拟结果集
        final String testId = "4fd0392c-55d3-eb11-9c19-4ccc6acf6129";
        final String testName = "Curiosity and Initiative in Learning";
        final String testAbbreviation = "ATL-REG4";
        final String testMeasureNumber = "1";
        final String testIconPath = "https://lg-dev-media.s3.amazonaws.com/Domain/4FD0392C-55D3-EB11-9C19-4CCC6ACF6129/Icon/4FD0392C-55D3-EB11-9C19-4CCC6ACF6129.png";
        final Integer testSortIndex = 1;
        final String testLinkUrl = "https://lg-dev-media.s3.amazonaws.com/Domain/4FD0392C-55D3-EB11-9C19-4CCC6ACF6129/Link/4FD0392C-55D3-EB11-9C19-4CCC6ACF6129.pdf";
        final String testNoteId = "4FD0392C-55D3-EB11-9C19-4CCC6ACF6129";
        final String testUseCondition = "1";
        final String testParentId = "4BD0392C-55D3-EB11-9C19-4CCC6ACF6129";
        final String testNewParentId = "1C5D38B3-14C7-4803-9487-3451B986F595";
        // 设置结果集
        Mockito.when(resultSet.getString(id)).thenReturn(testId);
        Mockito.when(resultSet.getString(name)).thenReturn(testName);
        Mockito.when(resultSet.getString(abbreviation)).thenReturn(testAbbreviation);
        Mockito.when(resultSet.getString(measureNumber)).thenReturn(testMeasureNumber);
        Mockito.when(resultSet.getString(iconPath)).thenReturn(testIconPath);
        Mockito.when(resultSet.getInt(sortIndex)).thenReturn(testSortIndex);
        Mockito.when(resultSet.getString(linkUrl)).thenReturn(testLinkUrl);
        Mockito.when(resultSet.getString(noteId)).thenReturn(testNoteId);
        Mockito.when(resultSet.getString(useCondition)).thenReturn(testUseCondition);
        Mockito.when(resultSet.getString(parentId)).thenReturn(testParentId);
        Mockito.when(resultSet.getString(newParentId)).thenReturn(testNewParentId);

        // 调用方法
        final NoteDomainModel domainModel = DomainMapper.NOTE_DOMAIN_ENTITY_ROW_MAPPER.mapRow(resultSet, 1);

        // 验证结果
        // 验证 id
        Assert.assertEquals(testId, domainModel.getId());
        // 验证 name
        Assert.assertEquals(testName, domainModel.getName());
        // 验证 abbreviation
        Assert.assertEquals(testAbbreviation, domainModel.getAbbreviation());
        // 验证 measureNumber
        Assert.assertEquals(testMeasureNumber, domainModel.getMeasureNumber());
        // 验证 iconPath
        Assert.assertEquals(testIconPath, domainModel.getIconPath());
        // 验证 sortIndex
        Assert.assertEquals(testSortIndex, domainModel.getSortIndex());
        // 验证 linkUrl
        Assert.assertEquals(testLinkUrl, domainModel.getLinkUrl());
        // 验证 noteId
        Assert.assertEquals(testNoteId, domainModel.getNoteId());
        // 验证 useCondition
        Assert.assertEquals(testUseCondition, domainModel.getUseCondition());
        // 验证 parentId
        Assert.assertEquals(testNewParentId, domainModel.getParent().getId());

    }
}

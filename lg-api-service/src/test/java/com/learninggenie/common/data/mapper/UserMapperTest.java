package com.learninggenie.common.data.mapper;

import com.learninggenie.common.data.dto.ParentDto;
import com.learninggenie.common.data.entity.UserEnrollmentModel;
import com.learninggenie.common.data.entity.UserEntity;
import com.learninggenie.common.data.entity.UserFileEntity;
import com.learninggenie.common.data.entity.UserMetaDataEntity;
import com.learninggenie.common.data.model.CenterGroupModel;
import com.learninggenie.common.data.model.UserModel;
import com.learninggenie.common.data.model.UserWithCenterGroup;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;


/**
 * UserMapper 测试
 */

@RunWith(MockitoJUnitRunner.class)
public class UserMapperTest {

    @InjectMocks
    private UserMapper userMapper;

    @Mock
    private ResultSetMetaData resultSetMetaData;

    @Mock
    private ResultSet resultSet;

    private static final String ID = "Id";
    private static final String EMAIL = "Email";
    private static final String FIRST_NAME = "FirstName";
    private static final String DISPLAY_NAME = "DisplayName";
    private static final String LAST_NAME = "LastName";
    private static final String TEMP_PASSWORD = "TempPassword";
    private static final String AVATAR_ID = "AvatarId";
    private static final String AVATAR_RELATIVE_PATH = "AvatarRelativePath";
    private static final String ROLE = "Role";
    private static final String REGISTER_COUNTRY = "RegisterCountry";
    private static final String PHONE_NUMBER = "PhoneNumber";

    private static final String CHILD_DISPLAY_NAME = "ChildDisplayName";
    private static final String ENROLLMENT_ID = "EnrollmentId";

    /**
     * 测试 userModel 的 mapper
     */
    @Test
    public void testMapperUserModer() throws Exception {
        // 列数
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(15);
        // 列名
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(ID);
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(FIRST_NAME);
        Mockito.when(resultSetMetaData.getColumnName(3)).thenReturn(LAST_NAME);
        Mockito.when(resultSetMetaData.getColumnName(4)).thenReturn(DISPLAY_NAME);
        Mockito.when(resultSetMetaData.getColumnName(5)).thenReturn(EMAIL);
        Mockito.when(resultSetMetaData.getColumnName(6)).thenReturn(TEMP_PASSWORD);
        Mockito.when(resultSetMetaData.getColumnName(7)).thenReturn(AVATAR_ID);
        Mockito.when(resultSetMetaData.getColumnName(8)).thenReturn(AVATAR_RELATIVE_PATH);
        Mockito.when(resultSetMetaData.getColumnName(9)).thenReturn(ROLE);
        Mockito.when(resultSetMetaData.getColumnName(10)).thenReturn(REGISTER_COUNTRY);
        Mockito.when(resultSetMetaData.getColumnName(11)).thenReturn(PHONE_NUMBER);
        final String centerId = "CenterId";
        final String groupId = "groupId";
        Mockito.when(resultSetMetaData.getColumnName(12)).thenReturn(centerId);
        Mockito.when(resultSetMetaData.getColumnName(13)).thenReturn(groupId);
        Mockito.when(resultSetMetaData.getColumnName(14)).thenReturn(CHILD_DISPLAY_NAME);
        Mockito.when(resultSetMetaData.getColumnName(15)).thenReturn(ENROLLMENT_ID);
        // 设置结果集元数据
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);
        final String testId = "2e18ee77-bad4-4611-aa65-dfb156b4e50f";
        final String testFirstName = "testFirstName";
        final String testLastName = "testLastName";
        final String testDisplayName = "testDisplayName";
        final String testEmail = "testEmail";
        final String testTempPassword = "testTempPassword";
        final String testAvatarId = "testAvatarId";
        final String testAvatarRelativePath = "testAvatarRelativePath";
        final String testRole = "testRole";
        final String testRegisterCountry = "testRegisterCountry";
        final String testPhoneNumber = "testPhoneNumber";
        final String testCenterId = "b044d78d-a9d5-4b9a-9d4e-0eec4f55aaa4";
        final String testGroupId = "3fe42dad-b857-46cd-a717-998363dc07bc";
        final String testChildDisplayName = "testChildDisplayName";
        final String testEnrollmentId = "7e6cbf06-1bb1-4542-94fc-c803b8d98d5c";
        Mockito.when(resultSet.getString(ID)).thenReturn(testId);
        Mockito.when(resultSet.getString(FIRST_NAME)).thenReturn(testFirstName);
        Mockito.when(resultSet.getString(LAST_NAME)).thenReturn(testLastName);
        Mockito.when(resultSet.getString(DISPLAY_NAME)).thenReturn(testDisplayName);
        Mockito.when(resultSet.getString(EMAIL)).thenReturn(testEmail);
        Mockito.when(resultSet.getString(TEMP_PASSWORD)).thenReturn(testTempPassword);
        Mockito.when(resultSet.getString(AVATAR_ID)).thenReturn(testAvatarId);
        Mockito.when(resultSet.getString(AVATAR_RELATIVE_PATH)).thenReturn(testAvatarRelativePath);
        Mockito.when(resultSet.getString(ROLE)).thenReturn(testRole);
        Mockito.when(resultSet.getString(REGISTER_COUNTRY)).thenReturn(testRegisterCountry);
        Mockito.when(resultSet.getString(PHONE_NUMBER)).thenReturn(testPhoneNumber);
        Mockito.when(resultSet.getString(centerId)).thenReturn(testCenterId);
        Mockito.when(resultSet.getString(groupId)).thenReturn(testGroupId);
        Mockito.when(resultSet.getString(CHILD_DISPLAY_NAME)).thenReturn(testChildDisplayName);
        Mockito.when(resultSet.getString(ENROLLMENT_ID)).thenReturn(testEnrollmentId);

        // 调用测试方法
        RowMapper<UserModel> mapperUserModel = UserMapper.MAPPER_USER_MODEL;

        // 验证结果
        UserModel userModel = mapperUserModel.mapRow(resultSet, 0);
        // 验证 ID
        Assert.assertEquals(testId, userModel.getId());
        // 验证 FirstName
        Assert.assertEquals(testFirstName, userModel.getFirstName());
    }

    /**
     * 测试 userModel 的 mapper
     */
    @Test
    public void testMapperUserAndChildModel() throws Exception {
        // 列数
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(16);
        // 列名
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(ID);
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(FIRST_NAME);
        Mockito.when(resultSetMetaData.getColumnName(3)).thenReturn(LAST_NAME);
        Mockito.when(resultSetMetaData.getColumnName(4)).thenReturn(DISPLAY_NAME);
        Mockito.when(resultSetMetaData.getColumnName(5)).thenReturn(EMAIL);
        Mockito.when(resultSetMetaData.getColumnName(6)).thenReturn(TEMP_PASSWORD);
        Mockito.when(resultSetMetaData.getColumnName(7)).thenReturn(AVATAR_ID);
        Mockito.when(resultSetMetaData.getColumnName(8)).thenReturn(AVATAR_RELATIVE_PATH);
        Mockito.when(resultSetMetaData.getColumnName(9)).thenReturn(ROLE);
        Mockito.when(resultSetMetaData.getColumnName(10)).thenReturn(REGISTER_COUNTRY);
        Mockito.when(resultSetMetaData.getColumnName(11)).thenReturn(PHONE_NUMBER);
        final String centerId = "CenterId";
        final String groupId = "groupId";
        final String relationShip = "RelationShip";
        Mockito.when(resultSetMetaData.getColumnName(12)).thenReturn(centerId);
        Mockito.when(resultSetMetaData.getColumnName(13)).thenReturn(groupId);
        Mockito.when(resultSetMetaData.getColumnName(14)).thenReturn(CHILD_DISPLAY_NAME);
        Mockito.when(resultSetMetaData.getColumnName(15)).thenReturn(ENROLLMENT_ID);
        Mockito.when(resultSetMetaData.getColumnName(16)).thenReturn(relationShip);
        // 设置结果集元数据
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);
        final String testId = "2e18ee77-bad4-4611-aa65-dfb156b4e50f";
        final String testFirstName = "testFirstName";
        final String testLastName = "testLastName";
        final String testDisplayName = "testDisplayName";
        final String testEmail = "testEmail";
        final String testTempPassword = "testTempPassword";
        final String testAvatarId = "testAvatarId";
        final String testAvatarRelativePath = "testAvatarRelativePath";
        final String testRole = "testRole";
        final String testRegisterCountry = "testRegisterCountry";
        final String testPhoneNumber = "testPhoneNumber";
        final String testCenterId = "b044d78d-a9d5-4b9a-9d4e-0eec4f55aaa4";
        final String testGroupId = "3fe42dad-b857-46cd-a717-998363dc07bc";
        final String testChildDisplayName = "testChildDisplayName";
        final String testEnrollmentId = "7e6cbf06-1bb1-4542-94fc-c803b8d98d5c";
        Mockito.when(resultSet.getString(ID)).thenReturn(testId);
        Mockito.when(resultSet.getString(FIRST_NAME)).thenReturn(testFirstName);
        Mockito.when(resultSet.getString(LAST_NAME)).thenReturn(testLastName);
        Mockito.when(resultSet.getString(DISPLAY_NAME)).thenReturn(testDisplayName);
        Mockito.when(resultSet.getString(EMAIL)).thenReturn(testEmail);
        Mockito.when(resultSet.getString(TEMP_PASSWORD)).thenReturn(testTempPassword);
        Mockito.when(resultSet.getString(AVATAR_ID)).thenReturn(testAvatarId);
        Mockito.when(resultSet.getString(AVATAR_RELATIVE_PATH)).thenReturn(testAvatarRelativePath);
        Mockito.when(resultSet.getString(ROLE)).thenReturn(testRole);
        Mockito.when(resultSet.getString(REGISTER_COUNTRY)).thenReturn(testRegisterCountry);
        Mockito.when(resultSet.getString(PHONE_NUMBER)).thenReturn(testPhoneNumber);
        Mockito.when(resultSet.getString(centerId)).thenReturn(testCenterId);
        Mockito.when(resultSet.getString(groupId)).thenReturn(testGroupId);
        Mockito.when(resultSet.getString(CHILD_DISPLAY_NAME)).thenReturn(testChildDisplayName);
        Mockito.when(resultSet.getString(ENROLLMENT_ID)).thenReturn(testEnrollmentId);

        // 调用测试方法
        RowMapper<UserModel> mapperUserModel = UserMapper.MAPPER_USER_AND_CHILD_MODEL;

        // 验证结果
        UserModel userModel = mapperUserModel.mapRow(resultSet, 0);
        // 验证 ID
        Assert.assertEquals(testId, userModel.getId());
        // 验证 FirstName
        Assert.assertEquals(testFirstName, userModel.getFirstName());
    }

    /**
     * 测试 UserEnrollmentModel 的 mapper
     */
    @Test
    public void testMapperUserEnrollment() throws Exception {
        // 列数
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(5);
        // 列名
        final String childId = "ChildId";
        final String enrollmentId = "EnrollmentId";
        final String userId = "UserId";
        final String relationShip = "RelationShip";
        final String displayName = "DisplayName";
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(childId);
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(enrollmentId);
        Mockito.when(resultSetMetaData.getColumnName(3)).thenReturn(userId);
        Mockito.when(resultSetMetaData.getColumnName(4)).thenReturn(relationShip);
        Mockito.when(resultSetMetaData.getColumnName(5)).thenReturn(displayName);
        // 设置结果集元数据
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);
        // 模拟结果集
        final String testEnrollmentId = "f495b383-79e1-4347-9180-6ab3ea53f3f5";
        final String testUserId = "f495b383-79e1-4347-1880-6ab3ea53f3f5";
        final String testDisplayName = "test";
        // 模拟结果集
        Mockito.when(resultSet.getString(childId)).thenReturn("");
        Mockito.when(resultSet.getString(enrollmentId)).thenReturn(testEnrollmentId);
        Mockito.when(resultSet.getString(userId)).thenReturn(testUserId);
        Mockito.when(resultSet.getString(displayName)).thenReturn(testDisplayName);

        // 调用测试方法
        RowMapper<UserEnrollmentModel> mapperUserEnrollment = UserMapper.MAPPER_USER_ENROLLMENT;
        // 验证结果
        UserEnrollmentModel userEnrollmentModel = mapperUserEnrollment.mapRow(resultSet, 0);
        // 验证 enrollmentId
        Assert.assertEquals(testEnrollmentId, userEnrollmentModel.getEnrollmentId());
    }


    /**
     * 测试 UserEntity 的 mapper
     */
    @Test
    public void testUserRowMapper() throws Exception {
        // 列数
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(8);
        // 列名
        final String id = "Id";
        final String firstName = "FirstName";
        final String lastName = "LastName";
        final String email = "email";
        final String tempPassword = "TempPassword";
        final String role = "Role";
        final String displayName = "DisplayName";
        final String createDateUtc = "CreateDateUtc";
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(id);
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(firstName);
        Mockito.when(resultSetMetaData.getColumnName(3)).thenReturn(lastName);
        Mockito.when(resultSetMetaData.getColumnName(4)).thenReturn(email);
        Mockito.when(resultSetMetaData.getColumnName(5)).thenReturn(tempPassword);
        Mockito.when(resultSetMetaData.getColumnName(6)).thenReturn(role);
        Mockito.when(resultSetMetaData.getColumnName(7)).thenReturn(displayName);
        Mockito.when(resultSetMetaData.getColumnName(8)).thenReturn(createDateUtc);
        // 设置结果集元数据
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);
        // 模拟结果集
        final String testId = "f495b383-79e1-4347-9180-6ab3ea53f3f5";
        Mockito.when(resultSet.getString(id)).thenReturn(testId);

        // 调用测试方法
        RowMapper<com.learninggenie.common.data.model.UserEntity> allUserMapper = UserMapper.USER_ENTITY_ROW_MAPPER1;
        // 验证结果
        com.learninggenie.common.data.model.UserEntity userEntity = allUserMapper.mapRow(resultSet, 0);
        // 验证 Id
        Assert.assertEquals(testId, userEntity.getId());
    }

    /**
     * 测试 UserEntity 的 mapper
     */
    @Test
    public void testUserEntityMapper() throws Exception {
        // 列数
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(13);
        // 列名
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(ID);
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(FIRST_NAME);
        Mockito.when(resultSetMetaData.getColumnName(3)).thenReturn(LAST_NAME);
        Mockito.when(resultSetMetaData.getColumnName(4)).thenReturn(DISPLAY_NAME);
        Mockito.when(resultSetMetaData.getColumnName(5)).thenReturn(EMAIL);
        Mockito.when(resultSetMetaData.getColumnName(6)).thenReturn(PHONE_NUMBER);
        Mockito.when(resultSetMetaData.getColumnName(7)).thenReturn(TEMP_PASSWORD);
        Mockito.when(resultSetMetaData.getColumnName(8)).thenReturn(ROLE);
        final String enrollmentId = "EnrollmentId";
        final String relationship = "Relationship";
        final String avatarPath = "AvatarPath";
        final String loginDateUtc = "LoginDateUtc";
        final String isDeleted = "IsDeleted";
        Mockito.when(resultSetMetaData.getColumnName(9)).thenReturn(enrollmentId);
        Mockito.when(resultSetMetaData.getColumnName(10)).thenReturn(relationship);
        Mockito.when(resultSetMetaData.getColumnName(11)).thenReturn(avatarPath);
        Mockito.when(resultSetMetaData.getColumnName(12)).thenReturn(loginDateUtc);
        Mockito.when(resultSetMetaData.getColumnName(13)).thenReturn(isDeleted);
        // 设置结果集元数据
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);
        final String testId = "2e18ee77-bad4-4611-aa65-dfb156b4e50f";
        final String testFirstName = "testFirstName";
        final String testLastName = "testLastName";
        final String testDisplayName = "testDisplayName";
        final String testEmail = "testEmail";
        final String testRole = "testRole";
        final String PhoneNumber = "testRegisterCountry";
        final String TempPassword = "testPhoneNumber";
        final String testEnrollmentId = "7e6cbf06-1bb1-4542-94fc-c803b8d98d5c";
        Mockito.when(resultSet.getString(ID)).thenReturn(testId);
        Mockito.when(resultSet.getString(FIRST_NAME)).thenReturn(testFirstName);
        Mockito.when(resultSet.getString(LAST_NAME)).thenReturn(testLastName);
        Mockito.when(resultSet.getString(DISPLAY_NAME)).thenReturn(testDisplayName);
        Mockito.when(resultSet.getString(EMAIL)).thenReturn(testEmail);
        Mockito.when(resultSet.getString(PHONE_NUMBER)).thenReturn(PhoneNumber);
        Mockito.when(resultSet.getString(TEMP_PASSWORD)).thenReturn(TempPassword);
        Mockito.when(resultSet.getString(ROLE)).thenReturn(testRole);
        Mockito.when(resultSet.getString(enrollmentId)).thenReturn(testEnrollmentId);


        // 调用测试方法
        RowMapper<UserEntity> mapperUserEntity = UserMapper.USER_ENTITY_ROW_MAPPER;

        // 验证结果
        UserEntity userEntity = mapperUserEntity.mapRow(resultSet, 0);
        // 验证 ID
        Assert.assertEquals(testId, userEntity.getId());
        // 验证 FirstName
        Assert.assertEquals(testFirstName, userEntity.getFirstName());
    }

    /**
     * 测试 userModel 的 mapper
     */
    @Test
    public void testMapperPaidUserModel() throws Exception {
        // 列数
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(6);
        // 列名
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(ID);
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(DISPLAY_NAME);
        Mockito.when(resultSetMetaData.getColumnName(3)).thenReturn(EMAIL);
        Mockito.when(resultSetMetaData.getColumnName(4)).thenReturn(FIRST_NAME);
        Mockito.when(resultSetMetaData.getColumnName(5)).thenReturn(LAST_NAME);
        Mockito.when(resultSetMetaData.getColumnName(6)).thenReturn(ROLE);
        // 设置结果集元数据
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);
        final String testId = "2e18ee77-bad4-4611-aa65-dfb156b4e50f";
        final String testFirstName = "testFirstName";
        final String testLastName = "testLastName";
        final String testDisplayName = "testDisplayName";
        final String testEmail = "testEmail";
        final String testRole = "testRole";
        Mockito.when(resultSet.getString(ID)).thenReturn(testId);
        Mockito.when(resultSet.getString(FIRST_NAME)).thenReturn(testFirstName);
        Mockito.when(resultSet.getString(LAST_NAME)).thenReturn(testLastName);
        Mockito.when(resultSet.getString(DISPLAY_NAME)).thenReturn(testDisplayName);
        Mockito.when(resultSet.getString(EMAIL)).thenReturn(testEmail);
        Mockito.when(resultSet.getString(ROLE)).thenReturn(testRole);


        // 调用测试方法
        RowMapper<UserModel> mapperUserModel = UserMapper.MAPPER_PAID_USER_MODEL;

        // 验证结果
        UserModel userModel = mapperUserModel.mapRow(resultSet, 0);
        // 验证 ID
        Assert.assertEquals(testId, userModel.getId());
        // 验证 FirstName
        Assert.assertEquals(testFirstName, userModel.getFirstName());
    }

    /**
     * 测试 userModel 的 mapper
     */
    @Test
    public void testMapperPaidUserModel2() throws Exception {
        // 列数
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(9);
        // 列名
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(ID);
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(DISPLAY_NAME);
        Mockito.when(resultSetMetaData.getColumnName(3)).thenReturn(AVATAR_ID);
        Mockito.when(resultSetMetaData.getColumnName(4)).thenReturn(EMAIL);
        Mockito.when(resultSetMetaData.getColumnName(5)).thenReturn(FIRST_NAME);
        Mockito.when(resultSetMetaData.getColumnName(6)).thenReturn(LAST_NAME);
        Mockito.when(resultSetMetaData.getColumnName(7)).thenReturn(ROLE);
        final String metaValue = "MetaValue";
        final String agencyId = "AgencyId";
        Mockito.when(resultSetMetaData.getColumnName(8)).thenReturn(metaValue);
        Mockito.when(resultSetMetaData.getColumnName(9)).thenReturn(agencyId);
        // 设置结果集元数据
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);
        final String testId = "2e18ee77-bad4-4611-aa65-dfb156b4e50f";
        final String testFirstName = "testFirstName";
        final String testLastName = "testLastName";
        final String testDisplayName = "testDisplayName";
        final String testEmail = "testEmail";
        final String testRole = "testRole";
        Mockito.when(resultSet.getString(ID)).thenReturn(testId);
        Mockito.when(resultSet.getString(FIRST_NAME)).thenReturn(testFirstName);
        Mockito.when(resultSet.getString(LAST_NAME)).thenReturn(testLastName);
        Mockito.when(resultSet.getString(DISPLAY_NAME)).thenReturn(testDisplayName);
        Mockito.when(resultSet.getString(EMAIL)).thenReturn(testEmail);
        Mockito.when(resultSet.getString(ROLE)).thenReturn(testRole);


        // 调用测试方法
        RowMapper<UserModel> mapperUserModel = UserMapper.MAPPER_USER_MAPPER;

        // 验证结果
        UserModel userModel = mapperUserModel.mapRow(resultSet, 0);
        // 验证 ID
        Assert.assertEquals(testId, userModel.getId());
        // 验证 FirstName
        Assert.assertEquals(testFirstName, userModel.getFirstName());
    }

    @Test
    public void testUserWithCenterGroup() throws Exception {
        // 列数
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(9);
        // 列名
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(ID);
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(FIRST_NAME);
        Mockito.when(resultSetMetaData.getColumnName(3)).thenReturn(LAST_NAME);
        Mockito.when(resultSetMetaData.getColumnName(4)).thenReturn(EMAIL);
        Mockito.when(resultSetMetaData.getColumnName(5)).thenReturn(ROLE);
        final String groupId = "GroupId";
        final String groupName = "GroupName";
        final String centerId = "CenterId";
        final String centerName = "CenterName";
        Mockito.when(resultSetMetaData.getColumnName(6)).thenReturn(groupId);
        Mockito.when(resultSetMetaData.getColumnName(7)).thenReturn(groupName);
        Mockito.when(resultSetMetaData.getColumnName(8)).thenReturn(centerId);
        Mockito.when(resultSetMetaData.getColumnName(9)).thenReturn(centerName);
        // 设置结果集元数据
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);
        final String testId = "2e18ee77-bad4-4611-aa65-dfb156b4e50f";
        final String testFirstName = "testFirstName";
        final String testLastName = "testLastName";
        final String testEmail = "testEmail";
        final String testRole = "testRole";
        Mockito.when(resultSet.getString(ID)).thenReturn(testId);
        Mockito.when(resultSet.getString(FIRST_NAME)).thenReturn(testFirstName);
        Mockito.when(resultSet.getString(LAST_NAME)).thenReturn(testLastName);
        Mockito.when(resultSet.getString(EMAIL)).thenReturn(testEmail);
        Mockito.when(resultSet.getString(ROLE)).thenReturn(testRole);


        // 调用测试方法
        RowMapper<UserWithCenterGroup> userWithCenterGroupRowMapper = UserMapper.USER_WITH_CENTER_GROUP_ROW_MAPPER;

        // 验证结果
        UserWithCenterGroup userModel = userWithCenterGroupRowMapper.mapRow(resultSet, 0);
        // 验证 ID
        Assert.assertEquals(testId, userModel.getUserId());
        // 验证 FirstName
        Assert.assertEquals(testFirstName, userModel.getFirstName());
    }

    /**
     * 测试 UserFileEntity mapper
     */
    @Test
    public void testUserFileEntityMapper() throws Exception {
        // 列数
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(13);
        // 列名
        final String id = "Id";
        final String name = "Name";
        final String type = "Type";
        final String excelType = "ExcelType";
        final String status = "Status";
        final String userId = "UserId";
        final String createAtUtc = "CreateAtUtc";
        final String fromAtLocal = "FromAtLocal";
        final String toAtLocal = "ToAtLocal";
        final String relativePath = "RelativePath";
        final String mediaId = "MediaId";
        final String data = "Data";
        final String language = "Language";
        // 设置列名
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(id);
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(name);
        Mockito.when(resultSetMetaData.getColumnName(3)).thenReturn(type);
        Mockito.when(resultSetMetaData.getColumnName(4)).thenReturn(excelType);
        Mockito.when(resultSetMetaData.getColumnName(5)).thenReturn(status);
        Mockito.when(resultSetMetaData.getColumnName(6)).thenReturn(userId);
        Mockito.when(resultSetMetaData.getColumnName(7)).thenReturn(createAtUtc);
        Mockito.when(resultSetMetaData.getColumnName(8)).thenReturn(fromAtLocal);
        Mockito.when(resultSetMetaData.getColumnName(9)).thenReturn(toAtLocal);
        Mockito.when(resultSetMetaData.getColumnName(10)).thenReturn(relativePath);
        Mockito.when(resultSetMetaData.getColumnName(11)).thenReturn(mediaId);
        Mockito.when(resultSetMetaData.getColumnName(12)).thenReturn(data);
        Mockito.when(resultSetMetaData.getColumnName(13)).thenReturn(language);
        // 设置结果集元数据
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);
        final String testId = "ddaa9952-5d2a-497c-87ff-b6a178ff4acf";
        final String testRelativePath = "https:///web.learning-genie.com/image";
        final String testMediaId = "690abe4a-874e-4a73-8699-4d5f800084eb";
        Mockito.when(resultSet.getString(id)).thenReturn(testId);
        Mockito.when(resultSet.getString(relativePath)).thenReturn(testRelativePath);
        Mockito.when(resultSet.getString(mediaId)).thenReturn(testMediaId);

        // 调用测试方法
        RowMapper<UserFileEntity> userFileMapper = UserMapper.USER_FILE_MAPPER;

        // 验证结果
        UserFileEntity userFileEntity = userFileMapper.mapRow(resultSet, 0);
        // 验证 Id
        Assert.assertEquals(testId, userFileEntity.getId());
        Assert.assertEquals(testRelativePath, userFileEntity.getMedia().getRelativePath());
        Assert.assertEquals(testMediaId, userFileEntity.getMedia().getId());
    }

    /**
     * 测试 UserMetaDataEntity mapper
     */
    @Test
    public void testUserMetaDataEntityMapper() throws Exception {
        // 列数
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(4);
        // 列名
        final String id = "Id";
        final String metaKey = "MetaKey";
        final String metaValue = "MetaValue";
        final String userId = "UserId";
        // 设置列名
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(id);
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(metaKey);
        Mockito.when(resultSetMetaData.getColumnName(3)).thenReturn(metaValue);
        Mockito.when(resultSetMetaData.getColumnName(4)).thenReturn(userId);
        // 设置结果集
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);
        final String testId = "9613e94c-8c08-41e5-8c77-8a8aa41e1c69";
        final String testMetaKey = "IN_KIND_GROUP_ID";
        final String testMetaValue = "cc365572-0f45-44f9-aceb-6cdf144da534";
        final String testUserId = "5a2203f8-7821-406a-b9e4-d64dfe89e63a";
        Mockito.when(resultSet.getString(id)).thenReturn(testId);
        Mockito.when(resultSet.getString(metaKey)).thenReturn(testMetaKey);
        Mockito.when(resultSet.getString(metaValue)).thenReturn(testMetaValue);
        Mockito.when(resultSet.getString(userId)).thenReturn(testUserId);

        // 调用测试方法
        RowMapper<UserMetaDataEntity> metaDataEntityRowMapper = UserMapper.USER_META_DATA_ENTITY_ROW_MAPPER;

        //验证结果
        UserMetaDataEntity metaDataEntity = metaDataEntityRowMapper.mapRow(resultSet, 0);
        // 验证用户 Id
        Assert.assertEquals(metaDataEntity.getUser().getId(), testUserId);
    }

    /**
     * 测试 CenterGroupModel mapper
     */
    @Test
    public void testCenterGroupModelMapper() throws Exception {
        // 列数
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(22);
        // 列名
        final String centerId = "centerId";
        final String centerName = "centerName";
        final String groupId = "groupId";
        final String groupName = "groupName";
        final String isDeleted = "IsDeleted";
        final String fromAtLocal = "FromAtLocal";
        final String toAtLocal = "ToAtLocal";
        final String alias = "Alias";
        final String groupIsInactive = "GroupIsInactive";
        final String centerDomainId = "centerDomainId";
        final String centerDomainName = "centerDomainName";
        final String centerDomainIsMultiType = "centerDomainIsMultiType";
        final String centerDomainType = "centerDomainType";
        final String centerDomainTypeDisplay = "centerDomainTypeDisplay";
        final String groupDomainId = "groupDomainId";
        final String groupDomainName = "groupDomainName";
        final String groupDomainIsMultiType = "groupDomainIsMultiType";
        final String groupDomainType = "groupDomainType";
        final String groupDomainTypeDisplay = "groupDomainTypeDisplay";
        final String frameworkId = "frameworkId";
        final String frameworkName = "frameworkName";
        final String periodGroupId = "periodGroupId";
        // 设置列名
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(centerId);
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(centerName);
        Mockito.when(resultSetMetaData.getColumnName(3)).thenReturn(groupId);
        Mockito.when(resultSetMetaData.getColumnName(4)).thenReturn(groupName);
        Mockito.when(resultSetMetaData.getColumnName(5)).thenReturn(isDeleted);
        Mockito.when(resultSetMetaData.getColumnName(6)).thenReturn(fromAtLocal);
        Mockito.when(resultSetMetaData.getColumnName(7)).thenReturn(toAtLocal);
        Mockito.when(resultSetMetaData.getColumnName(8)).thenReturn(alias);
        Mockito.when(resultSetMetaData.getColumnName(9)).thenReturn(groupIsInactive);
        Mockito.when(resultSetMetaData.getColumnName(10)).thenReturn(centerDomainId);
        Mockito.when(resultSetMetaData.getColumnName(11)).thenReturn(centerDomainName);
        Mockito.when(resultSetMetaData.getColumnName(12)).thenReturn(centerDomainIsMultiType);
        Mockito.when(resultSetMetaData.getColumnName(13)).thenReturn(centerDomainType);
        Mockito.when(resultSetMetaData.getColumnName(14)).thenReturn(centerDomainTypeDisplay);
        Mockito.when(resultSetMetaData.getColumnName(15)).thenReturn(groupDomainId);
        Mockito.when(resultSetMetaData.getColumnName(16)).thenReturn(groupDomainName);
        Mockito.when(resultSetMetaData.getColumnName(17)).thenReturn(groupDomainIsMultiType);
        Mockito.when(resultSetMetaData.getColumnName(18)).thenReturn(groupDomainType);
        Mockito.when(resultSetMetaData.getColumnName(19)).thenReturn(groupDomainTypeDisplay);
        Mockito.when(resultSetMetaData.getColumnName(20)).thenReturn(frameworkId);
        Mockito.when(resultSetMetaData.getColumnName(21)).thenReturn(frameworkName);
        Mockito.when(resultSetMetaData.getColumnName(22)).thenReturn(periodGroupId);
        // 设置结果集
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);
        final String testCenterId = "69f39f09-af3f-44f1-8cc3-a6f606f44263";
        final String testCenterName = "Test center";
        Mockito.when(resultSet.getString(centerId)).thenReturn(testCenterId);
        Mockito.when(resultSet.getString(centerName)).thenReturn(testCenterName);

        // 调用测试方法
        RowMapper<CenterGroupModel> mapperCenterGroup = UserMapper.MAPPER_CENTER_GROUP;

        // 验证结果
        CenterGroupModel centerGroupModel = mapperCenterGroup.mapRow(resultSet, 0);
        // 验证 centerId
        Assert.assertEquals(centerGroupModel.getCenterId(), testCenterId);
        // 验证 centerName
        Assert.assertEquals(centerGroupModel.getCenterName(), testCenterName);
    }

    /**
     * 测试 UserModel mapper
     */
    @Test
    public void testUserModelMapper() throws Exception {
        // 列数
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(8);
        // 列名
        final String noteId = "NoteId";
        final String metaValue = "MetaValue";
        final String userId = "UserId";
        final String displayName = "DisplayName";
        final String email = "Email";
        final String avatarId = "AvatarId";
        final String avatarRelativePath = "AvatarRelativePath";
        final String role = "Role";
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(noteId);
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(metaValue);
        Mockito.when(resultSetMetaData.getColumnName(3)).thenReturn(userId);
        Mockito.when(resultSetMetaData.getColumnName(4)).thenReturn(displayName);
        Mockito.when(resultSetMetaData.getColumnName(5)).thenReturn(email);
        Mockito.when(resultSetMetaData.getColumnName(6)).thenReturn(avatarId);
        Mockito.when(resultSetMetaData.getColumnName(7)).thenReturn(avatarRelativePath);
        Mockito.when(resultSetMetaData.getColumnName(8)).thenReturn(role);
        // 设置结果集
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);
        final String testNoteId = "310a415d-b8bc-4577-abd1-12dab898413c";
        final String testRole = "Admin";
        Mockito.when(resultSet.getString(noteId)).thenReturn(testNoteId);
        Mockito.when(resultSet.getString(role)).thenReturn(testRole);

        // 调用测试方法
        RowMapper<UserModel> mapperNoteHadQualityLevelUserModel = UserMapper.MAPPER_NOTE_HAD_QUALITY_LEVEL_USER_MODEL;

        // 验证结果
        UserModel userModel = mapperNoteHadQualityLevelUserModel.mapRow(resultSet, 0);
        Assert.assertEquals(userModel.getNoteId(), testNoteId);
        Assert.assertEquals(userModel.getRole(), testRole);
    }

    /**
     * 测试 common.data.model.UserEntity mapper
     */
    @Test
    public void testUserEntityMapper2() throws Exception {
        // 列数
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(9);
        // 列名
        final String enrollmentId = "EnrollmentId";
        final String childDisplayName = "ChildDisplayName";
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(ID);
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(FIRST_NAME);
        Mockito.when(resultSetMetaData.getColumnName(3)).thenReturn(LAST_NAME);
        Mockito.when(resultSetMetaData.getColumnName(4)).thenReturn(DISPLAY_NAME);
        Mockito.when(resultSetMetaData.getColumnName(5)).thenReturn(EMAIL);
        Mockito.when(resultSetMetaData.getColumnName(6)).thenReturn(PHONE_NUMBER);
        Mockito.when(resultSetMetaData.getColumnName(7)).thenReturn(ROLE);
        Mockito.when(resultSetMetaData.getColumnName(8)).thenReturn(enrollmentId);
        Mockito.when(resultSetMetaData.getColumnName(9)).thenReturn(childDisplayName);
        // 设置结果集
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);
        final String testId = "154d3ff0-86a2-4ff0-a512-e5045ba35f8c";
        final String testRole = "Admin";
        Mockito.when(resultSet.getString(ID)).thenReturn(testId);
        Mockito.when(resultSet.getString(ROLE)).thenReturn(testRole);

        // 调用测试方法
        RowMapper<com.learninggenie.common.data.model.UserEntity> userModelEntityRowMapper = UserMapper.USER_MODEL_ENTITY_ROW_MAPPER;

        // 验证结果
        com.learninggenie.common.data.model.UserEntity userEntity = userModelEntityRowMapper.mapRow(resultSet, 0);
        // 验证 ID
        Assert.assertEquals(userEntity.getId(), testId);
        Assert.assertEquals(userEntity.getRole(), testRole);
    }

    @Test
    public void testParentDtoMapper() throws Exception {
        // 列数
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(15);
        // 列名
        final String attendanceAvatar = "AttendanceAvatar";
        final String email = "Email";
        final String relationship = "Relationship";
        final String displayName = "DisplayName";
        final String role = "Role";
        final String id = "Id";
        final String authenticated = "Authenticated";
        final String certificatePicture = "CertificatePicture";
        final String signPicture = "SignPicture";
        final String isEmailIncorrent = "IsEmailIncorrent";
        final String state = "State";
        final String invitationId = "InvitationId";
        final String firstName = "FirstName";
        final String lastName = "LastName";
        final String childId = "ChildId";
        // 设置列名
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(attendanceAvatar);
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(email);
        Mockito.when(resultSetMetaData.getColumnName(3)).thenReturn(relationship);
        Mockito.when(resultSetMetaData.getColumnName(4)).thenReturn(displayName);
        Mockito.when(resultSetMetaData.getColumnName(5)).thenReturn(role);
        Mockito.when(resultSetMetaData.getColumnName(6)).thenReturn(id);
        Mockito.when(resultSetMetaData.getColumnName(7)).thenReturn(authenticated);
        Mockito.when(resultSetMetaData.getColumnName(8)).thenReturn(certificatePicture);
        Mockito.when(resultSetMetaData.getColumnName(9)).thenReturn(signPicture);
        Mockito.when(resultSetMetaData.getColumnName(10)).thenReturn(isEmailIncorrent);
        Mockito.when(resultSetMetaData.getColumnName(11)).thenReturn(state);
        Mockito.when(resultSetMetaData.getColumnName(12)).thenReturn(invitationId);
        Mockito.when(resultSetMetaData.getColumnName(13)).thenReturn(firstName);
        Mockito.when(resultSetMetaData.getColumnName(14)).thenReturn(lastName);
        Mockito.when(resultSetMetaData.getColumnName(15)).thenReturn(childId);
        // 设置结果集
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);
        final String testId = "dd2a7fa1-44c6-4278-803d-8ecb422ca210";
        final String testChildId = "e0243fb4-b3a3-4720-8251-cc5e38dc0d01";
        Mockito.when(resultSet.getString(id)).thenReturn(testId);
        Mockito.when(resultSet.getString(childId)).thenReturn(testChildId);

        // 调用测试方法
        RowMapper<ParentDto> checkInUserMapper = UserMapper.CHECK_IN_USER_MAPPER;

        // 验证结果
        ParentDto parentDto = checkInUserMapper.mapRow(resultSet, 0);
        // 验证 Id
        Assert.assertEquals(parentDto.getId(), testId);
        // 验证 childId
        Assert.assertEquals(parentDto.getChildId(), testChildId);
    }
}

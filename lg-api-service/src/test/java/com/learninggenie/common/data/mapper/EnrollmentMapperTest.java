package com.learninggenie.common.data.mapper;

import com.learninggenie.common.data.dto.EnrollmentDTO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;

/**
 * EnrollmentMapper 测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class EnrollmentMapperTest {
    private static final String ID = "Id";
    private static final String DISPALYNAME= "DisplayName";
    private static final String AVATARMEDIAID = "AvatarMediaId";
    private static final String USEREMAIL = "UserEmail";
    private static final String INVITATIONID = "InvitationId";
    private static final String ISCONFIRMED = "IsConfirmed";
    private static final String VERIFYNUM = "VerifyNum";
    private static final String USERDISPLAYNAME = "UserDisplayName";
    private static final String RELATIONSHIP = "RelationShip";
    private static final String RELATIVEPATH = "RelativePath";
    private static final String INITIALPWD = "InitialPwd";
    private static final String CREATEATUTC = "CreateAtUtc";
    private static final String ISEMAILINCORRENT = "IsEmailIncorrent";
    private static final String CLASSNAME = "className";
    private static final String ISDELETED = "IsDelete";
    private static final String RELATIONSHIPSTATE = "relationShipState";
    private static final String APPLYUSERID = "ApplyUserId";
    private static final String ISTRAINING= "IsTraining";
    private static final String CENTERID = "CenterId";
    private static final String GROUPID = "GroupId";
    private static final String STATE = "State";
    private static final String ISNEWBOUND = "IsNewBound";
    private static final String ENROLLMENTID= "EnrollmentId";

    @InjectMocks
    private EnrollmentMapper enrollmentMapper;

    @Mock
    private ResultSetMetaData resultSetMetaData;

    @Mock
    private ResultSet resultSet;

    /**
     * 测试基础 mapper.
     */
    @Test
    public void testBaseMapper() throws Exception {
        // 列数
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(22);
        // 列名
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(ENROLLMENTID);
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(DISPALYNAME);
        Mockito.when(resultSetMetaData.getColumnName(3)).thenReturn(AVATARMEDIAID);
        Mockito.when(resultSetMetaData.getColumnName(4)).thenReturn(USEREMAIL);
        Mockito.when(resultSetMetaData.getColumnName(5)).thenReturn(INVITATIONID);
        Mockito.when(resultSetMetaData.getColumnName(6)).thenReturn(ISCONFIRMED);
        Mockito.when(resultSetMetaData.getColumnName(7)).thenReturn(VERIFYNUM);
        Mockito.when(resultSetMetaData.getColumnName(8)).thenReturn(USERDISPLAYNAME);
        Mockito.when(resultSetMetaData.getColumnName(9)).thenReturn(RELATIONSHIP);
        Mockito.when(resultSetMetaData.getColumnName(10)).thenReturn(RELATIVEPATH);
        Mockito.when(resultSetMetaData.getColumnName(11)).thenReturn(INITIALPWD);
        Mockito.when(resultSetMetaData.getColumnName(12)).thenReturn(CREATEATUTC);
        Mockito.when(resultSetMetaData.getColumnName(13)).thenReturn(ISEMAILINCORRENT);
        Mockito.when(resultSetMetaData.getColumnName(14)).thenReturn(ISDELETED);
        Mockito.when(resultSetMetaData.getColumnName(15)).thenReturn(RELATIONSHIPSTATE);
        Mockito.when(resultSetMetaData.getColumnName(16)).thenReturn(CLASSNAME);
        Mockito.when(resultSetMetaData.getColumnName(17)).thenReturn(APPLYUSERID);
        Mockito.when(resultSetMetaData.getColumnName(18)).thenReturn(USEREMAIL);
        Mockito.when(resultSetMetaData.getColumnName(19)).thenReturn(GROUPID);
        Mockito.when(resultSetMetaData.getColumnName(20)).thenReturn(ID);
        Mockito.when(resultSetMetaData.getColumnName(21)).thenReturn(STATE);
        Mockito.when(resultSetMetaData.getColumnName(22)).thenReturn(ISNEWBOUND);
        // 设置结果集
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);
        final String testEnrollmentId = "a568ca85-36c2-4002-a250-3db4b7b7c162";
        final String testDisplayName = "testDisplayName";
        Mockito.when(resultSet.getString(ENROLLMENTID)).thenReturn(testEnrollmentId);
        Mockito.when(resultSet.getString(DISPALYNAME)).thenReturn(testDisplayName);

        // 调用测试方法
        RowMapper<EnrollmentDTO> baseMapper = EnrollmentMapper.BASE_MAPPER;

        // 验证结果
        EnrollmentDTO enrollmentDTO = baseMapper.mapRow(resultSet, 0);
        // 验证 EnrollmentId
        Assert.assertEquals(testEnrollmentId, enrollmentDTO.getEnrollmentId());
        // 验证 DisplayName
        Assert.assertEquals(testDisplayName, enrollmentDTO.getEnrollmentName());
    }

    /**
     * 测试 enrollment mapper
     */
    @Test
    public void testEnrollmentBaseMapper() throws Exception {
        // 列数
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(21);
        // 列名
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(ID);
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(DISPALYNAME);
        Mockito.when(resultSetMetaData.getColumnName(3)).thenReturn(AVATARMEDIAID);
        Mockito.when(resultSetMetaData.getColumnName(4)).thenReturn(USEREMAIL);
        Mockito.when(resultSetMetaData.getColumnName(5)).thenReturn(INVITATIONID);
        Mockito.when(resultSetMetaData.getColumnName(6)).thenReturn(ISCONFIRMED);
        Mockito.when(resultSetMetaData.getColumnName(7)).thenReturn(VERIFYNUM);
        Mockito.when(resultSetMetaData.getColumnName(8)).thenReturn(USERDISPLAYNAME);
        Mockito.when(resultSetMetaData.getColumnName(9)).thenReturn(RELATIONSHIP);
        Mockito.when(resultSetMetaData.getColumnName(10)).thenReturn(RELATIVEPATH);
        Mockito.when(resultSetMetaData.getColumnName(11)).thenReturn(INITIALPWD);
        Mockito.when(resultSetMetaData.getColumnName(12)).thenReturn(CREATEATUTC);
        Mockito.when(resultSetMetaData.getColumnName(13)).thenReturn(ISEMAILINCORRENT);
        Mockito.when(resultSetMetaData.getColumnName(14)).thenReturn(ISDELETED);
        Mockito.when(resultSetMetaData.getColumnName(15)).thenReturn(RELATIONSHIPSTATE);
        Mockito.when(resultSetMetaData.getColumnName(16)).thenReturn(CLASSNAME);
        Mockito.when(resultSetMetaData.getColumnName(17)).thenReturn(APPLYUSERID);
        Mockito.when(resultSetMetaData.getColumnName(18)).thenReturn(GROUPID);
        Mockito.when(resultSetMetaData.getColumnName(19)).thenReturn(STATE);
        Mockito.when(resultSetMetaData.getColumnName(20)).thenReturn(ISNEWBOUND);
        Mockito.when(resultSetMetaData.getColumnName(21)).thenReturn(ISTRAINING);
        // 设置结果集
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);
        final String testId = "a568ca85-36c2-4002-a250-3db4b7b7c162";
        final String testDisplayName = "testDisplayName";
        Mockito.when(resultSet.getString(ID)).thenReturn(testId);
        Mockito.when(resultSet.getString(DISPALYNAME)).thenReturn(testDisplayName);

        // 调用测试方法
        RowMapper<EnrollmentDTO> enrollmentBaseMapper = EnrollmentMapper.ENROLLMENT_BASE_MAPPER;

        // 验证结果
        EnrollmentDTO enrollmentDTO = enrollmentBaseMapper.mapRow(resultSet, 0);
        // 验证 Id
        Assert.assertEquals(testId, enrollmentDTO.getEnrollmentId());
        // 验证 DisplayName
        Assert.assertEquals(testDisplayName, enrollmentDTO.getEnrollmentName());
    }

    /**
     * 测试 base mapper sql
     */
    @Test
    public void testBaseMapperSql() throws Exception {
        // 列数
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(14);
        // 列名
        final String isTraining = "IsTraining";
        final String isInactive = "IsInactive";
        final String isDeleted = "IsDeleted";
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(ID);
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(DISPALYNAME);
        Mockito.when(resultSetMetaData.getColumnName(3)).thenReturn(AVATARMEDIAID);
        Mockito.when(resultSetMetaData.getColumnName(4)).thenReturn(USEREMAIL);
        Mockito.when(resultSetMetaData.getColumnName(5)).thenReturn(INVITATIONID);
        Mockito.when(resultSetMetaData.getColumnName(6)).thenReturn(ISCONFIRMED);
        Mockito.when(resultSetMetaData.getColumnName(7)).thenReturn(CREATEATUTC);
        Mockito.when(resultSetMetaData.getColumnName(8)).thenReturn(CLASSNAME);
        Mockito.when(resultSetMetaData.getColumnName(9)).thenReturn(CENTERID);
        Mockito.when(resultSetMetaData.getColumnName(10)).thenReturn(RELATIVEPATH);
        Mockito.when(resultSetMetaData.getColumnName(11)).thenReturn(GROUPID);
        Mockito.when(resultSetMetaData.getColumnName(12)).thenReturn(isTraining);
        Mockito.when(resultSetMetaData.getColumnName(13)).thenReturn(isInactive);
        Mockito.when(resultSetMetaData.getColumnName(14)).thenReturn(isDeleted);

        // 设置结果集
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);
        final String testId = "a568ca85-36c2-4002-a250-3db4b7b7c162";
        final String testDisplayName = "testDisplayName";
        Mockito.when(resultSet.getString(ID)).thenReturn(testId);
        Mockito.when(resultSet.getString(DISPALYNAME)).thenReturn(testDisplayName);

        // 调用测试方法
        RowMapper<EnrollmentDTO> enrollmentBaseMapper = EnrollmentMapper.BASE_MAPPER_SQL;

        // 验证结果
        EnrollmentDTO enrollmentDTO = enrollmentBaseMapper.mapRow(resultSet, 0);
        // 验证 Id
        Assert.assertEquals(testId, enrollmentDTO.getEnrollmentId());
        // 验证 DisplayName
        Assert.assertEquals(testDisplayName, enrollmentDTO.getEnrollmentName());
    }
}

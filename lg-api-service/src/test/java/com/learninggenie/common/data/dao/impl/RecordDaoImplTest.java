package com.learninggenie.common.data.dao.impl;

import com.learninggenie.common.data.mapper.RecordMapper;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.Date;

@RunWith(MockitoJUnitRunner.class)
public class RecordDaoImplTest {
    @InjectMocks
    private RecordDaoImpl recordDao;

    @Mock
    private JdbcTemplate jdbcTemplate;

    @Test
    public void testGetUploadTimeByUploadId() {
        String uploadId = "1";
        Date lastUploadTime = new Date();
        Mockito.when(jdbcTemplate.queryForObject(RecordMapper.SQL_GET_UPLOAD_TIME_BY_UPLOAD_ID, new Object[]{uploadId}, Date.class)).thenReturn(lastUploadTime);
        Date result = recordDao.getUploadTimeByUploadId("1");
        Assert.assertEquals(lastUploadTime.getTime(), result.getTime());
    }
}

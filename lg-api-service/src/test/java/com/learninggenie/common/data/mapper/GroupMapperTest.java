package com.learninggenie.common.data.mapper;


import com.learninggenie.common.data.dto.GroupWithCenter;
import com.learninggenie.common.data.dto.UnreadMessageUserModel;
import com.learninggenie.common.data.entity.GroupEntity;
import com.learninggenie.common.data.entity.GroupInvitationEntity;
import com.learninggenie.common.data.entity.GroupMetaDataEntity;
import com.learninggenie.common.data.entity.GroupPeriodEntity;
import com.learninggenie.common.data.entity.contents.UserGroupEntity;
import com.learninggenie.common.data.model.*;
import com.learninggenie.common.data.model.group.TeacherCenterGroupModel;
import com.learninggenie.common.data.model.monday.DailyActiveGroupModel;
import com.learninggenie.common.utils.TimeUtil;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.jdbc.core.RowMapper;

import java.sql.Date;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;

import static org.mockito.Mockito.mock;

/**
 * GroupMapperTest 测试
 */
@RunWith(MockitoJUnitRunner.class)
public class GroupMapperTest {

    private static final String ID = "Id";
    private static final String NAME = "Name";
    private static final String CREATE_AT_UTC = "CreateAtUtc";
    private static final String STAGE_ID = "StageId";
    private static final String DOMAIN_ID = "DomainId";
    private static final String DOMAIN_NAME = "DomainName";
    private static final String ABBREVIATION = "Abbreviation";
    private static final String MEASURE_NUMBER = "MeasureNumber";
    private static final String SORT_INDEX = "SortIndex";
    private static final String CHILD_ID = "ChildId";
    private static final String GROUP_ID = "GroupId";
    private static final String GROUP_NAME = "GroupName";
    private static final String FIRST_NAME = "FirstName";
    private static final String LAST_NAME = "LastName";
    private static final String DISPLAY_NAME = "DisplayName";
    private static final String CENTER_ID = "CenterId";
    private static final String CENTER_NAME = "CenterName";
    private static final String ENROLLMENT_DATE = "EnrollmentDate";
    private static final String WITHDRAWN_DATE = "WithdrawnDate";
    private static final String BIRTH_DATE = "BirthDate";
    private static final String GENDER = "Gender";
    private static final String CENTERTRAINING = "CenterTraining";

    @InjectMocks
    private GroupMapper groupMapper;

    @Mock
    private ResultSet resultSet;

    /**
     * 测试 mapper.
     */
    @Test
    public void testMapperGetGroupPeriods() throws SQLException {
        // 列数
        ResultSetMetaData resultSetMetaData = mock(ResultSetMetaData.class);
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(7);

        // 列名
        final String groupId = "GroupId";
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(groupId);
        final String periodId = "Id";
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(periodId);
        final String periodGroupId = "PeriodGroupId";
        Mockito.when(resultSetMetaData.getColumnName(3)).thenReturn(periodGroupId);
        final String fromAtLocal = "fromAtLocal";
        Mockito.when(resultSetMetaData.getColumnName(4)).thenReturn(fromAtLocal);
        final String toAtLocal = "toAtLocal";
        Mockito.when(resultSetMetaData.getColumnName(5)).thenReturn(toAtLocal);
        final String alias = "Alias";
        Mockito.when(resultSetMetaData.getColumnName(6)).thenReturn(alias);
        final String active = "Active";
        Mockito.when(resultSetMetaData.getColumnName(7)).thenReturn(active);

        // 设置结果集元数据
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);

        final String testGroupId = "123456";
        final String testPeriodId = "7890";
        final String testPeriodGroupId = "0987";
        final Date testFromAtLocal = new Date(TimeUtil.getUtcNow().getTime());
        final Date testToAtLocal = new Date(TimeUtil.getUtcNow().getTime());
        final String testAlias = "Test Alias";
        final boolean testActive = true;

        // 映射列
        Mockito.when(resultSet.getString(groupId)).thenReturn(testGroupId);
        Mockito.when(resultSet.getString(periodId)).thenReturn(testPeriodId);
        Mockito.when(resultSet.getString(periodGroupId)).thenReturn(testPeriodGroupId);
        Mockito.when(resultSet.getDate(fromAtLocal)).thenReturn(testFromAtLocal);
        Mockito.when(resultSet.getDate(toAtLocal)).thenReturn(testToAtLocal);
        Mockito.when(resultSet.getString(alias)).thenReturn(testAlias);
        Mockito.when(resultSet.getBoolean(active)).thenReturn(testActive);

        // 执行
        final GroupPeriodModel groupPeriodModel = GroupMapper.MAPPER_GET_GROUP_PERIODS.mapRow(resultSet, 1);

        // 验证
        Assert.assertEquals(testGroupId, groupPeriodModel.getGroupId());
        Assert.assertEquals(testPeriodId, groupPeriodModel.getPeriodId());
        Assert.assertEquals(testPeriodGroupId, groupPeriodModel.getPeriodGroupId());
        Assert.assertEquals(testFromAtLocal, groupPeriodModel.getFromAtLocal());
        Assert.assertEquals(testToAtLocal, groupPeriodModel.getToAtLocal());
        Assert.assertEquals(testAlias, groupPeriodModel.getAlias());
        Assert.assertEquals(testActive, groupPeriodModel.getActive());
    }

    @Test
    public void testGetGroupsMetadata() throws SQLException {
        // 列数
        // 列名
        final String id = "Id";
        final String metaKey = "MetaKey";
        final String metaValue = "MetaValue";
        final String groupId = "GroupId";

        // 设置结果集元数据

        final String testId = "123456";
        final String testMetaKey = "TestKey";
        final String testMetaValue = "TestValue";
        final String testGroupId = "7890";

        // 映射列
        Mockito.when(resultSet.getString(id)).thenReturn(testId);
        Mockito.when(resultSet.getString(metaKey)).thenReturn(testMetaKey);
        Mockito.when(resultSet.getString(metaValue)).thenReturn(testMetaValue);
        Mockito.when(resultSet.getString(groupId)).thenReturn(testGroupId);

        // 执行
        final GroupMetaDataEntity entity = GroupMapper.GET_GROUPS_METADATA.mapRow(resultSet, 1);

        // 验证
        Assert.assertEquals(testId, entity.getId());
        Assert.assertEquals(testMetaKey, entity.getMetaKey());
        Assert.assertEquals(testMetaValue, entity.getMetaValue());
        Assert.assertEquals(testGroupId, entity.getGroup().getId());
    }

    @Test
    public void testMapperCenterGroupModel() throws SQLException {
        // 列数
        ResultSetMetaData resultSetMetaData = mock(ResultSetMetaData.class);
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(7);

        // 列名
        final String stageId = "stageId";
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(stageId);
        final String centerId = "centerId";
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(centerId);
        final String centerName = "centerName";
        Mockito.when(resultSetMetaData.getColumnName(3)).thenReturn(centerName);
        final String groupId = "groupId";
        Mockito.when(resultSetMetaData.getColumnName(4)).thenReturn(groupId);
        final String groupName = "groupName";
        Mockito.when(resultSetMetaData.getColumnName(5)).thenReturn(groupName);
        final String isDeleted = "IsDeleted";
        Mockito.when(resultSetMetaData.getColumnName(6)).thenReturn(isDeleted);
        final String groupIsInactive = "GroupIsInactive";
        Mockito.when(resultSetMetaData.getColumnName(7)).thenReturn(groupIsInactive);
        final String stageId2 = "StageId";

        // 设置结果集元数据
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);

        final String stageIdValue = "stageIdValue";
        final String centerIdValue = "centerIdValue";
        final String centerNameValue = "centerNameValue";
        final String groupIdValue = "groupIdValue";
        final boolean isDeletedValue = false;
        final boolean groupIsInactiveValue = true;
        final String stageId2Value = "stageId2Value";

        // 映射列
        Mockito.when(resultSet.getString(stageId)).thenReturn(stageIdValue);
        Mockito.when(resultSet.getString(centerId)).thenReturn(centerIdValue);
        Mockito.when(resultSet.getString(centerName)).thenReturn(centerNameValue);
        Mockito.when(resultSet.getString(groupId)).thenReturn(groupIdValue);
        Mockito.when(resultSet.getBoolean(isDeleted)).thenReturn(isDeletedValue);
        Mockito.when(resultSet.getBoolean(groupIsInactive)).thenReturn(groupIsInactiveValue);
        Mockito.when(resultSet.getString(stageId2)).thenReturn(stageId2Value);

        // 执行
        final com.learninggenie.common.data.model.GroupEntity model = GroupMapper.MAPPER_CENTER_GROUP_MODEL.mapRow(resultSet, 1);

        // 验证
        Assert.assertEquals(stageId2Value, model.getStage().getId());
        Assert.assertEquals(centerIdValue, model.getCenterId());
        Assert.assertEquals(centerNameValue, model.getCenterName());
        Assert.assertEquals(groupIdValue, model.getId());
        Assert.assertEquals(centerNameValue, model.getName());
        Assert.assertEquals(isDeletedValue, model.isDeleted());
        Assert.assertEquals(groupIsInactiveValue, model.isInactive());
        Assert.assertEquals(stageId2Value, model.getStage().getId());
    }

    /**
     * 测试日活跃 mapper
     */
    @Test
    public void testMapperDayActive() throws Exception {
        // 列数
        ResultSetMetaData resultSetMetaData = mock(ResultSetMetaData.class);
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(2);
        // 列名
        final String date = "Date";
        final String activeGroupId = "ActiveGroupId";
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(date);
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(activeGroupId);
        // 设置结果集元数据
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);
        // 模拟结果集
        final Date testDate = new Date(TimeUtil.getUtcNow().getTime());
        final String testActiveGroupId = "a8b2fcde-88fe-45ca-b732-ccd7079bf30a";
        Mockito.when(resultSet.getDate(date)).thenReturn(testDate);
        Mockito.when(resultSet.getString(activeGroupId)).thenReturn(testActiveGroupId);

        // 调用测试方法
        RowMapper<DailyActiveGroupModel> mapperDayActive = GroupMapper.MAPPER_DAY_ACTIVE;

        // 验证结果
        DailyActiveGroupModel dailyActiveGroupModel = mapperDayActive.mapRow(resultSet, 0);
        // 验证 date
        Assert.assertEquals(testDate, dailyActiveGroupModel.getDate());
        // 验证 activeGroupId
        Assert.assertEquals(testActiveGroupId, dailyActiveGroupModel.getActiveGroupId());
    }

    /**
     * 测试 mapper group period
     */
    @Test
    public void testMapperGroupPeriod() throws Exception {
        // 列数
        ResultSetMetaData resultSetMetaData = mock(ResultSetMetaData.class);
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(8);
        // 列名
        final String id = "Id";
        final String updateAtUtc = "UpdateAtUtc";
        final String toAtLocal = "ToAtLocal";
        final String fromAtLocal = "FromAtLocal";
        final String createAtUtc = "CreateAtUtc";
        final String alias = "Alias";
        final String groupId = "GroupId";
        final String activePeriodId = "ActivePeriodId";
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(id);
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(updateAtUtc);
        Mockito.when(resultSetMetaData.getColumnName(3)).thenReturn(toAtLocal);
        Mockito.when(resultSetMetaData.getColumnName(4)).thenReturn(fromAtLocal);
        Mockito.when(resultSetMetaData.getColumnName(5)).thenReturn(createAtUtc);
        Mockito.when(resultSetMetaData.getColumnName(6)).thenReturn(alias);
        Mockito.when(resultSetMetaData.getColumnName(7)).thenReturn(groupId);
        Mockito.when(resultSetMetaData.getColumnName(8)).thenReturn(activePeriodId);
        // 设置结果集元数据
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);
        final String testId = "8cd30788-c78a-4674-8359-fa27537fc69b";
        // 模拟结果集
        Mockito.when(resultSet.getString(id)).thenReturn(testId);
        Mockito.when(resultSet.getString(activePeriodId)).thenReturn(testId);

        // 调用测试方法
        RowMapper<GroupPeriodEntity> mapperGroupPeriod = GroupMapper.MAPPER_GROUP_PERIOD;

        // 验证结果
        GroupPeriodEntity groupPeriodEntity = mapperGroupPeriod.mapRow(resultSet, 0);
        // 验证 id
        Assert.assertEquals(testId, groupPeriodEntity.getId());
        // 验证 activePeriodId
        Assert.assertTrue(groupPeriodEntity.getIsActive());
    }

    /**
     * 测试 mapper group entity
     */
    @Test
    public void testGroupEntityRowMapper() throws Exception {
        // 列数
        ResultSetMetaData resultSetMetaData = mock(ResultSetMetaData.class);
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(21);
        // 列名
        final String iconPath = "IconPath";
        final String isInactive = "IsInactive";
        final String isTraining = "IsTraining";
        final String centerTimeZone = "CenterTimeZone";
        final String periodGroupId = "PeriodGroupId";
        final String metaValue = "MetaValue";
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(ID);
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(NAME);
        Mockito.when(resultSetMetaData.getColumnName(3)).thenReturn(iconPath);
        Mockito.when(resultSetMetaData.getColumnName(4)).thenReturn(CREATE_AT_UTC);
        Mockito.when(resultSetMetaData.getColumnName(5)).thenReturn(isInactive);
        Mockito.when(resultSetMetaData.getColumnName(6)).thenReturn(isTraining);
        Mockito.when(resultSetMetaData.getColumnName(7)).thenReturn(DOMAIN_ID);
        Mockito.when(resultSetMetaData.getColumnName(8)).thenReturn(DOMAIN_NAME);
        Mockito.when(resultSetMetaData.getColumnName(9)).thenReturn(ABBREVIATION);
        Mockito.when(resultSetMetaData.getColumnName(10)).thenReturn(MEASURE_NUMBER);
        Mockito.when(resultSetMetaData.getColumnName(11)).thenReturn(SORT_INDEX);
        Mockito.when(resultSetMetaData.getColumnName(12)).thenReturn(CHILD_ID);
        Mockito.when(resultSetMetaData.getColumnName(13)).thenReturn(FIRST_NAME);
        Mockito.when(resultSetMetaData.getColumnName(14)).thenReturn(LAST_NAME);
        Mockito.when(resultSetMetaData.getColumnName(15)).thenReturn(DISPLAY_NAME);
        Mockito.when(resultSetMetaData.getColumnName(16)).thenReturn(CENTER_NAME);
        Mockito.when(resultSetMetaData.getColumnName(17)).thenReturn(CENTER_ID);
        Mockito.when(resultSetMetaData.getColumnName(18)).thenReturn(centerTimeZone);
        Mockito.when(resultSetMetaData.getColumnName(19)).thenReturn(STAGE_ID);
        Mockito.when(resultSetMetaData.getColumnName(20)).thenReturn(periodGroupId);
        Mockito.when(resultSetMetaData.getColumnName(21)).thenReturn(metaValue);
        // 设置结果集元数据
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);
        final String testId = "a568ca85-36c2-4002-a250-3db4b7b7c162";
        final String testName = "testName";
        final String testDomainId = "4f8391ab-c695-41e0-9cad-45ec17479630";
        final String testChildId = "316b2771-3a1c-43a2-a851-7a57a2e00773";
        final String testCenterName = "Tset Center";
        // 模拟结果集
        Mockito.when(resultSet.getString(ID)).thenReturn(testId);
        Mockito.when(resultSet.getString(NAME)).thenReturn(testName);
        Mockito.when(resultSet.getString(DOMAIN_ID)).thenReturn(testDomainId);
        Mockito.when(resultSet.getString(CHILD_ID)).thenReturn(testChildId);
        Mockito.when(resultSet.getString(CENTER_NAME)).thenReturn(testCenterName);

        // 调用测试方法
        RowMapper<GroupEntity> groupEntityRowMapper = GroupMapper.GROUP_ENTITY_ROW_MAPPER;

        // 验证结果
        GroupEntity groupEntity = groupEntityRowMapper.mapRow(resultSet, 0);
        // 验证 id
        Assert.assertEquals(testId, groupEntity.getId());
        // 验证 name
        Assert.assertEquals(testName, groupEntity.getName());
    }

    /**
     * 测试 teacher center group mapper
     */
    @Test
    public void testTeacherCenterGroupMapper() throws Exception {
        // 列数
        ResultSetMetaData resultSetMetaData = mock(ResultSetMetaData.class);
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(5);
        // 列名
        final String teacherId = "TeacherId";
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(GROUP_ID);
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(GROUP_NAME);
        Mockito.when(resultSetMetaData.getColumnName(3)).thenReturn(CENTER_ID);
        Mockito.when(resultSetMetaData.getColumnName(4)).thenReturn(CENTER_NAME);
        Mockito.when(resultSetMetaData.getColumnName(5)).thenReturn(teacherId);
        // 设置结果集元数据
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);
        final String testGroupId = "a568ca85-36c2-4002-a250-3db4b7b7c162";
        final String testGroupName = "testGroupName";
        final String testCenterId = "4f8391ab-c695-41e0-9cad-45ec17479630";
        final String testCenterName = "Tset Center";
        final String testTeacherId = "316b2771-3a1c-43a2-a851-7a57a2e00773";
        // 模拟结果集
        Mockito.when(resultSet.getString(GROUP_ID)).thenReturn(testGroupId);
        Mockito.when(resultSet.getString(GROUP_NAME)).thenReturn(testGroupName);
        Mockito.when(resultSet.getString(CENTER_ID)).thenReturn(testCenterId);
        Mockito.when(resultSet.getString(CENTER_NAME)).thenReturn(testCenterName);
        Mockito.when(resultSet.getString(teacherId)).thenReturn(testTeacherId);

        // 调用测试方法
        RowMapper<TeacherCenterGroupModel> teacherCenterGroupModelRowMapper = GroupMapper.TEACHER_CENTER_GROUP_MODEL_ROW_MAPPER;

        // 验证结果
        TeacherCenterGroupModel teacherCenterGroupModel = teacherCenterGroupModelRowMapper.mapRow(resultSet, 0);
        // 验证 groupId
        Assert.assertEquals(testGroupId, teacherCenterGroupModel.getGroupId());
        // 验证 groupName
        Assert.assertEquals(testGroupName, teacherCenterGroupModel.getGroupName());
        // 验证 centerId
        Assert.assertEquals(testCenterId, teacherCenterGroupModel.getCenterId());
        // 验证 centerName
        Assert.assertEquals(testCenterName, teacherCenterGroupModel.getCenterName());
        // 验证 teacherId
        Assert.assertEquals(testTeacherId, teacherCenterGroupModel.getTeacherId());
    }

    /**
     * 测试 group entity row mapper2
     */
    @Test
    public void testGroupEntityRowMapper2() throws Exception {
        // 列数
        ResultSetMetaData resultSetMetaData = mock(ResultSetMetaData.class);
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(19);
        // 列名
        final String iconPath = "IconPath";
        final String isInactive = "IsInactive";
        final String centerTimeZone = "CenterTimeZone";
        final String periodGroupId = "PeriodGroupId";
        final String metaValue = "MetaValue";
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(ID);
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(NAME);
        Mockito.when(resultSetMetaData.getColumnName(3)).thenReturn(iconPath);
        Mockito.when(resultSetMetaData.getColumnName(4)).thenReturn(CREATE_AT_UTC);
        Mockito.when(resultSetMetaData.getColumnName(5)).thenReturn(isInactive);
        Mockito.when(resultSetMetaData.getColumnName(6)).thenReturn(DOMAIN_ID);
        Mockito.when(resultSetMetaData.getColumnName(7)).thenReturn(DOMAIN_NAME);
        Mockito.when(resultSetMetaData.getColumnName(8)).thenReturn(ABBREVIATION);
        Mockito.when(resultSetMetaData.getColumnName(9)).thenReturn(MEASURE_NUMBER);
        Mockito.when(resultSetMetaData.getColumnName(10)).thenReturn(SORT_INDEX);
        Mockito.when(resultSetMetaData.getColumnName(11)).thenReturn(CHILD_ID);
        Mockito.when(resultSetMetaData.getColumnName(12)).thenReturn(FIRST_NAME);
        Mockito.when(resultSetMetaData.getColumnName(13)).thenReturn(LAST_NAME);
        Mockito.when(resultSetMetaData.getColumnName(14)).thenReturn(DISPLAY_NAME);
        Mockito.when(resultSetMetaData.getColumnName(15)).thenReturn(CENTER_ID);
        Mockito.when(resultSetMetaData.getColumnName(16)).thenReturn(centerTimeZone);
        Mockito.when(resultSetMetaData.getColumnName(17)).thenReturn(STAGE_ID);
        Mockito.when(resultSetMetaData.getColumnName(18)).thenReturn(periodGroupId);
        Mockito.when(resultSetMetaData.getColumnName(19)).thenReturn(metaValue);
        // 设置结果集元数据
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);
        final String testId = "a568ca85-36c2-4002-a250-3db4b7b7c162";
        final String testName = "testName";
        final String testDomainId = "4f8391ab-c695-41e0-9cad-45ec17479630";
        final String testChildId = "316b2771-3a1c-43a2-a851-7a57a2e00773";
        final String testCenterId = "49fcdb23-15dd-478c-b9aa-292a36cf49b5";
        final String testStageId = "afc497ae-3fde-4b2f-9e4e-a24c9dd9e008";
        Mockito.when(resultSet.getString(ID)).thenReturn(testId);
        Mockito.when(resultSet.getString(NAME)).thenReturn(testName);
        Mockito.when(resultSet.getString(DOMAIN_ID)).thenReturn(testDomainId);
        Mockito.when(resultSet.getString(CHILD_ID)).thenReturn(testChildId);
        Mockito.when(resultSet.getString(CENTER_ID)).thenReturn(testCenterId);
        Mockito.when(resultSet.getString(STAGE_ID)).thenReturn(testStageId);

        // 调用测试方法
        RowMapper<GroupEntity> groupEntityRowMapper = GroupMapper.GROUP_ENTITY_ROW_MAPPER2;

        // 验证结果
        GroupEntity groupEntity = groupEntityRowMapper.mapRow(resultSet, 0);
        // 验证 id
        Assert.assertEquals(testId, groupEntity.getId());
        // 验证 name
        Assert.assertEquals(testName, groupEntity.getName());
        // 验证 domainId
        Assert.assertEquals(testDomainId, groupEntity.getDomainId());
    }


    /**
     * 测试 ChildWithGroupModel mapper
     */
    @Test
    public void testChildWithGroupMapper() throws Exception {
        // 列数
        ResultSetMetaData resultSetMetaData = mock(ResultSetMetaData.class);
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(16);
        // 列名
        final String childFrameworkId = "ChildFrameworkId";
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(CHILD_ID);
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(GROUP_ID);
        Mockito.when(resultSetMetaData.getColumnName(3)).thenReturn(GROUP_NAME);
        Mockito.when(resultSetMetaData.getColumnName(4)).thenReturn(FIRST_NAME);
        Mockito.when(resultSetMetaData.getColumnName(5)).thenReturn(LAST_NAME);
        Mockito.when(resultSetMetaData.getColumnName(6)).thenReturn(DISPLAY_NAME);
        Mockito.when(resultSetMetaData.getColumnName(7)).thenReturn(CENTER_ID);
        Mockito.when(resultSetMetaData.getColumnName(8)).thenReturn(CENTER_NAME);
        Mockito.when(resultSetMetaData.getColumnName(9)).thenReturn(DOMAIN_ID);
        Mockito.when(resultSetMetaData.getColumnName(10)).thenReturn(DOMAIN_NAME);
        Mockito.when(resultSetMetaData.getColumnName(11)).thenReturn(ENROLLMENT_DATE);
        Mockito.when(resultSetMetaData.getColumnName(12)).thenReturn(WITHDRAWN_DATE);
        Mockito.when(resultSetMetaData.getColumnName(13)).thenReturn(BIRTH_DATE);
        Mockito.when(resultSetMetaData.getColumnName(14)).thenReturn(GENDER);
        Mockito.when(resultSetMetaData.getColumnName(15)).thenReturn(CENTERTRAINING);
        Mockito.when(resultSetMetaData.getColumnName(16)).thenReturn(childFrameworkId);
        // 设置结果集元数据
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);
        // 模拟结果集
        final String testChildId = "a568ca85-36c2-4002-a250-3db4b7b7c162";
        final String testGroupId = "a568ca85-36c2-4002-a250-3db4b7b7c162";
        final String testGroupName = "testGroupName";
        final String testFirstName = "testFirstName";
        Mockito.when(resultSet.getString(CHILD_ID)).thenReturn(testChildId);
        Mockito.when(resultSet.getString(GROUP_ID)).thenReturn(testGroupId);
        Mockito.when(resultSet.getString(GROUP_NAME)).thenReturn(testGroupName);
        Mockito.when(resultSet.getString(FIRST_NAME)).thenReturn(testFirstName);

        // 调用测试方法
        RowMapper<ChildWithGroupModel> childWithGroupMapper = GroupMapper.CHILD_WITH_GROUP_MAPPER;

        // 验证结果
        ChildWithGroupModel childWithGroupModel = childWithGroupMapper.mapRow(resultSet, 0);
        // 验证 childId
        Assert.assertEquals(testChildId, childWithGroupModel.getChildId());
        // 验证 groupId
        Assert.assertEquals(testGroupId, childWithGroupModel.getGroupId());
        // 验证 groupName
        Assert.assertEquals(testGroupName, childWithGroupModel.getGroupName());
        // 验证 firstName
        Assert.assertEquals(testFirstName, childWithGroupModel.getFirstName());
    }

    /**
     * 测试 mapper group with childCount
     */
    @Test
    public void testMapperGroupWithChildCount() throws Exception {
        // 列数
        ResultSetMetaData resultSetMetaData = mock(ResultSetMetaData.class);
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(5);
        // 列名
        final String childCount = "ChildCount";
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(GROUP_ID);
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(GROUP_NAME);
        Mockito.when(resultSetMetaData.getColumnName(3)).thenReturn(DOMAIN_ID);
        Mockito.when(resultSetMetaData.getColumnName(4)).thenReturn(DOMAIN_NAME);
        Mockito.when(resultSetMetaData.getColumnName(5)).thenReturn(childCount);
        // 设置结果集元数据
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);
        // 模拟结果集
        final String testGroupId = "a568ca85-36c2-4002-a250-3db4b7b7c162";
        final String testGroupName = "testGroupName";
        final String testDomainId = "4f8391ab-c695-41e0-9cad-45ec17479630";
        final String testDomainName = "Test Domain";
        final int testChildCount = 10;
        Mockito.when(resultSet.getString(GROUP_ID)).thenReturn(testGroupId);
        Mockito.when(resultSet.getString(GROUP_NAME)).thenReturn(testGroupName);
        Mockito.when(resultSet.getString(DOMAIN_ID)).thenReturn(testDomainId);
        Mockito.when(resultSet.getString(DOMAIN_NAME)).thenReturn(testDomainName);
        Mockito.when(resultSet.getInt(childCount)).thenReturn(testChildCount);

        // 调用测试方法
        RowMapper<com.learninggenie.common.data.model.GroupEntity> groupEntityRowMapper = GroupMapper.MAPPER_GROUP_WITH_CHILD_COUNT;

        // 验证结果
        com.learninggenie.common.data.model.GroupEntity groupEntity = groupEntityRowMapper.mapRow(resultSet, 0);
        // 验证 groupId
        Assert.assertEquals(testGroupId, groupEntity.getId());
        // 验证 groupName
        Assert.assertEquals(testGroupName, groupEntity.getName());
        // 验证 domainId
        Assert.assertEquals(testDomainId, groupEntity.getDomain().getId());
        // 验证 domainName
        Assert.assertEquals(testDomainName, groupEntity.getDomain().getName());
        // 验证 childCount
        Assert.assertEquals(testChildCount, groupEntity.getChildCount());
    }

    /**
     * 测试 groupInvitation mapper
     */
    @Test
    public void testMapperGroupInvitation() throws Exception {
        // 列数
        ResultSetMetaData resultSetMetaData = mock(ResultSetMetaData.class);
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(6);
        // 列名
        final String id = "Id";
        final String userDisplayName = "UserDisplayName";
        final String createUserId = "CreateUserId";
        final String applyUserId = "ApplyUserId";
        final String userEmail = "UserEmail";
        final String token = "Token";
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(id);
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(userDisplayName);
        Mockito.when(resultSetMetaData.getColumnName(3)).thenReturn(createUserId);
        Mockito.when(resultSetMetaData.getColumnName(4)).thenReturn(applyUserId);
        Mockito.when(resultSetMetaData.getColumnName(5)).thenReturn(userEmail);
        Mockito.when(resultSetMetaData.getColumnName(6)).thenReturn(token);
        // 设置结果集元数据
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);
        // 模拟结果集
        final String testId = "a568ca85-36c2-4002-a250-3db4b7b7c162";
        final String testUserDisplayName = "testUserDisplayName";
        final String testCreateUserId = "4f8391ab-c695-41e0-9cad-45ec17479630";
        final String testApplyUserId = "4f8391ab-c695-41e0-9cad-45ec17479630";
        final String testUserEmail = "user@testlearninggeniecom";
        Mockito.when(resultSet.getString(id)).thenReturn(testId);
        Mockito.when(resultSet.getString(userDisplayName)).thenReturn(testUserDisplayName);
        Mockito.when(resultSet.getString(createUserId)).thenReturn(testCreateUserId);
        Mockito.when(resultSet.getString(applyUserId)).thenReturn(testApplyUserId);
        Mockito.when(resultSet.getString(userEmail)).thenReturn(testUserEmail);

        // 调用测试方法
        RowMapper<GroupInvitationEntity> mapperGroupInvitation = GroupMapper.MAPPER_GROUP_INVITATION;

        // 验证结果
        GroupInvitationEntity mappedRow = mapperGroupInvitation.mapRow(resultSet, 0);
        // 验证 id
        Assert.assertEquals(testId, mappedRow.getId());
        // 验证 userDisplayName
        Assert.assertEquals(testUserDisplayName, mappedRow.getDisplayName());
        // 验证 createUserId
        Assert.assertEquals(testCreateUserId, mappedRow.getCreateUser().getId());
        // 验证 applyUserId
        Assert.assertEquals(testApplyUserId, mappedRow.getApplyUser().getId());
    }

    /**
     * 测试 group with center mapper
     */
    @Test
    public void testMapperGroupWithCenter() throws Exception {
        // 列数
        ResultSetMetaData resultSetMetaData = mock(ResultSetMetaData.class);
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(4);
        // 列名
        final String groupId = "GroupId";
        final String groupName = "GroupName";
        final String centerId = "CenterId";
        final String centerName = "CenterName";
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(groupId);
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(groupName);
        Mockito.when(resultSetMetaData.getColumnName(3)).thenReturn(centerId);
        Mockito.when(resultSetMetaData.getColumnName(4)).thenReturn(centerName);
        // 设置结果集元数据
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);
        // 模拟结果集
        final String testGroupId = "a568ca85-36c2-4002-a250-3db4b7b7c162";
        final String testGroupName = "testGroupName";
        final String testCenterId = "4f8391ab-c695-41e0-9cad-45ec17479630";
        final String testCenterName = "Test Center";
        Mockito.when(resultSet.getString(groupId)).thenReturn(testGroupId);
        Mockito.when(resultSet.getString(groupName)).thenReturn(testGroupName);
        Mockito.when(resultSet.getString(centerId)).thenReturn(testCenterId);
        Mockito.when(resultSet.getString(centerName)).thenReturn(testCenterName);

        // 调用测试方法
        RowMapper<GroupWithCenter> mapperGroupWithCenter = GroupMapper.MAPPER_GROUP_WITH_CENTER;

        // 验证结果
        GroupWithCenter groupWithCenter = mapperGroupWithCenter.mapRow(resultSet, 0);
        // 验证 groupId
        Assert.assertEquals(testGroupId, groupWithCenter.getGroupId());
        // 验证 groupName
        Assert.assertEquals(testGroupName, groupWithCenter.getGroupName());
        // 验证 centerId
        Assert.assertEquals(testCenterId, groupWithCenter.getCenterId());
        // 验证 centerName
        Assert.assertEquals(testCenterName, groupWithCenter.getCenterName());
    }

    /**
     * 测试 mapper group model entity
     */
    @Test
    public void testMapperGroupModelEntity() throws Exception {
        // 列数
        ResultSetMetaData resultSetMetaData = mock(ResultSetMetaData.class);
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(5);
        // 列名
        final String id = "Id";
        final String name = "Name";
        final String domainId = "DomainId";
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(id);
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(name);
        Mockito.when(resultSetMetaData.getColumnName(3)).thenReturn(domainId);
        // 设置结果集元数据
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);
        // 模拟结果集
        final String testId = "a568ca85-36c2-4002-a250-3db4b7b7c162";
        final String testName = "testGroupName";
        final String testDomainId = "4f8391ab-c695-41e0-9cad-45ec17479630";
        Mockito.when(resultSet.getString(id)).thenReturn(testId);
        Mockito.when(resultSet.getString(name)).thenReturn(testName);
        Mockito.when(resultSet.getString(domainId)).thenReturn(testDomainId);

        // 调用测试方法
        RowMapper<com.learninggenie.common.data.model.GroupEntity> mapperGroupModelEntity = GroupMapper.MAPPER_GROUP_MODEL_ENTITY;

        // 验证结果
        com.learninggenie.common.data.model.GroupEntity groupEntity = mapperGroupModelEntity.mapRow(resultSet, 0);
        // 验证 id
        Assert.assertEquals(testId, groupEntity.getId());
        // 验证 name
        Assert.assertEquals(testName, groupEntity.getName());
        // 验证 domainId
        Assert.assertEquals(testDomainId, groupEntity.getDomain().getId());
    }

    /**
     * 测试 mapper group model entity
     */
    @Test
    public void testMapperGroupModelEntity2() throws Exception {
        // 列数
        ResultSetMetaData resultSetMetaData = mock(ResultSetMetaData.class);
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(6);
        // 列名
        final String id = "Id";
        final String name = "Name";
        final String domainId = "DomainId";
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(id);
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(name);
        Mockito.when(resultSetMetaData.getColumnName(3)).thenReturn(domainId);
        // 设置结果集元数据
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);
        // 模拟结果集
        final String testId = "a568ca85-36c2-4002-a250-3db4b7b7c162";
        final String testName = "testGroupName";
        final String testDomainId = "4f8391ab-c695-41e0-9cad-45ec17479630";
        Mockito.when(resultSet.getString(id)).thenReturn(testId);
        Mockito.when(resultSet.getString(name)).thenReturn(testName);
        Mockito.when(resultSet.getString(domainId)).thenReturn(testDomainId);

        // 调用测试方法
        RowMapper<com.learninggenie.common.data.model.GroupEntity> mapperGroupModelEntity = GroupMapper.MAPPER_GROUP_ENTITY;

        // 验证结果
        com.learninggenie.common.data.model.GroupEntity groupEntity = mapperGroupModelEntity.mapRow(resultSet, 0);
        // 验证 id
        Assert.assertEquals(testId, groupEntity.getId());
        // 验证 name
        Assert.assertEquals(testName, groupEntity.getName());
        // 验证 domainId
        Assert.assertEquals(testDomainId, groupEntity.getDomain().getId());
    }

    /**
     * 测试 mapper center group user
     */
    @Test
    public void testMapperCenterGroupUser() throws Exception {
        // 列数
        ResultSetMetaData resultSetMetaData = mock(ResultSetMetaData.class);
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(6);
        // 列名
        final String userId = "UserId";
        final String displayName = "DisplayName";
        final String centerId = "centerId";
        final String centerName = "centerName";
        final String groupId = "groupId";
        final String groupName = "groupName";
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(userId);
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(displayName);
        Mockito.when(resultSetMetaData.getColumnName(3)).thenReturn(centerId);
        Mockito.when(resultSetMetaData.getColumnName(4)).thenReturn(centerName);
        Mockito.when(resultSetMetaData.getColumnName(5)).thenReturn(groupId);
        Mockito.when(resultSetMetaData.getColumnName(6)).thenReturn(groupName);
        // 设置结果集元数据
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);
        // 模拟结果集
        final String testUserId = "a568ca85-36c2-4002-a250-3db4b7b7c162";
        final String testDisplayName = "testDisplayName";
        final String testCenterId = "4f8391ab-c695-41e0-9cad-45ec17479630";
        final String testCenterName = "Test Center";
        final String testGroupId = "4f8391ab-c695-41e0-9cad-45ec17479630";
        final String testGroupName = "Test Group";
        Mockito.when(resultSet.getString(userId)).thenReturn(testUserId);
        Mockito.when(resultSet.getString(displayName)).thenReturn(testDisplayName);
        Mockito.when(resultSet.getString(centerId)).thenReturn(testCenterId);
        Mockito.when(resultSet.getString(centerName)).thenReturn(testCenterName);
        Mockito.when(resultSet.getString(groupId)).thenReturn(testGroupId);
        Mockito.when(resultSet.getString(groupName)).thenReturn(testGroupName);

        // 调用测试方法
        RowMapper<CenterGroupUserModel> mapperCenterGroupUser = GroupMapper.MAPPER_CENTER_GROUP_USER;

        // 验证结果
        CenterGroupUserModel centerGroupUserModel = mapperCenterGroupUser.mapRow(resultSet, 0);
        // 验证 userId
        Assert.assertEquals(testUserId, centerGroupUserModel.getUserId());
        // 验证 displayName
        Assert.assertEquals(testDisplayName, centerGroupUserModel.getDisplayName());
        // 验证 centerId
        Assert.assertEquals(testCenterId, centerGroupUserModel.getCenterId());
        // 验证 centerName
        Assert.assertEquals(testCenterName, centerGroupUserModel.getCenterName());
        // 验证 groupId
        Assert.assertEquals(testGroupId, centerGroupUserModel.getGroupId());
        // 验证 groupName
        Assert.assertEquals(testGroupName, centerGroupUserModel.getGroupName());
    }

    /**
     * 测试 mapper get teachers with unread messages
     */
    @Test
    public void testMapperGetTeachersWithUnreadMessages() throws Exception {
        // 列数
        ResultSetMetaData resultSetMetaData = mock(ResultSetMetaData.class);
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(5);
        // 列名
        final String teacherId = "teacherId";
        final String password = "password";
        final String imUserId = "imUserId";
        final String email = "email";
        final String centerTimeZone = "centerTimeZone";
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(teacherId);
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(password);
        Mockito.when(resultSetMetaData.getColumnName(3)).thenReturn(imUserId);
        Mockito.when(resultSetMetaData.getColumnName(4)).thenReturn(email);
        Mockito.when(resultSetMetaData.getColumnName(5)).thenReturn(centerTimeZone);
        // 设置结果集元数据
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);
        // 模拟结果集
        final String testTeacherId = "a568ca85-36c2-4002-a250-3db4b7b7c162";
        final String testImUserId = "4f8391ab-c695-41e0-9cad-45ec17479630";
        Mockito.when(resultSet.getString(teacherId)).thenReturn(testTeacherId);
        Mockito.when(resultSet.getString(imUserId)).thenReturn(testImUserId);

        // 调用测试方法
        RowMapper<UnreadMessageUserModel> mapperGetTeachersWithUnreadMessageInfo = GroupMapper.MAPPER_GET_TEACHERS_WITH_UNREAD_MESSAGE_INFO;

        // 验证结果
        UnreadMessageUserModel unreadMessageUserModel = mapperGetTeachersWithUnreadMessageInfo.mapRow(resultSet, 0);
        // 验证 teacherId
        Assert.assertEquals(testTeacherId, unreadMessageUserModel.getTeacherId());
        // 验证 imUserId
        Assert.assertEquals(testImUserId, unreadMessageUserModel.getImUserId());
    }

    /**
     * 测试 mapper user group
     */
    @Test
    public void testMapperUserGroup() throws Exception {
        // 列数
        ResultSetMetaData resultSetMetaData = mock(ResultSetMetaData.class);
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(2);
        // 列名
        final String userId = "UserId";
        final String groupId = "GroupId";
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(userId);
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(groupId);
        // 设置结果集元数据
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);
        // 模拟结果集
        final String testUserId = "a568ca85-36c2-4002-a250-3db4b7b7c162";
        final String testGroupId = "4f8391ab-c695-41e0-9cad-45ec17479630";
        Mockito.when(resultSet.getString(userId)).thenReturn(testUserId);
        Mockito.when(resultSet.getString(groupId)).thenReturn(testGroupId);

        // 调用测试方法
        RowMapper<UserGroupEntity> mapperUserGroup = GroupMapper.MAPPER_USER_GROUP;

        // 验证结果
        UserGroupEntity userGroupEntity = mapperUserGroup.mapRow(resultSet, 0);
        // 验证 userId
        Assert.assertEquals(testUserId, userGroupEntity.getUserId());
        // 验证 groupId
        Assert.assertEquals(testGroupId, userGroupEntity.getGroupId());
    }
}


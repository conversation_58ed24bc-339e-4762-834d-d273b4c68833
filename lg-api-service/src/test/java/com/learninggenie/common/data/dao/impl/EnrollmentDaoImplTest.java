package com.learninggenie.common.data.dao.impl;

import com.learninggenie.common.data.dto.ClassesTreeDataDto;
import com.learninggenie.common.data.dto.EnrollmentDTO;
import com.learninggenie.common.data.dto.ParentDto;
import com.learninggenie.common.data.mapper.CenterMapper;
import com.learninggenie.common.data.mapper.EnrollmentMapper;
import com.learninggenie.common.data.mapper.UserMapper;
import com.learninggenie.common.utils.GenerateCodeUtil;
import com.learninggenie.common.utils.StringUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.jdbc.core.JdbcTemplate;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.UUID;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * EnrollmentDaoImpl 的测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class EnrollmentDaoImplTest {

    @InjectMocks
    private EnrollmentDaoImpl enrollmentDao;

    @Mock
    private JdbcTemplate jdbcTemplate;

    /**
     * 测试根据班级 Id 获取邀请学生信息
     */
    @Test
    public void testGetInvitationsByGroupId() {
        // 准备入参
        String groupId = "349c97c4-979e-494c-a08d-bfa9d2aad1b2";  // 班级 Id
        // 模拟行为
        String enrollmentId = "f495b383-79e1-4347-9180-6ab3ea53f3f5"; // 小孩 Id
        String enrollmentName = "Jaxson Ramirez"; // 小孩名字
        String parentEmail = "<EMAIL>"; // 家长邮箱
        String invitationId = "f495"; // 家长邀请码 Id
        String sql = EnrollmentMapper.getInvitationsByGroupId;
        List<EnrollmentDTO> enrollmentDTOList = new ArrayList<>();
        EnrollmentDTO enrollmentDTO = new EnrollmentDTO();
        enrollmentDTO.setEnrollmentId(enrollmentId);
        enrollmentDTO.setEnrollmentName(enrollmentName);
        enrollmentDTO.setParentEmail(parentEmail);
        enrollmentDTO.setInvitationId(invitationId);
        enrollmentDTOList.add(enrollmentDTO);
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(EnrollmentMapper.ENROLLMENT_BASE_MAPPER))).thenReturn(enrollmentDTOList);

        // 调用方法
        List<EnrollmentDTO> result = enrollmentDao.getInvitationsByGroupId(groupId);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{groupId}, EnrollmentMapper.ENROLLMENT_BASE_MAPPER);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(result, enrollmentDTOList);
    }

    /**
     * 测试根据班级 Id 获取邀请码学生信息
     */
    @Test
    public void testGetCodeInvitationsByGroupId() {
        // 准备入参
        String groupId = "349c97c4-979e-494c-a08d-bfa9d2aad1b2";  // 班级 Id
        // 模拟行为
        String enrollmentId = "f495b383-79e1-4347-9180-6ab3ea53f3f5"; // 小孩 Id
        String enrollmentName = "Jaxson Ramirez"; // 小孩名字
        String parentEmail = "<EMAIL>"; // 家长邮箱
        String invitationId = "f495"; // 家长邀请码 Id
        String sql = EnrollmentMapper.getCodeInvitationByGroupId;
        List<EnrollmentDTO> enrollmentDTOList = new ArrayList<>();
        EnrollmentDTO enrollmentDTO = new EnrollmentDTO();
        enrollmentDTO.setEnrollmentId(enrollmentId);
        enrollmentDTO.setEnrollmentName(enrollmentName);
        enrollmentDTO.setParentEmail(parentEmail);
        enrollmentDTO.setInvitationId(invitationId);
        enrollmentDTOList.add(enrollmentDTO);
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(EnrollmentMapper.ENROLLMENT_BASE_MAPPER))).thenReturn(enrollmentDTOList);

        // 调用方法
        List<EnrollmentDTO> result = enrollmentDao.getCodeInvitationsByGroupId(groupId);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{groupId}, EnrollmentMapper.ENROLLMENT_BASE_MAPPER);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(result, enrollmentDTOList);
    }

    /**
     * 测试根据用户 Id 获取邀请学生信息
     */
    @Test
    public void testGetInvitationsByCreateUserId() {
        // 准备入参
        String createUserId = "349c97c4-979e-494c-a08d-bfa9d2aad1b2";  // 用户 Id
        // 模拟行为
        String enrollmentId = "f495b383-79e1-4347-9180-6ab3ea53f3f5"; // 小孩 Id
        String enrollmentName = "Jaxson Ramirez"; // 小孩名字
        String parentEmail = "<EMAIL>"; // 家长邮箱
        String invitationId = "f495"; // 家长邀请码 Id
        String sql = EnrollmentMapper.getInvitationsByCreateUserId;
        List<EnrollmentDTO> enrollmentDTOList = new ArrayList<>();
        EnrollmentDTO enrollmentDTO = new EnrollmentDTO();
        enrollmentDTO.setEnrollmentId(enrollmentId);
        enrollmentDTO.setEnrollmentName(enrollmentName);
        enrollmentDTO.setParentEmail(parentEmail);
        enrollmentDTO.setInvitationId(invitationId);
        enrollmentDTOList.add(enrollmentDTO);
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(EnrollmentMapper.BASE_MAPPER))).thenReturn(enrollmentDTOList);

        // 调用方法
        List<EnrollmentDTO> result = enrollmentDao.getInvitationsByCreateUserId(createUserId);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{createUserId}, EnrollmentMapper.BASE_MAPPER);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(result, enrollmentDTOList);
    }

    /**
     * 测试根据学生 Id 获取邀请学生信息
     */
    @Test
    public void testGetByEnrollmentId() {
        // 模拟行为
        String enrollmentId = "f495b383-79e1-4347-9180-6ab3ea53f3f5"; // 小孩 Id
        String enrollmentName = "Jaxson Ramirez"; // 小孩名字
        String parentEmail = "<EMAIL>"; // 家长邮箱
        String invitationId = "f495"; // 家长邀请码 Id
        String sql = EnrollmentMapper.getByEnrollmentIdSql;
        EnrollmentDTO enrollmentDTO = new EnrollmentDTO();
        enrollmentDTO.setEnrollmentId(enrollmentId);
        enrollmentDTO.setEnrollmentName(enrollmentName);
        enrollmentDTO.setParentEmail(parentEmail);
        enrollmentDTO.setInvitationId(invitationId);
        when(jdbcTemplate.queryForObject(anyString(), Mockito.any(Object[].class), Mockito.eq(EnrollmentMapper.BASE_MAPPER_SQL))).thenReturn(enrollmentDTO);

        // 调用方法
        EnrollmentDTO result = enrollmentDao.getByEnrollmentId(enrollmentId);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).queryForObject(sql, new String[]{enrollmentId}, EnrollmentMapper.BASE_MAPPER_SQL);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(result, enrollmentDTO);
    }

    /**
     * 测试根据邮箱获取邀请学生信息
     */
    @Test
    public void testGetByEmail() {
        // 准备入参
        String email = "<EMAIL>"; // 邮箱
        // 模拟行为
        String enrollmentId = "f495b383-79e1-4347-9180-6ab3ea53f3f5"; // 小孩 Id
        String enrollmentName = "Jaxson Ramirez"; // 小孩名字
        String parentEmail = "<EMAIL>"; // 家长邮箱
        String invitationId = "f495"; // 家长邀请码 Id
        String sql = EnrollmentMapper.getByEmailSql;
        List<EnrollmentDTO> enrollmentDTOList = new ArrayList<>();
        EnrollmentDTO enrollmentDTO = new EnrollmentDTO();
        enrollmentDTO.setEnrollmentId(enrollmentId);
        enrollmentDTO.setEnrollmentName(enrollmentName);
        enrollmentDTO.setParentEmail(parentEmail);
        enrollmentDTO.setInvitationId(invitationId);
        enrollmentDTOList.add(enrollmentDTO);
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(EnrollmentMapper.BASE_MAPPER))).thenReturn(enrollmentDTOList);

        // 调用方法
        List<EnrollmentDTO> result = enrollmentDao.getByEmail(email);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{email}, EnrollmentMapper.BASE_MAPPER);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(result, enrollmentDTOList);
    }

    /**
     * 测试根据班级 Id 获取该班级下学生总数
     */
    @Test
    public void testGetContEnrollmentNum() {
        // 准备入参
        String groupId = UUID.randomUUID().toString();
        // 模拟行为
        int count = GenerateCodeUtil.SECURE_RANDOM.nextInt(100);
        String sql = EnrollmentMapper.getContEnrollmentNum;
        when(jdbcTemplate.queryForObject(anyString(), Mockito.any(Object[].class), Mockito.eq(Integer.class))).thenReturn(count);

        // 调用方法
        int result = enrollmentDao.getContEnrollmentNum(groupId);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).queryForObject(sql, new String[]{groupId}, Integer.class);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(result, count);
    }

    /**
     * 测试根据用户 Id 获取已确认的邀请学生信息
     */
    @Test
    public void testGetByCreateUserIdAndConfirmed() {
        // 准备入参
        String createUserId = "349c97c4-979e-494c-a08d-bfa9d2aad1b2";  // 用户 Id
        // 模拟行为
        String enrollmentId = "f495b383-79e1-4347-9180-6ab3ea53f3f5"; // 小孩 Id
        String enrollmentName = "Jaxson Ramirez"; // 小孩名字
        String parentEmail = "<EMAIL>"; // 家长邮箱
        String invitationId = "f495"; // 家长邀请码 Id
        String sql = EnrollmentMapper.getByCreateUserIdAndConfirmed;
        List<EnrollmentDTO> enrollmentDTOList = new ArrayList<>();
        EnrollmentDTO enrollmentDTO = new EnrollmentDTO();
        enrollmentDTO.setEnrollmentId(enrollmentId);
        enrollmentDTO.setEnrollmentName(enrollmentName);
        enrollmentDTO.setParentEmail(parentEmail);
        enrollmentDTO.setInvitationId(invitationId);
        enrollmentDTOList.add(enrollmentDTO);
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(EnrollmentMapper.BASE_MAPPER))).thenReturn(enrollmentDTOList);

        // 调用方法
        List<EnrollmentDTO> result = enrollmentDao.getByCreateUserIdAndConfirmed(createUserId);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{createUserId}, EnrollmentMapper.BASE_MAPPER);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(result, enrollmentDTOList);
    }

    /**
     * 测试根据班级 ID 集合获取学生信息
     */
    @Test
    public void testGetByCreateUserIdAndNewBound() {
        // 准备入参
        List<String> groupIds = new ArrayList<>(); // 班级 Id 集合
        String groupId = UUID.randomUUID().toString();
        groupIds.add(groupId);
        // 模拟行为
        String enrollmentId = "f495b383-79e1-4347-9180-6ab3ea53f3f5"; // 小孩 Id
        String enrollmentName = "Jaxson Ramirez"; // 小孩名字
        String parentEmail = "<EMAIL>"; // 家长邮箱
        String invitationId = "f495"; // 家长邀请码 Id
        String sql = EnrollmentMapper.getByCreateUserIdAndNewBound;
        sql = sql.replace("{{groupIds}}", StringUtil.convertIdsToString(groupIds));
        List<EnrollmentDTO> enrollmentDTOList = new ArrayList<>();
        EnrollmentDTO enrollmentDTO = new EnrollmentDTO();
        enrollmentDTO.setEnrollmentId(enrollmentId);
        enrollmentDTO.setEnrollmentName(enrollmentName);
        enrollmentDTO.setParentEmail(parentEmail);
        enrollmentDTO.setInvitationId(invitationId);
        enrollmentDTOList.add(enrollmentDTO);
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(EnrollmentMapper.BASE_MAPPER))).thenReturn(enrollmentDTOList);

        // 调用方法
        List<EnrollmentDTO> result = enrollmentDao.getByCreateUserIdAndNewBound(groupIds);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{}, EnrollmentMapper.BASE_MAPPER);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(result, enrollmentDTOList);
    }

    /**
     * 测试根据用户 Id 获取学生信息
     */
    @Test
    public void testGetByApplyUserId() {
        // 准备入参
        String applyUserId = "349c97c4-979e-494c-a08d-bfa9d2aad1b2";  // 用户 Id
        // 模拟行为
        String enrollmentId = "f495b383-79e1-4347-9180-6ab3ea53f3f5"; // 小孩 Id
        String enrollmentName = "Jaxson Ramirez"; // 小孩名字
        String parentEmail = "<EMAIL>"; // 家长邮箱
        String invitationId = "f495"; // 家长邀请码 Id
        String sql = EnrollmentMapper.GET_BY_APPLYUSERID;
        List<EnrollmentDTO> enrollmentDTOList = new ArrayList<>();
        EnrollmentDTO enrollmentDTO = new EnrollmentDTO();
        enrollmentDTO.setEnrollmentId(enrollmentId);
        enrollmentDTO.setEnrollmentName(enrollmentName);
        enrollmentDTO.setParentEmail(parentEmail);
        enrollmentDTO.setInvitationId(invitationId);
        enrollmentDTOList.add(enrollmentDTO);
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(EnrollmentMapper.BASE_MAPPER))).thenReturn(enrollmentDTOList);

        // 调用方法
        List<EnrollmentDTO> result = enrollmentDao.getByApplyUserId(applyUserId);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{applyUserId}, EnrollmentMapper.BASE_MAPPER);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(result, enrollmentDTOList);
    }

    /**
     * 测试根据机构 Id 获取学生信息
     */
    @Test
    public void testGetInvitationsByAgencyId() {
        // 准备入参
        String agencyId = "349c97c4-979e-494c-a08d-bfa9d2aad1b2";  // 机构 Id
        // 模拟行为
        String enrollmentId = "f495b383-79e1-4347-9180-6ab3ea53f3f5"; // 小孩 Id
        String enrollmentName = "Jaxson Ramirez"; // 小孩名字
        String parentEmail = "<EMAIL>"; // 家长邮箱
        String invitationId = "f495"; // 家长邀请码 Id
        String sql = EnrollmentMapper.GET_INVITATION_BY_AGENCY_ID;
        List<EnrollmentDTO> enrollmentDTOList = new ArrayList<>();
        EnrollmentDTO enrollmentDTO = new EnrollmentDTO();
        enrollmentDTO.setEnrollmentId(enrollmentId);
        enrollmentDTO.setEnrollmentName(enrollmentName);
        enrollmentDTO.setParentEmail(parentEmail);
        enrollmentDTO.setInvitationId(invitationId);
        enrollmentDTOList.add(enrollmentDTO);
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(EnrollmentMapper.ENROLLMENT_BASE_MAPPER))).thenReturn(enrollmentDTOList);

        // 调用方法
        List<EnrollmentDTO> result = enrollmentDao.getInvitationsByAgencyId(agencyId);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{agencyId}, EnrollmentMapper.ENROLLMENT_BASE_MAPPER);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(result, enrollmentDTOList);
    }

    /**
     * 测试根据机构 Id 获取机构下的所有邀请，排除培训学校
     */
    @Test
    public void testGetInvitationsByAgencyIdExcludeTraining() {
        // 准备入参
        String agencyId = "349c97c4-979e-494c-a08d-bfa9d2aad1b2";  // 机构 Id
        // 模拟行为
        String enrollmentId = "f495b383-79e1-4347-9180-6ab3ea53f3f5"; // 小孩 Id
        String enrollmentName = "Jaxson Ramirez"; // 小孩名字
        String parentEmail = "<EMAIL>"; // 家长邮箱
        String invitationId = "f495"; // 家长邀请码 Id
        String sql = EnrollmentMapper.GET_INVITATION_BY_AGENCY_ID_EXCLUDE_TRAINING;
        List<EnrollmentDTO> enrollmentDTOList = new ArrayList<>();
        EnrollmentDTO enrollmentDTO = new EnrollmentDTO();
        enrollmentDTO.setEnrollmentId(enrollmentId);
        enrollmentDTO.setEnrollmentName(enrollmentName);
        enrollmentDTO.setParentEmail(parentEmail);
        enrollmentDTO.setInvitationId(invitationId);
        enrollmentDTOList.add(enrollmentDTO);
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(EnrollmentMapper.ENROLLMENT_BASE_MAPPER))).thenReturn(enrollmentDTOList);

        // 调用方法
        List<EnrollmentDTO> result = enrollmentDao.getInvitationsByAgencyIdExcludeTraining(agencyId);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{agencyId}, EnrollmentMapper.ENROLLMENT_BASE_MAPPER);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(result, enrollmentDTOList);
    }

    /**
     * 测试根据机构 Id 获取机构下的所有邀请，排除培训学校
     */
    @Test
    public void testGetInvitationsByAgencyIdExcludeTrainingByEndDate() {
        // 准备入参
        String agencyId = "349c97c4-979e-494c-a08d-bfa9d2aad1b2";  // 机构 Id
        String endDate = "2023-12-31"; // 截止日期
        // 模拟行为
        String enrollmentId = "f495b383-79e1-4347-9180-6ab3ea53f3f5"; // 小孩 Id
        String enrollmentName = "Jaxson Ramirez"; // 小孩名字
        String parentEmail = "<EMAIL>"; // 家长邮箱
        String invitationId = "f495"; // 家长邀请码 Id
        String sql = EnrollmentMapper.GET_INVITATION_BY_AGENCY_ID_EXCLUDE_TRAINING_AND_DATE_RANGE;
        List<EnrollmentDTO> enrollmentDTOList = new ArrayList<>();
        EnrollmentDTO enrollmentDTO = new EnrollmentDTO();
        enrollmentDTO.setEnrollmentId(enrollmentId);
        enrollmentDTO.setEnrollmentName(enrollmentName);
        enrollmentDTO.setParentEmail(parentEmail);
        enrollmentDTO.setInvitationId(invitationId);
        enrollmentDTOList.add(enrollmentDTO);
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(EnrollmentMapper.ENROLLMENT_BASE_MAPPER))).thenReturn(enrollmentDTOList);

        // 调用方法
        List<EnrollmentDTO> result = enrollmentDao.getInvitationsByAgencyIdExcludeTraining(agencyId, endDate);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{agencyId, endDate}, EnrollmentMapper.ENROLLMENT_BASE_MAPPER);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(result, enrollmentDTOList);
    }

    /**
     * 测试根据学校 ID 集合获取学生信息
     */
    @Test
    public void testGetInvitationsByCenterIds() {
        // 准备入参
        List<String> centerIds = new ArrayList<>(); // 学校 Id 集合
        String centerId = UUID.randomUUID().toString();
        centerIds.add(centerId);
        // 模拟行为
        String enrollmentId = "f495b383-79e1-4347-9180-6ab3ea53f3f5"; // 小孩 Id
        String enrollmentName = "Jaxson Ramirez"; // 小孩名字
        String parentEmail = "<EMAIL>"; // 家长邮箱
        String invitationId = "f495"; // 家长邀请码 Id
        String sql = EnrollmentMapper.GET_INVITATION_BY_CENTER_IDS;
        sql = sql.replace("@centerIds", StringUtil.convertIdsToString(centerIds));
        List<EnrollmentDTO> enrollmentDTOList = new ArrayList<>();
        EnrollmentDTO enrollmentDTO = new EnrollmentDTO();
        enrollmentDTO.setEnrollmentId(enrollmentId);
        enrollmentDTO.setEnrollmentName(enrollmentName);
        enrollmentDTO.setParentEmail(parentEmail);
        enrollmentDTO.setInvitationId(invitationId);
        enrollmentDTOList.add(enrollmentDTO);
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(EnrollmentMapper.ENROLLMENT_BASE_MAPPER))).thenReturn(enrollmentDTOList);

        // 调用方法
        List<EnrollmentDTO> result = enrollmentDao.getInvitationsByCenterIds(centerIds);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{}, EnrollmentMapper.ENROLLMENT_BASE_MAPPER);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(result, enrollmentDTOList);
    }

    /**
     * 测试根据班级 ID 集合获取学生信息
     */
    @Test
    public void testGetInvitationsByGroupIds() {
        // 准备入参
        List<String> groupIds = new ArrayList<>(); // 班级 Id 集合
        String groupId = UUID.randomUUID().toString();
        groupIds.add(groupId);
        // 模拟行为
        String enrollmentId = "f495b383-79e1-4347-9180-6ab3ea53f3f5"; // 小孩 Id
        String enrollmentName = "Jaxson Ramirez"; // 小孩名字
        String parentEmail = "<EMAIL>"; // 家长邮箱
        String invitationId = "f495"; // 家长邀请码 Id
        String sql = EnrollmentMapper.GET_INVITATION_BY_GROUP_IDS;
        sql = sql.replace("@groupIds", StringUtil.convertIdsToString(groupIds));
        List<EnrollmentDTO> enrollmentDTOList = new ArrayList<>();
        EnrollmentDTO enrollmentDTO = new EnrollmentDTO();
        enrollmentDTO.setEnrollmentId(enrollmentId);
        enrollmentDTO.setEnrollmentName(enrollmentName);
        enrollmentDTO.setParentEmail(parentEmail);
        enrollmentDTO.setInvitationId(invitationId);
        enrollmentDTOList.add(enrollmentDTO);
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(EnrollmentMapper.ENROLLMENT_BASE_MAPPER))).thenReturn(enrollmentDTOList);

        // 调用方法
        List<EnrollmentDTO> result = enrollmentDao.getInvitationsByGroupIds(groupIds);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{}, EnrollmentMapper.ENROLLMENT_BASE_MAPPER);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(result, enrollmentDTOList);
    }

    /**
     * 测试根据学生 Id 获取家长邀请信息
     */
    @Test
    public void testGetParentInvitationsByChild() {
        // 准备入参
        String childId = UUID.randomUUID().toString(); // 学生 Id
        // 模拟行为
        List<ParentDto> parentDtoList = new ArrayList<>();
        ParentDto parentDto = new ParentDto();
        String email = "<EMAIL>"; // 家长邮箱
        String id = UUID.randomUUID().toString(); // 家长 Id
        parentDto.setEmail(email);
        parentDto.setId(id);
        parentDtoList.add(parentDto);
        String sql = EnrollmentMapper.GET_PARENT_INVITATION_BY_CHILD_ID;
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(UserMapper.CHECK_IN_USER_MAPPER))).thenReturn(parentDtoList);

        // 调用方法
        List<ParentDto> result = enrollmentDao.getParentInvitationsByChild(childId);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{childId}, UserMapper.CHECK_IN_USER_MAPPER);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(result, parentDtoList);
    }

    /**
     * 测试根据学生 Id 列表获取家长邀请信息
     */
    @Test
    public void testGetParentInvitationsByChildIds() {
        // 准备入参
        List<String> childIds = new ArrayList<>();
        String childId = UUID.randomUUID().toString(); // 学生 Id
        childIds.add(childId);
        // 模拟行为
        List<ParentDto> parentDtoList = new ArrayList<>();
        ParentDto parentDto = new ParentDto();
        String email = "<EMAIL>"; // 家长邮箱
        String id = UUID.randomUUID().toString(); // 家长 Id
        parentDto.setEmail(email);
        parentDto.setId(id);
        parentDtoList.add(parentDto);
        String sql = EnrollmentMapper.GET_PARENT_INVITATION_BY_CHILD_IDS;
        sql = sql.replace("@ids", StringUtil.convertIdsToString(childIds));
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(UserMapper.CHECK_IN_USER_MAPPER))).thenReturn(parentDtoList);

        // 调用方法
        List<ParentDto> result = enrollmentDao.getParentInvitationsByChildIds(childIds);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{}, UserMapper.CHECK_IN_USER_MAPPER);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(result, parentDtoList);
    }

    /**
     * 测试根据机构 Id 获取机构下的所有邀请，排除培训学校
     */
    @Test
    public void testGetInvitationsCodeByAgencyId() {
        // 准备入参
        String agencyId = "349c97c4-979e-494c-a08d-bfa9d2aad1b2";  // 测试 Id
        // 模拟行为
        String enrollmentId = "f495b383-79e1-4347-9180-6ab3ea53f3f5"; // 小孩 Id
        String enrollmentName = "Jaxson Ramirez"; // 小孩名字
        String parentEmail = "<EMAIL>"; // 家长邮箱
        String invitationId = "f495"; // 家长邀请码 Id
        String sql = EnrollmentMapper.GET_INVITATION_CODE_BY_AGENCY_ID;
        List<EnrollmentDTO> enrollmentDTOList = new ArrayList<>();
        EnrollmentDTO enrollmentDTO = new EnrollmentDTO();
        enrollmentDTO.setEnrollmentId(enrollmentId);
        enrollmentDTO.setEnrollmentName(enrollmentName);
        enrollmentDTO.setParentEmail(parentEmail);
        enrollmentDTO.setInvitationId(invitationId);
        enrollmentDTOList.add(enrollmentDTO);
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(EnrollmentMapper.ENROLLMENT_BASE_MAPPER))).thenReturn(enrollmentDTOList);

        // 调用方法
        List<EnrollmentDTO> result = enrollmentDao.getInvitationsCodeByAgencyId(agencyId);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{agencyId}, EnrollmentMapper.ENROLLMENT_BASE_MAPPER);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(result, enrollmentDTOList);
    }

    /**
     * 测试根据机构 Id 获取机构下的所有邀请码，排除培训学校
     */
    @Test
    public void testGetInvitationsCodeByAgencyIdExcludeTraining() {
        // 准备入参
        String agencyId = "349c97c4-979e-494c-a08d-bfa9d2aad1b2";  // 机构 Id
        // 模拟行为
        String enrollmentId = "f495b383-79e1-4347-9180-6ab3ea53f3f5"; // 小孩 Id
        String enrollmentName = "Jaxson Ramirez"; // 小孩名字
        String parentEmail = "<EMAIL>"; // 家长邮箱
        String invitationId = "f495"; // 家长邀请码 Id
        String sql = EnrollmentMapper.GET_INVITATION_CODE_BY_AGENCY_ID_EXCLUDE_TRAINING;
        List<EnrollmentDTO> enrollmentDTOList = new ArrayList<>();
        EnrollmentDTO enrollmentDTO = new EnrollmentDTO();
        enrollmentDTO.setEnrollmentId(enrollmentId);
        enrollmentDTO.setEnrollmentName(enrollmentName);
        enrollmentDTO.setParentEmail(parentEmail);
        enrollmentDTO.setInvitationId(invitationId);
        enrollmentDTOList.add(enrollmentDTO);
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(EnrollmentMapper.ENROLLMENT_BASE_MAPPER))).thenReturn(enrollmentDTOList);

        // 调用方法
        List<EnrollmentDTO> result = enrollmentDao.getInvitationsCodeByAgencyIdExcludeTraining(agencyId);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{agencyId}, EnrollmentMapper.ENROLLMENT_BASE_MAPPER);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(result, enrollmentDTOList);
    }

    /**
     * 测试获取机构下到截止日期的邀请码信息，排除培训学校
     */
    @Test
    public void testGetInvitationsCodeByAgencyIdExcludeTrainingByEndDate() {
        // 准备入参
        String agencyId = "349c97c4-979e-494c-a08d-bfa9d2aad1b2";  // 机构 Id
        String endDate = "2023-12-31"; // 截止日期
        // 模拟行为
        String enrollmentId = "f495b383-79e1-4347-9180-6ab3ea53f3f5"; // 小孩 Id
        String enrollmentName = "Jaxson Ramirez"; // 小孩名字
        String parentEmail = "<EMAIL>"; // 家长邮箱
        String invitationId = "f495"; // 家长邀请码 Id
        String sql = EnrollmentMapper.GET_INVITATION_CODE_BY_AGENCY_ID_EXCLUDE_TRAINING_AND_DATE_RANGE;
        List<EnrollmentDTO> enrollmentDTOList = new ArrayList<>();
        EnrollmentDTO enrollmentDTO = new EnrollmentDTO();
        enrollmentDTO.setEnrollmentId(enrollmentId);
        enrollmentDTO.setEnrollmentName(enrollmentName);
        enrollmentDTO.setParentEmail(parentEmail);
        enrollmentDTO.setInvitationId(invitationId);
        enrollmentDTOList.add(enrollmentDTO);
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(EnrollmentMapper.ENROLLMENT_BASE_MAPPER))).thenReturn(enrollmentDTOList);

        // 调用方法
        List<EnrollmentDTO> result = enrollmentDao.getInvitationsCodeByAgencyIdExcludeTraining(agencyId, endDate);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{agencyId, endDate}, EnrollmentMapper.ENROLLMENT_BASE_MAPPER);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(result, enrollmentDTOList);
    }

    /**
     * 测试根据学校 ID 集合获取学生信息
     */
    @Test
    public void testGetInvitationsCodeByCenterIds() {
        // 准备入参
        List<String> centerIds = new ArrayList<>(); // 学校 Id 集合
        String centerId = UUID.randomUUID().toString();
        centerIds.add(centerId);
        // 模拟行为
        String enrollmentId = "f495b383-79e1-4347-9180-6ab3ea53f3f5"; // 小孩 Id
        String enrollmentName = "Jaxson Ramirez"; // 小孩名字
        String parentEmail = "<EMAIL>"; // 家长邮箱
        String invitationId = "f495"; // 家长邀请码 Id
        String sql = EnrollmentMapper.GET_INVITATION_CODE_BY_CENTER_IDS;
        sql = sql.replace("@centerIds", StringUtil.convertIdsToString(centerIds));
        List<EnrollmentDTO> enrollmentDTOList = new ArrayList<>();
        EnrollmentDTO enrollmentDTO = new EnrollmentDTO();
        enrollmentDTO.setEnrollmentId(enrollmentId);
        enrollmentDTO.setEnrollmentName(enrollmentName);
        enrollmentDTO.setParentEmail(parentEmail);
        enrollmentDTO.setInvitationId(invitationId);
        enrollmentDTOList.add(enrollmentDTO);
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(EnrollmentMapper.ENROLLMENT_BASE_MAPPER))).thenReturn(enrollmentDTOList);

        // 调用方法
        List<EnrollmentDTO> result = enrollmentDao.getInvitationsCodeByCenterIds(centerIds);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{}, EnrollmentMapper.ENROLLMENT_BASE_MAPPER);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(result, enrollmentDTOList);
    }

    /**
     * 测试根据班级 ID 集合获取学生信息
     */
    @Test
    public void testgetInvitationsCodeByGroupIds() {
        // 准备入参
        List<String> groupIds = new ArrayList<>(); // 班级 Id 集合
        String groupId = UUID.randomUUID().toString();
        groupIds.add(groupId);
        // 模拟行为
        String enrollmentId = "f495b383-79e1-4347-9180-6ab3ea53f3f5"; // 小孩 Id
        String enrollmentName = "Jaxson Ramirez"; // 小孩名字
        String parentEmail = "<EMAIL>"; // 家长邮箱
        String invitationId = "f495"; // 家长邀请码 Id
        String sql = EnrollmentMapper.GET_INVITATION_CODE_BY_GROUP_IDS;
        sql = sql.replace("@groupIds", StringUtil.convertIdsToString(groupIds));
        List<EnrollmentDTO> enrollmentDTOList = new ArrayList<>();
        EnrollmentDTO enrollmentDTO = new EnrollmentDTO();
        enrollmentDTO.setEnrollmentId(enrollmentId);
        enrollmentDTO.setEnrollmentName(enrollmentName);
        enrollmentDTO.setParentEmail(parentEmail);
        enrollmentDTO.setInvitationId(invitationId);
        enrollmentDTOList.add(enrollmentDTO);
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(EnrollmentMapper.ENROLLMENT_BASE_MAPPER))).thenReturn(enrollmentDTOList);

        // 调用方法
        List<EnrollmentDTO> result = enrollmentDao.getInvitationsCodeByGroupIds(groupIds);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{}, EnrollmentMapper.ENROLLMENT_BASE_MAPPER);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(result, enrollmentDTOList);
    }

    /**
     * 测试根据学生 Id 获取家长邀请信息
     */
    @Test
    public void testGetParentCodeByChild() {
        // 准备入参
        String childId = UUID.randomUUID().toString(); // 学生 Id
        // 模拟行为
        List<ParentDto> parentDtoList = new ArrayList<>();
        ParentDto parentDto = new ParentDto();
        String email = "<EMAIL>"; // 家长邮箱
        String id = UUID.randomUUID().toString(); // 家长 Id
        parentDto.setEmail(email);
        parentDto.setId(id);
        parentDtoList.add(parentDto);
        String sql = EnrollmentMapper.GET_PARENT_CODE_BY_CHILD;
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(UserMapper.CHECK_IN_USER_MAPPER))).thenReturn(parentDtoList);

        // 调用方法
        List<ParentDto> result = enrollmentDao.getParentCodeByChild(childId);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{childId}, UserMapper.CHECK_IN_USER_MAPPER);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(result, parentDtoList);
    }

    /**
     * 测试根据学生 Id 列表获取家长邀请信息
     */
    @Test
    public void testGetParentCodeByChildIds() {
        // 准备入参
        List<String> childIds = new ArrayList<>();
        String childId = UUID.randomUUID().toString(); // 学生 Id
        childIds.add(childId);
        // 模拟行为
        List<ParentDto> parentDtoList = new ArrayList<>();
        ParentDto parentDto = new ParentDto();
        String email = "<EMAIL>"; // 家长邮箱
        String id = UUID.randomUUID().toString(); // 家长 Id
        parentDto.setEmail(email);
        parentDto.setId(id);
        parentDtoList.add(parentDto);
        String sql = EnrollmentMapper.GET_PARENT_CODE_BY_CHILD_IDS;
        sql = sql.replace("@childIds", StringUtil.convertIdsToString(childIds));
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(UserMapper.CHECK_IN_USER_MAPPER))).thenReturn(parentDtoList);

        // 调用方法
        List<ParentDto> result = enrollmentDao.getParentCodeByChildIds(childIds);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{}, UserMapper.CHECK_IN_USER_MAPPER);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(result, parentDtoList);
    }

    /**
     * 测试根据班级 Id 获取班级信息
     */
    @Test
    public void testGetBatchAddTreeData() {
        // 准备入参
        String groupId = UUID.randomUUID().toString(); // 班级 Id
        // 模拟行为
        List<ClassesTreeDataDto> classesTreeDataDtoList = new ArrayList<>();
        ClassesTreeDataDto classesTreeDataDto = new ClassesTreeDataDto();
        String id = UUID.randomUUID().toString(); // Id
        String name = "name"; // 名字
        classesTreeDataDto.setId(id);
        classesTreeDataDto.setName(name);
        classesTreeDataDtoList.add(classesTreeDataDto);
        String sql = "select DisplayName as name,Id as id,GroupId as pid,'3' as level from contents_enrollment where GroupId = ? AND IsDeleted = 0 AND IsInactive = 0";
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(CenterMapper.MAPPER_ALLCLASSES))).thenReturn(classesTreeDataDtoList);

        // 调用方法
        List<ClassesTreeDataDto> result = enrollmentDao.getBatchAddTreeData(groupId);

        // 验证结果
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{groupId}, CenterMapper.MAPPER_ALLCLASSES);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(result, classesTreeDataDtoList);
    }

    /**
     * 测试根据班级 Id 集合获取班级信息
     */
    @Test
    public void testGetBatchAddTreeData2() {
        // 准备入参
        List<String> groupIds = new ArrayList<>();
        String groupId = UUID.randomUUID().toString(); // 班级 Id
        groupIds.add(groupId);
        // 模拟行为
        List<ClassesTreeDataDto> classesTreeDataDtoList = new ArrayList<>();
        ClassesTreeDataDto classesTreeDataDto = new ClassesTreeDataDto();
        String id = UUID.randomUUID().toString(); // Id
        String name = "name"; // 名字
        classesTreeDataDto.setId(id);
        classesTreeDataDto.setName(name);
        classesTreeDataDtoList.add(classesTreeDataDto);
        String sql = "SELECT DisplayName AS name,Id AS id,GroupId AS pid,'3' AS level, FrameworkId FROM contents_enrollment WHERE GroupId IN ({@groupIds}) AND IsDeleted = 0 AND IsInactive = 0";
        sql = sql.replace("{@groupIds}", StringUtil.convertIdsToString(groupIds));
        when(jdbcTemplate.query(anyString(), Mockito.eq(CenterMapper.MAPPER_ALLCLASSES))).thenReturn(classesTreeDataDtoList);

        // 调用方法
        List<ClassesTreeDataDto> result = enrollmentDao.getBatchAddTreeData(groupIds);

        // 验证结果
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, CenterMapper.MAPPER_ALLCLASSES);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(result, classesTreeDataDtoList);
    }
}

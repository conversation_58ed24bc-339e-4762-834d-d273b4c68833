package com.learninggenie.common.data.dao.bi.impl;

import com.learninggenie.common.data.mapper.BIMapper;
import com.learninggenie.common.data.model.bi.activity.DailyActivityModel;
import com.learninggenie.common.utils.TimeUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.jdbc.core.JdbcTemplate;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *  BI DAO 实现类测试
 */
@RunWith(MockitoJUnitRunner.class)
public class BIDaoImplTest {

    @InjectMocks
    private BIDaoImpl biDaoImpl;

    @Mock
    private JdbcTemplate jdbcTemplate;

    /**
     * 测试根据机构和日期范围查询观察记录日活跃记录列表
     */
    @Test
    public void testGetUpdatePortfolioDailyActivitiesByAgencyIdAndDateRange() {
        // 准备测试数据
        String agencyId = "f495b383-79e1-4347-9180-6ab3ea53f3f5"; // 机构 ID
        String centerId = "1b8f3c7b-f093-4afa-bd42-b841d4d69b15"; // 学校 ID
        String groupId = "072dcce5-24cd-4fb7-99f4-ccd42ecc0c67"; // 班级 ID
        String childId = "b3f5f5f5-5f5f-5f5f-5f5f-5f5f5f5f5f5f"; // 学生 ID
        String activityUserId = "e1be53a1-6131-44bd-bfb2-acb5c7506b8b"; // 操作用户 ID
        Date activityAtUtc = TimeUtil.parse("2024-01-01", TimeUtil.format10); // 活跃 UTC 时间
        Date activityAtLocal = TimeUtil.parse("2024-01-01", TimeUtil.format10); // 活跃本地时间
        String startDate = "2024-01-01";
        String endDate = "2024-01-31";
        // 模拟行为
        String sql = BIMapper.SQL_GET_NOTE_DAILY_ACTIVITIES_BY_AGENCY_ID_AND_DATE_RANGE;
        List<DailyActivityModel> dailyActivityModels = new ArrayList<>();
        DailyActivityModel dailyActivityModel = new DailyActivityModel();
        dailyActivityModel.setAgencyId(agencyId);
        dailyActivityModel.setCenterId(centerId);
        dailyActivityModel.setGroupId(groupId);
        dailyActivityModel.setChildId(childId);
        dailyActivityModel.setActivityUserId(activityUserId);
        dailyActivityModel.setActivityAtUtc(activityAtUtc);
        dailyActivityModel.setActivityAtLocal(activityAtLocal);
        dailyActivityModels.add(dailyActivityModel);
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(BIMapper.MAPPER_DAILY_ACTIVITY))).thenReturn(dailyActivityModels);

        // 方法调用
        List<DailyActivityModel> activities = biDaoImpl.getUpdatePortfolioDailyActivitiesByAgencyIdAndDateRange(agencyId, startDate, endDate);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{startDate, endDate, agencyId}, BIMapper.MAPPER_DAILY_ACTIVITY);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(activities, dailyActivityModels);
    }

    /**
     * 测试根据机构和日期范围查询评分日活跃记录列表
     */
    @Test
    public void testGetScoreDailyActivitiesByAgencyIdAndDateRange() {
        // 准备测试数据
        String agencyId = "689e0d6f-d449-4cf2-9993-adeacd2e74c0"; // 机构 ID
        String centerId = "fa9dad38-fdee-45a8-a24a-62032628f51a"; // 学校 id
        String groupId = "072dcce5-24cd-4fb7-99f4-ccd42ecc0c67"; // 班级 ID
        String childId = "b3f5f5f5-5f5f-5f5f-5f5f-5f5f5f5f5f5f"; // 学生 ID
        String activityUserId = "e1be53a1-6131-44bd-bfb2-acb5c7506b8b"; // 操作用户 ID
        Date activityAtUtc = TimeUtil.parse("2024-01-01", TimeUtil.format10); // 活跃 UTC 时间
        Date activityAtLocal = TimeUtil.parse("2024-01-01", TimeUtil.format10); // 活跃本地时间
        String startDate = "2024-01-01";
        String endDate = "2024-01-31";
        // 模拟行为
        String sql = BIMapper.SQL_GET_RATING_DAILY_ACTIVITIES_BY_AGENCY_ID_AND_DATE_RANGE;
        List<DailyActivityModel> dailyActivityModels = new ArrayList<>();
        DailyActivityModel dailyActivityModel = new DailyActivityModel();
        dailyActivityModel.setAgencyId(agencyId);
        dailyActivityModel.setCenterId(centerId);
        dailyActivityModel.setGroupId(groupId);
        dailyActivityModel.setChildId(childId);
        dailyActivityModel.setActivityUserId(activityUserId);
        dailyActivityModel.setActivityAtUtc(activityAtUtc);
        dailyActivityModel.setActivityAtLocal(activityAtLocal);
        dailyActivityModels.add(dailyActivityModel);
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(BIMapper.MAPPER_DAILY_ACTIVITY))).thenReturn(dailyActivityModels);

        // 方法调用
        List<DailyActivityModel> activities = biDaoImpl.getScoreDailyActivitiesByAgencyIdAndDateRange(agencyId, startDate, endDate);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{startDate, endDate, agencyId}, BIMapper.MAPPER_DAILY_ACTIVITY);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(activities, dailyActivityModels);
    }

    /**
     * 测试根据机构和日期范围查询锁定日活跃记录列表
     */
    @Test
    public void testGetLockDailyActivitiesByAgencyIdAndDateRange() {
        // 准备测试数据
        String agencyId = "f495b383-79e1-4347-9180-6ab3ea53f3f5"; // 机构 ID
        String centerId = "1b8f3c7b-f093-4afa-bd42-b841d4d69b15"; // 学校 ID
        String groupId = "072dcce5-24cd-4fb7-99f4-ccd42ecc0c67"; // 班级 ID
        String childId = "b3f5f5f5-5f5f-5f5f-5f5f-5f5f5f5f5f5f"; // 学生 ID
        String activityUserId = "e1be53a1-6131-44bd-bfb2-acb5c7506b8b"; // 操作用户 ID
        Date activityAtUtc = TimeUtil.parse("2024-01-01", TimeUtil.format10); // 活跃 UTC 时间
        Date activityAtLocal = TimeUtil.parse("2024-01-01", TimeUtil.format10); // 活跃本地时间
        String startDate = "2024-01-01";
        String endDate = "2024-01-31";
        // 模拟行为
        String sql = BIMapper.SQL_GET_LOCK_DAILY_ACTIVITIES_BY_AGENCY_ID_AND_DATE_RANGE;
        List<DailyActivityModel> dailyActivityModels = new ArrayList<>();
        DailyActivityModel dailyActivityModel = new DailyActivityModel();
        dailyActivityModel.setAgencyId(agencyId);
        dailyActivityModel.setCenterId(centerId);
        dailyActivityModel.setGroupId(groupId);
        dailyActivityModel.setChildId(childId);
        dailyActivityModel.setActivityUserId(activityUserId);
        dailyActivityModel.setActivityAtUtc(activityAtUtc);
        dailyActivityModel.setActivityAtLocal(activityAtLocal);
        dailyActivityModels.add(dailyActivityModel);
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(BIMapper.MAPPER_DAILY_ACTIVITY))).thenReturn(dailyActivityModels);

        // 方法调用
        List<DailyActivityModel> activities = biDaoImpl.getLockDailyActivitiesByAgencyIdAndDateRange(agencyId, startDate, endDate);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{startDate, endDate, agencyId}, BIMapper.MAPPER_DAILY_ACTIVITY);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(activities, dailyActivityModels);
    }

    /**
     * 测试根据机构和日期范围查询下载报告日活跃记录列表
     */
    @Test
    public void testGetDownloadPortfolioReportDailyActivitiesByAgencyIdAndDateRange() {
        // 准备测试数据
        String agencyId = "f495b383-79e1-4347-9180-6ab3ea53f3f5"; // 机构 ID
        String centerId = "1b8f3c7b-f093-4afa-bd42-b841d4d69b15"; // 学校 ID
        String groupId = "072dcce5-24cd-4fb7-99f4-ccd42ecc0c67"; // 班级 ID
        String childId = "b3f5f5f5-5f5f-5f5f-5f5f-5f5f5f5f5f5f"; // 学生 ID
        String activityUserId = "e1be53a1-6131-44bd-bfb2-acb5c7506b8b"; // 操作用户 ID
        Date activityAtUtc = TimeUtil.parse("2024-01-01", TimeUtil.format10); // 活跃 UTC 时间
        Date activityAtLocal = TimeUtil.parse("2024-01-01", TimeUtil.format10); // 活跃本地时间
        String startDate = "2024-01-01";
        String endDate = "2024-01-31";
        // 模拟行为
        String sql = BIMapper.SQL_GET_DOWNLOAD_REPORT_DAILY_ACTIVITIES_BY_AGENCY_ID_AND_DATE_RANGE;
        List<DailyActivityModel> dailyActivityModels = new ArrayList<>();
        DailyActivityModel dailyActivityModel = new DailyActivityModel();
        dailyActivityModel.setAgencyId(agencyId);
        dailyActivityModel.setCenterId(centerId);
        dailyActivityModel.setGroupId(groupId);
        dailyActivityModel.setChildId(childId);
        dailyActivityModel.setActivityUserId(activityUserId);
        dailyActivityModel.setActivityAtUtc(activityAtUtc);
        dailyActivityModel.setActivityAtLocal(activityAtLocal);
        dailyActivityModels.add(dailyActivityModel);
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(BIMapper.MAPPER_DAILY_ACTIVITY))).thenReturn(dailyActivityModels);

        // 方法调用
        List<DailyActivityModel> activities = biDaoImpl.getDownloadPortfolioReportDailyActivitiesByAgencyIdAndDateRange(agencyId, startDate, endDate);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{startDate, endDate, agencyId}, BIMapper.MAPPER_DAILY_ACTIVITY);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(activities, dailyActivityModels);
    }

    /**
     * 测试根据机构和日期范围查询家园互动日活跃记录列表
     */
    @Test
    public void testGetUpdateEngagementDailyActivitiesByAgencyIdAndDateRange() {
        // 准备测试数据
        String agencyId = "f495b383-79e1-4347-9180-6ab3ea53f3f5"; // 机构 ID
        String centerId = "1b8f3c7b-f093-4afa-bd42-b841d4d69b15"; // 学校 ID
        String groupId = "072dcce5-24cd-4fb7-99f4-ccd42ecc0c67"; // 班级 ID
        String childId = "b3f5f5f5-5f5f-5f5f-5f5f-5f5f5f5f5f5f"; // 学生 ID
        String activityUserId = "e1be53a1-6131-44bd-bfb2-acb5c7506b8b"; // 操作用户 ID
        Date activityAtUtc = TimeUtil.parse("2024-01-01", TimeUtil.format10); // 活跃 UTC 时间
        Date activityAtLocal = TimeUtil.parse("2024-01-01", TimeUtil.format10); // 活跃本地时间
        String startDate = "2024-01-01";
        String endDate = "2024-01-31";
        // 模拟行为
        String sql = BIMapper.SQL_GET_UPDATE_ENGAGEMENT_DAILY_ACTIVITIES_BY_AGENCY_ID_AND_DATE_RANGE;
        List<DailyActivityModel> dailyActivityModels = new ArrayList<>();
        DailyActivityModel dailyActivityModel = new DailyActivityModel();
        dailyActivityModel.setAgencyId(agencyId);
        dailyActivityModel.setCenterId(centerId);
        dailyActivityModel.setGroupId(groupId);
        dailyActivityModel.setChildId(childId);
        dailyActivityModel.setActivityUserId(activityUserId);
        dailyActivityModel.setActivityAtUtc(activityAtUtc);
        dailyActivityModel.setActivityAtLocal(activityAtLocal);
        dailyActivityModels.add(dailyActivityModel);
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(BIMapper.MAPPER_DAILY_ACTIVITY))).thenReturn(dailyActivityModels);

        // 方法调用
        List<DailyActivityModel> activities = biDaoImpl.getUpdateEngagementDailyActivitiesByAgencyIdAndDateRange(agencyId, startDate, endDate);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{startDate, endDate, agencyId}, BIMapper.MAPPER_DAILY_ACTIVITY);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(activities, dailyActivityModels);
    }

    /**
     * 测试根据机构和日期范围查询老师发送聊天消息日活跃记录列表
     */
    @Test
    public void testGetTeacherSendTwoWayMessageDailyActivitiesByAgencyIdAndDateRange() {
        // 准备测试数据
        String agencyId = "f495b383-79e1-4347-9180-6ab3ea53f3f5"; // 机构 ID
        String centerId = "1b8f3c7b-f093-4afa-bd42-b841d4d69b15"; // 学校 ID
        String groupId = "072dcce5-24cd-4fb7-99f4-ccd42ecc0c67"; // 班级 ID
        String childId = "b3f5f5f5-5f5f-5f5f-5f5f-5f5f5f5f5f5f"; // 学生 ID
        String activityUserId = "e1be53a1-6131-44bd-bfb2-acb5c7506b8b"; // 操作用户 ID
        Date activityAtUtc = TimeUtil.parse("2024-01-01", TimeUtil.format10); // 活跃 UTC 时间
        Date activityAtLocal = TimeUtil.parse("2024-01-01", TimeUtil.format10); // 活跃本地时间
        String startDate = "2024-01-01";
        String endDate = "2024-01-31";
        // 模拟行为
        String sql = BIMapper.SQL_GET_TEACHER_SEND_TWO_WAY_DAILY_ACTIVITIES_BY_AGENCY_ID_AND_DATE_RANGE;
        List<DailyActivityModel> dailyActivityModels = new ArrayList<>();
        DailyActivityModel dailyActivityModel = new DailyActivityModel();
        dailyActivityModel.setAgencyId(agencyId);
        dailyActivityModel.setCenterId(centerId);
        dailyActivityModel.setGroupId(groupId);
        dailyActivityModel.setChildId(childId);
        dailyActivityModel.setActivityUserId(activityUserId);
        dailyActivityModel.setActivityAtUtc(activityAtUtc);
        dailyActivityModel.setActivityAtLocal(activityAtLocal);
        dailyActivityModels.add(dailyActivityModel);
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(BIMapper.MAPPER_DAILY_ACTIVITY))).thenReturn(dailyActivityModels);

        // 方法调用
        List<DailyActivityModel> activities = biDaoImpl.getTeacherSendTwoWayMessageDailyActivitiesByAgencyIdAndDateRange(agencyId, startDate, endDate);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{startDate, endDate, agencyId}, BIMapper.MAPPER_DAILY_ACTIVITY);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(activities, dailyActivityModels);
    }

    /**
     * 测试根据机构和日期范围查询家长发送聊天消息日活跃记录列表
     */
    @Test
    public void testGetParentSendTwoWayMessageDailyActivitiesByAgencyIdAndDateRange() {
        // 准备测试数据
        String agencyId = "f495b383-79e1-4347-9180-6ab3ea53f3f5"; // 机构 ID
        String centerId = "1b8f3c7b-f093-4afa-bd42-b841d4d69b15"; // 学校 ID
        String groupId = "072dcce5-24cd-4fb7-99f4-ccd42ecc0c67"; // 班级 ID
        String childId = "b3f5f5f5-5f5f-5f5f-5f5f-5f5f5f5f5f5f"; // 学生 ID
        String activityUserId = "e1be53a1-6131-44bd-bfb2-acb5c7506b8b"; // 操作用户 ID
        Date activityAtUtc = TimeUtil.parse("2024-01-01", TimeUtil.format10); // 活跃 UTC 时间
        Date activityAtLocal = TimeUtil.parse("2024-01-01", TimeUtil.format10); // 活跃本地时间
        String startDate = "2024-01-01";
        String endDate = "2024-01-31";
        // 模拟行为
        String sql = BIMapper.SQL_GET_PARENT_SEND_TWO_WAY_DAILY_ACTIVITIES_BY_AGENCY_ID_AND_DATE_RANGE;
        List<DailyActivityModel> dailyActivityModels = new ArrayList<>();
        DailyActivityModel dailyActivityModel = new DailyActivityModel();
        dailyActivityModel.setAgencyId(agencyId);
        dailyActivityModel.setCenterId(centerId);
        dailyActivityModel.setGroupId(groupId);
        dailyActivityModel.setChildId(childId);
        dailyActivityModel.setActivityUserId(activityUserId);
        dailyActivityModel.setActivityAtUtc(activityAtUtc);
        dailyActivityModel.setActivityAtLocal(activityAtLocal);
        dailyActivityModels.add(dailyActivityModel);
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(BIMapper.MAPPER_DAILY_ACTIVITY))).thenReturn(dailyActivityModels);

        // 方法调用
        List<DailyActivityModel> activities = biDaoImpl.getParentSendTwoWayMessageDailyActivitiesByAgencyIdAndDateRange(agencyId, startDate, endDate);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{startDate, endDate, agencyId}, BIMapper.MAPPER_DAILY_ACTIVITY);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(activities, dailyActivityModels);
    }

    /**
     * 测试根据机构和日期范围查询家长查看家园互动日活跃记录列表
     */
    @Test
    public void testGetParentViewEngagementDailyActivitiesByAgencyIdAndDateRange() {
        // 准备测试数据
        String agencyId = "f495b383-79e1-4347-9180-6ab3ea53f3f5"; // 机构 ID
        String centerId = "1b8f3c7b-f093-4afa-bd42-b841d4d69b15"; // 学校 ID
        String groupId = "072dcce5-24cd-4fb7-99f4-ccd42ecc0c67"; // 班级 ID
        String childId = "b3f5f5f5-5f5f-5f5f-5f5f-5f5f5f5f5f5f"; // 学生 ID
        String activityUserId = "e1be53a1-6131-44bd-bfb2-acb5c7506b8b"; // 操作用户 ID
        Date activityAtUtc = TimeUtil.parse("2024-01-01", TimeUtil.format10); // 活跃 UTC 时间
        Date activityAtLocal = TimeUtil.parse("2024-01-01", TimeUtil.format10); // 活跃本地时间
        String startDate = "2024-01-01";
        String endDate = "2024-01-31";
        // 模拟行为
        String sql = BIMapper.SQL_GET_PARENT_VIEW_ENGAGEMENT_DAILY_ACTIVITIES_BY_AGENCY_ID_AND_DATE_RANGE;
        List<DailyActivityModel> dailyActivityModels = new ArrayList<>();
        DailyActivityModel dailyActivityModel = new DailyActivityModel();
        dailyActivityModel.setAgencyId(agencyId);
        dailyActivityModel.setCenterId(centerId);
        dailyActivityModel.setGroupId(groupId);
        dailyActivityModel.setChildId(childId);
        dailyActivityModel.setActivityUserId(activityUserId);
        dailyActivityModel.setActivityAtUtc(activityAtUtc);
        dailyActivityModel.setActivityAtLocal(activityAtLocal);
        dailyActivityModels.add(dailyActivityModel);
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(BIMapper.MAPPER_DAILY_ACTIVITY))).thenReturn(dailyActivityModels);

        // 方法调用
        List<DailyActivityModel> activities = biDaoImpl.getParentViewEngagementDailyActivitiesByAgencyIdAndDateRange(agencyId, startDate, endDate);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{startDate, endDate, agencyId}, BIMapper.MAPPER_DAILY_ACTIVITY);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(activities, dailyActivityModels);
    }

    /**
     * 测试根据机构和日期范围查询老师添加/审核签到日活跃记录列表
     */
    @Test
    public void testGetTeacherAttendanceDailyActivitiesByAgencyIdAndDateRange() {
        // 准备测试数据
        String agencyId = "f495b383-79e1-4347-9180-6ab3ea53f3f5"; // 机构 ID
        String centerId = "1b8f3c7b-f093-4afa-bd42-b841d4d69b15"; // 学校 ID
        String groupId = "072dcce5-24cd-4fb7-99f4-ccd42ecc0c67"; // 班级 ID
        String childId = "b3f5f5f5-5f5f-5f5f-5f5f-5f5f5f5f5f5f"; // 学生 ID
        String activityUserId = "e1be53a1-6131-44bd-bfb2-acb5c7506b8b"; // 操作用户 ID
        Date activityAtUtc = TimeUtil.parse("2024-01-01", TimeUtil.format10); // 活跃 UTC 时间
        Date activityAtLocal = TimeUtil.parse("2024-01-01", TimeUtil.format10); // 活跃本地时间
        String startDate = "2024-01-01";
        String endDate = "2024-01-31";
        // 模拟行为
        String sql = BIMapper.SQL_GET_TEACHER_ATTENDANCE_DAILY_ACTIVITIES_BY_AGENCY_ID_AND_DATE_RANGE;
        List<DailyActivityModel> dailyActivityModels = new ArrayList<>();
        DailyActivityModel dailyActivityModel = new DailyActivityModel();
        dailyActivityModel.setAgencyId(agencyId);
        dailyActivityModel.setCenterId(centerId);
        dailyActivityModel.setGroupId(groupId);
        dailyActivityModel.setChildId(childId);
        dailyActivityModel.setActivityUserId(activityUserId);
        dailyActivityModel.setActivityAtUtc(activityAtUtc);
        dailyActivityModel.setActivityAtLocal(activityAtLocal);
        dailyActivityModels.add(dailyActivityModel);
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(BIMapper.MAPPER_DAILY_ACTIVITY))).thenReturn(dailyActivityModels);

        // 方法调用
        List<DailyActivityModel> activities = biDaoImpl.getTeacherAttendanceDailyActivitiesByAgencyIdAndDateRange(agencyId, startDate, endDate);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{startDate, endDate, agencyId}, BIMapper.MAPPER_DAILY_ACTIVITY);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(activities, dailyActivityModels);
    }

    /**
     * 测试根据机构和日期范围查询老师添加/审核签到日活跃记录列表
     */
    @Test
    public void testGetTeacherInKindAssignmentDailyActivitiesByAgencyIdAndDateRange() {
        // 准备测试数据
        String agencyId = "f495b383-79e1-4347-9180-6ab3ea53f3f5"; // 机构 ID
        String centerId = "1b8f3c7b-f093-4afa-bd42-b841d4d69b15"; // 学校 ID
        String groupId = "072dcce5-24cd-4fb7-99f4-ccd42ecc0c67"; // 班级 ID
        String childId = "b3f5f5f5-5f5f-5f5f-5f5f-5f5f5f5f5f5f"; // 学生 ID
        String activityUserId = "e1be53a1-6131-44bd-bfb2-acb5c7506b8b"; // 操作用户 ID
        Date activityAtUtc = TimeUtil.parse("2024-01-01", TimeUtil.format10); // 活跃 UTC 时间
        Date activityAtLocal = TimeUtil.parse("2024-01-01", TimeUtil.format10); // 活跃本地时间
        String startDate = "2024-01-01";
        String endDate = "2024-01-31";
        // 模拟行为
        String sql = BIMapper.SQL_GET_TEACHER_IN_KIND_ASSIGNMENT_DAILY_ACTIVITIES_BY_AGENCY_ID_AND_DATE_RANGE;
        List<DailyActivityModel> dailyActivityModels = new ArrayList<>();
        DailyActivityModel dailyActivityModel = new DailyActivityModel();
        dailyActivityModel.setAgencyId(agencyId);
        dailyActivityModel.setCenterId(centerId);
        dailyActivityModel.setGroupId(groupId);
        dailyActivityModel.setChildId(childId);
        dailyActivityModel.setActivityUserId(activityUserId);
        dailyActivityModel.setActivityAtUtc(activityAtUtc);
        dailyActivityModel.setActivityAtLocal(activityAtLocal);
        dailyActivityModels.add(dailyActivityModel);
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(BIMapper.MAPPER_DAILY_ACTIVITY))).thenReturn(dailyActivityModels);

        // 方法调用
        List<DailyActivityModel> activities = biDaoImpl.getTeacherInKindAssignmentDailyActivitiesByAgencyIdAndDateRange(agencyId, startDate, endDate);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{startDate, endDate, agencyId}, BIMapper.MAPPER_DAILY_ACTIVITY);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(activities, dailyActivityModels);
    }

    /**
     * 测试根据机构和日期范围查询 InKind 审核日活跃记录列表
     */
    @Test
    public void testGetTeacherInKindReviewDailyActivitiesByAgencyIdAndDateRange() {
        // 准备测试数据
        String agencyId = "f495b383-79e1-4347-9180-6ab3ea53f3f5"; // 机构 ID
        String centerId = "1b8f3c7b-f093-4afa-bd42-b841d4d69b15"; // 学校 ID
        String groupId = "072dcce5-24cd-4fb7-99f4-ccd42ecc0c67"; // 班级 ID
        String childId = "b3f5f5f5-5f5f-5f5f-5f5f-5f5f5f5f5f5f"; // 学生 ID
        String activityUserId = "e1be53a1-6131-44bd-bfb2-acb5c7506b8b"; // 操作用户 ID
        Date activityAtUtc = TimeUtil.parse("2024-01-01", TimeUtil.format10); // 活跃 UTC 时间
        Date activityAtLocal = TimeUtil.parse("2024-01-01", TimeUtil.format10); // 活跃本地时间
        String startDate = "2024-01-01";
        String endDate = "2024-01-31";
        // 模拟行为
        String sql = BIMapper.SQL_GET_TEACHER_IN_KIND_REVIEW_DAILY_ACTIVITIES_BY_AGENCY_ID_AND_DATE_RANGE;
        List<DailyActivityModel> dailyActivityModels = new ArrayList<>();
        DailyActivityModel dailyActivityModel = new DailyActivityModel();
        dailyActivityModel.setAgencyId(agencyId);
        dailyActivityModel.setCenterId(centerId);
        dailyActivityModel.setGroupId(groupId);
        dailyActivityModel.setChildId(childId);
        dailyActivityModel.setActivityUserId(activityUserId);
        dailyActivityModel.setActivityAtUtc(activityAtUtc);
        dailyActivityModel.setActivityAtLocal(activityAtLocal);
        dailyActivityModels.add(dailyActivityModel);
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(BIMapper.MAPPER_DAILY_ACTIVITY))).thenReturn(dailyActivityModels);

        // 方法调用
        List<DailyActivityModel> activities = biDaoImpl.getTeacherInKindReviewDailyActivitiesByAgencyIdAndDateRange(agencyId, startDate, endDate);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{startDate, endDate, agencyId}, BIMapper.MAPPER_DAILY_ACTIVITY);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(activities, dailyActivityModels);
    }

    /**
     * 测试根据机构和日期范围查询家长提交 InKind 日活跃记录列表
     */
    @Test
    public void testGetParentSubmitInKindDailyActivitiesByAgencyIdAndDateRange() {
        // 准备测试数据
        String agencyId = "f495b383-79e1-4347-9180-6ab3ea53f3f5"; // 机构 ID
        String centerId = "1b8f3c7b-f093-4afa-bd42-b841d4d69b15"; // 学校 ID
        String groupId = "072dcce5-24cd-4fb7-99f4-ccd42ecc0c67"; // 班级 ID
        String childId = "b3f5f5f5-5f5f-5f5f-5f5f-5f5f5f5f5f5f"; // 学生 ID
        String activityUserId = "e1be53a1-6131-44bd-bfb2-acb5c7506b8b"; // 操作用户 ID
        Date activityAtUtc = TimeUtil.parse("2024-01-01", TimeUtil.format10); // 活跃 UTC 时间
        Date activityAtLocal = TimeUtil.parse("2024-01-01", TimeUtil.format10); // 活跃本地时间
        String startDate = "2024-01-01";
        String endDate = "2024-01-31";
        // 模拟行为
        String sql = BIMapper.SQL_GET_PARENT_SUBMIT_IN_KIND_DAILY_ACTIVITIES_BY_AGENCY_ID_AND_DATE_RANGE;
        List<DailyActivityModel> dailyActivityModels = new ArrayList<>();
        DailyActivityModel dailyActivityModel = new DailyActivityModel();
        dailyActivityModel.setAgencyId(agencyId);
        dailyActivityModel.setCenterId(centerId);
        dailyActivityModel.setGroupId(groupId);
        dailyActivityModel.setChildId(childId);
        dailyActivityModel.setActivityUserId(activityUserId);
        dailyActivityModel.setActivityAtUtc(activityAtUtc);
        dailyActivityModel.setActivityAtLocal(activityAtLocal);
        dailyActivityModels.add(dailyActivityModel);
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(BIMapper.MAPPER_DAILY_ACTIVITY))).thenReturn(dailyActivityModels);

        // 方法调用
        List<DailyActivityModel> activities = biDaoImpl.getParentSubmitInKindDailyActivitiesByAgencyIdAndDateRange(agencyId, startDate, endDate);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{startDate, endDate, agencyId}, BIMapper.MAPPER_DAILY_ACTIVITY);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(activities, dailyActivityModels);
    }

    /**
     * 测试根据机构和日期范围查询老师更新 DLL 日活跃记录列表
     */
    @Test
    public void testGetTeacherUpdateDLLDailyActivitiesByAgencyIdAndDateRange() {
        // 准备测试数据
        String agencyId = "f495b383-79e1-4347-9180-6ab3ea53f3f5"; // 机构 ID
        String centerId = "1b8f3c7b-f093-4afa-bd42-b841d4d69b15"; // 学校 ID
        String groupId = "072dcce5-24cd-4fb7-99f4-ccd42ecc0c67"; // 班级 ID
        String childId = "b3f5f5f5-5f5f-5f5f-5f5f-5f5f5f5f5f5f"; // 学生 ID
        String activityUserId = "e1be53a1-6131-44bd-bfb2-acb5c7506b8b"; // 操作用户 ID
        Date activityAtUtc = TimeUtil.parse("2024-01-01", TimeUtil.format10); // 活跃 UTC 时间
        Date activityAtLocal = TimeUtil.parse("2024-01-01", TimeUtil.format10); // 活跃本地时间
        String startDate = "2024-01-01";
        String endDate = "2024-01-31";
        // 模拟行为
        String sql = BIMapper.SQL_GET_TEACHER_UPDATE_DLL_DAILY_ACTIVITIES_BY_AGENCY_ID_AND_DATE_RANGE;
        List<DailyActivityModel> dailyActivityModels = new ArrayList<>();
        DailyActivityModel dailyActivityModel = new DailyActivityModel();
        dailyActivityModel.setAgencyId(agencyId);
        dailyActivityModel.setCenterId(centerId);
        dailyActivityModel.setGroupId(groupId);
        dailyActivityModel.setChildId(childId);
        dailyActivityModel.setActivityUserId(activityUserId);
        dailyActivityModel.setActivityAtUtc(activityAtUtc);
        dailyActivityModel.setActivityAtLocal(activityAtLocal);
        dailyActivityModels.add(dailyActivityModel);
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(BIMapper.MAPPER_DAILY_ACTIVITY))).thenReturn(dailyActivityModels);

        // 方法调用
        List<DailyActivityModel> activities = biDaoImpl.getTeacherUpdateDLLDailyActivitiesByAgencyIdAndDateRange(agencyId, startDate, endDate);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{startDate, endDate, agencyId}, BIMapper.MAPPER_DAILY_ACTIVITY);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(activities, dailyActivityModels);
    }

    /**
     * 测试根据机构和日期范围查询老师更新课程日活跃记录列表
     */
    @Test
    public void testGetTeacherUpdateLessonDailyActivitiesByAgencyIdAndDateRange() {
        // 准备测试数据
        String agencyId = "f495b383-79e1-4347-9180-6ab3ea53f3f5"; // 机构 ID
        String centerId = "1b8f3c7b-f093-4afa-bd42-b841d4d69b15"; // 学校 ID
        String groupId = "072dcce5-24cd-4fb7-99f4-ccd42ecc0c67"; // 班级 ID
        String childId = "b3f5f5f5-5f5f-5f5f-5f5f-5f5f5f5f5f5f"; // 学生 ID
        String activityUserId = "e1be53a1-6131-44bd-bfb2-acb5c7506b8b"; // 操作用户 ID
        Date activityAtUtc = TimeUtil.parse("2024-01-01", TimeUtil.format10); // 活跃 UTC 时间
        Date activityAtLocal = TimeUtil.parse("2024-01-01", TimeUtil.format10); // 活跃本地时间
        String startDate = "2024-01-01";
        String endDate = "2024-01-31";
        // 模拟行为
        String sql = BIMapper.SQL_GET_TEACHER_UPDATE_LESSON_DAILY_ACTIVITIES_BY_AGENCY_ID_AND_DATE_RANGE;
        List<DailyActivityModel> dailyActivityModels = new ArrayList<>();
        DailyActivityModel dailyActivityModel = new DailyActivityModel();
        dailyActivityModel.setAgencyId(agencyId);
        dailyActivityModel.setCenterId(centerId);
        dailyActivityModel.setGroupId(groupId);
        dailyActivityModel.setChildId(childId);
        dailyActivityModel.setActivityUserId(activityUserId);
        dailyActivityModel.setActivityAtUtc(activityAtUtc);
        dailyActivityModel.setActivityAtLocal(activityAtLocal);
        dailyActivityModels.add(dailyActivityModel);
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(BIMapper.MAPPER_DAILY_ACTIVITY))).thenReturn(dailyActivityModels);

        // 方法调用
        List<DailyActivityModel> activities = biDaoImpl.getTeacherUpdateLessonDailyActivitiesByAgencyIdAndDateRange(agencyId, startDate, endDate);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{startDate, endDate, agencyId}, BIMapper.MAPPER_DAILY_ACTIVITY);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(activities, dailyActivityModels);
    }

    /**
     * 测试根据机构和日期范围查询老师更新周计划日活跃记录列表
     */
    @Test
    public void testGetTeacherUpdateWeekPlanDailyActivitiesByAgencyIdAndDateRange() {
        // 准备测试数据
        String agencyId = "f495b383-79e1-4347-9180-6ab3ea53f3f5"; // 机构 ID
        String centerId = "1b8f3c7b-f093-4afa-bd42-b841d4d69b15"; // 学校 ID
        String groupId = "072dcce5-24cd-4fb7-99f4-ccd42ecc0c67"; // 班级 ID
        String childId = "b3f5f5f5-5f5f-5f5f-5f5f-5f5f5f5f5f5f"; // 学生 ID
        String activityUserId = "e1be53a1-6131-44bd-bfb2-acb5c7506b8b"; // 操作用户 ID
        Date activityAtUtc = TimeUtil.parse("2024-01-01", TimeUtil.format10); // 活跃 UTC 时间
        Date activityAtLocal = TimeUtil.parse("2024-01-01", TimeUtil.format10); // 活跃本地时间
        String startDate = "2024-01-01";
        String endDate = "2024-01-31";
        // 模拟行为
        String sql = BIMapper.SQL_GET_TEACHER_UPDATE_WEEKLY_PLAN_DAILY_ACTIVITIES_BY_AGENCY_ID_AND_DATE_RANGE;
        List<DailyActivityModel> dailyActivityModels = new ArrayList<>();
        DailyActivityModel dailyActivityModel = new DailyActivityModel();
        dailyActivityModel.setAgencyId(agencyId);
        dailyActivityModel.setCenterId(centerId);
        dailyActivityModel.setGroupId(groupId);
        dailyActivityModel.setChildId(childId);
        dailyActivityModel.setActivityUserId(activityUserId);
        dailyActivityModel.setActivityAtUtc(activityAtUtc);
        dailyActivityModel.setActivityAtLocal(activityAtLocal);
        dailyActivityModels.add(dailyActivityModel);
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(BIMapper.MAPPER_DAILY_ACTIVITY))).thenReturn(dailyActivityModels);

        // 方法调用
        List<DailyActivityModel> activities = biDaoImpl.getTeacherUpdateWeekPlanDailyActivitiesByAgencyIdAndDateRange(agencyId, startDate, endDate);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{startDate, endDate, agencyId}, BIMapper.MAPPER_DAILY_ACTIVITY);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(activities, dailyActivityModels);
    }

    /**
     * 测试根据机构和日期范围查询老师更新周计划反思日活跃记录列表
     */
    @Test
    public void testGetTeacherUpdateWeekPlanReflectionDailyActivitiesByAgencyIdAndDateRange() {
        // 准备测试数据
        String agencyId = "f495b383-79e1-4347-9180-6ab3ea53f3f5"; // 机构 ID
        String centerId = "1b8f3c7b-f093-4afa-bd42-b841d4d69b15"; // 学校 ID
        String groupId = "072dcce5-24cd-4fb7-99f4-ccd42ecc0c67"; // 班级 ID
        String childId = "b3f5f5f5-5f5f-5f5f-5f5f-5f5f5f5f5f5f"; // 学生 ID
        String activityUserId = "e1be53a1-6131-44bd-bfb2-acb5c7506b8b"; // 操作用户 ID
        Date activityAtUtc = TimeUtil.parse("2024-01-01", TimeUtil.format10); // 活跃 UTC 时间
        Date activityAtLocal = TimeUtil.parse("2024-01-01", TimeUtil.format10); // 活跃本地时间
        String startDate = "2024-01-01";
        String endDate = "2024-01-31";
        // 模拟行为
        String sql = BIMapper.SQL_GET_TEACHER_UPDATE_WEEKLY_PLAN_REFLECTION_DAILY_ACTIVITIES_BY_AGENCY_ID_AND_DATE_RANGE;
        List<DailyActivityModel> dailyActivityModels = new ArrayList<>();
        DailyActivityModel dailyActivityModel = new DailyActivityModel();
        dailyActivityModel.setAgencyId(agencyId);
        dailyActivityModel.setCenterId(centerId);
        dailyActivityModel.setGroupId(groupId);
        dailyActivityModel.setChildId(childId);
        dailyActivityModel.setActivityUserId(activityUserId);
        dailyActivityModel.setActivityAtUtc(activityAtUtc);
        dailyActivityModel.setActivityAtLocal(activityAtLocal);
        dailyActivityModels.add(dailyActivityModel);
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(BIMapper.MAPPER_DAILY_ACTIVITY))).thenReturn(dailyActivityModels);

        // 方法调用
        List<DailyActivityModel> activities = biDaoImpl.getTeacherUpdateWeekPlanReflectionDailyActivitiesByAgencyIdAndDateRange(agencyId, startDate, endDate);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{startDate, endDate, agencyId}, BIMapper.MAPPER_DAILY_ACTIVITY);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(activities, dailyActivityModels);
    }

    /**
     * 测试根据机构和日期范围查询老师更新睡眠检查日活跃记录列表
     */
    @Test
    public void testGetTeacherUpdateISTDailyActivitiesByAgencyIdAndDateRange() {
        // 准备测试数据
        String agencyId = "f495b383-79e1-4347-9180-6ab3ea53f3f5"; // 机构 ID
        String centerId = "1b8f3c7b-f093-4afa-bd42-b841d4d69b15"; // 学校 ID
        String groupId = "072dcce5-24cd-4fb7-99f4-ccd42ecc0c67"; // 班级 ID
        String childId = "b3f5f5f5-5f5f-5f5f-5f5f-5f5f5f5f5f5f"; // 学生 ID
        String activityUserId = "e1be53a1-6131-44bd-bfb2-acb5c7506b8b"; // 操作用户 ID
        Date activityAtUtc = TimeUtil.parse("2024-01-01", TimeUtil.format10); // 活跃 UTC 时间
        Date activityAtLocal = TimeUtil.parse("2024-01-01", TimeUtil.format10); // 活跃本地时间
        String startDate = "2024-01-01";
        String endDate = "2024-01-31";
        // 模拟行为
        String sql = BIMapper.SQL_GET_TEACHER_UPDATE_IST_DAILY_ACTIVITIES_BY_AGENCY_ID_AND_DATE_RANGE;
        List<DailyActivityModel> dailyActivityModels = new ArrayList<>();
        DailyActivityModel dailyActivityModel = new DailyActivityModel();
        dailyActivityModel.setAgencyId(agencyId);
        dailyActivityModel.setCenterId(centerId);
        dailyActivityModel.setGroupId(groupId);
        dailyActivityModel.setChildId(childId);
        dailyActivityModel.setActivityUserId(activityUserId);
        dailyActivityModel.setActivityAtUtc(activityAtUtc);
        dailyActivityModel.setActivityAtLocal(activityAtLocal);
        dailyActivityModels.add(dailyActivityModel);
        when(jdbcTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(BIMapper.MAPPER_DAILY_ACTIVITY))).thenReturn(dailyActivityModels);

        // 方法调用
        List<DailyActivityModel> activities = biDaoImpl.getTeacherUpdateISTDailyActivitiesByAgencyIdAndDateRange(agencyId, startDate, endDate);

        // 结果验证
        // 验证执行 sql
        verify(jdbcTemplate).query(sql, new String[]{startDate, endDate, agencyId, startDate, endDate, agencyId}, BIMapper.MAPPER_DAILY_ACTIVITY);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(activities, dailyActivityModels);
    }
}

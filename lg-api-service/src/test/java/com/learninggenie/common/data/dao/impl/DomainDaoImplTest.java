package com.learninggenie.common.data.dao.impl;

import com.learninggenie.common.data.entity.DomainEntity;
import com.learninggenie.common.data.mapper.DomainMapper;
import com.learninggenie.common.data.model.note.NoteDomainModel;
import com.learninggenie.common.utils.StringUtil;
import org.apache.commons.lang3.ArrayUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 测评点 dao 层测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class DomainDaoImplTest {

    @InjectMocks
    private DomainDaoImpl domainDao;

    @Mock
    private JdbcTemplate shardingTemplate;


    /**
     * 测试根据归档观察记录 Id 获取测评点信息 测评点信息包括特殊框架的 parentId
     */
    @Test
    public void testGetArchiveDomainByNoteIds() {
        // 准备入参
        // 记录 Id
        String noteId = UUID.randomUUID().toString();
        List<String> noteIds = Collections.singletonList(noteId);
        // 框架 Id
        String frameworkId = UUID.randomUUID().toString();
        // 设置模拟行为
        String sql = DomainMapper.SQL_GET_DOMAIN_AND_NEW_PARENT_BY_NOTE_IDS;
        sql = sql.replace("{@noteIds}", StringUtil.generateParamSql(noteIds.size()));
        // 设置模拟结果
        List<NoteDomainModel> noteDomainModels = new ArrayList<>();
        NoteDomainModel noteDomainModel = new NoteDomainModel();
        noteDomainModel.setNoteId(noteId); // 设置记录 Id
        noteDomainModel.setId(UUID.randomUUID().toString()); // 设置测评点 Id
        noteDomainModel.setName("Number Sense of Math Operations"); // 设置测评点名称
        noteDomainModel.setAbbreviation("COG4"); // 设置测评点缩写
        DomainEntity parentDomain = new DomainEntity();
        String parentId = UUID.randomUUID().toString();
        parentDomain.setId(parentId); // 设置父测评点 Id
        parentDomain.setName("Cognition: Math"); // 设置父测评点名称
        parentDomain.setAbbreviation("COG:MATH"); // 设置父测评点缩写
        noteDomainModel.setParent(parentDomain); // 设置父测评点
        noteDomainModels.add(noteDomainModel);
        when(shardingTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(DomainMapper.NOTE_DOMAIN_ENTITY_ROW_MAPPER))).thenReturn(noteDomainModels);
        // 方法调用
        List<NoteDomainModel> result = domainDao.getArchivedDomainByNoteIds(noteIds, frameworkId);

        // 结果验证
        // 验证执行 sql
        verify(shardingTemplate).query(sql, ArrayUtils.add(noteIds.toArray(), frameworkId), DomainMapper.NOTE_DOMAIN_ENTITY_ROW_MAPPER);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(noteDomainModels, result);
    }

    /**
     * 测试根据观察记录 Id 获取测评点信息 测评点信息包括特殊框架的 parentId
     */
    @Test
    public void testGetDomainByNoteIds() {
        // 准备入参
        // 记录 Id
        String noteId = UUID.randomUUID().toString();
        List<String> noteIds = Collections.singletonList(noteId);
        // 框架 Id
        String frameworkId = UUID.randomUUID().toString();
        // 设置模拟行为
        String sql = DomainMapper.SQL_GET_DOMAIN_AND_NEW_PARENT_BY_NOTE_IDS;
        sql = sql.replace("{@noteIds}", StringUtil.generateParamSql(noteIds.size()));
        // 设置模拟结果
        List<NoteDomainModel> noteDomainModels = new ArrayList<>();
        NoteDomainModel noteDomainModel = new NoteDomainModel();
        noteDomainModel.setNoteId(noteId); // 设置记录 Id
        noteDomainModel.setId(UUID.randomUUID().toString()); // 设置测评点 Id
        noteDomainModel.setName("Number Sense of Math Operations"); // 设置测评点名称
        noteDomainModel.setAbbreviation("COG4"); // 设置测评点缩写
        DomainEntity parentDomain = new DomainEntity();
        String parentId = UUID.randomUUID().toString();
        parentDomain.setId(parentId); // 设置父测评点 Id
        parentDomain.setName("Cognition: Math"); // 设置父测评点名称
        parentDomain.setAbbreviation("COG:MATH"); // 设置父测评点缩写
        noteDomainModel.setParent(parentDomain); // 设置父测评点
        noteDomainModels.add(noteDomainModel);
        when(shardingTemplate.query(anyString(), Mockito.any(Object[].class), Mockito.eq(DomainMapper.NOTE_DOMAIN_ENTITY_ROW_MAPPER))).thenReturn(noteDomainModels);
        // 方法调用
        List<NoteDomainModel> result = domainDao.getDomainByNoteIds(noteIds, frameworkId);

        // 结果验证
        // 验证执行 sql
        verify(shardingTemplate).query(sql, ArrayUtils.add(noteIds.toArray(), frameworkId), DomainMapper.NOTE_DOMAIN_ENTITY_ROW_MAPPER);
        // 验证查询出来的结果和预期结果是否一致
        assertEquals(noteDomainModels, result);
    }
    /**
     * 测试 getFrameworkMeasuresTreeByDomainIds 方法
     */
    @Test
    public void testGetFrameworkMeasuresTreeByDomainIds_EmptyDomainIds() {
        // 当 domainIds 为空时，期望返回空集合
        List<com.learninggenie.common.data.model.DomainEntity> result = domainDao.getFrameworkMeasuresTreeByDomainIds(null);
        assertTrue(result.isEmpty());

        result = domainDao.getFrameworkMeasuresTreeByDomainIds(Collections.emptyList());
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetFrameworkMeasuresTreeByDomainIds_NonEmptyDomainIds() {
        // 准备测试数据
        List<String> domainIds = Arrays.asList("domain1", "domain2", "domain3");
        String sql = DomainMapper.SQL_GET_ALL_MEASURES_BY_DOMAIN_IDS.replace("@IN", StringUtil.convertIdsToString(domainIds));
        List<com.learninggenie.common.data.model.DomainEntity> expectedDomainEntities = Arrays.asList(
                new com.learninggenie.common.data.model.DomainEntity(){{
                    setId("domain1");
                    setName("Domain 1");
                }},
                new com.learninggenie.common.data.model.DomainEntity(){{
                    setId("domain2");
                    setName("Domain 2");
                }}
        );

        // 模拟 shardingTemplate.query 的行为
        when(shardingTemplate.query(Mockito.eq(sql), Mockito.any(Object[].class), Mockito.eq(DomainMapper.MAPPER_DOMAIN)))
                .thenReturn(expectedDomainEntities);

        // 调用被测试方法
        List<com.learninggenie.common.data.model.DomainEntity> result = domainDao.getFrameworkMeasuresTreeByDomainIds(domainIds);

        // 验证结果
        assertEquals(expectedDomainEntities.size(), result.size());
        assertEquals(expectedDomainEntities, result);

        // 验证 shardingTemplate.query 被正确调用
        verify(shardingTemplate, times(1)).query(eq(sql), any(Object[].class), eq(DomainMapper.MAPPER_DOMAIN));
    }

}

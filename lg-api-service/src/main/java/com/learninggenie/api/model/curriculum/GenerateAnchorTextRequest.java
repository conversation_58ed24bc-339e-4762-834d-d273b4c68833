package com.learninggenie.api.model.curriculum;

import com.learninggenie.common.data.enums.lesson2.ReadingLevelTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 生成 Anchor Text 请求信息
 */
@Data
@ApiModel(description = "生成 Anchor Text 请求信息")
public class GenerateAnchorTextRequest {
    @ApiModelProperty(value = "课程 ID", required = true)
    private String lessonId;

    @ApiModelProperty(value = "单元 ID", required = true)
    private String unitId;

    @ApiModelProperty(value = "单元 ID", required = true)
    private String planId;

    private Integer week;

    private Integer day;

    @ApiModelProperty(value = "级别类型（Lexile 或 Grade）", required = true)
    private ReadingLevelTypeEnum levelType;

    @ApiModelProperty(value = "级别值（如：500L 或 Grade 3）")
    private String level;

    @ApiModelProperty(value = "基础 Anchor Text")
    private String baseAnchorText;

    @ApiModelProperty(value = "基础年级")
    private String baseGrade;

    @ApiModelProperty(value = "阅读类型（Poem, Excerpt, Short Story, Empty）", required = true)
    private String readingType;

    private boolean textLengthShorter;

    private boolean textLengthLonger;
}
